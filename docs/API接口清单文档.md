# Himall Order System API接口清单文档

## 文档概述

本文档详细列出了Himall Order System项目中所有的API接口，包括REST接口和Feign客户端接口。系统采用微服务架构，提供完整的订单管理、支付处理、财务管理等功能。

## 接口分类

### 1. 订单管理接口

#### 1.1 订单基础操作 (OrderController)
**Base Path**: `/himall-order`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 生成订单号 | GET | `/generateOrderNo` | 批量生成订单号 | size: Integer | List<String> |

#### 1.2 订单查询接口 (OrderQueryController)
**Base Path**: `/himall-order/orderQuery`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 订单分页查询 | POST | `/pageQuery` | 分页查询订单列表 | QueryOrderReq | BasePageResp<OrderResp> |
| 订单详情查询 | POST | `/getOrderDetail` | 获取订单详细信息 | OrderDetailReq | OrderDetailResp |
| 订单统计查询 | POST | `/getOrderStatistics` | 获取订单统计数据 | OrderStatisticsReq | OrderStatisticsResp |
| 商品购买统计 | POST | `/getProductBuyCount` | 获取商品购买统计 | ProductBuyCountReq | ProductBuyCountResp |
| 店铺订单统计 | POST | `/getShopOrderStats` | 获取店铺订单统计 | ShopOrderStatsReq | ShopOrderStatsResp |
| ERP批量查询 | POST | `/queryEachStatusCount` | ERP批量查询订单 | QueryBatchErpOrderReq | ErpBatchOrderListResp |
| 状态统计查询 | GET | `/queryEachStatusCount` | 查询各状态订单数量 | userId: Long | EachStatusCountResp |

#### 1.3 订单操作接口 (OrderCmdController)
**Base Path**: `/himall-order/orderCmd`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 创建订单 | POST | `/createOrder` | 创建新订单 | CreateOrderReq | CreateOrderResp |
| 取消订单 | POST | `/cancelOrder` | 取消订单 | CancelOrderReq | BaseResp |
| 确认收货 | POST | `/confirmReceive` | 确认收货 | ConfirmReceiveReq | BaseResp |
| 订单发货 | POST | `/deliverOrder` | 订单发货操作 | DeliverOrderReq | BaseResp |
| 批量发货 | POST | `/batchDeliverOrder` | 批量订单发货 | BatchDeliverOrderReq | BaseResp |
| 初始化订单ES | POST | `/initOrderES` | 初始化订单ES索引 | - | BaseResp |
| 初始化售后ES | POST | `/initOrderRefundES` | 初始化售后ES索引 | - | BaseResp |

### 2. 支付管理接口

#### 2.1 支付操作接口 (PayCmdController)
**Base Path**: `/himall-order/pay`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 创建支付 | POST | `/createPayment` | 创建支付订单 | PayPaymentCreateReq | PayPaymentCreateResp |
| 创建退款 | POST | `/createPaymentReverse` | 创建退款订单 | PayReverseCreateReq | PayReverseCreateResp |
| 创建分账 | POST | `/createPaymentConfirm` | 创建支付确认(分账) | PaymentConfirmCreateReq | PaymentConfirmCreateResp |

#### 2.2 支付查询接口 (PayQueryController)
**Base Path**: `/himall-order/pay`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 支付信息查询 | POST | `/queryOrderPayOne` | 查询单个支付信息 | OrderPayQueryReq | OrderPayResp |
| 退款信息查询 | POST | `/queryReverseOrderOne` | 查询单个退款信息 | ReverseOrderQueryReq | ReverseOrderResp |
| 支付完成查询 | POST | `/queryCompletePay` | 查询支付完成状态 | QueryCompletePayReq | Map<String,OrderPayResp> |

#### 2.3 支付回调接口 (PayCallBackController)
**Base Path**: `/himall-order/payCallBack`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 汇付支付回调 | POST | `/adaPayCallback` | 汇付支付回调处理 | PayCallBackReq | BaseResp |
| Mock支付创建 | POST | `/mock/pay` | Mock支付创建(测试) | OrderPayResp | String |
| Mock支付回调 | POST | `/mock/collBack` | Mock支付回调(测试) | MockPayCallBack | BaseResp |

### 3. 会员账户接口

#### 3.1 会员操作接口 (MemberCmdController)
**Base Path**: `/himall-order/member`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 查询会员信息 | POST | `/queryMember` | 查询会员账户信息 | PayBaseReq | BaseResp |
| 创建会员账户 | POST | `/createMember` | 创建会员账户 | PayMemberReq | BaseResp |
| 创建结算账户 | POST | `/createSettleAccount` | 创建结算账户 | PaySettleAccountReq | BaseResp |

#### 3.2 会员查询接口 (MemberQueryController)
**Base Path**: `/himall-order/member`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 结算账户检查 | POST | `/hasSettleAccount` | 检查是否有结算账户 | PaySettleAccountQryReq | Boolean |

### 4. 商品评论接口

#### 4.1 评论操作接口 (ProductCommentCmdController)
**Base Path**: `/himall-order/productCommentCmd`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 隐藏评论 | POST | `/hideProductCommentForPlatForm` | 平台隐藏商品评论 | HideProductCommentReq | BaseResp |
| 回复评论 | POST | `/replyProductCommentForSeller` | 商家回复商品评论 | ReplyProductCommentReq | BaseResp |

#### 4.2 评论查询接口 (ProductCommentQueryController)
**Base Path**: `/himall-order/productCommentQuery`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 平台评论查询 | POST | `/queryProductCommentForPlatform` | 平台端查询商品评论 | QueryProductCommentReq | BasePageResp<ProductCommentResp> |
| 商家评论查询 | POST | `/queryProductCommentForSeller` | 商家端查询商品评论 | QueryProductCommentReq | BasePageResp<ProductCommentResp> |

### 5. 投诉维权接口

#### 5.1 投诉操作接口 (ComplaintCmdController)
**Base Path**: `/himall-order/complaintCmd`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 发起投诉 | POST | `/startComplaint` | 发起投诉维权 | StartComplaintReq | BaseResp |
| 取消投诉 | POST | `/cancelComplaint` | 取消投诉维权 | CancelComplaintReq | BaseResp |

### 6. 异常订单接口

#### 6.1 异常订单查询 (ExceptionOrderQueryController)
**Base Path**: `/himall-order/exceptionOrder`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 异常订单分页查询 | POST | `/pageList` | 分页查询异常订单 | QueryExceptionOrderReq | BasePageResp<ExceptionOrderInfoDto> |

### 7. 电子面单接口

#### 7.1 面单相关接口 (HishopWayBillController)
**Base Path**: `/himall-order/wayBill`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 查询已售订单 | POST | `/getSoldTrades` | 查询已售订单分页列表 | OrderGetSoldTradesReq | BasePageResp<OrderInfoDto> |
| 查询增量订单 | POST | `/getIncrementSoldTrades` | 查询订单增量数据 | OrderGetSoldTradesReq | BasePageResp<OrderInfoDto> |
| 根据ID查询订单 | GET | `/getTradeByOrderId` | 根据订单ID查询订单信息 | orderId: String | OrderInfoDto |

### 8. 财务管理接口

#### 8.1 财务查询接口 (FinanceQueryController)
**Base Path**: `/himall-order/finance/financeQuery`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 财务首页数据 | POST | `/getFinanceIndex` | 获取财务首页统计数据 | BaseIdReq | FinanceIndexResp |
| 财务明细查询 | POST | `/getFinance` | 查询财务明细 | FinanceQueryReq | FinanceResp |

#### 8.2 保证金管理接口 (CashDepositController)
**Base Path**: `/himall-order/finance/cashDeposit`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 保证金扣减 | POST | `/deduction` | 保证金扣减操作 | DeductionReq | BaseResp |

#### 8.3 保证金支付接口 (CashDepositPayController)
**Base Path**: `/himall-order/finance/cashDepositPay`

| 接口名称 | HTTP方法 | 路径 | 功能描述 | 参数 | 返回值 |
|---------|---------|------|---------|------|--------|
| 查询保证金支付 | POST | `/selectCashDepositPay` | 查询保证金支付记录 | CashDepositPayReq | List<CashDepositPayResp> |

## Feign客户端接口

### 1. 订单相关Feign接口

| Feign接口 | 服务名称 | 功能描述 |
|----------|---------|---------|
| `OrderFeign` | histore-order | 订单基础服务 |
| `OrderQueryFeign` | himall-order | 订单查询服务 |
| `OrderCmdFeign` | himall-order | 订单操作服务 |

### 2. 支付相关Feign接口

| Feign接口 | 服务名称 | 功能描述 |
|----------|---------|---------|
| `PayQueryFeign` | himall-order | 支付查询服务 |
| `PayCmdFeign` | himall-order | 支付操作服务 |
| `PayCallBackFeign` | himall-order | 支付回调服务 |

### 3. 会员相关Feign接口

| Feign接口 | 服务名称 | 功能描述 |
|----------|---------|---------|
| `MemberQueryFeign` | himall-order | 会员查询服务 |
| `MemberCmdFeign` | himall-order | 会员操作服务 |

### 4. 其他Feign接口

| Feign接口 | 服务名称 | 功能描述 |
|----------|---------|---------|
| `ProductCommentQueryFeign` | himall-order | 评论查询服务 |
| `ProductCommentCmdFeign` | himall-order | 评论操作服务 |
| `ComplaintCmdFeign` | himall-order | 投诉操作服务 |
| `HishopWayBillFegin` | himall-order | 电子面单服务 |
| `FinanceQueryFeign` | himall-order | 财务查询服务 |
| `CashDepositDetailCmdFeign` | himall-order | 保证金详情操作服务 |
| `CashDepositPayFeign` | himall-order | 保证金支付服务 |

## 接口通用规范

### 1. 请求格式
- **Content-Type**: `application/json`
- **字符编码**: UTF-8
- **请求方法**: 主要使用POST方法，部分查询使用GET

### 2. 响应格式
```json
{
  "code": "200",
  "message": "success",
  "data": {},
  "success": true
}
```

### 3. 分页响应格式
```json
{
  "code": "200",
  "message": "success",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "pages": 5,
    "total": 50,
    "list": []
  },
  "success": true
}
```

### 4. 异常处理
- 统一异常处理机制
- 业务异常返回具体错误码和错误信息
- 系统异常返回通用错误信息

### 5. 接口安全
- 参数校验：所有接口都有参数校验
- 幂等性保证：关键操作支持幂等性
- 敏感数据加密：支付相关数据加密传输
- 访问权限控制：基于角色的权限控制

## 接口文档访问

项目集成了SpringDoc OpenAPI，启动后可通过以下地址访问完整的API文档：
- **Swagger UI**: http://localhost:8080/swagger-ui.html
- **OpenAPI JSON**: http://localhost:8080/v3/api-docs

## 接口测试说明

### 1. Mock接口
项目提供了Mock接口用于测试：
- Mock支付创建：`/mock/pay`
- Mock支付回调：`/mock/collBack`

### 2. 测试环境
- 开发环境：chengpei_local
- 测试环境：test
- 生产环境：prod

### 3. 测试建议
- 使用Postman或类似工具进行接口测试
- 关注接口响应时间和错误处理
- 验证分页查询的性能
- 测试异常场景的处理
- 验证支付回调的正确性
