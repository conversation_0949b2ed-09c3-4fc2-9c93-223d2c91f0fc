# Himall Order System 架构图文档

## 文档概述

本文档通过多个架构图详细展示了Himall Order System的系统架构设计，包括整体架构、模块架构、技术架构、数据流架构等，帮助开发者和架构师全面理解系统设计。

## 1. 系统整体架构图

### 1.1 分层架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │   Web应用   │ │  移动应用   │ │  管理后台   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    网关层                                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  API网关    │ │  负载均衡   │ │  服务发现   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    应用服务层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  订单服务   │ │  支付服务   │ │  财务服务   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    数据访问层                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  订单DAO    │ │  支付DAO    │ │  财务DAO    │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    基础设施层                                │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐           │
│  │ MySQL   │ │ Redis   │ │   ES    │ │RocketMQ │           │
│  └─────────┘ └─────────┘ └─────────┘ └─────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 微服务架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    服务注册与发现                            │
│                    Nacos Registry                           │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    配置管理中心                              │
│                    Nacos Config                             │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Himall Order Service                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │ order-api   │ │ order-core  │ │order-server │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │order-common │ │ order-dao   │ │order-finance│           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 2. 模块架构图

### 2.1 模块依赖关系图

```mermaid
graph TB
    Server[seashop-order-server<br/>服务启动模块] --> API[seashop-order-api<br/>API接口模块]
    Server --> Core[seashop-order-core<br/>核心业务模块]
    Server --> Finance[seashop-order-finance<br/>财务管理模块]
    
    Core --> Common[seashop-order-common<br/>公共组件模块]
    Core --> DAO[seashop-order-dao<br/>数据访问模块]
    Finance --> Common
    Finance --> DAO
    
    API --> Common
    
    Common --> ES[Elasticsearch<br/>搜索引擎]
    Common --> Redis[Redis<br/>缓存]
    DAO --> MySQL[MySQL<br/>数据库]
    
    Core --> MQ[RocketMQ<br/>消息队列]
    Finance --> MQ
    
    style Server fill:#e1f5fe
    style Core fill:#f3e5f5
    style Finance fill:#fff3e0
    style Common fill:#e8f5e8
    style DAO fill:#ffebee
```

### 2.2 业务模块架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    订单管理模块                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  订单创建   │ │  订单查询   │ │  订单操作   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  状态管理   │ │  物流管理   │ │  异常处理   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    支付管理模块                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  支付创建   │ │  支付查询   │ │  支付回调   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  退款处理   │ │  分账管理   │ │  会员管理   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    财务管理模块                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  财务查询   │ │  保证金管理 │ │  结算管理   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    公共服务模块                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  搜索服务   │ │  评论管理   │ │  投诉维权   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 3. 技术架构图

### 3.1 技术栈架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    前端技术栈                                │
│  Vue.js / React / 小程序 / H5 / 管理后台                    │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/HTTPS
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    网关层技术栈                              │
│  Spring Cloud Gateway / Nginx / Load Balancer              │
└─────────────────────────────────────────────────────────────┘
                              │ Feign/HTTP
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    应用层技术栈                              │
│  Spring Boot 2.7 / Spring Cloud / Spring Security         │
│  MyBatis Plus / Lombok / Validation                        │
└─────────────────────────────────────────────────────────────┘
                              │ JDBC/HTTP
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    中间件技术栈                              │
│  MySQL 5.7+ / Redis 3.0+ / Elasticsearch 7.x              │
│  RocketMQ 4.x / Nacos 2.x / XXL-Job                        │
└─────────────────────────────────────────────────────────────┘
```

### 3.2 服务治理架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    服务注册发现                              │
│                    Nacos Registry                           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  服务注册   │ │  健康检查   │ │  负载均衡   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    配置管理                                  │
│                    Nacos Config                             │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  配置中心   │ │  动态刷新   │ │  环境隔离   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    服务调用                                  │
│                    OpenFeign                                │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  声明式调用 │ │  负载均衡   │ │  熔断降级   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 4. 数据架构图

### 4.1 数据存储架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    关系型数据库 (MySQL)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  订单数据   │ │  支付数据   │ │  财务数据   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  用户数据   │ │  商品数据   │ │  评论数据   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    缓存层 (Redis)                            │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  会话缓存   │ │  业务缓存   │ │  分布式锁   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    搜索引擎 (Elasticsearch)                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  订单索引   │ │  退款索引   │ │  评论索引   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

### 4.2 数据流架构图

```mermaid
graph LR
    Client[客户端] --> Gateway[API网关]
    Gateway --> OrderService[订单服务]
    Gateway --> PayService[支付服务]
    Gateway --> FinanceService[财务服务]
    
    OrderService --> MySQL[(MySQL)]
    OrderService --> Redis[(Redis)]
    OrderService --> ES[(Elasticsearch)]
    OrderService --> MQ[RocketMQ]
    
    PayService --> MySQL
    PayService --> Redis
    PayService --> MQ
    PayService --> AdaPay[汇付支付]
    
    FinanceService --> MySQL
    FinanceService --> Redis
    
    MQ --> OrderConsumer[订单消费者]
    MQ --> PayConsumer[支付消费者]
    MQ --> ESConsumer[ES构建消费者]
    
    OrderConsumer --> MySQL
    PayConsumer --> MySQL
    ESConsumer --> ES
    
    style Client fill:#e1f5fe
    style Gateway fill:#e8f5e8
    style OrderService fill:#f3e5f5
    style PayService fill:#fff3e0
    style FinanceService fill:#e0f2f1
```

## 5. 消息架构图

### 5.1 消息队列架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    RocketMQ 消息队列                         │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                    Topic 设计                       │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │订单变更Topic│ │支付状态Topic│ │退款Topic    │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │DTS同步Topic │ │延时检查Topic│ │异常Topic    │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                    消费者组                         │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │订单消费者组│ │支付消费者组│ │ES构建消费者 │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │WDT构建消费者│ │退款消费者组│ │异常消费者组 │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

### 5.2 消息流转图

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant OrderService as 订单服务
    participant MQ as RocketMQ
    participant PayService as 支付服务
    participant ESService as ES服务
    participant WDTService as WDT服务
    
    Client->>OrderService: 创建订单
    OrderService->>MQ: 发送订单变更消息
    OrderService->>MQ: 发送延时检查消息
    
    MQ->>ESService: 订单ES构建
    MQ->>WDTService: WDT数据构建
    
    Client->>PayService: 发起支付
    PayService->>MQ: 发送支付状态变更消息
    
    MQ->>OrderService: 支付状态通知
    OrderService->>MQ: 发送订单状态变更消息
    
    MQ->>ESService: 更新订单ES索引
```

## 6. 定时任务架构图

### 6.1 定时任务调度架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    XXL-Job 调度中心                          │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                    任务分类                         │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │  订单任务   │ │  支付任务   │ │  评论任务   │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │  退款任务   │ │  数据任务   │ │  报表任务   │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  └─────────────────────────────────────────────────────┘   │
│                                                             │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                    执行器                           │   │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐   │   │
│  │  │订单执行器   │ │支付执行器   │ │财务执行器   │   │   │
│  │  └─────────────┘ └─────────────┘ └─────────────┘   │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
```

## 7. 监控架构图

### 7.1 监控体系架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    监控数据收集                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  应用监控   │ │  系统监控   │ │  业务监控   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    监控数据存储                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  时序数据库 │ │  日志存储   │ │  指标存储   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    监控数据展示                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  监控大屏   │ │  告警系统   │ │  报表系统   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 8. 部署架构图

### 8.1 部署环境架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    生产环境                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  应用集群   │ │  数据库集群 │ │  缓存集群   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    测试环境                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  应用实例   │ │  测试数据库 │ │  测试缓存   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
├─────────────────────────────────────────────────────────────┤
│                    开发环境                                  │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐           │
│  │  本地应用   │ │  开发数据库 │ │  本地缓存   │           │
│  └─────────────┘ └─────────────┘ └─────────────┘           │
└─────────────────────────────────────────────────────────────┘
```

## 架构设计原则

### 1. 设计原则
- **单一职责**: 每个模块职责单一明确
- **开闭原则**: 对扩展开放，对修改关闭
- **依赖倒置**: 依赖抽象而非具体实现
- **接口隔离**: 接口设计精简专一

### 2. 架构特点
- **高可用**: 多实例部署，故障自动转移
- **高性能**: 缓存优化，异步处理
- **可扩展**: 模块化设计，水平扩展
- **可维护**: 代码规范，文档完善

### 3. 技术选型
- **成熟稳定**: 选择经过验证的技术栈
- **社区活跃**: 技术社区支持良好
- **团队熟悉**: 团队技术栈匹配
- **未来发展**: 技术发展趋势良好
