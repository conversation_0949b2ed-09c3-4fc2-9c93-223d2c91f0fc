package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:58
 */
@Data
@ToString
@TypeDoc(description = "查询订单评论详情入参")
public class QueryOrderCommentDetailReq extends BaseParamReq {

    @FieldDoc(description = "订单id", requiredness = Requiredness.REQUIRED)
    private String orderId;

    @FieldDoc(description = "用户id", requiredness = Requiredness.OPTIONAL)
    private Long userId;

    @Override
    public void checkParameter() {
      AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(orderId), "订单id不能为空");
    }

    public void checkForBuyer() {
        checkParameter();
        AssertUtil.throwInvalidParamIfTrue(userId == null || userId <= 0, "用户id不能为空");
    }


}
