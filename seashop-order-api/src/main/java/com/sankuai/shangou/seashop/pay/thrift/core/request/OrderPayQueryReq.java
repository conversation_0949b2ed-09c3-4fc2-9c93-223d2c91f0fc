package com.sankuai.shangou.seashop.pay.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "订单支付查询条件对象")
public class OrderPayQueryReq extends BaseParamReq {

    @FieldDoc(description = "支付流水ID")
    private String payId;

    @FieldDoc(description = "来源订单ID")
    private String orderId;

    @FieldDoc(description = "渠道支付ID")
    private String channelPayId;

    @FieldDoc(description = "查询渠道支付（是否查询汇付，默认不查询）")
    private Boolean queryChannel = false;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.payId) && StrUtil.isBlank(this.orderId) && StrUtil.isBlank(this.channelPayId)) {
            throw new InvalidParamException("payId,orderId,channelPayId不能同时为空");
        }
    }


}
