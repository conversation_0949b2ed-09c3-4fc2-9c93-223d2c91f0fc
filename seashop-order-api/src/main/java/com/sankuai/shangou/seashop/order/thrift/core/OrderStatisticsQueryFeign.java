package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.StatsUserPurchaseSkuReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.stats.StatsShopTopNSaleProductReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.PlatformIndexTradeDataStatsResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.SellerIndexTradeStatsResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.StatsUserPurchaseSkuResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.TopProductSaleStatsResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * 主要应用于订单统计的操作场景
 */
@FeignClient(name = "himall-order", contextId = "OrderStatisticsQueryFeign", path = "/himall-order/orderStatistics", url = "${himall-order.dev.url:}")
public interface OrderStatisticsQueryFeign {

    /**
     * 统计用户订单sku维度的采购数据
     */
    @PostMapping(value = "/statsUserPurchaseSku", consumes = "application/json")
    ResultDto<StatsUserPurchaseSkuResp> statsUserPurchaseSku(@RequestBody StatsUserPurchaseSkuReq req) throws TException;

    /**
     * 平台首页交易数据统计
     */
    @GetMapping(value = "/statsPlatformIndexTradeData")
    ResultDto<PlatformIndexTradeDataStatsResp> statsPlatformIndexTradeData() throws TException;

    /**
     * 供应商首页交易数据统计
     */
    @GetMapping(value = "/statsSellerIndexTradeData")
    ResultDto<SellerIndexTradeStatsResp> statsSellerIndexTradeData(@RequestParam Long shopId) throws TException;

    /**
     * 供应商首页-控制台-销售排名前N的商品
     */
    @PostMapping(value = "/statsTopNSaleProduct", consumes = "application/json")
    ResultDto<TopProductSaleStatsResp> statsTopNSaleProduct(@RequestBody StatsShopTopNSaleProductReq req) throws TException;

}
