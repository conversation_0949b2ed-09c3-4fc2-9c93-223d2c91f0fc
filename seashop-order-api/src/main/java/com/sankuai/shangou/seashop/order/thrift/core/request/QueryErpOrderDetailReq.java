package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 查询erp订单入参
 *
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "查询订单明细请求入参")
public class QueryErpOrderDetailReq extends BaseParamReq {

    @FieldDoc(
            name = "orderId",
            description = "订单ID",
            requiredness = Requiredness.NONE
    )
    private String orderId;

    @FieldDoc(
            name = "sourceOrderId",
            description = "牵牛花订单号",
            requiredness = Requiredness.NONE
    )
    private String sourceOrderId;


    @Override
    public void checkParameter() {
        if (StringUtils.isBlank(orderId) && StringUtils.isBlank(sourceOrderId)) {
            throw new IllegalArgumentException("erp订单详情缺少入参");
        }
    }


}
