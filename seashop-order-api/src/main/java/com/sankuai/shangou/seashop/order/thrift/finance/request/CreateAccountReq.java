package com.sankuai.shangou.seashop.order.thrift.finance.request;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @description:
 * @author: LXH
 **/
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "创建支付请求对象")
public class CreateAccountReq extends BaseParamReq {
    private Long shopId;
    private String shopName;
    private String accountId;


}
