package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.ConfirmReceiptWxReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname OrderFeign
 * Description //TODO
 * @date 2024/11/19 15:03
 */
@FeignClient(name = "histore-order", contextId = "OrderFeign", path = "/histore-order", url = "${histore-order.dev.url:}")
public interface OrderFeign {
    /**
     * 创建订单
     * 订单服务的这个方法的前提是上游服务已经校验过数据的有效性了，订单服务只根据参数数据创建订单，不计算不校验
     */
    @GetMapping(value = "/generateOrderNo")
    ResultDto<List<String>> generateOrderNo(@RequestParam Integer size);
}
