package com.sankuai.shangou.seashop.order.thrift.core.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2023/12/05 11:51
 */
public class CommentEnum {

    // 评论类型 0-首次评论 1-追加评论
    @Getter
    public enum CommentType {
        FIRST(0, "首次评论"),
        APPEND(1, "追加评论");

        private final Integer code;
        private final String desc;

        CommentType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    // 回复状态 0-全部 1-未处理
    @Getter
    public enum ReplyStatus {
        ALL(0, "全部"),
        UN_HANDLE(1, "未处理");

        private final Integer code;
        private final String desc;

        ReplyStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    /**
     * 评价分数
     */
    @Getter
    public enum ReviewMark {
        ONE(1, "1分"),
        TWO(2, "2分"),
        THREE(3, "3分"),
        FOUR(4, "4分"),
        FIVE(5, "5分");

        private final Integer code;
        private final String desc;

        ReviewMark(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(Integer code) {
            for (ReviewMark reviewMark : ReviewMark.values()) {
                if (reviewMark.getCode().equals(code)) {
                    return reviewMark.getDesc();
                }
            }
            return null;
        }
    }


    @Getter
    public enum ReviewMarkRange {

        /**
         * code: 1 好评 5分
         * code: 2 中评 3-4分
         * code: 3 差评 1-2分
         */
        GOOD(1, "好评", 5),
        MIDDLE(2, "中评", 3, 4),
        BAD(3, "差评", 1, 2);

        private final Integer code;
        private final String desc;
        private final Integer[] markRange;

        ReviewMarkRange(Integer code, String desc, Integer... markRange) {
            this.code = code;
            this.desc = desc;
            this.markRange = markRange;
        }

        public static String getDescByCode(Integer code) {
            for (ReviewMarkRange reviewMarkRange : ReviewMarkRange.values()) {
                if (reviewMarkRange.getCode().equals(code)) {
                    return reviewMarkRange.getDesc();
                }
            }
            return null;
        }
    }


    // 0-全部 1-好评 2-中评 3-差评 4-有图 5-追加评论
    @Getter
    public enum MallCommentStatus {
        ALL(0, "全部"),
        GOOD(1, "好评"),
        MIDDLE(2, "中评"),
        BAD(3, "差评"),
        HAS_IMAGE(4, "有图"),
        HAS_APPEND(5, "追加评论");

        private final Integer code;
        private final String desc;

        MallCommentStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(Integer code) {
            for (MallCommentStatus mallCommentStatus : MallCommentStatus.values()) {
                if (mallCommentStatus.getCode().equals(code)) {
                    return mallCommentStatus.getDesc();
                }
            }
            return null;
        }

        public static MallCommentStatus getByCode(Integer code) {
            for (MallCommentStatus mallCommentStatus : MallCommentStatus.values()) {
                if (mallCommentStatus.getCode().equals(code)) {
                    return mallCommentStatus;
                }
            }
            return null;
        }
    }

}
