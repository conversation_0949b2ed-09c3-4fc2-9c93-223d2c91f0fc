package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/12/25 11:01
 */
@Data
@ToString
@TypeDoc(description = "商品评价汇总返回值")
public class ProductCommentSummaryResp extends BaseThriftDto {

    @FieldDoc(description = "商品id")
    private Long productId;

    @FieldDoc(description = "评价总数")
    private Integer totalCount;

    @FieldDoc(description = "五分评价数")
    private Integer fiveStarCount;

    @FieldDoc(description = "四分评价数")
    private Integer fourStarCount;

    @FieldDoc(description = "三分评价数")
    private Integer threeStarCount;

    @FieldDoc(description = "二分评价数")
    private Integer twoStarCount;

    @FieldDoc(description = "一分评价数")
    private Integer oneStarCount;

    @FieldDoc(description = "有图评价数")
    private Integer hasImageCount;

    @FieldDoc(description = "追加评论数")
    private Integer appendCount;

    @FieldDoc(description = "好评率")
    private BigDecimal goodRate;

    @FieldDoc(description = "中评率")
    private BigDecimal middleRate;

    @FieldDoc(description = "差评率")
    private BigDecimal badRate;

    @FieldDoc(description = "综合评分")
    private BigDecimal score;


    public String getGoodRateString() {
        return this.bigDecimal2String(this.goodRate);
    }


    public void setGoodRateString(String goodRate) {
        this.goodRate = this.string2BigDecimal(goodRate);
    }


    public String getMiddleRateString() {
        return this.bigDecimal2String(this.middleRate);
    }


    public void setMiddleRateString(String middleRate) {
        this.middleRate = this.string2BigDecimal(middleRate);
    }


    public String getBadRateString() {
        return this.bigDecimal2String(this.badRate);
    }


    public void setBadRateString(String badRate) {
        this.badRate = this.string2BigDecimal(badRate);
    }


    public String getScoreString() {
        return this.bigDecimal2String(this.score);
    }


    public void setScoreString(String score) {
        this.score = this.string2BigDecimal(score);
    }
}
