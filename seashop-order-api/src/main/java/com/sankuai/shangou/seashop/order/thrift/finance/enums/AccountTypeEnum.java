package com.sankuai.shangou.seashop.order.thrift.finance.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/12/22/022
 * @description:
 */
@ThriftEnum
public enum AccountTypeEnum {

    SETTLEMENT_INCOME(1, "结算入帐"),
    REFUND(2, "退款"),
    PLAT_COMMISSION_REFUND(3, "平台佣金退还"),
    DISTRIBUTOR_COMMISSION_REFUND(4, "分销佣金退还"),
    MARKETING_SERVICES(5, "营销服务费"),
    WITH_DRAW(6, "提现"),
    RECHARGE(7, "充值"),
    RENEW(8, "续费当前套餐"),
    UPGRADE(9, "升级套餐"),
    CASH_DEPOSIT(10, "保证金充值");

    private Integer type;
    private String desc;

    @ThriftEnumValue
    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    AccountTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
