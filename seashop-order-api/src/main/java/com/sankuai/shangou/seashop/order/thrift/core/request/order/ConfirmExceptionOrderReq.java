package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "异常订单确认退款入参")
public class ConfirmExceptionOrderReq extends BaseParamReq {

    @FieldDoc(description = "登录用户")
    private UserDto user;
    @FieldDoc(description = "异常订单id")
    private Long exceptionOrderId;


}
