package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WeChatTokenResponse {
    @FieldDoc(description = "access_token")
    private String accessToken;

    @FieldDoc(description = "expires_in")
    private int expiresIn;

    @FieldDoc(description = "errcode")
    private int errcode;

    @FieldDoc(description = "errmsg")
    private String errmsg;
}
