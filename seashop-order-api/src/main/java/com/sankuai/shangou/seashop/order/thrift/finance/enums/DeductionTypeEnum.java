package com.sankuai.shangou.seashop.order.thrift.finance.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@ThriftEnum
public enum DeductionTypeEnum {

    /**
     * /// <summary>
     * /// 无
     * /// </summary>
     * [Description("无")]
     * Normal = 0,
     * <p>
     * /// <summary>
     * /// 罚款
     * /// </summary>
     * [Description("罚款")]
     * Fine = 1,
     * <p>
     * /// <summary>
     * /// 代收代付
     * /// </summary>
     * [Description("代收代付")]
     * ColAndPay = 2
     */

    NORMAL(0, "无"),
    FINE(1, "罚款"),
    COL_AND_PAY(2, "代收代付");


    private Integer type;
    private String desc;

    @ThriftEnumValue
    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static DeductionTypeEnum getByType(Integer type) {
        for (DeductionTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    DeductionTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
