package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/21 18:57
 */
@Data
@ToString
@TypeDoc(description = "编辑投诉维权入参")
public class StartComplaintReq extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    @PrimaryField
    private Long id;

    @FieldDoc(description = "订单id")
    @ExaminField(description = "订单id")
    private String orderId;

    @FieldDoc(description = "审核状态(1:等待供应商处理,2:供应商处理完成,3:等待平台介入,4:已结束)")
    @ExaminField(description = "审核状态(1:等待供应商处理,2:供应商处理完成,3:等待平台介入,4:已结束)")
    private Integer status;

    @FieldDoc(description = "商家联系方式")
    @ExaminField(description = "商家联系方式")
    private String userPhone;

    @FieldDoc(description = "投诉日期")
    @ExaminField(description = "投诉日期")
    private Date complaintDate;

    @FieldDoc(description = "投诉原因")
    @ExaminField(description = "投诉原因")
    private String complaintReason;

    @FieldDoc(description = "店铺反馈信息")
    @ExaminField(description = "店铺反馈信息")
    private String sellerReply;

    @FieldDoc(description = "投诉备注")
    @ExaminField(description = "投诉备注")
    private String platRemark;

    @FieldDoc(description = "创建时间")
    private Date createTime;

    @FieldDoc(description = "修改时间")
    private Date updateTime;

    public void checkParameter() {
        if (orderId == null) {
            throw new InvalidParamException("订单ID不能为空");
        }
        if (userPhone == null) {
            throw new InvalidParamException("联系电话不能为空");
        }
        if (complaintReason == null) {
            throw new InvalidParamException("投诉原因不能为空");
        }
    }


    public Long getComplaintDateLong() {
        return this.date2Long(this.complaintDate);
    }


    public void setComplaintDateLong(Long complaintDate) {
        this.complaintDate = this.long2Date(complaintDate);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }
}
