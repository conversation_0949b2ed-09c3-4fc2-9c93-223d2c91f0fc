package com.sankuai.shangou.seashop.order.thrift.core.enums.event;

/**
 * <AUTHOR>
 */
public enum RefundEventEnum {

    // 用户申请
    APPLY("用户申请"),
    // 重新申请
    REAPPLY("重新申请"),
    // 用户取消
    CANCEL("用户取消"),
    // 平台通过审核，需要发起退款
    PLATFORM_CONFIRM("平台通过审核"),
    // 平台拒绝，需要推送ERP
    PLATFORM_REJECT("平台拒绝"),
    // 供应商审核
    SELLER_PASS("供应商审核通过"),
    // 供应商拒绝
    SELLER_REJECT("供应商拒绝"),
    // 供应商确认收货
    SELLER_CONFIRM_RECEIVE("供应商确认收货"),
    // 买家寄货
    USER_DELIVER("买家寄货"),
    // 售后成功，即退款成功，有退款中变为退款成功
    REFUND_SUCCESS("售后成功"),
    // 这个事件用于发通知
    WAIT_BUYER_SEND("待买家寄货"),

    ;

    private final String desc;

    RefundEventEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
