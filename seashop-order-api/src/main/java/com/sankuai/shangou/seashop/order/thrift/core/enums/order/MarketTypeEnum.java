package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname MarketTypeEnum
 * Description //TODO
 * @date 2024/11/1 09:16
 */
@Getter
public enum MarketTypeEnum {
    //2
//    INTEGRAL_REDEEM(1L << 1, "积分商场兑换"),
    //4
//    SEC_KILL(1L << 2, "秒杀"),
    //8
    GROUP(1L << 3, "组合购"),
    //16
    FLASH_SALE(1L << 4, "限时购"),
    //32
//    UPGRADE_GIFT(1L << 5, "会员等级礼品兑换"),
    //64
//    VIP_PRICE(1L << 6, "会员价"),
    //128
    LADDER_PRICE(1L << 7, "阶梯价"),
    //256
    DISCOUNT(1L << 8, "折扣"),
    //512
    FULL_DEDUCTION(1L << 9, "满减"),
    //1024
    COUPON(1L << 10, "优惠券"),
    //2048
//    INTEGRAL_DISCOUNT(1L << 11, "积分抵扣"),

    //4096
//    PAID_PROMOTION(1L << 12, "支付有礼-领取"),
//
//    //8192
//    REBATE(1L << 13, "第二件半价"),
//
//    //16384
//    NEWCOMER_GIFT(1L << 14, "新人礼包"),
//    //32768
//    BARGAIN(1L << 15, "砍价"),
//
//    //65536
//    ADD_ON_ITEM(1L << 16, "加价购"),
//
//    //131072
//    CYCLE_RESERVE(1L << 17, "周期购"),
//
//    //262144
//    GROUPON(1L << 18, "拼团"),
    ;

    private Long type;

    private String desc;

    MarketTypeEnum(Long type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getDescription() {
        return desc;
    }

    /**
     * 获取不包含指定类型的所有类型
     *
     * @param excludeTypes 需排除的类型
     * @return
     */
    public static Long getTypes(Long excludeTypes) {
        if (excludeTypes == null) {
            excludeTypes = 0L;
        }
        final long flag = excludeTypes;
        MarketTypeEnum[] values = MarketTypeEnum.values();
        Long marketTypes = Arrays.stream(values)
                .filter(typeEnum -> (typeEnum.getType() & flag) < 1)
                .map(MarketTypeEnum::getType)
                .reduce(Long::sum).orElse(0L);
        return marketTypes;
    }

    /**
     * 获取匹配的类型
     *
     * @param marketTypes
     * @return
     */
    public static List<MarketTypeEnum> getByType(Long marketTypes) {
        if (marketTypes == null) {
            return null;
        }
        MarketTypeEnum[] values = MarketTypeEnum.values();
        List<MarketTypeEnum> result = Arrays.stream(values)
                .filter(typeEnum -> (typeEnum.getType() & marketTypes) > 1)
                .collect(Collectors.toList());
        return result;
    }
}
