package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "erp查询退款请求参数")
public class ErpQueryRefundReq extends BaseParamReq {

    @FieldDoc(name = "shopId",
            description = "店铺ID",
            requiredness = Requiredness.OPTIONAL)
    private Long shopId;

    @FieldDoc(name = "orderIds",
            description = "订单编号列表",
            requiredness = Requiredness.OPTIONAL)
    private List<String> orderIds;

    @FieldDoc(name = "refundId",
            description = "售后单编号",
            requiredness = Requiredness.OPTIONAL)
    private Long refundId;

    @FieldDoc(name = "sourceRefundId",
            description = "三方售后单号",
            requiredness = Requiredness.OPTIONAL)
    private String sourceRefundId;

    @Override
    public void checkParameter() {
        if (orderIds == null || orderIds.isEmpty() || orderIds.size() > 200) {
            throw new IllegalArgumentException("订单编号列表不能为空，且不能超过200个");
        }
        AssertUtil.throwIfTrue(refundId == null && StrUtil.isBlank(sourceRefundId) && CollectionUtil.isEmpty(orderIds), "退款查询参数不能全为空");
    }


}
