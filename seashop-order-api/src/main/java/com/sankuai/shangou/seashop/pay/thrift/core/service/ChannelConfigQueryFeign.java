package com.sankuai.shangou.seashop.pay.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.pay.thrift.core.dto.AdaPayConfigModelDto;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ChannelConfigQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.ChannelConfigListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@FeignClient(name = "himall-order", contextId = "ChannelConfigQueryFeign", path = "/himall-order/channelConfig", url = "${himall-order.dev.url:}")
public interface ChannelConfigQueryFeign {


    /**
     * 通过条件查询支付渠道配置信息
     *
     */
    @PostMapping(value = "/queryList", consumes = "application/json")
    ResultDto<ChannelConfigListResp> queryList(@RequestBody ChannelConfigQueryReq request) throws TException;

    @GetMapping(value = "/getAdaPayConfig")
    ResultDto<AdaPayConfigModelDto> getAdaPayConfigModel();

}
