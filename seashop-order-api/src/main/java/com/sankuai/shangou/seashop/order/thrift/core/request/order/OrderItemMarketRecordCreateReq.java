package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description: 订单子项营销记录 新增入参对象
 * @Author: chenpeng
 * @since: 2024-09-01 10:50:00
 */

@Data
@Schema(name = "ItemMarketRecordCreateReq", description = "订单子项营销记录 新增入参对象")
public class OrderItemMarketRecordCreateReq {

    @Schema(name = "orderId", description = "订单ID")
    private Long orderId;

    @Schema(name = "orderItemId", description = "订单子项ID")
    private Long orderItemId;

    @Schema(name = "marketId", description = "营销活动ID")
    private Long marketId;

    @Schema(name = "marketType", description = "营销类型")
    private Long marketType;

    @Schema(name = "marketFlag", description = "优惠标记：例如：优惠券（0代表商家券，1代表商家红包）")
    private Integer marketFlag;

    @Schema(name = "discountAmount", description = "优惠金额")
    private BigDecimal discountAmount;

    @Schema(name = "marketName", description = "营销名称")
    private String marketName;

    @Schema(name = "token", description = "营销代币量（例如积分抵扣对应的积分量，或用于其它代币量）")
    private BigDecimal token;
}
