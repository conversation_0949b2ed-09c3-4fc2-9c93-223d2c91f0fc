package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "确认请求入参")
public class ConfirmReceiveReq extends BaseParamReq {

    @FieldDoc(description = "用户", requiredness = Requiredness.REQUIRED)
    private UserDto user;
    @FieldDoc(description = "订单号", requiredness = Requiredness.REQUIRED)
    private String orderId;

    @Override
    public void checkParameter() {
        if (this.user == null) {
            throw new InvalidParamException("user不能为空");
        }
        if (orderId == null) {
            throw new InvalidParamException("orderId不能为空");
        }
    }


}
