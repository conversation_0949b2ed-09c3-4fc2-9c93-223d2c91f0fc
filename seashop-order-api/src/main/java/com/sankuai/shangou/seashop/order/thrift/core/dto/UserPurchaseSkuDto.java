package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "用户SKU采购数据")
public class UserPurchaseSkuDto extends BaseThriftDto {

    /**
     * 订单ID
     */
    @FieldDoc(description = "订单ID")
    private String orderId;
    /**
     * 订单状态
     */
    @FieldDoc(description = "订单状态")
    private Integer orderStatus;
    /**
     * 订单状态描述
     */
    @FieldDoc(description = "订单状态描述")
    private String orderStatusDesc;
    /**
     * 明细ID
     */
    @FieldDoc(description = "明细ID")
    private Long itemId;
    /**
     * 商品名称
     */
    @FieldDoc(description = "商品名称")
    private String productName;
    /**
     * 商品ID
     */
    @FieldDoc(description = "商品ID")
    private String productId;
    /**
     * skuId
     */
    @FieldDoc(description = "skuId")
    private String skuId;
    /**
     * sku描述
     */
    @FieldDoc(description = "sku描述")
    private List<String> skuDescList;
    /**
     * sku采购数量
     */
    @FieldDoc(description = "sku采购数量")
    private Long quantity;
    /**
     * sku采购金额
     */
    @FieldDoc(description = "sku采购金额")
    private BigDecimal realTotalPrice;
    /**
     * skuId
     */
    @FieldDoc(description = "sku自增ID，内部使用")
    private Long skuAutoId;


    public String getRealTotalPriceString() {
        return this.bigDecimal2String(this.realTotalPrice);
    }


    public void setRealTotalPriceString(String realTotalPrice) {
        this.realTotalPrice = this.string2BigDecimal(realTotalPrice);
    }


}
