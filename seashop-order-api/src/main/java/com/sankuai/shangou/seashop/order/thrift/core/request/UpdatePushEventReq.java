package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.ThirdEvent;
import lombok.Data;

/**
 * <AUTHOR>
 */

@TypeDoc(description = "推送事件修改请求")
@Data
public class UpdatePushEventReq extends BaseParamReq {
    /**
     * 推送编号 可以为订单id或者 productId
     */
    @FieldDoc(description = "推送编号 可以为订单id或者 productId")
    private String sendCode;
    /**
     * 事件类型 0:订单事件 1:商品事件 2:退货单
     */
    @FieldDoc(description = "事件类型 0:订单事件 1:商品事件 2:退货单")
    private ThirdEvent.EventTypeEnum eventType;
    /**
     * 发送目标 1：旺店通 2：聚水潭 3：网店管家 4：吉客云
     */
    @FieldDoc(description = "发送目标 1：旺店通 2：聚水潭 3：网店管家 4：吉客云")
    private ThirdEvent.SendTargetEnum sendTarget;
    /**
     * 推送状态 0:待推送 1：推送成功 2：推送失败，3：推送中
     */
    @FieldDoc(description = "推送状态 0:待推送 1：推送成功 2：推送失败，3：推送中")
    private ThirdEvent.SendStateEnum sendState;
    /**
     * 事件body
     */
    @FieldDoc(description = "事件body")
    private String eventBody;
    /**
     * 推送返回的错误消息
     */
    @FieldDoc(description = "推送返回的错误消息")
    private String sendMsg;

    /**
     * 存在 则表示修改
     */
    @FieldDoc(description = "数据主键，存在表示修改")
    private Long id;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(sendCode, "推送编号不能为空");
        AssertUtil.throwInvalidParamIfNull(sendState, "推送状态不能为空");
        AssertUtil.throwInvalidParamIfNull(eventType, "事件类型不能为空");
        AssertUtil.throwInvalidParamIfNull(sendTarget, "发送目标不能为空");

        if (ThirdEvent.SendStateEnum.SEND_FAIL.equals(sendState)) {
            AssertUtil.throwInvalidParamIfNull(sendMsg, "推送状态为成功或失败时，错误消息不能为空");
        }
    }


}
