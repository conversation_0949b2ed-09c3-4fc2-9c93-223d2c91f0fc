package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "订单统计返回值列表")
public class OrderStatisticsListResp {

    /**
     * .net 原始数据格式
     * {
     *     "success": true,
     *     "chart": {
     *         "XAxisData": [
     *             "11/28",
     *             "11/29",
     *             "11/30",
     *             "12/01",
     *             "12/02",
     *             "12/03",
     *             "12/04"
     *         ],
     *         "SeriesData": [
     *             {
     *                 "Name": "交易额走势图",
     *                 "Data": [
     *                     99.23,
     *                     0.03,
     *                     0,
     *                     0,
     *                     0,
     *                     0,
     *                     0
     *                 ]
     *             }
     *         ],
     *         "ExpandProp": [
     *             "2023/11/28 0:00:00的销售额为:99.23",
     *             "2023/11/29 0:00:00的销售额为:0.03",
     *             "2023/11/30 0:00:00的销售额为:0",
     *             "2023/12/1 0:00:00的销售额为:0",
     *             "2023/12/2 0:00:00的销售额为:0",
     *             "2023/12/3 0:00:00的销售额为:0",
     *             "2023/12/4 0:00:00的销售额为:0"
     *         ]
     *     }
     * }
     */

    @FieldDoc(description = "订单统计返回值列表")
    private List<OrderStatisticsDataResp> list;


}
