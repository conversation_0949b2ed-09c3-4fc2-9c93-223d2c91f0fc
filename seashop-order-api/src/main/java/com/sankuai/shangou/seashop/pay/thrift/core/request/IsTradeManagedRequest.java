package com.sankuai.shangou.seashop.pay.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 查询小程序是否已开通发货管理服务
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IsTradeManagedRequest extends BaseRequest {
    @FieldDoc(description = "appid")
    private String appid; // 待查询小程序的AppID
}
