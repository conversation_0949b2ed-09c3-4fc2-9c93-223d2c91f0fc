package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/04 16:24
 */
@Data
@ToString
@TypeDoc(description = "商品评价图片对象")
public class ProductCommentImageResp extends BaseThriftDto {

    @FieldDoc(description = "评论图片")
    private String commentImage;

}
