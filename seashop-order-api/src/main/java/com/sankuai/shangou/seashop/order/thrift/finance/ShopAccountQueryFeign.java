package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description: 店铺结算信息查询相关接口
 */
@FeignClient(name = "himall-order", contextId = "ShopAccountQueryFeign", path = "/himall-order/finance/shopAccountQuery", url = "${himall-order.dev.url:}")
public interface ShopAccountQueryFeign {


    /**
     * 查询店铺结算信息
     */
    @PostMapping(value = "getShopAccountByShopId", consumes = "application/json")
    ResultDto<String> getShopAccountByShopId(@RequestParam("shopId") Long shopId) throws TException;
}
