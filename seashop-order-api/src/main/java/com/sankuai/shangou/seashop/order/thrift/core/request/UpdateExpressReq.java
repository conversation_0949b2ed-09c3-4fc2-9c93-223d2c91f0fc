package com.sankuai.shangou.seashop.order.thrift.core.request;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderExpressDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "修改运单号请求入参")
public class UpdateExpressReq extends BaseParamReq {

    @FieldDoc(description = "订单号", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;
    @FieldDoc(description = "是否需要物流", requiredness = Requiredness.REQUIRED)
    private Boolean needExpress;
    @FieldDoc(description = "物流列表", requiredness = Requiredness.REQUIRED)
    private List<OrderExpressDto> expressList;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(orderId)) {
            throw new InvalidParamException("订单号不能为空");
        }
        if (shopId == null) {
            throw new InvalidParamException("shopId不能为空");
        }
        if (needExpress == null) {
            throw new InvalidParamException("是否需要物流不能为空");
        }
        if (Boolean.TRUE.equals(needExpress)) {
            if (CollUtil.isEmpty(expressList)) {
                throw new InvalidParamException("需要物流时，物流列表不能为空");
            }
            for (OrderExpressDto orderExpressDto : expressList) {
                orderExpressDto.checkParameter();
            }
        }
    }


}
