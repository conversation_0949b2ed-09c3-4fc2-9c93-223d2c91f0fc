package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@TypeDoc(description = "已结算明细列表查询返回参数")
@ToString
@Data
public class SettledItemResp extends BaseThriftDto {

    @FieldDoc(description = "订单ID")
    private String orderId;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "结算时间")
    private Date settleTime;

    @FieldDoc(description = "结算金额")
    private BigDecimal settleAmount;

    @FieldDoc(description = "渠道金额（手续费）")
    private BigDecimal channelAmount;

    @FieldDoc(description = "订单金额")
    private BigDecimal orderAmount;

    @FieldDoc(description = "佣金金额")
    private BigDecimal commissionAmount;

    @FieldDoc(description = "退款金额")
    private BigDecimal refundAmount;

    @FieldDoc(description = "付款时间")
    private Date payTime;

    @FieldDoc(description = "订单完成时间")
    private Date orderFinishTime;

    @FieldDoc(description = "支付方式：1：支付宝扫码；2：支付宝H5；3：微信小程序；4：微信H5；5：企业网银；6：个人网银")
    private Integer paymentType;

    @FieldDoc(description = "支付方式名称")
    private String paymentTypeName;


    public Long getSettleTimeLong() {
        return this.date2Long(this.settleTime);
    }


    public void setSettleTimeLong(Long settleTime) {
        this.settleTime = this.long2Date(settleTime);
    }


    public String getSettleAmountString() {
        return this.bigDecimal2String(this.settleAmount);
    }


    public void setSettleAmountString(String settleAmount) {
        this.settleAmount = this.string2BigDecimal(settleAmount);
    }


    public String getChannelAmountString() {
        return this.bigDecimal2String(this.channelAmount);
    }


    public void setChannelAmountString(String channelAmount) {
        this.channelAmount = this.string2BigDecimal(channelAmount);
    }


    public String getOrderAmountString() {
        return this.bigDecimal2String(this.orderAmount);
    }


    public void setOrderAmountString(String orderAmount) {
        this.orderAmount = this.string2BigDecimal(orderAmount);
    }


    public String getCommissionAmountString() {
        return this.bigDecimal2String(this.commissionAmount);
    }


    public void setCommissionAmountString(String commissionAmount) {
        this.commissionAmount = this.string2BigDecimal(commissionAmount);
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


    public Long getPayTimeLong() {
        return this.date2Long(this.payTime);
    }


    public void setPayTimeLong(Long payTime) {
        this.payTime = this.long2Date(payTime);
    }


    public Long getOrderFinishTimeLong() {
        return this.date2Long(this.orderFinishTime);
    }


    public void setOrderFinishTimeLong(Long orderFinishTime) {
        this.orderFinishTime = this.long2Date(orderFinishTime);
    }


}
