package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 订单附加信息：留言、优惠券、发票等
 * <AUTHOR>
 */
@TypeDoc(description = "订单附加信息")
@ToString
@Data
public class OrderAdditionalDto extends BaseThriftDto {

    @FieldDoc(description = "配送方式。目前是固定值。1：快递配送")
    private Integer deliveryType;
    @FieldDoc(description = "优惠券ID")
    private Long couponId;
    @FieldDoc(description = "优惠券金额")
    private BigDecimal couponAmount;
    @FieldDoc(description = "折扣总金额")
    private BigDecimal discountAmount;
    @FieldDoc(description = "满减总金额")
    private BigDecimal reductionAmount;
    @FieldDoc(description = "运费")
    private BigDecimal freightAmount;
    @FieldDoc(description = "用户备注")
    private String remark;
    @FieldDoc(description = "税费")
    private BigDecimal taxAmount;
    @FieldDoc(description = "税率")
    private BigDecimal taxRate;
    @FieldDoc(description = "发票信息")
    private InvoiceDto invoice;
    @FieldDoc(description = "满减条件金额")
    private BigDecimal reductionConditionAmount;
    /**
     * 用户领用优惠券的记录ID
     */
    @FieldDoc(description = "用户领用优惠券的记录ID")
    private Long couponRecordId;
    /**
     * 满减活动ID
     */
    @FieldDoc(description = "满减活动ID")
    private Long reductionActivityId;

    /**
     * 第三方订单号(美团订单号)
     */
    @FieldDoc(description = "第三方订单号(美团订单号)")
    private String sourceOrderId;


    public String getCouponAmountString() {
        return this.bigDecimal2String(this.couponAmount);
    }


    public void setCouponAmountString(String couponAmount) {
        this.couponAmount = this.string2BigDecimal(couponAmount);
    }


    public String getDiscountAmountString() {
        return this.bigDecimal2String(this.discountAmount);
    }


    public void setDiscountAmountString(String discountAmount) {
        this.discountAmount = this.string2BigDecimal(discountAmount);
    }


    public String getReductionAmountString() {
        return this.bigDecimal2String(this.reductionAmount);
    }


    public void setReductionAmountString(String reductionAmount) {
        this.reductionAmount = this.string2BigDecimal(reductionAmount);
    }


    public String getFreightAmountString() {
        return this.bigDecimal2String(this.freightAmount);
    }


    public void setFreightAmountString(String freightAmount) {
        this.freightAmount = this.string2BigDecimal(freightAmount);
    }


    public String getTaxAmountString() {
        return this.bigDecimal2String(this.taxAmount);
    }


    public void setTaxAmountString(String taxAmount) {
        this.taxAmount = this.string2BigDecimal(taxAmount);
    }


    public String getTaxRateString() {
        return this.bigDecimal2String(this.taxRate);
    }


    public void setTaxRateString(String taxRate) {
        this.taxRate = this.string2BigDecimal(taxRate);
    }


    public String getReductionConditionAmountString() {
        return this.bigDecimal2String(this.reductionConditionAmount);
    }


    public void setReductionConditionAmountString(String reductionConditionAmount) {
        this.reductionConditionAmount = this.string2BigDecimal(reductionConditionAmount);
    }


}
