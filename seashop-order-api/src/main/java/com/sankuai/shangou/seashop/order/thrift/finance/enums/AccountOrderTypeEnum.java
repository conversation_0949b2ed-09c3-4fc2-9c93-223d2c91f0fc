package com.sankuai.shangou.seashop.order.thrift.finance.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/12/21/021
 * @description:
 */
@ThriftEnum
public enum AccountOrderTypeEnum {

    RETURN_ORDER(0, "退单列表"),
    FINISHED_ORDER(1, "订单列表");

    private Integer type;
    private String desc;

    @ThriftEnumValue
    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    AccountOrderTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
