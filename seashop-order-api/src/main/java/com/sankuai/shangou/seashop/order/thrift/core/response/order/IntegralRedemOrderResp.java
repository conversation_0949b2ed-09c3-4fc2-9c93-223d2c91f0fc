package com.sankuai.shangou.seashop.order.thrift.core.response.order;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class IntegralRedemOrderResp {

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "总金额")
    private BigDecimal totalAmount;

    @Schema(description = "用户名")
    private String userName;

    @Schema(description = "订单创建时间")
    private Date orderDate;

    @Schema(description = "订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private Integer orderStatus;

    @Schema(description = "活动商品数量")
    private Integer totalQty;


}
