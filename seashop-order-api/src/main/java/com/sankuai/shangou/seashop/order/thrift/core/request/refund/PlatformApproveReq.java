package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "平台审核参数")
public class PlatformApproveReq extends BaseParamReq {

    @FieldDoc(description = "退款单号", requiredness = Requiredness.REQUIRED)
    private Long refundId;
    @FieldDoc(description = "平台备注")
    private String remark;
    /**
     * 登录用户信息
     */
    @FieldDoc(description = "登录用户信息")
    private UserDto user;


    @Override
    public void checkParameter() {
        if (refundId == null) {
            throw new InvalidParamException("退款单号不能为空");
        }
        if (user == null) {
            throw new InvalidParamException("登录用户信息不能为空");
        }
    }


}
