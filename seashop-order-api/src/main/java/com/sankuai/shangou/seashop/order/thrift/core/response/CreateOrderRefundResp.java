package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TypeDoc(description = "创建售后单入参")
public class CreateOrderRefundResp extends BaseThriftDto {
    /**
     * 售后单id
     */
    @FieldDoc(
            name = "refundId",
            description = "售后单id",
            requiredness = Requiredness.REQUIRED
    )
    private Long refundId;
    /**
     * 订单id
     */
    @FieldDoc(
            name = "orderId",
            description = "订单id",
            requiredness = Requiredness.REQUIRED
    )
    private String orderId;
    /**
     * 牵牛花订单ID
     */
    @FieldDoc(
            name = "sourceOrderId",
            description = "牵牛花订单ID",
            requiredness = Requiredness.REQUIRED
    )
    private String sourceOrderId;
    /**
     * 退款金额
     */
    @Setter
    @Getter
    @FieldDoc(
            name = "refundAmount",
            description = "退款金额",
            requiredness = Requiredness.REQUIRED
    )
    private BigDecimal refundAmount;
    /**
     * 申请日期
     */
    @Setter
    @Getter
    @FieldDoc(
            name = "applyDate",
            description = "申请日期",
            requiredness = Requiredness.REQUIRED
    )
    private Date applyDate;
    /**
     * 退款状态
     */
    @FieldDoc(
            name = "status",
            description = "退款状态",
            requiredness = Requiredness.REQUIRED
    )
    private RefundStatusEnum status;


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


    public Long getApplyDateLong() {
        return this.date2Long(this.applyDate);
    }


    public void setApplyDateLong(Long applyDate) {
        this.applyDate = this.long2Date(applyDate);
    }


}
