package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ExcessPaymentRefundReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2024/1/8/008
 * @description: 订单支付后，修改了订单金额，导致超支，需要退款的场景
 */
@FeignClient(name = "himall-order", contextId = "OrderRefundRecordCmdFeign", path = "/himall-order/orderRefundRecord", url = "${himall-order.dev.url:}")
public interface OrderRefundRecordCmdFeign {

    /**
     * 订单进行超支退款
     */
    @PostMapping(value = "/excessPaymentRefund", consumes = "application/json")
    ResultDto<BaseResp> excessPaymentRefund(@RequestBody ExcessPaymentRefundReq request) throws TException;
}
