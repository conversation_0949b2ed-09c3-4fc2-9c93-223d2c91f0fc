package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@TypeDoc(description = "保证金支付明细")
@ToString
@Data
public class CashDepositDetailResp extends BaseThriftDto {

    /**
     * 主键
     */
    @FieldDoc(description = "主键")
    private Long id;

    /**
     * 保证金表id
     */
    @FieldDoc(description = "保证金表id")
    private Long cashDepositId;

    /**
     * 时间
     */
    @FieldDoc(description = "时间")
    private Date addDate;

    /**
     * 金额
     */
    @FieldDoc(description = "金额")
    private BigDecimal balance;

    /**
     * 操作人
     */
    @FieldDoc(description = "操作人")
    private String operator;

    /**
     * 说明
     */
    @FieldDoc(description = "说明")
    private String description;

    /**
     * 充值类型（银联、支付宝之类的）
     */
    @FieldDoc(description = "充值类型（银联、支付宝之类的）")
    private Integer rechargeWay;

    /**
     * 类型；1，付款；2，扣款；3，退款
     */
    @FieldDoc(description = "类型；1，付款；2，扣款；3，退款")
    private Integer operatorType;

    /**
     * 渠道支付单号
     */
    @FieldDoc(description = "渠道支付单号")
    private String channelOrderId;

    /**
     * 平台扣款金额
     */
    @FieldDoc(description = "平台扣款金额")
    private BigDecimal platformDeduction;

    /**
     * 冻结金额
     */
    @FieldDoc(description = "冻结金额")
    private BigDecimal forzenAmount;

    /**
     * 退款金额
     */
    @FieldDoc(description = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 是否有退款记录
     */
    @FieldDoc(description = "是否有退款记录")
    private Boolean flagRefund;

    @FieldDoc(description = "扣款类型，1：罚款；2：代收代付")
    private Integer deductionType;


    public Long getAddDateLong() {
        return this.date2Long(this.addDate);
    }


    public void setAddDateLong(Long addDate) {
        this.addDate = this.long2Date(addDate);
    }


    public String getBalanceString() {
        return this.bigDecimal2String(this.balance);
    }


    public void setBalanceString(String balance) {
        this.balance = this.string2BigDecimal(balance);
    }


    public String getPlatformDeductionString() {
        return this.bigDecimal2String(this.platformDeduction);
    }


    public void setPlatformDeductionString(String platformDeduction) {
        this.platformDeduction = this.string2BigDecimal(platformDeduction);
    }


    public String getForzenAmountString() {
        return this.bigDecimal2String(this.forzenAmount);
    }


    public void setForzenAmountString(String forzenAmount) {
        this.forzenAmount = this.string2BigDecimal(forzenAmount);
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


}
