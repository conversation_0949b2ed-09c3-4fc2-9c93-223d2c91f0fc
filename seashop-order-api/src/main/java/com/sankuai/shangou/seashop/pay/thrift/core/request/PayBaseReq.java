package com.sankuai.shangou.seashop.pay.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/12/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "支付请求基类")
public class PayBaseReq extends BaseParamReq {

    @FieldDoc(description = "支付渠道:1汇付天下支付", requiredness = Requiredness.REQUIRED)
    private Integer paymentChannel;

    @FieldDoc(description = "汇付支付 app_id")
    private String appId;

    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private String memberId;

    @Override
    public void checkParameter() {
        if (this.paymentChannel == null) {
            throw new InvalidParamException("paymentChannel不能为空");
        }
        if (StrUtil.isBlank(this.memberId)) {
            throw new InvalidParamException("memberId不能为空");
        }
        if (PaymentChannelEnum.getByCode(this.paymentChannel) == null) {
            throw new InvalidParamException("paymentChannel不合法");
        }
    }


}
