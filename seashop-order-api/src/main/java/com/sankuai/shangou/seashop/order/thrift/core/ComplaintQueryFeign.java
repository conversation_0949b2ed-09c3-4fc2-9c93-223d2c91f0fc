package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryMComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryMallComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderRightsReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QuerySellerComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintRespPage;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderRightsResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description：投诉维权查询类接口
 * @author： liweisong
 * @create： 2023/11/21 18:03
 */

@FeignClient(name = "himall-order", contextId = "ComplaintQueryFeign", path = "/himall-order/complaintQuery", url = "${himall-order.dev.url:}")
public interface ComplaintQueryFeign {

    /**
     * 平台-交易-交易投诉-申请记录分页查询
     */
    @PostMapping(value = "/pageMComplaint", consumes = "application/json")
    ResultDto<QueryOrderComplaintRespPage> pageMComplaint(@RequestBody QueryMComplaintReq queryMComplaintReq) throws TException;

    /**
     * 供应商-交易-交易投诉-申请记录分页查询
     */
    @PostMapping(value = "/pageSellerComplaint", consumes = "application/json")
    ResultDto<BasePageResp<QueryOrderComplaintResp>> pageSellerComplaint(@RequestBody QuerySellerComplaintReq querySellerComplaintReq) throws TException;

    /**
     * 商家-投诉维权-申请记录分页查询
     */
    @PostMapping(value = "/pageMallComplaint", consumes = "application/json")
    ResultDto<BasePageResp<QueryOrderComplaintResp>> pageMallComplaint(@RequestBody QueryMallComplaintReq queryMallComplaintReq) throws TException;

    /**
     * 商家-投诉维权-投诉维权分页查询
     */
    @PostMapping(value = "/pageQueryOrderRights", consumes = "application/json")
    ResultDto<BasePageResp<QueryOrderRightsResp>> pageQueryOrderRights(@RequestBody QueryOrderRightsReq queryOrderRightsReq) throws TException;
}
