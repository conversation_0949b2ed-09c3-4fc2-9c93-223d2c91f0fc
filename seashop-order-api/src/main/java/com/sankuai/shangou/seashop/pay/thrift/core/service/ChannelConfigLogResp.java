package com.sankuai.shangou.seashop.pay.thrift.core.service;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/17 10:29
 */
@Data
@TypeDoc(description = "保存渠道配置请求对象日志用")
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class ChannelConfigLogResp extends BaseParamReq {

    @FieldDoc(description = "汇付天下中的AppId")
    @ExaminField(description = "汇付天下中的AppId")
    private String appId;

    @FieldDoc(description = "汇付天下中的ApiMockKey")
    @ExaminField(description = "汇付天下中的ApiMockKey")
    private String apiMockKey;

    @FieldDoc(description = "汇付天下中的ApiKey")
    @ExaminField(description = "汇付天下中的ApiKey")
    private String apiKey;

    @FieldDoc(description = "汇付天下中的AdapayRSAKey")
    @ExaminField(description = "汇付天下中的AdapayRSAKey")
    private String rsaProductKey;

    @FieldDoc(description = "汇付天下中的PrivateKey")
    @ExaminField(description = "汇付天下中的PrivateKey")
    private String rsaPrivateKey;


}
