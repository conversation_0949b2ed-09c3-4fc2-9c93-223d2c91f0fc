package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@TypeDoc(description = "结算账号相关信息")
@ToString
@Data
public class FinanceIndexResp extends BaseThriftDto {

    @FieldDoc(description = "待结算金额")
    private BigDecimal pendingSettlement;

    @FieldDoc(description = "已结算金额")
    private BigDecimal settled;


    public String getPendingSettlementString() {
        return this.bigDecimal2String(this.pendingSettlement);
    }


    public void setPendingSettlementString(String pendingSettlement) {
        this.pendingSettlement = this.string2BigDecimal(pendingSettlement);
    }


    public String getSettledString() {
        return this.bigDecimal2String(this.settled);
    }


    public void setSettledString(String settled) {
        this.settled = this.string2BigDecimal(settled);
    }
}
