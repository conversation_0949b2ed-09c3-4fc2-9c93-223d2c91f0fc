package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositPayReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositPayResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 14:09
 * 保证金支付表查询服务相关操作
 */
@FeignClient(name = "himall-order", contextId = "CashDepositPayFeign", path = "/himall-order/finance/cashDepositPay", url = "${himall-order.dev.url:}")
public interface CashDepositPayFeign {

    /**
     * 保证金支付表查询
     */
    @PostMapping(value = "selectCashDepositPay", consumes = "application/json")
    ResultDto<List<CashDepositPayResp>> selectCashDepositPay(@RequestBody CashDepositPayReq req) throws TException;
}
