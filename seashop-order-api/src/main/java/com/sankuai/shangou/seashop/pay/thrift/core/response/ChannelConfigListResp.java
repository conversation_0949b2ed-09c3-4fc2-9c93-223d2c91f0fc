package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.pay.thrift.core.dto.ChannelConfigInfoDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@TypeDoc(description = "支付渠道配置响应体")
@ToString
@Data
public class ChannelConfigListResp extends BaseThriftDto {

    @FieldDoc(description = "支付渠道配置列表")
    private List<ChannelConfigInfoDto> configList;


}
