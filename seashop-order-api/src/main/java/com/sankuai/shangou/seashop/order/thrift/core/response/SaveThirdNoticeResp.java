package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/01/29 17:59
 */
@Data
@ToString
@TypeDoc(description = "保存第三方通知返回值")
public class SaveThirdNoticeResp extends BaseThriftDto {

    @FieldDoc(description = "通知id")
    private Long noticeId;


}
