package com.sankuai.shangou.seashop.order.thrift.core.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商品购买数量返回结果")
public class ProductBuyCountResp {

    @FieldDoc(description = "商品ID")
    private Long productId;
    @FieldDoc(description = "购买数量")
    private Long quantity;


}
