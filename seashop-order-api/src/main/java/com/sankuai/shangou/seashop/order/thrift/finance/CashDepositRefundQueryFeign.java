package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description: 保证金退款相关查询服务
 */
@FeignClient(name = "himall-order", contextId = "CashDepositRefundQueryFeign", path = "/himall-order/finance/cashDepositRefund", url = "${himall-order.dev.url:}")
public interface CashDepositRefundQueryFeign {

    /**
     * 通过条件查询保证金退款明细信息
     */
    @PostMapping(value = "refundList", consumes = "application/json")
    ResultDto<BasePageResp<CashDepositRefundResp>> refundList(@RequestBody CashDepositRefundQueryReq request) throws TException;

    /**
     * 通过ID查询审核信息
     */
    @PostMapping(value = "refundDetail", consumes = "application/json")
    ResultDto<CashDepositRefundDetailResp> refundDetail(@RequestParam("id") Long id) throws TException;
}
