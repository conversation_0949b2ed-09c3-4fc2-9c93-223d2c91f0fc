package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.order.thrift.core.enums.SearchTimeTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "erp查询退款分页入参")
public class ErpQueryRefundPageReq extends BasePageReq {

    @FieldDoc(name = "shopId",
            description = "店铺ID",
            requiredness = Requiredness.OPTIONAL)
    private Long shopId;

    @FieldDoc(name = "timeType",
            description = "时间类型",
            requiredness = Requiredness.OPTIONAL)
    private SearchTimeTypeEnum timeType;

    @Setter
    @Getter
    @FieldDoc(name = "startTime",
            description = "开始时间",
            requiredness = Requiredness.OPTIONAL)
    private Date startTime;

    @Setter
    @Getter
    @FieldDoc(name = "endTime",
            description = "结束时间",
            requiredness = Requiredness.OPTIONAL)
    private Date endTime;

    @FieldDoc(name = "auditStatus",
            description = "审核类型",
            requiredness = Requiredness.OPTIONAL)
    private Integer auditStatus;

    @FieldDoc(name = "refundModes",
            description = "退款模式",
            requiredness = Requiredness.OPTIONAL)
    private List<Integer> refundModes;


    @FieldDoc(name = "userId",
            description = "用户ID",
            requiredness = Requiredness.OPTIONAL)
    private Long userId;

    @FieldDoc(name = "orderId",
            description = "订单ID",
            requiredness = Requiredness.OPTIONAL)
    private String orderId;

    @FieldDoc(name = "statusList",
            description = "退款状态(综合状态)",
            requiredness = Requiredness.OPTIONAL)
    private List<RefundStatusEnum> statusList;


    @Override
    public void checkParameter() {
        if (timeType == null) {
            this.timeType = SearchTimeTypeEnum.CREATE_TIME;
        }
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
