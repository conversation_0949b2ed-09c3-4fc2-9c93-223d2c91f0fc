package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.OrderGetSoldTradesReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.StartComplaintReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @description：电子面单相关接口
 * @author： chenpeng
 * @create： 2023/11/21 18:02
 */

@FeignClient(name = "himall-order", contextId = "WayBillApiFegin", path = "/himall-order/wayBill", url = "${himall-order.dev.url:}")
public interface HishopWayBillFegin {

    /**
     * 查询订单分页列表
     */
    @PostMapping(value = "/getSoldTrades", consumes = "application/json")
    ResultDto<BasePageResp<OrderInfoDto>> getSoldTrades(@RequestBody OrderGetSoldTradesReq param) throws TException ;

    /**
     * 查询订单增量数据列表
     */
    @PostMapping(value = "/getIncrementSoldTrades")
    ResultDto<BasePageResp<OrderInfoDto>> getIncrementSoldTrades(@RequestBody OrderGetSoldTradesReq param) throws TException ;

    /**
     * 根据订单id查询订单信息
     */
    @GetMapping(value = "/getTradeByOrderId")
    ResultDto<OrderInfoDto> getTradeByOrderId(@RequestParam String orderId) throws TException ;

    /**
     * 订单发货
     */
    @PostMapping(value = "/deliverOrder")
    ResultDto<Void> deliverOrder(@RequestBody OrderGetSoldTradesReq param) throws TException ;

}
