package com.sankuai.shangou.seashop.pay.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.AllArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description: 业务类型
 */
@AllArgsConstructor
@ThriftEnum
public enum BusinessTypeEnum {

    // 业务类型 1：订单,4：保证金

    ORDER(1, "订单"),
    BAIL(4, "保证金"),

    ;

    private Integer type;
    private String name;

    @ThriftEnumValue
    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据type获取枚举
     *
     * @param type
     * @return
     */
    public static BusinessTypeEnum getByType(Integer type) {
        for (BusinessTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

}
