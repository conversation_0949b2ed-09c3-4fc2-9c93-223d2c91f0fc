package com.sankuai.shangou.seashop.order.thrift.finance.request;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "已结算明细列表请求参数")
public class SettledItemQryReq extends BasePageReq {

    @FieldDoc(description = "结算id", requiredness = Requiredness.REQUIRED)
    private Long detailId;

    @FieldDoc(description = "店铺id", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @FieldDoc(description = "订单完成时间-开始时间")
    private Date startOrderFinishTime;

    @FieldDoc(description = "订单完成时间-结束时间")
    private Date endOrderFinishTime;

    @FieldDoc(description = "结算时间-开始时间")
    private Date startSettlementTime;

    @FieldDoc(description = "结算时间-结束时间")
    private Date endSettlementTime;

    @FieldDoc(description = "订单id")
    private String orderId;

    @FieldDoc(description = "支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银")
    private Integer paymentType;

    @FieldDoc(description = "支付时间-开始时间")
    private Date startPayTime;

    @FieldDoc(description = "支付时间-结束时间")
    private Date endPayTime;

    @FieldDoc(description = "店铺id列表")
    private List<Long> shopIdList;
    @FieldDoc(description = "店铺名称")
    private String shopName;

    @Override
    public void checkParameter() {
        if (null == this.shopId && CollUtil.isEmpty(shopIdList)) {
            throw new InvalidParamException("店铺id不能为空");
        }
    }


    public Long getStartOrderFinishTimeLong() {
        return this.date2Long(this.startOrderFinishTime);
    }


    public void setStartOrderFinishTimeLong(Long startOrderFinishTime) {
        this.startOrderFinishTime = this.long2Date(startOrderFinishTime);
    }


    public Long getEndOrderFinishTimeLong() {
        return this.date2Long(this.endOrderFinishTime);
    }


    public void setEndOrderFinishTimeLong(Long endOrderFinishTime) {
        this.endOrderFinishTime = this.long2Date(endOrderFinishTime);
    }


    public Long getStartSettlementTimeLong() {
        return this.date2Long(this.startSettlementTime);
    }


    public void setStartSettlementTimeLong(Long startSettlementTime) {
        this.startSettlementTime = this.long2Date(startSettlementTime);
    }


    public Long getEndSettlementTimeLong() {
        return this.date2Long(this.endSettlementTime);
    }


    public void setEndSettlementTimeLong(Long endSettlementTime) {
        this.endSettlementTime = this.long2Date(endSettlementTime);
    }


    public Long getStartPayTimeLong() {
        return this.date2Long(this.startPayTime);
    }


    public void setStartPayTimeLong(Long startPayTime) {
        this.startPayTime = this.long2Date(startPayTime);
    }


    public Long getEndPayTimeLong() {
        return this.date2Long(this.endPayTime);
    }


    public void setEndPayTimeLong(Long endPayTime) {
        this.endPayTime = this.long2Date(endPayTime);
    }


}
