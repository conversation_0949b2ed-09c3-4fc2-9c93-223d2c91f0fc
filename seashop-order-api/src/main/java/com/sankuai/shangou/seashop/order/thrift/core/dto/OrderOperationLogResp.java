package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "订单操作日志")
@ToString
@Data
public class OrderOperationLogResp extends BaseThriftDto {

    /**
     * 操作者
     */
    @FieldDoc(description = "操作者")
    private String operator;

    /**
     * 操作日期
     */
    @FieldDoc(description = "操作日期")
    private Date operateDate;

    /**
     * 操作内容
     */
    @FieldDoc(description = "操作内容")
    private String operateContent;


    public Long getOperateDateLong() {
        return this.date2Long(this.operateDate);
    }


    public void setOperateDateLong(Long operateDate) {
        this.operateDate = this.long2Date(operateDate);
    }


}
