package com.sankuai.shangou.seashop.order.thrift.finance.request;

import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/17 15:26
 */

@ThriftStruct
@ToString
@TypeDoc(description = "退款审批拒绝请求对象")
public class CashDepositRefundRefuseLog extends BaseParamReq {

    @FieldDoc(description = "退款单id", requiredness = Requiredness.REQUIRED)
    @PrimaryField
    private Long id;

    @FieldDoc(description = "店铺名称")
    @ExaminField(description = "店铺名称")
    @PrimaryField
    private String shopName;

    @FieldDoc(description = "拒绝原因")
    @ExaminField(description = "拒绝原因")
    private String refuseReason;

    @FieldDoc(description = "状态 0，待审核；1，已通过；2，已拒绝；3，退款处理中；")
    @ExaminField(description = "状态 0，待审核；1，已通过；2，已拒绝；3，退款处理中；")
    private Integer status;

    @FieldDoc(description = "支付撤销处理ID")
    @ExaminField(description = "支付撤销处理ID")
    private String refundOrderId;


    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }


    public String getRefuseReason() {
        return refuseReason;
    }


    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }


    public Integer getStatus() {
        return status;
    }


    public void setStatus(Integer status) {
        this.status = status;
    }


    public String getRefundOrderId() {
        return refundOrderId;
    }


    public void setRefundOrderId(String refundOrderId) {
        this.refundOrderId = refundOrderId;
    }


    public String getShopName() {
        return shopName;
    }


    public void setShopName(String shopName) {
        this.shopName = shopName;
    }
}
