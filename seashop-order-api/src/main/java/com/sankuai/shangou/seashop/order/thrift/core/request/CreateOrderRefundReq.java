package com.sankuai.shangou.seashop.order.thrift.core.request;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.order.thrift.core.dto.CreateOrderRefundItemDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundTypeEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 售后单创建入参 可以指定创建售后状态，暂时支持一个售后单一个单品或者整单
 *
 * <AUTHOR>
 */
@Data
@TypeDoc(description = "创建售后单入参")
public class CreateOrderRefundReq extends BaseParamReq {
    /**
     * 订单ID
     */
    @FieldDoc(
            name = "orderId",
            description = "订单ID",
            requiredness = Requiredness.REQUIRED
    )
    private String orderId;
    /**
     * 原售后单ID
     */
    private String sourceRefundId;
    /**
     * 支付前 订单退款  仅退钱
     * 支付后 货品退款  仅退钱
     * 支付后 退货退款  退钱退货
     */
    @FieldDoc(
            name = "refundMode",
            description = "支付前 订单退款,支付后 货品退款,支付后 退货退款",
            requiredness = Requiredness.REQUIRED
    )
    private RefundModeEnum refundMode;

    /**
     * 指定状态 @link RefundStatusEnum
     */
    @FieldDoc(
            name = "status",
            description = "指定状态 @link RefundStatusEnum",
            requiredness = Requiredness.OPTIONAL
    )
    private RefundStatusEnum status;
    /**
     * 售后单列表
     */
    @FieldDoc(
            name = "refundItems",
            description = "售后单列表,商品退款，退货退款必传，退货退款商品需要数量",
            requiredness = Requiredness.OPTIONAL
    )
    private List<CreateOrderRefundItemDto> refundItems;
    /**
     * 退货原因
     */
    @FieldDoc(
            name = "refundReason",
            description = "退货原因",
            requiredness = Requiredness.REQUIRED
    )
    private String refundReason;
    /**
     * 退款说明
     */
    @FieldDoc(
            name = "remark",
            description = "退款说明",
            requiredness = Requiredness.OPTIONAL
    )
    private String remark;
    /**
     * 退款凭证，最多3张图片
     */
    @FieldDoc(
            name = "pics",
            description = "退款凭证，最多3张图片",
            requiredness = Requiredness.OPTIONAL
    )
    private List<String> pics;

    /**
     * 快递公司编码
     */
    @FieldDoc(
            name = "expressCompanyCode",
            description = "快递公司编码",
            requiredness = Requiredness.OPTIONAL
    )
    private String expressCompanyCode;
    /**
     * 快递公司名称
     */
    @FieldDoc(
            name = "expressCompanyName",
            description = "快递公司名称",
            requiredness = Requiredness.OPTIONAL
    )
    private String expressCompanyName;
    /**
     * 快递单号
     */
    @FieldDoc(
            name = "shipOrderNumber",
            description = "快递单号",
            requiredness = Requiredness.OPTIONAL
    )
    private String shipOrderNumber;

    /**
     * 运费
     */
    @FieldDoc(
            name = "refundFreight",
            description = "运费",
            requiredness = Requiredness.OPTIONAL
    )
    private BigDecimal refundFreight;
    /**
     * 退款金额
     */
    @FieldDoc(
            name = "refundAmount",
            description = "退款金额",
            requiredness = Requiredness.OPTIONAL
    )
    private BigDecimal refundAmount;

    /**
     * 售后类型。1：仅退款；2：退货退款
     */
    @FieldDoc(
            name = "refundType",
            description = "售后类型。1：仅退款；2：退货退款",
            requiredness = Requiredness.OPTIONAL
    )
    private RefundTypeEnum refundType;

    @Override
    public void checkParameter() {
        //仅退款 不校验数量
        AssertUtil.throwIfTrue(StrUtil.isBlank(orderId), "订单编号不能为空");
        AssertUtil.throwIfTrue(refundMode == null, "退货/退款类型不能为空");
        AssertUtil.throwIfTrue(refundType == null, "退货/退款方式不能为空");
        AssertUtil.throwIfTrue(CollectionUtil.size(pics) > 3, "退款凭证，最多3张图片");
        //退货退款
        if (RefundModeEnum.RETURN_AND_REFUND.equals(refundMode)) {
            // 校验商品列表
            AssertUtil.throwIfTrue(CollectionUtil.isEmpty(refundItems), "售后单列表不能为空");
            refundItems.forEach(item -> {
                item.checkParameter();
                AssertUtil.throwIfTrue(item.getQuantity() == null || item.getQuantity() <= 0,
                        "售后单金额不能小于等于0");
            });
        }
        if (RefundModeEnum.GOODS_REFUND.equals(refundMode)) {
            // 校验商品列表 不校验数量
            AssertUtil.throwIfTrue(CollectionUtil.isEmpty(refundItems), "售后单列表不能为空");
            refundItems.forEach(CreateOrderRefundItemDto::checkParameter);
        }
        if (RefundModeEnum.ORDER_REFUND.equals(refundMode)) {
            //整单退 不需要商品
            refundItems = null;
        }
    }


    public String getRefundFreightString() {
        return this.bigDecimal2String(this.refundFreight);
    }


    public void setRefundFreightString(String refundFreight) {
        this.refundFreight = this.string2BigDecimal(refundFreight);
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


}
