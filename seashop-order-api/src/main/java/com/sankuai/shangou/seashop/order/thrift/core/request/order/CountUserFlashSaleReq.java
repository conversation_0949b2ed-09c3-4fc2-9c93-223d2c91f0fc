package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "统计用户限时购数据入参")
public class CountUserFlashSaleReq extends BaseParamReq {

    @FieldDoc(description = "用户id")
    private Long userId;
    @FieldDoc(description = "限时购id")
    private Long flashSaleId;
    @FieldDoc(description = "商品id")
    private Long productId;
    @FieldDoc(description = "skuId")
    private String skuId;

    @Override
    public void checkParameter() {
        if (this.userId == null) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.flashSaleId == null) {
            throw new InvalidParamException("flashSaleId不能为空");
        }
        if (productId == null && StrUtil.isBlank(skuId)) {
            throw new InvalidParamException("productId和skuId不能同时为空");
        }
    }


}
