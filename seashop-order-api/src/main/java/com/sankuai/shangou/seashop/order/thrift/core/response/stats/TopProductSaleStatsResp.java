package com.sankuai.shangou.seashop.order.thrift.core.response.stats;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ProductSaleAmountDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "销售排行前N的商品统计返回对象")
public class TopProductSaleStatsResp extends BaseThriftDto {

    @FieldDoc(description = "商品销售额排行列表")
    private List<ProductSaleAmountDto> saleList;
    @FieldDoc(description = "平均销售额")
    private BigDecimal avgSaleAmount;


    public String getAvgSaleAmountString() {
        return this.bigDecimal2String(this.avgSaleAmount);
    }


    public void setAvgSaleAmountString(String avgSaleAmount) {
        this.avgSaleAmount = this.string2BigDecimal(avgSaleAmount);
    }
}
