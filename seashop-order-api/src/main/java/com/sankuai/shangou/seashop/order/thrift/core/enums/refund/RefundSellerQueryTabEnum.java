package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 供应商查询售后列表tab枚举
 * <AUTHOR>
 */
@ThriftEnum
public enum RefundSellerQueryTabEnum {

    // 退款记录(全部)：所有的仅退款、未发货整单取消的售后单
    REFUND_ALL(1, "退款记录-全部"),
    // 退款-待处理
    REFUND_WAIT_PROCESS(2, "退款-待处理"),
    // 退货记录(全部)：展示所有的退货数据
    RETURN_ALL(3, "退货记录-全部"),
    // 退货-待处理：展示待店铺审核的售后单、待店铺确认收货的售后单
    RETURN_WAIT_PROCESS(4, "退货-待处理"),
    // 退款-买家取消
    REFUND_BUYER_CANCEL(5, "退款记录-买家取消"),
    // 退货-买家取消
    RETURN_BUYER_CANCEL(6, "退货-买家取消"),
    ;

    private final Integer code;
    @Getter
    private final String desc;

    RefundSellerQueryTabEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static RefundSellerQueryTabEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
