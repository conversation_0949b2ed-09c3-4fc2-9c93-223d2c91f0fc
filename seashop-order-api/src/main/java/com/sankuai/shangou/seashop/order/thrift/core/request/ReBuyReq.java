package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 重新购买请求入参
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "重新购买请求入参")
@Getter
@Setter
public class ReBuyReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)

    private String orderId;
    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)

    private Long userId;

    @Override
    public void checkParameter() {
        if (userId == null) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (StringUtils.isNotBlank(orderId)) {
            throw new IllegalArgumentException("orderId不能为空");
        }
    }
}
