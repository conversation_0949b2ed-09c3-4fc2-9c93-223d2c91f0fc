package com.sankuai.shangou.seashop.pay.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: lhx
 * @date: 2023/11/7/007
 * @description:
 */
@ThriftEnum
@AllArgsConstructor
@Getter
public enum PaymentChannelEnum {

    /**
     * 汇付支付
     */
    ADAPAY(1, "汇付天下支付", "adapay", 1, 2),

    /**
     * 微信支付
     */
    WXPAY(2, "微信支付", "wxpay", 3, 4),

    /**
     * 支付宝支付
     */
    ALIPAY(3, "支付宝支付", "alipay", 5, 6),

    /**
     * mock支付
     */
    MOCKPAY(9, "mock支付", "mockpay", 7, 8),
    ;

    private Integer code;
    private String msg;
    private String name;

    private Integer payLogType;
    private Integer payNotifyLogType;

    public static PaymentChannelEnum getByCode(Integer code) {
        for (PaymentChannelEnum value : PaymentChannelEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static PaymentChannelEnum getByName(String name) {
        for (PaymentChannelEnum value : PaymentChannelEnum.values()) {
            if (value.getName().equals(name)) {
                return value;
            }
        }
        return null;
    }

}
