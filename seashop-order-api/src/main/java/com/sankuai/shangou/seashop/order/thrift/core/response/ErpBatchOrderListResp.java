package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@ToString
@TypeDoc(description = "ERP批量查询订单返回对象")
@Data
public class ErpBatchOrderListResp {

    @FieldDoc(description = "订单列表")
    private List<ErpOrderDetailResp> orders;


}
