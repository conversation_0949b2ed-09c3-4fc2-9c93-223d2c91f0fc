package com.sankuai.shangou.seashop.order.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description:
 */
@ThriftEnum
public enum CashDepositPayTypeEnum {

    ALIPAY(1, "alipay", "支付宝", 178),
    //工商银行
//    ICBC(2, "01020000", "工商银行", 178),
//    // 广发银行
//    GDB(3, "03060000", "广发银行", 178),
//    // 浦发银行
//    SPDB(4, "03100000", "浦发银行", 178),
    // 其他银行
    OTHER(5, "other", "对公支付", 178),
    ;

    private Integer code;
    private String type;
    private String name;
    private Integer refundDay;

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static Integer getRefundDay(String type) {
        for (CashDepositPayTypeEnum value : CashDepositPayTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.refundDay;
            }
        }
        return OTHER.refundDay;
    }

    CashDepositPayTypeEnum(Integer code, String type, String name, Integer refundDay) {
        this.code = code;
        this.type = type;
        this.name = name;
        this.refundDay = refundDay;
    }
}
