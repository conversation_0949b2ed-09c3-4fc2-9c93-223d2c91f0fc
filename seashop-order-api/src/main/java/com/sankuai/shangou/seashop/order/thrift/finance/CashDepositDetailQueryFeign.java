package com.sankuai.shangou.seashop.order.thrift.finance;

import org.apache.thrift.TException;import org.springframework.cloud.openfeign.FeignClient;

import org.springframework.web.bind.annotation.PostMapping;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositDetailListResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositDetailResp;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description: 保证金明细查询相关服务
 */
@FeignClient(name = "himall-order", contextId = "CashDepositDetailQueryFeign", path = "/himall-order/finance/cashDepositDetail", url = "${himall-order.dev.url:}")
public interface CashDepositDetailQueryFeign {

    /**
     * 通过CashDepositId查询支付成功的保证金支付列表信息
     *
     */
    @PostMapping(value = "getPayListByCashDepositId", consumes = "application/json")
    ResultDto<CashDepositDetailListResp> getPayListByCashDepositId(@RequestBody BaseIdReq request) throws TException;

    /**
     * 通过条件查询支付明细信息
     *
     */
    @PostMapping(value = "pageList", consumes = "application/json")
    ResultDto<BasePageResp<CashDepositDetailResp>> pageList(@RequestBody CashDepositDetailQueryReq request) throws TException;
}
