package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "支付方式对象")
@ToString
@Data
public class PayMethodDto extends BaseThriftDto {

    @FieldDoc(description = "支付方式code")
    private Integer type;
    @FieldDoc(description = "支付方式描述")
    private String desc;


}
