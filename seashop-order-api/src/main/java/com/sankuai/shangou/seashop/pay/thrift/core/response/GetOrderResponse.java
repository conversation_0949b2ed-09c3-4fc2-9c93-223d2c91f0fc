package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.OrderInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 查询订单响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetOrderResponse {
    @FieldDoc(description = "errcode")
    private int errCode;

    @FieldDoc(description = "errmsg")
    private String errMsg;

    @FieldDoc(description = "order")
    private OrderInfo order;
}
