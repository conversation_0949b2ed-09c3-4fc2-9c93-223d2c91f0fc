package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.CreateOrderRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.PlatformApproveBatchReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.*;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderRefundResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.PlatformApproveBatchResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * 主要应用于小程序端和PC端对订单售后的操作场景
 */
@FeignClient(name = "himall-order", contextId = "OrderRefundCmdFeign", path = "/himall-order/orderRefundCmd", url = "${himall-order.dev.url:}")
public interface OrderRefundCmdFeign {

    /**
     * 订单退款申请
     * 待发货状态下，申请订单退款
     */
    @PostMapping(value = "/applyRefund", consumes = "application/json")
    ResultDto<Long> applyRefund(@RequestBody ApplyOrderRefundReq applyReq) throws TException;

    /**
     * 整单退款退货
     * 待收货/已完成状态下，申请整单退款退货
     */
    @PostMapping(value = "/applyWholeOrderRefund", consumes = "application/json")
    ResultDto<Long> applyWholeOrderRefund(@RequestBody ApplyOrderRefundReq applyReq) throws TException;

    /**
     * 重新申请订单退款
     * 包括订单退款和整单退货退款。是售后取消或者拒绝后，修改信息重新提交
     */
    @PostMapping(value = "/reapplyOrderRefund", consumes = "application/json")
    ResultDto<BaseResp> reapplyOrderRefund(@RequestBody ReapplyRefundReq applyReq) throws TException;

    /**
     * 订单商品退款/退货
     * 待收货/已完成状态下，申请订单商品退款/退货
     */
    @PostMapping(value = "/applyItemRefund", consumes = "application/json")
    ResultDto<Long> applyItemRefund(@RequestBody ApplyOrderItemRefundReq applyReq) throws TException;

    /**
     * 取消订单售后
     */
    @PostMapping(value = "/cancelRefund", consumes = "application/json")
    ResultDto<BaseResp> cancelRefund(@RequestBody CancelOrderRefundReq cancelReq) throws TException;

    /**
     * 买家寄货
     */
    @PostMapping(value = "/userDeliver", consumes = "application/json")
    ResultDto<BaseResp> userDeliver(@RequestBody UserDeliverReq req) throws TException;

    /**
     * 退货寄回
     */
    @PostMapping(value = "/sendRefundGoods", consumes = "application/json")
    ResultDto<BaseResp> sendRefundGoods(@RequestBody SendRefundGoodsReq req) throws TException;

    /**
     * 供应商审核
     */
    @PostMapping(value = "/sellerApprove", consumes = "application/json")
    ResultDto<BaseResp> sellerApprove(@RequestBody SellerApproveReq req) throws TException;

    /**
     * 供应商确认收货
     */
    @PostMapping(value = "/sellerConfirmReceive", consumes = "application/json")
    ResultDto<BaseResp> sellerConfirmReceive(@RequestBody SellerConfirmReceiveReq req) throws TException;

    /**
     * 平台确认通过审核
     */
    @PostMapping(value = "/platformConfirm", consumes = "application/json")
    ResultDto<BaseResp> platformConfirm(@RequestBody PlatformApproveReq req) throws TException;

    /**
     * 平台驳回
     */
    @PostMapping(value = "/platformReject", consumes = "application/json")
    ResultDto<BaseResp> platformReject(@RequestBody PlatformApproveReq req) throws TException;

    /**
     * 平台批量确认通过审核
     */
    @PostMapping(value = "/platformBatchConfirm", consumes = "application/json")
    ResultDto<PlatformApproveBatchResp> platformBatchConfirm(@RequestBody PlatformApproveBatchReq batchReq);

    /**
     * 创建售后单（可以指定状态）
     */
    @PostMapping(value = "/createOrderRefund", consumes = "application/json")
    ResultDto<CreateOrderRefundResp> createOrderRefund(@RequestBody CreateOrderRefundReq req) throws TException;
}
