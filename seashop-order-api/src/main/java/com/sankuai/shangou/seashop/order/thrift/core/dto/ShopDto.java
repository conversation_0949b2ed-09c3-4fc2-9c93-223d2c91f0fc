package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "店铺信息对象。包括店铺信息和店铺登录时的用户信息")
@Getter
@Setter
public class ShopDto extends BaseParamReq {

    @FieldDoc(description = "店铺ID")

    private Long shopId;
    @FieldDoc(description = "店铺名称")

    private String shopName;

    @Override
    public void checkParameter() {
        if (shopId == null || shopId <= 0) {
            throw new InvalidParamException("shopId不能为空");
        }
    }
}
