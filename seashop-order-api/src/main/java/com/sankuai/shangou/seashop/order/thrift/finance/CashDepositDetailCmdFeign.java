package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ApplyRefundReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.DeductionReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description: 保证金明细操作相关服务
 */
@FeignClient(name = "himall-order", contextId = "CashDepositDetailCmdFeign", path = "/himall-order/finance/cashDepositDetail", url = "${himall-order.dev.url:}")
public interface CashDepositDetailCmdFeign {

    /**
     * 保证金扣款
     */
    @PostMapping(value = "deduction", consumes = "application/json")
    ResultDto<BaseResp> deduction(@RequestBody DeductionReq request) throws TException;

    /**
     * 保证金申请退款
     */
    @PostMapping(value = "applyRefund", consumes = "application/json")
    ResultDto<BaseResp> applyRefund(@RequestBody ApplyRefundReq request) throws TException;
}
