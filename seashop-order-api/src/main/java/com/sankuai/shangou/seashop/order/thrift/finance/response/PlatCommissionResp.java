package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@TypeDoc(description = "平台佣金总额")
@ToString
@Data
public class PlatCommissionResp extends BaseThriftDto {

    @FieldDoc(description = "平台佣金总额")
    private BigDecimal platCommission;


    public String getPlatCommissionString() {
        return this.bigDecimal2String(this.platCommission);
    }


    public void setPlatCommissionString(String platCommission) {
        this.platCommission = this.string2BigDecimal(platCommission);
    }
}
