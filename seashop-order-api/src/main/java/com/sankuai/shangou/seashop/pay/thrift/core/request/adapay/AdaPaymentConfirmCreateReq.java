package com.sankuai.shangou.seashop.pay.thrift.core.request.adapay;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.dto.AdaPaymentConfirmDivMembersDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "创建确认订单请求对象")
public class AdaPaymentConfirmCreateReq extends BaseParamReq {

    /**
     * Adapt生成的支付对象id
     */
    @FieldDoc(description = "来源订单id", requiredness = Requiredness.REQUIRED)
    private String sourceOrderId;

    /**
     * 请求订单号，只能为英文、数字或者下划线的一种或多种组合，保证在app_id下唯一
     */
    @FieldDoc(description = "请求订单号，只能为英文、数字或者下划线的一种或多种组合，保证在app_id下唯一")
    private String orderNo;

    /**
     * 确认金额，必须大于0，保留两位小数点，如0.10、100.05等。必须小于等于原支付金额-已确认金额-已撤销金额。
     */
    @FieldDoc(description = "确认金额（单位：元），必须大于0。必须小于等于原支付金额-已确认金额-已撤销金额。", requiredness = Requiredness.REQUIRED)
    private BigDecimal confirmAmount;

    /**
     * 附加说明
     */
    @FieldDoc(description = "附加说明")
    private String description;

    /**
     * 分账对象信息列表，一次请求最多仅支持7个分账方。json对象 形式，详见 分账对象信息列表
     */
    @FieldDoc(description = "分账对象信息列表不能为空", requiredness = Requiredness.REQUIRED)
    private List<AdaPaymentConfirmDivMembersDto> divMembers;

    /**
     * 手续费收取模式：O-商户手续费账户扣取手续费，I-交易金额中扣取手续费；值为空时，默认值为I；若为O时，分账对象列表中不支持传入手续费承担方
     */
    @FieldDoc(description = "手续费收取模式")
    private String feeMode;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.sourceOrderId)) {
            throw new InvalidParamException("sourceOrderId不能为空");
        }
        if (StrUtil.isBlank(this.orderNo)) {
            throw new InvalidParamException("orderNo不能为空");
        }
        if (null == this.confirmAmount || this.confirmAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("confirmAmount不能为空且不小于0");
        }
        if (CollUtil.isEmpty(this.divMembers)) {
            throw new InvalidParamException("divMembers不能为空");
        }
    }


    public String getConfirmAmountString() {
        return this.bigDecimal2String(this.confirmAmount);
    }


    public void setConfirmAmountString(String confirmAmount) {
        this.confirmAmount = this.string2BigDecimal(confirmAmount);
    }


}
