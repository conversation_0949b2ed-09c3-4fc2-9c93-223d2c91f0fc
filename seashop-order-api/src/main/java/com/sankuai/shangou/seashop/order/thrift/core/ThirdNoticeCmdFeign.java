package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.SaveThirdNoticeReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.SaveThirdNoticeResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2024/01/29 17:16
 * 主要应用于第三方通知的操作场景
 */
@FeignClient(name = "himall-order", contextId = "ThirdNoticeCmdFeign", path = "/himall-order/thirdNotice", url = "${himall-order.dev.url:}")
public interface ThirdNoticeCmdFeign {

    /**
     * 保存第三方推送通知
     */
    @PostMapping(value = "/createOrder", consumes = "application/json")
    ResultDto<SaveThirdNoticeResp> saveThirdNotice(@RequestBody SaveThirdNoticeReq request) throws TException;

}
