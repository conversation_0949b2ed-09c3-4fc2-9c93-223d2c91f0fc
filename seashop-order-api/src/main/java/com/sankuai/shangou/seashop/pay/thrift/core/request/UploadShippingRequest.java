package com.sankuai.shangou.seashop.pay.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.OrderKey;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.Payer;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.ShippingItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:统一发货请求参数
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
public class UploadShippingRequest extends BaseRequest {

    @FieldDoc(description = "order_key")
    private OrderKey order_key;

    @FieldDoc(description = "delivery_mode")
    private int delivery_mode; // 1-统一发货, 2-分拆发货

    @FieldDoc(description = "logistics_type")
    private int logistics_type; // 物流类型（1-快递, 2-同城, 3-虚拟, 4-自提）

    @FieldDoc(description = "shipping_list")
    private List<ShippingItem> shipping_list; // 物流列表

    @FieldDoc(description = "is_all_delivered")
    private Boolean is_all_delivered; // 分拆发货必填

    @FieldDoc(description = "upload_time")
    private String upload_time; // 上传时间戳（RFC3339格式）

    @FieldDoc(description = "payer")
    private Payer payer; // 支付者信息
}
