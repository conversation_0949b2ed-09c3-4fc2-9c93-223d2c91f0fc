package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@ThriftEnum
public enum ExceptionOrderTypeEnum {

    REPEAT_PAY(1, "重复支付"),
    TIMEOUT_CLOSE(2, "超时关闭"),
    ;

    private final Integer code;
    private final String desc;

    ExceptionOrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ExceptionOrderTypeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(exceptionOrderType -> exceptionOrderType.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(exceptionOrderType -> exceptionOrderType.getCode().equals(code))
                .findFirst()
                .map(ExceptionOrderTypeEnum::getDesc)
                .orElse("");
    }

}
