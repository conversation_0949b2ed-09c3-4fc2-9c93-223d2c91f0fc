package com.sankuai.shangou.seashop.order.thrift.core.response.stats;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 平台首页统计交易数据返回对象
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "平台首页交易数据统计对象")
public class PlatformIndexTradeDataStatsResp extends BaseThriftDto {

    /**
     * 交易笔数=[已完成]订单数量
     */
    @FieldDoc(description = "交易笔数=[已完成]订单数量")
    private Long completedOrderCount;
    /**
     * 待付款订单数量
     */
    @FieldDoc(description = "待付款订单数量")
    private Long underPayOrderCount;
    /**
     * 待发货订单数量
     */
    @FieldDoc(description = "待发货订单数量")
    private Long underDeliveryOrderCount;
    /**
     * 待处理 退款单数量
     */
    @FieldDoc(description = "待处理 退款单数量")
    private Long underDealRefundCount;
    /**
     * 待处理 退货单数量
     */
    @FieldDoc(description = "待处理 退货单数量")
    private Long underDealReturnCount;
    /**
     * 待处理 投诉单数量
     */
    @FieldDoc(description = "待处理 投诉单数量")
    private Long underDealComplaintCount;
    /**
     * 今日有效交易金额
     */
    @FieldDoc(description = "今日有效交易金额")
    private BigDecimal todayEffectiveAmount;


    public String getTodayEffectiveAmountString() {
        return this.bigDecimal2String(this.todayEffectiveAmount);
    }


    public void setTodayEffectiveAmountString(String todayEffectiveAmount) {
        this.todayEffectiveAmount = this.string2BigDecimal(todayEffectiveAmount);
    }
}
