package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "店铺信息对象")
@ToString
@Data
public class OrderShopDto extends BaseThriftDto {

    @FieldDoc(description = "店铺ID")
    private Long shopId;
    @FieldDoc(description = "店铺名称")
    private String shopName;
    @FieldDoc(description = "勾选的sku的总金额")
    private BigDecimal selectedTotalAmount;
    @FieldDoc(description = "订单和商品满足的营销活动")
    private ShopAndProductPromotionDto shopAndProductPromotion;
    @FieldDoc(description = "店铺商品总金额")
    private BigDecimal productTotalAmount;
    /**
     * 商品总数量
     */
    @FieldDoc(description = "商品总数量")
    private Long productQuantity;


    public String getSelectedTotalAmountString() {
        return this.bigDecimal2String(this.selectedTotalAmount);
    }


    public void setSelectedTotalAmountString(String selectedTotalAmount) {
        this.selectedTotalAmount = this.string2BigDecimal(selectedTotalAmount);
    }


    public String getProductTotalAmountString() {
        return this.bigDecimal2String(this.productTotalAmount);
    }


    public void setProductTotalAmountString(String productTotalAmount) {
        this.productTotalAmount = this.string2BigDecimal(productTotalAmount);
    }


}
