package com.sankuai.shangou.seashop.order.thrift.core.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "客服对象")
public class CustomServiceDto {

    @FieldDoc(description = "客服id")
    private Long id;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "工具 1 QQ 2 旺旺 3 美洽 4 海商")
    private Integer tool;

    @FieldDoc(description = "类型")
    private Integer type;

    @FieldDoc(description = "名称")
    private String name;

    @FieldDoc(description = "通信账号")
    private String accountCode;

    @FieldDoc(description = "终端类型")
    private Integer terminalType;

    @FieldDoc(description = "客服状态")
    private Integer serverStatus;


}
