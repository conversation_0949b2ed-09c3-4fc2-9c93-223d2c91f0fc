package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@TypeDoc(description = "待结算订单店铺维度")
@ToString
@Data
public class PendingSettlementResp extends BaseThriftDto {

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "待结算金额")
    private BigDecimal totalAmount;

    @FieldDoc(description = "预计结算时间")
    private String settlementTimeStr;

    @FieldDoc(description = "结算周期开始时间")
    private String settlementStartTimeStr;

    @FieldDoc(description = "结算周期结束时间")
    private String settlementEndTimeStr;


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


}
