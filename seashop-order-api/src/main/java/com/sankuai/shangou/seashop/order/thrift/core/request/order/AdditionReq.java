package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import com.sankuai.shangou.seashop.order.thrift.core.dto.InvoiceDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderDeliveryTypeEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname AdditionReq
 * Description //TODO
 * @date 2024/11/7 14:25
 */
@Data
public class AdditionReq implements Serializable {
    /**
     * 配送方式。目前是固定值。1：快递配送{@link OrderDeliveryTypeEnum.EXPRESS}
     */
    private Integer deliveryType = 1;
    /**
     * 用户备注
     */
    private String remark;
    /**
     * 发票信息
     */
    private InvoiceDto invoice;
}
