package com.sankuai.shangou.seashop.pay.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.TimeRange;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 查询订单列表请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetOrderListRequest extends BaseRequest {
    @FieldDoc(description = "pay_time_range")
    private TimeRange payTimeRange;

    @FieldDoc(description = "last_index")
    private String lastIndex;

    @FieldDoc(description = "page_size")
    private int pageSize;

    @FieldDoc(description = "order_state")
    private Integer orderState;

    @FieldDoc(description = "openid")
    private String openid;
}
