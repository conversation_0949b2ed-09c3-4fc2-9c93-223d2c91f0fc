package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.DeliveryList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/04/02/ $
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryListResponse {

    @FieldDoc(description = "errcode")
    private int errcode;

    @FieldDoc(description = "errmsg")
    private String errmsg;

    @FieldDoc(description = "count")
    private int count;

    @FieldDoc(description = "delivery_list")
    private List<DeliveryList> deliveryList;
}
