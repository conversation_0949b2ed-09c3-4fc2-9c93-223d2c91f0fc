package com.sankuai.shangou.seashop.pay.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.OrderKey;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.SubOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 合单发货请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadCombinedShippingRequest extends BaseRequest  {
    @FieldDoc(description = "order_key")
    private OrderKey orderKey;

    @FieldDoc(description = "sub_orders")
    private List<SubOrder> subOrders; // 子单列表
}
