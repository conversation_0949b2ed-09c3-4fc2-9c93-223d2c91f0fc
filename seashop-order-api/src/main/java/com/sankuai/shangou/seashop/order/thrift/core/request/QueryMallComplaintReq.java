package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/21 18:56
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "查询申请记录入参")
public class QueryMallComplaintReq extends BasePageReq {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "商家id")
    private Long userId;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (userId == null) {
            throw new InvalidParamException("userId不能为空");
        }
    }
}
