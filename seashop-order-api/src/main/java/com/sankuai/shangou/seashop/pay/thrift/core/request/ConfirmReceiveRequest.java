package com.sankuai.shangou.seashop.pay.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:  确认收货提醒
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfirmReceiveRequest extends BaseRequest {
    @FieldDoc(description = "transaction_id")
    private String transactionId; // 微信支付订单号

    @FieldDoc(description = "merchant_id")
    private String merchantId; // 商户号

    @FieldDoc(description = "merchant_trade_no")
    private String merchantTradeNo; // 商户内部订单号

    @FieldDoc(description = "received_time")
    private long receivedTime; // 签收时间戳（毫秒）
}
