package com.sankuai.shangou.seashop.pay.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessStatusTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "创建退款订单请求对象")
public class PayReverseCreateReq extends BaseParamReq {

    @FieldDoc(description = "来源订单id", requiredness = Requiredness.REQUIRED)
    private String orderId;

    @FieldDoc(description = "退款id", requiredness = Requiredness.REQUIRED)
    private String reverseId;

    @FieldDoc(description = "退款金额（单位：元）", requiredness = Requiredness.REQUIRED)
    private BigDecimal reverseAmount;

    @FieldDoc(description = "业务状态类型：1：正常订单退款；2：异常订单退款；3：订单补偿退款（支付金额大于订单金额）")
    private Integer businessStatusType = BusinessStatusTypeEnum.NORMAL.getType();

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.orderId)) {
            throw new InvalidParamException("来源订单id不能为空");
        }
        if (StrUtil.isBlank(this.reverseId)) {
            throw new InvalidParamException("退款id不能为空");
        }
        if (this.reverseAmount == null || this.reverseAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidParamException("退款金额必须大于0");
        }
        if (null == BusinessStatusTypeEnum.getByType(this.businessStatusType)) {
            throw new InvalidParamException("业务状态类型不正确");
        }

    }


    public String getReverseAmountString() {
        return this.bigDecimal2String(this.reverseAmount);
    }


    public void setReverseAmountString(String reverseAmount) {
        this.reverseAmount = this.string2BigDecimal(reverseAmount);
    }


}
