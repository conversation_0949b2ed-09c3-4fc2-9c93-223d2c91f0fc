package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
@TypeDoc(description = "订单支付响应体")
@ToString
@Data
public class ReverseOrderResp extends BaseThriftDto {

    /**
     * 主键
     */
    @FieldDoc(description = "主键")
    private Long id;

    /**
     * 渠道支付ID
     */
    @FieldDoc(description = "渠道支付ID")
    private String channelPayId;

    /**
     * 退款流水ID
     */
    @FieldDoc(description = "退款流水ID")
    private String reverseId;

    /**
     * 退款金额
     */
    @FieldDoc(description = "退款金额（单位：元）")
    private BigDecimal reverseAmount;

    /**
     * 退款状态 0: 退款中,1: 退款成功,2: 退款失败
     */
    @FieldDoc(description = "退款状态 0: 退款中,1: 退款成功,2: 退款失败")
    private Integer reverseState;

    /**
     * 退款时间
     */
    @FieldDoc(description = "退款时间")
    private Date reverseTime;

    /**
     * 渠道退款ID
     */
    @FieldDoc(description = "渠道退款ID")
    private String channelRefundId;

    /**
     * 错误描述
     */
    @FieldDoc(description = "错误描述")
    private String channelRefundMsg;

    /**
     * 退款类型 0：未结算退款；1：已结算退款
     */
    @FieldDoc(description = "退款类型 0：未结算退款；1：已结算退款")
    private Integer reverseType;

    /**
     * 业务类型 1：订单,4：保证金
     */
    @FieldDoc(description = "业务类型 1：订单,4：保证金")
    private Integer businessType;


    public String getReverseAmountString() {
        return this.bigDecimal2String(this.reverseAmount);
    }


    public void setReverseAmountString(String reverseAmount) {
        this.reverseAmount = this.string2BigDecimal(reverseAmount);
    }


    public Long getReverseTimeLong() {
        return this.date2Long(this.reverseTime);
    }


    public void setReverseTimeLong(Long reverseTime) {
        this.reverseTime = this.long2Date(reverseTime);
    }


}
