package com.sankuai.shangou.seashop.pay.thrift.core.request.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 支付者信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Payer {
    @FieldDoc(description = "openid")
    private String openid; // 用户OpenID
}
