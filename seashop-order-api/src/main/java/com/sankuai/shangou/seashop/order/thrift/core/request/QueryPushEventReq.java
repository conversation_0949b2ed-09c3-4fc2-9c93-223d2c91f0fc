package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

/**
 * <AUTHOR>
 */

@TypeDoc(description = "推送事件查询")
@Data
public class QueryPushEventReq extends BaseParamReq {

    /**
     * 存在 则表示修改
     */
    @FieldDoc(description = "数据主键")
    private Long id;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(id, "事件主键不能为空");
    }


}
