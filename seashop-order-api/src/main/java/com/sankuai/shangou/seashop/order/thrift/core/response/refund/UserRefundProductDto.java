package com.sankuai.shangou.seashop.order.thrift.core.response.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/26/026
 * @description:
 */
@Data
@ToString
@TypeDoc(description = "售后明细商品对象")
public class UserRefundProductDto extends BaseThriftDto {

    @FieldDoc(description = "退款商品id")
    private Long productId;
    @FieldDoc(description = "SKU ID")
    private String skuId;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "商品主图")
    private String mainImagePath;
    @FieldDoc(description = "规格描述列表")
    private List<String> skuDescList;
    @FieldDoc(description = "规格描述")
    private String skuDesc;
    @FieldDoc(description = "价格")
    private BigDecimal price;
    @FieldDoc(description = "退货数量")
    private Long returnQuantity;


    public String getPriceString() {
        return this.bigDecimal2String(this.price);
    }


    public void setPriceString(String price) {
        this.price = this.string2BigDecimal(price);
    }


}
