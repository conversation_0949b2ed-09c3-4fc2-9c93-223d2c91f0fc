package com.sankuai.shangou.seashop.order.thrift.finance.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "退款审批拒绝请求对象")
public class CashDepositRefundRefuseReq extends BaseParamReq {

    @FieldDoc(description = "退款单id", requiredness = Requiredness.REQUIRED)
    @PrimaryField
    private Long id;

    @FieldDoc(description = "拒绝原因")
    @ExaminField(description = "拒绝原因")
    private String refuseReason;

    @FieldDoc(description = "操作人", requiredness = Requiredness.REQUIRED)
    @ExaminField(description = "操作人")
    private String operator;

    @Override
    public void checkParameter() {
        if (null == this.id) {
            throw new InvalidParamException("id不能为空");
        }
        if (StrUtil.isBlank(this.operator)) {
            throw new InvalidParamException("operator不能为空");
        }
        if(refuseReason.length() > 200){
            throw new InvalidParamException("拒绝原因不能超过200个字符");
        }
    }


}
