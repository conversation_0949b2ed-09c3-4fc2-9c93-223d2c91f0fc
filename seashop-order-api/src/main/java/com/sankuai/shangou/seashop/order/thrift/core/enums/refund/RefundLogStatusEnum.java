package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/26 9:40
 */
@ThriftEnum
public enum RefundLogStatusEnum {

    PASS(1, "通过"),

    REJECT(-1, "驳回"),

    REVIEWING(0, "审核中"),
    ;

    private final Integer code;

    private final String desc;

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    RefundLogStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
