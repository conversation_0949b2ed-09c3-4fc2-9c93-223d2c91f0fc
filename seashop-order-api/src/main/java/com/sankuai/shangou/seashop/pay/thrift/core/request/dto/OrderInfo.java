package com.sankuai.shangou.seashop.pay.thrift.core.request.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 订单详情
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderInfo {
    @FieldDoc(description = "transaction_id")
    private String transactionId;

    @FieldDoc(description = "merchant_trade_no")
    private String merchantTradeNo;

    @FieldDoc(description = "order_state")
    private int orderState; // 1-待发货,2-已发货,3-确认收货,4-交易完成

    @FieldDoc(description = "shipping")
    private ShippingInfo shipping;
}
