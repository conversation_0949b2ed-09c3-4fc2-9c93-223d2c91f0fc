package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单发票信息对象")
public class OrderInvoiceDto extends BaseThriftDto {

    @FieldDoc(description = "发票类型 1：普通发票；2：电子发票；3：增值税发票")
    private Integer invoiceType;
    @FieldDoc(description = "发票抬头")
    private String invoiceTitle;
    @FieldDoc(description = "税号")
    private String invoiceCode;
    @FieldDoc(description = "收票人姓名")
    private String realName;
    @FieldDoc(description = "收票人手机号")
    private String cellPhone;
    @FieldDoc(description = "收票人邮箱")
    private String email;


    @FieldDoc(description = "发票内容(发票明细、商品类别)")
    private String invoiceContext;
    @FieldDoc(description = "注册地址")
    private String registerAddress;
    @FieldDoc(description = "注册电话")
    private String registerPhone;
    @FieldDoc(description = "开户银行")
    private String bankName;
    @FieldDoc(description = "银行帐号")
    private String bankNo;
    @FieldDoc(description = "收票人地址区域ID")
    private Integer regionId;
    @FieldDoc(description = "收票人详细地址")
    private String address;
    @FieldDoc(description = "订单完成后多少天开具增值税发票")
    private Integer vatInvoiceDay;
    @FieldDoc(description = "省")
    private Long provinceId;
    @FieldDoc(description = "省")
    private String provinceName;
    @FieldDoc(description = "市")
    private Long cityId;
    @FieldDoc(description = "市")
    private String cityName;
    @FieldDoc(description = "区")
    private Long countyId;
    @FieldDoc(description = "区")
    private String countyName;
    @FieldDoc(description = "乡镇")
    private String townIds;
    @FieldDoc(description = "乡镇")
    private String townsNames;


}
