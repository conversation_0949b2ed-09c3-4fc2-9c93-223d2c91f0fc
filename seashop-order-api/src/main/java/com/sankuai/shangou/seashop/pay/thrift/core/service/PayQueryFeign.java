package com.sankuai.shangou.seashop.pay.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.pay.thrift.core.request.BillDownloadReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.OrderPayQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ReverseOrderQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.BillDownloadResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.ReverseOrderResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description: 支付查询操作服务
 */
@FeignClient(name = "himall-order", contextId = "PayQueryFeign", path = "/himall-order/pay", url = "${himall-order.dev.url:}")
public interface PayQueryFeign {

    /**
     * 条件查询单个支付信息
     *
     */
    @PostMapping(value = "/queryOrderPayOne", consumes = "application/json")
    ResultDto<OrderPayResp> queryOrderPayOne(@RequestBody OrderPayQueryReq request) throws TException;

    /**
     * 条件查询单个退款信息
     */
    @PostMapping(value = "/queryReverseOrderOne", consumes = "application/json")
    ResultDto<ReverseOrderResp> queryReverseOrderOne(@RequestBody ReverseOrderQueryReq request) throws TException;

    /**
     * 对账单下载
     *
     */
    @PostMapping(value = "/billDownload", consumes = "application/json")
    ResultDto<BillDownloadResp> billDownload(@RequestBody BillDownloadReq request) throws TException;

    /**
     * 我已完成支付
     *
     */
    @PostMapping(value = "/queryCompletePay", consumes = "application/json")
    ResultDto<Map<String, OrderPayResp>> queryCompletePay(@RequestBody List<String> batchNos) throws TException;

    /**
     * 我已完成支付
     *
     */
    @PostMapping(value = "/queryPayOrderNoOutTransId", consumes = "application/json")
    ResultDto<Map<String, OrderPayResp>> queryPayOrderNoOutTransId(@RequestBody List<String> batchNos) throws TException;
}
