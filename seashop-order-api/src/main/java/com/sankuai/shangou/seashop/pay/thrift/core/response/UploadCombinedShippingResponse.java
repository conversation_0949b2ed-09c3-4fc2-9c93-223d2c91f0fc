package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 合单发货响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadCombinedShippingResponse {
    @FieldDoc(description = "errcode")
    private int errCode;

    @FieldDoc(description = "errmsg")
    private String errMsg;
}
