package com.sankuai.shangou.seashop.pay.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "订单支付查询条件对象")
public class ReverseOrderQueryReq extends BaseParamReq {

    @FieldDoc(description = "退款流水ID")
    private String reverseId;

    @FieldDoc(description = "渠道退款ID")
    private String channelRefundId;

    @Override
    public void checkParameter() {
        // reverseId和channelRefundId不能同时为空
        if (StrUtil.isBlank(this.reverseId) && StrUtil.isBlank(this.channelRefundId)) {
            throw new InvalidParamException("reverseId和channelRefundId不能同时为空");
        }
    }


}
