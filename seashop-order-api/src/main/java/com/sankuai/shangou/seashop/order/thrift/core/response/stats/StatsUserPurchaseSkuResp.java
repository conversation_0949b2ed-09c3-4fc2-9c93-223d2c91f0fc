package com.sankuai.shangou.seashop.order.thrift.core.response.stats;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserPurchaseSkuDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserPurchaseSkuStatsSummaryDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商家订单sku采购统计返回对象")
public class StatsUserPurchaseSkuResp extends BaseThriftDto {

    @FieldDoc(description = "分页数据")
    private BasePageResp<UserPurchaseSkuDto> pageData;
    @FieldDoc(description = "汇总数据")
    private UserPurchaseSkuStatsSummaryDto summary;


}
