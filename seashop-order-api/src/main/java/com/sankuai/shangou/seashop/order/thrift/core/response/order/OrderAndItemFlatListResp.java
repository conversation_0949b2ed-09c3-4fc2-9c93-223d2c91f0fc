package com.sankuai.shangou.seashop.order.thrift.core.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderAndItemInfoDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单和明细平铺展开的返回list对象")
public class OrderAndItemFlatListResp {

    @FieldDoc(description = "订单和明细平铺展开的返回list对象")
    private List<OrderAndItemInfoDto> dataList;
    @FieldDoc(description = "scrollId")
    private String scrollId;


}
