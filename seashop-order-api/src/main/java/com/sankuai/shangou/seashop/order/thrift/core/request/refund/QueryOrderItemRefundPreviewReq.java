package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "查询订单明细退款预览请求入参")
public class QueryOrderItemRefundPreviewReq extends BaseParamReq {

    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private UserDto user;
    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "订单明细ID", requiredness = Requiredness.REQUIRED)
    private Long orderItemId;
    @FieldDoc(description = "退款ID")
    private Long refundId;

    @Override
    public void checkParameter() {
        if (this.user == null) {
            throw new InvalidParamException("userId不能为空");
        }
        this.user.checkParameter();
        if (StrUtil.isBlank(this.orderId)) {
            throw new InvalidParamException("orderId不能为空");
        }
        if (this.orderItemId == null) {
            throw new InvalidParamException("orderItemId不能为空");
        }
    }


}
