package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.OrderInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 查询订单列表响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetOrderListResponse {
    @FieldDoc(description = "errcode")
    private int errCode;

    @FieldDoc(description = "errmsg")
    private String errMsg;

    @FieldDoc(description = "order_list")
    private List<OrderInfo> orderList;

    @FieldDoc(description = "has_more")
    private boolean hasMore;

    @FieldDoc(description = "last_index")
    private String lastIndex;
}