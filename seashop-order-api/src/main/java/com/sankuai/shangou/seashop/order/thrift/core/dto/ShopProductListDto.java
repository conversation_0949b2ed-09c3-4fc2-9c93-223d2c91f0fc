package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "购物车商品返回对象")
@ToString
@Data
public class ShopProductListDto extends BaseThriftDto {

    @FieldDoc(description = "店铺信息")
    private OrderShopDto shop;
    @FieldDoc(description = "商品列表")
    private List<OrderProductDto> productList;
    @FieldDoc(description = "附加信息")
    private OrderAdditionalDto additional;


}
