package com.sankuai.shangou.seashop.pay.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Map;

/**
 * 通用支付回调接口请求
 *
 * <AUTHOR> Lee
 * @see PayCallBackNotifyPayReq
 * @since 2025/6/26 15:34
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@Schema(description = "通用支付回调接口请求")
public class PayCallBackNotifyPayReq implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "回调数据")
    private String notifyData;

    @Schema(description = "回调签名头部信息")
    private Map<String, Object> notifySignHeaders;

}
