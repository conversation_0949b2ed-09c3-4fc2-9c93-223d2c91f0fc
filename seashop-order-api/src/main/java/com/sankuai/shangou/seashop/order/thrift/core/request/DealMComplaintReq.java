package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * @description：投诉维权平台处理
 * @author： liweisong
 * @create： 2023/11/24 8:59
 * 平台处理，入参应该为id,status传4，PlatRemark字段
 */
@Data
@ToString
@TypeDoc(description = "投诉维权平台处理")
public class DealMComplaintReq extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    @PrimaryField
    private Long id;
    @FieldDoc(description = "投诉备注")
    @ExaminField(description = "投诉备注")
    private String platRemark;


    public void checkParameter() {
        if (id == null) {
            throw new InvalidParamException("ID不能为空");
        }
        if (platRemark == null) {
            throw new InvalidParamException("平台回复备注不能为空");
        }
    }
}
