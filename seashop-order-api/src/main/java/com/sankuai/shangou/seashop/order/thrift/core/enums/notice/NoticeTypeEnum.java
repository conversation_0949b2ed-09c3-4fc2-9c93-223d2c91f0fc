package com.sankuai.shangou.seashop.order.thrift.core.enums.notice;

import com.facebook.swift.codec.ThriftEnum;

/**
 * <AUTHOR>
 * @date 2024/01/29 17:32
 */
@ThriftEnum
public enum NoticeTypeEnum {

    /**
     * 通知类型 0-9:订单通知 10-19:商品通知 20-29:售后单通知
     * 0-订单同步通知回调
     * 20-售后单同步通知回调
     */
    ORDER_SYNC_NOTICE(0, "订单同步通知回调"),
    REFUND_SYNC_NOTICE(20, "售后单同步通知回调"),

    ;

    private final Integer code;
    private final String desc;

    NoticeTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
