package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商家搜索订单入参")
public class QueryUserOrderReq extends BasePageReq {

    /**
     * 用户ID
     */
    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
    /**
     * 搜索关键字，支持 商品名称，订单编号，规格ID，商品ID
     */
    @FieldDoc(description = "搜索关键字，支持 商品名称，订单编号，规格ID，商品ID")
    private String searchKey;
    /**
     * 订单状态
     */
    @FieldDoc(description = "订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private Integer orderStatus;

    @FieldDoc(description = "订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private List<Integer> orderStatusList;

    /**
     * 下单时间-开始
     */
    @FieldDoc(description = "下单时间-开始")
    private Date orderStartTime;
    /**
     * 下单时间-结束
     */
    @FieldDoc(description = "下单时间-结束")
    private Date orderEndTime;
    /**
     * 完成时间-开始
     */
    @FieldDoc(description = "完成时间-开始")
    private Date finishStartTime;
    /**
     * 完成时间-结束
     */
    @FieldDoc(description = "完成时间-结束")
    private Date finishEndTime;
    /**
     * 查询来源。1-商家小程序；2-商家PC端；3-卖家PC端；4-平台PC端
     */
    @FieldDoc(description = "查询来源。1-商家小程序；2-商家PC端；3-卖家PC端；4-平台PC端")
    private OrderQueryFromEnum queryFrom;
    /**
     * 是否查询待评价订单
     */
    @FieldDoc(description = "是否查询待评价订单")
    private Boolean queryUnCommented;
    @FieldDoc(description = "基于scroll查询时的数据保留时间")
    private Long timeValueMinutes;

    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new IllegalArgumentException("userId不能为空");
        }
    }


    public Long getOrderStartTimeLong() {
        return this.date2Long(this.orderStartTime);
    }


    public void setOrderStartTimeLong(Long orderStartTime) {
        this.orderStartTime = this.long2Date(orderStartTime);
    }


    public Long getOrderEndTimeLong() {
        return this.date2Long(this.orderEndTime);
    }


    public void setOrderEndTimeLong(Long orderEndTime) {
        this.orderEndTime = this.long2Date(orderEndTime);
    }


    public Long getFinishStartTimeLong() {
        return this.date2Long(this.finishStartTime);
    }


    public void setFinishStartTimeLong(Long finishStartTime) {
        this.finishStartTime = this.long2Date(finishStartTime);
    }


    public Long getFinishEndTimeLong() {
        return this.date2Long(this.finishEndTime);
    }


    public void setFinishEndTimeLong(Long finishEndTime) {
        this.finishEndTime = this.long2Date(finishEndTime);
    }


}
