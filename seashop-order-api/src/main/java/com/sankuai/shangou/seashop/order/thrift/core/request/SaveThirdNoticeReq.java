package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.order.thrift.core.enums.notice.NoticeSourceEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.notice.NoticeTypeEnum;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/01/29 17:17
 */
@Data
@ToString
@TypeDoc(description = "保存第三方通知入参")
public class SaveThirdNoticeReq extends BaseParamReq {

    @FieldDoc(description = "通知类型", requiredness = Requiredness.REQUIRED)
    private NoticeTypeEnum noticeType;

    @FieldDoc(description = "通知来源", requiredness = Requiredness.REQUIRED)
    private NoticeSourceEnum noticeSource;

    @FieldDoc(description = "业务编号", requiredness = Requiredness.REQUIRED)
    private String bizCode;

    @FieldDoc(description = "通知内容", requiredness = Requiredness.REQUIRED)
    private String noticeBody;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(noticeType, "通知类型不能为空");
        AssertUtil.throwInvalidParamIfNull(noticeSource, "通知来源不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(bizCode), "业务编号不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(noticeBody), "通知内容不能为空");
    }


}
