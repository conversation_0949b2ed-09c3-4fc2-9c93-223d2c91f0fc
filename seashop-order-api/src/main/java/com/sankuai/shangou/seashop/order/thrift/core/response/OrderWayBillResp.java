package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderExpressDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单收货信息返回对象")
public class OrderWayBillResp extends BaseThriftDto {

    @FieldDoc(description = "订单号")
    private String orderId;
    /**
     * 区域ID
     */
    @FieldDoc(description = "区域ID")
    private Integer regionId;
    /**
     * 区域全称
     */
    @FieldDoc(description = "区域全称")
    private String regionFullName;
    /**
     * 收货人
     */
    @FieldDoc(description = "收货人")
    private String shipTo;
    @FieldDoc(description = "物流信息")
    private List<OrderExpressDto> expressList;
    /**
     * 收货地址完整路径，包括省市区和用户输入的
     */
    @FieldDoc(description = "收货地址完整路径，包括省市区和用户输入的")
    private String addressFullName;


}
