package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

import java.util.Map;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
@TypeDoc(description = "创建支付返回对象")
@ToString
@Data
public class OrderPayCreateResp {

    @FieldDoc(description = "汇付支付创建支付返回的发起参数")
    private Map<String,String> expend;

    @FieldDoc(description = "汇付支付创建支付返回的支付id")
    private String channelPayId;


}
