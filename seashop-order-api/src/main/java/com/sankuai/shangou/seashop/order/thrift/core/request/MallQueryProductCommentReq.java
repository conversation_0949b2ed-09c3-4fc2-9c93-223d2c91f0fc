package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.order.thrift.core.enums.CommentEnum;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/07 18:22
 */
@Data
@ToString
@TypeDoc(description = "商城端查询评论入参")
public class MallQueryProductCommentReq extends BasePageReq {

    @FieldDoc(description = "商品id", requiredness = Requiredness.REQUIRED)
    private Long productId;

    @FieldDoc(description = "评论状态", requiredness = Requiredness.REQUIRED)
    private CommentEnum.MallCommentStatus status;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(productId == null || productId <= 0, "商品id不能为空");
        AssertUtil.throwIfTrue(status == null, "评论状态不能为空");
    }


}
