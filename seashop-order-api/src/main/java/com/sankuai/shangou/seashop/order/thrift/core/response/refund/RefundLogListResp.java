package com.sankuai.shangou.seashop.order.thrift.core.response.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "退款日志对象")
public class RefundLogListResp {

    @FieldDoc(description = "日志列表")
    private List<RefundLogResp> logList;
    @FieldDoc(description = "用户id")
    private Long userId;
    @FieldDoc(description = "店铺id")
    private Long shopId;


}
