package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "平台审核通过结果")
public class PlatformApproveResp {

    @FieldDoc(description = "售后单id")
    private Long refundId;
    @FieldDoc(description = "错误描述")
    private String errorDesc;


}
