package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@TypeDoc(description = "已结算列表查询返回参数")
@ToString
@Data
public class SettledResp extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "结算金额")
    private BigDecimal settleAmount;

    @FieldDoc(description = "结算详情id")
    private Long detailId;

    @FieldDoc(description = "结算时间")
    private Date settleTime;

    @FieldDoc(description = "结算周期-开始时间")
    private Date startCycleTime;

    @FieldDoc(description = "结算周期-结束时间")
    private Date endCycleTime;

    @FieldDoc(description = "订单金额")
    private BigDecimal orderAmount;

    @FieldDoc(description = "退款金额")
    private BigDecimal refundAmount;

    @FieldDoc(description = "佣金金额")
    private BigDecimal commissionAmount;

    @FieldDoc(description = "渠道金额（手续费）")
    private BigDecimal channelAmount;


    public String getSettleAmountString() {
        return this.bigDecimal2String(this.settleAmount);
    }


    public void setSettleAmountString(String settleAmount) {
        this.settleAmount = this.string2BigDecimal(settleAmount);
    }


    public Long getSettleTimeLong() {
        return this.date2Long(this.settleTime);
    }


    public void setSettleTimeLong(Long settleTime) {
        this.settleTime = this.long2Date(settleTime);
    }


    public Long getStartCycleTimeLong() {
        return this.date2Long(this.startCycleTime);
    }


    public void setStartCycleTimeLong(Long startCycleTime) {
        this.startCycleTime = this.long2Date(startCycleTime);
    }


    public Long getEndCycleTimeLong() {
        return this.date2Long(this.endCycleTime);
    }


    public void setEndCycleTimeLong(Long endCycleTime) {
        this.endCycleTime = this.long2Date(endCycleTime);
    }


    public String getOrderAmountString() {
        return this.bigDecimal2String(this.orderAmount);
    }


    public void setOrderAmountString(String orderAmount) {
        this.orderAmount = this.string2BigDecimal(orderAmount);
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


    public String getCommissionAmountString() {
        return this.bigDecimal2String(this.commissionAmount);
    }


    public void setCommissionAmountString(String commissionAmount) {
        this.commissionAmount = this.string2BigDecimal(commissionAmount);
    }


    public String getChannelAmountString() {
        return this.bigDecimal2String(this.channelAmount);
    }


    public void setChannelAmountString(String channelAmount) {
        this.channelAmount = this.string2BigDecimal(channelAmount);
    }
}
