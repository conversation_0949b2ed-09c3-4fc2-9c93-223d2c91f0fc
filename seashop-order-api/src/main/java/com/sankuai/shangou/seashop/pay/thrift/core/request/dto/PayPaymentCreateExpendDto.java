package com.sankuai.shangou.seashop.pay.thrift.core.request.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description: 微信、银行卡支付时的扩展参数
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "创建支付时的扩展参数")
public class PayPaymentCreateExpendDto {

    /**
     * 小程序支付必传
     */
    @FieldDoc(description = "用户openId")
    private String openId;

    /**
     *  b2b b2c 参数
     */
    @FieldDoc(description = "个人网银支持的银行bank_code")
    private String acctIssrId;

    @FieldDoc(description = "银行卡类型：debit-借记卡；credit-贷记卡(默认借记卡，)")
    private String cardType;

    @FieldDoc(description = "发起支付的用户端ip")
    private String clientIp;

    @FieldDoc(description = "商户前端页面地址，支付成功或失败时，会向该地址跳转")
    private String callbackUrl;


}
