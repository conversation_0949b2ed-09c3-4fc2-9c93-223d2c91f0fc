package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPayChannelReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayMethodResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * 主要应用于小程序端和PC端对订单发起支付时的相关查询场景
 */
@FeignClient(name = "himall-order", contextId = "OrderPayQueryFeign", path = "/himall-order/orderPayQuery", url = "${himall-order.dev.url:}")
public interface OrderPayQueryFeign {

    /**
     * 获取PC端的支付渠道
     * 小程序目前只支持微信，且返回的数据不一样，接口分开
     */
    @PostMapping(value = "/queryPcPayChannel", consumes = "application/json")
    ResultDto<OrderPayMethodResp> queryPcPayChannel(@RequestBody QueryPayChannelReq queryReq) throws TException;

    /**
     * 确认支付结果
     */
    @PostMapping(value = "/confirmPayResult", consumes = "application/json")
    ResultDto<Boolean> confirmPayResult(@RequestBody QueryPayChannelReq queryReq)  throws TException;

}
