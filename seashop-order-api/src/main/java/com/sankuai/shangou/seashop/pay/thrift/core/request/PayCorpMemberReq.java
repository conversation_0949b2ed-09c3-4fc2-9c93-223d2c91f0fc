package com.sankuai.shangou.seashop.pay.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description: 汇付创建企业用户对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "汇付创建企业用户对象")
public class PayCorpMemberReq extends PayBaseReq {

    /**
     * 请求订单号
     */
    @FieldDoc(description = "请求订单号")
    private String orderNo;

    /**
     * 企业名称
     * 企业名称不能为空
     */
    @FieldDoc(description = "企业名称", requiredness = Requiredness.REQUIRED)
    private String name;

    /**
     * 省份编码
     */
    @FieldDoc(description = "省份编码", requiredness = Requiredness.REQUIRED)
    private String provCode;

    /**
     * 地区编码
     */
    @FieldDoc(description = "地区编码", requiredness = Requiredness.REQUIRED)
    private String areaCode;

    /**
     * 统一社会信用码
     */
    @FieldDoc(description = "统一社会信用码", requiredness = Requiredness.REQUIRED)
    private String socialCreditCode;

    /**
     * 统一社会信用证有效期
     */
    @FieldDoc(description = "统一社会信用证有效期", requiredness = Requiredness.REQUIRED)
    private String socialCreditCodeExpires;

    /**
     * 经营范围
     */
    @FieldDoc(description = "经营范围", requiredness = Requiredness.REQUIRED)
    private String businessScope;
    /**
     * 法人姓名
     */
    @FieldDoc(description = "法人姓名", requiredness = Requiredness.REQUIRED)
    private String legalPerson;
    /**
     * 法人身份证号码
     */
    @FieldDoc(description = "法人身份证号码", requiredness = Requiredness.REQUIRED)
    private String legalCertId;
    /**
     * 法人身份证有效期
     */
    @FieldDoc(description = "法人身份证有效期", requiredness = Requiredness.REQUIRED)
    private String legalCertIdExpires;

    /**
     * 法人手机号
     */
    @FieldDoc(description = "法人手机号", requiredness = Requiredness.REQUIRED)
    private String legalMp;
    /**
     * 企业地址
     */
    @FieldDoc(description = "企业地址", requiredness = Requiredness.REQUIRED)
    private String address;
    /**
     * 邮编
     */
    @FieldDoc(description = "邮编")
    private String zipCode;
    /**
     * 企业电话
     */
    @FieldDoc(description = "企业电话")
    private String telphone;
    /**
     * 企业邮箱
     */
    @FieldDoc(description = "企业邮箱")
    private String email;
    /**
     * 上传附件，传入的中文文件名称为 UTF-8 字符集 URLEncode 编码后的字符串。内容须包含三证合一证件照、法人身份证正面照、法人身份证反面照、开户银行许可证照。 压缩 zip包后上传，最大限制为 9 M。
     */
    @FieldDoc(description = "上传附件", requiredness = Requiredness.REQUIRED)
    private String attachFile;


    /**
     * 银行编码，详见附录 银行代码，银行账户类型对公时，必填
     */
    @FieldDoc(description = "银行编码", requiredness = Requiredness.REQUIRED)
    private String bankCode;

    /**
     * 银行账户类型：1-对公；2-对私，如果需要自动开结算账户，本字段必填
     */
    @FieldDoc(description = "银行账户类型", requiredness = Requiredness.REQUIRED)
    private String bankAcctType;

    /**
     * 银行卡号，如果需要自动开结算账户，本字段必填
     */
    @FieldDoc(description = "银行卡号", requiredness = Requiredness.REQUIRED)
    private String cardNo;

    /**
     * 银行卡对应的户名，如果需要自动开结算账户，本字段必填
     */
    @FieldDoc(description = "银行卡对应的户名", requiredness = Requiredness.REQUIRED)
    private String cardName;

    /**
     * 异步通知地址，url为http/https路径，服务器POST回调，URL 上请勿附带参数
     */
    @FieldDoc(description = "异步通知地址")
    private String notifyUrl;

    @Override
    public void checkParameter() {
        super.checkParameter();
        if (StrUtil.isBlank(this.name)) {
            throw new InvalidParamException("企业名称不能为空");
        }
        if (StrUtil.isBlank(this.provCode)) {
            throw new InvalidParamException("省份编码不能为空");
        }
        if (StrUtil.isBlank(this.areaCode)) {
            throw new InvalidParamException("地区编码不能为空");
        }
        if (StrUtil.isBlank(this.socialCreditCode)) {
            throw new InvalidParamException("统一社会信用码不能为空");
        }
        if (StrUtil.isBlank(this.socialCreditCodeExpires)) {
            throw new InvalidParamException("统一社会信用证有效期不能为空");
        }
        if (StrUtil.isBlank(this.businessScope)) {
            throw new InvalidParamException("经营范围不能为空");
        }
        if (StrUtil.isBlank(this.legalPerson)) {
            throw new InvalidParamException("法人姓名不能为空");
        }
        if (StrUtil.isBlank(this.legalCertId)) {
            throw new InvalidParamException("法人身份证号码不能为空");
        }
        if (StrUtil.isBlank(this.legalCertIdExpires)) {
            throw new InvalidParamException("法人身份证有效期不能为空");
        }
        if (StrUtil.isBlank(this.legalMp)) {
            throw new InvalidParamException("法人手机号不能为空");
        }
        if (StrUtil.isBlank(this.address)) {
            throw new InvalidParamException("企业地址不能为空");
        }
        if (StrUtil.isBlank(this.attachFile)) {
            throw new InvalidParamException("上传附件不能为空");
        }
        if (StrUtil.isBlank(this.bankCode)) {
            throw new InvalidParamException("银行编码不能为空");
        }
        if (StrUtil.isBlank(this.bankAcctType)) {
            throw new InvalidParamException("银行账户类型不能为空");
        }
        if (StrUtil.isBlank(this.cardNo)) {
            throw new InvalidParamException("银行卡号不能为空");
        }
        if (StrUtil.isBlank(this.cardName)) {
            throw new InvalidParamException("银行卡对应的户名不能为空");
        }
    }


}
