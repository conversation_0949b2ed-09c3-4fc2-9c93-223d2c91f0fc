package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 审核状态，退款可能会经过供应商以及平台的审核
 * 目前数据表是供应商和平台分两个字段，这里枚举放一起
 * <AUTHOR>
 */
@ThriftEnum
public enum RefundAuditStatusEnum {

    // 待供应商审核
    WAIT_SUPPLIER_AUDIT(1, "待供应商审核"),
    // 待买家寄货
    WAIT_BUYER_SEND(2, "待买家寄货"),
    // 待供应商收货
    WAIT_SUPPLIER_RECEIVE(3, "待供应商收货"),
    // 供应商拒绝
    SUPPLIER_REFUSE(4, "供应商拒绝"),
    // 供应商通过审核
    SUPPLIER_PASS(5, "供应商通过审核"),
    // 待平台确认
    WAIT_PLATFORM_CONFIRM(6, "待平台确认"),
    // 退款成功
    REFUND_SUCCESS(7, "退款成功"),
    // 平台驳回
    PLATFORM_REFUSE(8, "平台驳回"),
    // 平台确认通过，退款中
    PLATFORM_PASS(9, "退款中"),


    ;

    private final Integer code;
    @Getter
    private final String desc;

    RefundAuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static RefundAuditStatusEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .map(RefundAuditStatusEnum::getDesc)
                .orElse(null);
    }

    /**
     * 是否处于审核中状态，包含待供应商审核和待平台确认两种状态
     * <AUTHOR>
     * @param sellerCode
     * boolean
     */
    public static boolean underAudit(Integer sellerCode, Integer platformCode) {
        return WAIT_SUPPLIER_AUDIT.getCode().equals(sellerCode) ||
                (SUPPLIER_PASS.getCode().equals(sellerCode) && WAIT_PLATFORM_CONFIRM.getCode().equals(platformCode));
    }

    /**
     * 基于供应商状态和平台状态获取当前实际的审核状态
     * <AUTHOR>
     * @param sellerCode
     * @param platformCode
     */
    public static RefundAuditStatusEnum valueOf(Integer sellerCode, Integer platformCode) {
        // 如果供应商状态不是通过，则代表还是供应商处理中
        if (sellerCode < SUPPLIER_PASS.getCode()) {
            return valueOf(sellerCode);
        }
        return valueOf(platformCode);
    }

}
