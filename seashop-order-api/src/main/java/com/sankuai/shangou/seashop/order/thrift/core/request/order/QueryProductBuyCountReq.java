package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "查询商品购买数量入参")
public class QueryProductBuyCountReq extends BaseThriftDto {

    @FieldDoc(description = "用户ID")
    private Long userId;
    @FieldDoc(description = "商品ID列表")
    private List<Long> productIdList;


}
