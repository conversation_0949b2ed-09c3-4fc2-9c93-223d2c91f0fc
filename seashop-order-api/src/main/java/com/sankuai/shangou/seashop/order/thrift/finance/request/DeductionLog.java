package com.sankuai.shangou.seashop.order.thrift.finance.request;

import com.facebook.swift.codec.ThriftStruct;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/17 15:49
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ThriftStruct
@ToString
@TypeDoc(description = "保证金扣减日志对象")
public class DeductionLog extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    @PrimaryField
    private Long id;

    @FieldDoc(description = "可用金额")
    @ExaminField(description = "可用金额")
    private BigDecimal currentBalance;

    @FieldDoc(description = "店铺名称")
    @ExaminField(description = "店铺名称")
    @PrimaryField
    private String shopName;


    public Long getId() {
        return id;
    }


    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(BigDecimal currentBalance) {
        this.currentBalance = currentBalance;
    }


    public String getCurrentBalanceString() {
        return this.bigDecimal2String(this.currentBalance);
    }


    public void setCurrentBalanceString(String currentBalance) {
        this.currentBalance = this.string2BigDecimal(currentBalance);
    }


    public String getShopName() {
        return shopName;
    }


    public void setShopName(String shopName) {
        this.shopName = shopName;
    }
}
