package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.order.thrift.core.enums.SearchTimeTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.Date;

/**
 * 查询erp订单入参
 *
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "分页查询erp订单入参")
public class QueryErpPageOrderReq extends BasePageReq {
    /**
     * 店铺ID
     */
    @FieldDoc(
            name = "shopId",
            description = "店铺ID",
            requiredness = Requiredness.REQUIRED
    )
    private Long shopId;
    /**
     * 订单状态
     */
    @FieldDoc(
            name = "status",
            description = "订单状态",
            requiredness = Requiredness.OPTIONAL
    )
    private OrderStatusEnum status;
    /**
     * 平台订单号
     */
    @FieldDoc(
            name = "orderId",
            description = "平台订单号",
            requiredness = Requiredness.OPTIONAL
    )
    private String orderId;
    /**
     * 开始日期时间
     */
    @Setter
    @Getter
    @FieldDoc(
            name = "startTime",
            description = "开始日期时间",
            requiredness = Requiredness.OPTIONAL
    )
    private Date startTime;
    /**
     * 结束日期时间
     */
    @Setter
    @Getter
    @FieldDoc(
            name = "endTime",
            description = "结束日期时间",
            requiredness = Requiredness.OPTIONAL
    )
    private Date endTime;
    /**
     * 根据创建时间还是修改时间查询 默认 创建时间
     */
    @FieldDoc(
            name = "timeType",
            description = "根据创建时间还是修改时间查询 默认 创建时间",
            requiredness = Requiredness.OPTIONAL
    )
    private SearchTimeTypeEnum timeType;
    /**
     * 订单来源
     */
    //@FieldDoc(
    //        name = "orderSources",
    //        description = "订单来源",
    //        requiredness = Requiredness.OPTIONAL
    //)
    //private List<Integer> orderSources;
    @Override
    public void checkParameter() {
        if (StringUtils.isNotBlank(orderId)) {
            return;
        }
        super.checkParameter();
    }


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


    //
    //public List<Integer> getOrderSources() {
    //    return orderSources;
    //}
    //
    //
    //public void setOrderSources(List<Integer> orderSources) {
    //    this.orderSources = orderSources;
    //}
}
