package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/04 9:49
 */
@Data
@ToString
@TypeDoc(description = "保存商品评价入参")
public class SaveProductCommentReq extends BaseParamReq {

    @FieldDoc(description = "子订单id", requiredness = Requiredness.REQUIRED)
    private Long subOrderId;

    @FieldDoc(description = "评分 1-5", requiredness = Requiredness.REQUIRED)
    private Integer reviewMark;

    @FieldDoc(description = "评价内容 1000字", requiredness = Requiredness.REQUIRED)
    private String reviewContent;

    @FieldDoc(description = "评价图片 最多五张", requiredness = Requiredness.NONE)
    private List<String> commentImageList;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(subOrderId == null || subOrderId <= 0, "子订单id不能为空");
        AssertUtil.throwInvalidParamIfTrue(reviewMark == null, "商品评分不能为空");
        AssertUtil.throwInvalidParamIfTrue(reviewMark < 1 || reviewMark > 5, "商品评分只能在1-5分之间");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(reviewContent), "评价内容不能为空");
        AssertUtil.throwInvalidParamIfTrue(reviewContent.length() > 1000, "评价内容不能超过1000字");
        if (CollectionUtils.isNotEmpty(commentImageList)) {
            AssertUtil.throwInvalidParamIfTrue(commentImageList.size() > 5, "评价图片最多只能上传5张");
        }
    }


}
