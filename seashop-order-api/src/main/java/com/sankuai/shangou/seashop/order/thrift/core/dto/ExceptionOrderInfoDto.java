package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TypeDoc(description = "订单收货地址对象")
@ToString
public class ExceptionOrderInfoDto extends BaseThriftDto {


    /**
     * 主键ID
     */
    @FieldDoc(description = "主键ID")
    private Long id;

    /**
     * 订单编号
     */
    @FieldDoc(description = "订单编号")
    private String orderId;

    /**
     * 支付批次号，多笔订单同时支付时批次号相同
     */
    @FieldDoc(description = "支付批次号，多笔订单同时支付时批次号相同")
    private String batchNo;

    /**
     * 支付渠道对应的唯一标识。支付流水号
     */
    @FieldDoc(description = "支付渠道对应的唯一标识。支付流水号")
    private String payNo;

    /**
     * 支付时间
     */
    @FieldDoc(description = "支付时间")
    private Date payTime;

    /**
     * 支付金额
     */
    @FieldDoc(description = "支付金额")
    private BigDecimal payAmount;

    /**
     * 异常类型，1，重复支付；2，超时关闭；
     */
    @FieldDoc(description = "异常类型，1，重复支付；2，超时关闭；")
    private Integer errorType;
    /**
     * 异常类型描述
     */
    @FieldDoc(description = "异常类型描述")
    private String errTypeDesc;

    /**
     * 退款时间
     */
    @FieldDoc(description = "退款时间")
    private Date refundTime;

    /**
     * 退款状态。0:待退款；1:退款中；2:退款完成；3:退款失败；
     */
    @FieldDoc(description = "退款状态。0:待退款；1:退款中；2:退款完成；3:退款失败；")
    private Integer refundStatus;
    /**
     * 退款状态描述
     */
    @FieldDoc(description = "退款状态描述")
    private String refundStatusDesc;

    /**
     * 退款失败原因
     */
    @FieldDoc(description = "退款失败原因")
    private String refundFailReason;

    /**
     * 退款批次号
     */
    @FieldDoc(description = "退款批次号")
    private String refundBatchNo;

    /**
     * 退款操作员
     */
    @FieldDoc(description = "退款操作员")
    private String refundManager;


    public Long getPayTimeLong() {
        return this.date2Long(this.payTime);
    }


    public void setPayTimeLong(Long payTime) {
        this.payTime = this.long2Date(payTime);
    }


    public String getPayAmountString() {
        return this.bigDecimal2String(this.payAmount);
    }


    public void setPayAmountString(String payAmount) {
        this.payAmount = this.string2BigDecimal(payAmount);
    }


    public Long getRefundTimeLong() {
        return this.date2Long(this.refundTime);
    }


    public void setRefundTimeLong(Long refundTime) {
        this.refundTime = this.long2Date(refundTime);
    }


}
