package com.sankuai.shangou.seashop.order.thrift.core.enums.event;

/**
 * <AUTHOR>
 */
public enum OrderMessageEventEnum {

    // 创建订单
    CREATE_ORDER("创建订单"),
    // 发起支付
    INITIAL_PAYMENT("发起支付"),
    // 支付成功
    PAY_SUCCESS("支付成功"),
    // 支付失败
    PAY_FAIL("支付失败"),
    // 订单取消
    CANCEL_ORDER("订单取消"),
    // 订单关闭
    CLOSE_ORDER("订单关闭"),
    // 订单完成
    COMPLETE_ORDER("订单完成"),
    // 订单发货
    DELIVERY_ORDER("订单发货"),
    // 订单评价
    COMMENT_ORDER("订单评价"),
    // 订单追评
    APPEND_COMMENT("订单追评"),
    // 订单改价
    UPDATE_ORDER_PRICE("订单改价"),
    // 订单收货
    //RECEIVE_ORDER("订单收货"),

    ;


    private final String desc;

    OrderMessageEventEnum(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
