package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单与明细的展开对象，一个明细一个对象")
public class OrderAndItemInfoDto extends BaseThriftDto {

    @FieldDoc(description = "订单编号")
    private String orderId;
    @FieldDoc(description = "订单来源")
    private String platformDesc;
    @FieldDoc(description = "店铺")
    private String shopName;
    @FieldDoc(description = "买家ID")
    private Long userId;
    @FieldDoc(description = "下单时间")
    private String orderDateStr;
    @FieldDoc(description = "付款时间")
    private String payDateStr;
    @FieldDoc(description = "完成时间")
    private String finishDateStr;
    @FieldDoc(description = "支付方式")
    private String payMethodDesc;
    @FieldDoc(description = "交易单号")
    private String gatewayOrderId;
    @FieldDoc(description = "商品总额")
    private BigDecimal productTotalAmount;
    @FieldDoc(description = "运费")
    private BigDecimal freight;
    @FieldDoc(description = "税金")
    private BigDecimal tax;
    @FieldDoc(description = "优惠券抵扣")
    private BigDecimal couponAmount;
    @FieldDoc(description = "满额减")
    private BigDecimal moneyOffAmount;
    @FieldDoc(description = "门店改价")
    private BigDecimal updateAmount;
    @FieldDoc(description = "订单实付总额")
    private BigDecimal totalAmount;
    @FieldDoc(description = "平台佣金")
    private BigDecimal commissionTotalAmount;
    @FieldDoc(description = "订单状态")
    private String orderStatusDesc;
    @FieldDoc(description = "买家留言")
    private String userRemark;
    @FieldDoc(description = "收货人")
    private String shipTo;
    @FieldDoc(description = "手机号码")
    private String cellPhone;
    @FieldDoc(description = "收货地址")
    private String address;
    @FieldDoc(description = "商品ID")
    private Long productId;
    @FieldDoc(description = "SkuID")
    private String skuId;
    @FieldDoc(description = "规格ID")
    private Long skuAutoId;
    @FieldDoc(description = "一级分类")
    private String cateLevel1Name;
    @FieldDoc(description = "二级分类")
    private String cateLevel2Name;
    @FieldDoc(description = "三级分类")
    private String cateLevel3Name;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "SKU 级别的货号")
    private String skuCode;
    @FieldDoc(description = "UPC/69码")
    private String barCode;
    @FieldDoc(description = "单价")
    private BigDecimal salePrice;
    @FieldDoc(description = "数量")
    private Long quantity;
    @FieldDoc(description = "买家")
    private String userName;
    @FieldDoc(description = "订单类型")
    private String orderTypeDesc;
    @FieldDoc(description = "订单明细ID")
    private Long itemId;
    @FieldDoc(description = "发票类型")
    private String invoiceTypeDesc;
    @FieldDoc(description = "发票抬头")
    private String invoiceTitle;
    @FieldDoc(description = "税号")
    private String invoiceCode;
    @FieldDoc(description = "发票内容")
    private String invoiceContext;
    @FieldDoc(description = "注册地址")
    private String registerAddress;
    @FieldDoc(description = "注册电话")
    private String registerPhone;
    @FieldDoc(description = "开户银行")
    private String bankName;
    @FieldDoc(description = "银行帐号")
    private String bankNo;
    @FieldDoc(description = "收票人姓名")
    private String realName;
    @FieldDoc(description = "收票人手机号")
    private String invoiceCellPhone;
    @FieldDoc(description = "收票人邮箱")
    private String email;
    @FieldDoc(description = "收票人地址地址")
    private String invoiceFullAddress;
    /**
     * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
     */
    @FieldDoc(description = "发票类型")
    private Integer invoiceType;
    /**
     * 订单状态
     */
    @FieldDoc(description = "订单状态")
    private Integer orderStatus;
    /**
     * 订单类型
     */
    @FieldDoc(description = "订单类型")
    private Integer orderType;


    public String getProductTotalAmountString() {
        return this.bigDecimal2String(this.productTotalAmount);
    }


    public void setProductTotalAmountString(String productTotalAmount) {
        this.productTotalAmount = this.string2BigDecimal(productTotalAmount);
    }


    public String getFreightString() {
        return this.bigDecimal2String(this.freight);
    }


    public void setFreightString(String freight) {
        this.freight = this.string2BigDecimal(freight);
    }


    public String getTaxString() {
        return this.bigDecimal2String(this.tax);
    }


    public void setTaxString(String tax) {
        this.tax = this.string2BigDecimal(tax);
    }


    public String getCouponAmountString() {
        return this.bigDecimal2String(this.couponAmount);
    }


    public void setCouponAmountString(String couponAmount) {
        this.couponAmount = this.string2BigDecimal(couponAmount);
    }


    public String getMoneyOffAmountString() {
        return this.bigDecimal2String(this.moneyOffAmount);
    }


    public void setMoneyOffAmountString(String moneyOffAmount) {
        this.moneyOffAmount = this.string2BigDecimal(moneyOffAmount);
    }


    public String getUpdateAmountString() {
        return this.bigDecimal2String(this.updateAmount);
    }


    public void setUpdateAmountString(String updateAmount) {
        this.updateAmount = this.string2BigDecimal(updateAmount);
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


    public String getCommissionTotalAmountString() {
        return this.bigDecimal2String(this.commissionTotalAmount);
    }


    public void setCommissionTotalAmountString(String commissionTotalAmount) {
        this.commissionTotalAmount = this.string2BigDecimal(commissionTotalAmount);
    }


    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }


    public void setSalePriceString(String salePrice) {
        this.salePrice = this.string2BigDecimal(salePrice);
    }


}
