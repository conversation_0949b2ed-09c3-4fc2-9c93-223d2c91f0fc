package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "查询订单明细请求入参")
public class QueryOrderDetailReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
    /**
     * 查询来源。1-商家小程序；2-商家PC端；3-卖家PC端；4-平台PC端
     */
    @FieldDoc(description = "查询来源。1-商家小程序；2-商家PC端；3-卖家PC端；4-平台PC端")
    private OrderQueryFromEnum queryFrom;

    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (this.orderId == null) {
            throw new IllegalArgumentException("orderId不能为空");
        }
    }


}
