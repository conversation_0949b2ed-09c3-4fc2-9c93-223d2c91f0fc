package com.sankuai.shangou.seashop.order.thrift.core.enums.event;

import java.util.Arrays;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

import lombok.Getter;

/**
 * <AUTHOR>
 */
public class ThirdEvent {

    @ThriftEnum
    public enum EventTypeEnum {

        ORDER_CREATE_EVENT(0, "订单创建"),
        ORDER_PAY_EVENT(1, "订单支付"),
        ORDER_CONFIRM_RECEIVE_EVENT(2, "订单确认收货"),
        ORDER_CANCEL_EVENT(3, "订单取消"),
        ORDER_SETTLEMENT_EVENT(4, "订单结算"),
        ORDER_DELIVER_EVENT(5, "订单发货"),
        PRODUCT_CREATE_EVENT(10, "商品创建"),
        PRODUCT_PASS_EVENT(11, "商品审核通过"),
        PRODUCT_REFUSE_EVENT(12, "商品审核拒绝"),
        PRODUCT_ON_EVENT(13, "商品上架"),
        PRODUCT_OFF_EVENT(14, "商品下架"),
        PRODUCT_DELETE_EVENT(15, "商品删除"),
        PRODUCT_IMAGE_UPDATE_EVENT(16, "商品图片更新"),
        PRODUCT_UPDATE_EVENT(19, "商品更新"),
        REFUND_CREATE_EVENT(20, "售后单创建"),
        REFUND_CANCEL_EVENT(21, "售后单取消"),
        REFUND_PASS_EVENT(22, "确认退款"),
        REFUND_AUDIT_PASS_EVENT(23, "售后单审核通过"),
        REFUND_CONFIRM_RECEIVE_EVENT(24, "售后单确认收货"),
        REFUND_SUPPLIER_RECEIVE(25, "待供应商收货"),
        REFUND_SUCCESS(26, "退款成功"),
        ;

        private final Integer code;
        @Getter
        private final String desc;

        EventTypeEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static EventTypeEnum valueOf(Integer code) {
            return Arrays.stream(values())
                    .filter(eventType -> eventType.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }

        @ThriftEnumValue
        public Integer getCode() {
            return code;
        }
    }

    /**
     * 发送目标 1：旺店通 2：聚水潭 3：网店管家 4：吉客云
     *
     * <AUTHOR>
     */
    @ThriftEnum
    public enum SendTargetEnum {

        MT(0, "牵牛花"),
        WDT(1, "旺店通"),
        JST(2, "聚水潭"),
        WDGJ(3, "网店管家"),
        JKY(4, "吉客云"),
        YJP(5, "易久批"),
        ;


        private final Integer code;
        @Getter
        private final String desc;

        SendTargetEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static SendTargetEnum valueOf(Integer code) {
            return Arrays.stream(values())
                    .filter(sendTarget -> sendTarget.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }

        @ThriftEnumValue
        public Integer getCode() {
            return code;
        }
    }

    /**
     * 推送状态 0:待推送 1：推送成功 2：推送失败，3：推送中
     *
     * <AUTHOR>
     */
    @ThriftEnum
    public enum SendStateEnum {

        TO_SEND(0, "待推送"),
        SENDING(3, "推送中"),
        SEND_SUCCESS(1, "推送成功"),
        SEND_FAIL(2, "推送失败"),
        ;


        private final Integer code;
        @Getter
        private final String desc;

        SendStateEnum(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static SendStateEnum valueOf(Integer code) {
            return Arrays.stream(values())
                    .filter(sendState -> sendState.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }

        @ThriftEnumValue
        public Integer getCode() {
            return code;
        }
    }


}
