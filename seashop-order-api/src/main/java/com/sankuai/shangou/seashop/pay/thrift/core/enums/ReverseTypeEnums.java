package com.sankuai.shangou.seashop.pay.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.AllArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/23/022
 * @description: 退款类型
 */
@ThriftEnum
@AllArgsConstructor
public enum ReverseTypeEnums {

    /**
     * 未结算退款(支付撤销)
     */
    REVERSE_NO_SETTLEMENT(0, "未结算退款"),
    /**
     * 已结算退款（支付确认之后的退款）
     */
    REVERSE_SUCCESS(1, "已结算退款");


    /**
     * 返回状态
     */
    private Integer type;

    /**
     * 显示名
     */
    private String displayName;

    @ThriftEnumValue
    public Integer getType() {
        return type;
    }

    public String getDisplayName() {
        return displayName;
    }
}
