package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 营销快照业务类型
 * <AUTHOR>
 */
@ThriftEnum
public enum OrderPromotionSnapshotTypeEnum {

    ORDER(1, "订单"),
    PRODUCT(2, "商品");

    private final Integer code;
    @Getter
    private final String desc;

    OrderPromotionSnapshotTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static OrderPromotionSnapshotTypeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(promotionSnapshotType -> promotionSnapshotType.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
