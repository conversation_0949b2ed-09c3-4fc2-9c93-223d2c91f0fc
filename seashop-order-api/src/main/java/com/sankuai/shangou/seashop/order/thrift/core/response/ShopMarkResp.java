package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Data
@ToString
@TypeDoc(description = "店铺评分返回值")
@NoArgsConstructor
@AllArgsConstructor
public class ShopMarkResp extends BaseThriftDto {

    /**
     * 店铺ID
     */
    @FieldDoc(description = "店铺ID")
    private Long shopId;

    /**
     * 评分数量
     */
    @FieldDoc(description = "评分数量")
    private Long markCount;

    /**
     * 包装评分
     */
    @FieldDoc(description = "包装评分")
    private BigDecimal packMark;

    /**
     * 服务评分
     */
    @FieldDoc(description = "服务评分")
    private BigDecimal serviceMark;

    /**
     * 综合评分
     */
    @FieldDoc(description = "综合评分")
    private BigDecimal comprehensiveMark;


    public String getPackMarkString() {
        return this.bigDecimal2String(this.packMark);
    }


    public void setPackMarkString(String packMark) {
        this.packMark = this.string2BigDecimal(packMark);
    }


    public String getServiceMarkString() {
        return this.bigDecimal2String(this.serviceMark);
    }


    public void setServiceMarkString(String serviceMark) {
        this.serviceMark = this.string2BigDecimal(serviceMark);
    }


    public String getComprehensiveMarkString() {
        return this.bigDecimal2String(this.comprehensiveMark);
    }


    public void setComprehensiveMarkString(String comprehensiveMark) {
        this.comprehensiveMark = this.string2BigDecimal(comprehensiveMark);
    }
}
