package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 16:19
 */
@Data
@TypeDoc(description = "打印订单项出参")
public class PrintOrderItemDataResp extends BaseThriftDto {

    @FieldDoc(description = "序号")
    private Integer num;

    @FieldDoc(description = "订单号")
    private String orderId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "规格(color+size+version)")
    private String specName;

    @FieldDoc(description = "数量")
    private Long quantity;

    @FieldDoc(description = "单价")
    private BigDecimal salePrice;

    @FieldDoc(description = "总价")
    private BigDecimal realTotalPrice;


    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }


    public void setSalePriceString(String salePrice) {
        this.salePrice = this.string2BigDecimal(salePrice);
    }


    public String getRealTotalPriceString() {
        return this.bigDecimal2String(this.realTotalPrice);
    }


    public void setRealTotalPriceString(String realTotalPrice) {
        this.realTotalPrice = this.string2BigDecimal(realTotalPrice);
    }
}
