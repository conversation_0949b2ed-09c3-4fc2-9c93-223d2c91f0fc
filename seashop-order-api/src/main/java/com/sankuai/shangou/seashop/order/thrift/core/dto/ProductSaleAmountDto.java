package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商品销售金额对象")
public class ProductSaleAmountDto extends BaseThriftDto {

    @FieldDoc(description = "排名")
    private Integer rank;
    @FieldDoc(description = "商品id")
    private Long productId;
    @FieldDoc(description = "商品名称")
    private String productName;
    @FieldDoc(description = "销售金额")
    private BigDecimal saleAmount;


    public String getSaleAmountString() {
        return this.bigDecimal2String(this.saleAmount);
    }


    public void setSaleAmountString(String saleAmount) {
        this.saleAmount = this.string2BigDecimal(saleAmount);
    }
}
