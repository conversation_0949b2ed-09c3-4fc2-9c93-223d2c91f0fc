package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayCreateResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreatePaymentReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description: 保证金相关操作服务
 */
@FeignClient(name = "himall-order", contextId = "CashDepositCmdFeign", path = "/himall-order/finance/cashDeposit", url = "${himall-order.dev.url:}")
public interface CashDepositCmdFeign {

    /**
     * 保证金发起付款
     *
     */
    @PostMapping(value = "createPayment", consumes = "application/json")
    ResultDto<OrderPayCreateResp> createPayment(@RequestBody CreatePaymentReq request) throws TException;
}
