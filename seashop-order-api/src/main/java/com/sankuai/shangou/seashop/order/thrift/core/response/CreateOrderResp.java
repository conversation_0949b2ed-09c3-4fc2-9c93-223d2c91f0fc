package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "创建订单返回对象")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class CreateOrderResp {

    @FieldDoc(description = "订单ID。用于后续支付")

    private List<String> orderIdList;

    private List<String> allOrderIdList;

}
