package com.sankuai.shangou.seashop.pay.thrift.core.request.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:物流信息项
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShippingItem {
    @FieldDoc(description = "tracking_no")
    private String tracking_no; // 物流单号（快递必填）

    @FieldDoc(description = "express_company")
    private String express_company; // 物流公司编码（快递必填）

    @FieldDoc(description = "item_desc")
    private String item_desc; // 商品描述（必填，≤120字符）

    @FieldDoc(description = "contact")
    private Contact contact; // 联系方式（顺丰必填）
}
