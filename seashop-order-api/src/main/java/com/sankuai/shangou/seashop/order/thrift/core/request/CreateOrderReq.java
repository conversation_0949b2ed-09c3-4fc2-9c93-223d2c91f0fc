package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopProductListDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderPlatformEnum;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 此对象用于用户提交创建订单请求
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "创建订单请求入参")
public class CreateOrderReq extends BaseParamReq {

    /**
     * 用户ID
     */
    @FieldDoc(description = "用户信息", requiredness = Requiredness.REQUIRED)
    private UserDto userInfo;
    /**
     * 唯一标识，用于幂等校验
     */
    @FieldDoc(description = "唯一标识，用于幂等校验", requiredness = Requiredness.REQUIRED)
    private String uniqueId;
    /**
     * 收货地址
     */
    @FieldDoc(description = "收货地址。这个场景下，交易服务会查询一次，这里直接传入", requiredness = Requiredness.REQUIRED)
    private ShippingAddressDto shippingAddress;
    /**
     * 所有店铺总金额
     */
    @FieldDoc(description = "所有店铺总金额", requiredness = Requiredness.REQUIRED)
    private BigDecimal totalAmount;
    /**
     * 按店铺分组的商品列表
     */
    @FieldDoc(description = "按店铺分组的商品列表", requiredness = Requiredness.REQUIRED)
    private List<ShopProductListDto> shopProductList;
    @FieldDoc(description = "限时购活动id")
    private Long flashSaleId;
    /**
     * 组合购活动id，组合购活动提交时字段有值
     */
    private Long collocationId;
    /**
     * 订单来源
     */
    //@FieldDoc(description = "订单来源。1：本系统；2：牵牛花")
    //private OrderSourceEnum orderSource;
    /**
     * 订单平台
     */
    @FieldDoc(description = "订单平台，orderSource=2时不用传。0：PC；2：小程序")
    private OrderPlatformEnum platform;
    /**
     * 是否立即购买
     */
    @FieldDoc(description = "是否立即购买")
    private Boolean whetherBuyNow;
    @FieldDoc(description = "创建时间，用接口入口时间。交易服务设置传入")
    private Date createTime;

    @Override
    public void checkParameter() {
        userInfo.checkParameter();
        if (uniqueId == null || uniqueId.isEmpty()) {
            throw new InvalidParamException("幂等标识不能为空");
        }
        if (shopProductList == null || shopProductList.isEmpty()) {
            throw new InvalidParamException("商品列表不能为空");
        }
        //if (orderSource == null) {
        //    throw new InvalidParamException("订单来源不能为空");
        //}
        //if (OrderSourceEnum.SELF.equals(orderSource) && platform == null) {
        //    throw new InvalidParamException("订单平台不能为空");
        //}
        if (this.flashSaleId != null && this.collocationId != null) {
            throw new InvalidParamException("限时购和组合购不能同时存在");
        }
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


    //
    //public OrderSourceEnum getOrderSource() {
    //    return orderSource;
    //}
    //
    //
    //public void setOrderSource(OrderSourceEnum orderSource) {
    //    this.orderSource = orderSource;
    //}


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }
}
