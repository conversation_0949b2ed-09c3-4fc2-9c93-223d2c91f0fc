package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "供应商修改运费入参")
public class UpdateFreightReq extends BaseParamReq {

    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;
    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "修改后的运费", requiredness = Requiredness.REQUIRED)
    private BigDecimal updatedFreight;
    @FieldDoc(description = "用户名", requiredness = Requiredness.OPTIONAL)
    private String userName;

    @Override
    public void checkParameter() {
        if (shopId == null || shopId <= 0) {
            throw new InvalidParamException("shopId不能为空");
        }
        if (orderId == null || orderId.isEmpty()) {
            throw new InvalidParamException("orderId不能为空");
        }
        if (updatedFreight == null || updatedFreight.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("请输入正确的运费");
        }
    }


    public String getUpdatedFreightString() {
        return this.bigDecimal2String(this.updatedFreight);
    }


    public void setUpdatedFreightString(String updatedFreight) {
        this.updatedFreight = this.string2BigDecimal(updatedFreight);
    }


}
