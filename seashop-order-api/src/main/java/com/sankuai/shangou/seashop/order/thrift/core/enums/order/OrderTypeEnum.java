package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 订单类型。0:正常购,1:组合购,2:限时购
 * <AUTHOR>
 */
@ThriftEnum
public enum OrderTypeEnum {

    NORMAL(0, "正常购买"),
    COLLOCATION(1, "组合购"),
    FLASH_SALE(2, "限时购");

    private final Integer code;
    @Getter
    private final String desc;

    OrderTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static OrderTypeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(orderType -> orderType.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(orderType -> orderType.getCode().equals(code))
                .findFirst()
                .map(OrderTypeEnum::getDesc)
                .orElse("");
    }

}
