package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 退款类型
 * <AUTHOR>
 */
@ThriftEnum
public enum RefundTypeEnum {

    // 仅退款
    ONLY_REFUND(1, "仅退款"),
    // 退货退款
    RETURN_AND_REFUND(2, "退货退款"),

    ;

    private final Integer code;
    @Getter
    private final String desc;

    RefundTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static RefundTypeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
