package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderAndItemInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderOperationLogResp;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.*;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.CountUserFlashSaleReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryProductBuyCountReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryUserOrderWayBillReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.*;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.OrderAndItemFlatListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.ProductBuyCountListResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR>
 * 主要应用于小程序端和PC端对订单的查询场景
 */
@FeignClient(name = "himall-order", contextId = "OrderQueryFeign", path = "/himall-order/orderQuery", url = "${himall-order.dev.url:}")
public interface OrderQueryFeign {

    /**
     * 商家分页查询订单
     */
    @PostMapping(value = "/pageQueryUserOrder", consumes = "application/json")
    ResultDto<BasePageResp<OrderInfoDto>> pageQueryUserOrder(@RequestBody QueryUserOrderReq queryReq) throws TException;

    /**
     * 卖家分页查询订单
     */
    @PostMapping(value = "/pageQuerySellerOrder", consumes = "application/json")
    ResultDto<BasePageResp<OrderInfoDto>> pageQuerySellerOrder(@RequestBody QuerySellerOrderReq queryReq) throws TException;

    /**
     * 平台分页查询订单
     */
    @PostMapping(value = "/pageQueryPlatformOrder", consumes = "application/json")
    ResultDto<BasePageResp<OrderInfoDto>> pageQueryPlatformOrder(@RequestBody QueryPlatformOrderReq queryReq) throws TException;

    /**
     * 查询订单详情
     */
    @PostMapping(value = "/queryDetail", consumes = "application/json")
    ResultDto<OrderDetailResp> queryDetail(@RequestBody QueryOrderDetailReq queryReq) throws TException;

    /**
     * 平台查询订单详情
     */
    @PostMapping(value = "/queryDetailForPlatform", consumes = "application/json")
    ResultDto<OrderDetailResp> queryDetailForPlatform(@RequestBody QueryOrderDetailReq queryReq) throws TException;

    /**
     * 获取订单的物流信息(包括收货地址)
     */
    @PostMapping(value = "/queryOrderWayBill", consumes = "application/json")
    ResultDto<OrderWayBillListResp> queryOrderWayBill(@RequestBody QueryOrderWayBillReq queryReq) throws TException;


    /**
     * 订单统计（财务管理首页统计）
     * 传店铺ID是基于店铺的统计，不传店铺ID是基于平台的统计
     */
    @PostMapping(value = "/getOrderStatistics", consumes = "application/json")
    ResultDto<OrderStatisticsResp> getOrderStatistics(@RequestBody OrderStatisticsReq request) throws TException;

    /**
     * 订单统计通过商家Id（财务管理首页统计）
     */
    @PostMapping(value = "/getOrderStatisticsByMember", consumes = "application/json")
    ResultDto<OrderStatisticsResp> getOrderStatisticsByMember(@RequestBody OrderStatisticsMemberReq request) throws TException;

    /**
     * 订单统计列表（财务管理首页统计）
     * 传店铺ID是基于店铺的统计，不传店铺ID是基于平台的统计
     */
    @PostMapping(value = "/getOrderStatisticsList", consumes = "application/json")
    ResultDto<OrderStatisticsListResp> getOrderStatisticsList(@RequestBody OrderStatisticsReq request) throws TException;

    /**
     * 查询订单操作日志
     */
    @GetMapping(value = "/queryOrderLog")
    ResultDto<List<OrderOperationLogResp>> queryOrderLog(@RequestParam String orderId) throws TException;


    /**
     * ERP分页查询订单
     */
    @PostMapping(value = "/pageQueryErpOrder", consumes = "application/json")
    ResultDto<BasePageResp<ErpOrderDetailResp>> pageQueryErpOrder(@RequestBody QueryErpPageOrderReq req) throws TException;

    /**
     * 查询erp订单详情
     */
    @PostMapping(value = "/queryErpDetail", consumes = "application/json")
    ResultDto<ErpOrderDetailResp> queryErpDetail(@RequestBody QueryErpOrderDetailReq req) throws TException;

    /**
     * 批量查询erp订单
     */
    @PostMapping(value = "/queryBatchErpOrder", consumes = "application/json")
    ResultDto<ErpBatchOrderListResp> queryBatchErpOrder(@RequestBody QueryBatchErpOrderReq req) throws TException;

    /**
     * 根据用户ID统计各订单状态的数量
     */
    @GetMapping(value = "/queryEachStatusCount")
    ResultDto<EachStatusCountResp> queryEachStatusCount(@RequestParam Long userId) throws TException;

    /**
     * 根据用户ID查询最近一个订单信息
     */
    @GetMapping(value = "/queryLastOrderInfo")
    ResultDto<OrderInfoDto> queryLastOrderInfo(@RequestParam Long userId) throws TException;


    /**
     * 统计商品维度的用户限时购购买数量
     */
    @PostMapping(value = "/countFlashSaleByProduct", consumes = "application/json")
    ResultDto<Long> countFlashSaleByProduct(@RequestBody CountUserFlashSaleReq req) throws TException;


    /**
     * 统计SKU维度的用户限时购购买数量
     */
    @PostMapping(value = "/countFlashSaleBySku", consumes = "application/json")
    ResultDto<Long> countFlashSaleBySku(@RequestBody CountUserFlashSaleReq req) throws TException;

    /**
     * 买家-导出订单
     */
    @PostMapping(value = "/exportForUser", consumes = "application/json")
    ResultDto<BasePageResp<OrderAndItemInfoDto>> exportForUser(@RequestBody QueryUserOrderReq queryReq) throws TException;

    /**
     * 卖家-导出订单
     */
    @PostMapping(value = "/exportForSeller", consumes = "application/json")
    ResultDto<BasePageResp<OrderAndItemInfoDto>> exportForSeller(@RequestBody QuerySellerOrderReq queryReq) throws TException;

    /**
     * 平台-导出订单
     */
    @PostMapping(value = "/exportForPlatform", consumes = "application/json")
    ResultDto<BasePageResp<OrderAndItemInfoDto>> exportForPlatform(@RequestBody QueryPlatformOrderReq queryReq) throws TException;

    /**
     * 商家-导出订单
     */
    @PostMapping(value = "/getScrollIdForUserExport", consumes = "application/json")
    ResultDto<OrderAndItemFlatListResp> getScrollIdForUserExport(@RequestBody QueryUserOrderReq queryReq) throws TException;

    /**
     * 供应商-导出订单
     */
    @PostMapping(value = "/getScrollIdForSellerExport", consumes = "application/json")
    ResultDto<OrderAndItemFlatListResp> getScrollIdForSellerExport(@RequestBody QuerySellerOrderReq queryReq) throws TException;

    /**
     * 平台-导出订单
     */
    @PostMapping(value = "/getScrollIdForPlatformExport", consumes = "application/json")
    ResultDto<OrderAndItemFlatListResp> getScrollIdForPlatformExport(@RequestBody QueryPlatformOrderReq queryReq) throws TException;

    /**
     * 清除滚动数据
     */
    @PostMapping(value = "/clearScrollId", consumes = "application/json")
    ResultDto<BaseResp> clearScrollId(@RequestBody EsScrollClearReq queryReq) throws TException;

    /**
     * 基于滚动查询订单和明细平铺展开数据
     */
    @PostMapping(value = "/listOrderAndItemFlatByScroll", consumes = "application/json")
    ResultDto<OrderAndItemFlatListResp> listOrderAndItemFlatByScroll(@RequestBody EsScrollQueryReq queryReq) throws TException;

    /**
     * 卖家-导出订单配货单
     */
    @PostMapping(value = "/exportOrderDistribution", consumes = "application/json")
    ResultDto<List<OrderDistributionFormResp>> exportOrderDistribution(@RequestBody QueryOrderDistributionReq queryReq) throws TException;

    /**
     * 卖家-导出商品配货单
     */
    @PostMapping(value = "/exportOrderProductDistribution", consumes = "application/json")
    ResultDto<List<OrderProductDistributionFormResp>> exportOrderProductDistribution(@RequestBody QueryOrderDistributionReq queryReq) throws TException;

    /**
     * 通过店铺ID查询店铺下的已完成订单数量和已完成的商品数量
     */
    @PostMapping(value = "/countFlashOrderAndProduct", consumes = "application/json")
    ResultDto<CountFlashOrderAndProductResp> countFlashOrderAndProduct(@RequestBody ShopIdReq request) throws TException;

    /**
     * 查询订单的物流信息
     */
    @PostMapping(value = "/getUserOrderWayBill", consumes = "application/json")
    ResultDto<OrderWayBillResp> getUserOrderWayBill(@RequestBody QueryUserOrderWayBillReq queryReq) throws TException;

    /**
     * 查询用户商品的购买数量
     */
    @PostMapping(value = "/getUserProductBuyCount", consumes = "application/json")
    ResultDto<ProductBuyCountListResp> getUserProductBuyCount(@RequestBody QueryProductBuyCountReq queryReq) throws TException;

    /**
     * 获取最近一次下单的地址
     */
    @PostMapping(value = "/getNearOrderAddress", consumes = "application/json")
    ResultDto<ShippingAddressDto> getNearOrderAddress(@RequestBody BaseIdReq request) throws TException;

}
