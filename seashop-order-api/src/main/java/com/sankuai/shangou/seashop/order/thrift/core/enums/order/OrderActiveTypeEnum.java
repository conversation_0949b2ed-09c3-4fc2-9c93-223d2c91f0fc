package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 活动类型
 * <AUTHOR>
 */
@ThriftEnum
public enum OrderActiveTypeEnum {

    NONE(0, "无活动"),


    ;

    private final Integer code;
    @Getter
    private final String desc;

    OrderActiveTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static OrderActiveTypeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(activeType -> activeType.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
