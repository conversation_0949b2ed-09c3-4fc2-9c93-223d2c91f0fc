package com.sankuai.shangou.seashop.pay.thrift.core.request.adapay;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description: 汇付天下支付请求基类
 */

@Data
@TypeDoc(description = "汇付天下支付请求基类")
public class AdaPayBaseReq extends BaseParamReq {

    @FieldDoc(description = "汇付支付 app_id")
    private String appId;

    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private String memberId;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.memberId)) {
            throw new InvalidParamException("memberId不能为空");
        }
    }


}
