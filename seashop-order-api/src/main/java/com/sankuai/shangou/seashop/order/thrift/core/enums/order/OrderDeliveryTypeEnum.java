package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 配送类型
 * <AUTHOR>
 */
@ThriftEnum
public enum OrderDeliveryTypeEnum {

    EXPRESS(1, "快递配送"),
    SELF(2, "自提");

    private final Integer code;
    @Getter
    private final String desc;

    OrderDeliveryTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static OrderDeliveryTypeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(deliveryType -> deliveryType.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
