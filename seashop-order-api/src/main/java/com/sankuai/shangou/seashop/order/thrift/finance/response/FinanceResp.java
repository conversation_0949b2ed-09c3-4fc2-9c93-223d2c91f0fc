package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/01/26 15:52
 */
@TypeDoc(description = "结算账号相关信息")
@ToString
@Data
public class FinanceResp extends BaseThriftDto {

    /**
     * 主键
     */
    @FieldDoc(description = "主键")
    private Long id;

    /**
     * 订单号
     */
    @FieldDoc(description = "订单号")
    private String orderId;

    /**
     * 支付单号，保证金订单号和支付单号一致
     */
    @FieldDoc(description = "支付单号，保证金订单号和支付单号一致")
    private String payId;

    /**
     * 汇付支付流水号
     */
    @FieldDoc(description = "汇付支付流水号")
    private String adapayId;

    /**
     * 交易类型 [Description("支付")]Pay = 1,[Description("退款")]Refund =  2,[Description("扣款")]Deduction = 3
     */
    @FieldDoc(description = "交易类型 [Description(\"支付\")]Pay = 1,[Description(\"退款\")]Refund =  2,[Description(\"扣款\")]Deduction = 3")
    private Integer type;

    /**
     * 创建时间
     */
    @FieldDoc(description = "创建时间")
    private Date createDate;

    /**
     * 店铺ID
     */
    @FieldDoc(description = "店铺ID")
    private Long shopId;

    /**
     * 店铺名称
     */
    @FieldDoc(description = "店铺名称")
    private String shopName;

    /**
     * 会员ID
     */
    @FieldDoc(description = "会员ID")
    private Long userId;

    /**
     * 会员名称
     */
    @FieldDoc(description = "会员名称")
    private String userName;

    /**
     * 交易流水号
     */
    @FieldDoc(description = "交易流水号")
    private String transactionId;

    /**
     * 金额
     */
    @FieldDoc(description = "金额")
    private BigDecimal totalAmount;

    /**
     * 退款类型,1:订单完成前退款，2：订单完成后退款
     */
    @FieldDoc(description = "退款类型,1:订单完成前退款，2：订单完成后退款")
    private Integer refundType;

    /**
     * 运费
     */
    @FieldDoc(description = "运费")
    private BigDecimal freight;

    /**
     * 原价
     */
    @FieldDoc(description = "原价")
    private BigDecimal productAmount;

    /**
     * 针对该订单的优惠金额
     */
    @FieldDoc(description = "针对该订单的优惠金额")
    private BigDecimal discountAmount;

    /**
     * 平台优惠券抵扣金额
     */
    @FieldDoc(description = "平台优惠券抵扣金额")
    private BigDecimal platDiscountAmount;

    /**
     * 满额减金额
     */
    @FieldDoc(description = "满额减金额")
    private BigDecimal fullDiscount;

    /**
     * 满减活动优惠金额
     */
    @FieldDoc(description = "满减活动优惠金额")
    private BigDecimal moneyOff;

    /**
     * 积分优惠金额
     */
    @FieldDoc(description = "积分优惠金额")
    private BigDecimal integralDiscount;

    /**
     * 汇付手续费
     */
    @FieldDoc(description = "汇付手续费")
    private BigDecimal serviceAmount;

    /**
     * 供应商结算金额
     */
    @FieldDoc(description = "供应商结算金额")
    private BigDecimal settlementAmount;

    /**
     * 佣金
     */
    @FieldDoc(description = "佣金")
    private BigDecimal commissionAmount;

    /**
     * 扣款类型，1：罚款；2：代收代付；
     */
    @FieldDoc(description = "扣款类型，1：罚款；2：代收代付；")
    private Integer deductionType;

    /**
     * 扣款手续费
     */
    @FieldDoc(description = "扣款手续费")
    private BigDecimal serviceFee;

    /**
     * 退款Id
     */
    @FieldDoc(description = "退款Id")
    private Long orderRefundId;

    /**
     * 创建时间
     */
    @FieldDoc(description = "创建时间")
    private Date createTime;

    /**
     * 修改时间
     */
    @FieldDoc(description = "修改时间")
    private Date updateTime;

    /**
     * 实际支付金额
     */
    @FieldDoc(description = "实际支付金额")
    private BigDecimal actualPayAmount;


    public Long getCreateDateLong() {
        return this.date2Long(this.createDate);
    }


    public void setCreateDateLong(Long createDate) {
        this.createDate = this.long2Date(createDate);
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


    public String getFreightString() {
        return this.bigDecimal2String(this.freight);
    }


    public void setFreightString(String freight) {
        this.freight = this.string2BigDecimal(freight);
    }


    public String getProductAmountString() {
        return this.bigDecimal2String(this.productAmount);
    }


    public void setProductAmountString(String productAmount) {
        this.productAmount = this.string2BigDecimal(productAmount);
    }


    public String getDiscountAmountString() {
        return this.bigDecimal2String(this.discountAmount);
    }


    public void setDiscountAmountString(String discountAmount) {
        this.discountAmount = this.string2BigDecimal(discountAmount);
    }


    public String getPlatDiscountAmountString() {
        return this.bigDecimal2String(this.platDiscountAmount);
    }


    public void setPlatDiscountAmountString(String platDiscountAmount) {
        this.platDiscountAmount = this.string2BigDecimal(platDiscountAmount);
    }


    public String getFullDiscountString() {
        return this.bigDecimal2String(this.fullDiscount);
    }


    public void setFullDiscountString(String fullDiscount) {
        this.fullDiscount = this.string2BigDecimal(fullDiscount);
    }


    public String getMoneyOffString() {
        return this.bigDecimal2String(this.moneyOff);
    }


    public void setMoneyOffString(String moneyOff) {
        this.moneyOff = this.string2BigDecimal(moneyOff);
    }


    public String getIntegralDiscountString() {
        return this.bigDecimal2String(this.integralDiscount);
    }


    public void setIntegralDiscountString(String integralDiscount) {
        this.integralDiscount = this.string2BigDecimal(integralDiscount);
    }


    public String getServiceAmountString() {
        return this.bigDecimal2String(this.serviceAmount);
    }


    public void setServiceAmountString(String serviceAmount) {
        this.serviceAmount = this.string2BigDecimal(serviceAmount);
    }


    public String getSettlementAmountString() {
        return this.bigDecimal2String(this.settlementAmount);
    }


    public void setSettlementAmountString(String settlementAmount) {
        this.settlementAmount = this.string2BigDecimal(settlementAmount);
    }


    public String getCommissionAmountString() {
        return this.bigDecimal2String(this.commissionAmount);
    }


    public void setCommissionAmountString(String commissionAmount) {
        this.commissionAmount = this.string2BigDecimal(commissionAmount);
    }


    public String getServiceFeeString() {
        return this.bigDecimal2String(this.serviceFee);
    }


    public void setServiceFeeString(String serviceFee) {
        this.serviceFee = this.string2BigDecimal(serviceFee);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }


    public String getActualPayAmountString() {
        return this.bigDecimal2String(this.actualPayAmount);
    }


    public void setActualPayAmountString(String actualPayAmount) {
        this.actualPayAmount = this.string2BigDecimal(actualPayAmount);
    }
}
