package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@ThriftEnum
public enum OrderQueryFromEnum {

    /**
     * 1-商家小程序
     */
    SHOP_MINI_PROGRAM(1, "商家小程序"),
    /**
     * 2-商家PC端
     */
    SHOP_PC(2, "商家PC端"),
    /**
     * 3-卖家PC端
     */
    SELLER_PC(3, "卖家PC端"),
    /**
     * 4-平台PC端
     */
    PLATFORM_PC(4, "平台PC端"),
    ;

    private final Integer code;
    @Getter
    private final String desc;

    OrderQueryFromEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static OrderQueryFromEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(queryFrom -> queryFrom.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
