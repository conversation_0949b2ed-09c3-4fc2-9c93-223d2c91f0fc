package com.sankuai.shangou.seashop.order.thrift.finance.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;


/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "创建支付请求对象")
public class CreatePaymentReq extends BaseParamReq {

    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @FieldDoc(description = "支付金额", requiredness = Requiredness.REQUIRED)
    private BigDecimal payAmount;

    @FieldDoc(description = "支付方式", requiredness = Requiredness.REQUIRED)
    private Integer paymentType;

    @FieldDoc(description = "银行编码:如果使用b2b支付必传")
    private String bankCode;

    @FieldDoc(description = "ip地址", requiredness = Requiredness.REQUIRED)
    private String deviceIp;

    @Override
    public void checkParameter() {
        if (this.shopId == null) {
            throw new InvalidParamException("shopId不能为空");
        }
        if (this.payAmount == null || this.payAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("payAmount不能为空且不能小于0");
        }
        if (this.paymentType == null) {
            throw new InvalidParamException("paymentType不能为空");
        }
//        if (!PaymentTypeEnum.ALIPAY_SCAN.getType().equals(paymentType)
//                && !PaymentTypeEnum.COMPANY_BANK.getType().equals(paymentType)) {
//            throw new InvalidParamException("paymentType暂不支持其他类型付款");
//        }
//        if (PaymentTypeEnum.COMPANY_BANK.getType().equals(paymentType) && StrUtil.isBlank(this.bankCode)) {
//            throw new InvalidParamException("bankCode不能为空");
//        }
        if (StrUtil.isBlank(this.deviceIp)) {
            throw new InvalidParamException("deviceIp不能为空");
        }
    }


    public String getPayAmountString() {
        return this.bigDecimal2String(this.payAmount);
    }


    public void setPayAmountString(String payAmount) {
        this.payAmount = this.string2BigDecimal(payAmount);
    }


}
