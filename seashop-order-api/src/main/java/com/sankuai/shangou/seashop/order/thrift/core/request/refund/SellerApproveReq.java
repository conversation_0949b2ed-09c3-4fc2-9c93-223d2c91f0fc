package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "供应商审核请求参数")
public class SellerApproveReq extends BaseParamReq {

    @FieldDoc(description = "店铺信息", requiredness = Requiredness.REQUIRED)
    private ShopDto shop;
    @FieldDoc(description = "用户信息", requiredness = Requiredness.REQUIRED)
    private UserDto user;
    @FieldDoc(description = "退款单号", requiredness = Requiredness.REQUIRED)
    private Long refundId;
    @FieldDoc(description = "供应商备注")
    private String sellerRemark;
    @FieldDoc(description = "审核状态。2：待买家寄货(退货通过)；4：供应商拒绝；5：供应商通过审核(仅退款或者退货弃货)", requiredness = Requiredness.REQUIRED)
    private RefundAuditStatusEnum auditStatus;
    @FieldDoc(description = "是否退运费")
    private Boolean whetherRefundFreight;
    /**
     * 是否弃货，详情返回的refundType=2退货退款，才需要这个字段
     */
    @FieldDoc(description = "是否弃货，详情返回的refundType=2退货退款，才需要这个字段")
    private Boolean whetherAbandonGoods;

    @Override
    public void checkParameter() {
        if (shop == null) {
            throw new InvalidParamException("店铺信息不能为空");
        }
        this.shop.checkParameter();
        if (user == null) {
            throw new InvalidParamException("用户信息不能为空");
        }
        this.user.checkParameter();
        if (refundId == null) {
            throw new InvalidParamException("退款单号不能为空");
        }
        if (auditStatus == null) {
            throw new InvalidParamException("审核状态不能为空");
        }
        if (RefundAuditStatusEnum.SUPPLIER_REFUSE.equals(auditStatus) && StrUtil.isBlank(sellerRemark)) {
            throw new InvalidParamException("供应商拒绝时，备注不能为空");
        }
    }


}
