package com.sankuai.shangou.seashop.order.thrift.finance.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.DeductionTypeEnum;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保证金扣减对象")
public class DeductionReq extends BaseParamReq {

    @FieldDoc(description = "明细id", requiredness = Requiredness.REQUIRED)
    private Long cashDepositDetailId;

    @FieldDoc(description = "扣减金额", requiredness = Requiredness.REQUIRED)
    private BigDecimal deductionAmount;

    @FieldDoc(description = "扣款类型：1罚款；2代收代付", requiredness = Requiredness.REQUIRED)
    private Integer deductionType;

    @FieldDoc(description = "扣减手续费")
    private BigDecimal deductionFee;

    @FieldDoc(description = "描述说明")
    private String description;

    @FieldDoc(description = "操作人", requiredness = Requiredness.REQUIRED)
    private String operator;

    @Override
    public void checkParameter() {
        if (this.cashDepositDetailId == null) {
            throw new InvalidParamException("明细id不能为空");
        }
        if (this.deductionAmount == null) {
            throw new InvalidParamException("扣减金额不能为空");
        }
        if(this.deductionAmount.compareTo(new BigDecimal("0.01")) < 0){
            throw new InvalidParamException("扣减金额不能小于0.01");
        }
        if(deductionAmount.scale() > 2){
            throw new InvalidParamException("扣减金额请保留两位小数");
        }
        if (this.deductionType == null) {
            throw new InvalidParamException("扣款类型不能为空");
        }
        if (DeductionTypeEnum.getByType(this.deductionType) == null) {
            throw new InvalidParamException("扣款类型不合法");
        }
        if (this.deductionFee != null && this.deductionFee.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("扣减手续费不能为空且不能小于0");
        }
        if (StrUtil.isBlank(this.operator)) {
            throw new InvalidParamException("操作人不能为空");
        }
    }






    public String getDeductionAmountString() {
        return this.bigDecimal2String(this.deductionAmount);
    }


    public void setDeductionAmountString(String deductionAmount) {
        this.deductionAmount = this.string2BigDecimal(deductionAmount);
    }


    public String getDeductionFeeString() {
        return this.bigDecimal2String(this.deductionFee);
    }


    public void setDeductionFeeString(String deductionFee) {
        this.deductionFee = this.string2BigDecimal(deductionFee);
    }


}
