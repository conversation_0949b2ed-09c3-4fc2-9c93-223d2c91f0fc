package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.AppendOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.DeleteOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.SaveOrderCommentReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/12/04 16:12
 * 订单评价操作相关服务
 */
@FeignClient(name = "himall-order", contextId = "OrderCommentCmdFeign", path = "/himall-order/orderCommentCmd", url = "${himall-order.dev.url:}")
public interface OrderCommentCmdFeign {

    /**
     * 保存订单评价
     */
    @PostMapping(value = "/saveOrderComment", consumes = "application/json")
    ResultDto<BaseResp> saveOrderComment(@RequestBody SaveOrderCommentReq request) throws TException;

    /**
     * 追加订单评价
     */
    @PostMapping(value = "/appendOrderComment", consumes = "application/json")
    ResultDto<BaseResp> appendOrderComment(@RequestBody AppendOrderCommentReq request) throws TException;

    /**
     * 删除订单评价
     */
    @PostMapping(value = "/deleteOrderCommentForPlatForm", consumes = "application/json")
    ResultDto<BaseResp> deleteOrderCommentForPlatForm(@RequestBody DeleteOrderCommentReq request) throws TException;

    /**
     * 删除订单评价(删除风控未通过的追评/评价)
     */
    @PostMapping(value = "/deleteOrderCommentForBuyer", consumes = "application/json")
    ResultDto<BaseResp> deleteOrderCommentForBuyer(@RequestBody DeleteOrderCommentReq request) throws TException;

}
