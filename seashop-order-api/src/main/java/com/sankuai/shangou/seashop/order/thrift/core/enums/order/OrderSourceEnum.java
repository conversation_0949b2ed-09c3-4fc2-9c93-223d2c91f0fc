//package com.sankuai.shangou.seashop.order.thrift.core.enums.order;
//
//import com.facebook.swift.codec.ThriftEnum;
//import com.facebook.swift.codec.ThriftEnumValue;
//import lombok.Getter;
//
//import java.util.Arrays;
//
///**
// * 订单来源 0表示本系统，1表示牵牛花
// * <AUTHOR>
// */
//@ThriftEnum
//public enum OrderSourceEnum {
//
//    /**
//     * 0商城，代表的就是本系统内部订单，目前包括PC商城和小程序商城
//     */
//    SELF(0, "商城"),
//    QIAN_NIU_HUA(1, "牵牛花");
//
//    private final Integer code;
//    @Getter
//    private final String desc;
//
//    OrderSourceEnum(Integer code, String desc) {
//        this.code = code;
//        this.desc = desc;
//    }
//
//    @ThriftEnumValue
//    public Integer getCode() {
//        return code;
//    }
//
//    public static OrderSourceEnum valueOf(Integer code) {
//        return Arrays.stream(values())
//                .filter(orderSource -> orderSource.getCode().equals(code))
//                .findFirst()
//                .orElse(null);
//    }
//
//    public static String getDesc(Integer code) {
//        return Arrays.stream(values())
//                .filter(orderSource -> orderSource.getCode().equals(code))
//                .findFirst()
//                .map(OrderSourceEnum::getDesc)
//                .orElse("");
//    }
//
//}
