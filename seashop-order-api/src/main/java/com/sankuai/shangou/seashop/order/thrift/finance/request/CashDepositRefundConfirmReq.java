package com.sankuai.shangou.seashop.order.thrift.finance.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "退款审批通过请求对象")
public class CashDepositRefundConfirmReq extends BaseParamReq {

    @FieldDoc(description = "退款单id", requiredness = Requiredness.REQUIRED)
    private Long id;

    @FieldDoc(description = "操作人", requiredness = Requiredness.REQUIRED)
    private String operator;

    @Override
    public void checkParameter() {
        if (null == this.id) {
            throw new InvalidParamException("id不能为空");
        }
        if (StrUtil.isBlank(this.operator)) {
            throw new InvalidParamException("operator不能为空");
        }
    }


}
