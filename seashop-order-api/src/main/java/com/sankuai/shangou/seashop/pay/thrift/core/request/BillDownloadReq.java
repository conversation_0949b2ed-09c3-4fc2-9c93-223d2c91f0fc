package com.sankuai.shangou.seashop.pay.thrift.core.request;

import cn.hutool.core.date.DateUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2024/1/2/002
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "账单下载请求对象")
public class BillDownloadReq extends BaseParamReq {

    @FieldDoc(description = "支付渠道:1汇付天下支付，不填默认汇付")
    private Integer paymentChannel = PaymentChannelEnum.ADAPAY.getCode();

    @FieldDoc(description = "账单日期，格式：yyyy-MM-dd", requiredness = Requiredness.REQUIRED)
    private Date billDate;

    @Override
    public void checkParameter() {
        if (this.paymentChannel == null) {
            throw new InvalidParamException("paymentChannel不能为空");
        }
        if (null == this.billDate) {
            throw new InvalidParamException("billDate不能为空");
        }
        Date now = DateUtil.beginOfDay(new Date());
        if(this.billDate.after(now)){
            throw new InvalidParamException("billDate不能大于今天");
        }

    }


    public Long getBillDateLong() {
        return this.date2Long(this.billDate);
    }


    public void setBillDateLong(Long billDate) {
        this.billDate = this.long2Date(billDate);
    }
}
