package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description: 响应结果模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TradeManagementCheckResponse {
    @FieldDoc(description = "errcode")
    private int errCode;

    @FieldDoc(description = "errmsg")
    private String errMsg;

    @FieldDoc(description = "completed")
    private boolean isCompleted; // 是否已完成结算管理确认
}
