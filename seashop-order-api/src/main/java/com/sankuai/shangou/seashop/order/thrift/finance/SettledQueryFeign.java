package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.*;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemCountResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description: 已结算列表查询服务
 */
@FeignClient(name = "himall-order", contextId = "SettledQueryFeign", path = "/himall-order/finance/settledQuery", url = "${himall-order.dev.url:}")
public interface SettledQueryFeign {

    /**
     * 已结算列表查询
     */
    @PostMapping(value = "pageList", consumes = "application/json")
    ResultDto<BasePageResp<SettledResp>> pageList(@RequestBody SettledQryReq request) throws TException;

    /**
     * 已结算明细列表查询
     *
     */
    @PostMapping(value = "itemPageList", consumes = "application/json")
    ResultDto<BasePageResp<SettledItemResp>> itemPageList(@RequestBody SettledItemQryReq request) throws TException;

    /**
     * 已结算明细统计查询
     *
     */
    @PostMapping(value = "itemCount", consumes = "application/json")
    ResultDto<SettledItemCountResp> itemCount(@RequestBody SettledItemCountQryReq request) throws TException;

    /**
     * 通过订单id查询已结算明细
     *
     */
    @PostMapping(value = "getDetailByOrderId", consumes = "application/json")
    ResultDto<SettlementDetailResp> getDetailByOrderId(@RequestBody OrderIdQryReq request) throws TException;
}
