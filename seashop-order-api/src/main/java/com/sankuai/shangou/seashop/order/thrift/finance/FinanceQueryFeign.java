package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.FinanceQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceIndexResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description: 财务相关查询服务（统计相关接口）
 */
@FeignClient(name = "himall-order", contextId = "FinanceQueryFeign", path = "/himall-order/finance/financeQuery", url = "${himall-order.dev.url:}")
public interface FinanceQueryFeign {

    /**
     * 获取财务首页统计数据
     */
    @PostMapping(value = "getFinanceIndex", consumes = "application/json")
    ResultDto<FinanceIndexResp> getFinanceIndex(@RequestBody BaseIdReq request) throws TException;

    /**
     * 获取财务中间表数据
     */
    @PostMapping(value = "getFinance", consumes = "application/json")
    ResultDto<FinanceResp> getFinance(@RequestBody FinanceQueryReq request) throws TException;
}
