package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.PrintOrderDataReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderDataResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderItemDataResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 16:10
 * 主要应用于打印订单信息查询场景
 */
@FeignClient(name = "himall-order", contextId = "PrintOrderDataQueryFeign", path = "/himall-order/printOrderData", url = "${himall-order.dev.url:}")
public interface PrintOrderDataQueryFeign {

    /**
     * 批量查询订单信息
     */
    @PostMapping(value = "/batchQueryOrderByOrderIds", consumes = "application/json")
    ResultDto<List<PrintOrderDataResp>> batchQueryOrderByOrderIds(@RequestBody PrintOrderDataReq queryReq) throws TException;

    /**
     * 批量查询订单项信息
     */
    @PostMapping(value = "/batchQueryOrderItemByOrderIds", consumes = "application/json")
    ResultDto<List<PrintOrderItemDataResp>> batchQueryOrderItemByOrderIds(@RequestBody PrintOrderDataReq queryReq) throws TException;
}
