package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "店铺信息对象")
@ToString
@Data
@Getter
@Setter
public class PromotionDto {

    @FieldDoc(description = "营销活动ID")

    private Long promotionId;
    @FieldDoc(description = "营销活动名称")

    private String promotionName;
    @FieldDoc(description = "营销活动类型")

    private String promotionType;
    @FieldDoc(description = "营销活动类型描述")

    private String promotionTypeDesc;
    @FieldDoc(description = "营销活动的满足条件描述")

    private String matchConditionDesc;
    @FieldDoc(description = "满足条件的值描述")

    private String promotionValueDesc;

}
