package com.sankuai.shangou.seashop.pay.thrift.core.request.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 子单信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubOrder {
    @FieldDoc(description = "order_key")
    private OrderKey orderKey;

    @FieldDoc(description = "delivery_mode")
    private int deliveryMode;

    @FieldDoc(description = "logistics_type")
    private int logisticsType;

    @FieldDoc(description = "shipping_list")
    private List<ShippingItem> shippingList;

    @FieldDoc(description = "is_all_delivered")
    private Boolean isAllDelivered;
}
