package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname OrderItemReq
 * Description //TODO
 * @date 2024/11/7 14:06
 */
@Data
public class OrderItemReq implements Serializable {
    /**
     * 营销类型
     */
    private Long marketTypes;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 主副商品标记：1 主商品 ｜2 副商品 。用于标记活动赠送商品还是下单选中商品
     */
    private Integer productFlag;
    /**
     * skuId
     */
    private String skuId;
    /**
     * sku
     */
    private String sku;
    /**
     * 数量
     */
    private Integer quantity;
    /**
     * 规格：颜色，对应商品表的 spec1
     */
    private String color;
    /**
     * 规格：尺码，对应商品表的 spec2
     */
    private String size;
    /**
     * 规格：版本，对应商品表的 spec3
     */
    private String version;
    /**
     * sku自增主键ID
     */
    private Long skuAutoId;
    /**
     * 商品主图
     */
    private String mainImagePath;
    /**
     * 商品成本单价
     */
    private BigDecimal costPrice;
    /**
     * 原始销售价
     */
    private BigDecimal originalSalePrice;
    /**
     * 最终销售价（商品级优惠后）
     */
    private BigDecimal finalSalePrice;
    /**
     * 优惠总额
     */
    private BigDecimal discountAmount;
    /**
     * 改价金额
     */
    private BigDecimal changeAmount;
//    /**
//     * 总实付
//     */
//    private BigDecimal realTotalAmount;
//    /**
//     * 积分
//     */
//    private Long integral;
    /**
     * 营销
     */
    private List<OrderItemMarketRecordCreateReq> markets;
}
