package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.MallQueryProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentSummaryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryShopCommentSummaryReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentSummaryResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ShopCommentSummaryResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:31
 * 商品评价查询相关服务
 */
@FeignClient(name = "himall-order", contextId = "ProductCommentQueryFeign", path = "/himall-order/productCommentQuery", url = "${himall-order.dev.url:}")
public interface ProductCommentQueryFeign {

    /**
     * 查询商品评价列表(平台端)
     */
    @PostMapping(value = "/queryProductCommentForPlatform", consumes = "application/json")
    ResultDto<BasePageResp<ProductCommentResp>> queryProductCommentForPlatform(@RequestBody QueryProductCommentReq request) throws TException;

    /**
     * 查询商品评价列表(卖家端)
     */
    @PostMapping(value = "/queryProductCommentForSeller", consumes = "application/json")
    ResultDto<BasePageResp<ProductCommentResp>> queryProductCommentForSeller(@RequestBody QueryProductCommentReq request) throws TException;

    /**
     * 查询商品评价列表(商城端)
     */
    @PostMapping(value = "/queryProductCommentForMall", consumes = "application/json")
    ResultDto<BasePageResp<ProductCommentResp>> queryProductCommentForMall(@RequestBody MallQueryProductCommentReq request) throws TException;

    /**
     * 查询商品评论汇总数据
     */
    @PostMapping(value = "/queryProductCommentSummary", consumes = "application/json")
    ResultDto<ProductCommentSummaryResp> queryProductCommentSummary(@RequestBody QueryProductCommentSummaryReq request) throws TException;

    /**
     * 查询店铺评论汇总
     */
    @PostMapping(value = "/queryShopCommentSummary", consumes = "application/json")
    ResultDto<ShopCommentSummaryResp> queryShopCommentSummary(@RequestBody QueryShopCommentSummaryReq request) throws TException;

    /**
     * 查询平台控制台的评论汇总
     */
    @GetMapping(value = "/createOrder")
    ResultDto<ShopCommentSummaryResp> queryMCommentSummary() throws TException;
}
