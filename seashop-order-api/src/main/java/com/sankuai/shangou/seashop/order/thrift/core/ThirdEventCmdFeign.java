package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPushEventReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.UpdatePushEventReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryPushEventResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.UpdatePushEventResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * 主要应用于管理事件推送状态的操作场景
 */
@FeignClient(name = "himall-order", contextId = "ThirdEventCmdFeign", path = "/himall-order/thirdEvent", url = "${himall-order.dev.url:}")
public interface ThirdEventCmdFeign {

    /**
     * 修改推送状态
     */
    @PostMapping(value = "/updateThirdPushEvent", consumes = "application/json")
    ResultDto<UpdatePushEventResp> updateThirdPushEvent(@RequestBody UpdatePushEventReq req) throws TException;

    /**
     * 通过id查询推送事件
     */
    @PostMapping(value = "/queryById", consumes = "application/json")
    ResultDto<QueryPushEventResp> queryById(@RequestBody QueryPushEventReq req) throws TException;

}
