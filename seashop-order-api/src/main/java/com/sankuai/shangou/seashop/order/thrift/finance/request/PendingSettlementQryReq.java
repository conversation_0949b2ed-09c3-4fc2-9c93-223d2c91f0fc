package com.sankuai.shangou.seashop.order.thrift.finance.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "待结算列表店铺维度请求参数")
public class PendingSettlementQryReq extends BasePageReq {

    @FieldDoc(description = "店铺id列表")
    private List<Long> shopIdList;


}
