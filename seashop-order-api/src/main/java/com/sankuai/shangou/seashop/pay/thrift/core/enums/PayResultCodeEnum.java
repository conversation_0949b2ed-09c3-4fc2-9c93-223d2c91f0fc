package com.sankuai.shangou.seashop.pay.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.AllArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@ThriftEnum
@AllArgsConstructor
public enum PayResultCodeEnum {

    // 请传入正确的参数
    PARAM_ERROR(********, "请传入正确的参数"),
    PAY_MEMBER_CREATE_ERROR(********, "创建对私类型账号异常"),
    PAY_SETTLE_ACCOUNT_CREATE_ERROR(********, "创建结算账号异常异常"),
    PAY_CORP_MEMBER_CREATE_ERROR(********, "创建企业用户对象异常"),
    PAY_SETTLE_ACCOUNT_QUERY_ERROR(********, "查询结算账户对象异常"),
    PAY_SETTLE_ACCOUNT_DELETE_ERROR(********, "删除结算账户对象异常"),
//    查询汇付账户失败
    PAY_QUERY_ACCOUNT_ERROR(********, "查询汇付账户对象异常"),
    // 订单号已存在
    ORDER_ID_EXIST(********, "订单号已存在"),

    PAY_PAYMENT_CREATE_ERROR(********, "创建支付对象异常"),
    // 订单支付对象不存在
    ORDER_PAY_NOT_EXIST(********, "订单支付对象不存在"),
    // 退款订单号已存在
    REVERSE_ID_EXIST(********, "退款订单号已存在"),

    PAY_PAYMENT_REVERSE_CREATE_ERROR(********, "创建支付撤销对象异常"),
    // 退款信息不存在
    REVERSE_NOT_EXIST(********, "退款信息不存在"),

    PAY_PAYMENT_CONFIRM_CREATE_ERROR(********, "创建支付确认对象异常"),
    // 渠道返回的创建支付确认异常
    PAY_PAYMENT_CONFIRM_CREATE_CHANNEL_ERROR(********, "渠道返回的创建支付确认异常"),
    // 对账单下载异常
    BILL_DOWNLOAD_ERROR(********, "对账单下载异常"),
    // 退款次数超限
    REVERSE_COUNT_OVER_LIMIT(********, "退款次数超限"),
    // 更新汇付账号直接成功
    UPDATE_ACCOUNT_SUCCESS(********, "更新汇付账号直接成功"),
    ;

    private Integer code;
    private String msg;

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }

}
