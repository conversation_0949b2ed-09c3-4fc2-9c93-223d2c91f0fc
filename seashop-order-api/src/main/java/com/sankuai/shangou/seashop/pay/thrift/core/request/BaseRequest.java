package com.sankuai.shangou.seashop.pay.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseRequest {

    @FieldDoc(description = "access_token")
    private String accessToken;

    @FieldDoc(description = "appId")
    private String appId;

    @FieldDoc(description = "userId")
    private Long userId;

}
