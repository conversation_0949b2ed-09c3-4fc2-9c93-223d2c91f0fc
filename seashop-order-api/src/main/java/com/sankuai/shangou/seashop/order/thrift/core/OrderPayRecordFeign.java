package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayRecordResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/5 15:37
 * 主要应用于订单支付记录表，用于记录订单与支付单之间的映射
 */
@FeignClient(name = "himall-order", contextId = "OrderPayRecordFeign", path = "/himall-order/orderPayRecord", url = "${himall-order.dev.url:}")
public interface OrderPayRecordFeign {

    /**
     * 获取订单与支付单之间的映射
     */
    @PostMapping(value = "/queryOrderPayRecordList", consumes = "application/json")
    ResultDto<List<OrderPayRecordResp>> queryOrderPayRecordList(@RequestBody List<String> batchNoList) throws TException;

    @PostMapping(value = "/queryOrderPayRecordByOrderIds", consumes = "application/json")
    ResultDto<List<OrderPayRecordResp>> queryOrderPayRecordByOrderIds(@RequestBody List<String> orderIds) throws TException;
}
