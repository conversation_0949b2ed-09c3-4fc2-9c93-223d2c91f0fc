package com.sankuai.shangou.seashop.order.thrift.core.dto;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TypeDoc(description = "创建售后单商品入参")
public class CreateOrderRefundItemDto extends BaseParamReq {
    /**
     * 商品ID
     */
    @FieldDoc(
            name = "productId",
            description = "spu ID",
            requiredness = Requiredness.OPTIONAL
    )
    private Long productId;
    /**
     * 规格货号
     */
    @FieldDoc(
            name = "skuAutoId",
            description = "skuAutoId，跟规格货号二选一",
            requiredness = Requiredness.OPTIONAL
    )
    private Long skuAutoId;
    /**
     * 规格货号
     */
    @FieldDoc(
            name = "skuCode",
            description = "规格货号 跟skuAutoId 至少二选一",
            requiredness = Requiredness.OPTIONAL
    )
    private String skuCode;
    /**
     * 退款数量
     */
    @FieldDoc(
            name = "quantity",
            description = "退款数量",
            requiredness = Requiredness.REQUIRED
    )
    private Long quantity;
    /**
     * 退款金额
     */
    @Setter
    @Getter
    @FieldDoc(
            name = "refundAmount",
            description = "退款金额",
            requiredness = Requiredness.REQUIRED
    )
    private BigDecimal refundAmount;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(skuAutoId == null && StrUtil.isBlank(skuCode),
                "skuAutoId和 skuCode 至少二选一");
        AssertUtil.throwIfTrue((quantity == null || quantity <= 0) && (refundAmount == null || BigDecimal.ZERO.compareTo(refundAmount) >= 0),
                "退款金额或者退货数量不能都为空或者小于0");
        if (quantity == null) {
            quantity = 0L;
        }
        if (refundAmount == null) {
            refundAmount = BigDecimal.ZERO;
        }
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }
}
