package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/12/04 11:55
 */
@Data
@ToString
@TypeDoc(description = "查询订单评价入参")
public class QueryOrderCommentReq extends BasePageReq {

    @FieldDoc(description = "开始时间", requiredness = Requiredness.NONE)
    private Date startTime;

    @FieldDoc(description = "结束时间", requiredness = Requiredness.NONE)
    private Date endTime;

    @FieldDoc(description = "订单编号", requiredness = Requiredness.NONE)
    private String orderId;

    @FieldDoc(description = "用户id(外观传入)", requiredness = Requiredness.NONE)
    private Long userId;

    @FieldDoc(description = "店铺id(外观传入)", requiredness = Requiredness.NONE)
    private Long shopId;

    @FieldDoc(description = "店铺名称", requiredness = Requiredness.NONE)
    private String shopName;

    @FieldDoc(description = "评价人名称", requiredness = Requiredness.NONE)
    private String userName;


    public Long getStartTimeLong() {
        return this.date2Long(this.startTime);
    }


    public void setStartTimeLong(Long startTime) {
        this.startTime = this.long2Date(startTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


    public void checkForBuyer() {
        AssertUtil.throwIfTrue(userId == null || userId <= 0, "用户id不能为空");
    }

    public void checkForSeller() {
        AssertUtil.throwIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }
}
