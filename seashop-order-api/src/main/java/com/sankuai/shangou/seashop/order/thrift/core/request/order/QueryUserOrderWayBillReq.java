package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "查询用户订单快递公司")
public class QueryUserOrderWayBillReq extends BaseParamReq {

    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "订单号", requiredness = Requiredness.REQUIRED)
    private String orderId;

    @Override
    public void checkParameter() {
        if (userId == null) {
            throw new InvalidParamException("userId不能为空");
        }
        if (StrUtil.isBlank(orderId)) {
            throw new InvalidParamException("orderId不能为空");
        }
    }


}
