package com.sankuai.shangou.seashop.pay.thrift.core.request.adapay;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description: 汇付-结算账号查询请求体
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "结算账号查询请求对象")
public class AdaPaySettleAccountQryReq extends AdaPayBaseReq {

    /**
     * 结算账户ID
     */
    @FieldDoc(description = "结算账户ID", requiredness = Requiredness.REQUIRED)
    private String settleAccountId;

    @Override
    public void checkParameter() {
        super.checkParameter();
        if (StrUtil.isBlank(settleAccountId)) {
            throw new InvalidParamException("结算账户ID不能为空");
        }
    }


}
