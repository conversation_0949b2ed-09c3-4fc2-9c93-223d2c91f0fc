package com.sankuai.shangou.seashop.pay.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayPaymentCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayReverseCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaymentConfirmCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayPaymentCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayReverseCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PaymentConfirmCreateResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description: 支付相关操作服务
 */
@FeignClient(name = "himall-order", contextId = "PayCmdFeign", path = "/himall-order/pay", url = "${himall-order.dev.url:}")
public interface PayCmdFeign {

    /**
     * 创建支付
     *
     */
    @PostMapping(value = "/createPayment", consumes = "application/json")
    ResultDto<PayPaymentCreateResp> createPayment(@RequestBody PayPaymentCreateReq request) throws TException;

    /**
     * 创建退款订单
     * （撤销支付-用于未确认支付的退款）
     */
    @PostMapping(value = "/createPaymentReverse", consumes = "application/json")
    ResultDto<PayReverseCreateResp> createPaymentReverse(@RequestBody PayReverseCreateReq request) throws TException;

    /**
     * 创建支付确认对象(分账)
     */
    @PostMapping(value = "/createPaymentConfirm", consumes = "application/json")
    ResultDto<PaymentConfirmCreateResp> createPaymentConfirm(@RequestBody PaymentConfirmCreateReq request) throws TException;
}
