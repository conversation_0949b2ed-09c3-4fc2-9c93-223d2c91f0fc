package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "退货寄回")
public class SendRefundGoodsReq extends BaseParamReq {

    @FieldDoc(description = "用户信息", requiredness = Requiredness.REQUIRED)
    private UserDto user;
    @FieldDoc(description = "退款单号", requiredness = Requiredness.REQUIRED)
    private Long refundId;
    @FieldDoc(description = "快递公司名称", requiredness = Requiredness.REQUIRED)
    private String expressCompanyName;
    @FieldDoc(description = "快递单号", requiredness = Requiredness.REQUIRED)
    private String shipOrderNumber;
    @FieldDoc(description = "快递公司编码", requiredness = Requiredness.OPTIONAL)
    private String expressCompanyCode;

    @Override
    public void checkParameter() {
        if (user == null) {
            throw new InvalidParamException("用户信息不能为空");
        }
        this.user.checkParameter();
        if (refundId == null) {
            throw new InvalidParamException("退款单号不能为空");
        }
        long expressNotBlankCount = Stream.of(expressCompanyName, shipOrderNumber, expressCompanyCode).filter(StrUtil::isNotBlank).count();
        if (expressNotBlankCount > 0 && expressNotBlankCount < 3) {
            //三者必须同时存在或者同时不存在
            throw new InvalidParamException("快递公司名称、快递单号、快递公司编码不能为空");
        }
    }


}
