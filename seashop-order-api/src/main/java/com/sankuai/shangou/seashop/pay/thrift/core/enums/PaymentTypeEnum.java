package com.sankuai.shangou.seashop.pay.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.AllArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description:
 */
@ThriftEnum
@AllArgsConstructor
public enum PaymentTypeEnum {
    /**
     * .net对应的支付类型
     * [Description("支付宝扫码")]
     * AlipayScan = 1,
     * [Description("支付宝H5")]
     * AlipayH5 = 2,
     * [Description("微信小程序")]
     * WeixinApplet = 3,
     * [Description("微信H5")]
     * WeixinH5 = 4,
     * [Description("微信商城")]
     * WeixinJS = 11,
     * [Description("企业网银")]
     * CompanyBank = 5,
     * [Description("个人网银")]
     * PersonBank = 6,
     */

    // 支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，11: 微信商城，5: 企业网银，6: 个人网银，

    ALIPAY_SCAN(1, "支付宝扫码"),
    ALIPAY_H5(2, "支付宝H5"),
    WECHAT_APPLET(3, "微信小程序"),
    WECHAT_NATIVE(7, "微信扫码"),
    WECHAT_H5(4, "微信H5"),
    WECHAT_JS(11, "微信商城"),
    COMPANY_BANK(5, "企业网银"),
    PERSON_BANK(6, "个人网银"),
    ;

    private Integer type;
    private String name;

    @ThriftEnumValue
    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据type获取枚举
     *
     * @param type
     * @return
     */
    public static PaymentTypeEnum getByType(Integer type) {
        for (PaymentTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 是否需要有扩展参数
     *
     * @param type
     * @return
     */
    public static Boolean hasExpend(Integer type) {
        if (WECHAT_APPLET.type.equals(type) || COMPANY_BANK.type.equals(type) || PERSON_BANK.type.equals(type)) {
            return true;
        }
        return false;
    }
}
