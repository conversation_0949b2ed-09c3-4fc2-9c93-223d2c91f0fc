package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单发货信息")
public class OrderDeliveryDto extends BaseThriftDto {

    @FieldDoc(description = "订单号", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "物流列表", requiredness = Requiredness.REQUIRED)
    private List<OrderExpressDto> expressList;


}
