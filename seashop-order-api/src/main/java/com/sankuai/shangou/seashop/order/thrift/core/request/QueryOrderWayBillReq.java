package com.sankuai.shangou.seashop.order.thrift.core.request;

import cn.hutool.core.collection.CollUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "查询用户收货地址对象")
@ToString
@Data
public class QueryOrderWayBillReq extends BaseParamReq {

    @FieldDoc(description = "订单号", requiredness = Requiredness.REQUIRED)
    private List<String> orderIdList;
    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(orderIdList)) {
            throw new InvalidParamException("orderIdList不能为空");
        }
        if (shopId == null) {
            throw new InvalidParamException("shopId不能为空");
        }
    }


}
