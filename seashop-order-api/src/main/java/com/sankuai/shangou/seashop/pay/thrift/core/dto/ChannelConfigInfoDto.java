package com.sankuai.shangou.seashop.pay.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@TypeDoc(description = "支付渠道配置响应体")
@ToString
@Data
public class ChannelConfigInfoDto extends BaseThriftDto {

    /**
     * 配置名字
     */
    @FieldDoc(description = "配置名字")
    private String configName;

    /**
     * 配置Key值
     */
    @FieldDoc(description = "配置Key值")
    private String configKey;

    /**
     * 配置Value值
     */
    @FieldDoc(description = "配置Value值")
    private String configValue;

    /**
     * 描述
     */
    @FieldDoc(description = "描述")
    private String configDesc;


}
