package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
public class IntegralRedemOrderReq extends BasePageReq {

    @Schema(description = "积分兑换商品ID")
    private Long redemId;

    @Schema(description = "订单号")
    private String orderId;

    @Schema(description = "订单创建时间开始")
    private Date orderDateStart;

    @Schema(description = "订单创建时间结束")
    private Date orderDateEnd;

    @Schema(description = "订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private Integer orderStatus;

    @Schema(description = "用户名")
    private String userName;


}
