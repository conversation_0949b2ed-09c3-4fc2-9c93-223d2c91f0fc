package com.sankuai.shangou.seashop.order.thrift.finance.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/12/2/002
 * @description:
 */
@ThriftEnum
public enum TransactionTypesEnum {

    PAY(1, "支付"),
    REFUND(2, "退款"),
    DEDUCTION(3, "扣款"),
    FINISH(4, "订单完成"),
    SETTLEMENT(5, "结算"),

    ERROR_ORDER_REFUND(6, "异常订单退款"),
    EXCESS_PAYMENT_REFUND(7, "超支退款"),
    ;

    private Integer code;
    private String desc;

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    TransactionTypesEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
