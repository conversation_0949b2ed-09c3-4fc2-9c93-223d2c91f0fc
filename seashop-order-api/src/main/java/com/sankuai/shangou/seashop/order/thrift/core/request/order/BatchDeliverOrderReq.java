package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderDeliveryDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单物流信息")
public class BatchDeliverOrderReq extends BaseParamReq {

    @FieldDoc(description = "当前登录操作的店铺信息", requiredness = Requiredness.REQUIRED)
    private ShopDto shop;
    @FieldDoc(description = "当前登录的用户信息", requiredness = Requiredness.REQUIRED)
    private UserDto user;
    @FieldDoc(description = "是否需要物流", requiredness = Requiredness.REQUIRED)
    private Boolean needExpress;
    @FieldDoc(description = "批量发货列表。如果不需要物流，也需要传入订单号信息", requiredness = Requiredness.REQUIRED)
    private List<OrderDeliveryDto> orderWayBillList;

    @Override
    public void checkParameter() {
        if (shop == null) {
            throw new InvalidParamException("shop不能为空");
        }
        this.shop.checkParameter();
        if (user == null) {
            throw new InvalidParamException("user不能为空");
        }
        this.user.checkParameter();
        if (needExpress == null) {
            throw new InvalidParamException("是否需要物流不能为空");
        }
        if (CollUtil.isEmpty(orderWayBillList)) {
            throw new InvalidParamException("订单发货信息不能为空");
        }
        orderWayBillList.forEach(delivery -> {
            if (delivery == null) {
                throw new InvalidParamException("订单发货信息不能为空");
            }
            if (StrUtil.isBlank(delivery.getOrderId())) {
                throw new InvalidParamException("订单号不能为空");
            }
            if (Boolean.TRUE.equals(this.needExpress) && CollUtil.isEmpty(delivery.getExpressList())) {
                throw new InvalidParamException("需要物流时，订单的物流信息不能为空");
            }
        });
    }


}
