package com.sankuai.shangou.seashop.pay.thrift.core.request.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:联系方式
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Contact {
    @FieldDoc(description = "consignor_contact")
    private String consignor_contact; // 寄件人联系方式（顺丰必填）

    @FieldDoc(description = "receiver_contact")
    private String receiver_contact; // 收件人联系方式（顺丰必填）
}
