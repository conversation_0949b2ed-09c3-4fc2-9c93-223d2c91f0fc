package com.sankuai.shangou.seashop.order.thrift.finance.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/01/26 15:49
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "查询中间表查询入参")
public class FinanceQueryReq extends BaseParamReq {

    /**
     * 订单id
     */
    @FieldDoc(description = "订单id", requiredness = Requiredness.REQUIRED)
    private String orderId;

    /**
     * 类型 {@link com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum}
     * <p>
     * PAY(1, "支付"),
     * REFUND(2, "退款"),
     * DEDUCTION(3, "扣款"),
     * FINISH(4, "订单完成"),
     * SETTLEMENT(5, "结算"),
     * ERROR_ORDER_REFUND(6, "异常订单退款"),
     * EXCESS_PAYMENT_REFUND(7, "超支退款")
     */
    @FieldDoc(description = "类型", requiredness = Requiredness.REQUIRED)
    private Integer type;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isEmpty(orderId), "订单id不能为空");
        AssertUtil.throwInvalidParamIfNull(type, "类型不能为空");
    }


}
