package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description:
 */
@TypeDoc(description = "创建退款订单返回对象")
@ToString
@Data
public class PayReverseCreateResp {

    @FieldDoc(description = "汇付支付创建退款时返回的退款订单号")
    private String channelRefundId;


}
