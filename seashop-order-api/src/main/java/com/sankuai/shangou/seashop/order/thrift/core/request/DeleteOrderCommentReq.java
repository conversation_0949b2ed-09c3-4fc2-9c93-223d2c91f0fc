package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/04 16:12
 */
@Data
@ToString
@TypeDoc(description = "删除订单评论入参")
public class DeleteOrderCommentReq extends BaseParamReq {

    @FieldDoc(description = "订单评论id", requiredness = Requiredness.REQUIRED)
    @ExaminField(description = "订单评价id")
    private Long id;

    @FieldDoc(description = "用户id", requiredness = Requiredness.OPTIONAL)
    private Long userId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "订单评论id不能为空");
    }

    public void checkForUser() {
        checkParameter();
        AssertUtil.throwInvalidParamIfTrue(userId == null || userId <= 0, "用户id不能为空");
    }


}
