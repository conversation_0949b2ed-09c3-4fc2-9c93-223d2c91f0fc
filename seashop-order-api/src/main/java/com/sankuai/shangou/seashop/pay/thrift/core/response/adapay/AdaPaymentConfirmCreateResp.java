package com.sankuai.shangou.seashop.pay.thrift.core.response.adapay;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
@TypeDoc(description = "创建支付确认返回对象")
@ToString
@Data
public class AdaPaymentConfirmCreateResp extends BaseThriftDto {

    /**
     * 汇付支付流水号
     */
    @FieldDoc(description = "汇付创建支付确认流水号")
    private String id;

    @FieldDoc(description = "支付确认手续费金额（单位：元）")
    private BigDecimal feeAmt;


    public String getFeeAmtString() {
        return this.bigDecimal2String(this.feeAmt);
    }


    public void setFeeAmtString(String feeAmt) {
        this.feeAmt = this.string2BigDecimal(feeAmt);
    }
}
