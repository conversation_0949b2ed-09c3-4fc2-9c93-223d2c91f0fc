package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/5 10:07
 */
@Data
@ToString
@TypeDoc(description = "投诉维权图片地址返回")
public class OrderRightImagePathResp extends BaseThriftDto {

    @FieldDoc(description = "商品id")
    private String productId;

    @FieldDoc(description = "商品主图地址")
    private String imageUrl;


}
