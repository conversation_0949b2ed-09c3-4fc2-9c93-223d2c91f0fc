package com.sankuai.shangou.seashop.order.thrift.finance.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/6/006
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "已结算明细返回对象")
public class SettlementDetailResp extends BaseThriftDto {

    @FieldDoc(description = "订单实付金额")
    private BigDecimal orderAmount;

    @FieldDoc(description = "渠道金额（手续费）")
    private BigDecimal channelAmount;

    @FieldDoc(description = "佣金金额")
    private BigDecimal commissionAmount;

    @FieldDoc(description = "支付时间")
    private Date payTime;


    public String getOrderAmountString() {
        return this.bigDecimal2String(this.orderAmount);
    }


    public void setOrderAmountString(String orderAmount) {
        this.orderAmount = this.string2BigDecimal(orderAmount);
    }


    public String getChannelAmountString() {
        return this.bigDecimal2String(this.channelAmount);
    }


    public void setChannelAmountString(String channelAmount) {
        this.channelAmount = this.string2BigDecimal(channelAmount);
    }


    public String getCommissionAmountString() {
        return this.bigDecimal2String(this.commissionAmount);
    }


    public void setCommissionAmountString(String commissionAmount) {
        this.commissionAmount = this.string2BigDecimal(commissionAmount);
    }


    public Long getPayTimeLong() {
        return this.date2Long(this.payTime);
    }


    public void setPayTimeLong(Long payTime) {
        this.payTime = this.long2Date(payTime);
    }
}
