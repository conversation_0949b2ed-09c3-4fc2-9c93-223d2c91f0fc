package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopDto;
import lombok.Data;
import lombok.ToString;

@Data
@ToString
@TypeDoc(description = "供应商上传发票请求入参")
public class SellerUploadInvoiceReq extends BaseParamReq {
    @FieldDoc(
            description = "订单id",
            requiredness = Requiredness.REQUIRED
    )
    private String orderId;
    @FieldDoc(
            description = "当前登录店铺信息",
            requiredness = Requiredness.REQUIRED
    )
    private ShopDto shop;
    @FieldDoc(
            description = "发票地址",
            requiredness = Requiredness.REQUIRED
    )
    private String invoiceUrl;

    public void checkParameter() {
        if (this.orderId == null) {
            throw new InvalidParamException("订单id不能为空");
        } else if (this.shop == null) {
            throw new InvalidParamException("店铺信息不能为空");
        } else {
            this.shop.checkParameter();
            if (StrUtil.isBlank(this.invoiceUrl)) {
                throw new InvalidParamException("发票地址不能为空");
            }
        }
    }
}
