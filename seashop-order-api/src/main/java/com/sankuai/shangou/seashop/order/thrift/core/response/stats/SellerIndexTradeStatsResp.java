package com.sankuai.shangou.seashop.order.thrift.core.response.stats;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "供应商首页交易数据统计")
public class SellerIndexTradeStatsResp extends BaseThriftDto {

    /**
     * 全部订单数
     */
    @FieldDoc(description = "全部订单数")
    private Long orderCount;
    /**
     * 待付款订单数量
     */
    @FieldDoc(description = "待付款订单数量")
    private Long underPayOrderCount;
    /**
     * 待发货订单数量
     */
    @FieldDoc(description = "待发货订单数量")
    private Long underDeliveryOrderCount;
    /**
     * 待处理 退款单数量
     */
    @FieldDoc(description = "待处理 退款单数量")
    private Long underDealRefundCount;
    /**
     * 待处理 退货单数量
     */
    @FieldDoc(description = "待处理 退货单数量")
    private Long underDealReturnCount;
    /**
     * 待处理 投诉单数量
     */
    @FieldDoc(description = "待处理 投诉单数量")
    private Long underDealComplaintCount;
    /**
     * 待回复评论数
     */
    @FieldDoc(description = "待回复评论数")
    private Long underReplyCommentCount;
    /**
     * 昨日访客数
     */
    @FieldDoc(description = "昨日访客数")
    private Long yesterdayVisitCount;
    /**
     * 昨日下单数
     */
    @FieldDoc(description = "昨日下单数")
    private Long yesterdayOrderCount;
    /**
     * 昨日付款数
     */
    @FieldDoc(description = "昨日付款数")
    private Long yesterdayPayCount;
    /**
     * 商品总评价数
     */
    @FieldDoc(description = "商品总评价数")
    private Long totalProductCommentCount;


}
