package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnumValue;

import java.util.Arrays;

/**
 * 售后日志记录的步骤枚举，同.net保持一致，与状态有不完全一样
 * <AUTHOR>
 */
public enum RefundLogStepEnum {

    // 待供应商审核
    WAIT_SUPPLIER_AUDIT(1, "待供应商审核"),
    // 待买家寄货
    WAIT_BUYER_SEND(2, "待买家寄货"),
    // 待供应商收货
    WAIT_SUPPLIER_RECEIVE(3, "待供应商收货"),
    // 供应商拒绝
    SUPPLIER_REFUSE(4, "供应商拒绝"),
    // 待平台确认
    WAIT_PLATFORM_CONFIRM(5, "待平台确认"),
    // 退款成功
    REFUND_SUCCESS(6, "退款成功"),
    // 平台驳回
    PLATFORM_REFUSE(7, "平台驳回"),
    // 买家取消售后申请
    BUYER_CANCEL(8, "买家取消售后申请"),


    ;

    private final Integer code;
    private final String desc;

    RefundLogStepEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .map(RefundLogStepEnum::getDesc)
                .orElse(null);
    }

    /**
     * 供应商审核状态与步骤对应关系
     * @param code
     * @return
     */
    public static RefundLogStepEnum valueBySellerStatus(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
