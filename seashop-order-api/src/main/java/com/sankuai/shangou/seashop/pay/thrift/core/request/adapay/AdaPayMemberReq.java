package com.sankuai.shangou.seashop.pay.thrift.core.request.adapay;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description: 个人账号注册请求对象
 */

@Data
@TypeDoc(description = "个人账号注册请求对象")
public class AdaPayMemberReq extends AdaPayBaseReq {

    /**
     * 用户地址
     */
    @FieldDoc(description = "用户地址")
    private String location;
    /**
     * 用户邮箱
     */
    @FieldDoc(description = "用户邮箱")
    private String email;
    /**
     * MALE：男，FEMALE：女，为空时表示未填写
     */
    @FieldDoc(description = "MALE：男，FEMALE：女，为空时表示未填写")
    private String gender;
    /**
     * 用户昵称
     */
    @FieldDoc(description = "用户昵称")
    private String nickname;


}
