package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商家查询售后明细请求入参")
public class UserQueryRefundDetailReq extends BaseParamReq {

    @FieldDoc(description = "退款ID", requiredness = Requiredness.REQUIRED)
    private Long refundId;
    @FieldDoc(description = "用户", requiredness = Requiredness.REQUIRED)
    private UserDto user;

    @Override
    public void checkParameter() {
        if (refundId == null) {
            throw new InvalidParamException("退款ID不能为空");
        }
        if (user == null) {
            throw new InvalidParamException("用户信息不能为空");
        }
        this.user.checkParameter();
    }


}
