package com.sankuai.shangou.seashop.order.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 根据时间查询订单用户，查询对象
 * <AUTHOR>
 */
@Data
@Schema(description = "根据时间查询订单用户，查询对象入参")
public class QueryOrderUserReq {

    @Schema(name = "payDate", description = "支付时间")
    private Date payDate;

    @Schema(name = "orderDate", description = "下单时间")
    private Date orderDate;

    @Schema(name = "effectivePayAmountOperation", description = "有效购买金额操作符（1：大于，2：小于，3：区间）")
    private Integer effectivePayAmountOperation;

    @Schema(name = "effectivePayAmountValue", description = "有效购买金额（值）")
    private BigDecimal effectivePayAmountValue;
    @Schema(name = "effectivePayAmountValue2", description = "有效购买金额（值），区间后面一个值")
    private BigDecimal effectivePayAmountValue2;

    @Schema(name = "productIds", description = "购买商品列表")
    private List<Long> productIds;

    @Schema(name = "categoryId", description = "指定商品分类")
    private Long categoryId;

    @Schema(name = "averagePriceOperation", description = "笔单价操作符（1：大于，2：小于，3：区间）")
    private Integer averagePriceOperation;

    @Schema(name = "averagePriceValue", description = "笔单价（值）")
    private BigDecimal averagePriceValue;
    @Schema(name = "averagePriceValue2", description = "笔单价（值），区间后面一个值")
    private BigDecimal averagePriceValue2;

    @Schema(name = "noRefundOperation", description = "无售后订单操作符（1：大于，2：小于，3：区间）")
    private Integer noRefundOperation;

    @Schema(name = "noRefundValue", description = "无售后订单（值）")
    private Integer noRefundValue;
    @Schema(name = "noRefundValue2", description = "无售后订单（值），区间后面一个值")
    private Integer noRefundValue2;

    @Schema(name = "targetUserIds", description = "目标用户id列表（支付用户在这个范围内）")
    private List<Long> targetUserIds;



}
