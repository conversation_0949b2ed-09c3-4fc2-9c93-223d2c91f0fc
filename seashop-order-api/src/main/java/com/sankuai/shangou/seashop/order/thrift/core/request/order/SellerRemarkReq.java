package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "供应商备注请求入参")
public class SellerRemarkReq extends BaseParamReq {

    @FieldDoc(description = "订单id", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "当前登录店铺信息", requiredness = Requiredness.REQUIRED)
    private ShopDto shop;
    @FieldDoc(description = "备注", requiredness = Requiredness.REQUIRED)
    private String remark;
    @FieldDoc(description = "备注标记。一次1-4，4面小旗", requiredness = Requiredness.REQUIRED)
    private Integer remarkFlag;

    @Override
    public void checkParameter() {
        if (orderId == null) {
            throw new InvalidParamException("订单id不能为空");
        }
        if (shop == null) {
            throw new InvalidParamException("店铺信息不能为空");
        }
        shop.checkParameter();
        if (StrUtil.isBlank(remark)) {
            throw new InvalidParamException("备注不能为空");
        }
        if (remarkFlag == null) {
            throw new InvalidParamException("备注标记不能为空");
        }
    }


}
