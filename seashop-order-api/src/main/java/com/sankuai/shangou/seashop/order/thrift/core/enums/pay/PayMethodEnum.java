package com.sankuai.shangou.seashop.order.thrift.core.enums.pay;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ThriftEnum
public enum PayMethodEnum {

    ALIPAY_SCAN(1, "支付宝扫码", Arrays.asList(PayPlatformEnum.PC)),
    ALIPAY_H5(2, "支付宝H5", Arrays.asList()),
    WECHAT_APPLET(3, "微信小程序", Arrays.asList(PayPlatformEnum.WECHAT)),
    WECHAT_H5(4, "微信H5", Arrays.asList()),
    WECHAT_NATIVE(7, "微信扫码", Arrays.asList(PayPlatformEnum.PC)),
    WECHAT_JS(11, "微信商城", Arrays.asList(PayPlatformEnum.WECHAT)),
    COMPANY_BANK(5, "企业网银", Arrays.asList(PayPlatformEnum.PC)),
    PERSON_BANK(6, "个人网银", Arrays.asList(PayPlatformEnum.PC)),
    ;

    private final Integer code;
    @Getter
    private final String desc;
    @Getter
    private final List<PayPlatformEnum> supportPlatforms;

    PayMethodEnum(Integer code, String desc, List<PayPlatformEnum> supportPlatforms) {
        this.code = code;
        this.desc = desc;
        this.supportPlatforms = supportPlatforms;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(payMethod -> payMethod.code.equals(code))
                .findFirst()
                .map(PayMethodEnum::getDesc)
                .orElse("");
    }

    public static List<PayMethodEnum> getSupportMethods(PayPlatformEnum platform) {
        return Arrays.stream(values())
                .filter(payMethod -> payMethod.supportPlatforms.contains(platform))
                .collect(Collectors.toList());
    }

    public static boolean isWeChat(Integer code) {
        return WECHAT_APPLET.code.equals(code) || WECHAT_H5.code.equals(code) || WECHAT_NATIVE.code.equals(code);
    }

}
