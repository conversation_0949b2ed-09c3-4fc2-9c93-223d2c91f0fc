package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/5 15:41
 */
@Data
@ToString
@TypeDoc(description = "用于记录订单与支付单之间的映射返回对象")
public class OrderPayRecordResp extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    private Long id;

    @FieldDoc(description = "支付批次单号。多笔订单一起支付时batch_no一致")
    private String batchNo;

    @FieldDoc(description = "订单号。与 order 表 order_id 一致")
    private String orderId;

    @FieldDoc(description = "支付渠道的唯一标识。目前时汇付的支付ID")
    private String payNo;

    @FieldDoc(description = "支付渠道。目前固定为7：汇付天下支付")
    private Integer payChannel;

    @FieldDoc(description = "支付方式。1：支付宝扫码；2：支付宝H5；3：微信小程序；4：微信H5；5：企业网银；6：个人网银")
    private Integer payMethod;

    @FieldDoc(description = "订单需要付款金额")
    private BigDecimal orderAmount;

    @FieldDoc(description = "支付金额。可能在订单需付金额基础上还有渠道优惠")
    private BigDecimal payAmount;

    @FieldDoc(description = "支付状态。0：关闭；1：支付中；2：支付成功；3：支付失败")
    private Integer payStatus;

    @FieldDoc(description = "外部交易单号，比如支付宝的支付流水号")
    private String outTransId;

    @FieldDoc(description = "支付时间，支付回调时设置")
    private Date payTime;

    @FieldDoc(description = "备注")
    private String remark;


    public String getOrderAmountString() {
        return this.bigDecimal2String(this.orderAmount);
    }


    public void setOrderAmountString(String orderAmount) {
        this.orderAmount = this.string2BigDecimal(orderAmount);
    }


    public String getPayAmountString() {
        return this.bigDecimal2String(this.payAmount);
    }


    public void setPayAmountString(String payAmount) {
        this.payAmount = this.string2BigDecimal(payAmount);
    }


    public Long getPayTimeLong() {
        return this.date2Long(this.payTime);
    }


    public void setPayTimeLong(Long payTime) {
        this.payTime = this.long2Date(payTime);
    }


}
