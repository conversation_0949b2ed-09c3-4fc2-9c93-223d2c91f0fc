package com.sankuai.shangou.seashop.pay.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description: 开通私有账户以及结算账户请求对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "个人账号注册并创建结算账号请求对象")
public class PayMemberAndAccountReq extends BaseParamReq {

    @FieldDoc(description = "注册账号对象", requiredness = Requiredness.REQUIRED)
    private PayMemberReq payMemberReq;

    @FieldDoc(description = "创建结算账号对象", requiredness = Requiredness.REQUIRED)
    private PaySettleAccountReq paySettleAccountReq;

    @Override
    public void checkParameter() {
        if(null == this.payMemberReq){
            throw new InvalidParamException("payMemberReq不能为空");
        }
        if(null == this.paySettleAccountReq){
            throw new InvalidParamException("paySettleAccountReq不能为空");
        }
        this.payMemberReq.checkParameter();
        this.paySettleAccountReq.checkParameter();
    }


}
