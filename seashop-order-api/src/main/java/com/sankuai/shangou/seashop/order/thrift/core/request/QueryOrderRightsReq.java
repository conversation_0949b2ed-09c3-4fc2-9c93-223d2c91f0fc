package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/22 9:52
 */
@Data
@ToString
@TypeDoc(description = "查询投诉维权入参")
public class QueryOrderRightsReq extends BasePageReq {

    @FieldDoc(description = "商家ID")
    private Long userId;

    @FieldDoc(description = "订单状态")
    private Integer orderStatus;

    @FieldDoc(description = "投诉单状态")
    private Integer complaintStatus;


    public void checkParameter(){
        if (userId == null) {
            throw new IllegalArgumentException("userId is null");
        }
    }
}
