package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/25 12:01
 */
@Data
@ToString
@TypeDoc(description = "查询店铺评价数据汇总")
public class QueryShopCommentSummaryReq extends BaseParamReq {

    @FieldDoc(description = "店铺id", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }


}
