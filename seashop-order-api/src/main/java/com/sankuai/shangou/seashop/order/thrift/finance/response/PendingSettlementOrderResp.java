package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@TypeDoc(description = "待结算订单")
@ToString
@Data
public class PendingSettlementOrderResp extends BaseThriftDto {

    /**
     * 编号
     */
    @FieldDoc(description = "主键")
    private Long id;

    /**
     * 店铺ID
     */
    @FieldDoc(description = "店铺id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @FieldDoc(description = "店铺名称")
    private String shopName;

    /**
     * 订单号
     */
    @FieldDoc(description = "订单号")
    private String orderId;

    /**
     * 订单状态(1:待付款,2:待发货,3:待收货,4:已关闭,5:已完成,6:支付中)
     */
    @FieldDoc(description = "订单状态(1:待付款,2:待发货,3:待收货,4:已关闭,5:已完成,6:支付中)")
    private Integer orderStatus;

    /**
     * 订单金额
     */
    @FieldDoc(description = "订单金额")
    private BigDecimal orderAmount;

    /**
     * 平台佣金
     */
    @FieldDoc(description = "平台佣金")
    private BigDecimal platCommission;

    /**
     * 退款金额
     */
    @FieldDoc(description = "退款金额")
    private BigDecimal refundAmount;

    /**
     * 结算金额
     */
    @FieldDoc(description = "结算金额")
    private BigDecimal settlementAmount;

    /**
     * 渠道手续费
     */
    @FieldDoc(description = "渠道手续费")
    private BigDecimal channelAmount;

    /**
     * 创建时间
     */
    @FieldDoc(description = "创建时间")
    private Date createTime;

    /**
     * 支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银
     */
    @FieldDoc(description = "支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银")
    private Integer paymentType;

    @FieldDoc(description = "支付方式名称")
    private String paymentTypeName;

    @FieldDoc(description = "支付时间")
    private Date payTime;

    @FieldDoc(description = "订单完成时间")
    private Date orderFinishTime;


    public String getOrderAmountString() {
        return this.bigDecimal2String(this.orderAmount);
    }


    public void setOrderAmountString(String orderAmount) {
        this.orderAmount = this.string2BigDecimal(orderAmount);
    }


    public String getPlatCommissionString() {
        return this.bigDecimal2String(this.platCommission);
    }


    public void setPlatCommissionString(String platCommission) {
        this.platCommission = this.string2BigDecimal(platCommission);
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


    public String getSettlementAmountString() {
        return this.bigDecimal2String(this.settlementAmount);
    }


    public void setSettlementAmountString(String settlementAmount) {
        this.settlementAmount = this.string2BigDecimal(settlementAmount);
    }


    public String getChannelAmountString() {
        return this.bigDecimal2String(this.channelAmount);
    }


    public void setChannelAmountString(String channelAmount) {
        this.channelAmount = this.string2BigDecimal(channelAmount);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


    public Long getPayTimeLong() {
        return this.date2Long(this.payTime);
    }


    public void setPayTimeLong(Long payTime) {
        this.payTime = this.long2Date(payTime);
    }


    public Long getOrderFinishTimeLong() {
        return this.date2Long(this.orderFinishTime);
    }


    public void setOrderFinishTimeLong(Long orderFinishTime) {
        this.orderFinishTime = this.long2Date(orderFinishTime);
    }
}
