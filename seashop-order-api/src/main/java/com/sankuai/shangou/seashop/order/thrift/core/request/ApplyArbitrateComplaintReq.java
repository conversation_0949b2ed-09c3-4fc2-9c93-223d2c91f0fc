package com.sankuai.shangou.seashop.order.thrift.core.request;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/24 8:57
 */

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * @description：投诉维权供应商申请仲裁
 * @author： liweisong
 * @create： 2023/11/24 8:53
 * 申请仲裁，入参应该为id,status传3
 */
@Data
@ToString
@TypeDoc(description = "投诉维权供应商申请仲裁")
public class ApplyArbitrateComplaintReq extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    @PrimaryField
    private Long id;

    @FieldDoc(description = "审核状态(1:等待供应商处理,2:供应商处理完成,3:等待平台介入,4:已结束，5：取消)")
    @ExaminField(description = "审核状态(1:等待供应商处理,2:供应商处理完成,3:等待平台介入,4:已结束，5：取消)")
    private Integer status;

    @FieldDoc(description = "店铺ID")
    @ExaminField(description = "店铺ID")
    private Long shopId;


    /**
     * 参数校验
     */
    public void checkParameter() {
        if (id == null) {
            throw new InvalidParamException("id不能为空");
        }
        if (status == null) {
            throw new InvalidParamException("status不能为空");
        }
    }


}
