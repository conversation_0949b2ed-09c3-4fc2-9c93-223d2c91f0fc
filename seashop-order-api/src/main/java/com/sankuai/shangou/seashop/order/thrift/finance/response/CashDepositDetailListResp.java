package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@TypeDoc(description = "保证金支付明细列表")
@ToString
@Data
public class CashDepositDetailListResp extends BaseThriftDto {

    @FieldDoc(description = "明细列表")
    private List<CashDepositDetailResp> list;


}
