package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import lombok.Getter;

/**
 * 扩充的退款类型，根据是否整单退，返回前端的文案有区别，这个扩充类型是针对返回前端的文案的
 * <AUTHOR>
 */
@Getter
public enum ExtRefundTypeEnum {

    // 发货前整单退款
    BEFORE_DELIVER_REFUND("整单退款"),
    // 发货后整单仅退款
    AFTER_DELIVER_ONLY_REFUND("整单仅退款"),
    // 发货后整单退货退款
    AFTER_DELIVER_RETURN_AND_REFUND("整单退货退款"),
    // 发货后商品仅退款
    AFTER_DELIVER_GOODS_ONLY_REFUND("商品仅退款"),
    // 发货后商品退货退款
    AFTER_DELIVER_GOODS_RETURN_AND_REFUND("商品退货退款"),

    ;

    private final String desc;

    ExtRefundTypeEnum(String desc) {
        this.desc = desc;
    }

    public static String getDesc(Integer mode, Boolean hasAllReturn) {
        // 订单退款，对应的就是发货前整单退款，只能选仅退款
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(mode)) {
            return BEFORE_DELIVER_REFUND.getDesc();
        }
        // 如果是仅退款模式，则根据是否退货区分是商品还是整单
        if (RefundModeEnum.GOODS_REFUND.getCode().equals(mode)) {
            if (hasAllReturn) {
                return AFTER_DELIVER_ONLY_REFUND.getDesc();
            }
            return AFTER_DELIVER_GOODS_ONLY_REFUND.getDesc();
        }
        if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(mode)) {
            if (hasAllReturn) {
                return AFTER_DELIVER_RETURN_AND_REFUND.getDesc();
            }
            return AFTER_DELIVER_GOODS_RETURN_AND_REFUND.getDesc();
        }
        return null;
    }

}
