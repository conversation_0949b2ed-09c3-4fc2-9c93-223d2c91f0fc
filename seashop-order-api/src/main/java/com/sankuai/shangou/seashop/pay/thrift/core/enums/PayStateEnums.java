package com.sankuai.shangou.seashop.pay.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.AllArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
@ThriftEnum
@AllArgsConstructor
public enum PayStateEnums {

    /**
     * 订单支付结果
     */
    UNPAID(0, "未支付"),
    PAID(1, "已支付"),
    PAY_FAILED(2, "支付失败");

    /**
     * 返回状态
     */
    private Integer status;

    /**
     * 显示名
     */
    private String displayName;

    @ThriftEnumValue
    public Integer getStatus() {
        return status;
    }

    public String getDisplayName() {
        return displayName;
    }

}
