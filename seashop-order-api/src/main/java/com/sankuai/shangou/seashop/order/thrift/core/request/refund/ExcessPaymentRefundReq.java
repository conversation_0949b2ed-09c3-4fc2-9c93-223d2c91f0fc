package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2024/1/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "超支退款请求对象")
public class ExcessPaymentRefundReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;

    @FieldDoc(description = "渠道ID(交易流水)", requiredness = Requiredness.REQUIRED)
    private String channelId;

    @FieldDoc(description = "退款金额", requiredness = Requiredness.REQUIRED)
    private BigDecimal refundAmount;

    @FieldDoc(description = "操作人ID", requiredness = Requiredness.REQUIRED)
    private Long operatorId;

    @FieldDoc(description = "操作人名称", requiredness = Requiredness.REQUIRED)
    private String operatorName;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.orderId)) {
            throw new InvalidParamException("订单ID不能为空");
        }
        if (StrUtil.isBlank(this.channelId)) {
            throw new InvalidParamException("渠道ID不能为空");
        }
        if (this.refundAmount == null || this.refundAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("退款金额不能为空");
        }
        if (this.operatorId == null || this.operatorId <= 0) {
            throw new InvalidParamException("操作人ID不能为空");
        }
        if (StrUtil.isBlank(this.operatorName)) {
            throw new InvalidParamException("操作人名称不能为空");
        }
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


}
