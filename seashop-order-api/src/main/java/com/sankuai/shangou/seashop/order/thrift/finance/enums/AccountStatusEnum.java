package com.sankuai.shangou.seashop.order.thrift.finance.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/12/21/021
 * @description:
 */
@ThriftEnum
public enum AccountStatusEnum {

    UN_ACCOUNT(0, "未结算"),
    ACCOUNTED(1, "已结算");

    private Integer status;
    private String desc;

    @ThriftEnumValue
    public Integer getStatus() {
        return status;
    }

    public String getDesc() {
        return desc;
    }

    AccountStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
