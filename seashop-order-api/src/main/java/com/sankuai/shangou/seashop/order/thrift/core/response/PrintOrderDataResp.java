package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 16:19
 */
@Data
@TypeDoc(description = "打印订单出参")
public class PrintOrderDataResp extends BaseThriftDto {

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @FieldDoc(description = "订单号")
    private String orderId;

    @FieldDoc(description = "下单时间")
    private Date orderDate;

    @FieldDoc(description = "收货人姓名")
    private String shipTo;

    @FieldDoc(description = "联系方式")
    private String cellPhone;

    @FieldDoc(description = "地址(全路径详细地址regionFullName+address)")
    private String address;

    @FieldDoc(description = "商品总价")
    private BigDecimal productTotalAmount;

    @FieldDoc(description = "运费")
    private BigDecimal freight;

    @FieldDoc(description = "实付金额")
    private BigDecimal totalAmount;

    @FieldDoc(description = "备注")
    private String orderRemarks;


    public Long getOrderDateLong() {
        return this.date2Long(this.orderDate);
    }


    public void setOrderDateLong(Long orderDate) {
        this.orderDate = this.long2Date(orderDate);
    }


    public String getProductTotalAmountString() {
        return this.bigDecimal2String(this.productTotalAmount);
    }


    public void setProductTotalAmountString(String productTotalAmount) {
        this.productTotalAmount = this.string2BigDecimal(productTotalAmount);
    }


    public String getFreightString() {
        return this.bigDecimal2String(this.freight);
    }


    public void setFreightString(String freight) {
        this.freight = this.string2BigDecimal(freight);
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


}
