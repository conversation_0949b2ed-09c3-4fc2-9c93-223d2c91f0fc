package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "操作步骤对象")
@ToString
@Data
public class RefundStepDto extends BaseThriftDto {

    /**
     * 退款步骤
     */
    @FieldDoc(description = "退款步骤")
    private Integer stepCode;
    /**
     * 退款步骤描述
     */
    @FieldDoc(description = "退款步骤描述")
    private String stepDesc;
    /**
     * 是否需要高亮
     */
    @FieldDoc(description = "是否需要高亮。高亮控制是否灰色，为true时需要显示橘黄色")
    private Boolean highlight = false;
    /**
     * 是否是当前步骤，橘黄色高亮
     */
    @FieldDoc(description = "是否是当前步骤，橘黄色高亮")
    private Boolean currentStep = false;


}
