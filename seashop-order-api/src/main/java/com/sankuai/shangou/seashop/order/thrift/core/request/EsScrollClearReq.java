package com.sankuai.shangou.seashop.order.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "ES滚动清除入参")
public class EsScrollClearReq extends BaseParamReq {

    @FieldDoc(description = "滚动ID，需要先根据查询条件调用ES接口单独获取")
    private String scrollId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(StrUtil.isBlank(scrollId), "滚动查询ID不能为空");
    }


}
