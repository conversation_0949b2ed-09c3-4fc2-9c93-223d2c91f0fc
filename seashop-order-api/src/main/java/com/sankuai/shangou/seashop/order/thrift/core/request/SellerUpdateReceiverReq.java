package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "供应商修改收货地址请求入参")
public class SellerUpdateReceiverReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "顶级区域ID(省ID)")
    private Integer topRegionId;
    @FieldDoc(description = "区域ID", requiredness = Requiredness.REQUIRED)
    private Integer regionId;
    @FieldDoc(description = "收货人", requiredness = Requiredness.REQUIRED)
    private String shipTo;
    @FieldDoc(description = "收货地址", requiredness = Requiredness.REQUIRED)
    private String address;
    @FieldDoc(description = "详细地址", requiredness = Requiredness.REQUIRED)
    private String addressDetail;
    @FieldDoc(description = "收货人电话", requiredness = Requiredness.REQUIRED)
    private String cellPhone;
    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;
    @FieldDoc(description = "用户名", requiredness = Requiredness.REQUIRED)
    private String userName;

    @Override
    public void checkParameter() {
        if (orderId == null) {
            throw new InvalidParamException("orderId不能为空");
        }
        if (regionId == null) {
            throw new InvalidParamException("regionId不能为空");
        }
        if (shipTo == null) {
            throw new InvalidParamException("shipTo不能为空");
        }
        if (address == null) {
            throw new InvalidParamException("address不能为空");
        }
        if (shopId == null) {
            throw new InvalidParamException("shopId不能为空");
        }
    }


}
