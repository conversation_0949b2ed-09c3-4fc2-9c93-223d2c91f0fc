package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "供应商改价入参")
public class UpdateItemAmountReq extends BaseParamReq {

    @FieldDoc(description = "店铺ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;
    @FieldDoc(description = "订单明细ID", requiredness = Requiredness.REQUIRED)
    private Long itemId;
    @FieldDoc(description = "修改后的价格", requiredness = Requiredness.REQUIRED)
    private BigDecimal updatedAmount;
    @FieldDoc(description = "用户名", requiredness = Requiredness.OPTIONAL)
    private String userName;

    @Override
    public void checkParameter() {
        if (shopId == null || shopId <= 0) {
            throw new InvalidParamException("shopId不能为空");
        }
        if (itemId == null || itemId <= 0) {
            throw new InvalidParamException("itemId不能为空");
        }
        if (updatedAmount == null || updatedAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidParamException("请输入正确的改价金额");
        }
    }


    public String getUpdatedAmountString() {
        return this.bigDecimal2String(this.updatedAmount);
    }


    public void setUpdatedAmountString(String updatedAmount) {
        this.updatedAmount = this.string2BigDecimal(updatedAmount);
    }


}
