package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderAddressInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderExpressDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInvoiceDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderItemInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */

@ToString
@TypeDoc(description = "ERP订单明细返回对象")
@Data
public class ErpOrderDetailResp extends BaseThriftDto {

    @FieldDoc(description = "订单号")
    private String orderId;

    @FieldDoc(description = "牵牛花订单号")
    private String sourceOrderId;

    @FieldDoc(description = "userId")
    private Long userId;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "店铺名称")
    private String shopName;

    @Setter
    @Getter
    @FieldDoc(description = "下单时间")
    private Date orderDate;

    @Getter
    @Setter
    @FieldDoc(description = "商品总金额")
    private BigDecimal productTotalAmount;

    @Setter
    @Getter
    @FieldDoc(description = "实付金额")
    private BigDecimal totalAmount;

    @Setter
    @Getter
    @FieldDoc(description = "实收金额")
    private BigDecimal actualPayAmount;


    @FieldDoc(description = "订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中")
    private OrderStatusEnum orderStatus;

    @FieldDoc(description = "订单商品列表")
    private List<OrderItemInfoDto> itemList;

    @FieldDoc(description = "预计完成时间。待发货状态才有，显示自动确认收货时间")
    private String estimateCompleteTime;

    @Getter
    @Setter
    @FieldDoc(description = "优惠券金额")
    private BigDecimal couponAmount;

    @Setter
    @Getter
    @FieldDoc(description = "运费金额")
    private BigDecimal freightAmount;

    @Getter
    @Setter
    @FieldDoc(description = "税费金额")
    private BigDecimal taxAmount;

    @FieldDoc(description = "买家留言")
    private String userRemark;

    @Getter
    @Setter
    @FieldDoc(description = "折扣金额")
    private BigDecimal discountAmount;

    @Setter
    @Getter
    @FieldDoc(description = "满减金额")
    private BigDecimal reductionAmount;

    @FieldDoc(description = "支付渠道")
    private String payChannelName;

    @Setter
    @Getter
    @FieldDoc(description = "付款日期")
    private Date payDate;

    @Getter
    @Setter
    @FieldDoc(description = "发货日期")
    private Date shippingDate;

    @Setter
    @Getter
    @FieldDoc(description = "完成日期")
    private Date finishDate;

    @Setter
    @Getter
    @FieldDoc(description = "创建日期")
    private Date createDate;


    @FieldDoc(description = "是否有售后")
    private Boolean hasRefund;

    @FieldDoc(description = "售后ID")
    private Long refundId;

    @FieldDoc(description = "收货人地址信息")
    private OrderAddressInfoDto orderAddress;
    /**
     * 物流信息
     */
    @FieldDoc(description = "物流信息")
    private List<OrderExpressDto> expressList;

    @FieldDoc(description = "买家名称")
    private String userName;

    @FieldDoc(description = "买家备注")
    private String sellerRemark;

    @FieldDoc(description = "支付类型 1-线上支付")
    private Integer paymentType;

    @Setter
    @Getter
    @FieldDoc(description = "可退金额")
    private BigDecimal enabledRefundAmount;

    /**
     * 订单来源 0表示本系统，1表示牵牛花
     */
    //@FieldDoc(description = "订单来源 0表示本系统，1表示牵牛花")
    //private Integer orderSource;

    @Setter
    @Getter
    @FieldDoc(description = "更新时间")
    private Date updateTime;

    @FieldDoc(description = "卖家备注标记。一次1-4，4面小旗")
    private Integer sellerRemarkFlag;

    @FieldDoc(description = "发票信息")
    private OrderInvoiceDto orderInvoice;

    @FieldDoc(description = "支付ID")
    private String gatewayOrderId;

    @FieldDoc(description = "关闭原因")
    private String closeReason;


    public Long getOrderDateLong() {
        return this.date2Long(this.orderDate);
    }


    public void setOrderDateLong(Long orderDate) {
        this.orderDate = this.long2Date(orderDate);
    }


    public String getProductTotalAmountString() {
        return this.bigDecimal2String(this.productTotalAmount);
    }


    public void setProductTotalAmountString(String productTotalAmount) {
        this.productTotalAmount = this.string2BigDecimal(productTotalAmount);
    }


    public String getTotalAmountString() {
        return this.bigDecimal2String(this.totalAmount);
    }


    public void setTotalAmountString(String totalAmount) {
        this.totalAmount = this.string2BigDecimal(totalAmount);
    }


    public String getActualPayAmountString() {
        return this.bigDecimal2String(this.actualPayAmount);
    }


    public void setActualPayAmountString(String actualPayAmount) {
        this.actualPayAmount = this.string2BigDecimal(actualPayAmount);
    }


    public String getCouponAmountString() {
        return this.bigDecimal2String(this.couponAmount);
    }


    public void setCouponAmountString(String couponAmount) {
        this.couponAmount = this.string2BigDecimal(couponAmount);
    }


    public String getFreightAmountString() {
        return this.bigDecimal2String(this.freightAmount);
    }


    public void setFreightAmountString(String freightAmount) {
        this.freightAmount = this.string2BigDecimal(freightAmount);
    }


    public String getTaxAmountString() {
        return this.bigDecimal2String(this.taxAmount);
    }


    public void setTaxAmountString(String taxAmount) {
        this.taxAmount = this.string2BigDecimal(taxAmount);
    }


    public String getDiscountAmountString() {
        return this.bigDecimal2String(this.discountAmount);
    }


    public void setDiscountAmountString(String discountAmount) {
        this.discountAmount = this.string2BigDecimal(discountAmount);
    }


    public String getReductionAmountString() {
        return this.bigDecimal2String(this.reductionAmount);
    }


    public void setReductionAmountString(String reductionAmount) {
        this.reductionAmount = this.string2BigDecimal(reductionAmount);
    }


    public Long getPayDateLong() {
        return this.date2Long(this.payDate);
    }


    public void setPayDateLong(Long payDate) {
        this.payDate = this.long2Date(payDate);
    }


    public Long getShippingDateLong() {
        return this.date2Long(this.shippingDate);
    }


    public void setShippingDateLong(Long shippingDate) {
        this.shippingDate = this.long2Date(shippingDate);
    }


    public Long getFinishDateLong() {
        return this.date2Long(this.finishDate);
    }


    public void setFinishDateLong(Long finishDate) {
        this.finishDate = this.long2Date(finishDate);
    }


    public Long getCreateDateLong() {
        return this.date2Long(this.createDate);
    }


    public void setCreateDateLong(Long createDate) {
        this.createDate = this.long2Date(createDate);
    }


    public String getEnabledRefundAmountString() {
        return this.bigDecimal2String(this.enabledRefundAmount);
    }


    public void setEnabledRefundAmountString(String enabledRefundAmount) {
        this.enabledRefundAmount = this.string2BigDecimal(enabledRefundAmount);
    }

    //
    //public Integer getOrderSource() {
    //    return orderSource;
    //}
    //
    //
    //public void setOrderSource(Integer orderSource) {
    //    this.orderSource = orderSource;
    //}


    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }


    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }


}
