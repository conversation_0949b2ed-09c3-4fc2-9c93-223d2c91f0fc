package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "买家寄货请求参数")
public class UserDeliverReq extends BaseParamReq {

    @FieldDoc(description = "用户信息", requiredness = Requiredness.REQUIRED)
    private UserDto user;
    @FieldDoc(description = "退款单号", requiredness = Requiredness.REQUIRED)
    private Long refundId;
    @FieldDoc(description = "快递公司名称", requiredness = Requiredness.REQUIRED)
    private String expressCompanyName;
    @FieldDoc(description = "快递单号", requiredness = Requiredness.REQUIRED)
    private String shipOrderNumber;
    @FieldDoc(description = "快递公司编码", requiredness = Requiredness.OPTIONAL)
    private String expressCompanyCode;

    @Override
    public void checkParameter() {
        if (user == null) {
            throw new InvalidParamException("用户信息不能为空");
        }
        this.user.checkParameter();
        if (refundId == null) {
            throw new InvalidParamException("退款单号不能为空");
        }
        if (StrUtil.isBlank(expressCompanyName)) {
            throw new InvalidParamException("快递公司名称不能为空");
        }
        if (StrUtil.isBlank(shipOrderNumber)) {
            throw new InvalidParamException("快递单号不能为空");
        }
        if (StrUtil.isBlank(expressCompanyCode)) {
            throw new InvalidParamException("快递公司编码不能为空");
        }
    }


}
