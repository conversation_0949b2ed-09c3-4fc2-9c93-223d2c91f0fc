package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description:创建结算账户返回对象
 */
@TypeDoc(description = "创建结算账户返回对象")
@ToString
@Data
public class PaySettleAccountResp {

    /**
     * 生成的结算账户对象 id
     */
    @FieldDoc(description = "生成的结算账户对象 id")
    private String id;


}
