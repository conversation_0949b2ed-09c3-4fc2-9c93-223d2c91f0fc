package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "平台批量审核退款返回结果")
public class PlatformApproveBatchResp {

    @FieldDoc(description = "发起退款总条数")
    private Integer totalCount;
    @FieldDoc(description = "审核通过总条数")
    private Integer successCount;
    @FieldDoc(description = "审核失败总条数")
    private Integer failCount;
    @FieldDoc(description = "审核失败原因")
    private String errorDesc;
    @FieldDoc(description = "审核失败列表")
    private List<PlatformApproveResp> errorList;


}
