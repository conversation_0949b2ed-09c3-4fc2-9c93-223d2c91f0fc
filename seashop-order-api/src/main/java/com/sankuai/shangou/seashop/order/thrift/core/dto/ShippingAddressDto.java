package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "用户收货地址对象")
@ToString
@Data
public class ShippingAddressDto extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    private Long id;
    @FieldDoc(description = "用户ID")
    private Long userId;
    @FieldDoc(description = "区域ID")
    private Integer regionId;
    @FieldDoc(description = "收货人")
    private String shipTo;
    @FieldDoc(description = "收货地址")
    private String address;
    @FieldDoc(description = "详细地址")
    private String addressDetail;
    @FieldDoc(description = "收货人电话")
    private String phone;
    @FieldDoc(description = "区域路径")
    private String regionPath;
    @FieldDoc(description = "区域全称")
    private String regionFullName;
    @FieldDoc(description = "省ID")
    private Long provinceId;
    @FieldDoc(description = "省名称")
    private String provinceName;
    @FieldDoc(description = "市ID")
    private Long cityId;
    @FieldDoc(description = "市名称")
    private String cityName;
    @FieldDoc(description = "区ID")
    private Long districtId;
    @FieldDoc(description = "区名称")
    private String districtName;
    @FieldDoc(description = "收获地址经度")
    private BigDecimal receiveLongitude;
    @FieldDoc(description = "收获地址纬度")
    private BigDecimal receiveLatitude;

}
