package com.sankuai.shangou.seashop.order.thrift.core.enums.pay;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 支付类型。java目前只有线上支付
 * <AUTHOR>
 */
@ThriftEnum
public enum PaymentTypeEnum {

    ONLINE(1, "线上支付"),
    // 线下收款 和 货到付款，是.net有的类型，注释保留记录
    //OFFLINE(2, "线下收款"),
    //CASH_ON_DELIVERY(3, "货到付款"),
    ;
    private final Integer code;
    @Getter
    private final String desc;

    PaymentTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(paymentType -> paymentType.code.equals(code))
                .findFirst()
                .map(PaymentTypeEnum::getDesc)
                .orElse("");
    }

}
