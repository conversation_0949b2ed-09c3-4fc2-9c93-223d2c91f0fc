package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * PC端预支付时的订单信息
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单支付方式返回对象")
public class PcPrePayOrderInfoDto extends BaseThriftDto {

    /**
     * 订单ID
     */
    @FieldDoc(description = "订单ID")
    private List<String> orderIdList;
    /**
     * 订单总金额
     */
    @FieldDoc(description = "订单总金额")
    private BigDecimal orderAmount;
    /**
     * 剩余支付时间
     */
    @FieldDoc(description = "剩余支付时间")
    private Integer remainPayHour;


    public String getOrderAmountString() {
        return this.bigDecimal2String(this.orderAmount);
    }


    public void setOrderAmountString(String orderAmount) {
        this.orderAmount = this.string2BigDecimal(orderAmount);
    }


}
