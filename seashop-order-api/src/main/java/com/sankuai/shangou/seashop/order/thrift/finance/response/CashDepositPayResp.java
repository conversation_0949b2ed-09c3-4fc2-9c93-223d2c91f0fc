package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 14:04
 */
@Data
@TypeDoc(description = "保证金支付表")
public class CashDepositPayResp extends BaseParamReq {

    @FieldDoc(description = "支付状态 1 成功 2 失败")
    private Integer payStatus;

    @FieldDoc(description = "汇付支付ID")
    private String payId;


}
