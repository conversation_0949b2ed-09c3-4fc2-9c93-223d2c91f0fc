package com.sankuai.shangou.seashop.order.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@ThriftEnum
public enum SearchTimeTypeEnum {

    CREATE_TIME(1, "创建时间"),

    UPDATE_TIME(2, "修改时间"),
    ;

    private final Integer code;

    private final String desc;

    SearchTimeTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static SearchTimeTypeEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (SearchTimeTypeEnum searchTimeTypeEnum : SearchTimeTypeEnum.values()) {
            if (searchTimeTypeEnum.getCode().equals(code)) {
                return searchTimeTypeEnum;
            }
        }
        return null;
    }

}
