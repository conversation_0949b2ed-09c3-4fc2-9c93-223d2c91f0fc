package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreateAccountReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description: 财务相关查询服务（统计相关接口）
 */
@FeignClient(name = "himall-order", contextId = "ShopAccountCmdFeign", path = "/himall-order/finance/shopAccountCmd", url = "${himall-order.dev.url:}")
public interface ShopAccountCmdFeign {


    /**
     * 创建店铺结算信息
     */
    @PostMapping(value = "createAccount", consumes = "application/json")
    ResultDto<BaseResp> createAccount(@RequestBody CreateAccountReq request) throws TException;
}
