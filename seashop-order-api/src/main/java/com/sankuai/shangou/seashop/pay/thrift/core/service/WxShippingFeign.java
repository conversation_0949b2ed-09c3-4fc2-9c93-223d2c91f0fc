package com.sankuai.shangou.seashop.pay.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ChannelConfigSaveReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.GetOrderRequest;
import com.sankuai.shangou.seashop.pay.thrift.core.request.UploadShippingRequest;
import com.sankuai.shangou.seashop.pay.thrift.core.response.DeliveryListResponse;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description: 渠道配置操作服务  https://xframe.mws.cloud.test.sankuai.com
 */
@FeignClient(name = "himall-order", contextId = "WxShippingFeign", path = "/himall-order/wxShipping", url = "${himall-order.dev.url:}")
public interface WxShippingFeign {

    @GetMapping("/isTradeManaged")
    ResultDto<Boolean> isTradeManaged(@RequestParam("paymentType") Integer paymentType);

    @PostMapping("/uploadShipping")
    ResultDto<Boolean> uploadShipping(@RequestBody @Validated UploadShippingRequest request);

    @PostMapping("/getOrder")
    ResultDto<Boolean> getOrder(@RequestBody @Validated GetOrderRequest request);

    @GetMapping("/getDeliveryList")
    ResultDto<DeliveryListResponse> getDeliveryList(@RequestParam("paymentType") Integer paymentType);
}
