package com.sankuai.shangou.seashop.order.thrift.core.enums.pay;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@ThriftEnum
public enum PayPlatformEnum {

    PC(1, "PC"),
    WECHAT(2, "微信"),
    ;

    private final Integer code;
    @Getter
    private final String desc;

    PayPlatformEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

}
