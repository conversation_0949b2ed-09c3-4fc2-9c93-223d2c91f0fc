package com.sankuai.shangou.seashop.order.thrift.finance.request;

import cn.hutool.core.collection.CollectionUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 14:04
 */
@Data
@TypeDoc(description = "保证金支付表")
public class CashDepositPayReq extends BaseParamReq {

    @FieldDoc(description = "汇付支付ID")
    private List<String> payIdList;


    @Override
    public void checkParameter(){
        AssertUtil.throwIfTrue(CollectionUtil.isEmpty(payIdList), "payIdList不能为空");
    }
}
