package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * @description：投诉维权供应商处理
 * @author： liweisong
 * @create： 2023/11/24 8:53
 * 供应商处理，入参应该为id,status传2，SellerReply字段
 */
@Data
@ToString
@TypeDoc(description = "投诉维权供应商处理")
public class DealSellerComplaintReq extends BaseParamReq {

    @FieldDoc(description = "主键ID")
    @PrimaryField
    private Long id;

    @FieldDoc(description = "店铺反馈信息")
    @ExaminField(description = "店铺反馈信息")
    private String sellerReply;

    @FieldDoc(description = "店铺id")
    @ExaminField(description = "店铺id")
    private Long shopId;


    /**
     * 参数校验
     */
    public void checkParameter() {
        if (id == null) {
            throw new InvalidParamException("id不能为空");
        }
        if (StringUtils.isEmpty(sellerReply)) {
            throw new InvalidParamException("店铺反馈信息sellerReply不能为空");
        }
    }


}
