package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "发起支付返回对象")
@Getter
@Setter
public class InitiatePayResp {

    @FieldDoc(description = "汇付支付创建支付返回的发起参数的JSON字符串。不同的支付方式参数可能不一样，前端需要转json对象")

    private String payParamMap;

}
