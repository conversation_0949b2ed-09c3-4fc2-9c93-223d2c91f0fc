package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderCommentDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderCommentDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderCommentResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ShopMarkResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/12/04 16:12
 * 订单评价查询相关服务
 */
@FeignClient(name = "himall-order", contextId = "OrderCommentQueryFeign", path = "/himall-order/orderCommentQuery", url = "${himall-order.dev.url:}")
public interface OrderCommentQueryFeign {

    /**
     * 查询订单评价列表(买家端)
     */
    @PostMapping(value = "/queryOrderCommentForBuyer", consumes = "application/json")
    ResultDto<BasePageResp<OrderCommentResp>> queryOrderCommentForBuyer(@RequestBody QueryOrderCommentReq request) throws TException;

    /**
     * 查询订单评价列表(平台端)
     */
    @PostMapping(value = "/queryOrderCommentForPlatform", consumes = "application/json")
    ResultDto<BasePageResp<OrderCommentResp>> queryOrderCommentForPlatform(@RequestBody QueryOrderCommentReq request) throws TException;

    /**
     * 查询订单评论详情(买家端)
     */
    @PostMapping(value = "/queryOrderCommentDetailForBuyer", consumes = "application/json")
    ResultDto<OrderCommentDetailResp> queryOrderCommentDetailForBuyer(@RequestBody QueryOrderCommentDetailReq request) throws TException;

    /**
     * 通过店铺ID查询店铺评分
     */
    @PostMapping(value = "/queryShopMarkByShopId", consumes = "application/json")
    ResultDto<ShopMarkResp> queryShopMarkByShopId(@RequestBody ShopIdReq request) throws TException;
}
