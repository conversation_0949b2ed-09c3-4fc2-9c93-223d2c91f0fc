package com.sankuai.shangou.seashop.order.thrift.core.dto;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单物流信息")
@Getter
@Setter
public class OrderExpressDto extends BaseParamReq {

    @FieldDoc(description = "物流公司名称")

    private String expressCompanyName;
    @FieldDoc(description = "物流单号")

    private String shipOrderNumber;
    @FieldDoc(description = "物流公司编码")

    private String expressCompanyCode;

    @FieldDoc(description = "收货人手机号")
    private String cellPhone;
    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(expressCompanyName)) {
            throw new InvalidParamException("物流公司名称不能为空");
        }
        if (expressCompanyName.length() > 50) {
            throw new InvalidParamException("物流公司名称长度不能超过50个字符");
        }
        if (StrUtil.isBlank(shipOrderNumber)) {
            throw new InvalidParamException("物流单号不能为空");
        }
        if (shipOrderNumber.length() > 80) {
            throw new InvalidParamException("物流单号长度不能超过80个字符");
        }
        if (StrUtil.isBlank(expressCompanyCode)) {
            throw new InvalidParamException("物流公司编码不能为空");
        }
        if (expressCompanyCode.length() > 32) {
            throw new InvalidParamException("物流公司编码长度不能超过32个字符");
        }
    }
}
