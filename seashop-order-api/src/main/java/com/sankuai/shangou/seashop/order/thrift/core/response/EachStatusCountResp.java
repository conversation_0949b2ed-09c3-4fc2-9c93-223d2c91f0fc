package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/12/13 10:12
 */
@Data
@TypeDoc(description = "统计各订单状态的数量返参")
public class EachStatusCountResp extends BaseParamReq {

    @FieldDoc(description = "全部订单数量")
    private Long allOrderCount;

    @FieldDoc(description = "待支付订单数量")
    private Long waitingForPay;

    @FieldDoc(description = "待发货订单数量")
    private Long waitingForDelivery;

    @FieldDoc(description = "待收货订单数量")
    private Long waitingForRecieve;

    @FieldDoc(description = "待处理售后订单数量")
    private Long refundCount;

    @FieldDoc(description = "待评论订单数量")
    private Long waitingForComments;


}
