package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 商家查询售后列表tab枚举，与状态有区别
 *
 * <AUTHOR>
 */
@ThriftEnum
public enum RefundUserQueryTabEnum {

    ALL_APPLY(0, "全部"),
    // 我申请的退款:包含发货前整单退货/退款、发货后退款方式为-仅退款的售后单
    APPLY_REFUND(1, "我申请的退款"),
    // 我申请的退货:包含发货后退款方式为-退货退款的售后单
    APPLY_RETURN(2, "我申请的退货"),

    ;
    private final Integer code;
    @Getter
    private final String desc;

    RefundUserQueryTabEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static RefundUserQueryTabEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
