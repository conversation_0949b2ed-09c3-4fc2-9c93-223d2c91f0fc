package com.sankuai.shangou.seashop.order.thrift.core.response.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "商品购买数量返回批量结果")
public class ProductBuyCountListResp {

    @FieldDoc(description = "多个商品购买数量")
    private List<ProductBuyCountResp> dataList;


}
