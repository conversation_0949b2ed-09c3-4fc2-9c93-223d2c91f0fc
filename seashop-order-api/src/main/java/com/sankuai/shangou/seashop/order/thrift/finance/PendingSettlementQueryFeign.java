package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.OrderIdQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementOrderResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PlatCommissionResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description: 待结算订单查询服务
 */
@FeignClient(name = "himall-order", contextId = "PendingSettlementQueryFeign", path = "/himall-order/finance/pendingSettlementQuery", url = "${himall-order.dev.url:}")
public interface PendingSettlementQueryFeign {

    /**
     * 获取待结算订单合计列表
     */
    @PostMapping(value = "getPendingSettlementList", consumes = "application/json")
    ResultDto<BasePageResp<PendingSettlementResp>> getPendingSettlementList(@RequestBody PendingSettlementQryReq request) throws TException;

    /**
     * 获取平台佣金总额
     */
    @PostMapping(value = "getPlatCommission", consumes = "application/json")
    ResultDto<PlatCommissionResp> getPlatCommission() throws TException;

    /**
     * 待结算订单列表分页查询
     */
    @PostMapping(value = "pageList", consumes = "application/json")
    ResultDto<BasePageResp<PendingSettlementOrderResp>> pageList(PendingSettlementOrderQryReq request) throws TException;

    /**
     * 通过订单id查询待结算订单详情
     */
    @PostMapping(value = "getDetailByOrderId", consumes = "application/json")
    ResultDto<PendingSettlementOrderResp> getDetailByOrderId(OrderIdQryReq request) throws TException;
}
