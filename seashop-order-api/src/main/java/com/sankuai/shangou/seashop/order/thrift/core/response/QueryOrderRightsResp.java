package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/22 9:57
 */
@Data
@ToString
@TypeDoc(description = "查询投诉维权返参")
public class QueryOrderRightsResp extends BaseParamReq {

    @FieldDoc(description = "订单ID")
    private Long orderId;

    @FieldDoc(description = "下单时间")
    private Date createTime;

    @FieldDoc(description = "订单详情的商品主图地址")
    private List<OrderRightImagePathResp> imagePath;


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }


}
