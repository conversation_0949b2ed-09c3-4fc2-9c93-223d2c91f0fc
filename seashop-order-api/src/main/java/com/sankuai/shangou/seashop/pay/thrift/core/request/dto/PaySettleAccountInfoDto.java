package com.sankuai.shangou.seashop.pay.thrift.core.request.dto;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "结算账号信息")
public class PaySettleAccountInfoDto extends BaseParamReq {

    /**
     * 银行卡号
     * 银行卡号不能为空
     */
    @FieldDoc(description = "银行卡号", requiredness = Requiredness.REQUIRED)
    private String cardId;

    /**
     * 银行卡对应的户名
     * 银行卡对应的户名不能为空
     */
    @FieldDoc(description = "银行卡对应的户名", requiredness = Requiredness.REQUIRED)
    private String cardName;

    /**
     * 证件号，银行账户类型为对私时，必填
     */
    @FieldDoc(description = "证件号，银行账户类型为对私时，必填")
    private String certId;
    /**
     * 证件类型，仅支持：00-身份证，银行账户类型为对私时，必填
     */
    @FieldDoc(description = "证件类型，仅支持：00-身份证，银行账户类型为对私时，必填")
    private String certType;
    /**
     * 手机号
     * 手机号不能为空
     */
    @FieldDoc(description = "手机号", requiredness = Requiredness.REQUIRED)
    private String telNo;

    /**
     * 银行编码，详见附录 银行代码，银行账户类型对公时，必填
     */
    @FieldDoc(description = "银行编码，详见附录 银行代码，银行账户类型对公时，必填")
    private String bankCode;
    /**
     * 开户银行名称
     */
    @FieldDoc(description = "开户银行名称")
    private String bankName;

    /**
     * 银行账户类型：1-对公；2-对私
     * 银行账户类型不能为空
     */
    @FieldDoc(description = "银行账户类型：1-对公；2-对私", requiredness = Requiredness.REQUIRED)
    private String bankAcctType;
    /**
     * 银行账户开户银行所在省份编码 （省市编码），银行账户类型为对公时，必填
     */
    @FieldDoc(description = "银行账户开户银行所在省份编码 （省市编码），银行账户类型为对公时，必填")
    private String provCode;
    /**
     * 银行账户开户银行所在地区编码（省市编码），银行账户类型为对公时，必填
     */
    @FieldDoc(description = "银行账户开户银行所在地区编码（省市编码），银行账户类型为对公时，必填")
    private String areaCode;

    @Override
    public void checkParameter() {
        if (StrUtil.isBlank(this.cardId)) {
            throw new InvalidParamException("cardId不能为空(银行卡号不能为空)");
        }
        if (StrUtil.isBlank(this.cardName)) {
            throw new InvalidParamException("cardName不能为空(银行卡对应的户名不能为空)");
        }
        if(StrUtil.isBlank(this.bankAcctType)){
            throw new InvalidParamException("bankAcctType不能为空(银行账户类型不能为空)");
        }
    }


}
