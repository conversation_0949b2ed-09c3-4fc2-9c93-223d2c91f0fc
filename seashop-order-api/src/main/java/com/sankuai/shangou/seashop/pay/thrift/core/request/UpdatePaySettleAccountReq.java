package com.sankuai.shangou.seashop.pay.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "结算账号请求对象")
public class UpdatePaySettleAccountReq extends PaySettleAccountReq {

    /**
     * 目前仅支持：bank_account（银行卡）
     * 银行卡类型不能为空
     */
    @FieldDoc(description = "目前仅支持：bank_account（银行卡）", requiredness = Requiredness.REQUIRED)
    private String settleAccountId;

    @Override
    public void checkParameter() {
        super.checkParameter();
        if (StrUtil.isBlank(this.settleAccountId)) {
            throw new InvalidParamException("结算账号不能为空");
        }
    }


}
