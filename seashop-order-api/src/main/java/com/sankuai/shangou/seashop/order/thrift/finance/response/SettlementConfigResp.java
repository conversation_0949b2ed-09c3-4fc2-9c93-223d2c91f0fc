package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@TypeDoc(description = "结算配置相关信息")
@ToString
@Data
public class SettlementConfigResp extends BaseThriftDto {

    @FieldDoc(description = "结算周期")
    private Long settlementInterval;

    @FieldDoc(description = "结算手续费率")
    private BigDecimal settlementFeeRate;
    @FieldDoc(description = "微信结算手续费率")
    private BigDecimal wxFeeRate;


    public String getSettlementFeeRateString() {
        return this.bigDecimal2String(this.settlementFeeRate);
    }


    public void setSettlementFeeRateString(String settlementFeeRate) {
        this.settlementFeeRate = this.string2BigDecimal(settlementFeeRate);
    }


    public String getWxFeeRateString() {
        return this.bigDecimal2String(this.wxFeeRate);
    }


    public void setWxFeeRateString(String wxFeeRate) {
        this.wxFeeRate = this.string2BigDecimal(wxFeeRate);
    }
}
