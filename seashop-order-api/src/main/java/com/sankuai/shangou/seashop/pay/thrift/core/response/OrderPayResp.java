package com.sankuai.shangou.seashop.pay.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
@TypeDoc(description = "订单支付响应体")
@ToString
@Data
public class OrderPayResp extends BaseThriftDto {

    /**
     * 主键
     */
    @FieldDoc(description = "主键")
    private Long id;

    /**
     * 支付流水ID
     */
    @FieldDoc(description = "支付流水ID")
    private String payId;

    /**
     * 来源订单ID
     */
    @FieldDoc(description = "来源订单ID")
    private String orderId;

    /**
     * 支付金额
     */
    @FieldDoc(description = "支付金额（单位：元）")
    private BigDecimal payAmount;

    /**
     * 支付状态  0: 未支付, 1: 支付成功, 2: 支付失败
     */
    @FieldDoc(description = "支付状态  0: 未支付, 1: 支付成功, 2: 支付失败")
    private Integer payState;

    /**
     * 支付时间
     */
    @FieldDoc(description = "支付时间")
    private Date payTime;

    /**
     * 支付渠道 1：汇付天下
     */
    @FieldDoc(description = "支付渠道 1：汇付天下")
    private Integer paymentChannel;

    /**
     * 支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银
     */
    @FieldDoc(description = "支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银")
    private Integer paymentType;

    /**
     * 业务类型 1：订单,4：保证金
     */
    @FieldDoc(description = "业务类型 1：订单,4：保证金")
    private Integer businessType;

    /**
     * 银行编码
     */
    @FieldDoc(description = "银行编码")
    private String bankCode;

    /**
     * 渠道支付ID
     */
    @FieldDoc(description = "渠道支付ID")
    private String channelPayId;

    /**
     * 错误描述
     */
    @FieldDoc(description = "错误描述")
    private String channelPayMsg;

    @FieldDoc(description = "第三方支付流水号（支付宝/微信）")
    private String outTransId;


    public String getPayAmountString() {
        return this.bigDecimal2String(this.payAmount);
    }


    public void setPayAmountString(String payAmount) {
        this.payAmount = this.string2BigDecimal(payAmount);
    }


    public Long getPayTimeLong() {
        return this.date2Long(this.payTime);
    }


    public void setPayTimeLong(Long payTime) {
        this.payTime = this.long2Date(payTime);
    }


}
