package com.sankuai.shangou.seashop.order.thrift.core;

import org.apache.thrift.TException;import org.springframework.cloud.openfeign.FeignClient;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.ConfirmExceptionOrderReq;

/**
 * <AUTHOR>
 * 异常订单操作相关thrift服务
 */
@FeignClient(name = "himall-order", contextId = "ExceptionOrderCmdFeign", path = "/himall-order/exceptionOrderCmd", url = "${himall-order.dev.url:}")
public interface ExceptionOrderCmdFeign {

    /**
     * 确认退款
     */
    @PostMapping(value = "/confirm", consumes = "application/json")
    ResultDto<BaseResp> confirm(@RequestBody ConfirmExceptionOrderReq req) throws TException;


}
