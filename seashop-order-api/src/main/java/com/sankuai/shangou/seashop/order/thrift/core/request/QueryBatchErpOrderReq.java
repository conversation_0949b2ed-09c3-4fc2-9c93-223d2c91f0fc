package com.sankuai.shangou.seashop.order.thrift.core.request;

import cn.hutool.core.collection.CollectionUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "批量查询订单入参")
public class QueryBatchErpOrderReq extends BaseParamReq {

    @FieldDoc(
            name = "shopId",
            description = "店铺ID",
            requiredness = Requiredness.OPTIONAL
    )
    private Long shopId;

    @FieldDoc(
            name = "orderIds",
            description = "订单ID列表",
            requiredness = Requiredness.OPTIONAL
    )
    private List<String> orderIds;

    @FieldDoc(
            name = "sourceOrderIds",
            description = "牵牛花订单号列表",
            requiredness = Requiredness.OPTIONAL
    )
    private List<String> sourceOrderIds;

    @Override
    public void checkParameter() {
        if (CollectionUtil.isEmpty(orderIds) && CollectionUtil.isEmpty(sourceOrderIds)) {
            throw new IllegalArgumentException("订单ID列表/牵牛花订单列表不能都为空");
        }
    }


}
