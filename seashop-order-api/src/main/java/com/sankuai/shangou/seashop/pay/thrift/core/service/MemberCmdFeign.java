package com.sankuai.shangou.seashop.pay.thrift.core.service;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayBaseReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayCorpMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.UpdatePaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PaySettleAccountResp;

/**
 * @author: lhx
 * @date: 2023/12/8/008
 * @description: 账户相关操作服务
 */
@FeignClient(name = "himall-order", contextId = "MemberCmdFeign", path = "/himall-order/member", url = "${himall-order.dev.url:}")
public interface MemberCmdFeign {
    /**
     * 查询个人账号
     */
    @PostMapping(value = "/queryMember", consumes = "application/json")
    ResultDto<BaseResp> queryMember(@RequestBody PayBaseReq request) throws TException;

    /**
     * 创建个人账号
     *
     */
    @PostMapping(value = "/createMember", consumes = "application/json")
    ResultDto<BaseResp> createMember(@RequestBody PayMemberReq request) throws TException;

    /**
     * 创建结算账号
     */
    @PostMapping(value = "/createSettleAccount", consumes = "application/json")
    ResultDto<PaySettleAccountResp> createSettleAccount(@RequestBody PaySettleAccountReq request) throws TException;

    /**
     * 更新结算账号
     */
    @PostMapping(value = "/updateSettleAccount", consumes = "application/json")
    ResultDto<PaySettleAccountResp> updateSettleAccount(@RequestBody UpdatePaySettleAccountReq request) throws TException;

    /**
     * 创建个人账号和结算账号
     *
     */
//    @PostMapping(value = "createMemberAndSettleAccount", consumes = "application/json")
//    ResultDto<PaySettleAccountResp> createMemberAndSettleAccount(PayMemberAndAccountReq request) throws TException;


    /**
     * 创建企业账号和结算账号
     *
     */
    @PostMapping(value = "/createCompanyMember", consumes = "application/json")
    ResultDto<BaseResp> createCompanyMember(@RequestBody PayCorpMemberReq request) throws TException;

    /**
     * 更新企业账号和结算账号
     */
    @PostMapping(value = "/updateCompanyMember", consumes = "application/json")
    ResultDto<Boolean> updateCompanyMember(@RequestBody PayCorpMemberReq request);
}
