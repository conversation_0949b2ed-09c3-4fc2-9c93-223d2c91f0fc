package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 优惠类型。.net版本还有供应商红包，Java版本目前只有优惠券
 * <AUTHOR>
 */
@ThriftEnum
public enum OrderCouponTypeEnum {

    COUPON(0, "优惠券"),
        // .net版本有供应商红包，枚举注释在这里记录一下
        //SUPPLIER_RED_PACKET(1, "供应商红包")

    ;

    private final Integer code;
    @Getter
    private final String desc;

    OrderCouponTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static OrderCouponTypeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(couponType -> couponType.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
