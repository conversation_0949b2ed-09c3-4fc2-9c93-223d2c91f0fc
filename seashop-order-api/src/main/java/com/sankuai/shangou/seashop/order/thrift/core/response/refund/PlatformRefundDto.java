package com.sankuai.shangou.seashop.order.thrift.core.response.refund;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "售后对象")
public class PlatformRefundDto extends BaseThriftDto {

    @FieldDoc(description = "退款ID，退款表主键ID")
    private Long refundId;
    @FieldDoc(description = "订单号")
    private String orderId;
    @FieldDoc(description = "店铺ID")
    private Long shopId;
    @FieldDoc(description = "店铺名称")
    private String shopName;
    @FieldDoc(description = "订单实付金额")
    private BigDecimal orderPayAmount;
    /**
     * 买家ID
     */
    @FieldDoc(description = "买家ID")
    private Long userId;
    /**
     * 买家账号
     */
    @FieldDoc(description = "买家账号")
    private String userName;
    /**
     * 退款数量
     */
    @FieldDoc(description = "退款数量")
    private Long refundQuantity;
    @FieldDoc(description = "退款金额")
    private BigDecimal refundAmount;
    @FieldDoc(description = "申请日期")
    private Date applyDate;
    @FieldDoc(description = "退款状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；5：待平台确认；6：退款成功；7：平台驳回；8：退款中；9：买家取消")
    private Integer refundStatus;
    @FieldDoc(description = "退款状态描述")
    private String refundStatusDesc;
    @FieldDoc(description = "是否可以重新申请")
    private Boolean canReapply;
    @FieldDoc(description = "退款商品明细列表")
    private List<RefundItemDto> itemList;
    @FieldDoc(description = "是否订单全部退.店铺后台和平台后台需要根据这个字段区分显示商品信息。如果是true，显示 【订单所有商品】，否则从itemList取第一个商品显示")
    private Boolean hasAllReturn;
    /**
     * 是否显示【查看物流】按钮
     * 供应商登录，买家填写了物流信息显示此按钮
     */
    @FieldDoc(description = "是否显示【查看物流】按钮")
    private Boolean showWayBillBtn;
    /**
     * 退款商品描述
     */
    @FieldDoc(description = "退款商品描述。如果是整单，则显示【订单所有商品，否则显示具体商品名称】")
    private String productDesc;
    /**
     * 售后类型。1：订单退款
     */
    @FieldDoc(description = "售后类型。1：仅退款；2：退货退款")
    private Integer refundType;

    private Integer refundMode;


    public String getOrderPayAmountString() {
        return this.bigDecimal2String(this.orderPayAmount);
    }


    public void setOrderPayAmountString(String orderPayAmount) {
        this.orderPayAmount = this.string2BigDecimal(orderPayAmount);
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


    public Long getApplyDateLong() {
        return this.date2Long(this.applyDate);
    }


    public void setApplyDateLong(Long applyDate) {
        this.applyDate = this.long2Date(applyDate);
    }


}
