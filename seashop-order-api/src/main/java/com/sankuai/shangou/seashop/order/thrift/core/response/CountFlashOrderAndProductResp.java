package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Data
@ToString
@TypeDoc(description = "统计完成订单和商品数量返回值")
@NoArgsConstructor
@AllArgsConstructor
public class CountFlashOrderAndProductResp extends BaseThriftDto {

    @FieldDoc(description = "店铺ID")
    private Long shopId;

    @FieldDoc(description = "完成订单数量")
    private Long countFlashOrder;

    @FieldDoc(description = "完成商品数量")
    private Long countFlashProduct;


}
