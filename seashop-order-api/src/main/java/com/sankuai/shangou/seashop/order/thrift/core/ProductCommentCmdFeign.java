package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.HideProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.ReplyProductCommentReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:31
 * 商品评价操作相关服务
 */
@FeignClient(name = "himall-order", contextId = "ProductCommentCmdFeign", path = "/himall-order/productCommentCmd", url = "${himall-order.dev.url:}")
public interface ProductCommentCmdFeign {

    /**
     * 隐藏商品评论
     */
    @PostMapping(value = "/hideProductCommentForPlatForm", consumes = "application/json")
    ResultDto<BaseResp> hideProductCommentForPlatForm(@RequestBody HideProductCommentReq request) throws TException;

    /**
     * 回复商品评论
     */
    @PostMapping(value = "/replyProductCommentForSeller", consumes = "application/json")
    ResultDto<BaseResp> replyProductCommentForSeller(@RequestBody ReplyProductCommentReq request) throws TException;


}
