package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@TypeDoc(description = "保证金响应体")
@ToString
@Data
public class CashDepositResp extends BaseThriftDto {

    @FieldDoc(description = "主键")
    private Long id;

    /**
     * Shop表外键
     */
    @FieldDoc(description = "店铺Id")
    private Long shopId;

    /**
     * 可用金额
     */
    @FieldDoc(description = "可用金额")
    private BigDecimal currentBalance;

    /**
     * 已缴纳金额
     */
    @FieldDoc(description = "已缴纳金额")
    private BigDecimal totalBalance;

    /**
     * 最后一次缴纳时间
     */
    @FieldDoc(description = "最后一次缴纳时间")
    private Date date;

    /**
     * 是否显示标志，只有保证金欠费该字段才有用，默认显示
     */
    @FieldDoc(description = "是否显示标志，只有保证金欠费该字段才有用，默认显示")
    private Boolean enableLabels;


    public String getCurrentBalanceString() {
        return this.bigDecimal2String(this.currentBalance);
    }


    public void setCurrentBalanceString(String currentBalance) {
        this.currentBalance = this.string2BigDecimal(currentBalance);
    }


    public String getTotalBalanceString() {
        return this.bigDecimal2String(this.totalBalance);
    }


    public void setTotalBalanceString(String totalBalance) {
        this.totalBalance = this.string2BigDecimal(totalBalance);
    }


    public Long getDateLong() {
        return this.date2Long(this.date);
    }


    public void setDateLong(Long date) {
        this.date = this.long2Date(date);
    }


}
