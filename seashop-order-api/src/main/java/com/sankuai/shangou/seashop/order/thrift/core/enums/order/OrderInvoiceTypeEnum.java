package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
 * <AUTHOR>
 */
@ThriftEnum
public enum OrderInvoiceTypeEnum {

    NORMAL(1, "普通发票"),
    ELECTRONIC(2, "电子发票"),
    VAT(3, "增值税发票");

    private final Integer code;
    @Getter
    private final String desc;

    OrderInvoiceTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static OrderInvoiceTypeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(invoiceType -> invoiceType.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(invoiceType -> invoiceType.getCode().equals(code))
                .findFirst()
                .map(OrderInvoiceTypeEnum::getDesc)
                .orElse("");
    }

}
