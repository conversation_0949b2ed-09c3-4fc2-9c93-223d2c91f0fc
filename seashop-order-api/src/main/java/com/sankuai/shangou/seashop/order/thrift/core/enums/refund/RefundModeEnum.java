package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 退款模式，这个实际上是订单的售后类型
 * <AUTHOR>
 */
@ThriftEnum
public enum RefundModeEnum {

    // 定义一个不存在的
    UNKNOWN(-99, ""),
    // 订单退款
    ORDER_REFUND(1, "订单退款"),
    // 货品退款
    GOODS_REFUND(2, "货品退款"),
    // 退货退款
    RETURN_AND_REFUND(3, "退货退款"),
    ;

    private final Integer code;
    @Getter
    private final String desc;

    RefundModeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .map(RefundModeEnum::getDesc)
                .orElse(null);
    }

    /**
     * 退款模式是否对应仅退款。订单退款目前只能选仅退款；其他退款选择仅退款时，模式是 货品退款=2
     * <AUTHOR>
     * @param code
     * boolean
     */
    public static boolean onlyRefund(Integer code) {
        return GOODS_REFUND.getCode().equals(code) || ORDER_REFUND.getCode().equals(code);
    }

    public static RefundModeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
