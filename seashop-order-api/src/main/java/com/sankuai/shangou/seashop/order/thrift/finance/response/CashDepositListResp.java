package com.sankuai.shangou.seashop.order.thrift.finance.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@TypeDoc(description = "保证金查询响应体")
@ToString
@Data
public class CashDepositListResp {

    @FieldDoc(description = "保证金信息列表")
    private List<CashDepositResp> list;


}
