package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "店铺信息对象")
@ToString
@Data
@Getter
@Setter
public class ProductPromotionDto {

    @FieldDoc(description = "商品ID")

    private Long productId;
    @FieldDoc(description = "skuId")

    private String skuId;
    @FieldDoc(description = "商品满足的多个营销信息")

    private List<PromotionDto> promotionList;

}
