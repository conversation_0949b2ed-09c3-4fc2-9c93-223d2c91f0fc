package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "发起支付请求入参")
public class InitiatePayReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private List<String> orderIdList;
    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;
    @FieldDoc(description = "选择的支付方式。1：支付宝扫码；3：微信小程序；5：企业网银；6：个人网银", requiredness = Requiredness.REQUIRED)
    private Integer payMethod;
    @FieldDoc(description = "openId.小程序支付必传")
    private String openId;
    @FieldDoc(description = "银行编号")
    private String bankCode;
    @FieldDoc(description = "发起支付的客户端IP")
    private String clientIp;
    /**
     * 支付成功后的回调地址
     */
    @FieldDoc(description = "支付成功后的回调地址")
    private String callbackUrl;

    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (userId == null) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (orderIdList == null || orderIdList.isEmpty()) {
            throw new IllegalArgumentException("orderIdList不能为空");
        }
        if (payMethod == null) {
            throw new IllegalArgumentException("payMethod不能为空");
        }
    }


}
