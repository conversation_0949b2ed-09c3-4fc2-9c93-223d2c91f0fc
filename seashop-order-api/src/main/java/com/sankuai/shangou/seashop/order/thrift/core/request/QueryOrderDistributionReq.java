package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/3 14:48
 */
@Data
@TypeDoc(description = "导出订单配货单，导出商品配货单入参")
public class QueryOrderDistributionReq extends BasePageReq {

    @FieldDoc(description = "订单ID集合")
    private List<String> orderIdList;


    public void checkParameter(){
        AssertUtil.throwIfTrue(CollectionUtils.isEmpty(orderIdList), "orderIdList不能为空");
        AssertUtil.throwIfTrue(orderIdList.size()>200, "orderIdList数量不能大于200");
    }
}
