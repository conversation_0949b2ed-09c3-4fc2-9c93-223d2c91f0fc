package com.sankuai.shangou.seashop.order.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "ES滚动查询入参")
public class EsScrollQueryReq extends BaseParamReq {

    @FieldDoc(description = "滚动ID，需要先根据查询条件调用ES接口单独获取")
    private String scrollId;
    @FieldDoc(description = "滚动查询保留时间，单位分钟")
    private Long timeValueMinutes;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(StrUtil.isBlank(scrollId), "滚动查询ID不能为空");
        AssertUtil.throwIfTrue(timeValueMinutes == null || timeValueMinutes <= 0, "滚动查询保留时间不合法");
    }


}
