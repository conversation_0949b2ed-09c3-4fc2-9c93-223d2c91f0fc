package com.sankuai.shangou.seashop.pay.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description: 请求参数模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TradeManagementCheckRequest extends BaseRequest {
    @FieldDoc(description = "appid")
    private String appId; // 待查询小程序的AppID
}
