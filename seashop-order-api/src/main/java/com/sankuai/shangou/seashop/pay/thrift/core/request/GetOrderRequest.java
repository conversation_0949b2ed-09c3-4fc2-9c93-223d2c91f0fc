package com.sankuai.shangou.seashop.pay.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 查询订单请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetOrderRequest extends BaseRequest {
    @FieldDoc(description = "transaction_id")
    private String transactionId; // 微信支付单号

    @FieldDoc(description = "merchant_id")
    private String merchantId; // 商户号

    @FieldDoc(description = "sub_merchant_id")
    private String subMerchantId; // 二级商户号

    @FieldDoc(description = "merchant_trade_no")
    private String merchantTradeNo; // 商户内部订单号
}
