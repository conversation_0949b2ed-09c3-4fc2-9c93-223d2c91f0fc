package com.sankuai.shangou.seashop.order.thrift.finance.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "待结算列表请求参数")
public class PendingSettlementOrderQryReq extends BasePageReq {

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @FieldDoc(description = "创建时间-开始时间")
    private Date startCreateTime;

    @FieldDoc(description = "创建时间-结束时间")
    private Date endCreateTime;

    @FieldDoc(description = "订单id")
    private String orderId;

    /**
     * ALIPAY_SCAN(1, "支付宝扫码"),
     * ALIPAY_H5(2, "支付宝H5"),
     * WECHAT_APPLET(3, "微信小程序"),
     * WECHAT_H5(4, "微信H5"),
     * COMPANY_BANK(5, "企业网银"),
     * PERSON_BANK(6, "个人网银")
     */
    @FieldDoc(description = "支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银")
    private Integer paymentType;

    @FieldDoc(description = "完成时间-开始时间")
    private Date startFinishTime;

    @FieldDoc(description = "完成时间-结束时间")
    private Date endFinishTime;

    @FieldDoc(description = "支付时间-开始时间")
    private Date startPayTime;

    @FieldDoc(description = "支付时间-结束时间")
    private Date endPayTime;

    @Override
    public void checkParameter() {
//        if (this.shopId == null) {
//            throw new InvalidParamException("店铺id不能为空");
//        }
        if (null != this.startCreateTime && null != this.endCreateTime && this.startCreateTime.after(this.endCreateTime)) {
            throw new InvalidParamException("开始时间不能大于结束时间");
        }
        if (null != this.startFinishTime && null != this.endFinishTime && this.startFinishTime.after(this.endFinishTime)) {
            throw new InvalidParamException("开始时间不能大于结束时间");
        }
        if (null != this.startPayTime && null != this.endPayTime && this.startPayTime.after(this.endPayTime)) {
            throw new InvalidParamException("开始时间不能大于结束时间");
        }
    }


    public Long getStartCreateTimeLong() {
        return this.date2Long(this.startCreateTime);
    }


    public void setStartCreateTimeLong(Long startCreateTime) {
        this.startCreateTime = this.long2Date(startCreateTime);
    }


    public Long getEndCreateTimeLong() {
        return this.date2Long(this.endCreateTime);
    }


    public void setEndCreateTimeLong(Long endCreateTime) {
        this.endCreateTime = this.long2Date(endCreateTime);
    }


    public Long getStartFinishTimeLong() {
        return this.date2Long(this.startFinishTime);
    }


    public void setStartFinishTimeLong(Long startFinishTime) {
        this.startFinishTime = this.long2Date(startFinishTime);
    }


    public Long getEndFinishTimeLong() {
        return this.date2Long(this.endFinishTime);
    }


    public void setEndFinishTimeLong(Long endFinishTime) {
        this.endFinishTime = this.long2Date(endFinishTime);
    }


    public Long getStartPayTimeLong() {
        return this.date2Long(this.startPayTime);
    }


    public void setStartPayTimeLong(Long startPayTime) {
        this.startPayTime = this.long2Date(startPayTime);
    }


    public Long getEndPayTimeLong() {
        return this.date2Long(this.endPayTime);
    }


    public void setEndPayTimeLong(Long endPayTime) {
        this.endPayTime = this.long2Date(endPayTime);
    }
}
