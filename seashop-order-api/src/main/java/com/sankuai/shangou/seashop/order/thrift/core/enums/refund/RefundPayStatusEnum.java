package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@ThriftEnum
public enum RefundPayStatusEnum {

    // 支付成功
    PAY_SUCCESS(1, "支付成功"),
    // 支付失败
    PAY_FAIL(-1, "支付失败"),
    // 未支付
    UN_PAY(0, "未支付"),

    ;

    private final Integer code;
    @Getter
    private final String desc;

    RefundPayStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static RefundPayStatusEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
