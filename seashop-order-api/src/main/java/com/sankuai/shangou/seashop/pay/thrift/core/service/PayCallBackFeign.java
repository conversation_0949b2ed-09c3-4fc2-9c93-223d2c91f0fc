package com.sankuai.shangou.seashop.pay.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.pay.thrift.core.dto.MockPayCallBack;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayCallBackNotifyPayReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayCallBackReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletRequest;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description: 回调服务
 */
@FeignClient(name = "himall-order", contextId = "PayCallBackFeign", path = "/himall-order/payCallBack", url = "${himall-order.dev.url:}")
public interface PayCallBackFeign {

    /**
     * 汇付回调接口
     */
    @PostMapping(value = "/adaPayCallback", consumes = "application/json")
    ResultDto<BaseResp> adaPayCallback(@RequestBody PayCallBackReq request) throws TException;

    /**
     * 支付回调
     *
     * @param notifyPayReq 支付回调对象
     * @return 处理结果
     */
    @PostMapping(value = "/notifyPay/{channel}", consumes = "application/json")
    Object notifyPay(@PathVariable("channel") String channel, @RequestBody PayCallBackNotifyPayReq notifyPayReq);


    /**
     * 支付回调
     * @param request 上下文
     * @return 处理结果
     */
    @PostMapping("/aliPayCallback")
    Object aliPayCallback(HttpServletRequest request);


    /**
     * 退款回调
     *
     * @param notifyPayReq 退款回调对象
     * @return 处理结果
     */
    @PostMapping(value = "/notifyRefund/{channel}", consumes = "application/json")
    Object notifyRefund(@PathVariable("channel") String channel, @RequestBody PayCallBackNotifyPayReq notifyPayReq);

    @PostMapping("/aliNotifyRefund")
    Object aliNotifyRefund(HttpServletRequest request);

    @PostMapping(value = "/mock/pay", consumes = "application/json")
    ResultDto<String> mockCreatePay(@RequestBody OrderPayResp request) throws TException;

    @PostMapping(value = "/mock/collBack", consumes = "application/json")
    ResultDto<BaseResp> mockCallBack(@RequestBody MockPayCallBack callBack) throws TException;
}
