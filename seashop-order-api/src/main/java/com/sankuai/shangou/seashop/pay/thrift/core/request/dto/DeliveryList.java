package com.sankuai.shangou.seashop.pay.thrift.core.request.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/04/02/ $
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryList {

    @FieldDoc(description = "delivery_id")
    private String deliveryId;

    @FieldDoc(description = "delivery_name")
    private String deliveryName;
}
