package com.sankuai.shangou.seashop.order.thrift.core.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单退款主表
 * </p>
 *
 * @since 2023-11-15
 */
@ToString
@Getter
@Setter
public class OrderRefundReportDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单id
     */
    private String orderId;
    /**
     * 三方售后单号
     */
    private String sourceRefundId;

    /**
     * 订单详情id
     */
    private Long orderItemId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 商家id
     */
    private Long userId;

    /**
     * 申请人
     */
    private String applicant;

    /**
     * 联系人
     */
    private String contactPerson;

    /**
     * 联系电话
     */
    private String contactCellPhone;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     * 退款原因
     */
    private String reason;

    /**
     * 退款详情
     */
    private String reasonDetail;

    /**
     * 供应商审核状态(1:待审核,2:待买家寄货,3:待取货验收,4:审核拒绝,5:供应商通过审核)
     */
    private Integer sellerAuditStatus;

    /**
     * 供应商审核时间
     */
    private Date sellerAuditDate;

    /**
     * 供应商注释
     */
    private String sellerRemark;

    /**
     * 状态。根据供应商状态和平台状态，以及是否取消综合得到。供应商和平台的状态值保持一致。默认值0是还没有初始化数据的
     * 1:待审核,2:待买家寄货,3:待取货验收,4:审核拒绝,5:供应商通过审核,6:待平台确认,7:退款成功,8:平台驳回,9:退款中,-1:取消售后
     */
    private Integer status;

    /**
     * 平台审核状态(6:待平台确认,7:退款成功,8:平台驳回)
     */
    private Integer managerConfirmStatus;

    /**
     * 平台审核时间
     */
    private Date managerConfirmDate;

    /**
     * 平台注释
     */

    private String managerRemark;

    /**
     * 是否需要退货
     */
    private Boolean hasReturn;

    /**
     * 快递公司
     */
    private String expressCompanyName;

    /**
     * 快递公司编码
     */
    private String expressCompanyCode;

    /**
     * 快递单号
     */
    private String shipOrderNumber;

    /**
     * 退款方式(1:订单退款,2:货品退款,3:退货退款)
     */
    private Integer refundMode;

    /**
     * 退款支付状态
     */
    private Integer refundPayStatus;

    /**
     * 退款支付类型
     */
    private Integer refundPayType;

    /**
     * 买家发货时间
     */
    private Date buyerDeliverDate;

    /**
     * 卖家确认到货时间
     */
    private Date sellerConfirmArrivalDate;

    /**
     * 退款批次号
     */
    private String refundBatchNo;

    /**
     * 退款异步提交时间
     */
    private Date refundPostTime;

    /**
     * 申请售后数量
     */
    private Long applyQuantity;

    /**
     * 退货数量
     */
    private Long returnQuantity;

    /**
     * 平台佣金退还
     */
    private BigDecimal returnPlatCommission;

    /**
     * 申请次数
     */
    private Integer applyNumber;

    /**
     * 凭证图片1
     */
    private String certPic1;

    /**
     * 凭证图片2
     */
    private String certPic2;

    /**
     * 凭证图片3
     */
    private String certPic3;

    /**
     * 是否订单全部退
     */
    private Boolean hasAllReturn;

    /**
     * 退运费
     */
    private BigDecimal returnFreight;

    /**
     * 最后修改时间
     */
    private Date lastModifyTime;

    /**
     * 是否取消申请
     */
    private Boolean hasCancel;

    /**
     * 取消申请时间
     */
    private Date cancelDate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 重新申请对应的原始ID。0代表非重新申请的原始数据
     */
    private Long reapplyOriginId;
    /**
     * 是否删除。0：否；1：是
     */
    private Integer isDelete;

    private Long productId;

    private String skuId;
    private Integer platform;
}
