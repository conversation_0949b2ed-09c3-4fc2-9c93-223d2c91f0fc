package com.sankuai.shangou.seashop.pay.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.AllArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
@ThriftEnum
@AllArgsConstructor
public enum CardTypeEnum {

    /**
     * 个人网页支付时，传入的扣卡类型，默认：借记卡
     */
    DEBIT(1, "debit", "借记卡"),
    /**
     * 个人网页支付时，传入的扣卡类型：贷记卡
     */
    CREDIT(2, "credit", "贷记卡"),
    ;

    private Integer code;
    private String type;
    private String cardName;

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getType() {
        return type;
    }

    public String getCardName() {
        return cardName;
    }

    public static CardTypeEnum getByType(String type) {
        for (CardTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
