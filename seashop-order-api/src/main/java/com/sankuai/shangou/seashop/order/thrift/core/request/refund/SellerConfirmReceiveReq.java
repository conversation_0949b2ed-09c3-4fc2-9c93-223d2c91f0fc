package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "供应商确认收货审核请求参数")
public class SellerConfirmReceiveReq extends BaseParamReq {

    @FieldDoc(description = "店铺信息", requiredness = Requiredness.REQUIRED)
    private ShopDto shop;
    @FieldDoc(description = "用户信息", requiredness = Requiredness.REQUIRED)
    private UserDto user;
    @FieldDoc(description = "退款单号", requiredness = Requiredness.REQUIRED)
    private Long refundId;
    @FieldDoc(description = "确认收货数量")
    private Long confirmQuantity;

    @Override
    public void checkParameter() {
        if (shop == null) {
            throw new InvalidParamException("店铺信息不能为空");
        }
        if (user == null) {
            throw new InvalidParamException("用户信息不能为空");
        }
        if (refundId == null) {
            throw new InvalidParamException("退款单号不能为空");
        }
    }


}
