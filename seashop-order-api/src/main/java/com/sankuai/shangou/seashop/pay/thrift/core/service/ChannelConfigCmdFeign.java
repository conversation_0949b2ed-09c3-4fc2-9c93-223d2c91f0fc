package com.sankuai.shangou.seashop.pay.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ChannelConfigSaveReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description: 渠道配置操作服务  https://xframe.mws.cloud.test.sankuai.com
 */
@FeignClient(name = "himall-order", contextId = "ChannelConfigCmdFeign", path = "/himall-order/channelConfig", url = "${himall-order.dev.url:}")
public interface ChannelConfigCmdFeign {

    /**
     * 保存渠道配置信息
     */
    @PostMapping(value = "/update", consumes = "application/json")
    ResultDto<BaseResp> update(@RequestBody ChannelConfigSaveReq request) throws TException;
}
