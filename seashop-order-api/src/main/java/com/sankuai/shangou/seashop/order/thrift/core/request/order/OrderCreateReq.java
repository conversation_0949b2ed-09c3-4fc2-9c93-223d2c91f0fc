package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import com.sankuai.shangou.seashop.order.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderPlatformEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname OrderCreateReq
 * Description 创建订单实体类
 * @date 2024/11/7 13:52
 */
@Data
public class OrderCreateReq implements Serializable {
    /**
     * 订单号
     */
    private String orderNo;
    /**
     * 用户ID
     */
    private UserDto userInfo;
    /**
     * 收货地址
     */
    private ShippingAddressDto shippingAddress;
    /**
     * 订单子项列表
     */
    private List<OrderItemReq> orderItemList;
    /**
     * 订单平台，orderSource=2时不用传。0：PC；2：小程序
     */
    private OrderPlatformEnum platform;
    /**
     * 附加信息
     */
    private AdditionReq additionReq;
    /**
     * {@link com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderTypeEnum}
     */
    private Integer orderType;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 商品级优惠后总计
     */
    private BigDecimal productTotalAmount;
    /**
     * 订单级优惠总额
     */
    private BigDecimal discountAmount;
    /**
     * 订单改价增量金额(改价前-改价后)
     */
    private BigDecimal changeAmount;
    /**
     * 发票税钱
     */
    private BigDecimal tax;
    /**
     * 运费
     */
    private BigDecimal freight;
//    /**
//     * 订单实付金额：商品级优惠后总计-订单级优惠-改价金额总计+邮费+税费
//     */
//    private BigDecimal totalAmount;
    /**
     * 扣减积分量
     */
    private Long integral;
    /**
     * 积分兑换汇率：N 积分/元
     */
    private Long integralExchangeRate;
    /**
     * 是否包邮
     */
    private Boolean isFreeFreight;
    /**
     * 是否立即购买
     */
    private Boolean whetherBuyNow;
    /**
     * 供应商电话
     */
    private String sellerPhone;
    /**
     * 供应商发货地址
     */
    private String sellerAddress;
    /**
     * 供应商备注
     */
    private String sellerRemark;
    /**
     * 供应商说明标识
     */
    private Integer sellerRemarkFlag;
}
