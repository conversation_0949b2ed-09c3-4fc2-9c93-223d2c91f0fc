package com.sankuai.shangou.seashop.order.thrift.finance.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "保证金退款查询条件对象")
public class CashDepositRefundQueryReq extends BasePageReq {

    @FieldDoc(description = "店铺id列表")
    private List<Long> shopIdList;

    @FieldDoc(description = "退款状态")
    private Integer status;


}
