package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Data
@ToString
@AllArgsConstructor
@NoArgsConstructor
@TypeDoc(description = "订单统计入参")
public class OrderStatisticsReq extends BaseParamReq {

    @FieldDoc(description = "开始时间")
    private Date beginTime;

    @FieldDoc(description = "结束时间")
    private Date endTime;

    @FieldDoc(description = "店铺id")
    private Long shopId;

    @Override
    public void checkParameter() {
        if (null == this.beginTime) {
            throw new InvalidParamException("开始时间不能为空");
        }
        if (null == this.endTime) {
            throw new InvalidParamException("结束时间不能为空");
        }
        // 开始时间不能大于结束时间
        if (this.beginTime.getTime() > this.endTime.getTime()) {
            throw new InvalidParamException("开始时间不能大于结束时间");
        }
    }


    public Long getBeginTimeLong() {
        return this.date2Long(this.beginTime);
    }


    public void setBeginTimeLong(Long beginTime) {
        this.beginTime = this.long2Date(beginTime);
    }


    public Long getEndTimeLong() {
        return this.date2Long(this.endTime);
    }


    public void setEndTimeLong(Long endTime) {
        this.endTime = this.long2Date(endTime);
    }


}
