package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "查询异常订单入参")
public class QueryExceptionOrderReq extends BasePageReq {

    @FieldDoc(description = "订单id")
    private String orderId;
    @FieldDoc(description = "退款状态。0:待退款；1:退款中；2:退款完成；3:退款失败；")
    private Integer refundStatus;


}
