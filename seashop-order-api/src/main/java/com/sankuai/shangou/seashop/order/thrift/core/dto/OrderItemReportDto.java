package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单明细请求入参")
public class OrderItemReportDto extends BaseThriftDto {

    private Long id;

    /**
     * 订单id，对应order表的order_id
     */

    private String orderId;

    private Integer platform;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * skuid
     */
    private String skuId;

    /**
     * sku表sku字段
     */
    private String sku;

    /**
     * 购买数量
     */
    private Long quantity;

    /**
     * 退货数量
     */
    private Long returnQuantity;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 销售价，商品实际售价，不需要考虑折扣。如果是限时购与组合购商品，是活动价格，其他情况就是 专享价>阶梯价>原价
     */
    private BigDecimal salePrice;

    /**
     * 优惠金额，
     * 注意：用于改价，这个与折扣和优惠券都没有关系
     */
    private BigDecimal discountAmount;

    /**
     * 商品实际应付金额=salePrice*quantity-fullDiscount-moneyOff/couponDiscount
     */
    private BigDecimal realTotalPrice;

    /**
     * 退款价格
     */
    private BigDecimal refundPrice;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * sku颜色
     */
    private String color;

    /**
     * sku尺寸
     */
    private String size;

    /**
     * sku版本
     */
    private String version;

    /**
     * 缩略图
     */
    private String thumbnailsUrl;

    /**
     * 分佣比例
     */
    private BigDecimal commisRate;

    /**
     * 可退金额
     */
    private BigDecimal enabledRefundAmount;

    /**
     * 已申请退货数量
     */
    private Long applyRefundQuantity;

    /**
     * 是否为限时购商品
     */
    private Boolean isLimitBuy;

    /**
     * 优惠券抵扣金额-均摊到订单项的金额
     */
    private BigDecimal couponDiscount;

    /**
     * 折扣均摊金额
     */
    private BigDecimal fullDiscount;

    /**
     * 满减活动均摊金额
     */
    private BigDecimal moneyOff;

    /**
     * 折扣营销活动id
     */
    private Long activeId;

    /**
     * 限时购活动id
     */
    private Long flashSaleId;

    /**
     * sku表auto_id字段
     */
    private Long skuAutoId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 订单时间
     */
    private Date orderTime;

    /**
     * 支付时间
     */
    private Date paymentTime;

    private Long userId;
}
