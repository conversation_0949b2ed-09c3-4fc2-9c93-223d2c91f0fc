package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "重新购买请求入参")
public class UpdateReceiverReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "顶级区域ID(省ID)")
    private Integer topRegionId;
    @FieldDoc(description = "区域ID", requiredness = Requiredness.REQUIRED)
    private Integer regionId;
    @FieldDoc(description = "收货人", requiredness = Requiredness.REQUIRED)
    private String shipTo;
    @FieldDoc(description = "收货地址")
    private String address;
    @FieldDoc(description = "详细地址")
    private String addressDetail;
    @FieldDoc(description = "收货人电话", requiredness = Requiredness.REQUIRED)
    private String cellPhone;
    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)
    private Long userId;

    @Override
    public void checkParameter() {
        if (orderId == null) {
            throw new IllegalArgumentException("orderId不能为空");
        }
        if (regionId == null) {
            throw new IllegalArgumentException("regionId不能为空");
        }
        if (shipTo == null) {
            throw new IllegalArgumentException("shipTo不能为空");
        }
        if (cellPhone == null) {
            throw new IllegalArgumentException("cellPhone不能为空");
        }
        if (userId == null) {
            throw new IllegalArgumentException("userId不能为空");
        }
    }


}
