package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2024/1/29/029
 * @description:
 */
@ThriftEnum
public enum OrderRefundRecordStatusEnum {

    /*
    退款状态 0: 退款中,1: 退款成功,2: 退款失败
     */
    REFUNDING(0, "退款中"),
    REFUND_SUCCESS(1, "退款成功"),
    REFUND_FAIL(2, "退款失败"),
    ;

    private final Integer code;
    private final String desc;

    OrderRefundRecordStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
