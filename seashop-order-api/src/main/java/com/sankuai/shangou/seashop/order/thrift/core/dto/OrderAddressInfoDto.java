package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TypeDoc(description = "订单收货地址对象")
public class OrderAddressInfoDto extends BaseThriftDto {

    @FieldDoc(description = "收货人")
    private String shipTo;

    @FieldDoc(description = "收货人电话")
    private String cellPhone;

    @FieldDoc(description = "收货人区域id")
    private Integer regionId;

    @FieldDoc(description = "收货人地址省份id")
    private Integer topRegionId;

    @FieldDoc(description = "地址")
    private String address;

    @FieldDoc(description = "全名的收货地址")
    private String regionFullName;

    @Setter
    @Getter
    @FieldDoc(description = "收货地址坐标")
    private BigDecimal receiveLongitude;

    @Setter
    @Getter
    @FieldDoc(description = "收货地址坐标")
    private BigDecimal receiveLatitude;


    public String getReceiveLongitudeString() {
        return this.bigDecimal2String(this.receiveLongitude);
    }


    public void setReceiveLongitudeString(String receiveLongitude) {
        this.receiveLongitude = this.string2BigDecimal(receiveLongitude);
    }


    public String getReceiveLatitudeString() {
        return this.bigDecimal2String(this.receiveLatitude);
    }


    public void setReceiveLatitudeString(String receiveLatitude) {
        this.receiveLatitude = this.string2BigDecimal(receiveLatitude);
    }
}
