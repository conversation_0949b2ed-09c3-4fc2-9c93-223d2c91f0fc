package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

import java.util.Arrays;

/**
 * 异常订单退款状态
 * <AUTHOR>
 */
@ThriftEnum
public enum ExceptionOrderRefundStatusEnum {

    // 待退款
    WAIT_REFUND(0, "待退款"),
    // 退款中
    REFUNDING(1, "退款中"),
    // 退款完成
    REFUNDED(2, "退款完成"),
    // 退款失败
    REFUND_FAIL(3, "退款失败"),
    ;

    private final Integer code;
    private final String desc;

    ExceptionOrderRefundStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ExceptionOrderRefundStatusEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(exceptionOrderType -> exceptionOrderType.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(exceptionOrderType -> exceptionOrderType.getCode().equals(code))
                .findFirst()
                .map(ExceptionOrderRefundStatusEnum::getDesc)
                .orElse("");
    }

}
