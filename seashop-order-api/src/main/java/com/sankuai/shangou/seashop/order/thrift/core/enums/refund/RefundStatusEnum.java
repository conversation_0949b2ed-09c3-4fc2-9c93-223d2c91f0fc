package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 状态，与审核状态基本一致，有一些小差别，这个状态也对应售后单的处理进度，这个进度是显示在详情页顶部的，与日志表里面的step不是一个东西
 * <AUTHOR>
 */
@ThriftEnum
public enum RefundStatusEnum {

    // 买家申请售后 1
    BUYER_APPLY(0, "买家申请", "买家申请售后"),
    // 待供应商审核  1
    WAIT_SUPPLIER_AUDIT(1, "待供应商审核", "供应商审核申请"),
    // 待买家寄货  1
    WAIT_BUYER_SEND(2, "待买家寄货", "买家回寄商品"),
    // 待供应商收货 1
    WAIT_SUPPLIER_RECEIVE(3, "待供应商收货", "供应商确认收货"),
    // 供应商拒绝  -1
    SUPPLIER_REFUSE(4, "供应商拒绝", "供应商拒绝"),
    // 待平台确认  1
    WAIT_PLATFORM_CONFIRM(6, "待平台确认", "平台确认"),
    // 退款成功  1
    REFUND_SUCCESS(7, "退款成功", "汇付退款"),
    // 平台驳回  -1
    PLATFORM_REFUSE(8, "平台驳回", "平台驳回申请"),
    // 平台确认通过，退款中  0
    REFUNDING(9, "退款中", "退款中"),
    // 买家取消售后申请  1
    BUYER_CANCEL(-1, "买家取消", "买家取消"),


    ;

    private final Integer code;
    private final String desc;
    private final String stepDesc;

    RefundStatusEnum(Integer code, String desc, String stepDesc) {
        this.code = code;
        this.desc = desc;
        this.stepDesc = stepDesc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getStepDesc() {
        return stepDesc;
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .map(RefundStatusEnum::getDesc)
                .orElse(null);
    }

    public static String getStepDesc(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .map(RefundStatusEnum::getStepDesc)
                .orElse(null);
    }

    public static RefundStatusEnum valueOf(Integer code) {
        // 如果是供应商通过审核，则代表是待平台确认
        if (RefundAuditStatusEnum.SUPPLIER_PASS.getCode().equals(code)) {
            return WAIT_PLATFORM_CONFIRM;
        }
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
