package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/25 11:12
 */
@Data
@ToString
@TypeDoc(description = "查询商品评论汇总数据")
public class QueryProductCommentSummaryReq extends BaseParamReq {

    @FieldDoc(description = "商品id", requiredness = Requiredness.REQUIRED)
    private Long productId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(productId == null || productId <= 0, "商品id不能为空");
    }


}
