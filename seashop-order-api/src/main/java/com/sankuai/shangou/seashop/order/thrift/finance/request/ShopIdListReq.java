package com.sankuai.shangou.seashop.order.thrift.finance.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "店铺ID列表请求体")
public class ShopIdListReq extends BasePageReq {

    @FieldDoc(description = "店铺id列表", requiredness = Requiredness.REQUIRED)
    private List<Long> shopIdList;

    @Override
    public void checkParameter() {
//        if (CollUtil.isEmpty(this.shopIdList)) {
//            throw new InvalidParamException("shopIdList不能为空");
//        }
    }


}
