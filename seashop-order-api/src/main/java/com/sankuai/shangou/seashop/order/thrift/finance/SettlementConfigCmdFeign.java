package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettlementConfigReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description: 结算配置相关操作服务
 */
@FeignClient(name = "himall-order", contextId = "SettlementConfigCmdFeign", path = "/himall-order/finance/settlementConfigCmd", url = "${himall-order.dev.url:}")
public interface SettlementConfigCmdFeign {

    /**
     * 更新结算配置
     */
    @PostMapping(value = "update", consumes = "application/json")
    ResultDto<BaseResp> update(@RequestBody SettlementConfigReq request) throws TException;
}
