package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.*;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description：投诉维权新增、修改、删除类接口
 * @author： liweisong
 * @create： 2023/11/21 18:02
 */

@FeignClient(name = "himall-order", contextId = "ComplaintCmdFeign", path = "/himall-order/complaintCmd", url = "${himall-order.dev.url:}")
public interface ComplaintCmdFeign {

    /**
     * 商家-投诉维权-发起投诉(我要投诉)
     */
    @PostMapping(value = "/startComplaint", consumes = "application/json")
    ResultDto<BaseResp> startComplaint(@RequestBody StartComplaintReq cmdOrderComplaintReq) throws TException ;

    /**
     * 商家-投诉维权-投诉维权取消
     */
    @PostMapping(value = "/cancelComplaint", consumes = "application/json")
    ResultDto<BaseResp> cancelComplaint(@RequestBody CancelComplaintReq cancelComplaintReq) throws TException;

    /**
     * 供应商-交易-投诉维权处理
     */
    @PostMapping(value = "/dealSellerComplaint", consumes = "application/json")
    ResultDto<BaseResp> dealSellerComplaint(@RequestBody DealSellerComplaintReq dealSellerComplaintReq) throws TException;

    /**
     * 供应商-交易-投诉维权申请仲裁
     */
    @PostMapping(value = "/applyArbitrateComplaint", consumes = "application/json")
    ResultDto<BaseResp> applyArbitrateComplaint(@RequestBody ApplyArbitrateComplaintReq applyArbitrateComplaintReq) throws TException;

    /**
     * 商家投诉维权申请仲裁
     */
    @PostMapping(value = "/applyMallArbitrateComplaint", consumes = "application/json")
    ResultDto<BaseResp> applyMallArbitrateComplaint(@RequestBody ApplyArbitrateComplaintReq applyArbitrateComplaintReq) throws TException;

    /**
     * 平台-交易-交易投诉-投诉维权处理
     */
    @PostMapping(value = "/dealMComplaint", consumes = "application/json")
    ResultDto<BaseResp> dealMComplaint(@RequestBody DealMComplaintReq dealMComplaintReq) throws TException;

}
