package com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "创建确认订单分账信息请求对象")
public class AdaPaymentConfirmDivMembersDto extends BaseParamReq {

    /**
     * 分账用户 Member对象 的 id；若是商户本身时，传入0
     */
    @FieldDoc(description = "分账用户", requiredness = Requiredness.REQUIRED)
    private String memberId;
    /**
     * 分账金额，精确到分，如0.50，1.00等，分账总金额必须等于主交易金额,金额不能为0.00
     */
    @FieldDoc(description = "分账金额（单位：元）", requiredness = Requiredness.REQUIRED)
    private BigDecimal amount;

    /**
     * 是否手续费承担方，N-否，Y-是，手续费承担方有且只能有一个
     */
    private String feeFlag;

    /**
     * 控制台 主页面应用的app_id，不上送默认取商户自身app_id
     */
    private String appId;


    public String getAmountString() {
        return this.bigDecimal2String(this.amount);
    }


    public void setAmountString(String amount) {
        this.amount = this.string2BigDecimal(amount);
    }


}
