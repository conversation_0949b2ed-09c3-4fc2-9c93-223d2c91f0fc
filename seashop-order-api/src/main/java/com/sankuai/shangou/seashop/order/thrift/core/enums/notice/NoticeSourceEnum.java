package com.sankuai.shangou.seashop.order.thrift.core.enums.notice;

import com.facebook.swift.codec.ThriftEnum;

/**
 * <AUTHOR>
 * @date 2024/01/29 17:34
 */
@ThriftEnum
public enum NoticeSourceEnum {

    /**
     * 通知来源 0: 牵牛花 1：旺店通 2：聚水潭 3：网店管家 4：吉客云 5: 易久批
     */
    MT(0, "牵牛花"),
    WDT(1, "旺店通"),
    JST(2, "聚水潭"),
    WDGJ(3, "网店管家"),
    JKY(4, "吉客云"),
    YJP(5, "易久批"),
    ;

    private final Integer code;
    private final String desc;

    NoticeSourceEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
