package com.sankuai.shangou.seashop.pay.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.AllArgsConstructor;

/**
 * @author: lhx
 * @date: 2024/01/08
 * @description: 业务状态类型
 */
@AllArgsConstructor
@ThriftEnum
public enum BusinessStatusTypeEnum {

    // 业务状态类型：1：正常订单退款；2：异常订单退款；3：订单补偿退款（支付金额大于订单金额）

    NORMAL(1, "正常订单退款"),
    ABNORMAL(2, "异常订单退款"),
    COMPENSATION(3, "订单补偿退款（支付金额大于订单金额）"),

    ;

    private Integer type;
    private String name;

    @ThriftEnumValue
    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    /**
     * 根据type获取枚举
     *
     * @param type
     * @return
     */
    public static BusinessStatusTypeEnum getByType(Integer type) {
        for (BusinessStatusTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }

}
