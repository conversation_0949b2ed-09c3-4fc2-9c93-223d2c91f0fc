package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettlementConfigResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description: 结算配置相关查询服务
 */
@FeignClient(name = "himall-order", contextId = "SettlementConfigQueryFeign", path = "/himall-order/finance/settlementConfigQuery", url = "${himall-order.dev.url:}")
public interface SettlementConfigQueryFeign {

    /**
     * 获取结算配置
     */
    @PostMapping(value = "getConfig", consumes = "application/json")
    ResultDto<SettlementConfigResp> getConfig() throws TException;
}
