package com.sankuai.shangou.seashop.order.thrift.finance;

import org.apache.thrift.TException;import org.springframework.cloud.openfeign.FeignClient;

import org.springframework.web.bind.annotation.PostMapping;

import com.facebook.swift.service.ThriftMethod;
import com.facebook.swift.service.ThriftService;
import com.meituan.servicecatalog.api.annotations.ExceptionDoc;
import com.meituan.servicecatalog.api.annotations.InterfaceDoc;
import com.meituan.servicecatalog.api.annotations.MethodDoc;
import com.meituan.servicecatalog.api.annotations.ParamDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdListReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositListResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositResp;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description: 保证金相关查询服务
 */
@FeignClient(name = "himall-order", contextId = "CashDepositQueryFeign", path = "/himall-order/finance/cashDeposit", url = "${himall-order.dev.url:}")
public interface CashDepositQueryFeign {

    /**
     * 通过ShopId列表查询保证金信息
     */
    @PostMapping(value = "queryListByShopId", consumes = "application/json")
    ResultDto<BasePageResp<CashDepositResp>> queryListByShopId(@RequestBody ShopIdListReq request) throws TException;


    /**
     * 通过ShopId查询保证金信息
     */
    @PostMapping(value = "queryOneByShopId", consumes = "application/json")
    ResultDto<CashDepositResp> queryOneByShopId(@RequestBody ShopIdReq request) throws TException;


    /**
     * 通过ShopId查询店铺保证金列表
     */
    @PostMapping(value = "queryByShopIdList", consumes = "application/json")
    ResultDto<CashDepositListResp> queryByShopIdList(@RequestBody ShopIdListReq request) throws TException;

}
