package com.sankuai.shangou.seashop.pay.thrift.core.request;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.CardTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.PayPaymentCreateExpendDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@ToString
@TypeDoc(description = "创建支付请求对象")
public class PayPaymentCreateReq extends BaseParamReq {

    @FieldDoc(description = "支付渠道:1汇付天下支付", requiredness = Requiredness.REQUIRED)
    private Integer paymentChannel;

    @FieldDoc(description = "来源订单id", requiredness = Requiredness.REQUIRED)
    private String orderId;

    @FieldDoc(description = "支付金额（单元：元）", requiredness = Requiredness.REQUIRED)
    private BigDecimal payAmount;

    @FieldDoc(description = "支付类型", requiredness = Requiredness.REQUIRED)
    private Integer paymentType;

    @FieldDoc(description = "业务类型", requiredness = Requiredness.REQUIRED)
    private Integer businessType;

    /**
     * 商品标题
     */
    @FieldDoc(description = "商品标题", requiredness = Requiredness.REQUIRED)
    private String goodsTitle;

    /**
     * 商品描述信息，微信小程序和微信公众号该字段最大长度42个字符
     */
    @FieldDoc(description = "商品描述信息", requiredness = Requiredness.REQUIRED)
    private String goodsDesc;

    /**
     * 交易设备所在的公网 IP
     */
    @FieldDoc(description = "交易设备所在的公网 IP", requiredness = Requiredness.REQUIRED)
    private String deviceIp;

    @FieldDoc(description = "支付渠道扩展参数")
    private PayPaymentCreateExpendDto expend;

    @Override
    public void checkParameter() {
        if (this.paymentChannel == null) {
            throw new InvalidParamException("paymentChannel不能为空");
        }
        if (PaymentChannelEnum.getByCode(this.paymentChannel) == null) {
            throw new InvalidParamException("paymentChannel不合法");
        }
        if (StrUtil.isBlank(this.orderId)) {
            throw new InvalidParamException("orderId不能为空");
        }
        if (null == this.payAmount || this.payAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("payAmount不能为空且不小于0");
        }
        if (null == this.paymentType) {
            throw new InvalidParamException("paymentType不能为空");
        }
        if (null == PaymentTypeEnum.getByType(this.paymentType)) {
            throw new InvalidParamException("paymentType不合法");
        }
        if (null == this.businessType) {
            throw new InvalidParamException("businessType不能为空");
        }
        if (null == BusinessTypeEnum.getByType(this.businessType)) {
            throw new InvalidParamException("businessType不合法");
        }
        if (StrUtil.isBlank(this.goodsTitle)) {
            throw new InvalidParamException("商品标题不能为空");
        }
        if (StrUtil.isBlank(this.goodsDesc)) {
            throw new InvalidParamException("商品描述信息不能为空");
        }
        if (StrUtil.isBlank(this.deviceIp)) {
            throw new InvalidParamException("交易设备所在的公网 IP不能为空");
        }
        if (PaymentTypeEnum.hasExpend(this.paymentType)) {
            if (null == this.expend) {
                throw new InvalidParamException("paymentType为" + this.paymentType + "时，expend不能为空");
            }
            if (PaymentTypeEnum.WECHAT_APPLET.getType().equals(this.paymentType)) {
                if (StrUtil.isBlank(this.expend.getOpenId())) {
                    throw new InvalidParamException("paymentType为" + this.paymentType + "时，openId不能为空");
                }
            } else if (PaymentTypeEnum.COMPANY_BANK.getType().equals(this.paymentType) || PaymentTypeEnum.PERSON_BANK.getType().equals(this.paymentType)) {
                if (StrUtil.isBlank(this.expend.getAcctIssrId())) {
                    throw new InvalidParamException("paymentType为" + this.paymentType + "时，acctIssrId不能为空");
                }
                if (StrUtil.isBlank(this.expend.getCardType())) {
                    this.expend.setCardType(CardTypeEnum.DEBIT.getType());
//                    throw new InvalidParamException("paymentType为" + this.paymentType + "时，cardType不能为空");
                }
                if (StrUtil.isBlank(this.expend.getClientIp())) {
                    throw new InvalidParamException("paymentType为" + this.paymentType + "时，clientIp不能为空");
                }
                if (StrUtil.isBlank(this.expend.getCallbackUrl())) {
                    throw new InvalidParamException("paymentType为" + this.paymentType + "时，callbackUrl不能为空");
                }
            }
        }
    }


    public String getPayAmountString() {
        return this.bigDecimal2String(this.payAmount);
    }


    public void setPayAmountString(String payAmount) {
        this.payAmount = this.string2BigDecimal(payAmount);
    }


}
