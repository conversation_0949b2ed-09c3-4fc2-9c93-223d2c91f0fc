package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderExpressDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInvoiceDto;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单明细返回对象")
@Getter
@Setter
public class OrderDetailResp {

    @FieldDoc(description = "状态变更的时间，前端根据数量来判断和显示步骤")
    private List<String> statusChangeTimeList;

    @FieldDoc(description = "订单基本信息")
    private OrderInfoDto orderInfo;

    @FieldDoc(description = "订单发票信息")
    private OrderInvoiceDto orderInvoice;

    @FieldDoc(description = "订单物流信息")
    private List<OrderExpressDto> expressList;

    @FieldDoc(description = "微信发货开关")
    private Boolean wxSend;

}
