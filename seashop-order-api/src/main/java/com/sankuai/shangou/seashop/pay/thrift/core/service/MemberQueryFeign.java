package com.sankuai.shangou.seashop.pay.thrift.core.service;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaySettleAccountQryReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description: 账户相关查询服务
 */
@FeignClient(name = "himall-order", contextId = "MemberQueryFeign", path = "/himall-order/member", url = "${himall-order.dev.url:}")
public interface MemberQueryFeign {

    /**
     * 判断结算账号是否存在
     *
     */
    @PostMapping(value = "/hasSettleAccount", consumes = "application/json")
    ResultDto<Boolean> hasSettleAccount(@RequestBody PaySettleAccountQryReq request) throws TException;
}
