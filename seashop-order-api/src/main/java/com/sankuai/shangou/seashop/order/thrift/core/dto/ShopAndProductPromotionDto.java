package com.sankuai.shangou.seashop.order.thrift.core.dto;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "店铺信息对象")
@ToString
@Data
@Getter
@Setter
public class ShopAndProductPromotionDto {

    @FieldDoc(description = "店铺营销，可能满足多个营销")

    private List<PromotionDto> shopPromotionList;
    @FieldDoc(description = "店铺下多个商品的营销,以SKU为key")

    private Map<String/*skuId*/, ProductPromotionDto> productPromotionMap;

}
