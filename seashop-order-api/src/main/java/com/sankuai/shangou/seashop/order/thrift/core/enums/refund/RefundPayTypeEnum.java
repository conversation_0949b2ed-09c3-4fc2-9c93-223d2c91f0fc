package com.sankuai.shangou.seashop.order.thrift.core.enums.refund;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 退款方式
 * <AUTHOR>
 */
@ThriftEnum
public enum RefundPayTypeEnum {

    // 原路返回
    ORIGINAL(1, "原路返回"),
    // 线下收款。.net枚举，注释保留记录
    //OFFLINE(2, "线下收款"),
    // 退到预存款。.net枚举，注释保留记录
    //PRE_DEPOSIT(3, "退到预存款"),
    ;

    private final Integer code;
    @Getter
    private final String desc;

    RefundPayTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .map(RefundPayTypeEnum::getDesc)
                .orElse(null);
    }

    public static RefundPayTypeEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

}
