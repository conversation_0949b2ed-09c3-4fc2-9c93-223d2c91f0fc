package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/25 13:40
 */
@Data
@ToString
@TypeDoc(description = "店铺评价汇总返回值")
public class ShopCommentSummaryResp extends BaseThriftDto {

    @FieldDoc(description = "待回复评价数")
    private Integer waitReplyCount;

    @FieldDoc(description = "评价总数")
    private Integer totalCount;


}
