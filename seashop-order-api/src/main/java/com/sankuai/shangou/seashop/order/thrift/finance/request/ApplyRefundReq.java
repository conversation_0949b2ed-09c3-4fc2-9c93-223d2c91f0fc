package com.sankuai.shangou.seashop.order.thrift.finance.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/25 14:20
 */
@Data
@TypeDoc(description = "公共id入参对象")
@ToString
public class ApplyRefundReq extends BaseParamReq {


    @FieldDoc(description = "主键id")
    @PrimaryField(title = "主键id")
    @ExaminField(description = "主键id")
    private Long id;

    @FieldDoc(description = "店铺Id")
    private Long shopId;


    @Override
    public void checkParameter() {
        Optional.ofNullable(id).filter(x -> x > 0).orElseThrow(() -> new IllegalArgumentException("主键不能为空"));
    }


}
