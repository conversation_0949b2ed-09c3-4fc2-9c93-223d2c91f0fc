package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "取消支付请求入参")
@Getter
@Setter
public class QueryPayChannelReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)

    private List<String> orderIdList;
    @FieldDoc(description = "用户ID", requiredness = Requiredness.REQUIRED)

    private Long userId;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (userId == null) {
            throw new IllegalArgumentException("userId不能为空");
        }
        if (orderIdList == null || orderIdList.isEmpty()) {
            throw new IllegalArgumentException("orderIdList不能为空");
        }
    }

}
