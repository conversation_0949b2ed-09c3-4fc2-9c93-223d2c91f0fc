package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.*;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.*;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 * 主要应用于小程序端和PC端对订单售后的操作场景
 */
@FeignClient(name = "himall-order", contextId = "OrderRefundQueryFeign", path = "/himall-order/orderRefundQuery", url = "${himall-order.dev.url:}")
public interface OrderRefundQueryFeign {

    /**
     * 查询 订单 退款预览信息，订单列表页面使用
     */
    @PostMapping(value = "getOrderRefundPreview", consumes = "application/json")
    ResultDto<OrderRefundPreviewResp> getOrderRefundPreview(@RequestBody QueryOrderRefundPreviewReq queryReq) throws TException;

    /**
     * 查询 订单明细 退款预览信息，订单列表页面使用
     */
    @PostMapping(value = "getOrderItemRefundPreview", consumes = "application/json")
    ResultDto<OrderItemRefundPreviewResp> getOrderItemRefundPreview(@RequestBody QueryOrderItemRefundPreviewReq queryReq) throws TException;

    /**
     * 商家查询订单售后详情
     */
    @PostMapping(value = "userQueryDetail", consumes = "application/json")
    ResultDto<UserRefundDetailResp> userQueryDetail(@RequestBody UserQueryRefundDetailReq queryReq) throws TException;

    /**
     * 商家查询订单售后详情
     */
    @PostMapping(value = "userQueryDetailExt", consumes = "application/json")
    ResultDto<UserRefundDetailExtResp> userQueryDetailExt(@RequestBody UserQueryRefundDetailReq queryReq) throws TException;

    /**
     * 供应商查询订单售后详情
     */
    @PostMapping(value = "sellerQueryDetail", consumes = "application/json")
    ResultDto<SellerRefundDetailResp> sellerQueryDetail(@RequestBody SellerQueryRefundDetailReq queryReq) throws TException;

    /**
     * 平台查询订单售后详情
     */
    @PostMapping(value = "platformQueryDetail", consumes = "application/json")
    ResultDto<PlatformRefundDetailResp> platformQueryDetail(@RequestBody PlatformQueryRefundDetailReq queryReq) throws TException;

    /**
     * 查询订单售后操作日志
     */
    @PostMapping(value = "queryRefundLog", consumes = "application/json")
    ResultDto<RefundLogListResp> queryRefundLog(@RequestBody BaseIdReq queryReq) throws TException;

    /**
     * 商家分页查询订单售后
     */
    @PostMapping(value = "userQueryRefundPage", consumes = "application/json")
    ResultDto<BasePageResp<UserRefundDto>> userQueryRefundPage(@RequestBody UserQueryRefundReq queryReq) throws TException;

    /**
     * 供应商分页查询订单售后
     */
    @PostMapping(value = "sellerQueryRefundPage", consumes = "application/json")
    ResultDto<BasePageResp<SellerRefundDto>> sellerQueryRefundPage(@RequestBody SellerQueryRefundReq queryReq) throws TException;

    /**
     * 平台分页查询订单售后
     */
    @PostMapping(value = "platformQueryRefundPage", consumes = "application/json")
    ResultDto<BasePageResp<PlatformRefundDto>> platformQueryRefundPage(@RequestBody PlatformQueryRefundReq queryReq) throws TException;

    /**
     * 查询买家寄货的物流信息
     */
    @PostMapping(value = "queryUserDeliverExpress", consumes = "application/json")
    ResultDto<RefundUserDeliverExpressResp> queryUserDeliverExpress(@RequestBody BaseIdReq queryReq) throws TException;

    /**
     * ERP根据订单ID列表批量查询退款信息
     */
    @PostMapping(value = "queryErpRefundList", consumes = "application/json")
    ResultDto<List<PlatformRefundDetailResp>> queryErpRefundList(@RequestBody ErpQueryRefundReq req) throws TException;

    /**
     * ERP分页查询退款信息
     */
    @PostMapping(value = "queryErpRefundPage", consumes = "application/json")
    ResultDto<BasePageResp<PlatformRefundDetailResp>> queryErpRefundPage(@RequestBody ErpQueryRefundPageReq req) throws TException;

}
