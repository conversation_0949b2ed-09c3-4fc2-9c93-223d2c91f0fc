package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ExceptionOrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryExceptionOrderReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 主要应用于平台对异常订单的操作场景
 * <AUTHOR>
 */
@FeignClient(name = "himall-order", contextId = "ExceptionOrderQueryFeign", path = "/himall-order/exceptionOrderQuery", url = "${himall-order.dev.url:}")
public interface ExceptionOrderQueryFeign {

    /**
     * 分页查询异常订单
     */
    @PostMapping(value = "/pageList", consumes = "application/json")
    ResultDto<BasePageResp<ExceptionOrderInfoDto>> pageList(@RequestBody QueryExceptionOrderReq queryReq) throws TException;


}
