package com.sankuai.shangou.seashop.order.thrift.finance;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundConfirmReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundRefuseReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description: 保证金退款相关操作服务
 */
@FeignClient(name = "himall-order", contextId = "CashDepositRefundCmdFeign", path = "/himall-order/finance/cashDepositRefundCmd", url = "${himall-order.dev.url:}")
public interface CashDepositRefundCmdFeign {

    /**
     * 保证金退款审批拒绝
     *
     */
    @PostMapping(value = "/refuse", consumes = "application/json")
    ResultDto<BaseResp> refuse(@RequestBody CashDepositRefundRefuseReq request) throws TException;

    /**
     * 保证金退款审批通过
     *
     */
    @PostMapping(value = "/confirm", consumes = "application/json")
    ResultDto<BaseResp> confirm(@RequestBody CashDepositRefundConfirmReq request) throws TException;
}
