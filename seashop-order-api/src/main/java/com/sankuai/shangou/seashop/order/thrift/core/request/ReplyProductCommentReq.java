package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:37
 */
@Data
@ToString
@TypeDoc(description = "回复商品评论入参")
public class ReplyProductCommentReq extends BaseParamReq {

    @FieldDoc(description = "商品评论id", requiredness = Requiredness.REQUIRED)
    @PrimaryField(title = "商品评论id")
    @ExaminField(description = "商品评价id")
    private Long productCommentId;

    @FieldDoc(description = "店铺id", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @FieldDoc(description = "回复内容 ", requiredness = Requiredness.NONE)
    @ExaminField(description = "回复内容")
    private String replyContent;

    @FieldDoc(description = "追加回复内容", requiredness = Requiredness.NONE)
    @ExaminField(description = "追加回复内容")
    private String replyAppendContent;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(productCommentId == null || productCommentId <= 0, "商品评论id不能为空");
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isNotEmpty(replyContent) && replyContent.length() > 1000, "回复内容不能超过1000个字符");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isNotEmpty(replyAppendContent) && replyAppendContent.length() > 1000, "追加回复内容不能超过1000个字符");
    }


}
