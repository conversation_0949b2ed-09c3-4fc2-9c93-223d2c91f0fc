package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;
import lombok.ToString;
import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2025/04/02/ $
 * @description:
 */
@Data
@ToString
@TypeDoc(description = "确认收货")
public class ConfirmReceiptWxReq {

    @FieldDoc(description = "支付id")
    @NotBlank(message = "支付id不能空")
    private String thirdTransactionNo;
}
