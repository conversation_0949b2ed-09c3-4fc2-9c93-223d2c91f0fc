package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import java.util.Arrays;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

import lombok.Getter;

/**
 * 订单来源平台。0:PC,1:APP,2:小程序,3:H5
 * <AUTHOR>
 */
@ThriftEnum
public enum OrderPlatformEnum {

    PC(0, "PC商城"),
    //APP(1, "APP"),
    MINI_PROGRAM(2, "小程序商城"),
    //H5(3, "H5"),
    //QIAN_NIU_HUA(4, "牵牛花")
    ;

    private final Integer code;
    @Getter
    private final String desc;

    OrderPlatformEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public static OrderPlatformEnum valueOf(Integer code) {
        return Arrays.stream(values())
                .filter(platform -> platform.getCode().equals(code))
                .findFirst()
                .orElse(null);
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(platform -> platform.getCode().equals(code))
                .findFirst()
                .map(OrderPlatformEnum::getDesc)
            .orElse("其他");
    }

}
