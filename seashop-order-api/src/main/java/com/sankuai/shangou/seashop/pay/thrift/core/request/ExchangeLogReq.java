package com.sankuai.shangou.seashop.pay.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 16:22
 */
@Data
@TypeDoc(description = "汇付交互表入参")
public class ExchangeLogReq {

    @FieldDoc(description = "1:创建汇付支付单，2汇付支付成功回调")
    private Integer type;

    @FieldDoc(description = "方法")
    private String method;

    @FieldDoc(description = "接口请求入参")
    private String param;

    @FieldDoc(description = "接口返回返参")
    private String result;


}
