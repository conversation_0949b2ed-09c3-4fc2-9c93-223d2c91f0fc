package com.sankuai.shangou.seashop.pay.thrift.core.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.AllArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/23/022
 * @description: 退款状态
 */
@ThriftEnum
@AllArgsConstructor
public enum ReverseStateEnums {

    /**
     * 退款申请初始状态：退款中
     */
    REVERSE_ING(0, "退款中"),
    /**
     * 渠道返回成功：退款成功
     */
    REVERSE_SUCCESS(1, "退款成功"),
    /**
     * 渠道返回失败：退款失败
     */
    REVERSE_ERROR(2, "退款失败");

    /**
     * 返回状态
     */
    private Integer status;

    /**
     * 显示名
     */
    private String displayName;

    @ThriftEnumValue
    public Integer getStatus() {
        return status;
    }

    public String getDisplayName() {
        return displayName;
    }

}
