package com.sankuai.shangou.seashop.order.thrift.core.request.order;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "供应商重新购买请求入参")
public class ReBuyBySellerReq extends BaseParamReq {

    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "订单ID", requiredness = Requiredness.REQUIRED)
    private Long shopId;

    @Override
    public void checkParameter() {
        if (shopId == null) {
            throw new IllegalArgumentException("shopId不能为空");
        }
        if (StrUtil.isBlank(orderId)) {
            throw new IllegalArgumentException("orderId不能为空");
        }
    }


}
