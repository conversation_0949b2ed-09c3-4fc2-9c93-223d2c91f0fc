package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/3 14:35
 */
@Data
@TypeDoc(description = "订单配货单")
public class OrderDistributionFormResp extends BaseThriftDto {

    @FieldDoc(description = "订单单号")
    private String orderId;

    @FieldDoc(description = "商品名称")
    private String productName;

    @FieldDoc(description = "货号")
    private String productCode;

    @FieldDoc(description = "规格")
    private String skuName;

    @FieldDoc(description = "拣货数量")
    private String quantity;

    @FieldDoc(description = "现库存数")
    private String stockQuantity;

    @FieldDoc(description = "备注")
    private String remark;


}
