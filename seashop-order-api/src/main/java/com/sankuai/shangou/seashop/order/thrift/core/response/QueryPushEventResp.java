package com.sankuai.shangou.seashop.order.thrift.core.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.ThirdEvent;

/**
 * <AUTHOR>
 */
@TypeDoc(description = "推送事件返回")
public class QueryPushEventResp {
    /**
     * 数据主键
     */
    @FieldDoc(description = "数据主键，存在表示修改")
    private Long id;
    /**
     * 推送编号 可以为订单id或者 productId
     */
    @FieldDoc(description = "推送编号 可以为订单id或者 productId")
    private String sendCode;
    /**
     * 事件类型 0:订单事件 1:商品事件 2:退货单
     */
    @FieldDoc(description = "事件类型 0:订单事件 1:商品事件 2:退货单")
    private ThirdEvent.EventTypeEnum eventType;
    /**
     * 发送目标 1：旺店通 2：聚水潭 3：网店管家 4：吉客云
     */
    @FieldDoc(description = "发送目标 1：旺店通 2：聚水潭 3：网店管家 4：吉客云")
    private ThirdEvent.SendTargetEnum sendTarget;
    /**
     * 推送状态 0:待推送 1：推送成功 2：推送失败，3：推送中
     */
    @FieldDoc(description = "推送状态 0:待推送 1：推送成功 2：推送失败，3：推送中")
    private ThirdEvent.SendStateEnum sendState;
    /**
     * 事件body
     */
    @FieldDoc(description = "事件body")
    private String eventBody;
    /**
     * 推送返回的错误消息
     */
    @FieldDoc(description = "推送返回的错误消息")
    private String sendMsg;


}
