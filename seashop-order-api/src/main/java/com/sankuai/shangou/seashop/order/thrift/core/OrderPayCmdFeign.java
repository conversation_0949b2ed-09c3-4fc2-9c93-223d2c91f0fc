package com.sankuai.shangou.seashop.order.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.InitiatePayReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.InitiatePayResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * 主要应用于小程序端和PC端对订单发起支付时的相关查询场景
 */
@FeignClient(name = "himall-order", contextId = "OrderPayCmdFeign", path = "/himall-order/orderPayCmd", url = "${himall-order.dev.url:}")
public interface OrderPayCmdFeign {

    /**
     * 发起支付
     * 支持合并支付、PC端和小程序端发起支付
     */
    @PostMapping(value = "/initiatePay", consumes = "application/json")
    ResultDto<InitiatePayResp> initiatePay(@RequestBody InitiatePayReq initiatePayReq) throws TException;

}
