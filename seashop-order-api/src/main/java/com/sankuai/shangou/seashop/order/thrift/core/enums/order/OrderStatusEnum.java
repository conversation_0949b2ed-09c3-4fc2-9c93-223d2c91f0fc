package com.sankuai.shangou.seashop.order.thrift.core.enums.order;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.Getter;

import java.util.Arrays;

/**
 * 订单状态枚举：1:待付款,2:待发货,3:待收货,4:已关闭,5:已完成,6:支付中,7:待核销,8:待自提
 * <AUTHOR>
 */
@ThriftEnum
public enum OrderStatusEnum {

    // -1 前端查询全部
    UNKNOWN(-1, ""),
    UNDER_PAY(1, "待付款"),
    UNDER_SEND(2, "待发货"),
    UNDER_RECEIVE(3, "待收货"),
    CLOSED(4, "已关闭"),
    FINISHED(5, "已完成"),
    PAYING(6, "支付中"),
    UNDER_VERIFY(7, "待核销"),
    UNDER_PICKUP(8, "待自提"),
    ;

    private final Integer code;
    @Getter
    private final String desc;

    OrderStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }


    public static OrderStatusEnum getByCode(Integer code) {
        for (OrderStatusEnum value : OrderStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static OrderStatusEnum valueOf(Integer code) {
        for (OrderStatusEnum value : OrderStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(orderStatus -> orderStatus.getCode().equals(code))
                .findFirst()
                .map(OrderStatusEnum::getDesc)
                .orElse("");
    }

    public static boolean isPaid(Integer status) {
        return UNDER_SEND.getCode().equals(status) || UNDER_RECEIVE.getCode().equals(status) ||
                FINISHED.getCode().equals(status);
    }

    /**
     * 等同于待发货状态：待发货、待核销、待自提
     * @param status 订单状态
     * @return 是否待发货状态
     */
    public static boolean isEqualsUnderSend(Integer status) {
        return UNDER_SEND.getCode().equals(status) || UNDER_VERIFY.getCode().equals(status) ||
                UNDER_PICKUP.getCode().equals(status);
    }
}
