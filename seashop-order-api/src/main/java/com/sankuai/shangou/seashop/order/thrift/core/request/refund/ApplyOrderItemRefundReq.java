package com.sankuai.shangou.seashop.order.thrift.core.request.refund;

import cn.hutool.core.util.StrUtil;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundTypeEnum;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "订单明细退款申请入参")
public class ApplyOrderItemRefundReq extends BaseParamReq {

    @FieldDoc(description = "订单号", requiredness = Requiredness.REQUIRED)
    private String orderId;
    @FieldDoc(description = "订单明细id", requiredness = Requiredness.REQUIRED)
    private Long orderItemId;
    @FieldDoc(description = "售后类型。1：仅退款；2：退货退款", requiredness = Requiredness.REQUIRED)
    private RefundTypeEnum refundType;
    @FieldDoc(description = "申请退款金额", requiredness = Requiredness.REQUIRED)
    private BigDecimal refundAmount;
    // 这个数量字段，如果是仅退款，代表的是申请售后的数量，不是实际的退货数量
    @FieldDoc(description = "申请退款数量", requiredness = Requiredness.REQUIRED)
    private Long refundQuantity;
    @FieldDoc(description = "退款原因描述。基础服务提供接口获取选项", requiredness = Requiredness.REQUIRED)
    private String refundReasonDesc;
    @FieldDoc(description = "退款说明")
    private String refundRemark;
    @FieldDoc(description = "联系人姓名", requiredness = Requiredness.REQUIRED)
    private String contactUserName;
    @FieldDoc(description = "联系人电话", requiredness = Requiredness.REQUIRED)
    private String contactUserPhone;
    @FieldDoc(description = "退款方式。1：原路返回", requiredness = Requiredness.REQUIRED)
    private RefundPayTypeEnum refundPayType;
    @FieldDoc(description = "售后凭证1。最多三张，与数据表保持一致分开")
    private String certPic1;
    @FieldDoc(description = "售后凭证2。最多三张，与数据表保持一致分开")
    private String certPic2;
    @FieldDoc(description = "售后凭证3。最多三张，与数据表保持一致分开")
    private String certPic3;
    @FieldDoc(description = "用户信息", requiredness = Requiredness.REQUIRED)
    private UserDto user;

    @Override
    public void checkParameter() {
        if (refundType == null) {
            throw new InvalidParamException("退款类型不能为空");
        }
        if (refundAmount == null || refundAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("退款金额不能为空");
        }
        if (StrUtil.isBlank(refundReasonDesc)) {
            throw new InvalidParamException("退款原因不能为空");
        }
        if (StrUtil.isBlank(contactUserName)) {
            throw new InvalidParamException("联系人姓名不能为空");
        }
        if (StrUtil.isBlank(contactUserPhone)) {
            throw new InvalidParamException("联系人电话不能为空");
        }
        if (refundPayType == null) {
            throw new InvalidParamException("退款方式不能为空");
        }
        if (user == null) {
            throw new InvalidParamException("用户信息不能为空");
        }
        this.user.checkParameter();
        if (StrUtil.isNotBlank(refundRemark) && refundRemark.length() > 1000) {
            throw new InvalidParamException("退款说明不能超过1000个字符");
        }
    }


    public String getRefundAmountString() {
        return this.bigDecimal2String(this.refundAmount);
    }


    public void setRefundAmountString(String refundAmount) {
        this.refundAmount = this.string2BigDecimal(refundAmount);
    }


}
