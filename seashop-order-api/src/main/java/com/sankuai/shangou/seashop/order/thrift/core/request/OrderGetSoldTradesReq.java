package com.sankuai.shangou.seashop.order.thrift.core.request;

import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

import java.util.Date;

/**
 * 【电子面单】查询订单列表入参
 * <AUTHOR>
 */
@TypeDoc(description = "查询订单列表入参")
@Data
public class OrderGetSoldTradesReq extends BasePageReq {

    private Long shopId;
    private Date startCreated;
    private Date endCreated;
    private String status;
    private String buyerUname;
    private Integer pageNo;
    private Integer pageSize;

    public void checkParam() {
        if (startCreated != null) {
            //大于当前时间，提示“开始时间不能大于当前时间”
            if (startCreated.after(new Date())) {
                throw new IllegalArgumentException("开始时间不能大于当前时间");
            }
        }
        if (endCreated != null) {
            //结束时间不能大于开始时间
            if (endCreated.before(startCreated)) {
                throw new IllegalArgumentException("结束时间不能小于开始时间");
            }
        }
    }

}
