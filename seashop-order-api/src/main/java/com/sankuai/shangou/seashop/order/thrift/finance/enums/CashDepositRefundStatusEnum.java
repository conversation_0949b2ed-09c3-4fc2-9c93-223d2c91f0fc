package com.sankuai.shangou.seashop.order.thrift.finance.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description:
 */
@ThriftEnum
public enum CashDepositRefundStatusEnum {

    /**
     * /// <summary>
     *             /// 待审核
     *             /// </summary>
     *             [Description("待审核")]
     *             ToAudit = 0,
     *
     *             /// <summary>
     *             /// 通过
     *             /// </summary>
     *             [Description("通过")]
     *             Pass=1,
     *
     *             /// <summary>
     *             /// 拒绝
     *             /// </summary>
     *             [Description("拒绝")]
     *             Refuse = 2,
     *
     *             /// <summary>
     *             /// 退款处理中
     *             /// </summary>
     *             [Description("退款处理中")]
     *             Refunding = 3,
     *
     *             /// <summary>
     *             /// 退款失败
     *             /// </summary>
     *             [Description("退款失败")]
     *             Fail = 4,
     */

    TO_AUDIT(0, "待审核"),
    PASS(1, "通过"),
    REFUSE(2, "拒绝"),
    REFUNDING(3, "退款处理中"),
    FAIL(4, "退款失败");


    private Integer status;
    private String name;

    @ThriftEnumValue
    public Integer getStatus() {
        return status;
    }

    public String getName() {
        return name;
    }

    CashDepositRefundStatusEnum(Integer status, String name) {
        this.status = status;
        this.name = name;
    }
}
