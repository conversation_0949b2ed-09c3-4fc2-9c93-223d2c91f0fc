package com.sankuai.shangou.seashop.order.thrift.core.request.stats;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@TypeDoc(description = "统计商品销售排行前N的入参")
public class StatsShopTopNSaleProductReq extends BaseParamReq {

    @FieldDoc(description = "店铺id", requiredness = Requiredness.REQUIRED)
    private Long shopId;
    @FieldDoc(description = "前N", requiredness = Requiredness.REQUIRED)
    private Integer topN;
    @FieldDoc(description = "开始时间戳")
    private Long startTimeStamp;
    @FieldDoc(description = "结束时间戳")
    private Long endTimeStamp;

    @Override
    public void checkParameter() {
        if (shopId == null || shopId <= 0) {
            throw new InvalidParamException("店铺id不能为空");
        }
        if (topN == null || topN <= 0) {
            throw new InvalidParamException("前N不能为空");
        }
    }


}
