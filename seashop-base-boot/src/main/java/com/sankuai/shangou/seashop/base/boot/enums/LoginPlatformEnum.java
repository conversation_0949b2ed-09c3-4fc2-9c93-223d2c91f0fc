package com.sankuai.shangou.seashop.base.boot.enums;


import lombok.Getter;

/**
 * @author: cdd
 * @date: 2024/5/11/011
 * @description: 登录平台
 */
@Getter
public enum LoginPlatformEnum {
    PLATFORM_BE("platform_be", "平台后台"),
    SELLER_BE("seller_be","卖家后台"),
    BUSINESS_FE("business_fe", "商家前台")
    ;

    private String code;
    private String msg;

    private LoginPlatformEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public String getMsg() {
        return this.msg;
    }

    public static LoginPlatformEnum nameOf(String name){
        if (name == null){
            return null;
        }
        for (LoginPlatformEnum typeEnum:values()){
            if (typeEnum.name().equals(name)){
                return typeEnum;
            }
        }
        return null;
    }
}
