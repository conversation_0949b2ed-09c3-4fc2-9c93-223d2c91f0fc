package com.sankuai.shangou.seashop.base.boot.request;


import java.util.List;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;

import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class BasePageReq extends BaseParamReq {

    /**
     * 排序字段
     */
    private List<FieldSortReq> sortList;

    /**
     * 每页条数
     */
    private Integer pageSize = 10;

    /**
     * 当前页
     */
    private Integer pageNo = 1;

    public BasePageParam buildPage() {
        BasePageParam page = new BasePageParam();
        page.setPageNum(this.pageNo);
        page.setPageSize(this.pageSize);
        return page;
    }

}
