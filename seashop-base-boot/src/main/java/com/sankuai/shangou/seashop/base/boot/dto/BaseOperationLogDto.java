package com.sankuai.shangou.seashop.base.boot.dto;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

@Data
public class BaseOperationLogDto extends BaseThriftDto {
    /**
     * id
     */
    private long id;

//    @FieldDoc(description = "模块id")
    private long moduleId;

//    @FieldDoc(description = "模块名称")
    private String moduleName;

//    @FieldDoc(description = "操作类型")
    private int operationType;

//    @FieldDoc(description = "操作类型名称")
    private String operationName;

//    @FieldDoc(description = "操作时间")
    private Date operationTime;

//    @FieldDoc(description = "操作用户id")
    private long operationUserId;

//    @FieldDoc(description = "操作账号")
    private String operationUserAccount;

//    @FieldDoc(description = "操作用户名称")
    private String operationUserName;

//    @FieldDoc(description = "操作的json")
    private String operationContent;

//    @FieldDoc(description = "具体的操作功能")
    private String actionName;

//    @FieldDoc(description = "店铺id")
    private Long shopId;
}
