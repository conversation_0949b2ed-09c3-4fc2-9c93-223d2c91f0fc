package com.sankuai.shangou.seashop.base.boot.common;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.Arrays;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 分批次查询的辅助类
 * <AUTHOR>
 */
public class BatchQueryHelper {

    private final static int BATCH_SIZE = 200;


    /**
     * 批次查询
     * <p>使用场景：服务之间的RPC调用，为了对下游服务的影响尽量小，约定批量查询时下游服务的接口最大支持200个，
     * 服务之间的调用可能需要批量查询的数量不止200个，所以需要分批次，每批次最多200个进行查询</p>
     * <p>每批次查询默认为BATCH_SIZE=200个</p>
     * <p>泛型说明：E=最终返回的对象类型；P=下游需要的参数类型；C=从P里面获取的原始的批量数据</p>
     * <AUTHOR>
     * @param queryParam 批次查询的参数，包括分批次的数据以及其他查询条件。比如是根据ID批量查询，参数对象里面需要包括List<ID>
	 * @param paramResetConsumer 重置参数对象的方法。将批量数据分批次后，需要重新设置查询参数里面的批量数据，供下游服务查询
	 * @param condFunction 获取批量数据的方法。就是如何从queryParam拿到批量数据
	 * @param queryFunction 实际执行的查询方法，可以查询数据库或者调用其他服务的接口
     * java.util.List<E>
     */
    public static <E, P, C> List<E> batchQuery(P queryParam, BiConsumer<P, List<C>> paramResetConsumer,
                                               Function<P, List<C>> condFunction, Function<P, List<E>> queryFunction) {
        return batchQuery(queryParam, BATCH_SIZE, paramResetConsumer, condFunction, queryFunction);
    }

    /**
     * 批次查询
     * <p>使用场景：服务之间的RPC调用，为了对下游服务的影响尽量小，约定批量查询时下游服务的接口最大支持200个，
     * 服务之间的调用可能需要批量查询的数量不止200个，所以需要分批次，每批次最多200个进行查询</p>
     * <p>每批次查询默认为BATCH_SIZE=200个</p>
     * <p>泛型说明：E=最终返回的对象类型；P=下游需要的参数类型；C=从P里面获取的原始的批量数据</p>
     * <AUTHOR>
     * @param queryParam 批次查询的参数，包括分批次的数据以及其他查询条件。比如是根据ID批量查询，参数对象里面需要包括List<ID>
     * @param batchSize 每批次查询的数量
     * @param paramResetConsumer 重置参数对象的方法。将批量数据分批次后，需要重新设置查询参数里面的批量数据，供下游服务查询
     * @param condFunction 获取批量数据的方法。就是如何从queryParam拿到批量数据
     * @param queryFunction 实际执行的查询方法，可以查询数据库或者调用其他服务的接口
     * java.util.List<E>
     */
    public static <E, P, C> List<E> batchQuery(P queryParam, int batchSize, BiConsumer<P, List<C>> paramResetConsumer,
                                               Function<P, List<C>> condFunction, Function<P, List<E>> queryFunction) {
        List<C> batchList;
        if (queryParam == null || condFunction == null ||
                (batchList = condFunction.apply(queryParam)) == null || batchList.isEmpty()) {
            return null;
        }
        int size = batchList.size();
        if (size <= batchSize) {
            return queryFunction.apply(queryParam);
        }
        int mode = size % batchSize;
        int batchCount = size / batchSize;
        if (mode != 0) {
            batchCount = batchCount + 1;
        }
        List<E> resultList = null;
        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * batchSize;
            int toIndex = (i + 1) * batchSize;
            if (toIndex > size) {
                toIndex = size;
            }
            List<C> subList = batchList.subList(fromIndex, toIndex);
            paramResetConsumer.accept(queryParam, subList);
            List<E> list = queryFunction.apply(queryParam);
            if (list != null && !list.isEmpty()) {
                if (resultList == null) {
                    resultList = list;
                } else {
                    resultList.addAll(list);
                }
            }
        }
        return resultList;
    }

    public static <E> void batchInvoke(List<E> dataList, int batchSize, Consumer<List<E>> invokeConsumer) {
        if (dataList == null || dataList.isEmpty()) {
            return;
        }
        int size = dataList.size();
        if (size <= batchSize) {
            invokeConsumer.accept(dataList);
            return;
        }
        int mode = size % batchSize;
        int batchCount = size / batchSize;
        if (mode != 0) {
            batchCount = batchCount + 1;
        }
        for (int i = 0; i < batchCount; i++) {
            int fromIndex = i * batchSize;
            int toIndex = (i + 1) * batchSize;
            if (toIndex > size) {
                toIndex = size;
            }
            List<E> subList = dataList.subList(fromIndex, toIndex);
            invokeConsumer.accept(subList);
        }
    }


    public static void main(String[] args) {
        TestParam testParam = new TestParam();
        testParam.setName("hello");
        testParam.setIdList(Arrays.asList(1L, 2L, 3L, 4L, 5L, 6L, 7L));
        List<String> result = BatchQueryHelper.batchQuery(testParam, TestParam::setIdList, TestParam::getIdList, BatchQueryHelper::printMethod);
        System.out.println(String.join(",", result));
    }

    @Getter
    @Setter
    static class TestParam {

        private List<Long> idList;
        private String name;
    }

    public static List<String> printMethod(TestParam param) {
        List<Long> idList = param.getIdList();
        String name = param.getName();
        return idList.stream()
                .map(id -> name + id)
                .collect(Collectors.toList());
    }

    public static void testInvoke(List<Long> idList) {
        System.out.println("idList: " + JsonUtil.toJsonString(idList));
    }

}
