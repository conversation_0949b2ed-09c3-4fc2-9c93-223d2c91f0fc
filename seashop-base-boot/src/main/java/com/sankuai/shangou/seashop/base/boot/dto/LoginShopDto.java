package com.sankuai.shangou.seashop.base.boot.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 卖家信息dto
 * @author: LXH
 **/
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class LoginShopDto extends LoginBaseDto {
//    @FieldDoc(description = "管理员ID")
    private Long managerId;
//    @FieldDoc(description = "商家ID")
    private Long userId;
//    @FieldDoc(description = "管理员名称")
    private String managerName;
//    @FieldDoc(description = "商家名称")
    private String userName;
//    @FieldDoc(description = "店铺ID")
    private Long shopId;
//    @FieldDoc(description = "商家用户角色标识集合")
    private List<Long> roles;
//    @FieldDoc(description = "商家用户权限url集合")
    private List<String> privileges;
}
