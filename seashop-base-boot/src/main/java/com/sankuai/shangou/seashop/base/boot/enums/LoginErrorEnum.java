package com.sankuai.shangou.seashop.base.boot.enums;

import lombok.Getter;

/**
 * @author: cdd
 * @date: 2024/5/11/011
 * @description: 登录异常枚举
 */
@Getter
public enum LoginErrorEnum implements Code  {
    SUCCESS(0, "登录成功"),
    LOGIN_FAIL(********, "账号密码错误"),
    INVALID_TOKEN(********, "对不起,您没有权限操作"),
    NO_LOGIN(********, "未登录，请先登录"),
    PROHIBIT_LOGIN(********, "用户已禁用"),
    ACCOUNT_FROZEN(********, "用户已冻结"),
    KICKED(********, "您被踢下线了，请重新登录"),
    FAILED(********, "登录失败,请联系管理员"),
    NOT_SELLER(********, "对不起,您还不是卖家!"),
    // 店铺未启用
    SHOP_NOT_ENABLE(********, "对不起,您的店铺暂未启用!"),
    //失败次数太多，开启安全认证
    FAILED_TOO_MANY_TIMES(********, "登录失败次数过多，请稍后再试"),
    //人机验证失败
    FAILED_MACHINE_VERIFY(********, "人机验证失败"),
    ;

    private int code;
    private String msg;

    private LoginErrorEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @Override
    public Integer value() {
        return this.code;
    }

    @Override
    public String desc() {
        return this.msg;
    }
}
