package com.sankuai.shangou.seashop.base.boot.request;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description:
 */
@Data
public class BaseLoginParamReq extends BaseParamReq {
    /**
     * 登录信息
     */
    private LoginBaseDto loginBaseDto;

    public void setLoginBaseDto(LoginBaseDto loginBaseDto) {
        this.loginBaseDto = loginBaseDto;
    }
    /**
     * 参数初始化
     */
    public void valueInit() {
    }

    /**
     * 参数校验
     */
    public void checkParameter() {
    }
}
