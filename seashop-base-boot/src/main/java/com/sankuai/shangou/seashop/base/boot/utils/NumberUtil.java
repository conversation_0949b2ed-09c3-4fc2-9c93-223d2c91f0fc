package com.sankuai.shangou.seashop.base.boot.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 数值相关工具类
 * <AUTHOR>
 */
public class NumberUtil {

    public static BigDecimal multiply(BigDecimal a, BigDecimal b) {
        if (a == null || b == null) {
            return BigDecimal.ZERO;
        }
        return a.multiply(b).setScale(2, RoundingMode.HALF_UP);
    }

    public static BigDecimal multiply(BigDecimal a, Integer b) {
        if (a == null || b == null) {
            return BigDecimal.ZERO;
        }
        return a.multiply(new BigDecimal(b)).setScale(2, RoundingMode.HALF_UP);
    }

    public static BigDecimal multiply(BigDecimal a, Long b) {
        if (a == null || b == null) {
            return BigDecimal.ZERO;
        }
        return a.multiply(new BigDecimal(b)).setScale(2, RoundingMode.HALF_UP);
    }

    public static BigDecimal nullToZero(BigDecimal a) {
        return cn.hutool.core.util.NumberUtil.nullToZero(a);
    }

}
