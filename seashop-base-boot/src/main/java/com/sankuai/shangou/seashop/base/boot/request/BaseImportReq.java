package com.sankuai.shangou.seashop.base.boot.request;

import lombok.Data;
import org.springframework.util.StringUtils;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/21 22:26
 */
@ToString
@Data
public class BaseImportReq extends BaseParamReq {

    /**
     * 文件路径
     */
    private String filePath;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(StringUtils.isEmpty(filePath), "文件路径不能为空");
    }
}
