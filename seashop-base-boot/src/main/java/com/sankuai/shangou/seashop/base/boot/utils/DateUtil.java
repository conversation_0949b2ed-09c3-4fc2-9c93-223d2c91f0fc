package com.sankuai.shangou.seashop.base.boot.utils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * 日期工具类
 * <p>由于SimpleDateFormat线程不安全，这里采用将Date转换成LocalDatetime处理</p>
 * <AUTHOR>
 */
public class DateUtil {

    public static final String DEFAULT_PATTERN = "yyyy-MM-dd HH:mm:ss";
    public static final String HOUR_PATTERN = "yyyyMMddHH";

    private final static DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern(DEFAULT_PATTERN);
    private final static DateTimeFormatter HOUR_FORMATTER = DateTimeFormatter.ofPattern(HOUR_PATTERN);

    public static String format(Date date) {
        LocalDateTime localDateTime = convertDate(date);
        return DEFAULT_FORMATTER.format(localDateTime);
    }

    public static String format(Date date, String pattern) {
        LocalDateTime localDateTime = convertDate(date);
        return DateTimeFormatter.ofPattern(pattern).format(localDateTime);
    }

    public static String formatHour(Date date) {
        LocalDateTime localDateTime = convertDate(date);
        return HOUR_FORMATTER.format(localDateTime);
    }

    public static LocalDateTime convertDate(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

}
