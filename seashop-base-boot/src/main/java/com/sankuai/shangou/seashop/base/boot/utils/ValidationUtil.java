package com.sankuai.shangou.seashop.base.boot.utils;

import java.util.regex.Pattern;

import com.sankuai.shangou.seashop.base.boot.constant.RegexConstant;

/**
 * 基础校验工具类
 *
 * <AUTHOR>
 */
public class ValidationUtil {

    static class PatternPool {
        /**
         * 手机号码正则表达式
         */
        public final static Pattern PHONE_REGEX_PATTERN = Pattern.compile(RegexConstant.PHONE_REGEX);

        /**
         * 正整数正则表达式
         */
        public final static Pattern POSITIVE_INTEGER_REGEX_PATTERN = Pattern.compile(RegexConstant.POSITIVE_INTEGER_REGEX);

        /**
         * 非负整数正则表达式
         */
        public final static Pattern NON_NEGATIVE_INTEGER_REGEX_PATTERN = Pattern.compile(RegexConstant.NON_NEGATIVE_INTEGER_REGEX);

        /**
         * 金额校验正则表达式, 必须大于0, 最多两位小数
         */
        public final static Pattern AMOUNT_REGEX_PATTERN = Pattern.compile(RegexConstant.AMOUNT_REGEX);
    }

    /**
     * 校验输入的字符串是否是符合系统规范的手机号码
     *
     * @param phoneStr 待校验的手机号码
     * <AUTHOR>
     */
    public static boolean isPhone(String phoneStr) {
        return isMatchRegex(PatternPool.PHONE_REGEX_PATTERN, phoneStr);
    }

    /**
     * 校验输入的字符串是否是符合系统规范的正整数
     *
     * @param str 待校验的字符串
     * @return 是否是正整数
     */
    public static boolean isPositiveInteger(String str) {
        return isMatchRegex(PatternPool.POSITIVE_INTEGER_REGEX_PATTERN, str);
    }

    /**
     * 校验输入的字符串是否是符合系统规范的非负整数
     *
     * @param str 待校验的字符串
     * @return 是否是非负整数
     */
    public static boolean isNonNegativeInteger(String str) {
        return isMatchRegex(PatternPool.NON_NEGATIVE_INTEGER_REGEX_PATTERN, str);
    }

    /**
     * 校验输入的字符串是否是符合系统规范的金额
     *
     * @param str 待校验的字符串
     * @return 是否是金额
     */
    public static boolean isAmount(String str) {
        return isMatchRegex(PatternPool.AMOUNT_REGEX_PATTERN, str);
    }


    /**
     * 通过正则表达式验证
     *
     * @param pattern 正则模式
     * @param value   值
     * @return 是否匹配正则
     */
    public static boolean isMatchRegex(Pattern pattern, CharSequence value) {
        return isMatch(pattern, value);
    }

    /**
     * 给定内容是否匹配正则
     *
     * @param pattern 模式
     * @param content 内容
     * @return 正则为null或者""则不检查，返回true，内容为null返回false
     */
    public static boolean isMatch(Pattern pattern, CharSequence content) {
        if (content == null || pattern == null) {
            // 提供null的字符串为不匹配
            return false;
        }
        return pattern.matcher(content).matches();
    }

}
