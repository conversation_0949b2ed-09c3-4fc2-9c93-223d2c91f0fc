package com.sankuai.shangou.seashop.base.boot.utils;

/**
 * <AUTHOR>
 */
public class PageUtil {


    /**
     * 根据页码和每页大小计算当前页 开始 索引位置
     *
     * @param pageNo   页码（从1计数）
     * @param pageSize 每页条目数
     * @return 开始位置
     */
    public static int getStart(int pageNo, int pageSize) {
        if (pageNo < 1) {
            pageNo = 1;
        }

        if (pageSize < 1) {
            pageSize = 0;
        }

        return (pageNo - 1) * pageSize;
    }

    /**
     * 根据页码和每页大小计算当前页 结束 索引位置
     *
     * @param pageNo   页码（从1计数）
     * @param pageSize 每页条目数
     * @return 开始位置
     */
    public static int getEnd(int pageNo, int pageSize) {
        final int start = getStart(pageNo, pageSize);
        return getEndByStart(start, pageSize);
    }

    /**
     * 根据起始位置获取结束位置
     *
     * @param start    起始位置
     * @param pageSize 每页条目数
     * @return 结束位置
     */
    private static int getEndByStart(int start, int pageSize) {
        if (pageSize < 1) {
            pageSize = 0;
        }
        return start + pageSize;
    }

    /**
     * 根据总数计算总页数
     *
     * @param totalCount 总数
     * @param pageSize   每页数
     * @return 总页数
     */
    public static int totalPage(int totalCount, int pageSize) {
        return totalPage((long) totalCount,pageSize);
    }

    /**
     * 根据总数计算总页数
     *
     * @param totalCount 总数
     * @param pageSize   每页数
     * @return 总页数
     * @since 5.8.5
     */
    public static int totalPage(long totalCount, int pageSize) {
        if (pageSize == 0) {
            return 0;
        }
        return Math.toIntExact(totalCount % pageSize == 0 ? (totalCount / pageSize) : (totalCount / pageSize + 1));
    }

    public static void main(String[] args) {
        System.out.println(getStart(1, 10));
        System.out.println(getEnd(1, 10));
        System.out.println(getStart(2, 10));
        System.out.println(getEnd(2, 10));
    }

}
