package com.sankuai.shangou.seashop.base.boot.utils;

import com.sankuai.shangou.seashop.base.boot.enums.Code;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;

import java.util.Objects;

/**
 * @description: 断言工具类
 * @author: LXH
 **/
public class AssertUtil {
    public static void throwIfTrue(boolean expression, String message) {
        if (expression) {
            throw new BusinessException(message);
        }
    }

    public static void throwIfTrue(boolean expression, Code message) {
        if (expression) {
            throw new BusinessException(message.value(), message.desc());
        }
    }

    public static void throwIfTrue(boolean expression, Code message, String... params) {
        if (expression) {
            throw new BusinessException(message.value(), String.format(message.desc(), params));
        }
    }

    public static void throwIfNull(Object object, String message) {
        if (object == null) {
            throw new BusinessException(message);
        }
    }

    public static void throwIfNull(Object object, Code message) {
        if (object == null) {
            throw new BusinessException(message.value(), message.desc());
        }
    }

    public static void throwIfNull(Object object, Code message, String... params) {
        if (object == null) {
            throw new BusinessException(message.value(), String.format(message.desc(), params));
        }
    }

    public static void throwInvalidParamIfTrue(boolean expression, String message) {
        if (expression) {
            throw new InvalidParamException(message);
        }
    }

    public static void throwInvalidParamIfNull(Object object, String message) {
        if (object == null) {
            throw new InvalidParamException(message);
        }
    }

    public static void throwIfNotEquals(Object object1, Object object2, String message){
        if (!Objects.equals(object1, object2)){
            throw new InvalidParamException(message);
        }
    }

}
