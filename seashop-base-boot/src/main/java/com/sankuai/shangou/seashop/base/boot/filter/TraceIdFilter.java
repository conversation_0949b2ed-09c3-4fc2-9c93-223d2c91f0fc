package com.sankuai.shangou.seashop.base.boot.filter;

import org.slf4j.MDC;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname TraceIdFilter
 * Description 日志traceId过滤
 * @date 2023/3/11 12:33
 */

@Order(1)
@WebFilter(urlPatterns = "/*", filterName = "traceIdFilter")
@Configuration
@ConditionalOnClass(Filter.class)
public class TraceIdFilter implements Filter {

    public final static String MDC_TRACE_ID = "traceId";

    @Override
    public void doFilter(ServletRequest request, ServletResponse response, Filter<PERSON>hain chain) throws IOException, ServletException {
        HttpServletResponse httpResponse = (HttpServletResponse) response;
        httpResponse.setHeader(MDC_TRACE_ID, MDC.get(MDC_TRACE_ID));
        chain.doFilter(request, response);
    }


}
