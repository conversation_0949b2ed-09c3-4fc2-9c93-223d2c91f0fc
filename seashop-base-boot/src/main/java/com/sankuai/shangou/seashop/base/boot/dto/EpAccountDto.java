package com.sankuai.shangou.seashop.base.boot.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/

@NoArgsConstructor
@AllArgsConstructor
@Data
public class EpAccountDto extends BaseAccountDto {
//    @FieldDoc(description = "状态")
    private int status;
//    @FieldDoc(description = "创建时间")
    private long createTime;
//    @FieldDoc(description = "修改时间")
    private long modifyTime;
//    @FieldDoc(description = "手机号")
    private String phone;
//    @FieldDoc(description = "国际区号")
    private String interCode;
//    @FieldDoc(description = "联系人")
    private String contactName;
//    @FieldDoc(description = "部门ID")
    private int dpID;
//    @FieldDoc(description = "部门类型")
    private int partType;
//    @FieldDoc(description = "部门key")
    private String partKey;
//    @FieldDoc(description = "邮箱")
    private String email;
//    @FieldDoc(description = "联系电话")
    private String contactPhone;
//    @FieldDoc(description = "联系电话国际区号")
    private String contactPhoneInterCode;
//    @FieldDoc(description = "最后登录时间")
    private int lastLoginTime;
//    @FieldDoc(description = "类型")
    private int type;
//    @FieldDoc(description = "手机token")
    private String phoneToken;
//    @FieldDoc(description = "手机密文")
    private String phoneCipher;

}