package com.sankuai.shangou.seashop.base.boot.response;

import java.util.List;

import lombok.Data;
import lombok.ToString;

/**
 * 公共的分页返回对象
 * <AUTHOR>
 * @date 2023/11/07
 */
@ToString
@Data
public class BasePageResp<T> {

//    @FieldDoc(
//            description = "每页条数"
//    )
    private Integer pageSize;

//    @FieldDoc(
//            description = "当前页"
//    )
    private Integer pageNo;

//    @FieldDoc(
//            description = "总页数"
//    )
    private Integer pages;

//    @FieldDoc(
//            description = "总条数"
//    )
    private Long totalCount;

//    @FieldDoc(
//            description = "数据"
//    )
    private List<T> data;

//    @FieldDoc(
//            description = "滚动Id"
//    )
    private String scrollId;

    public BasePageResp() {
    }

    public BasePageResp(Integer pageSize, Integer pageNo, Integer pages, Long totalCount, List<T> data) {
        this.pageSize = pageSize;
        this.pageNo = pageNo;
        this.pages = pages;
        this.totalCount = totalCount;
        this.data = data;
    }

    public BasePageResp(Integer pageSize, Integer pageNo, Integer pages, Long totalCount, List<T> data, String scrollId) {
        this.pageSize = pageSize;
        this.pageNo = pageNo;
        this.pages = pages;
        this.totalCount = totalCount;
        this.data = data;
        this.scrollId = scrollId;
    }
}
