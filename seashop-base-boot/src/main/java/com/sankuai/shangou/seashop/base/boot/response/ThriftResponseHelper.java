package com.sankuai.shangou.seashop.base.boot.response;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.enums.TResultCode;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Function;

/**
 * <AUTHOR>
 * @since 2021/09/05
 */
@Slf4j
public class ThriftResponseHelper {

    private static final JSONConfig jsonConfig = JSONConfig.create().setIgnoreNullValue(false);
    private ThriftResponseHelper() {
    }

    public static <Req, Rsp> ResultDto<Rsp> responseInvoke(String logPrefix, Req req, Function<Req, Rsp> function) {
        ResultDto<Rsp> result = new ResultDto<>();
        result.setCode(TResultCode.SERVER_ERROR.value());
        try {
            log.info("{} 请求入参. request={}", logPrefix, JSONUtil.toJsonStr(req, jsonConfig));
            result.setData(function.apply(req));
            result.setCode(TResultCode.SUCCESS.value());
            log.info("{} success. 请求结果. response={}", logPrefix, JSONUtil.toJsonStr(result, jsonConfig));
        } catch (IllegalArgumentException e) {
            log.info("{} illegal argument error. request={}", logPrefix, req, e);
            result.fail(TResultCode.BAD_REQUEST.value(), e.getMessage());
            return result;
        } catch (InvalidParamException e) {
            log.info("{} invalid param error. request={}", logPrefix, req, e);
            result.fail(TResultCode.BAD_REQUEST.value(), e.getMessage());
            return result;
        } catch (IllegalStateException e) {
            log.warn("{} illegal state error. request={}", logPrefix, req, e);
            result.fail(TResultCode.SERVER_ERROR.value(), e.getMessage());
            return result;
        } catch (BusinessException e) {
            log.warn("{} business error. request={}", logPrefix, req, e);
            result.fail(e.getCode(), e.getMessage());
            return result;
        } catch (Exception e) {
            log.error("{} failed. request={}", logPrefix, req, e);
            String msg = e.getMessage();
            result.fail(TResultCode.SERVER_ERROR.value(), StrUtil.isBlank(msg) ? "服务端异常" : msg);
            return result;
        }
        return result;
    }

    public static <Rsp> Rsp executeThriftCall(ThrowingSupplier<ResultDto<Rsp>> thriftCall) {
        ResultDto<Rsp> result = null;
        try {
            result = thriftCall.get();
        } catch (InvalidParamException e) {
            log.warn("business error", e);
            throw new InvalidParamException(e.getMessage());
        } catch (BusinessException e) {
            log.warn("business error", e);
            throw new BusinessException(e.getCode(), e.getMessage());
        } catch (Exception e) {
            log.error("thrift call failed", e);
            throw new BusinessException(TResultCode.SERVER_ERROR.value(), "服务端异常");
        }
        if (result == null) {
            throw new BusinessException(TResultCode.SERVER_ERROR.value(), "服务端异常，响应结果为空");
        }
        if (!TResultCode.SUCCESS.value().equals(result.getCode())) {
            throw new BusinessException(result.getCode(), result.getMessage());
        }
        return result.getData();
    }


}