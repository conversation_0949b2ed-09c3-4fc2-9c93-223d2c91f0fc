package com.sankuai.shangou.seashop.base.boot.common;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
public class BaseThriftDto implements Serializable {
    public BaseThriftDto() {
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this);
    }

    protected Date long2Date(Long date) {
        return date == null ? null : new Date(date);
    }

    protected Long date2Long(Date date) {
        return date == null ? null : date.getTime();
    }

    protected String bigDecimal2String(BigDecimal bigDecimal) {
        return bigDecimal == null ? null : bigDecimal.toString();
    }

    protected BigDecimal string2BigDecimal(String str) {
        return StringUtils.isEmpty(str) ? null : new BigDecimal(str);
    }


}
