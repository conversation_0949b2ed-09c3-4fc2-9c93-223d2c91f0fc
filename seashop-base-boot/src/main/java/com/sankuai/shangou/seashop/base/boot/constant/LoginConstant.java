package com.sankuai.shangou.seashop.base.boot.constant;

/**
 * @description:
 * @author: LXH
 **/
public class LoginConstant {
//    public static final String LOGIN_SSO_USER = "login_sso_user";
//    public static final String LOGIN_EP_MEMBER = "login_ep_member";
//    public static final String LOGIN_EP_SHOP = "login_ep_shop";
//    public static final String LOGIN_FORCE = "login_force";
//
//    public static final String SSO_USER_KEY = "sso.user"; // sso用户信息key
//    public static final String EP_USER_KEY = "shepherd.epassport.account"; // sso用户信息key
//    public static final String PATH_KEY = "MTRootMethod"; // 接口uri key
//    public static final String CLIENT_IP = "c-client-ip"; // 接口ip key
//    public static final String DOMAIN = "c-domain"; // 接口域名 key
//    public static final String USER_AGENT = "User-Agent"; // 接口UA key
    /**
     * 登录key前缀
     */
    public static final String LOGIN_KEY_PREFIX = "login:";
    /**
     * 登录token key
     */
    public static final String TOKEN_KEY = "userToken:";
    /**
     * 登录用户信息 key
     */
    public static final String USER_INFO_KEY = "userInfo:";
    /**
     * token跟用户信息绑定 key
     */
    public static final String BIND_TOKEN_KEY = "userBindToken:";

    /**
     * Token风格: uuid 带下划线
     */
    public static final String TOKEN_STYLE_UUID = "uuid";

    /**
     * Token风格: 简单uuid (不带下划线)
     */
    public static final String TOKEN_STYLE_SIMPLE_UUID = "simple-uuid";

    /**
     * Token风格: 32位随机字符串
     */
    public static final String TOKEN_STYLE_RANDOM_32 = "random-32";

    /**
     * Token风格: 64位随机字符串
     */
    public static final String TOKEN_STYLE_RANDOM_64 = "random-64";

    /**
     * Token风格: 128位随机字符串
     */
    public static final String TOKEN_STYLE_RANDOM_128 = "random-128";

    /**
     * 是否被踢 0 未被踢 -1 被踢
     */
    public static final String NO_KICKED = "0";
    public static final String KICKED = "-1";

    /**
     * 微信授权后自动注册会员默认密码
     */
    public static final String DEFAULT_PASSWORD = "123456";

    /**
     * 自动注册后用户昵称前缀
     */
    public static final String DEFAULT_USER_NAME_PREFIX = "会员";

    /**
     * 登录失败次数
     */
    public static final String LOGIN_FAIL_KEY = "fail:";
}
