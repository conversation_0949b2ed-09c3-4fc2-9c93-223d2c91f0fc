package com.sankuai.shangou.seashop.base.boot.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;

import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.google.common.collect.Lists;

/**
 * MybatisPlus工具类
 *
 * <AUTHOR>
 * @date 2023/7/5
 */
public class MybatisUtil {

    private final static String SORT_SQL_TEMPLATE = " %s %s,";

    private final static String EMPTY = "";
    private final static String ASC = "ASC";
    private final static String DESC = "DESC";
    private final static Integer DEFAULT_BATCH_SIZE = 200;


    /**
     * 获取 通用排序sql
     *
     * @param sortField 排序字段
     * @return 拼接后的sql
     */
    public static String getOrderSql(LinkedHashMap<String, Boolean> sortField) {
        if (sortField != null && !sortField.isEmpty()) {
            StringBuilder sqlBuilder = new StringBuilder();
            sortField.forEach((key, isAsc) -> {
                sqlBuilder.append(String.format(SORT_SQL_TEMPLATE, key, isAsc ? ASC : DESC));
            });
            String sql = sqlBuilder.toString();
            return sql.substring(0, sql.length() - 1);
        }
        return EMPTY;
    }

    /**
     * 获取排序sql
     *
     * @param sortList 排序列表
     * @param mapping  字段映射关系
     * @return 拼接后的sql
     */
    public static String getOrderSql(List<FieldSortReq> sortList, Map<String, String> mapping) {
        if (CollectionUtils.isEmpty(sortList)) {
            return EMPTY;
        }
        LinkedHashMap<String, Boolean> sortField = new LinkedHashMap<>();
        sortList.forEach(sortItem -> {
            String sort = sortItem.getSort();
            String mappingSort = mapping.get(sort);
            sortField.put(StringUtils.isEmpty(mappingSort) ? sort : mappingSort, sortItem.getIzAsc());
        });
        return getOrderSql(sortField);
    }

    /**
     * 获取排序sql
     *
     * @param sortList 排序列表
     * @return 拼接后的sql
     */
    public static String getOrderSql(List<FieldSortReq> sortList) {
        return getOrderSql(sortList, new HashMap<>());
    }


    /**
     * 分批查询
     *
     * @param queryFun  查询方法
     * @param ids       查询的ids
     * @param batchSize 每批次查询的数量
     * @param <T>
     * @param <R>
     * @return 查询结果
     */
    public static <T, R> List<R> queryBatch(Function<List<T>, List<R>> queryFun, List<T> ids, int batchSize) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.EMPTY_LIST;
        }

        // 将ids 根据50个一组分组
        List<List<T>> subIdsArr = Lists.partition(ids, batchSize);
        List<R> result = new ArrayList<>();
        for (List<T> subIds : subIdsArr) {
            // 根据ids 查询数据
            result.addAll(queryFun.apply(subIds));
        }
        return result;
    }

    /**
     * 分批查询
     *
     * @param queryFun 查询方法
     * @param ids      查询的ids
     * @param <T>
     * @param <R>
     * @return 查询结果
     */
    public static <T, R> List<R> queryBatch(Function<List<T>, List<R>> queryFun, List<T> ids) {
        return queryBatch(queryFun, ids, DEFAULT_BATCH_SIZE);
    }

    /**
     * 分批统计
     *
     * @param countFun
     * @param ids
     * @param batchSize
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T> Long countBatch(Function<List<T>, Long> countFun, List<T> ids, int batchSize) {
        if (CollectionUtils.isEmpty(ids)) {
            return 0L;
        }

        // 将ids 根据50个一组分组
        List<List<T>> subIdsArr = Lists.partition(ids, batchSize);
        Long count = 0L;
        for (List<T> subIds : subIdsArr) {
            // 根据ids 查询数据
            count += countFun.apply(subIds);
        }
        return count;
    }

    /**
     * 分批统计
     *
     * @param countFun
     * @param ids
     * @param <T>
     * @param <R>
     * @return
     */
    public static <T, R> Long countBatch(Function<List<T>, Long> countFun, List<T> ids) {
        return countBatch(countFun, ids, DEFAULT_BATCH_SIZE);
    }

    /**
     * 分批执行
     *
     * @param executeFun 执行方法
     * @param ids        查询的ids
     * @param batchSize  每批次查询的数量
     * @param <T>
     */
    public static <T> void executeBatch(Consumer<List<T>> executeFun, List<T> ids, int batchSize) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        // 将ids 根据50个一组分组
        List<List<T>> subIdsArr = Lists.partition(ids, batchSize);
        for (List<T> subIds : subIdsArr) {
            executeFun.accept(subIds);
        }
    }

    /**
     * 分批执行
     *
     * @param executeFun 执行方法
     * @param ids        查询的ids
     * @param <T>
     */
    public static <T> void executeBatch(Consumer<List<T>> executeFun, List<T> ids) {
        executeBatch(executeFun, ids, DEFAULT_BATCH_SIZE);
    }
}
