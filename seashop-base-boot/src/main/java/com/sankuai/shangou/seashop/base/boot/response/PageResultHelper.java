package com.sankuai.shangou.seashop.base.boot.response;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;

/**
 * <AUTHOR>
 * @date 2023/6/19
 */
public class PageResultHelper {


    /**
     * 返回默认的空数据。分页数据按照入参返回。列表返回大小为0的空集合
     *
     * <AUTHOR>
     * @date 2023/3/16
     */
    public static <D, R> BasePageResp<R> defaultEmpty(Page<D> pageResult) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(0L);
        result.setPageNo(pageResult.getPageNum());
        result.setPageSize(pageResult.getPageSize());
        result.setPages(pageResult.getPages());
        result.setData(Collections.EMPTY_LIST);
        return result;
    }

    /**
     * 返回默认的空数据。分页数据按照入参返回。列表返回大小为0的空集合
     *
     * <AUTHOR>
     * @date 2023/3/16
     */
    public static <R> BasePageResp<R> defaultEmpty(BasePageReq pageReq) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(0L);
        result.setPageNo(pageReq.getPageNo());
        result.setPageSize(pageReq.getPageSize());
        result.setPages(0);
        result.setData(Collections.EMPTY_LIST);
        return result;
    }

    /**
     * 返回默认的空数据。分页数据按照入参返回。列表返回大小为0的空集合
     *
     * <AUTHOR>
     * @date 2023/3/16
     */
    public static <R> BasePageResp<R> defaultEmpty(BasePageParam pageReq) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(0L);
        result.setPageNo(pageReq.getPageNum());
        result.setPageSize(pageReq.getPageSize());
        result.setPages(0);
        result.setData(Collections.EMPTY_LIST);
        return result;
    }

    /**
     * 将DB层的mybatis分页对象转换为系统的分页相应对象
     *
     * @param fromPage  业务层的分页对象
     * @param resultClz 接口需要返回的类型
     * <AUTHOR>
     * @date 2023/3/16
     */
    public static <D, R> BasePageResp<R> transfer(Page<D> fromPage, Class<R> resultClz) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(fromPage.getTotal());
        result.setPageNo(fromPage.getPageNum());
        result.setPageSize(fromPage.getPageSize());
        result.setPages(fromPage.getPages());
        if (fromPage.getResult() == null || fromPage.getResult().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            result.setData(JsonUtil.copyList(fromPage.getResult(), resultClz));
        }
        return result;
    }

    /**
     * 将DB层的mybatis分页对象转换为系统的分页相应对象，并支持对相应对象做基础的数据转换
     *
     * @param fromPage        业务层的分页对象
     * @param resultClz       接口需要返回的类型
     * @param convertConsumer 对数据进行转换的consumer
     * <AUTHOR>
     * @date 2023/3/16
     */
    public static <D, R> BasePageResp<R> transfer(Page<D> fromPage, Class<R> resultClz, Consumer<R> convertConsumer) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(fromPage.getTotal());
        result.setPageNo(fromPage.getPageNum());
        result.setPageSize(fromPage.getPageSize());
        result.setPages(fromPage.getPages());
        if (fromPage.getResult() == null || fromPage.getResult().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            List<R> list = JsonUtil.copyList(fromPage.getResult(), resultClz);
            list.forEach(x -> convertConsumer.accept(x));
            result.setData(list);
        }
        return result;
    }


    /**
     * 将DB层的mybatis分页对象转换为系统的分页相应对象，并支持对相应对象做基础的数据转换
     *
     * @param dbPage          业务层的分页对象
     * @param resultClz       接口需要返回的类型
     * @param convertConsumer 对数据进行转换的consumer
     * @param <D>
     * @param <R>
     * @return
     */
    public static <D, R> BasePageResp<R> transfer(Page<D> dbPage, Class<R> resultClz, BiConsumer<D, R> convertConsumer) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(dbPage.getTotal());
        result.setPageNo(dbPage.getPageNum());
        result.setPageSize(dbPage.getPageSize());
        result.setPages(dbPage.getPages());
        if (dbPage.getResult() == null || dbPage.getResult().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            List<R> list = new ArrayList<>();
            for (D dClass : dbPage.getResult()) {
                R rClass = JsonUtil.copy(dClass, resultClz);
                convertConsumer.accept(dClass, rClass);
                list.add(rClass);
            }
            result.setData(list);
        }
        return result;
    }

    /**
     * 将DB层的mybatis分页对象转换为系统的分页相应对象，并支持对相应对象做基础的数据转换
     *
     * @param fromPage        业务层的分页对象
     * @param convertFunction 自定义转换方法
     * @param <D>
     * @param <R>
     * @return
     */
    public static <D, R> BasePageResp<R> transfer(Page<D> fromPage, Function<D, R> convertFunction) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(fromPage.getTotal());
        result.setPageNo(fromPage.getPageNum());
        result.setPageSize(fromPage.getPageSize());
        result.setPages(fromPage.getPages());
        if (fromPage.getResult() == null || fromPage.getResult().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            List<R> list = new ArrayList<>();
            for (D dClass : fromPage.getResult()) {
                list.add(convertFunction.apply(dClass));
            }
            result.setData(list);
        }
        return result;
    }

    /**
     * 将业务层的分页对象转换为外观的分页相应对象
     *
     * @param fromPage  业务层的分页对象
     * @param resultClz 接口需要返回的类型
     * <AUTHOR>
     * @date 2023/3/16
     */
    public static <D, R> BasePageResp<R> transfer(BasePageResp<D> fromPage, Class<R> resultClz) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(fromPage.getTotalCount());
        result.setPageNo(fromPage.getPageNo());
        result.setPageSize(fromPage.getPageSize());
        result.setPages(fromPage.getPages());
        result.setScrollId(fromPage.getScrollId());
        if (fromPage.getData() == null || fromPage.getData().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            List<R> list = JsonUtil.copyList(fromPage.getData(), resultClz);
            result.setData(list);
        }
        return result;
    }

    /**
     * 将业务层的分页对象转换为外观的分页相应对象
     *
     * @param fromPage        业务层的分页对象
     * @param resultClz       接口需要返回的类型
     * @param convertConsumer 对数据进行转换的consumer
     * <AUTHOR>
     * @date 2023/3/16
     */
    public static <D, R> BasePageResp<R> transfer(BasePageResp<D> fromPage, Class<R> resultClz, Consumer<R> convertConsumer) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(fromPage.getTotalCount());
        result.setPageNo(fromPage.getPageNo());
        result.setPageSize(fromPage.getPageSize());
        result.setPages(fromPage.getPages());
        result.setScrollId(fromPage.getScrollId());
        if (fromPage.getData() == null || fromPage.getData().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            List<R> list = JsonUtil.copyList(fromPage.getData(), resultClz);
            list.forEach(x -> convertConsumer.accept(x));
            result.setData(list);
        }
        return result;
    }

    /**
     * 将业务层的分页对象转换为外观的分页相应对象
     *
     * @param fromPage        业务层的分页对象
     * @param resultClz       接口需要返回的类型
     * @param convertConsumer 对数据进行转换的consumer
     * @param <D>
     * @param <R>
     * @return
     */
    public static <D, R> BasePageResp<R> transfer(BasePageResp<D> fromPage, Class<R> resultClz, BiConsumer<D, R> convertConsumer) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(fromPage.getTotalCount());
        result.setPageNo(fromPage.getPageNo());
        result.setPageSize(fromPage.getPageSize());
        result.setPages(fromPage.getPages());
        result.setScrollId(fromPage.getScrollId());
        if (fromPage.getData() == null || fromPage.getData().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            List<R> list = new ArrayList<>();
            for (D dClass : fromPage.getData()) {
                R rClass = JsonUtil.copy(dClass, resultClz);
                convertConsumer.accept(dClass, rClass);
                list.add(rClass);
            }
            result.setData(list);
        }
        return result;
    }


    /**
     * 将业务层的分页对象转换为外观的分页相应对象
     *
     * @param fromPage        业务层的分页对象
     * @param convertFunction 自定义转换方法
     * @param <D>
     * @param <R>
     * @return
     */
    public static <D, R> BasePageResp<R> transfer(BasePageResp<D> fromPage, Function<D, R> convertFunction) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(fromPage.getTotalCount());
        result.setPageNo(fromPage.getPageNo());
        result.setPageSize(fromPage.getPageSize());
        result.setPages(fromPage.getPages());
        result.setScrollId(fromPage.getScrollId());
        if (fromPage.getData() == null || fromPage.getData().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            List<R> list = new ArrayList<>();
            for (D dClass : fromPage.getData()) {
                list.add(convertFunction.apply(dClass));
            }
            result.setData(list);
        }
        return result;
    }

    /**
     * 提取分页对象中的数据
     *
     * @param pageResp
     * @param <R>
     * @return
     */
    public static <R> List<R> getData(BasePageResp<R> pageResp) {
        if (pageResp == null || pageResp.getData() == null) {
            return Collections.EMPTY_LIST;
        }
        return pageResp.getData();
    }


}
