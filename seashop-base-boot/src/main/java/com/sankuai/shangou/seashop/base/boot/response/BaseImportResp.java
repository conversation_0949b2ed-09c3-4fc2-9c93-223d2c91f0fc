package com.sankuai.shangou.seashop.base.boot.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/21 22:39
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BaseImportResp {

    /**
     * 导出成功条数
     */
    private int successCount;

    /**
     * 导入失败条数
     */
    private int errCount;

    /**
     * 失败文件下载路径
     */
    private String filePath;
}
