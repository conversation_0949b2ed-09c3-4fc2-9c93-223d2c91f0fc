package com.sankuai.shangou.seashop.base.boot.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.sankuai.shangou.seashop.base.boot.enums.LoginPlatformEnum;
import com.sankuai.shangou.seashop.base.boot.enums.LoginTypeEnum;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import cn.hutool.core.util.StrUtil;
import lombok.*;

/**
 * @author: cdd
 * @date: 2024/5/13/013
 * @description:
 */
@ToString
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class LoginReq {
//    @FieldDoc(description = "登录类型")
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    private String loginType;

//    @FieldDoc(description = "登录平台")
    private String loginPlatform;

//    @FieldDoc(description = "登录名称")
    private String userName;
//    @FieldDoc(description = "登录密码")
    private String password;
//    @FieldDoc(description = "滑块验证码")
    private AfsReq slideCode;

//    @FieldDoc(description = "手机号")
    private String phone;
//    @FieldDoc(description = "手机验证码")
    private String phoneCode;

//    @FieldDoc(description = "微信登录code")
    private String weCode;
//    @FieldDoc(description = "微信openId")
    private String openId;
//    @FieldDoc(description = "微信unionId")
    private String unionId;
    // 解密手机号的wx-code
    private String mobileCode;
    // 微信公众号code 用来获取微信公众号的openId
    private String code;

    public void checkParameter() {
        AssertUtil.throwIfNull(loginType, "登录类型必填");
        AssertUtil.throwIfNull(loginPlatform, "登录平台类型必填");
        AssertUtil.throwIfNull(LoginTypeEnum.nameOf(loginType),"登录类型有误");
        AssertUtil.throwIfNull(LoginPlatformEnum.nameOf(loginPlatform),"登录平台类型有误");
        /**
         * 账号密码登录
         */
        if (LoginTypeEnum.PASSWORD.name().equals(loginType)) {
            AssertUtil.throwIfTrue(userName == null || userName.trim().length() == 0, "登录名必填");
            AssertUtil.throwIfTrue(password == null || password.trim().length() == 0, "密码必填");
        }

        /**
         * 手机验证码登录
         */
        if (LoginTypeEnum.MOBILE.name().equals(loginType)) {
            AssertUtil.throwIfTrue(phone == null || phone.trim().length() == 0, "手机号必填");
            AssertUtil.throwIfTrue(phoneCode == null || phoneCode.trim().length() == 0, "短信验证码必填");
        }

        /**
         * 微信小程序code授权登录
         */
        if (LoginTypeEnum.WX_CODE.name().equals(loginType)) {
            AssertUtil.throwIfTrue(weCode == null || weCode.trim().length() == 0, "微信授权登录缺少code码");
            AssertUtil.throwIfTrue(StrUtil.isEmpty(mobileCode), "微信授权登录缺少mobile-code码");
        }

    }



    public void setLoginTypeWithDefault(String loginType) {
        if(StrUtil.isNotEmpty(loginType)){
            if(loginType.equals("SUB_ACCOUNT") ){
                this.loginType = LoginTypeEnum.SUB_ACCOUNT.name();
            }
        }
        if (LoginTypeEnum.nameOf(loginType) == null) {
            this.loginType = LoginTypeEnum.PASSWORD.name();
        }else {
            this.loginType = loginType;
        }
    }
}
