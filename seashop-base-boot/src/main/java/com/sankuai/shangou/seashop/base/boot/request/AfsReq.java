package com.sankuai.shangou.seashop.base.boot.request;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AfsReq {
    /**
     * 签名串。必填参数，从前端获取，不可更改。
     */
    private String sig;

    /**
     * 客户端IP。必填参数，后端填写。
     */
    private String remoteIp;

    /**
     * 会话标识。必填参数，从前端获取，不可更改。
     */
    private String sessionId;

    /**
     * 请求唯一标识。必填参数，从前端获取，不可更改。
     */
    private String token;
    /**
     * 场景标识。必填参数，从前端获取，不可更改。
     */
    private String scene;

}
