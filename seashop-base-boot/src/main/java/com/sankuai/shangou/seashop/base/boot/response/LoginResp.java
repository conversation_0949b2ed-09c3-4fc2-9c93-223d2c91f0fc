package com.sankuai.shangou.seashop.base.boot.response;

import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.LoginTypeEnum;
import lombok.*;

/**
 * @author: cdd
 * @date: 2024/5/13/013
 * @description:
 */
@ToString
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class LoginResp {
    //    @FieldDoc(description = "登录类型")
    private LoginTypeEnum loginType;
    //    @FieldDoc(description = "微信openId")
    private String openId;
    //    @FieldDoc(description = "微信unionId")
    private String unionId;
    //    @FieldDoc(description = "登录token信息")
    private TokenCache token;
    // 是否有密码
    private Boolean haveNotPassword;
}
