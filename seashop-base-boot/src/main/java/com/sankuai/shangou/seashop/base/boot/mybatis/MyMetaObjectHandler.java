package com.sankuai.shangou.seashop.base.boot.mybatis;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import org.apache.ibatis.reflection.MetaObject;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Date;

@Component
public class MyMetaObjectHandler implements MetaObjectHandler {
    @Override
    public void insertFill(MetaObject metaObject) {
        Object createTime = null;
        try {
            Object obj = metaObject.getOriginalObject();
            Class<?> aClass = obj.getClass();
            boolean hasCreateTime = Arrays.stream(aClass.getDeclaredFields()).filter(t -> t.getName().equals("createTime")).count() > 0;
            if (hasCreateTime) {
                Field createTimeField = aClass.getDeclaredField("createTime");
                createTimeField.setAccessible(true);
                createTime = createTimeField.get(obj);
            } else {
                Class<?> superClass = aClass.getSuperclass();
                Field createTimeField = superClass.getDeclaredField("createTime");
                createTimeField.setAccessible(true);
                createTime = createTimeField.get(obj);
            }


        } catch (Exception ex) {
            createTime = null;
        }

        if (ObjectUtil.isNull(createTime)) {
            createTime = new Date();
        }

        this.setFieldValByName("createTime", createTime, metaObject);

        Object updateTime = null;
        try {
            Object obj = metaObject.getOriginalObject();
            Class<?> aClass = obj.getClass();
            boolean hasUpdateTime = Arrays.stream(aClass.getDeclaredFields()).filter(t -> t.getName().equals("updateTime")).count() > 0;
            if (hasUpdateTime) {
                Field updateTimeField = aClass.getDeclaredField("updateTime");
                updateTimeField.setAccessible(true);
                updateTime = updateTimeField.get(obj);
            } else {
                Class<?> superClass = aClass.getSuperclass();
                Field updateTimeField = superClass.getDeclaredField("updateTime");
                updateTimeField.setAccessible(true);
                updateTime = updateTimeField.get(obj);
            }


        } catch (Exception ex) {
            updateTime = null;
        }

        if (ObjectUtil.isNull(updateTime)) {
            updateTime = new Date();
        }
        this.setFieldValByName("updateTime", updateTime, metaObject);
    }

    @Override
    public void updateFill(MetaObject metaObject) {

        Object updateTime = null;
        try {
            Object obj = metaObject.getOriginalObject();
            Class<?> aClass = obj.getClass();
            boolean hasUpdateTime = Arrays.stream(aClass.getDeclaredFields()).filter(t -> t.getName().equals("updateTime")).count() > 0;
            if (hasUpdateTime) {
                Field updateTimeField = aClass.getDeclaredField("updateTime");
                updateTimeField.setAccessible(true);
                updateTime = updateTimeField.get(obj);
            } else {
                Class<?> superClass = aClass.getSuperclass();
                Field updateTimeField = superClass.getDeclaredField("updateTime");
                updateTimeField.setAccessible(true);
                updateTime = updateTimeField.get(obj);
            }


        } catch (Exception ex) {
            updateTime = null;
        }

        if (ObjectUtil.isNull(updateTime)) {
            updateTime = new Date();
        }
        this.setFieldValByName("updateTime", updateTime, metaObject);
    }
}
