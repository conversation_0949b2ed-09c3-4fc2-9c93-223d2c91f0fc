package com.sankuai.shangou.seashop.base.boot.utils;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.json.JsonReadFeature;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.fasterxml.jackson.databind.ser.BeanPropertyWriter;
import com.fasterxml.jackson.databind.ser.BeanSerializerModifier;
import com.fasterxml.jackson.databind.type.CollectionType;
import com.fasterxml.jackson.datatype.guava.GuavaModule;
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.fasterxml.jackson.module.paramnames.ParameterNamesModule;
import com.google.common.collect.Sets;
import com.sankuai.shangou.seashop.base.boot.exception.JsonException;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Assert;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class JsonUtil {

    private static ObjectMapper MAPPER;
    private static Set<JsonReadFeature> JSON_READ_FEATURES_ENABLED;

    static {
        try {
            JSON_READ_FEATURES_ENABLED = Sets.newHashSet(
                    //允许在JSON中使用Java注释
                    JsonReadFeature.ALLOW_JAVA_COMMENTS,
                    //允许 json 存在没用双引号括起来的 field
                    JsonReadFeature.ALLOW_UNQUOTED_FIELD_NAMES,
                    //允许 json 存在使用单引号括起来的 field
                    JsonReadFeature.ALLOW_SINGLE_QUOTES,
                    //允许 json 存在没用引号括起来的 ascii 控制字符
                    JsonReadFeature.ALLOW_UNESCAPED_CONTROL_CHARS,
                    //允许 json number 类型的数存在前导 0 (例: 0001)
                    JsonReadFeature.ALLOW_LEADING_ZEROS_FOR_NUMBERS,
                    //允许 json 存在 NaN, INF, -INF 作为 number 类型
                    JsonReadFeature.ALLOW_NON_NUMERIC_NUMBERS,
                    //允许 只有Key没有Value的情况
                    JsonReadFeature.ALLOW_MISSING_VALUES,
                    //允许数组json的结尾多逗号
                    JsonReadFeature.ALLOW_TRAILING_COMMA
            );

            //初始化
            MAPPER = initMapper();
        } catch (Exception e) {
            log.error("jackson config error: , {}", e);
        }
    }

    private static ObjectMapper initMapper() {
        JsonReadFeature[] array = JSON_READ_FEATURES_ENABLED.toArray(new JsonReadFeature[0]);
        JsonMapper.Builder builder = JsonMapper.builder().enable(array);
        return initMapperConfig(builder.build());
    }

    private static ObjectMapper initMapperConfig(ObjectMapper objectMapper) {
        String dateTimeFormat = "yyyy-MM-dd HH:mm:ss";
        objectMapper.setDateFormat(new SimpleDateFormat(dateTimeFormat));
        //配置序列化级别
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        //配置JSON缩进支持
        objectMapper.configure(SerializationFeature.INDENT_OUTPUT, false);
        //允许单个数值当做数组处理
        objectMapper.enable(DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY);
        //禁止重复键, 抛出异常
        objectMapper.enable(DeserializationFeature.FAIL_ON_READING_DUP_TREE_KEY);
        //禁止使用int代表Enum的order()來反序列化Enum, 抛出异常
        objectMapper.enable(DeserializationFeature.FAIL_ON_NUMBERS_FOR_ENUMS);
        //有属性不能映射的时候不报错
        objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
        //对象为空时不抛异常
        objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
        //时间格式
        objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
        //允许未知字段
        objectMapper.enable(JsonGenerator.Feature.IGNORE_UNKNOWN);
        //序列化BigDecimal时之间输出原始数字还是科学计数, 默认false, 即是否以toPlainString()科学计数方式来输出
        objectMapper.enable(JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN);
        //识别Java8时间
        objectMapper.registerModule(new ParameterNamesModule());
        objectMapper.registerModule(new Jdk8Module());
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(DateTimeFormatter.ofPattern(dateTimeFormat)))
                .addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(DateTimeFormatter.ofPattern(dateTimeFormat)));
        objectMapper.registerModule(javaTimeModule);
        //识别Guava包的类
        objectMapper.registerModule(new GuavaModule());
        return objectMapper;
    }

    public static ObjectMapper getObjectMapper() {
        return MAPPER;
    }

    public static String toJsonString(Object obj) {
        try {
            return MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new JsonException(e);
        }
    }

    public static String toJsonString(Object obj, ObjectMapper mapper) {
        try {
            return mapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new JsonException(e);
        }
    }

    public static <T> T parseObject(String json, Class<T> clazz) {
        Assert.notBlank(json, "json not null");
        try {
            return MAPPER.readValue(json, clazz);
        } catch (IOException e) {
            throw new JsonException(String.format("jackson parse object error, json: %s, class: %s", json, clazz), e);
        }
    }

    public static <T> T parseObject(byte[] bytes, Class<T> clazz) {
        Assert.notNull(bytes, "bytes not null");
        try {
            return MAPPER.readValue(bytes, clazz);
        }
        catch (IOException e) {
            throw new JsonException(String.format("jackson parse object error, json: %s, class: %s", new String(bytes, StandardCharsets.UTF_8), clazz), e);
        }
    }

    public static <T> T parseObject(InputStream stream, Class<T> clazz) {
        try {
            return MAPPER.readValue(stream, clazz);
        } catch (IOException e) {
            throw new JsonException(String.format("jackson parse object error, json: %s, class: %s", stream, clazz), e);
        }
    }
    public static <T> T parseObject(String json, Class<T> clazz, ObjectMapper mapper) {
        try {
            return mapper.readValue(json, clazz);
        } catch (IOException e) {
            throw new JsonException(String.format("jackson parse object error, json: %s, class: %s", json, clazz), e);
        }
    }


    /**
     * 从json串中获取某个字段
     *
     * @return String，默认为 null
     */
    public static String getAsString(String json, String key) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        try {
            JsonNode jsonNode = getAsJsonObject(json, key);
            if (null == jsonNode) {
                return null;
            }
            return getAsString(jsonNode);
        } catch (Exception e) {
            throw new JsonException(String.format("jackson get string error, json: %s, key: %s", json, key), e);
        }
    }

    private static String getAsString(JsonNode jsonNode) {
        return jsonNode.isTextual() ? jsonNode.textValue() : jsonNode.toString();
    }

    /**
     * 从json串中获取某个字段
     *
     * @return object, 默认为 null
     */
    public static <V> V getAsObject(String json, String key, Class<V> type) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        try {
            JsonNode jsonNode = getAsJsonObject(json, key);
            if (null == jsonNode) {
                return null;
            }
            JavaType javaType = MAPPER.getTypeFactory().constructType(type);
            return parseObject(getAsString(jsonNode), javaType);
        } catch (Exception e) {
            throw new JsonException(String.format("jackson get list error, json: %s, key: %s, type: %s", json, key, type), e);
        }
    }

    /**
     * JSON反序列化
     */
    public static <V> V parseObject(String json, TypeReference<V> type) {
        return parseObject(json, type.getType());
    }

    /**
     * JSON反序列化
     */
    public static <V> V parseObject(String json, Type type) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        try {
            JavaType javaType = MAPPER.getTypeFactory().constructType(type);
            return MAPPER.readValue(json, javaType);
        } catch (IOException e) {
            throw new JsonException(String.format("jackson from error, json: %s, type: %s", json, type), e);
        }
    }

    /**
     * JSON反序列化
     */
    public static <V> List<V> parseArray(String json, Class<V> type) {
        if (StringUtils.isEmpty(json)) {
            return null;
        }
        try {

            CollectionType collectionType = MAPPER.getTypeFactory().constructCollectionType(List.class, type);
            return MAPPER.readValue(json, collectionType);
        } catch (IOException e) {
            throw new JsonException(String.format("jackson from error, json: %s, type: %s", json, type), e);
        }
    }

    public static<V> List<V> parseArray(InputStream steam, Class<V> type) {

        try {

            CollectionType collectionType = MAPPER.getTypeFactory().constructCollectionType(List.class, type);
            return MAPPER.readValue(steam, collectionType);
        } catch (IOException e) {
            throw new JsonException(String.format("jackson from error, json: %s, type: %s", steam, type), e);
        }
    }


    /**
     * JSON反序列化
     */
    public static Map beanToMap(Object bean) {
        if (bean == null) {
            return null;
        }
        return MAPPER.convertValue(bean, Map.class);
    }

    /**
     * 从json串中获取某个字段
     *
     * @return JsonNode, 默认为 null
     */
    public static JsonNode getAsJsonObject(String json, String key) {
        try {
            JsonNode node = MAPPER.readTree(json);
            if (null == node) {
                return null;
            }
            return node.get(key);
        } catch (IOException e) {
            throw new JsonException(String.format("jackson get object from json error, json: %s, key: %s", json, key), e);
        }
    }

    /**
     * 向json中添加属性
     *
     * @return json
     */
    public static <V> String add(String json, String key, V value) {
        try {
            JsonNode node = MAPPER.readTree(json);
            add(node, key, value);
            return node.toString();
        } catch (IOException e) {
            throw new JsonException(String.format("jackson add error, json: %s, key: %s, value: %s", json, key, value), e);
        }
    }

    /**
     * 向json中添加属性
     */
    private static <V> void add(JsonNode jsonNode, String key, V value) {
        if (value instanceof String) {
            ((ObjectNode) jsonNode).put(key, (String) value);
        } else if (value instanceof Short) {
            ((ObjectNode) jsonNode).put(key, (Short) value);
        } else if (value instanceof Integer) {
            ((ObjectNode) jsonNode).put(key, (Integer) value);
        } else if (value instanceof Long) {
            ((ObjectNode) jsonNode).put(key, (Long) value);
        } else if (value instanceof Float) {
            ((ObjectNode) jsonNode).put(key, (Float) value);
        } else if (value instanceof Double) {
            ((ObjectNode) jsonNode).put(key, (Double) value);
        } else if (value instanceof BigDecimal) {
            ((ObjectNode) jsonNode).put(key, (BigDecimal) value);
        } else if (value instanceof BigInteger) {
            ((ObjectNode) jsonNode).put(key, (BigInteger) value);
        } else if (value instanceof Boolean) {
            ((ObjectNode) jsonNode).put(key, (Boolean) value);
        } else if (value instanceof byte[]) {
            ((ObjectNode) jsonNode).put(key, (byte[]) value);
        } else {
            ((ObjectNode) jsonNode).put(key, toJsonString(value));
        }
    }

    /**
     * 格式化Json(美化)
     *
     * @return 格式化后的字符串
     */
    public static String format(String json) {
        try {
            JsonNode node = MAPPER.readTree(json);
            return MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(node);
        } catch (IOException e) {
            throw new JsonException(String.format("jackson format json error, json: %s", json), e);
        }
    }

    /**
     * 判断字符串是否是json
     *
     * @return true/false
     */
    public static boolean isJson(String json) {
        try {
            MAPPER.readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 拷贝对象
     */
    public static <S, T> T copy(S t, Class<T> clazz) {
        return parseObject(toJsonString(t), clazz);
    }

    public static <S, T> T copy(S t, TypeReference<T> typeReference) {
        return parseObject(toJsonString(t), typeReference);
    }

    /**
     * 拷贝对象
     */
    public static <S, T> T copy(S t, Class<T> clazz, String... ignoreProperties) {
        SimpleModule module = new SimpleModule();
        module.setSerializerModifier(new BeanSerializerModifier() {
            @Override
            public List<BeanPropertyWriter> changeProperties(SerializationConfig config, BeanDescription beanDesc, List<BeanPropertyWriter> beanProperties) {
                List<BeanPropertyWriter> result = new ArrayList<>();
                List<String> ignoreFileNames = Arrays.asList(ignoreProperties);
                for (BeanPropertyWriter writer : beanProperties) {
                    if (!ignoreFileNames.contains(writer.getName())) {
                        result.add(writer);
                    }
                }
                return result;
            }
        });

        ObjectMapper mapper = MAPPER.copy();
        mapper.registerModule(module);
        return parseObject(toJsonString(t, mapper), clazz, mapper);
    }

    /**
     * 拷贝集合
     */
    public static <S, T> List<T> copyList(List<S> list, Class<T> clazz) {
        return parseArray(toJsonString(list), clazz);
    }

    /**
     * 拷贝集合，循环单个对象转换
     */
    public static <S, T> List<T> copyList(List<S> list, Class<T> clazz, BiConsumer<S, T> convertConsumer) {
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        return list.stream().map(s -> {
            T t = parseObject(toJsonString(s), clazz);
            convertConsumer.accept(s, t);
            return t;
        }).collect(Collectors.toList());
    }

}
