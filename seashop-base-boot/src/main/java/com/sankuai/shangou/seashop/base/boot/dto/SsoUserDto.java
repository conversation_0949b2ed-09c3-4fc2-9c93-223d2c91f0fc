package com.sankuai.shangou.seashop.base.boot.dto;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: sso用户信息dto
 * @author: LXH
 **/
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SsoUserDto extends BaseAccountDto{
    private String code;
    private String email;
    private String tenantId;
    private String roles;
    private Boolean isVerified;
    private String verifyType;
    private Long verifyExpireTime;
    private String passport;
    private Boolean verified;


    public SsoUserDto(String ssoUser) {
        if (ssoUser == null) {
            return;
        }
        SsoUserDto ssoUserDto = JsonUtil.parseObject(ssoUser,SsoUserDto.class);
        this.code = ssoUserDto.getCode();
        this.email = ssoUserDto.getEmail();
        this.tenantId = ssoUserDto.getTenantId();
        this.roles = ssoUserDto.getRoles();
        this.isVerified = ssoUserDto.getIsVerified();
        this.verifyType = ssoUserDto.getVerifyType();
        this.verifyExpireTime = ssoUserDto.getVerifyExpireTime();
        this.passport = ssoUserDto.getPassport();
        this.verified = ssoUserDto.getVerified();
    }
}
