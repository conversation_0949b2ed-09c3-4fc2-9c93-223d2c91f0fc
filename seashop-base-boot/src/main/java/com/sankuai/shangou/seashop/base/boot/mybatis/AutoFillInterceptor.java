package com.sankuai.shangou.seashop.base.boot.mybatis;

import com.baomidou.mybatisplus.extension.parser.JsqlParserSupport;
import com.baomidou.mybatisplus.extension.plugins.inner.InnerInterceptor;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.update.Update;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class AutoFillInterceptor extends JsqlParserSupport implements InnerInterceptor {

//    @Override
//    public void processInsert(Insert insert, int index, String sql, Object obj) {
//        insert.getColumns().contains(new Column("createTime"));
//    }
//
//    @Override
//    public void processUpdate(Update update, int index, String sql, Object obj) {
//        update.getColumns().contains(new Column("createTime"));
//    }
}
