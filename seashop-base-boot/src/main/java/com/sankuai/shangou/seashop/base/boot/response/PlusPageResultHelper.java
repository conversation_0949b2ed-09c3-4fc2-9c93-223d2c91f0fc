package com.sankuai.shangou.seashop.base.boot.response;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
public class PlusPageResultHelper {


    /**
     * 返回默认的空数据。分页数据按照入参返回。列表返回大小为0的空集合
     *
     * <AUTHOR>
     */
    public static <R> BasePageResp<R> defaultEmpty(BasePageReq pageReq) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(0L);
        result.setPageNo(pageReq.getPageNo());
        result.setPageSize(pageReq.getPageSize());
        result.setPages(0);
        result.setData(Collections.EMPTY_LIST);
        return result;
    }


    /**
     * 将DB层的mybatis分页对象转换为系统的分页相应对象
     *
     * @param fromPage  业务层的分页对象
     * @param resultClz 接口需要返回的类型
     * <AUTHOR>
     */
    public static <D, R> BasePageResp<R> transfer(Page<D> fromPage, Class<R> resultClz) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(fromPage.getTotal());
        result.setPageNo((int)fromPage.getCurrent());
        result.setPageSize((int)fromPage.getSize());
        result.setPages((int)fromPage.getPages());
        if (fromPage.getRecords() == null || fromPage.getRecords().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            result.setData(JsonUtil.copyList(fromPage.getRecords(), resultClz));
        }
        return result;
    }

    /**
     * 将DB层的mybatis分页对象转换为系统的分页相应对象，并支持对相应对象做基础的数据转换
     *
     * @param fromPage        业务层的分页对象
     * @param resultClz       接口需要返回的类型
     * @param convertConsumer 对数据进行转换的consumer
     * <AUTHOR>
     * @date 2023/3/16
     */
    public static <D, R> BasePageResp<R> transfer(Page<D> fromPage, Class<R> resultClz, Consumer<R> convertConsumer) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(fromPage.getTotal());
        result.setPageNo((int)fromPage.getCurrent());
        result.setPageSize((int)fromPage.getSize());
        result.setPages((int)fromPage.getPages());
        if (fromPage.getRecords() == null || fromPage.getRecords().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            List<R> list = JsonUtil.copyList(fromPage.getRecords(), resultClz);
            list.forEach(x -> convertConsumer.accept(x));
            result.setData(list);
        }
        return result;
    }


    /**
     * 将DB层的mybatis分页对象转换为系统的分页相应对象，并支持对相应对象做基础的数据转换
     *
     * @param dbPage          业务层的分页对象
     * @param resultClz       接口需要返回的类型
     * @param convertConsumer 对数据进行转换的consumer
     * @param <D>
     * @param <R>
     * @return
     */
    public static <D, R> BasePageResp<R> transfer(Page<D> dbPage, Class<R> resultClz, BiConsumer<D, R> convertConsumer) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(dbPage.getTotal());
        result.setPageNo((int)dbPage.getCurrent());
        result.setPageSize((int)dbPage.getSize());
        result.setPages((int)dbPage.getPages());
        if (dbPage.getRecords() == null || dbPage.getRecords().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            List<R> list = new ArrayList<>();
            for (D dClass : dbPage.getRecords()) {
                R rClass = JsonUtil.copy(dClass, resultClz);
                convertConsumer.accept(dClass, rClass);
                list.add(rClass);
            }
            result.setData(list);
        }
        return result;
    }

    /**
     * 将DB层的mybatis分页对象转换为系统的分页相应对象，并支持对相应对象做基础的数据转换
     *
     * @param fromPage        业务层的分页对象
     * @param convertFunction 自定义转换方法
     * @param <D>
     * @param <R>
     * @return
     */
    public static <D, R> BasePageResp<R> transfer(Page<D> fromPage, Function<D, R> convertFunction) {
        BasePageResp<R> result = new BasePageResp<>();
        result.setTotalCount(fromPage.getTotal());
        result.setPageNo((int)fromPage.getCurrent());
        result.setPageSize((int)fromPage.getSize());
        result.setPages((int)fromPage.getPages());
        if (fromPage.getRecords() == null || fromPage.getRecords().isEmpty()) {
            result.setData(Collections.EMPTY_LIST);
        }
        else {
            List<R> list = new ArrayList<>();
            for (D dClass : fromPage.getRecords()) {
                list.add(convertFunction.apply(dClass));
            }
            result.setData(list);
        }
        return result;
    }



}
