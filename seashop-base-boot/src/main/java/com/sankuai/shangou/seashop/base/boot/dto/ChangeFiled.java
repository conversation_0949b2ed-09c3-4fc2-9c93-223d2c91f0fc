package com.sankuai.shangou.seashop.base.boot.dto;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class ChangeFiled extends BaseThriftDto {
//    @FieldDoc(description = "字段")
    private String fieldId;

//    @FieldDoc(description = "字段说明")
    private String fieldName;

//    @FieldDoc(description = "之前的值")
    private String before;

//    @FieldDoc(description = "之后的值")
    private String after;

//    @FieldDoc(description = "子对象")
    private List<ChangeFiled> childFiled;

//    @FieldDoc(description = "子对象")
    private List<List<ChangeFiled>> childList;
}
