package com.sankuai.shangou.seashop.base.boot.dto;

import lombok.*;

/**
 * @author: cdd
 * @date: 2024/5/13/013
 * @description:
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TokenCache {
    /**
     * token
     */
    private String token;
    /**
     * token关联的用户ID
     */
    private Long userId;
    /**
     * 当前token 失效时间
     */
    private Long expiresTime;
    /**
     * 当前用户类型 {@link com.sankuai.shangou.seashop.base.boot.enums.LoginTypeEnum}
     */
    private String userType;

    /**
     * 是否被踢 0正常 -1被踢
     */
    private String kick = "0";

}
