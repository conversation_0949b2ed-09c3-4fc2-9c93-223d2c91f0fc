package com.sankuai.shangou.seashop.base.boot.request;

import java.util.Optional;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/07 19:46
 */
@ToString
@Data
public class BaseIdReq extends BaseParamReq {

    @PrimaryField(title = "主键id")
    @ExaminField(description = "主键id")
    private Long id;

    @Override
    public void checkParameter() {
        Optional.ofNullable(id).filter(x -> x > 0).orElseThrow(() -> new IllegalArgumentException("主键不能为空"));
    }
}
