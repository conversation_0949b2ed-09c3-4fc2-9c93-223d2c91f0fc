package com.sankuai.shangou.seashop.base.boot.response;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.sankuai.shangou.seashop.base.boot.enums.TResultCode;

@Data
public class ResultDto<T> {
//    @FieldDoc(
//            description = "返回结果"
//    )
    private T data;
//    @FieldDoc(
//            description = "状态码"
//    )
    private Integer code;
//    @FieldDoc(
//            description = "接口返回信息"
//    )
    private String message;

    public ResultDto() {
    }

    public ResultDto(String message) {
        this((String) message, null);
    }

    public ResultDto(T data) {
        this((String) null, data);
    }

    public ResultDto(String message, T data) {
        this((Integer) null, message, data);
    }

    public ResultDto(Integer code, String message) {
        this(code, message, null);
    }

    public ResultDto(Integer code, String message, T data) {
        this.code = code;
        this.data = data;
        this.message = message;
    }

    public static <T> ResultDto<T> newWithData(T data) {
        ResultDto<T> result = new ResultDto();
        result.setCode(TResultCode.SUCCESS.value());
        result.setData(data);
        return result;
    }

    public static <T> ResultDto<T> ok(T data) {
        ResultDto<T> result = new ResultDto();
        result.setCode(TResultCode.SUCCESS.value());
        result.setData(data);
        return result;
    }
    public static <T> ResultDto<T> ok() {
        ResultDto<T> result = new ResultDto();
        result.setCode(TResultCode.SUCCESS.value());
        return result;
    }

    public boolean isSuccess() {
        return TResultCode.SUCCESS.value().equals(this.code);
    }


    public ResultDto<T> success(T data) {
        this.code = TResultCode.SUCCESS.value();
        this.data = data;
        return this;
    }


    public ResultDto<T> fail(Integer code, String message) {
        this.code = code;
        this.message = message;
        return this;
    }


    public ResultDto<T> data(Integer code, String message, T data) {
        this.data = data;
        this.code = code;
        this.message = message;
        return this;
    }

    public ResultDto<T> data(T data) {
        this.data = data;
        return this;
    }

    public ResultDto<T> message(String message) {
        this.message = message;
        return this;
    }

    public ResultDto<T> code(Integer code) {
        this.code = code;
        return this;
    }

    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }
}



