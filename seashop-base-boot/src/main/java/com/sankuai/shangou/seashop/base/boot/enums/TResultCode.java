package com.sankuai.shangou.seashop.base.boot.enums;

import lombok.Getter;

@Getter
public enum TResultCode implements Code {
    SUCCESS(0, "请求成功"),
    BAD_REQUEST(400, "客户端请求错误"),
    UNAUTHORIZED(401, "未授权请求"),
    SERVER_ERROR(500, "服务端异常"),



    /**
     * 业务异常定义：xx-xxx-xxx
     * <pre>
     * 一、业务系统区分：020-基础服务；030-交易服务；040-用户服务；050-营销服务；060商品服务；070：订单服务；080：支付服务；
     * 二、前面两位代表错误类型：40-参数校验错误；50-业务逻辑错误；60-系统错误；
     * 三、后面三位代表具体错误码
     * </pre>
     */

    BIZ_PRE_ORDER_VALIDATE_FAIL(50030001, "商品不满足下单条件，请刷新页面后重新提交"),


    ;



    private final Integer value;
    private final String desc;

    private TResultCode(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    @Override
    public Integer value() {
        return this.value;
    }

    public String desc() {
        return this.desc;
    }
}