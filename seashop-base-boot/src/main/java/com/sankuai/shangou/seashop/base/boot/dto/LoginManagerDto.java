package com.sankuai.shangou.seashop.base.boot.dto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 管理员信息dto
 * @author: LXH
 **/
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class LoginManagerDto extends LoginBaseDto {
//    @FieldDoc(description = "用户角色标识集合")
    private List<Long> roles;
//    @FieldDoc(description = "用户权限url集合")
    private List<String> privileges;

}
