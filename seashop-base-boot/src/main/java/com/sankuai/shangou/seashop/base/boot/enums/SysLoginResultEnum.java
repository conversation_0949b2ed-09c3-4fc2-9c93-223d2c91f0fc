package com.sankuai.shangou.seashop.base.boot.enums;

import lombok.Getter;

/**
 * @description:
 * @author: LXH
 **/
@Getter
public enum SysLoginResultEnum implements Code {
    NOT_LOGGED_IN(********, "未登录"),
    THE_LOGIN_INFORMATION_ERROR(********, "登录信息当前系统不存在"),
    PROHIBIT_LOGIN(********, "禁止登录"),
    ACCOUNT_FROZEN(********, "用户已冻结"),
    SHOP_NOT_EXIST_OR_NOT_OPEN(********, "店铺不存在或已冻结,暂时无法访问"),
    ;

    private final int code;
    private final String msg;

    SysLoginResultEnum(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getMsg() {
        return msg;
    }

    @Override
    public Integer value() {
        return code;
    }

    @Override
    public String desc() {
        return msg;
    }
}
