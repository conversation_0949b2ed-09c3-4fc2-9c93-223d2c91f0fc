package com.sankuai.shangou.seashop.base.boot.constant;

/**
 * <AUTHOR>
 * @date 2023/11/22 16:56
 */
public class RegexConstant {

    /**
     * 手机号正则
     */
    public final static String PHONE_REGEX = "^\\d{11}$";

    /**
     * 正整数正则
     */
    public final static String POSITIVE_INTEGER_REGEX = "^[1-9]\\d*$";

    /**
     * 非负整数正则
     */
    public final static String NON_NEGATIVE_INTEGER_REGEX = "^\\d+$";

    /**
     * 金额校验正则表达式, 必须大于0, 最多两位小数
     */
    public final static String AMOUNT_REGEX = "^(?!0+(?:\\.0+)?$)(?:[1-9]\\d*|0)(?:\\.\\d{1,2})?$";

    /**
     * 是否校验正则表达式, 取值必须为是或者否
     */
    public final static String YES_OR_NO_REGEX = "^(是|否)$";

}
