package com.sankuai.shangou.seashop.base.boot.utils;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * AES加解密工具类
 * <AUTHOR>
 */
public class AesUtil {

    private static final String AES_ALGORITHM = "AES";
    private static final String CIPHER_MODE = "AES/CBC/PKCS5Padding";

    private final static byte[] ivBytes = {
                    18, 52, 86, 120, (byte)144, (byte)171, (byte)205, (byte)239, 18, 52,
                    86, 120, (byte)144, (byte)171, (byte)205, (byte)239
            };

    /**
     * AES加密
     * <AUTHOR>
     * @param encryptStr 待加密的字符串
	 * @param encryptKey 加密密钥
     * @return 返回加密后的字符串，base64编码
     */
    public static String encrypt(String encryptStr, String encryptKey) {
        try {
            if (encryptStr == null || encryptStr.isEmpty()) {
                return "";
            }

            // 确保密钥长度为32字节
            encryptKey = ensureAesKey(encryptKey);

            // 转换key和加密数据为字节数组
            byte[] keyBytes = encryptKey.getBytes(StandardCharsets.UTF_8);
            byte[] plaintextBytes = encryptStr.getBytes(StandardCharsets.UTF_8);

            // 设置key和IV偏移量
            SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, AES_ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);

            // 执行加密
            Cipher cipher = Cipher.getInstance(CIPHER_MODE);
            cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec, ivParameterSpec);
            byte[] encryptedBytes = cipher.doFinal(plaintextBytes);

            // 结果转换成base64
            return Base64.getEncoder().encodeToString(encryptedBytes);
        } catch (Exception e) {
            throw new BusinessException("加密失败");
        }
    }

    /**
     * AES解密
     * <AUTHOR>
     * @param decryptStr 待解密的字符串
	 * @param decryptKey 密钥
     * @return 返回解密后的字符串
     */
    public static String decrypt(String decryptStr, String decryptKey) {
        try {
            if (decryptStr == null || decryptStr.trim().isEmpty()) {
                return "";
            }

            // 确保密钥长度为32字节
            decryptKey = decryptKey.substring(0, Math.min(decryptKey.length(), 32));
            decryptKey = String.format("%-32s", decryptKey);

            // 转换key和解密数据为字节数组
            byte[] keyBytes = decryptKey.getBytes(StandardCharsets.UTF_8);
            byte[] ciphertextBytes = Base64.getDecoder().decode(decryptStr);

            // 设置key和IV偏移量
            SecretKeySpec secretKeySpec = new SecretKeySpec(keyBytes, AES_ALGORITHM);
            IvParameterSpec ivParameterSpec = new IvParameterSpec(ivBytes);

            // 执行加密
            Cipher cipher = Cipher.getInstance(CIPHER_MODE);
            cipher.init(Cipher.DECRYPT_MODE, secretKeySpec, ivParameterSpec);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int bytesRead;

            try {
                bytesRead = cipher.doFinal(ciphertextBytes, 0, ciphertextBytes.length, buffer, 0);
                outputStream.write(buffer, 0, bytesRead);
            } catch (Exception e) {
                throw new BusinessException("解密失败");
            } finally {
                outputStream.close();
            }

            // 结果转换成base64
            String decryptedString = outputStream.toString("UTF-8");
            return decryptedString.replace("\0", "");
        } catch (Exception e) {
            throw new BusinessException("解密失败");
        }
    }

    private static String ensureAesKey(String originKey) {
        originKey = originKey.substring(0, Math.min(originKey.length(), 32));
        return String.format("%-32s", originKey);
    }

    public static void main(String[] args) {
//        System.out.println(decrypt("fpO2ciZTt4j0/SI3au3Mrw==", "ae125efkk4454eeff444ferfkny6oxi8"));
//        System.out.println(decrypt("Qd1lSCINF6HOFk2Iyh288pEu7TUAEOz8COi72ZZQur4CU91k6YxIJRzNGX7UNyAJxaHOgx7YSnvARyhS7sSYEA==", "ae125efkk4454eeff444ferfkny6oxi8"));
//        System.out.println(decrypt("VudcAiAJEl732BHQpAtpPA==", "ae125efkk4454eeff444ferfkny6oxi8"));
//        System.out.println(encrypt("张三", "ae125efkk4454eeff444ferfkny6oxi8"));
        System.out.println(encrypt("***********", "ae125efkk4454eeff444ferfkny6oxi8"));
    }

}
