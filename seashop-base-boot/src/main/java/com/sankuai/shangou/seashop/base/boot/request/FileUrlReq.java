package com.sankuai.shangou.seashop.base.boot.request;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Optional;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@Data
public class FileUrlReq extends BaseParamReq {

    private String fileUrl;

    @Override
    public void checkParameter() {
        Optional.ofNullable(fileUrl).filter(c -> StringUtils.isNotBlank(c)).orElseThrow(() -> new IllegalArgumentException("文件地址不能为空"));
    }
}
