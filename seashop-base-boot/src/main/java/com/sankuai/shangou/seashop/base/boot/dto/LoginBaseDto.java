package com.sankuai.shangou.seashop.base.boot.dto;

import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @description: 登录基本信息dto
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class LoginBaseDto implements Serializable {
    //    @FieldDoc(description = "用户ID")
    private Long id;
    //    @FieldDoc(description = "用户名称")
    private String name;
    //    @FieldDoc(description = "角色类型")
    private RoleEnum roleType;
    //    @FieldDoc(description = "用户手机号")
    private String userPhone;

    //    @FieldDoc(description = "是否禁用")
    private Boolean disable = false;
    //    @FieldDoc(description = "用户ID")
    private Long shopId;

    // 是否删除(注销)
    private Boolean whetherDelete = false;
    // 是否有密码
    private Boolean haveNotPassword = true;
}
