package com.sankuai.shangou.seashop.base.boot.enums;

import lombok.Getter;

/**
 * @author: cdd
 * @date: 2024/5/11/011
 * @description: 登录类型枚举
 */
@Getter
public enum LoginTypeEnum{
    PASSWORD("password", "账号密码登录"),
    MOBILE("mobile", "手机短信登录"),
    WX_CODE("wx_mini_code", "微信小程序code码登录"),
    TOKEN("token", "token登录"),
    SUB_ACCOUNT("sub_acount","子账号登录"),
    ;

    private String code;
    private String msg;

    private LoginTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public String getMsg() {
        return this.msg;
    }

    public static LoginTypeEnum nameOf(String name){
        if (name == null){
            return null;
        }
        for (LoginTypeEnum typeEnum:values()){
            if (typeEnum.name().equals(name)){
                return typeEnum;
            }
        }
        return null;
    }

}
