package com.sankuai.shangou.seashop.base.boot.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 商家/会员信息dto
 * @author: LXH
 **/
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Data
public class LoginMemberDto extends LoginBaseDto {
//    @FieldDoc(description = "是否微信用户")
    private Boolean weiXinUser;

//    @FieldDoc(description = "微信用户ID")
    private String weiXinOpenId;

//    @FieldDoc(description = "店铺id")
    private Long shopId;

//    @FieldDoc(description = "管理员id")
    private Long managerId;

//    @FieldDoc(description = "微信用户UnionID")
    private String weiXinUnionId;

    private String wxmpOpenId;

    private String wxmpUnionId;

}
