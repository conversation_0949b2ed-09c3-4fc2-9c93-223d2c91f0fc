package com.sankuai.shangou.seashop.base.boot.enums;

import lombok.Getter;

@Getter
public enum RoleEnum {
    /**
     * 平台管理员，manager表 shopId=0的用户
     */
    MANAGER(1, "管理员"),
    /**
     * 卖家 manager表 shopId 大于0的用户
     */
    SHOP(2, "卖家"),
    /**
     * 会员 只存在member表，如果入驻成为卖家，会将信息同步并在manager表创建用户
     */
    MEMBER(3, "会员"),
    /**
     * 未知
     */
    UNKNOWN(4, "未知");

    private final int value;
    private final String name;

    RoleEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static RoleEnum valueOf(int value) {
        switch (value) {
            case 1:
                return MANAGER;
            case 2:
                return SHOP;
            case 3:
                return MEMBER;
            default:
                return UNKNOWN;
        }
    }

    public static RoleEnum nameOf(String name) {
        for (RoleEnum role:values()){
            if (role.name().equals(name)){
                return role;
            }
        }
        return null;
    }

    public String getName() {
        return name;
    }
}
