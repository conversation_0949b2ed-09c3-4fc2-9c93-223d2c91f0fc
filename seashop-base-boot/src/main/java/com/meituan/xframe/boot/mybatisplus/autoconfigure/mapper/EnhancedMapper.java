package com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface EnhancedMapper<T> extends BaseMapper<T> {

    Integer insertBatchSomeColumn(Collection<T> entityList);

    Integer deleteByIdWithFill(T entity);

    Integer deleteAll();

    List<T> selectAll();

    Long selectAllCount();

}
