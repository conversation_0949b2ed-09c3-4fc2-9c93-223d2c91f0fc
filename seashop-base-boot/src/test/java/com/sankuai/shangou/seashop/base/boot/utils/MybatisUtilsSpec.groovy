package com.sankuai.shangou.seashop.base.boot.utils

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq
import spock.lang.Specification
import spock.lang.Subject

/**
 * <AUTHOR>
 * @date 2024/03/22 14:14
 */
@Subject(MybatisUtil)
class MybatisUtilsSpec extends Specification {

    def "test getOrderSql with sortField"() {
        given:
        LinkedHashMap<String, Boolean> sortField = new LinkedHashMap<>()
        sortField.put("name", true)
        sortField.put("age", false)

        when:
        def result = MybatisUtil.getOrderSql(sortField)

        then:
        result == " name ASC, age DESC"
    }

    def "test getOrderSql with sortList and mapping"() {
        given:
        List<FieldSortReq> sortList = [
                new FieldSortReq(sort: "name", izAsc: true),
                new FieldSortReq(sort: "age", izAsc: false)
        ]
        Map<String, String> mapping = ["name": "username", "age": "userage"]

        when:
        def result = MybatisUtil.getOrderSql(sortList, mapping)

        then:
        result == " username ASC, userage DESC"
    }

    def "test getOrderSql with empty sortField"() {
        when:
        def result = MybatisUtil.getOrderSql(new LinkedHashMap<>())

        then:
        result == ""
    }

    def "test getOrderSql with null sortField"() {
        when:
        def result = MybatisUtil.getOrderSql(null)

        then:
        result == ""
    }

    def "test getOrderSql with null sortList"() {
        when:
        def result = MybatisUtil.getOrderSql(null, new HashMap<>())

        then:
        result == ""
    }

    def "test queryBatch"() {
        given:
        def ids = [1, 2, 3, 4, 5]
        def batchSize = 2

        when:
        def result = MybatisUtil.queryBatch({ subIds -> subIds.collect { "result_$it" } }, ids, batchSize)

        then:
        result == ["result_1", "result_2", "result_3", "result_4", "result_5"]
    }

    def "test countBatch"() {
        given:
        def ids = [1, 2, 3, 4, 5]
        def batchSize = 2

        when:
        def result = MybatisUtil.countBatch({ subIds -> subIds.size() }, ids, batchSize)

        then:
        result == 5
    }

    def "test executeBatch"() {
        given:
        def ids = [1, 2, 3, 4, 5]
        def batchSize = 2
        def result = []

        when:
        MybatisUtil.executeBatch({ subIds -> result.addAll(subIds.collect { "result_$it" }) }, ids, batchSize)

        then:
        result == ["result_1", "result_2", "result_3", "result_4", "result_5"]
    }

}
