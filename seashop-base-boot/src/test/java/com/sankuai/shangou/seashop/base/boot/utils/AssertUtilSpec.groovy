package com.sankuai.shangou.seashop.base.boot.utils

import com.sankuai.shangou.seashop.base.boot.enums.Code
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException
import spock.lang.Specification
import spock.lang.Subject

@Subject(AssertUtil)
class AssertUtilSpec extends Specification {

    def "throwIfTrue should throw BusinessException when expression is true"() {
        given:
        def expression = true

        when:
        AssertUtil.throwIfTrue(expression, "Test BusinessException")

        then:
        thrown(BusinessException)
    }

    def "throwIfTrue should not throw BusinessException when expression is false"() {
        given:
        def expression = false

        when:
        AssertUtil.throwIfTrue(expression, "Test BusinessException")

        then:
        noExceptionThrown()
    }

    def "throwIfNull should throw BusinessException when object is null"() {
        given:
        def obj = null

        when:
        AssertUtil.throwIfNull(obj, "Test BusinessException")

        then:
        thrown(BusinessException)
    }

    def "throwIfNull should not throw BusinessException when object is not null"() {
        given:
        def obj = "Not null"

        when:
        AssertUtil.throwIfNull(obj, "Test BusinessException")

        then:
        noExceptionThrown()
    }

    def "throwIfTrue with Code should throw BusinessException with message from Code when expression is true"() {
        given:
        def expression = true
        Code code = new Code() {
            @Override
            Integer value() {
                return 123;
            }

            @Override
            String desc() {
                return "Custom Description";
            }
        }

        when:
        AssertUtil.throwIfTrue(expression, code)

        then:
        thrown(BusinessException)
    }

    def "throwIfNull with Code should throw BusinessException with message from Code when object is null"() {
        given:
        def obj = null
        Code code = new Code() {
            @Override
            Integer value() {
                return 456;
            }

            @Override
            String desc() {
                return "Custom Description";
            }
        }

        when:
        AssertUtil.throwIfNull(obj, code)

        then:
        thrown(BusinessException)
    }

    def "throwIfTrue with Code and params should throw BusinessException with formatted message from Code when expression is true"() {
        given:
        def expression = true
        Code code = new Code() {
            @Override
            Integer value() {
                return 789;
            }

            @Override
            String desc() {
                return "Custom Description with %s";
            }
        }
        String[] params = ["parameter"]

        when:
        AssertUtil.throwIfTrue(expression, code, params)

        then:
        thrown(BusinessException)
    }

    def "throwIfNull with Code and params should throw BusinessException with formatted message from Code when object is null"() {
        given:
        def obj = null
        Code code = new Code() {
            @Override
            Integer value() {
                return 101;
            }

            @Override
            String desc() {
                return "Custom Description with %s";
            }
        }

        String[] params = ["parameter"]

        when:
        AssertUtil.throwIfNull(obj, code, params)

        then:
        thrown(BusinessException)
    }

    def "throwInvalidParamIfTrue should throw InvalidParamException when expression is true"() {
        given:
        def expression = true

        when:
        AssertUtil.throwInvalidParamIfTrue(expression, "Test InvalidParamException")

        then:
        thrown(InvalidParamException)
    }

    def "throwInvalidParamIfTrue should not throw InvalidParamException when expression is false"() {
        given:
        def expression = false

        when:
        AssertUtil.throwInvalidParamIfTrue(expression, "Test InvalidParamException")

        then:
        noExceptionThrown()
    }

    def "throwInvalidParamIfNull should throw InvalidParamException when object is null"() {
        given:
        def obj = null

        when:
        AssertUtil.throwInvalidParamIfNull(obj, "Test InvalidParamException")

        then:
        thrown(InvalidParamException)
    }

    def "throwInvalidParamIfNull should not throw InvalidParamException when object is not null"() {
        given:
        def obj = "Not null"

        when:
        AssertUtil.throwInvalidParamIfNull(obj, "Test InvalidParamException")

        then:
        noExceptionThrown()
    }

}
