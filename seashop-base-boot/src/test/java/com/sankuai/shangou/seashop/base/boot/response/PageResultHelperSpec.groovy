package com.sankuai.shangou.seashop.base.boot.response

import com.github.pagehelper.Page
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq
import com.sankuai.shangou.seashop.base.boot.response.model.OriginalItem
import com.sankuai.shangou.seashop.base.boot.response.model.TransformedItem
import spock.lang.Specification
import spock.lang.Subject

import java.util.function.BiConsumer
import java.util.function.Consumer
import java.util.function.Function

/**
 * <AUTHOR>
 * @date 2024/03/04 9:39
 */
@Subject(PageResultHelper)
class PageResultHelperSpec extends Specification {

    def "transfer method should convert Page to BasePageResp with proper data"() {
        given:
        def page = new Page()
        page.setTotal(1L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(1)
        page.add(new OriginalItem("data1"))

        when:
        def result = PageResultHelper.transfer(page, TransformedItem)

        then:
        result.totalCount == 1L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 1
        result.data.size() == 1
        result.data[0].data == "data1"
    }

    def "transfer method should convert Page to BasePageResp with proper data and apply convertConsumer"() {
        given:
        def page = new Page()
        page.setTotal(1L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(1)
        page.add(new OriginalItem("data1"))
        def convertConsumer = new Consumer<TransformedItem>() {
            @Override
            void accept(TransformedItem o) {
                o.data += "_converted"
            }
        }

        when:
        def result = PageResultHelper.transfer(page, TransformedItem, convertConsumer)

        then:
        result.totalCount == 1L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 1
        result.data.size() == 1
        result.data[0].data == "data1_converted"
    }

    def "transfer method should convert Page to BasePageResp with proper data and apply biConvertConsumer"() {
        given:
        def page = new Page()
        page.setTotal(1L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(1)
        page.add(new OriginalItem("data1"))
        def biConvertConsumer = new BiConsumer<OriginalItem, TransformedItem>() {
            @Override
            void accept(OriginalItem o, TransformedItem o2) {
                o2.data = o.data + "_biConverted"
            }
        }

        when:
        def result = PageResultHelper.transfer(page, TransformedItem, biConvertConsumer)

        then:
        result.totalCount == 1L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 1
        result.data.size() == 1
        result.data[0].data == "data1_biConverted"
    }

    def "transfer method should convert Page to BasePageResp with proper data and apply convertFunction"() {
        given:
        def page = new Page()
        page.setTotal(1L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(1)
        page.add(new OriginalItem("data1"))
        def convertFunction = { original -> new TransformedItem(data: original.data + "_convertedFunction") }

        when:
        def result = PageResultHelper.transfer(page, convertFunction)

        then:
        result.totalCount == 1L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 1
        result.data.size() == 1
        result.data[0].data == "data1_convertedFunction"
    }

    def "transfer method should handle empty page"() {
        given:
        def emptyPage = new Page()

        when:
        def result = PageResultHelper.transfer(emptyPage, TransformedItem)

        then:
        result.totalCount == 0L
        result.pageNo == 0
        result.pageSize == 0
        result.pages == 0
        result.data.size() == 0
    }

    def "defaultEmpty method should handle Page input"() {
        given:
        def page = new Page()

        when:
        def result = PageResultHelper.defaultEmpty(page)

        then:
        result.totalCount == 0L
        result.pageNo == page.pageNum
        result.pageSize == page.pageSize
        result.pages == page.pages
        result.data.size() == 0
    }

    def "defaultEmpty method should handle BasePageReq input"() {
        given:
        def pageReq = new BasePageReq()

        when:
        def result = PageResultHelper.defaultEmpty(pageReq)

        then:
        result.totalCount == 0L
        result.pageNo == pageReq.pageNo
        result.pageSize == pageReq.pageSize
        result.pages == 0
        result.data.size() == 0
    }

    def "defaultEmpty method should handle BasePageParam input"() {
        given:
        def pageParam = new BasePageParam()

        when:
        def result = PageResultHelper.defaultEmpty(pageParam)

        then:
        result.totalCount == 0L
        result.pageNo == pageParam.pageNum
        result.pageSize == pageParam.pageSize
        result.pages == 0
        result.data.size() == 0
    }

    def "transfer method should handle empty result list"() {
        given:
        def page = new Page()
        page.setTotal(0L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(0)

        when:
        def result = PageResultHelper.transfer(page, TransformedItem)

        then:
        result.totalCount == 0L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 0
        result.data.size() == 0
    }

    def "transfer method should handle non-empty result list with null values"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem(null))
        page.add(new OriginalItem(null))

        when:
        def result = PageResultHelper.transfer(page, TransformedItem)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == null
        result.data[1].data == null
    }

    def "transfer method should handle non-empty result list with valid values"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem("data1"))
        page.add(new OriginalItem("data2"))

        when:
        def result = PageResultHelper.transfer(page, TransformedItem)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == "data1"
        result.data[1].data == "data2"
    }

    def "transfer method should handle non-empty result list with valid values and apply convertConsumer"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem("data1"))
        page.add(new OriginalItem("data2"))
        def convertConsumer = new Consumer<TransformedItem>() {
            @Override
            void accept(TransformedItem o) {
                o.data += "_converted"
            }
        }

        when:
        def result = PageResultHelper.transfer(page, TransformedItem, convertConsumer)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == "data1_converted"
        result.data[1].data == "data2_converted"
    }

    def "transfer method should handle non-empty result list with valid values and apply biConvertConsumer"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem("data1"))
        page.add(new OriginalItem("data2"))
        def biConvertConsumer = new BiConsumer<OriginalItem, TransformedItem>() {
            @Override
            void accept(OriginalItem o, TransformedItem o2) {
                o2.data = o.data + "_biConverted"
            }
        }

        when:
        def result = PageResultHelper.transfer(page, TransformedItem, biConvertConsumer)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == "data1_biConverted"
        result.data[1].data == "data2_biConverted"
    }

    def "transfer method should handle non-empty result list with valid values and apply convertFunction"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem("data1"))
        page.add(new OriginalItem("data2"))
        def convertFunction = { original -> new TransformedItem(data: original.data + "_convertedFunction") }

        when:
        def result = PageResultHelper.transfer(page, convertFunction)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == "data1_convertedFunction"
        result.data[1].data == "data2_convertedFunction"
    }

    def "transfer method should handle non-empty result list with valid values and apply convertConsumer with null values"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem(null))
        page.add(new OriginalItem(null))
        def convertConsumer = new Consumer<TransformedItem>() {
            @Override
            void accept(TransformedItem o) {
                o.data = null
            }
        }

        when:
        def result = PageResultHelper.transfer(page, TransformedItem, convertConsumer)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == null
        result.data[1].data == null
    }

    def "transfer method should handle non-empty result list with valid values and apply biConvertConsumer with null values"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem(null))
        page.add(new OriginalItem(null))
        def biConvertConsumer = new BiConsumer<OriginalItem, TransformedItem>() {
            @Override
            void accept(OriginalItem o, TransformedItem o2) {
                o2.data = null
            }
        }

        when:
        def result = PageResultHelper.transfer(page, TransformedItem, biConvertConsumer)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == null
        result.data[1].data == null
    }

    def "transfer method should handle non-empty result list with valid values and apply convertFunction with null values"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem(null))
        page.add(new OriginalItem(null))
        def convertFunction = new Function<OriginalItem, TransformedItem>() {
            @Override
            TransformedItem apply(OriginalItem o) {
                TransformedItem o1 = new TransformedItem()
                o1.setData(o.data)
                return o1
            }
        }

        when:
        def result = PageResultHelper.transfer(page, convertFunction)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == null
        result.data[1].data == null
    }

    def "transfer method should handle non-empty result list with valid values and apply convertConsumer with empty values"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem(""))
        page.add(new OriginalItem(""))
        def convertConsumer = new Consumer<TransformedItem>() {
            @Override
            void accept(TransformedItem o) {
                o.data += "_converted"
            }
        }

        when:
        def result = PageResultHelper.transfer(page, TransformedItem, convertConsumer)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == "_converted"
        result.data[1].data == "_converted"
    }

    def "transfer method should handle non-empty result list with valid values and apply biConvertConsumer with empty values"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem(""))
        page.add(new OriginalItem(""))
        def biConvertConsumer = new BiConsumer<OriginalItem, TransformedItem>() {
            @Override
            void accept(OriginalItem o, TransformedItem o2) {
                o2.data = o.data + "_biConverted"
            }
        }

        when:
        def result = PageResultHelper.transfer(page, TransformedItem, biConvertConsumer)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == "_biConverted"
        result.data[1].data == "_biConverted"
    }

    def "transfer method should handle non-empty result list with valid values and apply convertFunction with empty values"() {
        given:
        def page = new Page()
        page.setTotal(2L)
        page.setPageNum(1)
        page.setPageSize(1)
        page.setPages(2)
        page.add(new OriginalItem(""))
        page.add(new OriginalItem(""))
        def convertFunction = { original -> new TransformedItem(data: original.data + "_convertedFunction") }

        when:
        def result = PageResultHelper.transfer(page, convertFunction)

        then:
        result.totalCount == 2L
        result.pageNo == 1
        result.pageSize == 1
        result.pages == 2
        result.data.size() == 2
        result.data[0].data == "_convertedFunction"
        result.data[1].data == "_convertedFunction"
    }

    def "test transfer(BasePageResp<D> fromPage, Class<R> resultClz)"() {
        given:
        def originalPage = new BasePageResp<OriginalItem>()
        originalPage.setTotalCount(10)
        originalPage.setPageNo(1)
        originalPage.setPageSize(10)
        originalPage.setPages(1)
        def originalItemList = []
        originalItemList.add(new OriginalItem(data: "data1"))
        originalItemList.add(new OriginalItem(data: "data2"))
        originalPage.setData(originalItemList)

        when:
        def result = PageResultHelper.transfer(originalPage, TransformedItem)

        then:
        result.totalCount == 10
        result.pageNo == 1
        result.pageSize == 10
        result.pages == 1
        result.data.size() == 2
        result.data[0].data == "data1"
        result.data[1].data == "data2"
    }

    def "test transfer(BasePageResp<D> fromPage, Class<R> resultClz, Consumer<R> convertConsumer)"() {
        given:
        def originalPage = new BasePageResp<OriginalItem>()
        originalPage.setTotalCount(10)
        originalPage.setPageNo(1)
        originalPage.setPageSize(10)
        originalPage.setPages(1)
        def originalItemList = []
        originalItemList.add(new OriginalItem(data: "data1"))
        originalItemList.add(new OriginalItem(data: "data2"))
        originalPage.setData(originalItemList)

        when:
        def result = PageResultHelper.transfer(originalPage, TransformedItem,
                new Consumer<TransformedItem>() {
                    @Override
                    void accept(TransformedItem item) {
                        item.setData(item.getData().toUpperCase())
                    }
                })

        then:
        result.totalCount == 10
        result.pageNo == 1
        result.pageSize == 10
        result.pages == 1
        result.data.size() == 2
        result.data[0].data == "DATA1"
        result.data[1].data == "DATA2"
    }

    def "test transfer(BasePageResp<D> fromPage, Class<R> resultClz, BiConsumer<D, R> convertConsumer)"() {
        given:
        def originalPage = new BasePageResp<OriginalItem>()
        originalPage.setTotalCount(10)
        originalPage.setPageNo(1)
        originalPage.setPageSize(10)
        originalPage.setPages(1)
        def originalItemList = []
        originalItemList.add(new OriginalItem(data: "data1"))
        originalItemList.add(new OriginalItem(data: "data2"))
        originalPage.setData(originalItemList)

        when:
        def result = PageResultHelper.transfer(originalPage, TransformedItem, new BiConsumer<OriginalItem, TransformedItem>() {
            @Override
            void accept(OriginalItem originalItem, TransformedItem transformedItem) {
                transformedItem.setData(originalItem.getData().toUpperCase())
            }
        })

        then:
        result.totalCount == 10
        result.pageNo == 1
        result.pageSize == 10
        result.pages == 1
        result.data.size() == 2
        result.data[0].data == "DATA1"
        result.data[1].data == "DATA2"
    }

    def "test transfer(BasePageResp<D> fromPage, Function<D, R> convertFunction)"() {
        given:
        def originalPage = new BasePageResp<OriginalItem>()
        originalPage.setTotalCount(10)
        originalPage.setPageNo(1)
        originalPage.setPageSize(10)
        originalPage.setPages(1)
        def originalItemList = []
        originalItemList.add(new OriginalItem(data: "data1"))
        originalItemList.add(new OriginalItem(data: "data2"))
        originalPage.setData(originalItemList)

        when:
        def result = PageResultHelper.transfer(originalPage) { originalItem ->
            new TransformedItem(data: originalItem.getData().toUpperCase())
        }

        then:
        result.totalCount == 10
        result.pageNo == 1
        result.pageSize == 10
        result.pages == 1
        result.data.size() == 2
        result.data[0].data == "DATA1"
        result.data[1].data == "DATA2"
    }

    def "test getData(BasePageResp<R> pageResp)"() {
        given:
        def pageResp = new BasePageResp<OriginalItem>()
        def data = [new OriginalItem(data: "data1"), new OriginalItem(data: "data2")]
        pageResp.setData(data)

        when:
        def result = PageResultHelper.getData(pageResp)

        then:
        result == data
    }


}