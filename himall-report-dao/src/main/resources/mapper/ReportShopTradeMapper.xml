<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.himall.report.dao.mapper.ReportShopTradeMapper">
    <select id="getNewShops" resultType="com.hishop.himall.report.dao.models.IncreaseSource">
        select
        <choose>
            <when test="range == 'DAY'">Date( opening_time ) as date,</when>
            <when test="range == 'WEEK'">YEARWEEK( opening_time ) as week,</when>
            <when test="range == 'MONTH'">
                YEAR( opening_time ) as year,
                MONTH( opening_time ) as month,
            </when>
        </choose>
            COUNT( shop_id ) as count
        from report_source_shop
        where date(opening_time) between  #{start} and #{end}
        group by
        <choose>
            <when test="range == 'DAY'">Date( opening_time )</when>
            <when test="range == 'WEEK'">YEARWEEK( opening_time )</when>
            <when test="range == 'MONTH'">year,month</when>
        </choose>
    </select>

    <select id="getProvinceShops" resultType="com.hishop.himall.report.dao.models.ProvinceSource">
        select province_id,r.name as province_name,COUNT( shop_id ) as shops
        from report_source_shop s
        left join report_source_region r
        on s.province_id = r.id
        group by province_id
    </select>

    <select id="getShopTrades" resultType="com.hishop.himall.report.dao.models.ShopTrade">
        select
        t.shop_id,
        s.shop_name,
        sum( payment_amount ) as payment_amount,
        sum( payment_orders ) as payment_orders,
        sum( refund_amount ) as refund_amount,
        sum( refund_orders ) as refund_orders,
        sum( visits_users ) as visits_users,
        sum( visits_count ) as visits_count
        from report_shop_trade t
        left join report_source_shop s
        on t.shop_id = s.shop_id
        where t.shop_id > 0 and t.`range` = #{range} and `date` between #{start} and #{end}
        <if test="shopName != null and shopName != ''">
            and s.shop_name like concat('%',#{shopName},'%')
        </if>
        group by t.shop_id
    </select>
    <select id="getTradeSummary" resultType="com.hishop.himall.report.dao.models.TradeSummary">
        select
        sum( visits_users ) as visits_users,
        sum( visits_count ) as visits_count,
        sum( order_amount ) as order_amount,
        sum( order_orders ) as order_orders,
        sum( order_users ) as order_users,
        sum( payment_amount ) as payment_amount,
        sum( payment_quantity ) as payment_quantity,
        sum( payment_orders ) as payment_orders,
        sum( payment_users ) as payment_users
        from report_shop_trade
        where `range` = #{range}
          <if test="shopId!= null">
            and shop_id = #{shopId}
          </if>
          and date between #{start} and #{end}
    </select>

    <select id="getTrades" resultType="com.hishop.himall.report.dao.models.TradeSummary">
        select
        date,
        visits_users,
        visits_count,
        order_amount,
        order_orders,
        order_users,
        payment_amount,
        payment_quantity,
        payment_orders,
        payment_users
        from report_shop_trade
        where shop_id = #{shopId} and `range` = #{range} and date between #{start} and #{end}
    </select>

    <select id="getPriovinceTrades" resultType="com.hishop.himall.report.dao.models.ProvinceSource">
        select
            t.province_id,
            r.name as provinceName,
            sum( payment_amount ) as paymentAmount,
            count( distinct order_id ) as paymentOrders,
            count( distinct user_id ) as paymentUsers
        from report_source_order t
                 left join report_source_region r
                           on t.province_id = r.id
        where DATE(payment_time) between #{start} and #{end}
        <if test="shopId != null">
            and shop_id = #{shopId}
        </if>
        group by t.province_id
    </select>
</mapper>
