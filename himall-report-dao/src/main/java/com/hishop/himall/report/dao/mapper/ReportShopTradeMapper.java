package com.hishop.himall.report.dao.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.himall.report.dao.domain.ReportShopTrade;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.himall.report.dao.models.IncreaseSource;
import com.hishop.himall.report.dao.models.ProvinceSource;
import com.hishop.himall.report.dao.models.ShopTrade;
import com.hishop.himall.report.dao.models.TradeSummary;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 门店交易统计表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-12
 */
public interface ReportShopTradeMapper extends BaseMapper<ReportShopTrade> {

    /**
     * 获取新增店铺
     * @param range 日期维度
     * @param start 开始时间
     * @param end 结束时间
     * @return 新增店铺
     */
    List<IncreaseSource> getNewShops(@Param("range") String range,@Param("start") Date start,@Param("end") Date end);

    /**
     * 省份新增门店
     * @param range 日期维度
     * @param start 开始时间
     * @param end 结束时间
     * @return 省份新增门店
     */
    List<ProvinceSource> getProvinceShops(@Param("range") String range,@Param("start") Date start,@Param("end") Date end);

    /**
     * 获取门店交易
     * @param range 日期维度
     * @param start 开始时间
     * @param end 结束时间
     * @param shopName 门店名称
     * @return
     */
    List<ShopTrade> getShopTrades(@Param("range") String range,@Param("start") Date start,@Param("end") Date end,@Param("shopName") String shopName);

    /**
     * 获取门店交易
     *
     * @param page     分页
     * @param range    日期维度
     * @param start    开始时间
     * @param end      结束时间
     * @param shopName 门店名称
     * @return
     */
    Page<ShopTrade> getShopTrades(@Param("page") Page<ShopTrade> page,
                                  @Param("range") String range, @Param("start") Date start,
                                  @Param("end") Date end,
                                  @Param("shopName") String shopName);

    /**
     * 获取交易概览
     * @param range 日期维度
     * @param start 开始时间
     * @param end 结束时间
     * @return 交易概览
     */
    TradeSummary getTradeSummary(@Param("shopId") Long shopId, @Param("range") String range,@Param("start") Date start,@Param("end") Date end);

    /**
     * 获取交易概览
     * @param range 日期维度
     * @param start 开始时间
     * @param end 结束时间
     * @return 交易概览
     */
    List<TradeSummary> getTrades(@Param("range") String range,@Param("start") Date start,@Param("end") Date end,@Param("shopId") Long shopId);

    /**
     * 获取交易概览
     * @param page 分页
     * @param range 日期维度
     * @param start 开始时间
     * @param end 结束时间
     * @return 交易概览
     */
    Page<TradeSummary> getTrades(@Param("page") Page<TradeSummary> page, @Param("range") String range,@Param("start") Date start,@Param("end") Date end,@Param("shopId") Long shopId);

    /**
     * 获取省份交易
     * @param range 日期维度
     * @param start 开始时间
     * @param end 结束时间
     * @param shopId 门店id
     * @return 省份交易
     */
    List<ProvinceSource> getPriovinceTrades(@Param("range") String range,@Param("start") Date start,@Param("end") Date end, @Param("shopId")Long shopId);
}
