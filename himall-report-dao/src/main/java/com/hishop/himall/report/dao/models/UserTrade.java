package com.hishop.himall.report.dao.models;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Setter
public class UserTrade {
    private Integer userId;
    private String nickname;
    private String phone;
    private LocalDateTime registrationTime;
    private Integer order_orders;
    private BigDecimal paymentAmount;
    private Integer paymentOrders;
    private Integer paymentQuantity;
    private BigDecimal paymentOrderAmount;
    private BigDecimal paymentProductAmount;
    private BigDecimal refundAmount;
    private Integer refundOrders;
    public BigDecimal getConsumptionAmount(){
        if (paymentOrderAmount == null) paymentOrderAmount = BigDecimal.ZERO;
        if (refundAmount == null) refundAmount = BigDecimal.ZERO;
        return paymentOrderAmount.subtract(refundAmount);
    }

}
