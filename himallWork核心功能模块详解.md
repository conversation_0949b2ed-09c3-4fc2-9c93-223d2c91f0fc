# himallWork核心功能模块详解

## 1. 区域禁售校验功能详解

### 1.1 功能背景
在电商业务中，由于法律法规、物流限制、商品特性等原因，某些商品可能不能销售到特定地区。区域禁售校验功能确保这些限制得到严格执行，避免违规销售。

### 1.2 实现原理

#### 核心类和方法
```java
// 运费计算助手类
public class FreightCalculateAssistant {
    /**
     * 校验区域禁售限制并移除禁售商品
     */
    private void checkIfAreaForbiddenAndRemoveProductIfNecessary(
        String shippingRegionPath, 
        List<CalculateFreightProductBo> productList,
        List<Long> templateIdList, 
        boolean ignoreForbidden) {
        // 校验逻辑实现
    }
    
    /**
     * 公共校验方法，用于订单提交时严格校验
     */
    public void validateAreaForbidden(
        String shippingRegionPath, 
        List<CalculateFreightProductBo> productList,
        List<Long> templateIdList, 
        boolean ignoreForbidden) {
        this.checkIfAreaForbiddenAndRemoveProductIfNecessary(
            shippingRegionPath, productList, templateIdList, ignoreForbidden);
    }
}
```

#### 预订单服务中的应用
```java
public class PreOrderServiceImpl implements PreOrderService {
    /**
     * 在订单提交时校验区域禁售限制
     */
    private void validateAreaForbidden(SubmitOrderContext buildContext, BuildResult buildResult) {
        ShippingAddressBo shippingAddress = buildContext.getShippingAddress();
        if (shippingAddress == null || shippingAddress.getRegionPath() == null) {
            return;
        }

        for (ShopProductListBo shopProduct : buildResult.getShopProductList()) {
            List<CalculateFreightProductBo> productList = convertToFreightProducts(shopProduct);
            List<Long> templateIdList = extractTemplateIds(productList);

            // 严格校验，不允许忽略禁售区域
            freightCalculateAssistant.validateAreaForbidden(
                shippingAddress.getRegionPath(), 
                productList, 
                templateIdList, 
                false
            );
        }
    }
}
```

### 1.3 校验流程

```mermaid
flowchart TD
    A[用户选择收货地址] --> B[获取地址区域路径]
    B --> C[获取购物车商品列表]
    C --> D[遍历商品运费模板]
    D --> E{检查模板禁售区域配置}
    E -->|无禁售配置| F[允许配送]
    E -->|有禁售配置| G{当前地址是否在禁售区域}
    G -->|不在禁售区域| F
    G -->|在禁售区域| H[标记为禁售商品]
    H --> I{是否忽略禁售}
    I -->|预览模式-忽略| J[移除禁售商品继续]
    I -->|提交模式-严格| K[抛出业务异常]
    F --> L[校验通过]
    J --> L
    K --> M[提示用户配送限制]
```

### 1.4 配置管理

#### 运费模板配置
- 在运费模板中配置禁售区域
- 支持省、市、区多级区域配置
- 支持批量配置和单独配置
- 支持配置原因说明

#### 数据库设计
```sql
-- 运费模板表
CREATE TABLE freight_template (
    id BIGINT PRIMARY KEY,
    shop_id BIGINT NOT NULL,
    template_name VARCHAR(100),
    is_free_shipping TINYINT DEFAULT 0,
    create_time DATETIME,
    update_time DATETIME
);

-- 运费区域配置表
CREATE TABLE freight_area (
    id BIGINT PRIMARY KEY,
    template_id BIGINT NOT NULL,
    region_path VARCHAR(500), -- 区域路径，如：1,2,3
    is_forbidden TINYINT DEFAULT 0, -- 是否禁售
    freight_fee DECIMAL(10,2), -- 运费
    free_shipping_amount DECIMAL(10,2), -- 包邮金额
    create_time DATETIME
);
```

### 1.5 业务场景

#### 场景1：预览订单
- 用户在预览订单时，系统自动过滤禁售商品
- 给出友好提示："部分商品不支持配送到该地区"
- 用户可以修改收货地址或移除禁售商品

#### 场景2：提交订单
- 严格校验，不允许禁售商品下单
- 如发现禁售商品，直接抛出异常
- 阻止订单创建，保证业务合规

#### 场景3：地址变更
- 用户修改收货地址时，重新校验商品配送限制
- 实时提示哪些商品不能配送到新地址

---

## 2. 用户数据加密功能详解

### 2.1 功能背景
为保护用户隐私和满足数据安全合规要求，系统对用户敏感信息进行AES加密存储。

### 2.2 加密实现

#### 核心加密工具类
```java
public class AesUtil {
    // 固定IV偏移量，保证相同明文产生相同密文
    private static final byte[] IV = {18, 52, 86, 120, 144, 171, 205, 239, 
                                     18, 52, 86, 120, 144, 171, 205, 239};
    
    /**
     * AES加密
     * @param encryptStr 待加密字符串
     * @param encryptKey 加密密钥（32字节）
     * @return 加密后的Base64字符串
     */
    public static String encrypt(String encryptStr, String encryptKey) {
        try {
            // 密钥处理：不足32字节用空格填充，超过32字节截取
            String key = String.format("%-32s", encryptKey).substring(0, 32);
            
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(IV);
            
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            byte[] encrypted = cipher.doFinal(encryptStr.getBytes("UTF-8"));
            
            return Base64.getEncoder().encodeToString(encrypted);
        } catch (Exception e) {
            throw new BusinessException("加密失败", e);
        }
    }
    
    /**
     * AES解密
     * @param decryptStr 待解密的Base64字符串
     * @param decryptKey 解密密钥
     * @return 解密后的明文
     */
    public static String decrypt(String decryptStr, String decryptKey) {
        try {
            String key = String.format("%-32s", decryptKey).substring(0, 32);
            
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            SecretKeySpec keySpec = new SecretKeySpec(key.getBytes(), "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(IV);
            
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(decryptStr));
            
            return new String(decrypted, "UTF-8");
        } catch (Exception e) {
            throw new BusinessException("解密失败", e);
        }
    }
}
```

#### 加密配置管理
```java
@Configuration
@Getter
public class EncryptConfig {
    @Value("${seashop.aes.secret:'ae125efkk4454eeff444ferfkny6oxi8'}")
    private String aesSecret;
}
```

### 2.3 应用场景

#### 用户注册时加密
```java
@Service
public class MemberRegisterService {
    @Resource
    private EncryptConfig encryptConfig;
    
    public void register(String phone, String password) {
        Member member = new Member();
        // 手机号加密存储
        member.setCellPhone(AesUtil.encrypt(phone, encryptConfig.getAesSecret()));
        member.setUserName(phone); // 用户名使用明文（用于登录）
        
        memberRepository.save(member);
    }
}
```

#### 用户查询时处理
```java
@Service
public class MemberServiceImpl implements MemberService {
    
    public List<Member> queryMembers(QueryMemberListDto queryDto) {
        // 查询条件中的手机号需要加密
        if (StrUtil.isNotBlank(queryDto.getMobile())) {
            queryDto.setMobile(AesUtil.encrypt(queryDto.getMobile(), encryptConfig.getAesSecret()));
        }
        
        List<Member> members = memberRepository.queryList(queryDto);
        
        // 返回结果需要解密
        for (Member member : members) {
            try {
                member.setCellPhone(AesUtil.decrypt(member.getCellPhone(), encryptConfig.getAesSecret()));
            } catch (Exception e) {
                log.warn("手机号解密失败，用户ID：{}", member.getId());
                member.setCellPhone(""); // 解密失败时返回空字符串
            }
        }
        
        return members;
    }
}
```

#### 收货地址加密
```java
@Service
public class ShippingAddressServiceImpl implements ShippingAddressService {
    
    public void saveAddress(ShippingAddressBo addressBo) {
        ShippingAddress address = new ShippingAddress();
        
        // 敏感信息加密存储
        address.setShipTo(AesUtil.encrypt(addressBo.getShipTo(), encryptConfig.getAesSecret()));
        address.setPhone(AesUtil.encrypt(addressBo.getPhone(), encryptConfig.getAesSecret()));
        address.setAddress(AesUtil.encrypt(addressBo.getAddress(), encryptConfig.getAesSecret()));
        address.setAddressDetail(AesUtil.encrypt(addressBo.getAddressDetail(), encryptConfig.getAesSecret()));
        
        shippingAddressRepository.save(address);
    }
    
    public List<ShippingAddress> getUserAddresses(Long userId) {
        List<ShippingAddress> addresses = shippingAddressRepository.getByUserId(userId);
        
        // 返回时解密
        for (ShippingAddress address : addresses) {
            address.setShipTo(AesUtil.decrypt(address.getShipTo(), encryptConfig.getAesSecret()));
            address.setPhone(AesUtil.decrypt(address.getPhone(), encryptConfig.getAesSecret()));
            address.setAddress(AesUtil.decrypt(address.getAddress(), encryptConfig.getAesSecret()));
            address.setAddressDetail(AesUtil.decrypt(address.getAddressDetail(), encryptConfig.getAesSecret()));
        }
        
        return addresses;
    }
}
```

### 2.4 加密字段范围

#### 用户相关
- **Member表**：`cell_phone`（手机号）
- **Manager表**：`cellphone`（管理员手机号）

#### 地址相关
- **ShippingAddress表**：
  - `ship_to`（收货人姓名）
  - `phone`（收货人手机号）
  - `address`（收货地址）
  - `address_detail`（详细地址）

#### 店铺相关
- **Shop表**：
  - `id_card`（身份证号）
  - `bank_account_number`（银行账号）

### 2.5 查询策略

#### 精确查询
```java
// 手机号精确查询，需要先加密查询条件
String encryptedPhone = AesUtil.encrypt(phone, encryptConfig.getAesSecret());
Member member = memberRepository.getByPhone(encryptedPhone);
```

#### 模糊查询
```java
// 由于加密后无法进行模糊匹配，通过关联联系人表查询
/**
 * 根据手机号模糊查询用户id
 * 因为用户表手机号码是加密的，所以是关联联系人表查询的
 */
public List<Long> getUserIdsByPhoneLike(String phone) {
    // 通过联系人表进行模糊查询
    return memberContactRepository.getUserIdsByPhoneLike(phone);
}
```

---

## 3. 报表数据收集机制详解

### 3.1 功能背景
为了支持数据驱动的业务决策，系统需要收集各种业务数据并进行统计分析。报表系统采用数据源表 + 定时汇总的架构模式。

### 3.2 数据源表设计

#### 订单数据源表
```sql
CREATE TABLE report_source_order (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    order_id BIGINT NOT NULL COMMENT '订单ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    shop_id BIGINT NOT NULL COMMENT '店铺ID',
    province_id BIGINT COMMENT '省份ID',
    order_time DATETIME COMMENT '下单时间',
    payment_time DATETIME COMMENT '支付时间',
    finish_time DATETIME COMMENT '完成时间',
    payment_amount DECIMAL(10,2) COMMENT '支付金额',
    delivery_time DATETIME COMMENT '发货时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_order_id (order_id),
    INDEX idx_user_id (user_id),
    INDEX idx_shop_id (shop_id),
    INDEX idx_order_time (order_time)
) COMMENT '订单源数据表';
```

#### 用户数据源表
```sql
CREATE TABLE report_source_user (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    register_time DATETIME COMMENT '注册时间',
    province_id BIGINT COMMENT '省份ID',
    user_type TINYINT COMMENT '用户类型',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_user_id (user_id),
    INDEX idx_register_time (register_time)
) COMMENT '用户源数据表';
```

#### 商品数据源表
```sql
CREATE TABLE report_source_product (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    product_id BIGINT NOT NULL COMMENT '商品ID',
    product_name VARCHAR(200) COMMENT '商品名称',
    product_spu VARCHAR(100) COMMENT '商品编码',
    thumbnail_url VARCHAR(500) COMMENT '商品缩略图',
    category_first VARCHAR(100) COMMENT '一级分类名称',
    category_second VARCHAR(100) COMMENT '二级分类名称',
    shop_id BIGINT COMMENT '店铺ID',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_product_id (product_id),
    INDEX idx_shop_id (shop_id)
) COMMENT '商品源数据表';
```

### 3.3 数据收集接口

#### 报表控制器
```java
@RestController
@RequestMapping("/report")
@Slf4j
public class ReportController {
    
    @Resource
    private ReportService reportService;
    
    /**
     * 创建订单源数据
     */
    @PostMapping("/createSourceOrder")
    public Result<Boolean> createSourceOrder(@RequestBody CreateSourceOrderReq req) {
        try {
            reportService.createSourceOrder(req);
            return Result.success(true);
        } catch (Exception e) {
            log.error("创建订单源数据失败", e);
            return Result.fail("创建失败");
        }
    }
    
    /**
     * 批量创建订单源数据
     */
    @PostMapping("/batchCreateSourceOrder")
    public Result<Boolean> batchCreateSourceOrder(@RequestBody List<CreateSourceOrderReq> reqList) {
        try {
            reportService.batchCreateSourceOrder(reqList);
            return Result.success(true);
        } catch (Exception e) {
            log.error("批量创建订单源数据失败", e);
            return Result.fail("创建失败");
        }
    }
    
    /**
     * 创建用户源数据
     */
    @PostMapping("/createSourceUser")
    public Result<Boolean> createSourceUser(@RequestBody CreateSourceUserReq req) {
        try {
            reportService.createSourceUser(req);
            return Result.success(true);
        } catch (Exception e) {
            log.error("创建用户源数据失败", e);
            return Result.fail("创建失败");
        }
    }
    
    // 其他数据源创建接口...
}
```

#### 报表服务实现
```java
@Service
public class ReportServiceImpl implements ReportService {
    
    @Resource
    private ReportSourceOrderService reportSourceOrderService;
    
    @Override
    public void createSourceOrder(CreateSourceOrderReq req) {
        ReportSourceOrder sourceOrder = new ReportSourceOrder();
        BeanUtils.copyProperties(req, sourceOrder);
        sourceOrder.setCreateTime(new Date());
        
        reportSourceOrderService.save(sourceOrder);
    }
    
    @Override
    @Transactional
    public void batchCreateSourceOrder(List<CreateSourceOrderReq> reqList) {
        if (CollectionUtils.isEmpty(reqList)) {
            return;
        }
        
        // 分页处理，避免内存溢出
        List<List<CreateSourceOrderReq>> partitions = PageUtils.split(reqList, 1000);
        
        for (List<CreateSourceOrderReq> partition : partitions) {
            List<ReportSourceOrder> sourceOrders = partition.parallelStream()
                .map(req -> {
                    ReportSourceOrder sourceOrder = new ReportSourceOrder();
                    BeanUtils.copyProperties(req, sourceOrder);
                    sourceOrder.setCreateTime(new Date());
                    return sourceOrder;
                })
                .collect(Collectors.toList());
            
            // 批量插入
            reportSourceOrderService.saveBatch(sourceOrders);
        }
    }
}
```

### 3.4 数据同步机制

#### 实时同步
```java
@Service
public class OrderServiceImpl implements OrderService {
    
    @Resource
    private ReportRemoteService reportRemoteService;
    
    @Override
    @Transactional
    public void createOrder(CreateOrderReq req) {
        // 创建订单
        Order order = buildOrder(req);
        orderRepository.save(order);
        
        // 实时推送数据到报表系统
        CreateSourceOrderReq reportReq = new CreateSourceOrderReq();
        reportReq.setOrderId(order.getId());
        reportReq.setUserId(order.getUserId());
        reportReq.setShopId(order.getShopId());
        reportReq.setOrderTime(order.getCreateTime());
        reportReq.setPaymentAmount(order.getTotalAmount());
        
        // 异步推送，避免影响主业务
        CompletableFuture.runAsync(() -> {
            try {
                reportRemoteService.createSourceOrder(reportReq);
            } catch (Exception e) {
                log.error("推送订单数据到报表系统失败，订单ID：{}", order.getId(), e);
            }
        });
    }
}
```

#### 批量同步
```java
@Component
public class ReportDataSyncTask {
    
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private ReportRemoteService reportRemoteService;
    
    /**
     * 每日凌晨3点执行订单数据同步
     */
    @XxlJob("syncOrderDataTask")
    public void syncOrderData() {
        log.info("开始同步订单数据到报表系统");
        
        LocalDate yesterday = LocalDate.now().minusDays(1);
        LocalDateTime startTime = yesterday.atStartOfDay();
        LocalDateTime endTime = yesterday.atTime(23, 59, 59);
        
        // 分页查询昨日订单
        int pageSize = 1000;
        int pageNum = 1;
        
        while (true) {
            List<Order> orders = orderRepository.getOrdersByTimeRange(
                startTime, endTime, pageNum, pageSize);
            
            if (CollectionUtils.isEmpty(orders)) {
                break;
            }
            
            // 转换为报表数据格式
            List<CreateSourceOrderReq> reportReqs = orders.stream()
                .map(this::convertToReportReq)
                .collect(Collectors.toList());
            
            // 批量推送
            try {
                reportRemoteService.batchCreateSourceOrder(reportReqs);
                log.info("同步订单数据成功，数量：{}", reportReqs.size());
            } catch (Exception e) {
                log.error("同步订单数据失败，页码：{}", pageNum, e);
            }
            
            pageNum++;
        }
        
        log.info("订单数据同步完成");
    }
    
    private CreateSourceOrderReq convertToReportReq(Order order) {
        CreateSourceOrderReq req = new CreateSourceOrderReq();
        req.setOrderId(order.getId());
        req.setUserId(order.getUserId());
        req.setShopId(order.getShopId());
        req.setOrderTime(order.getCreateTime());
        req.setPaymentTime(order.getPaymentTime());
        req.setFinishTime(order.getFinishTime());
        req.setPaymentAmount(order.getTotalAmount());
        return req;
    }
}
```

### 3.5 数据汇总统计

#### 用户统计服务
```java
@Service
public class ReportUserServiceImpl implements ReportUserService {
    
    @Resource
    private ReportSourceOrderService reportSourceOrderService;
    @Resource
    private ReportUserTradeService reportUserTradeService;
    
    @Override
    public void settlement(String range, LocalDate start, LocalDate end) {
        log.info("开始用户统计结算，范围：{}，开始时间：{}，结束时间：{}", range, start, end);
        
        // 查询时间范围内的所有用户交易数据
        List<ReportUserTrade> trades = new ArrayList<>();
        
        // 下单统计
        fillUserOrderTrades(trades, start, end);
        
        // 支付订单统计
        fillUserPaymentOrderTrades(trades, start, end);
        
        // 支付商品统计
        fillUserPaymentItemsTrades(trades, start, end);
        
        // 售后统计
        fillUserRefundTrades(trades, start, end);
        
        // 批量保存统计结果
        if (!CollectionUtils.isEmpty(trades)) {
            reportUserTradeService.saveBatch(trades);
        }
        
        log.info("用户统计结算完成，统计记录数：{}", trades.size());
    }
    
    private void fillUserOrderTrades(List<ReportUserTrade> trades, LocalDate start, LocalDate end) {
        // 按用户统计下单数据
        String sql = """
            SELECT user_id, shop_id, COUNT(*) as order_count, SUM(payment_amount) as total_amount
            FROM report_source_order 
            WHERE DATE(order_time) BETWEEN ? AND ?
            GROUP BY user_id, shop_id
            """;
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, start, end);
        
        for (Map<String, Object> result : results) {
            ReportUserTrade trade = new ReportUserTrade();
            trade.setDate(start);
            trade.setRange(range);
            trade.setUserId(MapUtils.getLong(result, "user_id"));
            trade.setShopId(MapUtils.getLong(result, "shop_id"));
            trade.setOrderOrders(MapUtils.getInteger(result, "order_count"));
            trade.setOrderAmount(MapUtils.getBigDecimal(result, "total_amount"));
            trade.setCreateTime(new Date());
            
            trades.add(trade);
        }
    }
    
    private void fillUserPaymentOrderTrades(List<ReportUserTrade> trades, LocalDate start, LocalDate end) {
        // 按用户统计支付订单数据
        String sql = """
            SELECT user_id, shop_id, COUNT(*) as payment_count, SUM(payment_amount) as payment_amount
            FROM report_source_order 
            WHERE DATE(payment_time) BETWEEN ? AND ? AND payment_time IS NOT NULL
            GROUP BY user_id, shop_id
            """;
        
        List<Map<String, Object>> results = jdbcTemplate.queryForList(sql, start, end);
        
        // 更新已存在的统计记录或创建新记录
        for (Map<String, Object> result : results) {
            Long userId = MapUtils.getLong(result, "user_id");
            Long shopId = MapUtils.getLong(result, "shop_id");
            
            ReportUserTrade trade = findOrCreateTrade(trades, userId, shopId, start, range);
            trade.setPaymentOrders(MapUtils.getInteger(result, "payment_count"));
            trade.setPaymentOrderAmount(MapUtils.getBigDecimal(result, "payment_amount"));
        }
    }
    
    // 其他统计方法...
}
```

### 3.6 定时任务调度

#### XXL-JOB任务配置
```java
@Component
public class ReportTask {
    
    @Resource
    private ReportUserService reportUserService;
    @Resource
    private ReportShopService reportShopService;
    @Resource
    private ReportProductService reportProductService;
    
    /**
     * 用户数据日结算
     */
    @XxlJob("userDailySettlement")
    public void userDailySettlement() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        reportUserService.settlement("day", yesterday, yesterday);
    }
    
    /**
     * 用户数据周结算
     */
    @XxlJob("userWeeklySettlement")
    public void userWeeklySettlement() {
        LocalDate endDate = LocalDate.now().minusDays(1);
        LocalDate startDate = endDate.minusDays(6);
        reportUserService.settlement("week", startDate, endDate);
    }
    
    /**
     * 用户数据月结算
     */
    @XxlJob("userMonthlySettlement")
    public void userMonthlySettlement() {
        LocalDate endDate = LocalDate.now().minusDays(1);
        LocalDate startDate = endDate.withDayOfMonth(1);
        reportUserService.settlement("month", startDate, endDate);
    }
    
    /**
     * 店铺数据结算
     */
    @XxlJob("shopSettlement")
    public void shopSettlement() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        List<Long> allShops = getAllActiveShops();
        reportShopService.settlement("day", yesterday, yesterday, allShops);
    }
    
    /**
     * 商品数据结算
     */
    @XxlJob("productSettlement")
    public void productSettlement() {
        LocalDate yesterday = LocalDate.now().minusDays(1);
        List<Long> allProducts = getAllActiveProducts();
        reportProductService.settlement("day", yesterday, yesterday, allProducts);
    }
}
```

---

## 4. 支付系统集成详解

### 4.1 支付架构设计

#### 统一支付接口
```java
public interface PayService {
    /**
     * 创建支付订单
     */
    PayOrderResp createPayOrder(CreatePayOrderReq req);
    
    /**
     * 查询支付状态
     */
    PayStatusResp queryPayStatus(String payOrderNo);
    
    /**
     * 支付回调处理
     */
    void handlePayCallback(String channel, String callbackData);
    
    /**
     * 申请退款
     */
    RefundResp applyRefund(RefundReq req);
}
```

#### 支付渠道抽象
```java
public abstract class AbstractPayChannel {
    
    /**
     * 创建支付订单
     */
    public abstract PayOrderResp createPayOrder(CreatePayOrderReq req);
    
    /**
     * 查询支付状态
     */
    public abstract PayStatusResp queryPayStatus(String payOrderNo);
    
    /**
     * 处理支付回调
     */
    public abstract PayCallbackResult handleCallback(String callbackData);
    
    /**
     * 申请退款
     */
    public abstract RefundResp applyRefund(RefundReq req);
    
    /**
     * 获取支付渠道类型
     */
    public abstract PayChannelType getChannelType();
}
```

### 4.2 微信支付实现

#### 微信支付处理器
```java
@Component
public class WxPayServiceHandler extends AbstractPayChannel {
    
    @Resource
    private WxPayService wxPayService;
    
    @Override
    public PayOrderResp createPayOrder(CreatePayOrderReq req) {
        try {
            WxPayUnifiedOrderRequest wxReq = new WxPayUnifiedOrderRequest();
            wxReq.setBody(req.getSubject());
            wxReq.setOutTradeNo(req.getPayOrderNo());
            wxReq.setTotalFee(req.getAmount().multiply(new BigDecimal(100)).intValue());
            wxReq.setSpbillCreateIp(req.getClientIp());
            wxReq.setNotifyUrl(getNotifyUrl());
            wxReq.setTradeType(getTradeType(req.getPayType()));
            wxReq.setOpenid(req.getOpenId());
            
            WxPayUnifiedOrderResult wxResult = wxPayService.unifiedOrder(wxReq);
            
            PayOrderResp resp = new PayOrderResp();
            resp.setPayOrderNo(req.getPayOrderNo());
            resp.setPayInfo(buildPayInfo(wxResult, req.getPayType()));
            resp.setSuccess(true);
            
            return resp;
        } catch (Exception e) {
            log.error("微信支付下单失败", e);
            return PayOrderResp.fail("微信支付下单失败：" + e.getMessage());
        }
    }
    
    @Override
    public PayCallbackResult handleCallback(String callbackData) {
        try {
            WxPayOrderNotifyResult notifyResult = wxPayService.parseOrderNotifyResult(callbackData);
            
            PayCallbackResult result = new PayCallbackResult();
            result.setPayOrderNo(notifyResult.getOutTradeNo());
            result.setTransactionId(notifyResult.getTransactionId());
            result.setPayAmount(new BigDecimal(notifyResult.getTotalFee()).divide(new BigDecimal(100)));
            result.setPayTime(notifyResult.getTimeEnd());
            result.setSuccess("SUCCESS".equals(notifyResult.getResultCode()));
            
            return result;
        } catch (Exception e) {
            log.error("微信支付回调处理失败", e);
            return PayCallbackResult.fail("回调处理失败");
        }
    }
    
    @Override
    public PayChannelType getChannelType() {
        return PayChannelType.WECHAT;
    }
    
    private String getTradeType(PayType payType) {
        switch (payType) {
            case JSAPI:
                return "JSAPI";
            case H5:
                return "MWEB";
            case APP:
                return "APP";
            default:
                throw new BusinessException("不支持的支付类型：" + payType);
        }
    }
    
    private Map<String, Object> buildPayInfo(WxPayUnifiedOrderResult wxResult, PayType payType) {
        Map<String, Object> payInfo = new HashMap<>();
        
        switch (payType) {
            case JSAPI:
                // 小程序支付参数
                payInfo.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
                payInfo.put("nonceStr", wxResult.getNonceStr());
                payInfo.put("package", "prepay_id=" + wxResult.getPrepayId());
                payInfo.put("signType", "MD5");
                payInfo.put("paySign", generatePaySign(payInfo));
                break;
            case H5:
                // H5支付链接
                payInfo.put("mwebUrl", wxResult.getMwebUrl());
                break;
            case APP:
                // APP支付参数
                payInfo.put("appid", wxResult.getAppid());
                payInfo.put("partnerid", wxResult.getMchId());
                payInfo.put("prepayid", wxResult.getPrepayId());
                payInfo.put("package", "Sign=WXPay");
                payInfo.put("noncestr", wxResult.getNonceStr());
                payInfo.put("timestamp", String.valueOf(System.currentTimeMillis() / 1000));
                payInfo.put("sign", generateAppPaySign(payInfo));
                break;
        }
        
        return payInfo;
    }
}
```

### 4.3 支付宝支付实现

#### 支付宝支付处理器
```java
@Component
public class AliPayServiceHandler extends AbstractPayChannel {
    
    @Resource
    private AlipayClient alipayClient;
    
    @Override
    public PayOrderResp createPayOrder(CreatePayOrderReq req) {
        try {
            AlipayTradeCreateRequest alipayReq = new AlipayTradeCreateRequest();
            alipayReq.setNotifyUrl(getNotifyUrl());
            
            JSONObject bizContent = new JSONObject();
            bizContent.put("out_trade_no", req.getPayOrderNo());
            bizContent.put("total_amount", req.getAmount().toString());
            bizContent.put("subject", req.getSubject());
            bizContent.put("buyer_id", req.getBuyerId());
            
            alipayReq.setBizContent(bizContent.toString());
            
            AlipayTradeCreateResponse alipayResp = alipayClient.execute(alipayReq);
            
            PayOrderResp resp = new PayOrderResp();
            resp.setPayOrderNo(req.getPayOrderNo());
            resp.setSuccess(alipayResp.isSuccess());
            
            if (alipayResp.isSuccess()) {
                Map<String, Object> payInfo = new HashMap<>();
                payInfo.put("tradeNo", alipayResp.getTradeNo());
                resp.setPayInfo(payInfo);
            } else {
                resp.setErrorMsg(alipayResp.getSubMsg());
            }
            
            return resp;
        } catch (Exception e) {
            log.error("支付宝支付下单失败", e);
            return PayOrderResp.fail("支付宝支付下单失败：" + e.getMessage());
        }
    }
    
    @Override
    public PayCallbackResult handleCallback(String callbackData) {
        try {
            Map<String, String> params = parseCallbackParams(callbackData);
            
            // 验证签名
            boolean signVerified = AlipaySignature.rsaCheckV1(
                params, getAlipayPublicKey(), "UTF-8", "RSA2");
            
            if (!signVerified) {
                return PayCallbackResult.fail("签名验证失败");
            }
            
            PayCallbackResult result = new PayCallbackResult();
            result.setPayOrderNo(params.get("out_trade_no"));
            result.setTransactionId(params.get("trade_no"));
            result.setPayAmount(new BigDecimal(params.get("total_amount")));
            result.setPayTime(params.get("gmt_payment"));
            result.setSuccess("TRADE_SUCCESS".equals(params.get("trade_status")));
            
            return result;
        } catch (Exception e) {
            log.error("支付宝支付回调处理失败", e);
            return PayCallbackResult.fail("回调处理失败");
        }
    }
    
    @Override
    public PayChannelType getChannelType() {
        return PayChannelType.ALIPAY;
    }
}
```

### 4.4 支付服务实现

#### 支付服务主类
```java
@Service
public class PayServiceImpl implements PayService {
    
    @Resource
    private Map<String, AbstractPayChannel> payChannelMap;
    @Resource
    private OrderPayRepository orderPayRepository;
    
    @Override
    public PayOrderResp createPayOrder(CreatePayOrderReq req) {
        // 参数校验
        validateCreatePayOrderReq(req);
        
        // 获取支付渠道
        AbstractPayChannel payChannel = getPayChannel(req.getPayChannel());
        
        // 创建支付记录
        OrderPay orderPay = createOrderPayRecord(req);
        orderPayRepository.save(orderPay);
        
        // 调用支付渠道创建支付订单
        PayOrderResp resp = payChannel.createPayOrder(req);
        
        // 更新支付记录状态
        if (resp.isSuccess()) {
            orderPay.setStatus(PayStatus.CREATED.getCode());
            orderPay.setPayInfo(JsonUtil.toJson(resp.getPayInfo()));
        } else {
            orderPay.setStatus(PayStatus.FAILED.getCode());
            orderPay.setErrorMsg(resp.getErrorMsg());
        }
        orderPayRepository.updateById(orderPay);
        
        return resp;
    }
    
    @Override
    public void handlePayCallback(String channel, String callbackData) {
        AbstractPayChannel payChannel = getPayChannel(channel);
        PayCallbackResult callbackResult = payChannel.handleCallback(callbackData);
        
        if (callbackResult.isSuccess()) {
            // 查询支付记录
            OrderPay orderPay = orderPayRepository.getByPayOrderNo(callbackResult.getPayOrderNo());
            if (orderPay == null) {
                log.warn("支付回调找不到支付记录，支付单号：{}", callbackResult.getPayOrderNo());
                return;
            }
            
            // 防重复处理
            if (PayStatus.SUCCESS.getCode().equals(orderPay.getStatus())) {
                log.info("支付订单已处理，支付单号：{}", callbackResult.getPayOrderNo());
                return;
            }
            
            // 更新支付状态
            orderPay.setStatus(PayStatus.SUCCESS.getCode());
            orderPay.setTransactionId(callbackResult.getTransactionId());
            orderPay.setPayAmount(callbackResult.getPayAmount());
            orderPay.setPayTime(callbackResult.getPayTime());
            orderPayRepository.updateById(orderPay);
            
            // 更新订单状态
            updateOrderStatus(orderPay.getOrderId(), OrderStatus.PAID);
            
            // 发送支付成功消息
            sendPaySuccessMessage(orderPay);
        }
    }
    
    private AbstractPayChannel getPayChannel(String channelCode) {
        AbstractPayChannel payChannel = payChannelMap.get(channelCode + "PayChannel");
        if (payChannel == null) {
            throw new BusinessException("不支持的支付渠道：" + channelCode);
        }
        return payChannel;
    }
}
```

---

## 5. 总结

以上详细介绍了himallWork项目的核心功能模块：

### 5.1 技术亮点

1. **区域禁售校验**：通过运费模板配置实现商品区域销售限制，确保业务合规
2. **数据加密存储**：使用AES加密保护用户敏感信息，满足数据安全要求
3. **报表数据收集**：采用数据源表+定时汇总的架构，支持多维度数据分析
4. **统一支付系统**：抽象支付接口，支持多种支付渠道，易于扩展

### 5.2 设计原则

1. **单一职责**：每个模块职责明确，功能内聚
2. **开闭原则**：对扩展开放，对修改关闭
3. **依赖倒置**：依赖抽象而非具体实现
4. **接口隔离**：接口设计精简，避免冗余

### 5.3 最佳实践

1. **异常处理**：完善的异常处理机制，保证系统稳定性
2. **日志记录**：详细的日志记录，便于问题排查
3. **参数校验**：严格的参数校验，防止非法数据
4. **事务管理**：合理的事务边界，保证数据一致性

这些核心功能模块体现了现代电商系统的设计思路和技术实现，为业务发展提供了坚实的技术基础。
