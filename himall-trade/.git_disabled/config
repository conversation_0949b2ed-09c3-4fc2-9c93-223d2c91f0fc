[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[submodule]
	active = .
[remote "origin"]
	url = https://codehub-cn-south-1.devcloud.huaweicloud.com/himall00001/himall-trade.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "develop"]
	remote = origin
	merge = refs/heads/develop
[branch "develop_chengpei"]
	remote = origin
	merge = refs/heads/develop_chengpei
	vscode-merge-base = origin/develop_chengpei
[branch "develop_combine"]
	remote = origin
	merge = refs/heads/develop_combine
	vscode-merge-base = origin/develop_combine
[branch "develop_bbc"]
	remote = origin
	merge = refs/heads/develop_bbc
	vscode-merge-base = origin/develop_bbc
[branch "feat-chengpei-v2-20250714"]
	remote = origin
	merge = refs/heads/feat-chengpei-v2-20250714
	vscode-merge-base = origin/feat-chengpei-v2-20250714
