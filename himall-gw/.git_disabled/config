[core]
	repositoryformatversion = 0
	filemode = false
	bare = false
	logallrefupdates = true
	symlinks = false
	ignorecase = true
[submodule]
	active = .
[remote "origin"]
	url = https://codehub-cn-south-1.devcloud.huaweicloud.com/3233bbc3ec464ce5816a4ce26104c380/himall-gw.git
	fetch = +refs/heads/*:refs/remotes/origin/*
[branch "develop_chengpei"]
	remote = origin
	merge = refs/heads/develop_chengpei
	vscode-merge-base = origin/develop
	vscode-merge-base = origin/develop
[branch "develop_combine"]
	remote = origin
	merge = refs/heads/develop_combine
	vscode-merge-base = origin/develop_combine
[branch "develop_bbc"]
	remote = origin
	merge = refs/heads/develop_bbc
[branch "develop_combine-wxsend"]
	remote = origin
	merge = refs/heads/develop_combine-wxsend
	vscode-merge-base = origin/develop
