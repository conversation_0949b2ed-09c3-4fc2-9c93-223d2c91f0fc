# 用户手机号加密解密逻辑分析

## 1. 加密解密工具类

### 1.1 主要工具类

项目中存在两套加密解密工具类：

#### AesUtil (主要使用)
**位置**: `himall-base/seashop-base-boot/src/main/java/com/sankuai/shangou/seashop/base/boot/utils/AesUtil.java`

**加密算法**: AES/CBC/PKCS5Padding
- **算法**: AES
- **模式**: CBC (Cipher Block Chaining)
- **填充**: PKCS5Padding
- **IV偏移量**: 固定16字节 `{18, 52, 86, 120, 144, 171, 205, 239, 18, 52, 86, 120, 144, 171, 205, 239}`

**核心方法**:
```java
// 加密方法
public static String encrypt(String encryptStr, String encryptKey)

// 解密方法  
public static String decrypt(String decryptStr, String decryptKey)
```

**密钥处理**:
- 密钥长度限制为32字节
- 不足32字节时用空格右填充
- 超过32字节时截取前32字节

#### EncryptUtils (辅助使用)
**位置**: `himall-base/seashop-base-user-common/src/main/java/com/sankuai/shangou/seashop/user/common/util/EncryptUtils.java`

**加密算法**: AES/GCM模式
- 使用Keyczar加密库
- 固定密钥: `DvfCfVzo0HMbVFOp_q1jvw`
- 主要用于合同ID等特殊场景

### 1.2 加密配置

#### EncryptConfig配置类
**位置**: `himall-base/seashop-base-user-common/src/main/java/com/sankuai/shangou/seashop/user/common/config/EncryptConfig.java`

```java
@Configuration
@Getter
public class EncryptConfig {
    @Value("${seashop.aes.secret:'ae125efkk4454eeff444ferfkny6oxi8'}")
    private String aesSecret;
}
```

**默认密钥**: `ae125efkk4454eeff444ferfkny6oxi8`

## 2. 手机号加密解密应用场景

### 2.1 用户注册场景

#### 会员注册 (MemberRegisterService)
```java
// 注册时加密手机号存储
member.setCellPhone(AesUtil.encrypt(phone, encryptConfig.getAesSecret()));
member.setUserName(phone);

// 绑定联系人时也需要加密
bindContactCmdReq.setContact(AesUtil.encrypt(phone, encryptConfig.getAesSecret()));
```

#### 管理员注册 (ManagerServiceImpl)
```java
// 创建管理员时加密手机号
String encryCellphone = AesUtil.encrypt(cmdManagerReq.getCellphone(), encryptConfig.getAesSecret());

// 查询时使用加密后的手机号
List<Manager> managerList = managerRepository.lambdaQuery()
    .eq(Manager::getCellphone, encryCellphone)
    .eq(Manager::getRoleId, roleId)
    .list();
```

### 2.2 用户登录场景

#### 手机号登录 (MobileLoginStrategy)
```java
// 登录时需要加密手机号进行查询
String orgPhone = phone;  // 保存原始手机号
phone = AesUtil.encrypt(phone, encryptConfig.getAesSecret());  // 加密后查询
Manager manager = managerRepository.getByPhone(phone);
```

#### 微信小程序登录 (WxMiniLoginStrategy)
```java
// 微信登录绑定手机号时加密存储
String encryptPhone = AesUtil.encrypt(phone, encryptConfig.getAesSecret());
member.setCellPhone(encryptPhone);
```

### 2.3 用户信息查询场景

#### 会员信息查询 (MemberServiceImpl)
```java
// 查询时先加密手机号
if (StrUtil.isNotBlank(queryMemberListDto.getMobile())) {
    queryMemberListDto.setMobile(AesUtil.encrypt(queryMemberListDto.getMobile(), encryptConfig.getAesSecret()));
}

// 返回时解密手机号
try {
    member.setCellPhone(AesUtil.decrypt(member.getCellPhone(), encryptConfig.getAesSecret()));
} catch (Exception e) {
    // 解密失败处理
}
```

#### 管理员信息查询 (ManagerServiceImpl)
```java
// 返回管理员信息时解密手机号
if (StrUtil.isNotBlank(manager.getCellphone())) {
    manager.setCellphone(AesUtil.decrypt(manager.getCellphone(), encryptConfig.getAesSecret()));
}
```

### 2.4 收货地址场景

#### 收货地址管理 (ShippingAddressServiceImpl)
```java
// 保存收货地址时加密
address.setShipTo(AesUtil.encrypt(addBo.getShipTo(), encryptConfig.getAesSecret()));
address.setPhone(AesUtil.encrypt(addBo.getPhone(), encryptConfig.getAesSecret()));
address.setAddress(AesUtil.encrypt(addBo.getAddress(), encryptConfig.getAesSecret()));
address.setAddressDetail(AesUtil.encrypt(addBo.getAddressDetail(), encryptConfig.getAesSecret()));

// 查询收货地址时解密
address.setShipTo(AesUtil.decrypt(address.getShipTo(), encryptConfig.getAesSecret()));
address.setPhone(AesUtil.decrypt(address.getPhone(), encryptConfig.getAesSecret()));
address.setAddress(AesUtil.decrypt(address.getAddress(), encryptConfig.getAesSecret()));
address.setAddressDetail(AesUtil.decrypt(address.getAddressDetail(), encryptConfig.getAesSecret()));
```

### 2.5 店铺信息场景

#### 店铺信息管理 (ShopServiceImpl)
```java
// 保存店铺信息时加密敏感信息
shop.setIdCard(AesUtil.encrypt(cmdShopStepsOneReq.getIdCard(), encryptConfig.getAesSecret()));
shop.setBankAccountNumber(AesUtil.encrypt(cmdShopStepsTwoReq.getBankAccountNumber(), encryptConfig.getAesSecret()));

// 查询店铺信息时解密
shopDetailResp.setBankAccountNumber(AesUtil.decrypt(shop.getBankAccountNumber(), encryptConfig.getAesSecret()));
shopDetailResp.setIdCard(AesUtil.decrypt(shop.getIdCard(), encryptConfig.getAesSecret()));
```

## 3. 数据库存储策略

### 3.1 加密字段
数据库中以下字段采用AES加密存储：

#### 用户相关表
- **Member表**: `cell_phone` (手机号)
- **Manager表**: `cellphone` (管理员手机号)

#### 收货地址表
- **ShippingAddress表**: 
  - `ship_to` (收货人姓名)
  - `phone` (收货人手机号)
  - `address` (收货地址)
  - `address_detail` (详细地址)

#### 店铺相关表
- **Shop表**:
  - `id_card` (身份证号)
  - `bank_account_number` (银行账号)

### 3.2 查询策略
由于手机号在数据库中是加密存储的，查询时需要：

1. **精确查询**: 先加密查询条件，再进行数据库查询
2. **模糊查询**: 通过关联联系人表进行查询（因为加密后无法进行模糊匹配）

```java
/**
 * 根据手机号模糊查询用户id
 * 因为用户表手机号码是加密的，所以是关联联系人表查询的
 */
```

## 4. 安全特性

### 4.1 加密强度
- **算法**: AES-256 (密钥长度32字节)
- **模式**: CBC模式，提供良好的安全性
- **填充**: PKCS5Padding，防止填充攻击
- **IV**: 固定IV，虽然降低了部分安全性，但保证了相同明文产生相同密文，便于数据库查询

### 4.2 异常处理
```java
// 加密失败时返回原文
public static String encrypt(String encryptStr, String encryptKey) {
    try {
        // 加密逻辑
    } catch (Exception e) {
        throw new BusinessException("加密失败");
    }
}

// 解密失败时返回密文
public static String decrypt(String decryptStr, String decryptKey) {
    try {
        // 解密逻辑  
    } catch (Exception e) {
        throw new BusinessException("解密失败");
    }
}
```

### 4.3 配置管理
- 密钥通过Spring配置文件管理
- 支持环境变量覆盖默认配置
- 密钥统一管理，便于维护和更换

## 5. 使用注意事项

### 5.1 开发规范
1. **存储时必须加密**: 所有敏感信息存储前必须加密
2. **查询时先加密**: 使用敏感信息查询时，先对查询条件加密
3. **返回时必须解密**: 返回给前端的数据必须解密
4. **异常处理**: 加密解密失败时要有合适的异常处理

### 5.2 性能考虑
1. **批量操作**: 大量数据加密解密时考虑性能影响
2. **缓存策略**: 频繁查询的数据可以考虑缓存解密后的结果
3. **索引优化**: 加密字段无法使用普通索引，需要特殊处理

### 5.3 数据迁移
1. **历史数据**: 需要考虑历史未加密数据的处理
2. **兼容性**: 加密解密失败时的兼容性处理
3. **密钥轮换**: 密钥更换时的数据迁移策略

## 6. 测试用例

### 6.1 加密解密测试
```java
public static void main(String[] args) {
    // 测试手机号加密解密
    String phone = "18670374313";
    String key = "ae125efkk4454eeff444ferfkny6oxi8";
    
    String encrypted = AesUtil.encrypt(phone, key);
    System.out.println("加密后: " + encrypted);
    
    String decrypted = AesUtil.decrypt(encrypted, key);
    System.out.println("解密后: " + decrypted);
}
```

### 6.2 预期结果
- 相同的手机号和密钥，每次加密结果相同（固定IV的特性）
- 解密后能完全还原原始手机号
- 加密后的字符串为Base64编码格式

## 7. 总结

项目采用AES加密算法对用户手机号等敏感信息进行加密存储，主要特点：

1. **统一加密**: 使用统一的AesUtil工具类和密钥配置
2. **透明处理**: 在业务层面透明处理加密解密，数据库存储加密数据
3. **查询兼容**: 通过加密查询条件实现精确查询，通过关联表实现模糊查询
4. **异常容错**: 加密解密失败时有相应的异常处理机制
5. **配置灵活**: 支持通过配置文件自定义加密密钥

这种设计既保证了数据安全性，又保持了业务逻辑的简洁性，是一个较为完善的敏感信息加密解决方案。