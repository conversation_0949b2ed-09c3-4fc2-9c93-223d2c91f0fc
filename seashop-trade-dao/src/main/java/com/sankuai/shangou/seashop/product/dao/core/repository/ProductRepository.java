package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductMapper;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopSaleCountsDto;
import com.sankuai.shangou.seashop.product.dao.core.model.AddProductSaleCountDto;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:05
 */
@Repository
@Slf4j
public class ProductRepository extends ServiceImpl<ProductMapper, Product> {

    /**
     * 根据商品id获取商品信息
     *
     * @param productId 商品id
     * @return 商品信息
     */
    public Product getByProductId(Long productId) {
        return baseMapper.selectOne(new LambdaQueryWrapper<Product>().eq(Product::getProductId, productId));
    }

    /**
     * 根据商品id的集合查询商品列表
     *
     * @param productIdList 商品id的集合
     * @return 商品列表
     */
    public List<Product> listByProductIds(List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.EMPTY_LIST;
        }
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<Product>().in(Product::getProductId, ids)), productIdList);
    }

    public List<Product> queryProductByIds(List<String> productIdList, String productName) {
        LambdaQueryWrapper<Product> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(!CollectionUtils.isEmpty(productIdList)){
            lambdaQueryWrapper.in(Product::getProductId, productIdList);
        }
        if(!StringUtils.isEmpty(productName)){
            lambdaQueryWrapper.like(Product::getProductName, productName);
        }
        return this.list(lambdaQueryWrapper);
    }

    /**
     * 根据商品id更新商品信息
     *
     * @param product 商品信息
     */
    public void updateByProductId(Product product) {
        baseMapper.update(product, new LambdaQueryWrapper<Product>().eq(Product::getProductId, product.getProductId()));
    }

    /**
     * 根据商品id批量更新商品信息
     *
     * @param updProduct    更新信息
     * @param productIdList 商品id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchByProductId(Product updProduct, List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        MybatisUtil.executeBatch(ids ->
                update(updProduct, new LambdaQueryWrapper<Product>().in(Product::getProductId, ids)), productIdList);
    }

    /**
     * 根据商品id批量更新商品信息
     *
     * @param productList 商品信息集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchByProductId(List<Product> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }

        productList.forEach(product -> updateByProductId(product));
    }

    /**
     * 根据商品id 获取商品map
     *
     * @param productIdList 商品id集合
     * @return 商品map
     */
    public Map<Long, Product> getProductMap(List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.EMPTY_MAP;
        }
        return listByProductIds(productIdList).stream()
                .collect(Collectors.toMap(Product::getProductId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 根据商品code获取商品信息
     *
     * @param productCodeList 货号集合
     * @param shopId          店铺id
     * @return 商品信息
     */
    public Map<String, Product> getProductMapByProductCode(List<String> productCodeList, Long shopId) {
        if (CollectionUtils.isEmpty(productCodeList)) {
            return Collections.EMPTY_MAP;
        }
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<Product>().eq(Product::getShopId, shopId).eq(Product::getWhetherDelete, Boolean.FALSE);
        return MybatisUtil.queryBatch(code -> list(wrapper.in(Product::getProductCode, code)), productCodeList)
                .stream().collect(Collectors.toMap(Product::getProductCode, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 根据店铺ID查询销量前8的在售商品
     * 如果店铺ID为空，则返回平台销量前8的在售商品
     *
     * @param shopId
     * @return
     */
    public List<Product> queryRecommendProducts(Long shopId, Integer limitNum) {
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
        if (shopId != null) {
            queryWrapper.lambda().eq(Product::getShopId, shopId);
        }
        queryWrapper.lambda().eq(Product::getSaleStatus, 1);
        queryWrapper.lambda().eq(Product::getAuditStatus, 2);
        queryWrapper.lambda().eq(Product::getWhetherDelete, 0);
        queryWrapper.last("order by sale_counts + virtual_sale_counts desc " + CommonConstant.LIMIT + limitNum);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 货号是否存在
     *
     * @param productCode 货号
     * @param shopId      店铺id
     * @return 是否存在
     */
    public Boolean existProductCode(String productCode, Long shopId) {
        return count(new LambdaQueryWrapper<Product>()
                .eq(Product::getProductCode, productCode)
                .eq(Product::getShopId, shopId)
                .eq(Product::getWhetherDelete, Boolean.FALSE)) > 0;
    }

    /**
     * 根据商品id删除商品
     *
     * @param productIds    商品id集合
     * @param deleteVersion 删除版本号
     */
    public void removeByProductIds(List<Long> productIds, Long deleteVersion) {
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }

        Product updProduct = new Product();
        updProduct.setWhetherDelete(Boolean.TRUE);
        updProduct.setDeleteVersion(deleteVersion);
        MybatisUtil.executeBatch(ids -> update(updProduct, new LambdaQueryWrapper<Product>().in(Product::getProductId, ids)), productIds);
    }

    public int countByCategoryIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return 0;
        }
        return Math.toIntExact(MybatisUtil.countBatch(ids -> count(new LambdaQueryWrapper<Product>()
            .in(Product::getCategoryId, ids).eq(Product::getWhetherDelete, Boolean.FALSE)), categoryIds));
    }

    public List<Map<String, Object>> queryProductCountByTemplateId(List<Long> templateIds, Long shopId) {
        QueryWrapper<Product> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("freight_template_id as templateId", "count(freight_template_id) as num");
        if (shopId != null) {
            queryWrapper.eq("shop_id", shopId);
        }
        if (!CollectionUtils.isEmpty(templateIds)) {
            queryWrapper.in("freight_template_id", templateIds);

        }
        queryWrapper.groupBy("freight_template_id");
        List<Map<String, Object>> result = baseMapper.selectMaps(queryWrapper);
        return result;
    }

    /**
     * 更新商品更新时间
     *
     * @param productIds
     */
    public void updateUpdateTime(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }

        Product updProduct = new Product();
        updProduct.setUpdateTime(new Date());
        MybatisUtil.executeBatch(ids -> update(updProduct, new LambdaQueryWrapper<Product>().in(Product::getProductId, ids)), productIds);
    }

    public void addSaleCount(AddProductSaleCountDto saleCountParam) {
        baseMapper.addSaleCount(saleCountParam);
    }

    public ShopSaleCountsDto getShopSaleCounts(Long shopId) {
        return baseMapper.getShopSaleCounts(shopId);
    }

    public Integer countBySpecNameId(Long nameId) {
        return baseMapper.countBySpecNameId(nameId);
    }

    public List<Product> getByUpdateTime(Date updateTime) {
        LambdaQueryWrapper<Product> queryWrapper = new LambdaQueryWrapper<>();
        if (updateTime != null) {
            queryWrapper.ge(Product::getUpdateTime, updateTime);
        }

        List<Product> products = baseMapper.selectList(queryWrapper);
        return products;
    }
}
