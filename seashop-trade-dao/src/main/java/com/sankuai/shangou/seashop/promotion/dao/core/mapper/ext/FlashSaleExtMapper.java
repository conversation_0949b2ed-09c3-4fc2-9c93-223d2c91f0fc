package com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext;

import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSale;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.FlashSaleDetail;
import com.sankuai.shangou.seashop.promotion.dao.core.model.MallFlashSaleDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.MallFlashSaleParamDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
public interface FlashSaleExtMapper {

    /**
     * 商城限时购列表查询
     *
     * @param paramModel
     * @return
     */
    List<MallFlashSaleDto> queryMallFlashSaleList(MallFlashSaleParamDto paramModel);

    /**
     * 查询所有正在进行或者未开始的限时购活动
     *
     * @param shopId
     * @param categoryId
     * @return
     */
    List<FlashSale> queryAllActiveFlashSale(@Param("shopId") Long shopId,@Param("categoryId") Long categoryId);

    /**
     * 通过明细查询当前正在进行的活动
     *
     * @param detail
     * @return
     */
    FlashSale queryActiveByDetailParam(FlashSaleDetail detail);

    /**
     * 通过活动ID增加销量
     *
     * @param id
     * @param number
     * @return
     */
    int updateSaleCountById(@Param("id") Long id, @Param("number") Integer number);
}
