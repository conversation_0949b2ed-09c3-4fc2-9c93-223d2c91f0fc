package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.CategoryCashDeposit;
import com.sankuai.shangou.seashop.product.dao.core.mapper.CategoryCashDepositMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Repository
@Slf4j
public class CategoryCashDepositRepository extends ServiceImpl<CategoryCashDepositMapper, CategoryCashDeposit> {

    public List<CategoryCashDeposit> queryByCategoryId(List<Long> categoryIds) {
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<CategoryCashDeposit>()
                .in(CategoryCashDeposit::getCategoryId, ids)), categoryIds.stream().distinct().collect(Collectors.toList()));
    }

    /**
     * 获取保证金map
     *
     * @param categoryIds 类目id集合
     * @return 保证金map
     */
    public Map<Long, BigDecimal> getCashDepositMap(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return MapUtils.EMPTY_MAP;
        }

        return queryByCategoryId(categoryIds).stream().collect(Collectors
                .toMap(CategoryCashDeposit::getCategoryId, CategoryCashDeposit::getNeedPayCashDeposit, (k1, k2) -> k2));
    }

    /**
     * 获取保证金信息map
     *
     * @param categoryIds 类目id集合
     * @return 保证金map
     */
    public Map<Long, CategoryCashDeposit> getCategoryCashDepositMap(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return MapUtils.EMPTY_MAP;
        }

        return queryByCategoryId(categoryIds).stream().collect(Collectors
                .toMap(CategoryCashDeposit::getCategoryId, categoryCashDeposit -> categoryCashDeposit, (k1, k2) -> k2));
    }
}
