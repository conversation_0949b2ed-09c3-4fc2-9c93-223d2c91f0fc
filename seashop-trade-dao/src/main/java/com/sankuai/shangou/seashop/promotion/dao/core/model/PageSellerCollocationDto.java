package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 16:27
 */
@Data
public class PageSellerCollocationDto {

    /**
     * 店铺ID（供应商只能查自己店铺的组合购活动）
     */
    private Long shopId;

    /**
     * 组合购活动名称
     */
    private String title;

    /**
     * 组合购活动状态,0未开始，1进行中，2已结束
     */
    private Integer status;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 是否查主商品
     */
    private Boolean mainFlag;

    /**
     * 主商品Id集合
     */
    private List<Long> mainProductIds;

}
