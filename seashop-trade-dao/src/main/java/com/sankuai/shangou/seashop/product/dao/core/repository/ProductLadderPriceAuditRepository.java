package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.math.BigDecimal;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPriceAudit;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductLadderPriceAuditMapper;

/**
 * <AUTHOR>
 * @date 2023/11/15 17:46
 */
@Repository
public class ProductLadderPriceAuditRepository extends ServiceImpl<ProductLadderPriceAuditMapper, ProductLadderPriceAudit> {

    /**
     * 根据商品id删除阶梯价审核记录
     *
     * @param productId 商品id
     */
    public void removeByProductId(Long productId) {
        remove(new LambdaQueryWrapper<ProductLadderPriceAudit>().eq(ProductLadderPriceAudit::getProductId, productId));
    }

    public BigDecimal getMaxLadderPrice(Long productId) {
        ProductLadderPriceAudit priceAudit = getOne(new LambdaQueryWrapper<ProductLadderPriceAudit>()
                .eq(ProductLadderPriceAudit::getProductId, productId)
                .orderByDesc(ProductLadderPriceAudit::getPrice)
                .last("limit 1"));

        return priceAudit == null ? null : priceAudit.getPrice();
    }
}
