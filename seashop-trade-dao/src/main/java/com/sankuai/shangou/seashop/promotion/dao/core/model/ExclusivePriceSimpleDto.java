package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:专享价活动响应体
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class ExclusivePriceSimpleDto {

    /** 主键ID */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 活动名称 */
    private String name;

    /** 开始时间 */
    private Date startTime;

    /** 结束时间 */
    private Date endTime;

    /** 状态 */
    private Integer status;

    /** 状态名称 */
    private String statusDesc;

    /** 商品数量 */
    private Integer productCount;

    /** 会员数量 */
    private Integer memberCount;

}
