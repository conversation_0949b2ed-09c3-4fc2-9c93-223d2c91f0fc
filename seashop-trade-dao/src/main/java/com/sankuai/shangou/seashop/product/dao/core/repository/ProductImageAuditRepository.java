package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImageAudit;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductImageAuditMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:05
 */
@Repository
@Slf4j
public class ProductImageAuditRepository extends ServiceImpl<ProductImageAuditMapper, ProductImageAudit> {

    /**
     * 根据商品id查询商品图片列表
     *
     * @param productId 商品id
     * @return 商品图片列表
     */
    public List<String> listImagesByProductId(Long productId) {
        return listObjs(new LambdaQueryWrapper<ProductImageAudit>().eq(ProductImageAudit::getProductId, productId).select(ProductImageAudit::getImageUrl), String::valueOf);
    }
}
