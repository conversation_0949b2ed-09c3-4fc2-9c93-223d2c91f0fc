package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.Collections;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockTaskInfo;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SkuStockTaskInfoMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/20 14:27
 */
@Repository
@Slf4j
public class SkuStockTaskInfoRepository extends ServiceImpl<SkuStockTaskInfoMapper, SkuStockTaskInfo> {

    public List<SkuStockTaskInfo> listByTaskIds(List<Long> taskIds) {
        if (CollectionUtils.isEmpty(taskIds)) {
            return Collections.EMPTY_LIST;
        }
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<SkuStockTaskInfo>().in(SkuStockTaskInfo::getTaskId, ids)), taskIds);
    }

    public SkuStockTaskInfo getByIdForceMaster(Long id) {
        return getById(id);
    }
}
