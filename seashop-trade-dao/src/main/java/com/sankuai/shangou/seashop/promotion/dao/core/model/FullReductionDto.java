package com.sankuai.shangou.seashop.promotion.dao.core.model;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description: 满减活动列表响应体
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class FullReductionDto {

    @PrimaryField(title = "主键ID")
    private Long id;

    @ExaminField(description = "店铺ID")
    private Long shopId;

    @ExaminField(description = "店铺名称")
    private String shopName;

    @ExaminField(description = "活动名称")
    private String activeName;

    @ExaminField(description = "单笔订单满减金额门槛")
    private BigDecimal moneyOffCondition;

    @ExaminField(description = "单笔订单满减金额")
    private BigDecimal moneyOffFee;

    @ExaminField(description = "开始时间")
    private Date startTime;

    @ExaminField(description = "结束时间")
    private Date endTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusDesc;

    @ExaminField(description = "是否叠加优惠")
    private Boolean moneyOffOverLay;
}
