package com.sankuai.shangou.seashop.product.dao.core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;
import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;

/**
 * <p>
 * 规格库存表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-01-23
 */
public interface SkuStockMapper extends EnhancedMapper<SkuStock> {

    List<SkuStock> listBySkuIdsForUpdate(@Param("skuIds") List<String> skuIds);

    void updateBatchBySkuId(@Param("skuList") List<SkuStock> skuStockList);
}
