package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 品牌表
 * </p>
 *
 * <AUTHOR> @since 2023-11-25
 */
public class Brand implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 品牌名称
     */
    @TableField("name")
    private String name;

    /**
     * 顺序
     */
    @TableField("display_sequence")
    private Long displaySequence;

    /**
     * LOGO
     */
    @TableField("logo")
    private String logo;

    /**
     * 品牌简介
     */
    @TableField("description")
    private String description;


    /**
     * 是否推荐
     */
    @TableField("whether_recommend")
    private Boolean whetherRecommend;

    /**
     * 是否已删除
     */
    @TableField("whether_delete")
    private Boolean whetherDelete;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人Id
     */
    @TableField("create_user")
    private Long createUser;

    /**
     * 更新人Id
     */
    @TableField("update_user")
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getDisplaySequence() {
        return displaySequence;
    }

    public void setDisplaySequence(Long displaySequence) {
        this.displaySequence = displaySequence;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getWhetherRecommend() {
        return whetherRecommend;
    }

    public void setWhetherRecommend(Boolean whetherRecommend) {
        this.whetherRecommend = whetherRecommend;
    }

    public Boolean getWhetherDelete() {
        return whetherDelete;
    }

    public void setWhetherDelete(Boolean whetherDelete) {
        this.whetherDelete = whetherDelete;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "Brand{" +
        "id=" + id +
        ", name=" + name +
        ", displaySequence=" + displaySequence +
        ", logo=" + logo +
        ", description=" + description +
        ", whetherRecommend=" + whetherRecommend +
        ", whetherDelete=" + whetherDelete +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", createUser=" + createUser +
        ", updateUser=" + updateUser +
        "}";
    }
}
