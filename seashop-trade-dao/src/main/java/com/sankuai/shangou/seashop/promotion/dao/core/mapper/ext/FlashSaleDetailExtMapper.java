package com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext;


import com.sankuai.shangou.seashop.promotion.dao.core.model.FlashSaleDetailCountDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:
 */
public interface FlashSaleDetailExtMapper {

    /**
     * 通过ID扣减库存
     *
     * @param id
     * @param number
     * @return
     */
    int reduceStockByFlashSaleIdAndSkuId(@Param("id") Long id, @Param("number") Integer number);

    /**
     * 通过ID统计库存
     *
     * @param flashSaleIdList
     * @return
     */
    List<FlashSaleDetailCountDto> countByFlashSaleId(@Param("flashSaleIdList") List<Long> flashSaleIdList);
}
