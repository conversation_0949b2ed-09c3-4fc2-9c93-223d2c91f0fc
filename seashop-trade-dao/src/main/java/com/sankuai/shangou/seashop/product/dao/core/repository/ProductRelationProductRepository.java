package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductRelationProduct;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductRelationProductMapper;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @date 2023/11/20 16:36
 */
@Repository
public class ProductRelationProductRepository extends ServiceImpl<ProductRelationProductMapper, ProductRelationProduct> {

    /**
     * 根据productId 查询当前记录
     */
    public ProductRelationProduct getByProductId(Long productId) {
        return getOne(new LambdaQueryWrapper<ProductRelationProduct>().eq(ProductRelationProduct::getProductId, productId));
    }

    /**
     * 查询推荐商品id的集合
     */
    public List<Long> getRelationProductIds(Long productId) {
        ProductRelationProduct relationProduct = getByProductId(productId);
        if (relationProduct == null) {
            return Collections.EMPTY_LIST;
        }
        if (StringUtils.isEmpty(relationProduct.getRelation())) {
            return Collections.EMPTY_LIST;
        }

        return Arrays.stream(relationProduct.getRelation().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
    }

    /**
     * 查询推荐商品map
     */
    public Map<Long, List<Long>> getRelationProductMap(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return MapUtils.EMPTY_MAP;
        }

        productIds = productIds.stream().distinct().collect(Collectors.toList());
        List<ProductRelationProduct> relationList = MybatisUtil.queryBatch(ids ->
                list(new LambdaQueryWrapper<ProductRelationProduct>().in(ProductRelationProduct::getProductId, ids)), productIds);
        return relationList.stream().collect(Collectors.toMap(ProductRelationProduct::getProductId, relationProduct -> {
            if (relationProduct == null || StringUtils.isEmpty(relationProduct.getRelation())) {
                return Collections.EMPTY_LIST;
            }
            return Arrays.stream(relationProduct.getRelation().split(StrUtil.COMMA)).map(Long::parseLong).collect(Collectors.toList());
        }));
    }
}
