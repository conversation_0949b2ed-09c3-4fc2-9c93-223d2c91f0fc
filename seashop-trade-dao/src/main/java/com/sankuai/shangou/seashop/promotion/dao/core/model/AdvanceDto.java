package com.sankuai.shangou.seashop.promotion.dao.core.model;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description: 弹窗广告响应体
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class AdvanceDto {

    /**
     * 是否开启弹窗广告
     */
    @ExaminField(description = "是否开启弹窗广告")
    private Boolean isEnable;

    /**
     * 广告位图片
     */
    @ExaminField(description = "广告位图片")
    private String img;

    /**
     * 图片外联链接
     */
    @ExaminField(description = "图片外联链接")
    private String link;

    /**
     * 开始时间
     */
    @ExaminField(description = "开始时间")
    private Date startTime;

    /**
     * 结束时间
     */
    @ExaminField(description = "结束时间")
    private Date endTime;

    /**
     * 是否重复播放
     */
    @ExaminField(description = "是否重复播放")
    private Boolean isReplay;
}
