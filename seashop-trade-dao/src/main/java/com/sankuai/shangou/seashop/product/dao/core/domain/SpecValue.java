package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

/**
 * <p>
 * 规格值表
 * </p>
 *
 * <AUTHOR> @since 2023-11-25
 */
@Data
public class SpecValue implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规格 1-规格1 2-规格2 3-规格3
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 规格模板Id
     */
    @TableField("name_id")
    private Long nameId;

    /**
     * 规格值
     */
    @TableField("value")
    private String value;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;


    @Override
    public String toString() {
        return "SpecValue{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", nameId=" + nameId +
        ", value=" + value +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
