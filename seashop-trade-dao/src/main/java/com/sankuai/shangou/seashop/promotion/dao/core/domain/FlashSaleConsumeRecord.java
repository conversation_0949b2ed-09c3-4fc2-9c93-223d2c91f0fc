package com.sankuai.shangou.seashop.promotion.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 限时购核销记录表
 * </p>
 *
 * <AUTHOR> @since 2024-01-08
 */
public class FlashSaleConsumeRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 限时购ID
     */
    @TableField("flash_sale_id")
    private Long flashSaleId;

    /**
     * 限时购明细ID
     */
    @TableField("flash_sale_detail_id")
    private Long flashSaleDetailId;

    /**
     * 用户ID
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * skuId
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 核销数量
     */
    @TableField("consume_num")
    private Integer consumeNum;

    /**
     * 是否核销:1核销;0撤销
     */
    @TableField("consume_flag")
    private Boolean consumeFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFlashSaleId() {
        return flashSaleId;
    }

    public void setFlashSaleId(Long flashSaleId) {
        this.flashSaleId = flashSaleId;
    }

    public Long getFlashSaleDetailId() {
        return flashSaleDetailId;
    }

    public void setFlashSaleDetailId(Long flashSaleDetailId) {
        this.flashSaleDetailId = flashSaleDetailId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getConsumeNum() {
        return consumeNum;
    }

    public void setConsumeNum(Integer consumeNum) {
        this.consumeNum = consumeNum;
    }

    public Boolean getConsumeFlag() {
        return consumeFlag;
    }

    public void setConsumeFlag(Boolean consumeFlag) {
        this.consumeFlag = consumeFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "FlashSaleConsumeRecord{" +
                "id=" + id +
                ", flashSaleId=" + flashSaleId +
                ", flashSaleDetailId=" + flashSaleDetailId +
                ", memberId=" + memberId +
                ", productId=" + productId +
                ", skuId=" + skuId +
                ", orderId=" + orderId +
                ", consumeNum=" + consumeNum +
                ", consumeFlag=" + consumeFlag +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
