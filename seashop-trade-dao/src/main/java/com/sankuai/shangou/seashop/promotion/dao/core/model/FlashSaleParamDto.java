package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:限购活动响应体
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
public class FlashSaleParamDto {

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品id列表
     */
    private List<Long> productIdList;

    /**
     * 状态：1待审核,2进行中,3未通过,4已结束,5已取消,6未开始
     */
    private List<Integer> statusList;

    /**
     * 活动开始日期
     */
    private Date beginDate;

    /**
     * 活动结束日期
     */
    private Date endDate;

    /**
     * 排除的活动ID
     */
    private Long excludeId;

    /**
     * 活动ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String title;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺ID列表
     */
    private List<Long> shopIdList;

    /**
     * 状态：1待审核 2进行中 3未通过 4已结束 5已取消 6未开始
     */
    private Integer status;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 是否前端显示
     */
    private Boolean frontFlag;

    /**
     * 查询是否正常（未开始和进行中的）
     */
    private Boolean normalFlag;

    /**
     * 限购活动ID列表
     */
    private List<Long> flashSaleIds;

}
