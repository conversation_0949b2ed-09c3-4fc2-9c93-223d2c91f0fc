package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStock;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SkuStockMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/16 15:56
 */
@Repository
@Slf4j
public class SkuStockRepository extends ServiceImpl<SkuStockMapper, SkuStock> {

    /**
     * 根据skuId 更新
     *
     * @param skuStock sku库存对象
     */
    public void updateBySkuId(SkuStock skuStock) {
        baseMapper.update(skuStock, new LambdaQueryWrapper<SkuStock>().eq(SkuStock::getSkuId, skuStock.getSkuId()));
    }

    /**
     * 根据skuId 批量更新
     *
     * @param skuStockList sku库存对象集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchBySkuId(List<SkuStock> skuStockList) {
        if (CollectionUtils.isEmpty(skuStockList)) {
            return;
        }
        skuStockList = skuStockList.stream().filter(sku -> sku.getStock() != null || sku.getSafeStock() != null).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(skuStockList)) {
            return;
        }
        // MybatisUtil.executeBatch(skuList -> baseMapper.updateBatchBySkuId(skuList), skuStockList);
        // todo 后续优化修改成批量修改
        skuStockList.forEach(skuStock -> {
            baseMapper.update(skuStock, new LambdaQueryWrapper<SkuStock>().eq(SkuStock::getSkuId, skuStock.getSkuId()));
        });
    }

    /**
     * 根据skuAutoId 更新
     *
     * @param skuStock sku库存对象
     */
    public void updateBySkuAutoId(SkuStock skuStock) {
        baseMapper.update(skuStock, new LambdaQueryWrapper<SkuStock>().eq(SkuStock::getSkuAutoId, skuStock.getSkuAutoId()));
    }

    /**
     * 根据skuId 批量更新
     *
     * @param updSkuStock sku库存对象
     * @param skuIdList   skuId集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchBySkuId(SkuStock updSkuStock, List<String> skuIdList) {
        if (CollectionUtils.isEmpty(skuIdList)) {
            return;
        }

        MybatisUtil.executeBatch(ids -> baseMapper.update(updSkuStock, new LambdaQueryWrapper<SkuStock>().in(SkuStock::getSkuId, ids)), skuIdList);
    }

    /**
     * 根据productId 批量更新
     *
     * @param updSkuStock   sku库存对象
     * @param productIdList 商品id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchByProductId(SkuStock updSkuStock, List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }
        MybatisUtil.executeBatch(ids -> baseMapper.update(updSkuStock, new LambdaQueryWrapper<SkuStock>().in(SkuStock::getProductId, ids)), productIdList);
    }


    /**
     * 根据skuAutoId 查询库存信息
     *
     * @param autoIds sku自增id集合
     * @return 库存信息集合
     */
    public List<SkuStock> listBySkuAutoIds(List<Long> autoIds) {
        if (CollectionUtils.isEmpty(autoIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<SkuStock>().in(SkuStock::getSkuAutoId, ids)), autoIds);
    }

    /**
     * 根据商品id的集合 查询库存信息
     *
     * @param productIdList 商品id集合
     * @return 库存信息集合
     */
    public List<SkuStock> listByProductIds(List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.EMPTY_LIST;
        }

        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<SkuStock>().in(SkuStock::getProductId, ids)), productIdList);
    }

    public Page<SkuStock> listPage(Long shopId, Integer pageNo, Integer pageSize) {
        return PageHelper.startPage(pageNo, pageSize)
                .doSelectPage(() -> baseMapper.selectList(new LambdaQueryWrapper<SkuStock>().eq(SkuStock::getShopId, shopId).orderByDesc(SkuStock::getUpdateTime)));
    }


    public List<SkuStock> listByShopIdAndSkuAutoIds(Long shopId, List<Long> skuAutoIds) {
        if (CollectionUtils.isEmpty(skuAutoIds)) {
            return Collections.EMPTY_LIST;
        }
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<SkuStock>().eq(SkuStock::getShopId, shopId).in(SkuStock::getSkuAutoId, ids)), skuAutoIds);
    }

    public List<SkuStock> listByShopIdAndSkuCodes(Long shopId, List<String> skuCodes) {
        if (CollectionUtils.isEmpty(skuCodes)) {
            return Collections.EMPTY_LIST;
        }
        return MybatisUtil.queryBatch(codes -> list(new LambdaQueryWrapper<SkuStock>().eq(SkuStock::getShopId, shopId).in(SkuStock::getSkuCode, codes)), skuCodes);
    }

    public List<SkuStock> listBySkuIds(List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_LIST;
        }
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<SkuStock>().in(SkuStock::getSkuId, ids)), skuIds);
    }

    public List<SkuStock> listBySkuIdsForUpdate(List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> baseMapper.listBySkuIdsForUpdate(ids), skuIds);
    }

    public void removeBySkuIds(List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }
        MybatisUtil.executeBatch(ids -> remove(new LambdaQueryWrapper<SkuStock>().in(SkuStock::getSkuId, ids)), skuIds);
    }

    public Long getTotalStock(Long productId) {
        return listByProductIds(Collections.singletonList(productId)).stream().mapToLong(SkuStock::getStock).sum();
    }

}
