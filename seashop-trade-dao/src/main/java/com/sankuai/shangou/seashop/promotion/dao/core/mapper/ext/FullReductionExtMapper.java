package com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext;

import com.sankuai.shangou.seashop.promotion.dao.core.domain.FullReduction;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/23/023
 * @description:
 */
public interface FullReductionExtMapper {

    /**
     * 查询所有正在进行或者未开始的满减活动
     *
     * @param shopId
     * @return
     */
    List<FullReduction> queryAllActiveFullReduction(@Param("shopId") Long shopId);

    /**
     * 查询当前正在进行的满减活动
     *
     * @return
     */
    FullReduction currentEnableFullReduction();
}
