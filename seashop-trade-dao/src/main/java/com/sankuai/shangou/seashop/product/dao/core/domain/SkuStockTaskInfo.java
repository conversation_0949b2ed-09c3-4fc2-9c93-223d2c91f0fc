package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 库存变更任务明细
 * </p>
 *
 * <AUTHOR> @since 2024-01-18
 */
public class SkuStockTaskInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 库存变更任务id
     */
    @TableField("task_id")
    private Long taskId;

    /**
     * 执行状态 0-待执行 1-执行中 2-执行成功 3-执行失败
     */
    @TableField("status")
    private Integer status;

    /**
     * 失败原因
     */
    @TableField("error_reason")
    private String errorReason;

    /**
     * 商品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * sku自增id
     */
    @TableField("sku_auto_id")
    private Long skuAutoId;

    /**
     * 商品ID_规格1ID_规格2ID_规格3ID
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 货号
     */
    @TableField("sku_code")
    private String skuCode;

    /**
     * 任务提交的库存, 变动库存/覆盖库存
     */
    @TableField("stock")
    private Long stock;

    /**
     * 店铺id, 根据skuCode更新必传
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 修改前的库存
     */
    @TableField("before_stock")
    private Long beforeStock;

    /**
     * 增加/减少的库存
     */
    @TableField("change_stock")
    private Long changeStock;

    /**
     * 修改后的库存
     */
    @TableField("after_stock")
    private Long afterStock;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTaskId() {
        return taskId;
    }

    public void setTaskId(Long taskId) {
        this.taskId = taskId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getErrorReason() {
        return errorReason;
    }

    public void setErrorReason(String errorReason) {
        this.errorReason = errorReason;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getSkuAutoId() {
        return skuAutoId;
    }

    public void setSkuAutoId(Long skuAutoId) {
        this.skuAutoId = skuAutoId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public Long getStock() {
        return stock;
    }

    public void setStock(Long stock) {
        this.stock = stock;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getBeforeStock() {
        return beforeStock;
    }

    public void setBeforeStock(Long beforeStock) {
        this.beforeStock = beforeStock;
    }

    public Long getChangeStock() {
        return changeStock;
    }

    public void setChangeStock(Long changeStock) {
        this.changeStock = changeStock;
    }

    public Long getAfterStock() {
        return afterStock;
    }

    public void setAfterStock(Long afterStock) {
        this.afterStock = afterStock;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "SkuStockTaskInfo{" +
        "id=" + id +
        ", taskId=" + taskId +
        ", status=" + status +
        ", errorReason=" + errorReason +
        ", productId=" + productId +
        ", skuAutoId=" + skuAutoId +
        ", skuId=" + skuId +
        ", skuCode=" + skuCode +
        ", stock=" + stock +
        ", shopId=" + shopId +
        ", beforeStock=" + beforeStock +
        ", changeStock=" + changeStock +
        ", afterStock=" + afterStock +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
