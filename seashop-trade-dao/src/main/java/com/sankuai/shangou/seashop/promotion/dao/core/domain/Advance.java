package com.sankuai.shangou.seashop.promotion.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 首页广告设置
 * </p>
 *
 * <AUTHOR> @since 2023-11-10
 */
public class Advance implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 首页广告设置
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 是否开启弹窗广告
     */
    @TableField("is_enable")
    private Boolean isEnable;

    /**
     * 广告位图片
     */
    @TableField("img")
    private String img;

    /**
     * 图片外联链接
     */
    @TableField("link")
    private String link;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 是否重复播放
     */
    @TableField("is_replay")
    private Boolean isReplay;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", exist = false)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", exist = false)
    private Date updateTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Boolean getIsEnable() {
        return isEnable;
    }

    public void setIsEnable(Boolean isEnable) {
        this.isEnable = isEnable;
    }

    public String getImg() {
        return img;
    }

    public void setImg(String img) {
        this.img = img;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Boolean getIsReplay() {
        return isReplay;
    }

    public void setIsReplay(Boolean isReplay) {
        this.isReplay = isReplay;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "Advance{" +
                "id=" + id +
                ", isEnable=" + isEnable +
                ", img=" + img +
                ", link=" + link +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", isReplay=" + isReplay +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
