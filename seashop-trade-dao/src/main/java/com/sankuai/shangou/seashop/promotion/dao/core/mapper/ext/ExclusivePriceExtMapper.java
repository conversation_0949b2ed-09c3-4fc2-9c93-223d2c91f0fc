package com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext;

import com.sankuai.shangou.seashop.promotion.dao.core.domain.ExclusivePrice;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceExtDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.ExclusivePriceParamDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
public interface ExclusivePriceExtMapper {

    /**
     * 根据参数查询数量
     *
     * @param paramDto
     * @return
     */
    List<ExclusivePrice> listByParams(ExclusivePriceParamDto paramDto);

    /**
     * 根据参数查询数量
     *
     * @param paramDto
     * @return
     */
    List<ExclusivePriceExtDto> listExtByParams(ExclusivePriceParamDto paramDto);

    /**
     * 查询所有正在进行或者未开始的专享价活动
     *
     * @param shopId
     * @return
     */
    List<ExclusivePrice> queryAllActiveExclusivePrice(@Param("shopId") Long shopId);
}
