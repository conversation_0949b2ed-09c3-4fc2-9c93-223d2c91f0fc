package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockAudit;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SkuStockAuditMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/23 11:18
 */
@Repository
@Slf4j
public class SkuStockAuditRepository extends ServiceImpl<SkuStockAuditMapper, SkuStockAudit>  {
    public List<SkuStockAudit> listByProductIds(List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return null;
        }

        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<SkuStockAudit>()
                .in(SkuStockAudit::getProductId, ids)), productIdList);
    }
}
