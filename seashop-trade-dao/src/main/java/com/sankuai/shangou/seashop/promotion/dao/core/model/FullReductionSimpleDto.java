package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:满减活动列表响应体
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class FullReductionSimpleDto extends ActiveBaseDto {

    /** 主键ID */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 店铺名称 */
    private String shopName;

    /** 活动名称 */
    private String activeName;

    /** 单笔订单满减金额门槛 */
    private BigDecimal moneyOffCondition;

    /** 单笔订单满减金额 */
    private BigDecimal moneyOffFee;

    //    /** 开始时间 */
    //    private Date startTime;
    //
    //    /** 结束时间 */
    //    private Date endTime;

    //    /** 状态 */
    //    private Integer status;
    //
    //    /** 状态名称 */
    //    private String statusDesc;

    /** 是否叠加优惠 */
    private Boolean moneyOffOverLay;

}
