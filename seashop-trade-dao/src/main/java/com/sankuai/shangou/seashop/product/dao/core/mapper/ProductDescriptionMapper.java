package com.sankuai.shangou.seashop.product.dao.core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescription;
import com.sankuai.shangou.seashop.product.dao.core.model.DescriptionRelateProductDto;

/**
 * <p>
 * 商品详情表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-03-20
 */
public interface ProductDescriptionMapper extends EnhancedMapper<ProductDescription> {

    List<DescriptionRelateProductDto> listRelateProductCount(@Param("templateIds") List<Long> templateIds);
}
