package com.sankuai.shangou.seashop.promotion.dao.core.mapper;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.Collocation;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CollocationActivityRespDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.PageCollocationDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.PageMCollocationDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.PageSellerCollocationDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 组合购信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-11-10
 */
public interface CollocationMapper extends EnhancedMapper<Collocation> {

    List<PageCollocationDto> pageSellerCollocation(@Param("req") PageSellerCollocationDto req);

    List<PageCollocationDto> pageMCollocation(@Param("req") PageMCollocationDto req);

    List<CollocationActivityRespDto> queryCollocationByProductIdsAndStatus(@Param("productIds") List<String> productIds, @Param("flag") Integer flag);
}
