package com.sankuai.shangou.seashop.trade.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 购物车表
 * </p>
 *
 * <AUTHOR> @since 2023-11-11
 */
@Data
@TableName("trade_shopping_cart")
public class ShoppingCart implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 商品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * skuid
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 购买数量
     */
    @TableField("quantity")
    private Long quantity;

    private Integer platform;

    /**
     * 添加时间
     */
    @TableField("add_time")
    private Date addTime;

    /**
     * 是否选中
     */
    @TableField("whether_select")
    private Boolean whetherSelect;

    /**
     * 创建时间(等价于原add_time，Java版统一添加)
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Boolean getWhetherSelect() {
        return whetherSelect;
    }

    public void setWhetherSelect(Boolean whetherSelect) {
        this.whetherSelect = whetherSelect;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ShoppingCart{" +
        "id=" + id +
        ", userId=" + userId +
        ", productId=" + productId +
        ", skuId=" + skuId +
        ", quantity=" + quantity +
        ", addTime=" + addTime +
        ", whetherSelect=" + whetherSelect +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
