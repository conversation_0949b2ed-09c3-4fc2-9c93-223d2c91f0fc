package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductImage;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductImageMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:05
 */
@Repository
@Slf4j
public class ProductImageRepository extends ServiceImpl<ProductImageMapper, ProductImage> {

    /**
     * 根据商品id查询商品图片
     *
     * @param productId 商品id
     * @return 商品图片
     */
    public List<String> listImagesByProductId(Long productId) {
        return listObjs(new LambdaQueryWrapper<ProductImage>().eq(ProductImage::getProductId, productId).select(ProductImage::getImageUrl), String::valueOf);
    }

    /**
     * 根据商品id的集合查询商品图片
     *
     * @param productIds 商品id的集合
     * @return 商品图片
     */
    public List<ProductImage> listByProductIds(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<ProductImage>().in(ProductImage::getProductId, ids)), productIds);
    }

    /**
     * 根据商品id的集合查询商品图片map
     *
     * @param productIds 商品id的集合
     * @return 商品图片map
     */
    public Map<Long, List<String>> getImageMap(List<Long> productIds) {
        List<ProductImage> productImages = listByProductIds(productIds);
        if (CollectionUtils.isEmpty(productImages)) {
            return Collections.EMPTY_MAP;
        }

        return productImages.stream().collect(Collectors.groupingBy(ProductImage::getProductId,
                Collectors.mapping(ProductImage::getImageUrl, Collectors.toList())));
    }
}
