package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class CouponRecordParamDto {

    private Long couponId;

    private Long userId;

    private Integer status;

    private Long shopId;

    private List<Long> shopIds;

    private String shopName;

    private String couponName;

    /** 订单ID */
    private String orderId;

    /** 领用人账号 */
    private String userName;

    /** 领取时间-开始 */
    private Date startReceiveTime;

    /** 领取时间-结束 */
    private Date endReceiveTime;

    /** 使用时间-开始 */
    private Date startUseTime;

    /** 使用时间-结束 */
    private Date endUseTime;

    /** 排序 */
    private String sortSql;
}
