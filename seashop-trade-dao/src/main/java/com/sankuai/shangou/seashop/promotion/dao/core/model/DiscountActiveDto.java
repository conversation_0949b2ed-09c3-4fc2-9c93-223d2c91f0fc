package com.sankuai.shangou.seashop.promotion.dao.core.model;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description: 折扣活动响应体
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class DiscountActiveDto extends BaseThriftDto {

    /** 主键ID */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 活动名称 */
    private String activeName;

    /** 开始时间 */
    private Date startTime;

    /** 结束时间 */
    private Date endTime;

    /** 是否全部商品 */
    private Boolean izAllProduct;

    /** 折扣规则 */
    private List<DiscountActiveRuleDto> ruleList;

    /** 商品列表 */
    private List<DiscountActiveProductDto> productList;

}
