package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/7 10:22
 */
@Data
public class CollocationActivityRespDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 活动id
     */
    private String id;

    /**
     * 活动标题
     */
    private String title;

    /**
     * 活动开始时间
     */
    private Date startTime;

    /**
     * 活动结束时间
     */
    private Date endTime;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 是否主商品
     */
    private Boolean mainFlag;

    /**
     * 活动状态
     */
    private String statusName;


}
