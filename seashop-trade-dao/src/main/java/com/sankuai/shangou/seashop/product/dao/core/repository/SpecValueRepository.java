package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.product.dao.core.domain.SpecValue;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SpecValueMapper;
import com.sankuai.shangou.seashop.product.dao.core.model.SpecValueDto;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/07/18 15:30
 */
@Repository
@Slf4j
public class SpecValueRepository  extends ServiceImpl<SpecValueMapper, SpecValue> {
    public List<SpecValue> getByCondition(SpecValueDto param) {
        LambdaQueryWrapper<SpecValue> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(param.getShopId() != null, SpecValue::getShopId, param.getShopId());
        wrapper.eq(param.getNameId() != null, SpecValue::getNameId, param.getNameId());
        wrapper.in(CollUtil.isNotEmpty(param.getValues()), SpecValue::getValue, param.getValues());
        return list(wrapper);
    }
}
