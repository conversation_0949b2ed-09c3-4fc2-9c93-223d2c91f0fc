package com.sankuai.shangou.seashop.promotion.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 组合购商品表
 * </p>
 *
 * <AUTHOR> @since 2023-11-10
 */
public class CollocationProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 组合购ID
     */
    @TableField("collo_id")
    private Long colloId;

    /**
     * 是否主商品
     */
    @TableField("main_flag")
    private Boolean mainFlag;

    /**
     * 排序
     */
    @TableField("display_sequence")
    private Integer displaySequence;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getColloId() {
        return colloId;
    }

    public void setColloId(Long colloId) {
        this.colloId = colloId;
    }

    public Boolean getMainFlag() {
        return mainFlag;
    }

    public void setMainFlag(Boolean mainFlag) {
        this.mainFlag = mainFlag;
    }

    public Integer getDisplaySequence() {
        return displaySequence;
    }

    public void setDisplaySequence(Integer displaySequence) {
        this.displaySequence = displaySequence;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CollocationProduct{" +
        "id=" + id +
        ", productId=" + productId +
        ", colloId=" + colloId +
        ", mainFlag=" + mainFlag +
        ", displaySequence=" + displaySequence +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
