package com.sankuai.shangou.seashop.promotion.dao.core.model;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description: 商城限时购查询条件对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class MallFlashSaleParamDto extends BasePageReq {

    private List<Long> productIds;

    private Long categoryId;

    /**
     * 是否按照销量排序
     */
    private Boolean saleCountSort;

    /**
     * 是否前端显示
     */
    private Boolean frontFlag;
}
