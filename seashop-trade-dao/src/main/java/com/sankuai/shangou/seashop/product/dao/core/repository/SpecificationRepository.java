package com.sankuai.shangou.seashop.product.dao.core.repository;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.shangou.seashop.product.dao.core.domain.SpecName;
import com.sankuai.shangou.seashop.product.dao.core.domain.SpecValue;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SpecNameMapper;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SpecValueMapper;

@Repository
public class SpecificationRepository {

    @Resource
    private SpecNameMapper specNameMapper;
    @Resource
    private SpecValueMapper specValueMapper;

    public boolean existName(Long shopId, String name) {
        QueryWrapper<SpecName> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("shop_id", shopId);
        queryWrapper.eq("spec_name", name);
        return specNameMapper.exists(queryWrapper);
    }

    public boolean existValue(Long nameId, String value) {
        QueryWrapper<SpecValue> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name_id", nameId);
        queryWrapper.eq("value", value);
        return specValueMapper.exists(queryWrapper);
    }





    public void remove(Long nameId) {
        specNameMapper.deleteById(nameId);
        QueryWrapper<SpecValue> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name_id", nameId);
        specValueMapper.delete(queryWrapper);
    }

    public Long createName(Long shopId, String name) {
        SpecName info = new SpecName();
        info.setShopId(shopId);
        info.setSpecName(name);
        info.setSpecAlias(name);
        specNameMapper.insert(info);
        return info.getId();
    }

    public Long createValue(Long shopId, Long nameId, String value) {
        SpecValue info = new SpecValue();
        info.setShopId(shopId);
        info.setNameId(nameId);
        info.setValue(value);
        specValueMapper.insert(info);
        return info.getId();
    }


}
