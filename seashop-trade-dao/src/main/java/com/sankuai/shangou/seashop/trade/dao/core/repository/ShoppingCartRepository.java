package com.sankuai.shangou.seashop.trade.dao.core.repository;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.baomidou.dynamic.datasource.annotation.Master;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;
import com.sankuai.shangou.seashop.trade.dao.core.mapper.CustomShoppingCartMapper;
import com.sankuai.shangou.seashop.trade.dao.core.mapper.ShoppingCartMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * 购物车仓储
 * <AUTHOR>
 */
@Repository
@Slf4j
public class ShoppingCartRepository extends ServiceImpl<ShoppingCartMapper, ShoppingCart> {

    @Resource
    private ShoppingCartMapper shoppingCartMapper;
    @Resource
    private CustomShoppingCartMapper customShoppingCartMapper;

    /**
     * 根据用户ID获取购物车
     * <p>Notice: 这里暂时不强制主库，用户添加完购物车到进入列表有一定时延，秒级的主从延时应该都能接受</p>
     * <AUTHOR>
     * @param userId 商家用户ID
     */
    public List<ShoppingCart> queryByUserId(Long userId) {
        LambdaQueryWrapper<ShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShoppingCart::getUserId, userId)
                .orderByDesc(ShoppingCart::getAddTime);
        return shoppingCartMapper.selectList(queryWrapper);
    }

    /**
     * 根据用户ID和商品SKUID获取购物车数据，用于判断是否已经添加过该商品到购物车中了
     * <p>这里强制走主库</p>
     * <AUTHOR>
     * @param userId 商家用户ID
	 * @param skuId skuId
     */
    @Master
    public ShoppingCart queryByUserIdAndSkuIdForceMaster(Long userId, String skuId) {
        LambdaQueryWrapper<ShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShoppingCart::getUserId, userId)
                .eq(ShoppingCart::getSkuId, skuId);
        List<ShoppingCart> shoppingCarts = shoppingCartMapper.selectList(queryWrapper);
        if (!shoppingCarts.isEmpty()) {
            return shoppingCarts.get(0);
        }
        return null;
    }

    /**
     * 添加购物车数据
     * <AUTHOR>
     * @param record
     * int
     */
    public int insert(ShoppingCart record) {
        record.setWhetherSelect(false);
        return shoppingCartMapper.insert(record);
    }

    /**
     * 根据主键ID获取购物车数据
     * <AUTHOR>
     * @param idList 购物车主键ID
     */
    public List<ShoppingCart> queryByIdList(List<Long> idList) {
        LambdaQueryWrapper<ShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ShoppingCart::getId, idList)
                .orderByDesc(ShoppingCart::getAddTime);
        return shoppingCartMapper.selectList(queryWrapper);
    }

    /**
     * 批量删除购物车数据
     * <AUTHOR>
     * @param userId 待删除的用户ID
	 * @param idList 需要删除的购物车ID列表
     */
    public int deleteByIdList(Long userId, List<Long> idList) {
        LambdaUpdateWrapper<ShoppingCart> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ShoppingCart::getUserId, userId)
                .in(ShoppingCart::getId, idList);
        return shoppingCartMapper.delete(updateWrapper);
    }

    /**
     * 根据用户ID获取购物车商品SKU数量
     * <AUTHOR>
     * @param userId
     * long
     */
    public int getUserSkuCount(Long userId) {
        LambdaQueryWrapper<ShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShoppingCart::getUserId, userId);
        return Math.toIntExact(shoppingCartMapper.selectCount(queryWrapper));
    }

    /**
     * 增加购物车sku数量
     * <AUTHOR>
     * @param id 购物车主键
	 * @param skuCount 待增加的数量
     * int
     */
    public int increaseSkuQuantity(Long id, Long skuCount) {
        return customShoppingCartMapper.increaseSkuQuantity(id, skuCount);
    }

    /**
     * 增加购物车sku数量并覆盖是否选中
     * <AUTHOR>
     * @param id 购物车主键
     * @param skuCount 待增加的数量
     * @param selected 是否选中，如果为null则不修改
     * int
     */
    public int increaseSkuQuantityAndOverSelect(Long id, Long skuCount, Boolean selected) {
        return customShoppingCartMapper.increaseSkuQuantityAndOverSelect(id, skuCount, selected);
    }

    /**
     * 修改购物车SKU数量
     * <AUTHOR>
     * @param id 购物车主键
     * @param skuCount 待增加的数量
     * int
     */
    public int updateSkuQuantity(Long id, Long skuCount) {
        return customShoppingCartMapper.updateSkuQuantity(id, skuCount);
    }

    /**
     * 修改购物车是否选中标识
     * <AUTHOR>
     * @param id 购物车主键
     * @param izSelect 是否选中
     * int
     */
    public int updateWhetherSelect(Long id, boolean izSelect) {
        return customShoppingCartMapper.updateWhetherSelect(id, izSelect);
    }

    /**
     * 修改购物车是否选中标识
     * <AUTHOR>
     * @param idList 购物车主键
     * @param izSelect 是否选中
     * int
     */
    public boolean updateWhetherSelectBatch(List<Long> idList, boolean izSelect) {
        LambdaUpdateWrapper<ShoppingCart> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(ShoppingCart::getId, idList)
                .set(ShoppingCart::getWhetherSelect, izSelect);
        return this.update(wrapper);
    }

    /**
     * 删除用户指定的SKU
     * <AUTHOR>
     * @param userId 用户ID
	 * @param skuIdList  skuId列表
     * void
     */
    public void removeByUserIdAndSku(Long userId, List<String> skuIdList) {
        LambdaQueryWrapper<ShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShoppingCart::getUserId, userId)
                .in(ShoppingCart::getSkuId, skuIdList);
        baseMapper.delete(queryWrapper);
    }

    /**
     * 根据用户ID和商品ID获取购物车数据
     * <p>这里强制走主库</p>
     * @param userId 用户ID
     * @param productIds 商品ID
     * @return 购物车数据
     */
    @Master
    public List<ShoppingCart> getByUserAndProductIds(Long userId, List<Long> productIds) {
        LambdaQueryWrapper<ShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShoppingCart::getUserId, userId)
                .in(ShoppingCart::getProductId, productIds);
        return shoppingCartMapper.selectList(queryWrapper);
    }
    public List<ShoppingCart> getByCreateTime(Date createTime) {
        LambdaQueryWrapper<ShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        if (createTime != null) {
            queryWrapper.ge(ShoppingCart::getCreateTime, createTime);
        }

        return baseMapper.selectList(queryWrapper);
    }

    public List<ShoppingCart> getByUpdateTime(Date updateTimer) {
        LambdaQueryWrapper<ShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        if (updateTimer != null) {
            queryWrapper.ge(ShoppingCart::getUpdateTime, updateTimer);
        }

        return baseMapper.selectList(queryWrapper);
    }

}
