package com.sankuai.shangou.seashop.promotion.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 限时购配置表
 * </p>
 *
 * <AUTHOR> @since 2023-12-11
 */
public class FlashSaleConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID（平台的ID默认=0）
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 预热时间（店铺配置）
     */
    @TableField("preheat")
    private Integer preheat;

    /**
     * 是否允许正常购买（店铺配置）
     */
    @TableField("normal_purchase_flag")
    private Boolean normalPurchaseFlag;

    /**
     * 是否需要审核（平台配置）
     */
    @TableField("need_audit_flag")
    private Boolean needAuditFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", exist = false)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", exist = false)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getPreheat() {
        return preheat;
    }

    public void setPreheat(Integer preheat) {
        this.preheat = preheat;
    }

    public Boolean getNormalPurchaseFlag() {
        return normalPurchaseFlag;
    }

    public void setNormalPurchaseFlag(Boolean normalPurchaseFlag) {
        this.normalPurchaseFlag = normalPurchaseFlag;
    }

    public Boolean getNeedAuditFlag() {
        return needAuditFlag;
    }

    public void setNeedAuditFlag(Boolean needAuditFlag) {
        this.needAuditFlag = needAuditFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "FlashSaleConfig{" +
                "id=" + id +
                ", shopId=" + shopId +
                ", preheat=" + preheat +
                ", normalPurchaseFlag=" + normalPurchaseFlag +
                ", needAuditFlag=" + needAuditFlag +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
