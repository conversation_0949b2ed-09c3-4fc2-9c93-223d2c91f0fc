package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuStockTask;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SkuStockTaskMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/20 14:27
 */
@Repository
@Slf4j
public class SkuStockTaskRepository extends ServiceImpl<SkuStockTaskMapper, SkuStockTask> {

    public SkuStockTask getStockTask(Integer updateType, String bizCode, String seqCode) {
        return getOne(new LambdaQueryWrapper<SkuStockTask>()
                .eq(SkuStockTask::getUpdateType, updateType)
                .eq(SkuStockTask::getBizCode, bizCode)
                .eq(SkuStockTask::getSeqCode, seqCode));
    }

    public List<SkuStockTask> listStockTask(Integer updateType, String bizCode) {
        return list(new LambdaQueryWrapper<SkuStockTask>()
                .eq(SkuStockTask::getUpdateType, updateType)
                .eq(SkuStockTask::getBizCode, bizCode));
    }

    public SkuStockTask getByIdForceMaster(Long id) {
        return getById(id);
    }

    public int countByUpdateTypeAndBizCode(Integer updateType, String rollBackBizCode) {
        return Math.toIntExact(count(new LambdaQueryWrapper<SkuStockTask>()
            .eq(SkuStockTask::getUpdateType, updateType).eq(SkuStockTask::getBizCode, rollBackBizCode)));
    }
}
