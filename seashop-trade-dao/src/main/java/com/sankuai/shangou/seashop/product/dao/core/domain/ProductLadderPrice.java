package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 商品梯度价格表
 * </p>
 *
 * <AUTHOR> @since 2023-11-25
 */
public class ProductLadderPrice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 阶梯价格ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 最小批量
     */
    @TableField("min_bath")
    private Integer minBath;

    /**
     * 最大批量
     */
    @TableField("max_bath")
    private Integer maxBath;

    /**
     * 价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getMinBath() {
        return minBath;
    }

    public void setMinBath(Integer minBath) {
        this.minBath = minBath;
    }

    public Integer getMaxBath() {
        return maxBath;
    }

    public void setMaxBath(Integer maxBath) {
        this.maxBath = maxBath;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ProductLadderPrice{" +
        "id=" + id +
        ", productId=" + productId +
        ", minBath=" + minBath +
        ", maxBath=" + maxBath +
        ", price=" + price +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
