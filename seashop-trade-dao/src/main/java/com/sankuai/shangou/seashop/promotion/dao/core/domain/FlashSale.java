package com.sankuai.shangou.seashop.promotion.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 限时活动表
 * </p>
 *
 * <AUTHOR> @since 2023-12-12
 */
public class FlashSale implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 标题
     */
    @TableField("title")
    private String title;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 产品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 状态：1待审核,2进行中,3未通过,4已结束,5已取消,6未开始
     */
    @TableField("status")
    private Integer status;

    /**
     * 活动开始日期
     */
    @TableField("begin_date")
    private Date beginDate;

    /**
     * 活动结束日期
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 限购类型:1商品;2规格
     */
    @TableField("limit_type")
    private Integer limitType;

    /**
     * 限购数量
     */
    @TableField("limit_count")
    private Integer limitCount;

    /**
     * 仅仅只计算在限时购里的销售数
     */
    @TableField("sale_count")
    private Integer saleCount;

    /**
     * 活动id
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 活动分类：默认是限时购分类
     */
    @TableField("category_name")
    private String categoryName;

    /**
     * 跳转路径
     */
    @TableField("url_path")
    private String urlPath;

    /**
     * 最小价格（多规格价格可能不一样）
     */
    @TableField("min_price")
    private BigDecimal minPrice;

    /**
     * 前端是否显示
     */
    @TableField("front_flag")
    private Boolean frontFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getBeginDate() {
        return beginDate;
    }

    public void setBeginDate(Date beginDate) {
        this.beginDate = beginDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getLimitType() {
        return limitType;
    }

    public void setLimitType(Integer limitType) {
        this.limitType = limitType;
    }

    public Integer getLimitCount() {
        return limitCount;
    }

    public void setLimitCount(Integer limitCount) {
        this.limitCount = limitCount;
    }

    public Integer getSaleCount() {
        return saleCount;
    }

    public void setSaleCount(Integer saleCount) {
        this.saleCount = saleCount;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getUrlPath() {
        return urlPath;
    }

    public void setUrlPath(String urlPath) {
        this.urlPath = urlPath;
    }

    public BigDecimal getMinPrice() {
        return minPrice;
    }

    public void setMinPrice(BigDecimal minPrice) {
        this.minPrice = minPrice;
    }

    public Boolean getFrontFlag() {
        return frontFlag;
    }

    public void setFrontFlag(Boolean frontFlag) {
        this.frontFlag = frontFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "FlashSale{" +
        "id=" + id +
        ", title=" + title +
        ", shopId=" + shopId +
        ", productId=" + productId +
        ", status=" + status +
        ", beginDate=" + beginDate +
        ", endDate=" + endDate +
        ", limitType=" + limitType +
        ", limitCount=" + limitCount +
        ", saleCount=" + saleCount +
        ", categoryId=" + categoryId +
        ", categoryName=" + categoryName +
        ", urlPath=" + urlPath +
        ", minPrice=" + minPrice +
        ", frontFlag=" + frontFlag +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
