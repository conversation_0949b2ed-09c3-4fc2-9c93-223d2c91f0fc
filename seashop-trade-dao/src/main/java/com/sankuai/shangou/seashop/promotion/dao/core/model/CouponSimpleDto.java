package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description: 优惠券活动响应体
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class CouponSimpleDto extends ActiveBaseDto {

    /** 主键ID */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 店铺名称 */
    private String shopName;

    /** 优惠劵金额 */
    private Long price;

    /** 最大可领取张数 */
    private Integer perMax;

    /** 订单金额（满足多少钱才能使用） */
    private Long orderAmount;

    /** 发行张数 */
    private Integer num;

    //    /** 开始时间 */
    //    private Date startTime;
    //
    //    /** 结束时间 */
    //    private Date endTime;

    /** 优惠券名称 */
    private String couponName;

    /** 使用范围：0=全场通用，1=部分商品可用 */
    private Integer useArea;

    /** 领用人数 */
    private Integer receiveCount = 0;

    /** 领用张数 */
    private Integer receiveNum = 0;

    /** 已使用数量 */
    private Integer useCount = 0;

    //    /** 状态 */
    //    private Integer status;
    //
    //    /** 状态名称 */
    //    private String statusDesc;

    /** 商品数量 */
    private Long productCount;

    /** 创建时间 */
    private Date createTime;
}
