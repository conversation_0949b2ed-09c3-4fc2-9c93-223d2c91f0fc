package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.dao.core.mapper.BrandMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:05
 */
@Repository
@Slf4j
public class BrandRepository extends ServiceImpl<BrandMapper, Brand> {

    /**
     * 查询品牌名称map
     *
     * @param brandIdList 品牌id的集合
     * @return 品牌名称map
     */
    public Map<Long, String> getBrandNameMap(List<Long> brandIdList) {
        if (CollectionUtils.isEmpty(brandIdList)) {
            return MapUtils.EMPTY_MAP;
        }

        List<Brand> brandList = getByIdList(brandIdList);

        return brandList.stream().collect(Collectors.toMap(Brand::getId, Brand::getName,
                (k1, k2) -> k2));
    }

    /**
     * 根据品牌名称查询品牌信息
     *
     * @param brandNames 品牌名称
     * @return 品牌信息
     */
    public List<Brand> getByBrandNames(List<String> brandNames) {
        if (CollectionUtils.isEmpty(brandNames)) {
            return Collections.EMPTY_LIST;
        }
        return MybatisUtil.queryBatch(names -> list(new LambdaQueryWrapper<Brand>().in(Brand::getName, names)), brandNames);
    }

    public List<Brand> getByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.EMPTY_LIST;
        }

        idList = idList.stream().distinct().collect(Collectors.toList());
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<Brand>().in(Brand::getId, ids).eq(Brand::getWhetherDelete, Boolean.FALSE)), idList);
    }

    public void logicDeleteById(Long id) {
        Brand brand = new Brand();
        brand.setId(id);
        brand.setWhetherDelete(Boolean.TRUE);
        updateById(brand);
    }
}
