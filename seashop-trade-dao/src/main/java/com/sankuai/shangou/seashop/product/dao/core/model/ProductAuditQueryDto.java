package com.sankuai.shangou.seashop.product.dao.core.model;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/16 12:43
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductAuditQueryDto {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * sku自增id
     */
    private Long skuAutoId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品编码
     */
    private String productCode;

    /**
     * 类目id
     */
    private String categoryId;

    /**
     * 店铺分类id
     */
    private Long shopCategoryId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 警戒库存
     */
    private Boolean stockWarnStatus;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 审核状态 1-待审核 2-销售中 3-未通过 4-违规下架
     */
    private Integer auditStatusCode;
}
