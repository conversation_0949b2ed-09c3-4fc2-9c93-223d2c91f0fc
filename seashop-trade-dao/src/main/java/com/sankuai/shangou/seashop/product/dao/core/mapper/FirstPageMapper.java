package com.sankuai.shangou.seashop.product.dao.core.mapper;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/12/11 16:23
 */
public interface FirstPageMapper extends EnhancedMapper{

    // 根据店铺ID查询商品总数
    Integer queryProductsCount(Long shopId);

    // 根据店铺ID查询商品草稿箱数
    Integer queryProductsInDraft(Long shopId);

    // 根据店铺ID查询商品在售数
    Integer queryOnSaleProducts(Long shopId);

    // 根据店铺ID查询违规下架数
    Integer queryInfractionSaleOffProducts(Long shopId);

    // 根据店铺ID查询仓库中数
    Integer queryInStockProducts(Long shopId);

    // 根据店铺ID查询待审核数
    Integer queryWaitForAuditingProducts(Long shopId);

    // 根据店铺ID查询审核失败数
    Integer queryAuditFailureProducts(Long shopId);

    // 根据店铺ID查询库存告警数
    Integer queryOverSafeStockProducts(Long shopId);

    // 根据店铺ID查询库存告警数
    Integer queryProductsBrands(Long shopId);
}
