package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 规格库存审核表
 * </p>
 *
 * <AUTHOR> @since 2024-01-23
 */
public class SkuStockAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 商品ID_规格1ID_规格2ID_规格3ID
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * sku自增id
     */
    @TableField("sku_auto_id")
    private Long skuAutoId;

    /**
     * 货号
     */
    @TableField("sku_code")
    private String skuCode;

    /**
     * 库存
     */
    @TableField("stock")
    private Long stock;

    /**
     * 警戒库存
     */
    @TableField("safe_stock")
    private Long safeStock;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Long getSkuAutoId() {
        return skuAutoId;
    }

    public void setSkuAutoId(Long skuAutoId) {
        this.skuAutoId = skuAutoId;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public Long getStock() {
        return stock;
    }

    public void setStock(Long stock) {
        this.stock = stock;
    }

    public Long getSafeStock() {
        return safeStock;
    }

    public void setSafeStock(Long safeStock) {
        this.safeStock = safeStock;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "SkuStockAudit{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", productId=" + productId +
        ", skuId=" + skuId +
        ", skuAutoId=" + skuAutoId +
        ", skuCode=" + skuCode +
        ", stock=" + stock +
        ", safeStock=" + safeStock +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
