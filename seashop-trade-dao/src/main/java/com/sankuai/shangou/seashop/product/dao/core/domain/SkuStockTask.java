package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 库存变更任务
 * </p>
 *
 * <AUTHOR> @since 2024-01-18
 */
public class SkuStockTask implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 修改类型 1-编辑商品 2-批量调整库存 3-批量导入库存 4-生成订单 5-生成订单失败6-牵牛花API生成订单 7-售后退回 8-订单关闭 9-聚水潭同步 10-牵牛花API关闭订单 11-旺店通同步 12-牵牛花API同步库存 13-网店管家/吉客云同步库存
     */
    @TableField("update_type")
    private Integer updateType;

    /**
     * 更新方式 1-调整库存(加减) 2-覆盖库存
     */
    @TableField("update_way")
    private Integer updateWay;

    /**
     * 更新key的类型 1-根据skuId更新 2-根据skuAutoId更新 3-根据skuCode更新
     */
    @TableField("update_key")
    private Integer updateKey;

    /**
     * 业务编码
     */
    @TableField("biz_code")
    private String bizCode;

    /**
     * 业务序号(biz_code + seq_code唯一)
     */
    @TableField("seq_code")
    private String seqCode;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人Id
     */
    @TableField("create_user")
    private Long createUser;

    /**
     * 更新人Id
     */
    @TableField("update_user")
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getUpdateType() {
        return updateType;
    }

    public void setUpdateType(Integer updateType) {
        this.updateType = updateType;
    }

    public Integer getUpdateWay() {
        return updateWay;
    }

    public void setUpdateWay(Integer updateWay) {
        this.updateWay = updateWay;
    }

    public Integer getUpdateKey() {
        return updateKey;
    }

    public void setUpdateKey(Integer updateKey) {
        this.updateKey = updateKey;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public String getSeqCode() {
        return seqCode;
    }

    public void setSeqCode(String seqCode) {
        this.seqCode = seqCode;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "SkuStockTask{" +
        "id=" + id +
        ", updateType=" + updateType +
        ", updateWay=" + updateWay +
        ", updateKey=" + updateKey +
        ", bizCode=" + bizCode +
        ", seqCode=" + seqCode +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", createUser=" + createUser +
        ", updateUser=" + updateUser +
        "}";
    }
}
