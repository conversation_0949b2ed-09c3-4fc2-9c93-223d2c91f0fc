package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.product.dao.core.domain.BrandApply;
import com.sankuai.shangou.seashop.product.dao.core.mapper.BrandApplyMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:05
 */
@Repository
@Slf4j
public class BrandApplyRepository extends ServiceImpl<BrandApplyMapper, BrandApply> {

    /**
     * 根据店铺id 查询已经申请的品牌id的集合
     *
     * @param shopId 店铺id
     * @return 品牌id集合
     */
    public List<Long> getBrandIdsByShopId(Long shopId, List<Integer> auditStatusList) {
        LambdaQueryWrapper<BrandApply> wrapper = new LambdaQueryWrapper<BrandApply>()
                .eq(BrandApply::getShopId, shopId)
                .eq(BrandApply::getWhetherDelete, Boolean.FALSE)
                .in(CollectionUtils.isNotEmpty(auditStatusList), BrandApply::getAuditStatus, auditStatusList)
                .select(BrandApply::getBrandId);

        return listObjs(wrapper, brandId -> Long.parseLong(String.valueOf(brandId)));
    }

    public void logicDeleteByBrandName(String name) {
        BrandApply apply = new BrandApply();
        apply.setWhetherDelete(Boolean.TRUE);
        update(apply, new LambdaQueryWrapper<BrandApply>().eq(BrandApply::getBrandName, name).eq(BrandApply::getWhetherDelete, Boolean.FALSE));
    }
}
