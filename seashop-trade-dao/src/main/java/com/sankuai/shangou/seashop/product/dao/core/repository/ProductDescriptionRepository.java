package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescription;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductDescriptionMapper;
import com.sankuai.shangou.seashop.product.dao.core.model.DescriptionRelateProductDto;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:05
 */
@Repository
@Slf4j
public class ProductDescriptionRepository extends ServiceImpl<ProductDescriptionMapper, ProductDescription> {

    /**
     * 根据商品id查询商品详情
     *
     * @param productId 商品id
     * @return 商品详情
     */
    public ProductDescription getProductDescriptionByProductId(Long productId) {
        return getOne(new LambdaQueryWrapper<ProductDescription>().eq(ProductDescription::getProductId, productId));
    }

    /**
     * 根据商品id批量更新商品详情
     *
     * @param productDescription 更新信息
     * @param productIdList      商品id集合
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchByProductId(ProductDescription productDescription, List<Long> productIdList) {
        MybatisUtil.executeBatch((ids) -> update(productDescription,
                new LambdaQueryWrapper<ProductDescription>().in(ProductDescription::getProductId, ids)), productIdList);
    }

    /**
     * 根据商品id更新商品详情
     *
     * @param productDescription 更新信息
     */
    public void updateByProductId(ProductDescription productDescription) {
        update(productDescription, new LambdaQueryWrapper<ProductDescription>().eq(ProductDescription::getProductId, productDescription.getProductId()));
    }

    /**
     * 根据商品id批量更新商品详情
     *
     * @param productDescriptionList 更新信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateBatchByProductId(List<ProductDescription> productDescriptionList) {
        if (CollectionUtils.isEmpty(productDescriptionList)) {
            return;
        }

        productDescriptionList.forEach(item -> updateByProductId(item));
    }

    public void logicRemoveByProductIds(List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return;
        }

        ProductDescription updDescription = new ProductDescription();
        updDescription.setDeleted(Boolean.TRUE);
        MybatisUtil.executeBatch((ids) -> update(updDescription, new LambdaQueryWrapper<ProductDescription>().in(ProductDescription::getProductId, ids)), productIdList);
    }

    public Map<Long, Long> getRelateProductMap(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return MapUtils.EMPTY_MAP;
        }

        List<List<Long>> templateIdsArr = Lists.partition(templateIds, CommonConstant.QUERY_LIMIT);
        List<DescriptionRelateProductDto> relateList = new ArrayList<>();
        templateIdsArr.forEach(ids -> {
            relateList.addAll(baseMapper.listRelateProductCount(ids));
        });

        return relateList.stream().collect(Collectors.groupingBy(DescriptionRelateProductDto::getTemplateId,
                Collectors.summingLong(DescriptionRelateProductDto::getRelateProductCount)));
    }

    public List<ProductDescription> listByProductIds(List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.emptyList();
        }

        productIdList = productIdList.stream().distinct().collect(Collectors.toList());
        return MybatisUtil.queryBatch((ids) -> list(new LambdaQueryWrapper<ProductDescription>().in(ProductDescription::getProductId, ids)), productIdList);
    }
}
