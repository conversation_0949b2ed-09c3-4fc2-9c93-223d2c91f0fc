package com.sankuai.shangou.seashop.product.dao.core.repository;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionTemplate;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductDescriptionTemplateMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/20 10:29
 */
@Repository
@Slf4j
public class DescriptionTemplateRepository extends ServiceImpl<ProductDescriptionTemplateMapper, ProductDescriptionTemplate> {

    /**
     * 根据Id 查询版式信息 (排除逻辑删除)
     */
    public ProductDescriptionTemplate getByIdExcludeDeleted(Long id) {
        return getOne(new LambdaQueryWrapper<ProductDescriptionTemplate>()
                .eq(ProductDescriptionTemplate::getId, id)
                .eq(ProductDescriptionTemplate::getWhetherDelete, false));
    }
}
