package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 商品SKU审核表
 * </p>
 *
 * <AUTHOR> @since 2024-01-09
 */
public class SkuAudit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID_颜色规格ID_颜色规格ID_尺寸规格
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 颜色规格
     */
    @TableField("spec1_value")
    private String spec1Value;

    /**
     * 尺寸规格
     */
    @TableField("spec2_value")
    private String spec2Value;

    /**
     * 版本规格
     */
    @TableField("spec3_value")
    private String spec3Value;

    /**
     * 规格值json
     */
    @TableField("spec_value_json")
    private String specValueJson;

    /**
     * SKU
     */
    @TableField("sku_code")
    private String skuCode;

    /**
     * 成本价
     */
    @TableField("cost_price")
    private BigDecimal costPrice;

    /**
     * 销售价
     */
    @TableField("sale_price")
    private BigDecimal salePrice;

    /**
     * 显示图片
     */
    @TableField("show_pic")
    private String showPic;

    /**
     * 计量单位
     */
    @TableField("measure_unit")
    private String measureUnit;

    /**
     * 是否已删除
     */
    @TableField("whether_delete")
    private Boolean whetherDelete;

    /**
     * 删除版本号 为0表示未删除
     */
    @TableField("delete_version")
    private Long deleteVersion;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getSpec1Value() {
        return spec1Value;
    }

    public void setSpec1Value(String spec1Value) {
        this.spec1Value = spec1Value;
    }

    public String getSpec2Value() {
        return spec2Value;
    }

    public void setSpec2Value(String spec2Value) {
        this.spec2Value = spec2Value;
    }

    public String getSpec3Value() {
        return spec3Value;
    }

    public void setSpec3Value(String spec3Value) {
        this.spec3Value = spec3Value;
    }

    public String getSkuCode() {
        return skuCode;
    }

    public void setSkuCode(String skuCode) {
        this.skuCode = skuCode;
    }

    public BigDecimal getCostPrice() {
        return costPrice;
    }

    public void setCostPrice(BigDecimal costPrice) {
        this.costPrice = costPrice;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public String getShowPic() {
        return showPic;
    }

    public void setShowPic(String showPic) {
        this.showPic = showPic;
    }

    public String getMeasureUnit() {
        return measureUnit;
    }

    public void setMeasureUnit(String measureUnit) {
        this.measureUnit = measureUnit;
    }

    public Boolean getWhetherDelete() {
        return whetherDelete;
    }

    public void setWhetherDelete(Boolean whetherDelete) {
        this.whetherDelete = whetherDelete;
    }

    public Long getDeleteVersion() {
        return deleteVersion;
    }

    public void setDeleteVersion(Long deleteVersion) {
        this.deleteVersion = deleteVersion;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getSpecValueJson() {
        return specValueJson;
    }

    public void setSpecValueJson(String specValueJson) {
        this.specValueJson = specValueJson;
    }

    @Override
    public String toString() {
        return "SkuAudit{" +
        "id=" + id +
        ", skuId=" + skuId +
        ", productId=" + productId +
        ", shopId=" + shopId +
        ", spec1Value=" + spec1Value +
        ", spec2Value=" + spec2Value +
        ", spec3Value=" + spec3Value +
        ", skuCode=" + skuCode +
        ", costPrice=" + costPrice +
        ", salePrice=" + salePrice +
        ", showPic=" + showPic +
        ", measureUnit=" + measureUnit +
        ", whetherDelete=" + whetherDelete +
        ", deleteVersion=" + deleteVersion +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
