package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductShopCategoryMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:05
 */
@Repository
@Slf4j
public class ProductShopCategoryRepository extends ServiceImpl<ProductShopCategoryMapper, ProductShopCategory> {

    /**
     * 批量更新关联关系
     *
     * @param shopCategoryId 店铺分类id
     * @param autoIds        关联表主键id的集合
     */
    public void batchUpdateRelate(Long shopCategoryId, List<Long> autoIds) {
        if (CollectionUtils.isEmpty(autoIds)) {
            return;
        }

        ProductShopCategory updRelate = new ProductShopCategory();
        updRelate.setShopCategoryId(shopCategoryId);
        MybatisUtil.executeBatch(ids -> baseMapper.update(updRelate,
                new LambdaQueryWrapper<ProductShopCategory>().in(ProductShopCategory::getId, ids)), autoIds);
    }

    public List<ProductShopCategory> listByProductIds(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        return baseMapper.selectList(new LambdaQueryWrapper<ProductShopCategory>().in(ProductShopCategory::getProductId, productIds));
    }
}
