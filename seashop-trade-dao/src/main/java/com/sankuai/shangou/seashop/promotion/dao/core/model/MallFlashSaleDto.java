package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/14/014
 * @description:商城限时购查询返回对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class MallFlashSaleDto {

    /** 主键ID */
    private Long id;

    /** 活动名称 */
    private String title;

    /** 店铺ID */
    private Long shopId;

    /** 产品ID */
    private Long productId;

    /** 产品名称 */
    private String productName;

    /** 跳转地址 */
    private String urlPath;

    /** 商品主图 */
    private String imagePath;

    /** 活动开始日期 */
    private Date beginDate;

    /** 活动结束日期 */
    private Date endDate;

    /** 预热时间（店铺配置） */
    private Integer preheat;

    /** 是否允许正常购买（店铺配置） */
    private Boolean normalPurchaseFlag;

    /** 商城价格 */
    private BigDecimal salePrice;

    /** 最小价格 */
    private BigDecimal minPrice;

    /** 销售数量 */
    private Integer saleCount;

}
