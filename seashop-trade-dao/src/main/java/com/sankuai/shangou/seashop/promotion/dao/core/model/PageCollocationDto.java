package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 16:51
 */
@Data
public class PageCollocationDto {

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 主商品ID
     */
    private Long mainProductId;

    /**
     * 主商品名称
     */
    private String mainProductName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态翻译
     */
    private String statusName;
}
