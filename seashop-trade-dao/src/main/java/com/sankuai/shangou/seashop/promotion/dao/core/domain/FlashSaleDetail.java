package com.sankuai.shangou.seashop.promotion.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 限时活动明细表
 * </p>
 *
 * <AUTHOR> @since 2023-12-12
 */
public class FlashSaleDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 对应FlashSale表主键
     */
    @TableField("flash_sale_id")
    private Long flashSaleId;

    @TableField("product_id")
    private Long productId;

    /**
     * 规格id
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 限时购时金额
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 活动库存
     */
    @TableField("total_count")
    private Integer totalCount;

    /**
     * 限购数量
     */
    @TableField("limit_count")
    private Integer limitCount;

    /**
     * 是否删除
     */
    @TableField("del_flag")
    private Boolean delFlag;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFlashSaleId() {
        return flashSaleId;
    }

    public void setFlashSaleId(Long flashSaleId) {
        this.flashSaleId = flashSaleId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public Integer getLimitCount() {
        return limitCount;
    }

    public void setLimitCount(Integer limitCount) {
        this.limitCount = limitCount;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "FlashSaleDetail{" +
        "id=" + id +
        ", flashSaleId=" + flashSaleId +
        ", productId=" + productId +
        ", skuId=" + skuId +
        ", price=" + price +
        ", totalCount=" + totalCount +
        ", limitCount=" + limitCount +
        ", delFlag=" + delFlag +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
