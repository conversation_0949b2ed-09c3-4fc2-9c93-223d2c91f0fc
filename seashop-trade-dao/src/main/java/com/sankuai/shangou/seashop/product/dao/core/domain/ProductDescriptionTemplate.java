package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 商品版式信息表
 * </p>
 *
 * <AUTHOR> @since 2023-11-25
 */
public class ProductDescriptionTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 板式名称
     */
    @TableField("name")
    private String name;

    /**
     * 位置（1-上、2-下）
     */
    @TableField("position")
    private Integer position;

    /**
     * PC端版式
     */
    @TableField("content")
    private String content;

    /**
     * 移动端版式
     */
    @TableField("mobile_content")
    private String mobileContent;

    /**
     * 是否已删除
     */
    @TableField("whether_delete")
    private Boolean whetherDelete;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人Id
     */
    @TableField("create_user")
    private Long createUser;

    /**
     * 更新人Id
     */
    @TableField("update_user")
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPosition() {
        return position;
    }

    public void setPosition(Integer position) {
        this.position = position;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getMobileContent() {
        return mobileContent;
    }

    public void setMobileContent(String mobileContent) {
        this.mobileContent = mobileContent;
    }

    public Boolean getWhetherDelete() {
        return whetherDelete;
    }

    public void setWhetherDelete(Boolean whetherDelete) {
        this.whetherDelete = whetherDelete;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "ProductDescriptionTemplate{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", name=" + name +
        ", position=" + position +
        ", content=" + content +
        ", mobileContent=" + mobileContent +
        ", whetherDelete=" + whetherDelete +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", createUser=" + createUser +
        ", updateUser=" + updateUser +
        "}";
    }
}
