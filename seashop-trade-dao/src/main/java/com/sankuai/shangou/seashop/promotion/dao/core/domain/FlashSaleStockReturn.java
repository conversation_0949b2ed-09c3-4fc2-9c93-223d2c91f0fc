package com.sankuai.shangou.seashop.promotion.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 限时购库存归还表
 * </p>
 *
 * <AUTHOR> @since 2024-01-17
 */
public class FlashSaleStockReturn implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 限时购ID
     */
    @TableField("flash_sale_id")
    private Long flashSaleId;

    /**
     * skuId
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 关联ID（售后ID,用于做幂等）
     */
    @TableField("relation_id")
    private Long relationId;

    /**
     * 库存归还数量
     */
    @TableField("stock_return_num")
    private Integer stockReturnNum;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", exist = false)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", exist = false)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFlashSaleId() {
        return flashSaleId;
    }

    public void setFlashSaleId(Long flashSaleId) {
        this.flashSaleId = flashSaleId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Long getRelationId() {
        return relationId;
    }

    public void setRelationId(Long relationId) {
        this.relationId = relationId;
    }

    public Integer getStockReturnNum() {
        return stockReturnNum;
    }

    public void setStockReturnNum(Integer stockReturnNum) {
        this.stockReturnNum = stockReturnNum;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "FlashSaleStockReturn{" +
                "id=" + id +
                ", flashSaleId=" + flashSaleId +
                ", skuId=" + skuId +
                ", orderId=" + orderId +
                ", relationId=" + relationId +
                ", stockReturnNum=" + stockReturnNum +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
