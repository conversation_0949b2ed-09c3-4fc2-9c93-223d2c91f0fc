package com.sankuai.shangou.seashop.product.dao.core.repository;

import com.sankuai.shangou.seashop.product.dao.core.mapper.FirstPageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/12/11 9:43
 */
@Repository
@Slf4j
public class FirstPageRepository {

    @Resource
    private FirstPageMapper firstPageMapper;

    // 根据店铺ID查询商品总数
    public Integer queryProductsCount(Long shopId){
        return firstPageMapper.queryProductsCount(shopId);
    }

    // 根据店铺ID查询商品草稿箱数
    public Integer queryProductsInDraft(Long shopId){
        return firstPageMapper.queryProductsInDraft(shopId);
    }

    // 根据店铺ID查询商品在售数
    public Integer queryOnSaleProducts(Long shopId){
        return firstPageMapper.queryOnSaleProducts(shopId);
    }

    // 根据店铺ID查询违规下架数
    public Integer queryInfractionSaleOffProducts(Long shopId){
        return firstPageMapper.queryInfractionSaleOffProducts(shopId);
    }

    // 根据店铺ID查询仓库中数
    public Integer queryInStockProducts(Long shopId){
        return firstPageMapper.queryInStockProducts(shopId);
    }

    // 根据店铺ID查询待审核数
    public Integer queryWaitForAuditingProducts(Long shopId){
        return firstPageMapper.queryWaitForAuditingProducts(shopId);
    }

    // 根据店铺ID查询审核失败数
    public Integer queryAuditFailureProducts(Long shopId){
        return firstPageMapper.queryAuditFailureProducts(shopId);
    }

    // 根据店铺ID查询库存告警数
    public Integer queryOverSafeStockProducts(Long shopId){
        return firstPageMapper.queryOverSafeStockProducts(shopId);
    }

    // 根据店铺ID查询库存告警数
    public Integer queryProductsBrands(Long shopId){
        return firstPageMapper.queryProductsBrands(shopId);
    }
}
