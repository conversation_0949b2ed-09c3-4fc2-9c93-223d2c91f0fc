package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/3/003
 * @description: 折扣活动响应体
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class DiscountActiveSimpleDto extends ActiveBaseDto {

    /** 主键ID */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 活动名称 */
    private String activeName;

    //    /** 开始时间 */
    //    private Date startTime;
    //
    //    /** 结束时间 */
    //    private Date endTime;

    /** 是否全部商品 */
    private Boolean izAllProduct;

    //    /** 状态 */
    //    private Integer status;
    //
    //    /** 状态名称 */
    //    private String statusDesc;

    /** 商品数量 */
    private Integer productCount;

}
