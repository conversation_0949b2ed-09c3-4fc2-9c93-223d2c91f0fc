package com.sankuai.shangou.seashop.product.dao.core.model;

import lombok.Data;

import java.io.Serializable;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/12/11 14:15
 */
@Data
public class StatisticalProductDto implements Serializable {

    /**
     * 出售中
     */
    private Integer onSaleProducts;

    /**
     * 草稿箱
     */
    private Integer productsInDraft;

    /**
     *待审核
     */
    private Integer waitForAuditingProducts;

    /**
     *审核未通过
     */
    private Integer auditFailureProducts;

    /**
     *违规下架
     */
    private Integer infractionSaleOffProducts;

    /**
     *仓库中
     */
    private Integer inStockProducts;

    /**
     *警戒库存数
     */
    private Integer overSafeStockProducts;

    /**
     *商品评价
     */
    private Integer productsEvaluation;

    /**
     *授权品牌
     */
    private Integer productsBrands;

    /**
     *发布商品数量(总数)
     */
    private Integer productsCount;
}
