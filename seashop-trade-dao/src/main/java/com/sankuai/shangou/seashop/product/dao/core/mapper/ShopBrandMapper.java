package com.sankuai.shangou.seashop.product.dao.core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopBrand;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopBrandExtDto;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopBrandDto;

/**
 * <p>
 * 供应商品牌表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-11-25
 */
public interface ShopBrandMapper extends EnhancedMapper<ShopBrand> {

    /**
     * 查询商家品牌信息(包括品牌信息)
     *
     * @param param 筛选条件
     * @return 商家品牌信息
     */
    List<ShopBrandExtDto> listShopBrandExt(@Param("param") ShopBrandDto param);

}
