package com.sankuai.shangou.seashop.product.dao.core.mapper;

import org.apache.ibatis.annotations.Param;

import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopSaleCountsDto;
import com.sankuai.shangou.seashop.product.dao.core.model.AddProductSaleCountDto;

/**
 * <p>
 * 商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2024-01-22
 */
public interface ProductMapper extends EnhancedMapper<Product> {

    void addSaleCount(@Param("param") AddProductSaleCountDto saleCountParam);

    ShopSaleCountsDto getShopSaleCounts(@Param("shopId") Long shopId);

    Integer countBySpecNameId(@Param("nameId") Long nameId);
}
