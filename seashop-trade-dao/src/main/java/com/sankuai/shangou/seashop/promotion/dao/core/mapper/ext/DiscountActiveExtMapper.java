package com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext;

import com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActive;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.DiscountActiveProduct;
import com.sankuai.shangou.seashop.promotion.dao.core.model.DiscountActiveDetailDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.DiscountActiveParamDto;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
public interface DiscountActiveExtMapper {

    /**
     * 条件查询
     *
     * @param paramDto
     * @return
     */
    List<DiscountActive> selectProductInActive(DiscountActiveParamDto paramDto);

    /**
     * 条件查询
     *
     * @param paramDto
     * @return
     */
    List<DiscountActiveDetailDto> selectProductInActiveDetail(DiscountActiveParamDto paramDto);

    /**
     * 查询所有正在进行或者未开始的折扣活动
     *
     * @param shopId
     * @return
     */
    List<DiscountActive> queryAllActiveDiscountActive(@Param("shopId") Long shopId);

        /**
     * 查询进行中的全品类活动
     *
     * @return
     */
    DiscountActive selectCurrentEnableIzAllProduct();

    /**
     * 查询指定商品的单前有效活动商品信息(非全品活动)
     *
     * @param productIds
     * @return
     */
    List<DiscountActiveProduct> selectCurrentByProductId(@Param("productIds") Collection<Long> productIds);

}
