package com.sankuai.shangou.seashop.promotion.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 组合购商品SKU表
 * </p>
 *
 * <AUTHOR> @since 2023-11-10
 */
public class CollocationSku implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID自增
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 商品SkuId
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 组合商品表ID
     */
    @TableField("collo_product_id")
    private Long colloProductId;

    /**
     * 组合购价格
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 原始价格
     */
    @TableField("sku_pirce")
    private BigDecimal skuPirce;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Long getColloProductId() {
        return colloProductId;
    }

    public void setColloProductId(Long colloProductId) {
        this.colloProductId = colloProductId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getSkuPirce() {
        return skuPirce;
    }

    public void setSkuPirce(BigDecimal skuPirce) {
        this.skuPirce = skuPirce;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CollocationSku{" +
        "id=" + id +
        ", productId=" + productId +
        ", skuId=" + skuId +
        ", colloProductId=" + colloProductId +
        ", price=" + price +
        ", skuPirce=" + skuPirce +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
