package com.sankuai.shangou.seashop.promotion.dao.core.model;

import com.sankuai.shangou.seashop.promotion.common.enums.ActiveStatusEnum;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2024/2/21/021
 * @description: 活动基础模型
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@ToString
public class ActiveBaseDto {

    //开始时间
    private Date startTime;

    //结束时间
    private Date endTime;

    //状态
    private Integer status;

    //状态名称
    private String statusDesc;

    public static void opStatus(ActiveBaseDto activeBaseModel) {
        Date now = new Date();
        Date startTime = activeBaseModel.getStartTime();
        Date endTime = activeBaseModel.getEndTime();
        if (now.before(startTime) && now.before(endTime)) {
            activeBaseModel.setStatus(ActiveStatusEnum.NOT_START.getCode());
            activeBaseModel.setStatusDesc(ActiveStatusEnum.NOT_START.getMsg());
        } else if (now.after(startTime) && now.before(endTime)) {
            activeBaseModel.setStatus(ActiveStatusEnum.START.getCode());
            activeBaseModel.setStatusDesc(ActiveStatusEnum.START.getMsg());
        } else {
            activeBaseModel.setStatus(ActiveStatusEnum.END.getCode());
            activeBaseModel.setStatusDesc(ActiveStatusEnum.END.getMsg());
        }
    }

}
