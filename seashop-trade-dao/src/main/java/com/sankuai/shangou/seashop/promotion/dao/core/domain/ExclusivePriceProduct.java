package com.sankuai.shangou.seashop.promotion.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 专享价格商品关系表
 * </p>
 *
 * <AUTHOR> @since 2023-11-10
 */
public class ExclusivePriceProduct implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 活动ID
     */
    @TableField("active_id")
    private Long activeId;

    /**
     * 商品Id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 规格ID
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 专享价
     */
    @TableField("price")
    private BigDecimal price;

    /**
     * 会员ID
     */
    @TableField("member_id")
    private Long memberId;

    /**
     * 删除标记
     */
    @TableField("del_flag")
    private Boolean delFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", exist = false)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", exist = false)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getActiveId() {
        return activeId;
    }

    public void setActiveId(Long activeId) {
        this.activeId = activeId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public Long getMemberId() {
        return memberId;
    }

    public void setMemberId(Long memberId) {
        this.memberId = memberId;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ExclusivePriceProduct{" +
                "id=" + id +
                ", activeId=" + activeId +
                ", productId=" + productId +
                ", skuId=" + skuId +
                ", price=" + price +
                ", memberId=" + memberId +
                ", delFlag=" + delFlag +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
