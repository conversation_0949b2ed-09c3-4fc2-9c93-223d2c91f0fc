package com.sankuai.shangou.seashop.promotion.dao.core.model;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DiscountActiveParamDto extends BasePageReq {

    private Long id;

    private Long shopId;

    private String activeName;

    private Date startTime;

    private Date endTime;

    private Integer status;

    private Long notEqId;

    /**
     * 用于查询时间段内的活动（主要用于查当前时间范围内的数据）
     */
    private Date betweenTime;

    private List<Long> productIds;

    private List<Long> shopIdList;
}
