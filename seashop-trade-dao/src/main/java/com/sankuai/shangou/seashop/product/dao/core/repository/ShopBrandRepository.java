package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopBrand;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ShopBrandMapper;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopBrandExtDto;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopBrandDto;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:05
 */
@Repository
@Slf4j
public class ShopBrandRepository extends ServiceImpl<ShopBrandMapper, ShopBrand> {

    /**
     * 查询商家品牌
     *
     * @param param 商家品牌查询参数
     * @return 商家品牌列表
     */
    public List<ShopBrandExtDto> listShopBrandExt(ShopBrandDto param) {
        return baseMapper.listShopBrandExt(param);
    }

    /**
     * 根据商家id查询品牌id集合
     *
     * @param shopId 商家id
     * @return 品牌id集合
     */
    public List<Long> listBrandIdsByShopId(Long shopId) {
        return list(new LambdaQueryWrapper<ShopBrand>().eq(ShopBrand::getShopId, shopId).
                select(ShopBrand::getBrandId)).stream().map(ShopBrand::getBrandId).collect(java.util.stream.Collectors.toList());
    }

    /**
     * 是否存在商家品牌
     *
     * @param brandId
     * @param shopId
     * @return
     */
    public Boolean existShopBrand(Long brandId, Long shopId) {
        return count(new LambdaQueryWrapper<ShopBrand>().eq(ShopBrand::getBrandId, brandId).eq(ShopBrand::getShopId, shopId)) > 0;
    }
}
