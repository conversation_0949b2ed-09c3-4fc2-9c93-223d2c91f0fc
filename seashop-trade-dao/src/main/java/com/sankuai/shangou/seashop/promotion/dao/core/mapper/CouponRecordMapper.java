package com.sankuai.shangou.seashop.promotion.dao.core.mapper;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponRecord;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 优惠券记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-11-10
 */
public interface CouponRecordMapper extends EnhancedMapper<CouponRecord> {

    Integer queryAvailableCouponCountByUser(@Param("userId") Long userId);
}
