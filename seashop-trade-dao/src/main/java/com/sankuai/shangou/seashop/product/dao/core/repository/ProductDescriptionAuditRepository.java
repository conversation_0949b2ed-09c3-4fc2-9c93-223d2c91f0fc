package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductDescriptionAudit;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductDescriptionAuditMapper;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:12
 */
@Repository
public class ProductDescriptionAuditRepository extends ServiceImpl<ProductDescriptionAuditMapper, ProductDescriptionAudit> {

    /**
     * 根据商品id列表查询 审核原因map
     *
     * @param productIdList 商品id列表
     * @return 审核原因map
     */
    public Map<Long, String> getAuditReasonMap(List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return MapUtils.EMPTY_MAP;
        }

        List<ProductDescriptionAudit> auditList = listByProductIdList(productIdList);
        return auditList.stream().collect(Collectors.toMap(ProductDescriptionAudit::getProductId,
                ProductDescriptionAudit::getAuditReason, (k1, k2) -> k2));
    }

    /**
     * 根据商品id查询商品描述审核信息
     *
     * @param productId 商品id
     * @return 商品描述审核信息
     */
    public ProductDescriptionAudit getProductDescriptionByProductId(Long productId) {
        return getOne(new LambdaQueryWrapper<ProductDescriptionAudit>().eq(ProductDescriptionAudit::getProductId, productId));
    }

    /**
     * 根据商品id列表查询商品描述审核信息
     *
     * @param productIdList 商品id列表
     * @return 商品描述审核信息列表
     */
    public List<ProductDescriptionAudit> listByProductIdList(List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.emptyList();
        }
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<ProductDescriptionAudit>()
                .in(ProductDescriptionAudit::getProductId, ids)), productIdList);
    }

    @Transactional
    public void updateBatchByProductId(List<ProductDescriptionAudit> updList) {
        if (CollectionUtils.isEmpty(updList)) {
            return;
        }

        List<Long> productIds = updList.stream().map(ProductDescriptionAudit::getProductId).collect(Collectors.toList());
        Map<Long, ProductDescriptionAudit> auditMap =
                listByProductIdList(productIds).stream().collect(Collectors.toMap(ProductDescriptionAudit::getProductId, Function.identity(), (k1, k2) -> k2));
        updList.forEach(audit -> {
            ProductDescriptionAudit dbAudit = auditMap.get(audit.getProductId());
            audit.setId(dbAudit == null ? null : dbAudit.getId());
        });

        updList = updList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updList)) {
            return;
        }
        MybatisUtil.executeBatch(list -> updateBatchById(list), updList);
    }
}
