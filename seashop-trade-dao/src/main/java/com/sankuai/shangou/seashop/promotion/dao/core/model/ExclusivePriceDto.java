package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description: 专享价活动响应体
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class ExclusivePriceDto {

    /** 主键ID */
    private Long id;

    /** 店铺ID */
    private Long shopId;

    /** 活动名称 */
    private String name;

    /** 开始时间 */
    private Date startTime;

    /** 结束时间 */
    private Date endTime;

    /** 状态 */
    private Integer status;

    /** 专享价商品列表 */
    private List<ExclusivePriceProductDto> productList;
}
