package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:专享价商品响应对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class ExclusivePriceProductDto {

    /** 主键ID */
    private Long id;

    /** 活动ID */
    private Long activeId;

    /** 商品ID */
    private Long productId;

    /** SkuID */
    private String skuId;

    /** 价格 */
    private BigDecimal price;

    /** 会员id */
    private Long memberId;

}
