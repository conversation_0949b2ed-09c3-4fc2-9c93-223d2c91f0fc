package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopCategory;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ShopCategoryMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/13 13:40
 */
@Repository
@Slf4j
public class ShopCategoryRepository extends ServiceImpl<ShopCategoryMapper, ShopCategory> {

    /**
     * 查询店铺分类名称map
     *
     * @param shopCategoryIdList 店铺分类id集合
     * @return 店铺分类名称map
     */
    public Map<Long, String> getShopCategoryNameMap(List<Long> shopCategoryIdList) {
        if (CollectionUtils.isEmpty(shopCategoryIdList)) {
            return MapUtils.EMPTY_MAP;
        }

        return baseMapper.selectBatchIds(shopCategoryIdList).stream().collect(Collectors.toMap(ShopCategory::getId, ShopCategory::getName));
    }

    public Map<Long, List<String>> getFullShopCategoryNameMap(List<Long> shopCategoryIdList) {
        if (CollectionUtils.isEmpty(shopCategoryIdList)) {
            return MapUtils.EMPTY_MAP;
        }

        List<ShopCategory> shopCategories = listShopCategory(shopCategoryIdList);

        List<Long> parentIds = shopCategories.stream()
                .filter(item -> item.getParentCategoryId() > 0).map(ShopCategory::getParentCategoryId).collect(Collectors.toList());
        Map<Long, ShopCategory> parentMap = listShopCategory(parentIds).stream().collect(Collectors.toMap(ShopCategory::getId, Function.identity(), (k1, k2) -> k1));

        return shopCategories.stream().collect(Collectors.toMap(ShopCategory::getId, item -> {
            List<String> names = new ArrayList<>();
            names.add(item.getName());
            ShopCategory parent = parentMap.get(item.getParentCategoryId());
            if (parent != null) {
                names.add(0, parent.getName());
            }
            return names;
        }, (k1, k2) -> k1));
    }

    /**
     * 根据名称的集合和shopId查询店铺分类
     */
    public List<ShopCategory> listShopCategory(List<String> shopCategoryNameList, Long shopId) {
        if (CollectionUtils.isEmpty(shopCategoryNameList)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(names -> list(
                        new LambdaQueryWrapper<ShopCategory>()
                                .in(ShopCategory::getName, names)
                                .eq(ShopCategory::getShopId, shopId)
                                .eq(ShopCategory::getWhetherDelete, Boolean.FALSE)),
                shopCategoryNameList);
    }

    /**
     * 根据名称和shopId查询店铺分类
     */
    public ShopCategory getShopCategory(String shopCategoryName, Long shopId) {
        return getOne(new LambdaQueryWrapper<ShopCategory>()
                .eq(ShopCategory::getName, shopCategoryName)
                .eq(ShopCategory::getShopId, shopId)
                .eq(ShopCategory::getWhetherDelete, Boolean.FALSE));
    }

    public void logicRemoveByIds(List<Long> shopCategoryIds) {
        if (CollectionUtils.isEmpty(shopCategoryIds)) {
            return;
        }

        ShopCategory updShopCategory = new ShopCategory();
        updShopCategory.setWhetherDelete(Boolean.TRUE);
        MybatisUtil.executeBatch(ids -> baseMapper.update(updShopCategory,
                new LambdaQueryWrapper<ShopCategory>().in(ShopCategory::getId, ids)), shopCategoryIds);
    }

    public Long getMaxDisplaySequence(Long parentId, Long shopId) {
        ShopCategory shopCategory = getOne(new LambdaQueryWrapper<ShopCategory>()
                .eq(ShopCategory::getParentCategoryId, parentId)
                .eq(ShopCategory::getWhetherDelete, Boolean.FALSE)
                .eq(ShopCategory::getShopId, shopId)
                .orderByDesc(ShopCategory::getDisplaySequence)
                .last("limit 1"));
        return Optional.ofNullable(shopCategory).map(ShopCategory::getDisplaySequence).orElse(null);
    }

    public List<ShopCategory> listShopCategory(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.EMPTY_LIST;
        }
        ids = ids.stream().distinct().collect(Collectors.toList());
        return MybatisUtil.queryBatch(curIds -> list(new LambdaQueryWrapper<ShopCategory>()
                .in(ShopCategory::getId, curIds).eq(ShopCategory::getWhetherDelete, Boolean.FALSE)), ids);
    }

    public void updateShowStatus(List<Long> shopCategoryIds, Boolean showStatus) {
        if (CollectionUtils.isEmpty(shopCategoryIds) || showStatus == null) {
            return;
        }

        ShopCategory updShopCategory = new ShopCategory();
        updShopCategory.setWhetherShow(showStatus);
        MybatisUtil.executeBatch(ids -> baseMapper.update(updShopCategory,
                new LambdaQueryWrapper<ShopCategory>().in(ShopCategory::getId, ids)), shopCategoryIds);
    }

    public List<Long> getChildCategoryIds(Long categoryId) {
        return listObjs(new LambdaQueryWrapper<ShopCategory>()
                        .eq(ShopCategory::getParentCategoryId, categoryId)
                        .eq(ShopCategory::getWhetherDelete, Boolean.FALSE)
                        .select(ShopCategory::getId),
                id -> Long.valueOf(String.valueOf(id)));
    }
}
