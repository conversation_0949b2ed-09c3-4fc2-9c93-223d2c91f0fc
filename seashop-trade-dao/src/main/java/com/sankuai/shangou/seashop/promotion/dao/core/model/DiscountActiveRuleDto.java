package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description: 折扣规则响应对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class DiscountActiveRuleDto {

    /** 主键ID */
    private Long id;

    /** 活动ID */
    private Long activeId;

    /** 满减的条件 */
    private BigDecimal quota;

    /** 满减的金额 */
    private BigDecimal discount;
}
