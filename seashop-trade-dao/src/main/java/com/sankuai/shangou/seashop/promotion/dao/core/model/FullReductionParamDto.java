package com.sankuai.shangou.seashop.promotion.dao.core.model;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/13/013
 * @description:满减活动查询对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class FullReductionParamDto extends BasePageReq {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 店铺编号
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 满减活动名称
     */
    private String activeName;

    /**
     * 店铺ID列表
     */
    private List<Long> shopIdList;

    /**
     * 状态 0-未开始 1-进行中 2-已结束
     */
    private Integer status;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 不等于的主键Id
     */
    private Long notEqId;
    /**
     * 用于查询时间段内的活动（主要用于查当前时间范围内的数据）
     */
    private Date betweenTime;
}
