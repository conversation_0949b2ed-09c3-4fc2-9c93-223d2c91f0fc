package com.sankuai.shangou.seashop.trade.dao.core.mapper;

/**
 * 自定义的购物车数据表操作mapper，区别于自动生成
 * <AUTHOR>
 */
public interface CustomShoppingCartMapper {

    /**
     * 增加购物车sku数量
     * <AUTHOR>
     * @param id 购物车主键
	 * @param quantity 待增加的数量
     * int
     */
    int increaseSkuQuantity(Long id, Long quantity);

    /**
     * 增加购物车sku数量
     * <AUTHOR>
     * @param id 购物车主键
     * @param quantity 待增加的数量
     * int
     */
    int increaseSkuQuantityAndOverSelect(Long id, Long quantity, Boolean selected);

    /**
     * 修改购物车sku数量
     * <AUTHOR>
     * @param id 购物车主键
     * @param quantity 待增加的数量
     * int
     */
    int updateSkuQuantity(Long id, Long quantity);

    /**
     * 修改购物车是否选中
     * <AUTHOR>
     * @param id 购物车主键
     * @param selected 修改是否选中
     */
    int updateWhetherSelect(Long id, boolean selected);
}