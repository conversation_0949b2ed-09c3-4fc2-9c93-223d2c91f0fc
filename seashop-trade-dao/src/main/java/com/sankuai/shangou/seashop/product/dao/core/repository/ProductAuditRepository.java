package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductAuditMapper;

/**
 * <AUTHOR>
 * @date 2023/11/17 11:12
 */
@Repository
public class ProductAuditRepository extends ServiceImpl<ProductAuditMapper, ProductAudit> {

    /**
     * 根据商品id集合查询商品审核信息
     *
     * @param productIds 商品id集合
     * @return 商品审核信息
     */
    public List<ProductAudit> listByProductIds(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<ProductAudit>().in(ProductAudit::getProductId, ids)), productIds);
    }

    /**
     * 根据商品id查询商品审核信息
     *
     * @param productId 商品id
     * @return 商品审核信息
     */
    public ProductAudit getByProductId(Long productId) {
        return getOne(new LambdaQueryWrapper<ProductAudit>().eq(ProductAudit::getProductId, productId));
    }

    /**
     * 根据商品id更新商品审核信息
     *
     * @param productAudit 商品审核信息
     */
    public void updateByProductId(ProductAudit productAudit) {
        update(productAudit, new LambdaQueryWrapper<ProductAudit>().eq(ProductAudit::getProductId, productAudit.getProductId()));
    }

    public int countByCategoryIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return 0;
        }
        return Math.toIntExact(MybatisUtil.countBatch(ids -> count(new LambdaQueryWrapper<ProductAudit>()
            .in(ProductAudit::getCategoryId, ids).eq(ProductAudit::getWhetherDelete, Boolean.FALSE)), categoryIds));
    }
}
