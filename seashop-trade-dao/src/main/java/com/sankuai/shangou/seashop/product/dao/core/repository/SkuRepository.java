package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.Sku;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SkuMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:05
 */
@Repository
@Slf4j
public class SkuRepository extends ServiceImpl<SkuMapper, Sku> {

    /**
     * 根据skuId的集合查询sku列表
     *
     * @param skuIdList skuId的集合
     * @return sku列表
     */
    public List<Sku> listSkuBySkuIds(List<String> skuIdList) {
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<Sku>().in(Sku::getSkuId, skuIdList)), skuIdList);
    }

    /**
     * 根据id的集合查询sku列表
     *
     * @param autoIds id的集合
     * @return sku列表
     */
    public List<Sku> listSkuByIds(List<Long> autoIds) {
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<Sku>().in(Sku::getId, ids)), autoIds);
    }

    /**
     * 根据skuAutoId的集合查询sku map
     *
     * @param autoIds id的集合
     * @return sku列表
     */
    public Map<Long, Sku> getSkuMap(List<Long> autoIds) {
        return listSkuByIds(autoIds).stream().collect(Collectors.toMap(Sku::getId,
                Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 根据商品id的集合查询sku列表
     *
     * @param productIds 商品id的集合
     * @return sku列表
     */
    public List<Sku> listByProductIds(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<Sku>().in(Sku::getProductId, productIds)), productIds);
    }

    /**
     * 根据skuCode和商家id查询sku列表
     *
     * @param skuCodes skuCode的集合
     * @param shopId   商家id
     * @return sku列表
     */
    public List<Sku> listSkuBySkuCodesAndShopId(List<String> skuCodes, Long shopId) {
        if (shopId == null || shopId <= 0) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<Sku>()
                .in(Sku::getSkuCode, skuCodes).eq(Sku::getShopId, shopId).eq(Sku::getWhetherDelete, Boolean.FALSE)), skuCodes);
    }

    public void removeBySkuIds(List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return;
        }

        MybatisUtil.executeBatch(ids -> remove(new LambdaQueryWrapper<Sku>().in(Sku::getSkuId, ids)), skuIds);
    }

    public void logicRemoveByProductIds(List<Long> productIds, Long deleteVersion) {
        if (CollectionUtils.isEmpty(productIds)) {
            return;
        }

        Sku updSku = new Sku();
        updSku.setWhetherDelete(Boolean.TRUE);
        updSku.setDeleteVersion(deleteVersion);
        MybatisUtil.executeBatch(ids -> update(updSku, new LambdaQueryWrapper<Sku>().in(Sku::getProductId, ids)), productIds);
    }

    public BigDecimal getMinSalePrice(Long productId) {
        Sku sku = getOne(new LambdaQueryWrapper<Sku>()
            .eq(Sku::getProductId, productId).orderByAsc(Sku::getSalePrice)
            .last("limit 1"));
        return sku == null ? null : sku.getSalePrice();
    }

    public BigDecimal getMaxSalePrice(Long productId) {
        Sku sku = getOne(new LambdaQueryWrapper<Sku>()
            .eq(Sku::getProductId, productId)
            .orderByDesc(Sku::getSalePrice)
            .last("limit 1"));
        return sku == null ? null : sku.getSalePrice();
    }
}
