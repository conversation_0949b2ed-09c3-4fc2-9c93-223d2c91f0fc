package com.sankuai.shangou.seashop.product.dao.core.repository;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.product.dao.core.domain.SpecName;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SpecNameMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/07/18 15:30
 */
@Repository
@Slf4j
public class SpecNameRepository extends ServiceImpl<SpecNameMapper, SpecName> {
    public SpecName getByName(Long shopId, String specName) {
        return getOne(new LambdaQueryWrapper<SpecName>()
            .eq(SpecName::getShopId, shopId)
            .eq(SpecName::getSpecName, specName)
            .last("limit 1"));
    }
}
