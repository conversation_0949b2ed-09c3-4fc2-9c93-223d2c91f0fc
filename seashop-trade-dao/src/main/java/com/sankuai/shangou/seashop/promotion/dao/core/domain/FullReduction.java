package com.sankuai.shangou.seashop.promotion.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 满减活动表
 * </p>
 *
 * <AUTHOR> @since 2023-11-10
 */
public class FullReduction implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺编号
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 活动名称
     */
    @TableField("active_name")
    private String activeName;

    /**
     * 单笔订单满减金额门槛
     */
    @TableField("money_off_condition")
    private BigDecimal moneyOffCondition;

    /**
     * 单笔订单满减金额
     */
    @TableField("money_off_fee")
    private BigDecimal moneyOffFee;

    /**
     * 是否叠加优惠
     */
    @TableField("money_off_over_lay")
    private Boolean moneyOffOverLay;

    /**
     * 开始时间
     */
    @TableField("start_time")
    private Date startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 活动状态：0正常;
     */
    @TableField("active_status")
    private Integer activeStatus;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",exist = false)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",exist = false)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getActiveName() {
        return activeName;
    }

    public void setActiveName(String activeName) {
        this.activeName = activeName;
    }

    public BigDecimal getMoneyOffCondition() {
        return moneyOffCondition;
    }

    public void setMoneyOffCondition(BigDecimal moneyOffCondition) {
        this.moneyOffCondition = moneyOffCondition;
    }

    public BigDecimal getMoneyOffFee() {
        return moneyOffFee;
    }

    public void setMoneyOffFee(BigDecimal moneyOffFee) {
        this.moneyOffFee = moneyOffFee;
    }

    public Boolean getMoneyOffOverLay() {
        return moneyOffOverLay;
    }

    public void setMoneyOffOverLay(Boolean moneyOffOverLay) {
        this.moneyOffOverLay = moneyOffOverLay;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getActiveStatus() {
        return activeStatus;
    }

    public void setActiveStatus(Integer activeStatus) {
        this.activeStatus = activeStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "FullReduction{" +
                "id=" + id +
                ", shopId=" + shopId +
                ", shopName=" + shopName +
                ", activeName=" + activeName +
                ", moneyOffCondition=" + moneyOffCondition +
                ", moneyOffFee=" + moneyOffFee +
                ", moneyOffOverLay=" + moneyOffOverLay +
                ", startTime=" + startTime +
                ", endTime=" + endTime +
                ", activeStatus=" + activeStatus +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
