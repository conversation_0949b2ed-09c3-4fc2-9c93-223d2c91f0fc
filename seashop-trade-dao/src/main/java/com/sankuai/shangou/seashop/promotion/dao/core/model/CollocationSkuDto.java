package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 组合购商品SKU表
 * </p>
 *
 * <AUTHOR> @since 2023-11-10
 */
@Data
public class CollocationSkuDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID自增
     */
    private Long id;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品SkuId
     */
    private String skuId;

    /**
     * 组合商品表ID
     */
    private Long colloProductId;

    /**
     * 组合商品表ID
     */
    private List<Long> colloProductIdList;

}
