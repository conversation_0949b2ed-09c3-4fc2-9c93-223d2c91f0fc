package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:专享价参数对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class ExclusivePriceParamDto {

    private Long id;

    private Long shopId;

    private String name;

    private Date startTime;

    private Date endTime;

    private Integer status;

    private Long notEqId;

    private List<Long> productIds;

    private List<Long> shopIdList;

    private Long memberId;

    /**
     * 用于查询时间段内的活动（主要用于查当前时间范围内的数据）
     */
    private Date betweenTime;

}
