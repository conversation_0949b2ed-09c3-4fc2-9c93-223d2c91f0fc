package com.sankuai.shangou.seashop.promotion.dao.core.model;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/8/008
 * @description: 优惠券查询请求对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class CouponParamDto extends BasePageReq {

    /** 店铺ID */
    private Long shopId;

    /** 店铺名称 */
    private String shopName;

    /** 优惠券ID */
    private Long couponId;

    /** 优惠券名称 */
    private String couponName;

    /** 店铺ID列表 */
    private List<Long> shopIdList;

    /** 状态 0-未开始 1-进行中 2-已结束 */
    private Integer status;

    /** 可以领用的：true-可以领用的 false-不可以领用的（true时包含状态0、1的数据） */
    private Boolean claimable;

    /** 领取方式 0 店铺首页 1 积分兑换 2 主动发放 */
    private List<Integer> receiveTypeList;

    private Boolean hasStock;
}
