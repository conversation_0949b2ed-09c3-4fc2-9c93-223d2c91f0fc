package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 类目需缴纳保证金信息表
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
public class CategoryCashDeposit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键Id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类名id
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 需要缴纳保证金
     */
    @TableField("need_pay_cash_deposit")
    private BigDecimal needPayCashDeposit;

    /**
     * 允许七天无理由退货
     */
    @TableField("enable_no_reason_return")
    private Boolean enableNoReasonReturn;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public BigDecimal getNeedPayCashDeposit() {
        return needPayCashDeposit;
    }

    public void setNeedPayCashDeposit(BigDecimal needPayCashDeposit) {
        this.needPayCashDeposit = needPayCashDeposit;
    }

    public Boolean getEnableNoReasonReturn() {
        return enableNoReasonReturn;
    }

    public void setEnableNoReasonReturn(Boolean enableNoReasonReturn) {
        this.enableNoReasonReturn = enableNoReasonReturn;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CategoryCashDeposit{" +
                "id=" + id +
                ", categoryId=" + categoryId +
                ", needPayCashDeposit=" + needPayCashDeposit +
                ", enableNoReasonReturn=" + enableNoReasonReturn +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
