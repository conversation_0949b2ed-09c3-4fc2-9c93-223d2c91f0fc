package com.sankuai.shangou.seashop.promotion.dao.core.mapper;

import com.sankuai.shangou.seashop.promotion.dao.core.domain.CouponRecord;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordConsumeListDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordParamDto;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponRecordSimpleDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/10/010
 * @description:
 */
public interface CouponRecordExtMapper {

    /**
     * 分页查询优惠券领取记录
     *
     * @param queryBo
     * @return
     */
    List<CouponRecordSimpleDto> pageList(@Param("param") CouponRecordParamDto queryBo);

    /**
     * 根据优惠券ID和用户ID查询优惠券领取记录
     *
     * @param userId
     * @param list
     * @return
     */
    List<CouponRecord> queryListByIdAndUserId(@Param("userId") Long userId, @Param("list") List<CouponRecordConsumeListDto> list);
}
