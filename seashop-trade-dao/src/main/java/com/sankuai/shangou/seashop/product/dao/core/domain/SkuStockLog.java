package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 库存日志表
 * </p>
 *
 * <AUTHOR> @since 2023-12-18
 */
public class SkuStockLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规格ID
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * Sku表里的自增id
     */
    @TableField("sku_auto_id")
    private Long skuAutoId;

    /**
     * 修改前库存
     */
    @TableField("before_stock")
    private Long beforeStock;

    /**
     * 当前库存
     */
    @TableField("stock")
    private Long stock;

    /**
     * 修改类型 1-编辑商品 2-批量调整库存 3-批量导入库存 4-生成订单 5-生成订单失败 6-牵牛花API生成订单 7-售后退回 8-订单关闭 9-聚水潭同步 10-牵牛花API关闭订单 11-旺店通同步 12-牵牛花API同步库存 13-网店管家/吉客云同步库存
     */
    @TableField("update_type")
    private Integer updateType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人Id
     */
    @TableField("create_user")
    private Long createUser;

    /**
     * 更新人Id
     */
    @TableField("update_user")
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Long getSkuAutoId() {
        return skuAutoId;
    }

    public void setSkuAutoId(Long skuAutoId) {
        this.skuAutoId = skuAutoId;
    }

    public Long getBeforeStock() {
        return beforeStock;
    }

    public void setBeforeStock(Long beforeStock) {
        this.beforeStock = beforeStock;
    }

    public Long getStock() {
        return stock;
    }

    public void setStock(Long stock) {
        this.stock = stock;
    }

    public Integer getUpdateType() {
        return updateType;
    }

    public void setUpdateType(Integer updateType) {
        this.updateType = updateType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "SkuStockLog{" +
        "id=" + id +
        ", skuId=" + skuId +
        ", skuAutoId=" + skuAutoId +
        ", beforeStock=" + beforeStock +
        ", stock=" + stock +
        ", updateType=" + updateType +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", createUser=" + createUser +
        ", updateUser=" + updateUser +
        "}";
    }
}
