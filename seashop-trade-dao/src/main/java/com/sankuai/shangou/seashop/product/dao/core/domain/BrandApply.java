package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 品牌申请表
 * </p>
 *
 * <AUTHOR> @since 2023-11-25
 */
public class BrandApply implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 品牌ID
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 品牌Logo
     */
    @TableField("logo")
    private String logo;

    /**
     * 描述
     */
    @TableField("description")
    private String description;

    /**
     * 品牌授权证书
     */
    @TableField("auth_certificate")
    private String authCertificate;

    /**
     * 申请类型 1-平台已有品牌 2-供应商新增品牌
     */
    @TableField("apply_mode")
    private Integer applyMode;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 审核状态 0-未审核 1-审核通过 2-审核拒绝
     */
    @TableField("audit_status")
    private Integer auditStatus;

    /**
     * 申请时间
     */
    @TableField("apply_time")
    private Date applyTime;

    /**
     * 审核时间
     */
    @TableField("audit_time")
    private Date auditTime;

    /**
     * 平台备注
     */
    @TableField("plat_remark")
    private String platRemark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人Id
     */
    @TableField("create_user")
    private Long createUser;

    /**
     * 更新人Id
     */
    @TableField("update_user")
    private Long updateUser;

    /**
     * 是否已删除
     */
    @TableField("whether_delete")
    private Boolean whetherDelete;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getAuthCertificate() {
        return authCertificate;
    }

    public void setAuthCertificate(String authCertificate) {
        this.authCertificate = authCertificate;
    }

    public Integer getApplyMode() {
        return applyMode;
    }

    public void setApplyMode(Integer applyMode) {
        this.applyMode = applyMode;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getAuditStatus() {
        return auditStatus;
    }

    public void setAuditStatus(Integer auditStatus) {
        this.auditStatus = auditStatus;
    }

    public Date getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    public Date getAuditTime() {
        return auditTime;
    }

    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    public String getPlatRemark() {
        return platRemark;
    }

    public void setPlatRemark(String platRemark) {
        this.platRemark = platRemark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    public Boolean getWhetherDelete() {
        return whetherDelete;
    }

    public void setWhetherDelete(Boolean whetherDelete) {
        this.whetherDelete = whetherDelete;
    }

    @Override
    public String toString() {
        return "BrandApply{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", brandId=" + brandId +
        ", brandName=" + brandName +
        ", logo=" + logo +
        ", description=" + description +
        ", authCertificate=" + authCertificate +
        ", applyMode=" + applyMode +
        ", remark=" + remark +
        ", auditStatus=" + auditStatus +
        ", applyTime=" + applyTime +
        ", auditTime=" + auditTime +
        ", platRemark=" + platRemark +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", createUser=" + createUser +
        ", updateUser=" + updateUser +
        ", whetherDelete=" + whetherDelete +
        "}";
    }
}
