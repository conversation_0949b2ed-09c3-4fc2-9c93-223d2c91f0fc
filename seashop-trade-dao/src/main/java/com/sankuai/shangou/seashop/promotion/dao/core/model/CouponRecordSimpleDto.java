package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/10/010
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class CouponRecordSimpleDto {

    /** 主键ID */
    private Long id;

    /** 优惠券活动ID */
    private Long couponId;

    /** 优惠券名称 */
    private String couponName;

    /** 优惠券优惠码 */
    private String couponSn;

    /** 优惠券领用时间 */
    private Date couponTime;

    /** 订单金额（满足多少钱才能使用） */
    private Long orderAmount;

    /** 面值(价格) */
    private Long price;

    /** 用户名称 */
    private String userName;

    /** 用户ID */
    private Long userId;

    /** 优惠券使用时间 */
    private Date usedTime;

    /** 订单ID */
    private String orderId;

    /** 店铺ID */
    private Long shopId;

    /** 店铺名称 */
    private String shopName;

    /** 优惠券状态 0-未使用 1-已使用 2-已过期 */
    private Integer couponStatus;

    /** 优惠券状态名称 */
    private String couponStatusDesc;

    /** 优惠券创建时间 */
    private Date createTime;

    /** 优惠券开始时间 */
    private Date startTime;

    /** 优惠券结束时间 */
    private Date endTime;

    /** 备注 */
    private String remark;

    /** 使用范围：0=全场通用，1=部分商品可用 */
    private Integer useArea;

    /** 商品ID列表(部分商品可用时使用) */
    private List<Long> productIdList;
}
