package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductLadderPrice;
import com.sankuai.shangou.seashop.product.dao.core.mapper.ProductLadderPriceMapper;

/**
 * <AUTHOR>
 * @date 2023/11/15 17:46
 */
@Repository
public class ProductLadderPriceRepository extends ServiceImpl<ProductLadderPriceMapper, ProductLadderPrice> {

    /**
     * 根据商品id查询阶梯价格
     *
     * @param productIdList 商品id集合
     * @return 阶梯价格列表
     */
    public List<ProductLadderPrice> listByProductIds(List<Long> productIdList) {
        if (CollectionUtils.isEmpty(productIdList)) {
            return Collections.emptyList();
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<ProductLadderPrice>()
                .in(ProductLadderPrice::getProductId, ids)), productIdList);
    }

    public BigDecimal getMinLadderPrice(Long productId) {
        ProductLadderPrice ladderPrice = getOne(new LambdaQueryWrapper<ProductLadderPrice>()
                .eq(ProductLadderPrice::getProductId, productId)
                .orderByAsc(ProductLadderPrice::getPrice)
                .last("limit 1"));

        return ladderPrice == null ? null : ladderPrice.getPrice();
    }

    public BigDecimal getMaxLadderPrice(Long productId) {
        ProductLadderPrice ladderPrice = getOne(new LambdaQueryWrapper<ProductLadderPrice>()
                .eq(ProductLadderPrice::getProductId, productId)
                .orderByDesc(ProductLadderPrice::getPrice)
                .last("limit 1"));

        return ladderPrice == null ? null : ladderPrice.getPrice();
    }
}
