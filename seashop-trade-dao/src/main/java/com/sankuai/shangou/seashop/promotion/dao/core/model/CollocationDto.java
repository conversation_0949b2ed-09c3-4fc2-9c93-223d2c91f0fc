package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 组合购信息表
 * </p>
 *
 * <AUTHOR> @since 2023-11-10
 */
@Data
public class CollocationDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID自增
     */
    private Long id;

    /**
     * ID集合
     */
    private List<Long> colloIdList;

    /**
     * 组合购标题
     */
    private String title;

    /**
     * 开始日期
     */
    private Date startTime;

    /**
     * 结束日期
     */
    private Date endTime;

    /**
     * 组合描述
     */
    private String shortDesc;

    /**
     * 组合购店铺ID
     */
    private Long shopId;

}
