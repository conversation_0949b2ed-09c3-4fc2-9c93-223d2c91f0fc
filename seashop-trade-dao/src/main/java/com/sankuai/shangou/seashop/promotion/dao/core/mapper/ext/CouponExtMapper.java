package com.sankuai.shangou.seashop.promotion.dao.core.mapper.ext;


import com.sankuai.shangou.seashop.promotion.dao.core.domain.Coupon;
import com.sankuai.shangou.seashop.promotion.dao.core.model.CouponExtDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/9/009
 * @description:
 */
public interface CouponExtMapper {

    /**
     * 库存扣减
     *
     * @param couponId
     * @return
     */
    int reduceStock(@Param("couponId") Long couponId);

    /**
     * 库存回滚
     *
     * @param couponId
     * @param receiveNum
     * @return
     */
    int receiveCoupon(@Param("couponId") Long couponId, @Param("receiveNum") Integer receiveNum);

    /**
     * 根据商品id查询优惠券
     *
     * @param productId
     * @param shopId
     * @return
     */
    List<Coupon> selectByProductId(@Param("productId") Long productId, @Param("shopId") Long shopId);

    /**
     * 查询可用优惠券
     *
     * @param shopIds
     * @param productIds
     * @return
     */
    List<CouponExtDto> selectAvailableList(@Param("shopIds") List<Long> shopIds, @Param("productIds") List<Long> productIds);

    /**
     * 查询所有正在进行或者未开始的优惠活动
     *
     * @param shopId
     * @return
     */
    List<Coupon> queryAllActiveCoupon(@Param("shopId") Long shopId);
}
