package com.sankuai.shangou.seashop.product.dao.core.repository;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.product.dao.core.domain.SkuAudit;
import com.sankuai.shangou.seashop.product.dao.core.mapper.SkuAuditMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/02 14:05
 */
@Repository
@Slf4j
public class SkuAuditRepository extends ServiceImpl<SkuAuditMapper, SkuAudit> {

    /**
     * 根据skuId查询sku审核信息
     *
     * @param skuIds skuId集合
     * @return sku审核信息集合
     */
    public List<SkuAudit> listSkuAuditBySkuId(List<String> skuIds) {
        if (CollectionUtils.isEmpty(skuIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<SkuAudit>().in(SkuAudit::getSkuId, ids)), skuIds);
    }

    /**
     * 根据skuId查询sku审核信息
     *
     * @param skuIds skuId集合
     * @return sku审核信息集合
     */
    public Map<String, SkuAudit> getSkuAuditMap(List<String> skuIds) {
        return listSkuAuditBySkuId(skuIds).stream().collect(Collectors.toMap(SkuAudit::getSkuId, Function.identity(), (k1, k2) -> k2));
    }

    /**
     * 根据skuId 更新审核信息
     *
     * @param skuAudit 审核信息
     */
    public void updateBySkuId(SkuAudit skuAudit) {
        update(skuAudit, new LambdaQueryWrapper<SkuAudit>().eq(SkuAudit::getSkuId, skuAudit.getSkuId()));
    }

    /**
     * 根据商品id查询sku审核信息
     *
     * @param productIds 商品id集合
     * @return sku审核信息集合
     */
    public List<SkuAudit> listByProductIds(List<Long> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }
        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<SkuAudit>().in(SkuAudit::getProductId, ids)), productIds);
    }

    public BigDecimal getMaxSalePrice(Long productId) {
        SkuAudit skuAudit = getOne(new LambdaQueryWrapper<SkuAudit>()
            .eq(SkuAudit::getProductId, productId)
            .orderByDesc(SkuAudit::getSalePrice)
            .last("limit 1"));

        return skuAudit == null ? null : skuAudit.getSalePrice();
    }
}
