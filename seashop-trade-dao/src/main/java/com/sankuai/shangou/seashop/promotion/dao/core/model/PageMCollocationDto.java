package com.sankuai.shangou.seashop.promotion.dao.core.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/18 16:27
 */
@Data
public class PageMCollocationDto {

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺ID集合（如果店铺名称有值就会转换成店铺ID集合）
     */
    private List<Long> shopIdList;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 活动ID
     */
    private Long activityId;

    /**
     * 组合购活动名称
     */
    private String title;

    /**
     * 主商品ID
     */
    private Long mainproductId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品ID集合
     */
    private List<Long> productIdList;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
