package com.sankuai.shangou.seashop.product.dao.core.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 平台类目表
 * </p>
 *
 * <AUTHOR> @since 2024-01-11
 */
public class Category implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 类目名称
     */
    @TableField("name")
    private String name;

    /**
     * 类目图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 排序
     */
    @TableField("display_sequence")
    private Long displaySequence;

    /**
     * 上级类目id
     */
    @TableField("parent_category_id")
    private Long parentCategoryId;

    /**
     * 类目的深度
     */
    @TableField("depth")
    private Integer depth;

    /**
     * 类目的路径（以|分离）
     */
    @TableField("path")
    private String path;

    /**
     * 是否有子类目
     */
    @TableField("has_children")
    private Boolean hasChildren;

    /**
     * 分佣比例
     */
    @TableField("commission_rate")
    private BigDecimal commissionRate;

    /**
     * 是否已删除
     */
    @TableField("whether_delete")
    private Boolean whetherDelete;

    /**
     * 是否显示
     */
    @TableField("whether_show")
    private Boolean whetherShow;

    /**
     * 自定义表单Id
     */
    @TableField("custom_form_id")
    private Long customFormId;

    /**
     * 是否默认 0-否 1-是
     */
    @TableField("default_status")
    private Boolean defaultStatus;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人Id
     */
    @TableField("create_user")
    private Long createUser;

    /**
     * 更新人Id
     */
    @TableField("update_user")
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Long getDisplaySequence() {
        return displaySequence;
    }

    public void setDisplaySequence(Long displaySequence) {
        this.displaySequence = displaySequence;
    }

    public Long getParentCategoryId() {
        return parentCategoryId;
    }

    public void setParentCategoryId(Long parentCategoryId) {
        this.parentCategoryId = parentCategoryId;
    }

    public Integer getDepth() {
        return depth;
    }

    public void setDepth(Integer depth) {
        this.depth = depth;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public Boolean getHasChildren() {
        return hasChildren;
    }

    public void setHasChildren(Boolean hasChildren) {
        this.hasChildren = hasChildren;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }


    public Boolean getWhetherDelete() {
        return whetherDelete;
    }

    public void setWhetherDelete(Boolean whetherDelete) {
        this.whetherDelete = whetherDelete;
    }

    public Boolean getWhetherShow() {
        return whetherShow;
    }

    public void setWhetherShow(Boolean whetherShow) {
        this.whetherShow = whetherShow;
    }

    public Long getCustomFormId() {
        return customFormId;
    }

    public void setCustomFormId(Long customFormId) {
        this.customFormId = customFormId;
    }

    public Boolean getDefaultStatus() {
        return defaultStatus;
    }

    public void setDefaultStatus(Boolean defaultStatus) {
        this.defaultStatus = defaultStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "Category{" +
        "id=" + id +
        ", name=" + name +
        ", icon=" + icon +
        ", displaySequence=" + displaySequence +
        ", parentCategoryId=" + parentCategoryId +
        ", depth=" + depth +
        ", path=" + path +
        ", hasChildren=" + hasChildren +
        ", commissionRate=" + commissionRate +
        ", whetherDelete=" + whetherDelete +
        ", whetherShow=" + whetherShow +
        ", customFormId=" + customFormId +
        ", defaultStatus=" + defaultStatus +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", createUser=" + createUser +
        ", updateUser=" + updateUser +
        "}";
    }
}
