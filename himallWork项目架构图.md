# himallWork项目架构图

## 1. 整体架构图

```mermaid
graph TB
    subgraph "前端层"
        A1[商家管理后台]
        A2[移动端APP]
        A3[微信小程序]
        A4[PC商城]
    end
    
    subgraph "网关层"
        B[himall-gw 统一网关]
    end
    
    subgraph "业务服务层"
        C1[himall-base<br/>基础服务]
        C2[himall-trade<br/>交易服务]
        C3[himall-order<br/>订单服务]
        C4[himall-report<br/>报表服务]
    end
    
    subgraph "数据层"
        D1[(MySQL<br/>主数据库)]
        D2[(Redis<br/>缓存)]
        D3[(Elasticsearch<br/>搜索引擎)]
        D4[对象存储]
    end
    
    subgraph "基础设施层"
        E1[RocketMQ<br/>消息队列]
        E2[XXL-JOB<br/>任务调度]
        E3[监控告警]
        E4[日志收集]
    end
    
    A1 --> B
    A2 --> B
    A3 --> B
    A4 --> B
    
    B --> C1
    B --> C2
    B --> C3
    B --> C4
    
    C1 --> D1
    C1 --> D2
    C2 --> D1
    C2 --> D2
    C2 --> D3
    C3 --> D1
    C3 --> D2
    C4 --> D1
    
    C1 --> E1
    C2 --> E1
    C3 --> E1
    C4 --> E2
```

## 2. 服务依赖关系图

```mermaid
graph LR
    subgraph "himall-gw 网关服务"
        GW[API网关]
    end
    
    subgraph "himall-base 基础服务"
        B1[用户管理]
        B2[店铺管理]
        B3[权限认证]
        B4[公共组件]
    end
    
    subgraph "himall-trade 交易服务"
        T1[商品管理]
        T2[购物车]
        T3[营销促销]
        T4[预订单]
    end
    
    subgraph "himall-order 订单服务"
        O1[订单管理]
        O2[支付服务]
        O3[售后服务]
        O4[财务结算]
    end
    
    subgraph "himall-report 报表服务"
        R1[数据收集]
        R2[统计分析]
        R3[报表展示]
    end
    
    GW --> B1
    GW --> B2
    GW --> B3
    GW --> T1
    GW --> T2
    GW --> T3
    GW --> O1
    GW --> O2
    GW --> R3
    
    T1 --> B1
    T1 --> B2
    T2 --> B1
    T3 --> B1
    T4 --> B1
    T4 --> B2
    
    O1 --> B1
    O1 --> B2
    O1 --> T1
    O1 --> T4
    O2 --> B1
    O3 --> B1
    O4 --> B1
    
    R1 --> B1
    R1 --> T1
    R1 --> O1
    R2 --> R1
    R3 --> R2
```

## 3. 数据流向图

```mermaid
sequenceDiagram
    participant U as 用户
    participant GW as 网关服务
    participant B as 基础服务
    participant T as 交易服务
    participant O as 订单服务
    participant R as 报表服务
    
    Note over U,R: 用户注册登录流程
    U->>GW: 注册/登录请求
    GW->>B: 用户认证
    B->>B: AES加密存储
    B-->>GW: 返回JWT Token
    GW-->>U: 登录成功
    
    Note over U,R: 商品购买流程
    U->>GW: 浏览商品
    GW->>T: 获取商品信息
    T-->>GW: 商品详情
    GW-->>U: 展示商品
    
    U->>GW: 加入购物车
    GW->>T: 购物车操作
    T-->>GW: 操作结果
    
    U->>GW: 提交订单
    GW->>T: 预订单计算
    T->>T: 区域禁售校验
    T-->>GW: 预订单信息
    GW->>O: 创建订单
    O->>O: 库存扣减
    O-->>GW: 订单创建成功
    
    U->>GW: 支付订单
    GW->>O: 处理支付
    O->>O: 调用支付接口
    O-->>GW: 支付结果
    
    Note over U,R: 数据统计流程
    O->>R: 推送订单数据
    T->>R: 推送商品数据
    B->>R: 推送用户数据
    R->>R: 数据汇总统计
    R-->>GW: 统计报表
    GW-->>U: 展示报表
```

## 4. 核心业务流程图

### 4.1 订单创建流程

```mermaid
flowchart TD
    A[用户提交订单] --> B{用户登录校验}
    B -->|未登录| C[跳转登录页面]
    B -->|已登录| D[获取购物车商品]
    
    D --> E[商品状态校验]
    E -->|商品下架| F[提示商品不可购买]
    E -->|商品正常| G[库存校验]
    
    G -->|库存不足| H[提示库存不足]
    G -->|库存充足| I[区域禁售校验]
    
    I -->|禁售区域| J[提示配送限制]
    I -->|可配送| K[计算订单金额]
    
    K --> L[优惠券校验]
    L --> M[创建订单]
    M --> N[扣减库存]
    N --> O[生成支付单]
    O --> P[返回订单信息]
    
    C --> A
    F --> Q[结束]
    H --> Q
    J --> Q
    P --> R[跳转支付页面]
```

### 4.2 支付处理流程

```mermaid
flowchart TD
    A[用户发起支付] --> B[选择支付方式]
    
    B --> C{支付渠道}
    C -->|微信支付| D[调用微信支付API]
    C -->|支付宝支付| E[调用支付宝API]
    C -->|其他支付| F[调用对应支付API]
    
    D --> G[等待支付结果]
    E --> G
    F --> G
    
    G --> H{支付结果}
    H -->|支付成功| I[更新订单状态]
    H -->|支付失败| J[支付失败处理]
    H -->|支付超时| K[订单超时处理]
    
    I --> L[发送支付成功通知]
    L --> M[推送数据到报表系统]
    M --> N[支付完成]
    
    J --> O[释放库存]
    K --> O
    O --> P[通知用户]
```

### 4.3 数据统计流程

```mermaid
flowchart TD
    A[业务系统操作] --> B{数据类型}
    
    B -->|用户数据| C[推送到用户数据源]
    B -->|商品数据| D[推送到商品数据源]
    B -->|订单数据| E[推送到订单数据源]
    B -->|其他数据| F[推送到对应数据源]
    
    C --> G[数据清洗和校验]
    D --> G
    E --> G
    F --> G
    
    G --> H[存储到源数据表]
    H --> I{定时任务触发}
    
    I -->|日结算| J[按日汇总统计]
    I -->|周结算| K[按周汇总统计]
    I -->|月结算| L[按月汇总统计]
    
    J --> M[生成统计报表]
    K --> M
    L --> M
    
    M --> N[报表数据缓存]
    N --> O[API接口输出]
```

## 5. 技术架构图

### 5.1 微服务架构

```mermaid
graph TB
    subgraph "外部系统"
        EXT1[微信支付]
        EXT2[支付宝]
        EXT3[物流系统]
        EXT4[短信服务]
    end
    
    subgraph "负载均衡层"
        LB[Nginx负载均衡]
    end
    
    subgraph "API网关层"
        GW[Spring Cloud Gateway]
    end
    
    subgraph "服务注册中心"
        REG[Nacos/Eureka]
    end
    
    subgraph "配置中心"
        CONF[Nacos Config]
    end
    
    subgraph "业务服务集群"
        SVC1[himall-base-server]
        SVC2[himall-trade-server]
        SVC3[himall-order-server]
        SVC4[himall-report-server]
    end
    
    subgraph "数据存储层"
        DB1[(MySQL主库)]
        DB2[(MySQL从库)]
        CACHE[(Redis集群)]
        ES[(Elasticsearch)]
        MQ[RocketMQ]
    end
    
    subgraph "监控运维"
        MON1[Prometheus监控]
        MON2[Grafana展示]
        LOG[ELK日志系统]
    end
    
    LB --> GW
    GW --> REG
    GW --> CONF
    GW --> SVC1
    GW --> SVC2
    GW --> SVC3
    GW --> SVC4
    
    SVC1 --> DB1
    SVC1 --> CACHE
    SVC2 --> DB1
    SVC2 --> CACHE
    SVC2 --> ES
    SVC3 --> DB1
    SVC3 --> CACHE
    SVC3 --> MQ
    SVC4 --> DB1
    SVC4 --> CACHE
    
    DB1 --> DB2
    
    SVC3 --> EXT1
    SVC3 --> EXT2
    SVC3 --> EXT3
    SVC1 --> EXT4
    
    SVC1 --> MON1
    SVC2 --> MON1
    SVC3 --> MON1
    SVC4 --> MON1
    MON1 --> MON2
    
    SVC1 --> LOG
    SVC2 --> LOG
    SVC3 --> LOG
    SVC4 --> LOG
```

### 5.2 数据库设计架构

```mermaid
erDiagram
    USER ||--o{ ORDER : places
    USER ||--o{ SHOPPING_CART : has
    USER ||--o{ SHIPPING_ADDRESS : has
    
    SHOP ||--o{ PRODUCT : sells
    SHOP ||--o{ ORDER : receives
    SHOP ||--o{ COUPON : issues
    
    PRODUCT ||--o{ SKU : contains
    PRODUCT ||--o{ ORDER_ITEM : ordered
    PRODUCT }o--|| CATEGORY : belongs_to
    PRODUCT }o--|| BRAND : belongs_to
    
    ORDER ||--o{ ORDER_ITEM : contains
    ORDER ||--o{ ORDER_PAY : payment
    ORDER ||--o{ ORDER_REFUND : refund
    
    COUPON ||--o{ COUPON_RECORD : used_by
    
    USER {
        bigint id PK
        string username
        string cell_phone "AES加密"
        string password
        datetime create_time
        datetime update_time
    }
    
    SHOP {
        bigint id PK
        string shop_name
        string contact_phone "AES加密"
        int status
        datetime create_time
    }
    
    PRODUCT {
        bigint id PK
        string product_name
        decimal price
        int status
        bigint category_id FK
        bigint brand_id FK
        bigint shop_id FK
    }
    
    ORDER {
        bigint id PK
        string order_no
        bigint user_id FK
        bigint shop_id FK
        decimal total_amount
        int status
        datetime create_time
    }
```

## 6. 部署架构图

```mermaid
graph TB
    subgraph "CDN层"
        CDN[CDN加速]
    end
    
    subgraph "负载均衡层"
        SLB[阿里云SLB]
    end
    
    subgraph "Web服务器集群"
        WEB1[Nginx-1]
        WEB2[Nginx-2]
    end
    
    subgraph "应用服务器集群"
        APP1[himall-gw-1]
        APP2[himall-gw-2]
        APP3[himall-base-1]
        APP4[himall-base-2]
        APP5[himall-trade-1]
        APP6[himall-trade-2]
        APP7[himall-order-1]
        APP8[himall-order-2]
        APP9[himall-report-1]
    end
    
    subgraph "数据库集群"
        DB_MASTER[(MySQL主库)]
        DB_SLAVE1[(MySQL从库1)]
        DB_SLAVE2[(MySQL从库2)]
    end
    
    subgraph "缓存集群"
        REDIS1[(Redis-1)]
        REDIS2[(Redis-2)]
        REDIS3[(Redis-3)]
    end
    
    subgraph "搜索集群"
        ES1[(ES-Master)]
        ES2[(ES-Data-1)]
        ES3[(ES-Data-2)]
    end
    
    subgraph "消息队列集群"
        MQ1[RocketMQ-1]
        MQ2[RocketMQ-2]
    end
    
    subgraph "存储服务"
        OSS[对象存储OSS]
    end
    
    CDN --> SLB
    SLB --> WEB1
    SLB --> WEB2
    
    WEB1 --> APP1
    WEB1 --> APP3
    WEB1 --> APP5
    WEB1 --> APP7
    WEB2 --> APP2
    WEB2 --> APP4
    WEB2 --> APP6
    WEB2 --> APP8
    
    APP1 --> APP3
    APP1 --> APP5
    APP1 --> APP7
    APP1 --> APP9
    APP2 --> APP4
    APP2 --> APP6
    APP2 --> APP8
    APP2 --> APP9
    
    APP3 --> DB_MASTER
    APP4 --> DB_SLAVE1
    APP5 --> DB_MASTER
    APP6 --> DB_SLAVE2
    APP7 --> DB_MASTER
    APP8 --> DB_SLAVE1
    APP9 --> DB_SLAVE2
    
    DB_MASTER --> DB_SLAVE1
    DB_MASTER --> DB_SLAVE2
    
    APP3 --> REDIS1
    APP4 --> REDIS2
    APP5 --> REDIS1
    APP6 --> REDIS3
    APP7 --> REDIS2
    APP8 --> REDIS3
    
    APP5 --> ES1
    APP6 --> ES2
    
    APP7 --> MQ1
    APP8 --> MQ2
    APP9 --> MQ1
    
    APP3 --> OSS
    APP5 --> OSS
```

这些架构图清晰地展示了himallWork项目的整体架构、服务依赖关系、数据流向、核心业务流程、技术架构和部署架构，有助于更好地理解系统设计和实现。

## 7. 安全架构图

```mermaid
graph TB
    subgraph "外部访问"
        USER[用户]
        HACKER[恶意攻击]
    end
    
    subgraph "安全防护层"
        WAF[Web应用防火墙]
        DDoS[DDoS防护]
        SSL[SSL证书]
    end
    
    subgraph "网关安全"
        AUTH[身份认证]
        RATE[限流控制]
        SIGN[接口签名]
    end
    
    subgraph "应用安全"
        JWT[JWT Token验证]
        RBAC[权限控制]
        ENCRYPT[数据加密]
        AUDIT[操作审计]
    end
    
    subgraph "数据安全"
        DB_ENCRYPT[数据库加密]
        BACKUP[数据备份]
        MASK[数据脱敏]
    end
    
    subgraph "网络安全"
        VPC[私有网络]
        SG[安全组]
        FW[防火墙]
    end
    
    USER --> WAF
    HACKER -.-> WAF
    WAF --> DDoS
    DDoS --> SSL
    SSL --> AUTH
    
    AUTH --> RATE
    RATE --> SIGN
    SIGN --> JWT
    
    JWT --> RBAC
    RBAC --> ENCRYPT
    ENCRYPT --> AUDIT
    
    AUDIT --> DB_ENCRYPT
    DB_ENCRYPT --> BACKUP
    BACKUP --> MASK
    
    MASK --> VPC
    VPC --> SG
    SG --> FW
```

## 8. 监控架构图

```mermaid
graph TB
    subgraph "应用层监控"
        APP1[himall-base]
        APP2[himall-trade]
        APP3[himall-order]
        APP4[himall-report]
        APP5[himall-gw]
    end
    
    subgraph "基础设施监控"
        SYS[系统监控]
        NET[网络监控]
        DB[数据库监控]
        CACHE[缓存监控]
    end
    
    subgraph "日志收集"
        FILEBEAT[Filebeat]
        LOGSTASH[Logstash]
        ES[Elasticsearch]
        KIBANA[Kibana]
    end
    
    subgraph "指标收集"
        PROMETHEUS[Prometheus]
        GRAFANA[Grafana]
        ALERTMANAGER[AlertManager]
    end
    
    subgraph "链路追踪"
        JAEGER[Jaeger]
        ZIPKIN[Zipkin]
    end
    
    subgraph "告警通知"
        EMAIL[邮件通知]
        SMS[短信通知]
        DINGTALK[钉钉通知]
        WECHAT[微信通知]
    end
    
    APP1 --> FILEBEAT
    APP2 --> FILEBEAT
    APP3 --> FILEBEAT
    APP4 --> FILEBEAT
    APP5 --> FILEBEAT
    
    APP1 --> PROMETHEUS
    APP2 --> PROMETHEUS
    APP3 --> PROMETHEUS
    APP4 --> PROMETHEUS
    APP5 --> PROMETHEUS
    
    APP1 --> JAEGER
    APP2 --> JAEGER
    APP3 --> JAEGER
    APP4 --> JAEGER
    APP5 --> JAEGER
    
    SYS --> PROMETHEUS
    NET --> PROMETHEUS
    DB --> PROMETHEUS
    CACHE --> PROMETHEUS
    
    FILEBEAT --> LOGSTASH
    LOGSTASH --> ES
    ES --> KIBANA
    
    PROMETHEUS --> GRAFANA
    PROMETHEUS --> ALERTMANAGER
    
    ALERTMANAGER --> EMAIL
    ALERTMANAGER --> SMS
    ALERTMANAGER --> DINGTALK
    ALERTMANAGER --> WECHAT
```

## 9. 数据流架构图

```mermaid
graph LR
    subgraph "数据源"
        DS1[用户行为数据]
        DS2[交易数据]
        DS3[商品数据]
        DS4[订单数据]
        DS5[支付数据]
    end
    
    subgraph "数据采集"
        DC1[实时采集]
        DC2[批量采集]
        DC3[日志采集]
    end
    
    subgraph "数据处理"
        DP1[数据清洗]
        DP2[数据转换]
        DP3[数据校验]
        DP4[数据去重]
    end
    
    subgraph "数据存储"
        OLTP[(OLTP数据库)]
        OLAP[(OLAP数据库)]
        CACHE[(缓存层)]
        ES[(搜索引擎)]
    end
    
    subgraph "数据应用"
        BI[商业智能]
        REPORT[报表系统]
        REALTIME[实时监控]
        ML[机器学习]
    end
    
    DS1 --> DC1
    DS2 --> DC1
    DS3 --> DC2
    DS4 --> DC1
    DS5 --> DC3
    
    DC1 --> DP1
    DC2 --> DP2
    DC3 --> DP3
    
    DP1 --> DP4
    DP2 --> DP4
    DP3 --> DP4
    
    DP4 --> OLTP
    DP4 --> OLAP
    DP4 --> CACHE
    DP4 --> ES
    
    OLTP --> BI
    OLAP --> REPORT
    CACHE --> REALTIME
    ES --> ML
```

## 10. 容器化部署架构

```mermaid
graph TB
    subgraph "容器编排层"
        K8S[Kubernetes集群]
    end
    
    subgraph "服务网格"
        ISTIO[Istio Service Mesh]
    end
    
    subgraph "应用容器"
        POD1[himall-gw Pod]
        POD2[himall-base Pod]
        POD3[himall-trade Pod]
        POD4[himall-order Pod]
        POD5[himall-report Pod]
    end
    
    subgraph "中间件容器"
        REDIS_POD[Redis Pod]
        MQ_POD[RocketMQ Pod]
        ES_POD[Elasticsearch Pod]
    end
    
    subgraph "数据持久化"
        PV1[MySQL PV]
        PV2[Redis PV]
        PV3[ES PV]
        PV4[Log PV]
    end
    
    subgraph "配置管理"
        CM[ConfigMap]
        SECRET[Secret]
    end
    
    subgraph "服务发现"
        SVC[Service]
        INGRESS[Ingress]
    end
    
    subgraph "监控日志"
        PROMETHEUS_POD[Prometheus Pod]
        GRAFANA_POD[Grafana Pod]
        ELK_POD[ELK Pod]
    end
    
    K8S --> ISTIO
    ISTIO --> POD1
    ISTIO --> POD2
    ISTIO --> POD3
    ISTIO --> POD4
    ISTIO --> POD5
    
    K8S --> REDIS_POD
    K8S --> MQ_POD
    K8S --> ES_POD
    
    REDIS_POD --> PV2
    ES_POD --> PV3
    ELK_POD --> PV4
    
    POD1 --> CM
    POD2 --> CM
    POD3 --> CM
    POD4 --> CM
    POD5 --> CM
    
    POD1 --> SECRET
    POD2 --> SECRET
    POD3 --> SECRET
    POD4 --> SECRET
    POD5 --> SECRET
    
    POD1 --> SVC
    POD2 --> SVC
    POD3 --> SVC
    POD4 --> SVC
    POD5 --> SVC
    
    SVC --> INGRESS
    
    K8S --> PROMETHEUS_POD
    K8S --> GRAFANA_POD
    K8S --> ELK_POD
```

## 总结

以上架构图从多个维度展示了himallWork项目的设计思路：

### 架构特点

1. **分层架构**：清晰的分层设计，职责分离
2. **微服务架构**：服务独立部署，易于扩展
3. **高可用设计**：多实例部署，故障自动转移
4. **安全防护**：多层安全防护，数据加密存储
5. **监控完善**：全方位监控，及时发现问题

### 技术优势

1. **可扩展性**：支持水平扩展，应对业务增长
2. **可维护性**：模块化设计，便于维护升级
3. **高性能**：缓存机制，数据库读写分离
4. **高可靠**：容错机制，数据备份恢复
5. **易部署**：容器化部署，自动化运维

### 业务价值

1. **支撑业务快速发展**：灵活的架构设计
2. **保障系统稳定运行**：完善的监控告警
3. **提升用户体验**：高性能的系统响应
4. **降低运维成本**：自动化的运维管理
5. **支持数据驱动决策**：完整的数据分析体系

这套架构设计体现了现代互联网应用的最佳实践，为电商平台的稳定运行和快速发展提供了坚实的技术基础。
