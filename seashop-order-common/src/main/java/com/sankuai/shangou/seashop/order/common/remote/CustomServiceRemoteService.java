package com.sankuai.shangou.seashop.order.common.remote;

import cn.hutool.core.collection.CollUtil;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.CustomerServiceQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CustomerServiceQueryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.CustomerServiceResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.CustomerServiceRespList;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CustomServiceRemoteService {

    @Resource
    private CustomerServiceQueryFeign customerServiceQueryFeign;

    /**
     * 批量查询店铺的客服配置
     *
     * @param shopIdList 店铺ID
     */
    public List<CustomerServiceRespList> getByShop(List<Long> shopIdList) {
        CustomerServiceQueryPageReq req = new CustomerServiceQueryPageReq();
        req.setShopIds(shopIdList);
        log.info("【远程调用-用户】批量查询用户信息, 请求参数={}", JsonUtil.toJsonString(req));
        List<CustomerServiceRespList> resp = ThriftResponseHelper.executeThriftCall(() -> customerServiceQueryFeign.queryList(req));
        log.info("【远程调用-用户】批量查询用户信息, 响应结果={}", JsonUtil.toJsonString(resp));
        return resp;
    }

    public Map<Long, List<CustomerServiceResp>> getByShopToMap(List<Long> shopIdList) {
        try {
            List<CustomerServiceRespList> resp = getByShop(shopIdList);
            if (CollUtil.isEmpty(resp)) {
                return Collections.emptyMap();
            }
            return resp.stream().collect(Collectors.toMap(CustomerServiceRespList::getShopId, CustomerServiceRespList::getCustomerServiceRespList));
        }
        catch (Exception e) {
            // 客服的接口，异常不影响主流程
            log.error("【远程调用-客服】批量查询店铺客服配置异常", e);
            return Collections.emptyMap();
        }
    }

}
