package com.sankuai.shangou.seashop.order.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@ThriftEnum
public enum OrderResultCodeEnum {

    /**
     * 业务异常定义：xx-xxx-xxx
     * <pre>
     * 一、业务系统区分：020-基础服务；030-交易服务；040-用户服务；050-营销服务；060商品服务；070：订单服务；080：支付服务；
     * 二、前面两位代表错误类型：40-参数校验错误；50-业务逻辑错误；60-系统错误；
     * 三、后面三位代表具体错误码
     * </pre>
     */

    // 订单不存在
    ORDER_NOT_EXIST(50070101, "订单不存在"),
    ORDER_STATUS_NOT_ALLOW_REFUND(50070102, "订单状态不允许退款"),
    ORDER_PAY_RECORD_NOT_EXIST(50070103, "订单支付记录不存在"),
    // 没有可退款的金额
    ORDER_REFUND_AMOUNT_NOT_EXIST(50070104, "没有可退款的金额"),
    // 退款金额大于超支金额
    ORDER_REFUND_AMOUNT_GT_EXCESS_AMOUNT(50070105, "退款金额大于超支金额"),

    ;

    private Integer code;
    private String msg;

    OrderResultCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
