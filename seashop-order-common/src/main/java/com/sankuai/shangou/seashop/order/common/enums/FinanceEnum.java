package com.sankuai.shangou.seashop.order.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2024/1/10/010
 * @description:
 */
public class FinanceEnum {

    @ThriftEnum
    public enum RefundTypes {

        BEFORE_FINISH(1, "订单完成前退款"),
        AFTER_FINISH(2, "订单完成后退款"),
        ;

        private Integer code;
        private String desc;

        @ThriftEnumValue
        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        RefundTypes(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
