package com.sankuai.shangou.seashop.order.common.remote.model.pay;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class CreateRefundBo {

    private String originPayNo;
    private String refundNo;
    private BigDecimal refundAmount;
    /**
     * 业务状态类型：1：正常订单退款；2：异常订单退款；3：订单补偿退款（支付金额大于订单金额）
     */
    private Integer businessStatusType;
}
