package com.sankuai.shangou.seashop.order.common.remote;

import com.sankuai.shangou.seashop.base.boot.common.BatchQueryHelper;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.remote.model.product.DecreaseStockBo;
import com.sankuai.shangou.seashop.product.thrift.core.SkuStockCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateKeyEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateWayEnum;
import com.sankuai.shangou.seashop.product.thrift.core.request.stock.RollBackStockReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.stock.SkuStockReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.stock.UpdateStockReq;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class StockRemoteService {

    @Resource
    private SkuStockCmdFeign skuStockCmdFeign;

    /**
     * 调用库存服务扣减库存
     * <AUTHOR>
     * @param orderItemList 扣减库存的参数
     */
    public void decreaseStock(List<DecreaseStockBo> orderItemList) {
        log.info("【订单创建】扣减库存, orderItemList: {}", JsonUtil.toJsonString(orderItemList));
        Map<String, List<DecreaseStockBo>> itemMap = orderItemList.stream()
                .collect(Collectors.groupingBy(DecreaseStockBo::getOrderId));
        // 每个订单遍历扣减
        itemMap.forEach((orderId, itemList) -> {
            // 同一个订单属于同一个店铺
            Long shopId = itemList.get(0).getShopId();
            // 库存接口每次最多扣减50个商品
            AtomicReference<Integer> seq = new AtomicReference<>(0);
            BatchQueryHelper.batchInvoke(itemList, CommonConst.SKU_DECREASE_BATCH, (list) -> {
                UpdateStockReq req = new UpdateStockReq();
                req.setSeqCode(String.valueOf(seq.getAndSet(seq.get() + 1)));
                req.setBizCode(orderId);
                req.setUpdateWay(StockUpdateWayEnum.CHANGE);
                req.setUpdateType(StockUpdateTypeEnum.CREATE_ORDER);
                req.setUpdateKey(StockUpdateKeyEnum.SKU_ID);
                List<SkuStockReq> stockList = itemList.stream()
                        .map(item -> SkuStockReq.builder()
                                // 扣减库存，要传负数
                                .stock(-item.getQuantity())
                                .shopId(shopId)
                                .skuId(item.getSkuId()).build()
                        )
                        .collect(Collectors.toList());
                req.setStockList(stockList);
                log.info("【订单创建】扣减库存, 请求参数={}", JsonUtil.toJsonString(req));
                ThriftResponseHelper.executeThriftCall(() -> skuStockCmdFeign.syncChangeStock(req));
            });
        });

    }

    /**
     * 调用库存服务回滚库存
     * @param orderIdList 需要回滚的订单ID
     */
    public void rollbackStock(List<String> orderIdList, StockUpdateTypeEnum stockUpdateTypeEnum) {
        log.info("【订单创建】回滚库存, orderIdList: {}", JsonUtil.toJsonString(orderIdList));
        RollBackStockReq req = new RollBackStockReq();
        req.setOrgBizCodes(orderIdList);
        req.setOrgUpdateType(stockUpdateTypeEnum);
        log.info("【订单创建】回滚库存, 请求参数={}", JsonUtil.toJsonString(req));
        ThriftResponseHelper.executeThriftCall(() -> skuStockCmdFeign.rollBackSkuStock(req));
    }

    public void rollbackStockBySku(StockUpdateTypeEnum updateType, String bizCode, List<DecreaseStockBo> orderItemList) {
        log.info("【订单创建】根据SKU回退库存, orderItemList: {}", JsonUtil.toJsonString(orderItemList));
        Map<String, List<DecreaseStockBo>> itemMap = orderItemList.stream()
                .collect(Collectors.groupingBy(DecreaseStockBo::getOrderId));
        // 每个订单遍历扣减
        itemMap.forEach((orderId, itemList) -> {
            // 同一个订单属于同一个店铺
            Long shopId = itemList.get(0).getShopId();
            // 库存接口每次最多扣减50个商品
            AtomicReference<Integer> seq = new AtomicReference<>(0);
            BatchQueryHelper.batchInvoke(itemList, CommonConst.SKU_DECREASE_BATCH, (list) -> {
                UpdateStockReq req = new UpdateStockReq();
                req.setSeqCode(String.valueOf(seq.getAndSet(seq.get() + 1)));
                req.setBizCode(bizCode);
                req.setUpdateWay(StockUpdateWayEnum.CHANGE);
                req.setUpdateType(updateType);
                req.setUpdateKey(StockUpdateKeyEnum.SKU_ID);
                List<SkuStockReq> stockList = itemList.stream()
                        .map(item -> SkuStockReq.builder()
                                // 回滚库存，传整数
                                .stock(item.getQuantity())
                                .shopId(shopId)
                                .skuId(item.getSkuId()).build()
                        )
                        .collect(Collectors.toList());
                req.setStockList(stockList);
                log.info("【订单创建】根据SKU回退库存, 请求参数={}", JsonUtil.toJsonString(req));
                BaseResp resp = ThriftResponseHelper.executeThriftCall(() -> skuStockCmdFeign.syncChangeStock(req));
                log.info("【订单创建】根据SKU回退库存, 返回结果: {}", JsonUtil.toJsonString(resp));
            });

        });
    }

}
