package com.sankuai.shangou.seashop.order.common.remote;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.remote.model.user.RemoteMemberBo;
import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberListReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryUserIdReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberListResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.UserIdListResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/05 10:11
 */
@Service
@Slf4j
public class RemoteMemberService {

    @Resource
    private ShopMemberQueryFeign shopMemberQueryFeign;

    /**
     * 根据会员id列表查询会员信息
     *
     * @param memberIds 会员id列表
     * @return 会员信息列表
     */
    public List<RemoteMemberBo> listByMemberIds(List<Long> memberIds) {
        if (CollectionUtils.isEmpty(memberIds)) {
            return Collections.EMPTY_LIST;
        }

        memberIds = memberIds.stream().distinct().collect(Collectors.toList());
        List<RemoteMemberBo> memberList = new ArrayList<>();
        List<List<Long>> subMemberIdsList = Lists.partition(memberIds, CommonConst.QUERY_LIMIT);
        subMemberIdsList.forEach(subMemberIds -> {
            QueryMemberListReq req = new QueryMemberListReq();
            req.setMemberIds(subMemberIds);
            MemberListResp resp = ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMemberList(req));
            if (CollectionUtils.isNotEmpty(resp.getMemberRespList())) {
                memberList.addAll(JsonUtil.copyList(resp.getMemberRespList(), RemoteMemberBo.class));
            }
        });
        return memberList;
    }

    /**
     * 根据会员id查询会员信息
     *
     * @param memberId 会员id
     * @return 会员信息
     */
    public RemoteMemberBo getByMemberId(Long memberId) {
        if (memberId == null) {
            return null;
        }

        return listByMemberIds(Arrays.asList(memberId)).stream().findFirst().orElse(null);
    }

    /**
     * 根据手机号查询会员id
     *
     * @param mobile 手机号
     * @return 商家ID
     */
    public List<Long> queryUserIdByMobile(String mobile) {
        QueryUserIdReq req = new QueryUserIdReq();
        req.setMobile(mobile);
        log.info("通过手机号码模糊查询商家信息:{}", JsonUtil.toJsonString(req));
        UserIdListResp resp = ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryUserId(req));
        log.debug("通过手机号码模糊查询商家信息:{}", JsonUtil.toJsonString(resp));
        if (resp == null) {
            return null;
        }
        return resp.getUserIdList();
    }
}
