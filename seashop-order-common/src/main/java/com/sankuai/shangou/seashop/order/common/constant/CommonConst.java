package com.sankuai.shangou.seashop.order.common.constant;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
public class CommonConst {

    public static final String CURRENCY_SYMBOL_RMB = "¥";

    public static final String PATTERN_REGION_PATH = "\\|";
    public static final String PATTERN_CATEGORY_FULL_NAME = ",";

    public static final String FORMAT_PATTERN_ORDER_FREIGHT_UPDATED_DESC = "（原运费¥%s元，已%s¥%s元）";

    public static final String DESC_INCREASE = "增加";
    public static final String DESC_DECREASE = "减少";

    public static final String DESC_REFUND_LOG_CANCEL_ORDER_REFUND = "取消售后申请";
    public static final String DESC_REFUND_LOG_PLATFORM_REJECT = "平台驳回售后申请";
    public static final String DESC_REFUND_LOG_SELLER_AUDIT_PREFIX = "供应商处理退款退货申请：";
    public static final String DESC_REFUND_AUDIT_PASS = "通过";
    public static final String DESC_REFUND_AUDIT_REJECT = "拒绝";
    public static final String DESC_REFUND_FREIGHT = "，退运费";
    public static final String DESC_REFUND_NOT_FREIGHT = "，不退运费";
    public static final String DESC_REFUND_LOG_SELLER_CONFIRM_RECEIVE = "供应商确认收货";
    public static final String DESC_REFUND_LOG_SELLER_CONFIRM_RECEIVE_CHANGE = "供应商修改退货数量，退货数量由【%s】改为【%s】";
    public static final Long REFUND_QUANTITY_DEFAULT_ZERO = 0L;

    public static final String DESC_REFUND_ALL_PRODUCT_NAME = "订单所有商品";

    public static final String SKU_SPLITTER = "；";

    public static final Long USER_ID_DEFAULT_ZERO = 0L;

    /**
     * 查询的初始页面
     */
    public static final Integer DEFAULT_PAGE = 1;

    /**
     * 列表最大处理数量
     */
    public static final Integer MAX_LIST_SIZE = 1000;

    // 库存扣减每批次数量
    public static final Integer SKU_DECREASE_BATCH = 50;

    /**
     * 每次查询的最大数量
     */
    public static final Integer QUERY_LIMIT = 200;

    public static final String REFUND_SELLER_AUTO_AUDIT_REMARK = "卖家超时未处理，系统自动同意售后";
    public static final String REFUND_SELLER_AUTO_AUDIT_USERNAME = "系统Job";
    public static final String REFUND_SELLER_AUTO_REJECT_REMARK = "买家超时未寄货，系统自动拒绝售后";

    public static final String MESSAGE_REMIND_SELLER_DELIVERY = "%s店铺，你好，贵店铺有%s笔订单付款已超过%s小时没发货，请务必保证及时发出。";
    public static final String MESSAGE_REMIND_BUYER_NOT_PAY = "尊敬的会员:%s,您还有订单未成功付款，尽快支付别让好货错过！";
    public static final String MESSAGE_AUTO_FINISH_REMARK = "完成过期未确认收货的订单";
    public static final String MESSAGE_REFUND_AMOUNT_NO_FREIGHT_DESC = "最多%s元";
    public static final String MESSAGE_REFUND_AMOUNT_INCLUDE_FREIGHT_DESC = "最多%s元，包含运费%s元";
    public static final String MESSAGE_VERIFY_PRODUCT_SUCCESS_DESC = "商品核销/自提成功";

    // 默认的交易配置
    public static final int DEFAULT_AUTO_FINISH_DAYS = 7;
    public static final String DEFAULT_AUTO_FINISH_DAYS_STR = "7";
    public static final int DEFAULT_BEFORE_RECEIVING_DAYS = 3;
    public static final int DEFAULT_DELAY_DAYS = 3;
    public static final int DEFAULT_PAY_TIMEOUT_HOUR = 168;
    public static final String DEFAULT_PAY_TIMEOUT_HOUR_STR = "168";
    public static final String DEFAULT_AMOUNT_STR_SHOW_COMPANY_BANK = "1000";
    public static final long HOUR_MILLIS = 60 * 60 * 1000L;
    public static final int DEFAULT_COMMENT_TIMEOUT_DAYS = 30;
    public static final int DEFAULT_REFUND_CLOSE_DAYS = 0;
    public static final int DEFAULT_PAID_REVERSE_TIMEOUT_DAYS = 178;
    public static final int DEFAULT_ORDER_ITEM_SIZE_DISPLAY = 20;


    public static final String ORDER_OPERATION_LOG_REFUND_CONFIRM_RECEIVE = "供应商确认收到退货";

    public static final String LOG_ORDER_UPDATE_ITEM_AMOUNT_PATTERN = "供应商修改订单商品的优惠金额：从%s元改价到%s元";

    // 微信消息长度限制
    public static final int WECHAT_MESSAGE_LENGTH = 20;

    public static final String TIME_PATTERN_ORDER_ESTIMATED_FINISH = "YYYY.MM.dd HH:mm:ss";

    // 运费模板是否立即发货特殊标识
    public static final String FLAG_DELIVER_IMMEDIATELY = "0";

    public static final String DATE_TIME_PATTERN_DEFAULT = "yyyy-MM-dd HH:mm:ss";

    /*
     * 查询相关配置常量
     */
    public static final String MESSAGE_REACH_MAX_PAGE_TOTAL = "请输入查询条件，检索您所需要的数据";
    public static final int MAX_PAGE_TOTAL_DEFAULT = 10000;

    public static final BigDecimal HUNDRED = new BigDecimal(100);

}
