package com.sankuai.shangou.seashop.order.common.remote.model.base;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 店铺交易设置
 * <AUTHOR>
 */
@Getter
@Setter
public class ShopTradeSettingBo {

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 起购量校验方式：1：起购数量和起购金额同时满足 2：起购数量和起购金额满足其一
     */
    private Integer purchaseMinValidType;

    /**
     * 起购数量
     */
    private Integer purchaseMinQuantity;

    /**
     * 起购金额
     */
    private BigDecimal purchaseMinPrice;
}
