package com.sankuai.shangou.seashop.order.common.remote.model.base.message;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderDeliveredAppletMessage extends BaseAppletMessage {

    /**
     * 商品名称(20个以内字符,可汉字、数字、字母或符号组合)
     */
    private String thing1;

    /**
     * 订单号(32位以内数字、字母或符号组合)
     */
    private String character_string2;

    /**
     * 订单金额(可带小数，结尾带元)
     */
    private String amount7;

    /**
     * 快递公司(20个以内字符,可汉字、数字、字母或符号组合)
     */
    private String thing4;

    /**
     * 备注(20个以内字符,可汉字、数字、字母或符号组合)
     */
    private String thing6;

}
