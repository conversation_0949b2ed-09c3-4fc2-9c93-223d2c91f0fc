package com.sankuai.shangou.seashop.order.common.remote;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.remote.model.user.MemberContactBo;
import com.sankuai.shangou.seashop.user.thrift.account.MemberContactQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberContactReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberListReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberContactRespList;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberListResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Service
@Slf4j
public class MemberRemoteService {

    @Resource
    private ShopMemberQueryFeign shopMemberQueryFeign;
    @Resource
    private MemberContactQueryFeign memberContactQueryFeign;

    ///**
    // * 查询用户信息
    // *
    // * @param userId 用户id
    // * @return 用户信息
    // */
    //public MemberResp getUserById(Long userId) {
    //    QueryMemberReq req = new QueryMemberReq();
    //    req.setId(userId);
    //    return ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMember(req));
    //}

    /**
     * 批量查询用户信息
     *
     * @param userIdList 用户ID
     */
    public List<MemberResp> getMemberByUserIdList(List<Long> userIdList) {
        QueryMemberListReq req = new QueryMemberListReq();
        req.setMemberIds(userIdList);
        log.info("【远程调用-用户】批量查询用户信息, 请求参数={}", JsonUtil.toJsonString(req));
        MemberListResp resp = ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMemberList(req));
        log.info("【远程调用-用户】批量查询用户信息, 响应结果={}", JsonUtil.toJsonString(resp));
        if (resp == null || CollUtil.isEmpty(resp.getMemberRespList())) {
            return null;
        }
        return resp.getMemberRespList();
    }

    public Map<Long, MemberResp> getMemberByUserIdListMap(List<Long> userIdList) {
        List<MemberResp> memberByUserIdList = getMemberByUserIdList(userIdList);
        if (CollUtil.isEmpty(memberByUserIdList)) {
            return Collections.emptyMap();
        }
        return memberByUserIdList.stream().collect(Collectors.toMap(MemberResp::getId, Function.identity(), (oldV, newV) -> newV));
    }

    /**
     * 根据用户ID获取用户的联系方式
     * 用户服务进行了处理，每个用户一条记录
     *
     * @param userIdList 用户ID
     * <AUTHOR>
     */
    public List<MemberContactBo> getMemberContactByUserId(List<Long> userIdList) {
        QueryMemberContactReq req = new QueryMemberContactReq();
        req.setUserIds(userIdList);
        log.info("【远程调用-用户】查询用户的联系方式, 请求参数={}", JsonUtil.toJsonString(req));
        MemberContactRespList resp = ThriftResponseHelper.executeThriftCall(() -> memberContactQueryFeign.queryMemberContact(req));
        log.info("【远程调用-用户】查询用户的联系方式, 响应结果={}", JsonUtil.toJsonString(resp));
        if (resp == null || CollUtil.isEmpty(resp.getMemberContactRespList())) {
            return null;
        }
        return JsonUtil.copyList(resp.getMemberContactRespList(), MemberContactBo.class);
    }

    /**
     * 根据用户ID获取用户的联系方式
     * 用户服务进行了处理，每个用户一条记录
     *
     * @param userId 用户ID
     */
    public MemberContactBo getMemberContactByUserId(Long userId) {
        List<MemberContactBo> list = getMemberContactByUserId(CollUtil.newArrayList(userId));
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

    /**
     * 根据店铺ID，获取店铺的联系方式
     * 用户服务进行了处理，每个用户一条记录
     * <AUTHOR>
     * @param shopIdList 店铺ID
     */
    public List<MemberContactBo> getMemberContactByShopId(List<Long> shopIdList) {
        QueryMemberContactReq req = new QueryMemberContactReq();
        req.setShopIds(shopIdList);
        log.info("【远程调用-用户】查询店铺的联系方式, 请求参数={}", JsonUtil.toJsonString(req));
        MemberContactRespList resp = ThriftResponseHelper.executeThriftCall(() -> memberContactQueryFeign.queryMemberContact(req));
        log.info("【远程调用-用户】查询店铺的联系方式, 响应结果={}", JsonUtil.toJsonString(resp));
        if (resp == null || CollUtil.isEmpty(resp.getMemberContactRespList())) {
            return null;
        }
        return JsonUtil.copyList(resp.getMemberContactRespList(), MemberContactBo.class);
    }

    /**
     * 根据店铺ID，获取店铺的联系方式
     * 用户服务进行了处理，每个用户一条记录
     * @param shopId 店铺ID
     */
    public MemberContactBo getMemberContactByShopId(Long shopId) {
        List<MemberContactBo> list = getMemberContactByShopId(CollUtil.newArrayList(shopId));
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return list.get(0);
    }

}
