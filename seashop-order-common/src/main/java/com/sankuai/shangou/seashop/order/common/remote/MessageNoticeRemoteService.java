package com.sankuai.shangou.seashop.order.common.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageNoticeRemoteService {

    //@Resource
    //private MessageNoticeSettingQueryThriftService messageNoticeSettingQueryThriftService;

    ///**
    // * 获取消息通知设置
    // *
    // * @param template 模板
    // * @return 消息通知设置
    // */
    //public BaseMessageNoticeSettingRes queryMessageNoticeSetting(PlatformMessageTemplateEnum template) {
    //    log.info("获取消息通知设置,模板:{}", template);
    //    BaseMessageNoticeSettingRes resp = ThriftResponseHelper.executeThriftCall(() -> messageNoticeSettingQueryThriftService.getMessageNoticeSettingByType(template.getCode()));
    //    log.info("获取消息通知设置,模板:{}", JsonUtil.toJsonString(resp));
    //    return resp;
    //}

}
