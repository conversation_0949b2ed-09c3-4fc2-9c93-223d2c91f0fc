package com.sankuai.shangou.seashop.order.common.constant;

/**
 * Mafka相关的常量，包括topic名称、消费者组名称等
 * <AUTHOR>
 */
public class MafkaConst {

    // mafka命名空间，用于区分不同的业务，这里固定为waimai
    public static final String DEFAULT_NAMESPACE = "waimai";

    /*
     * topic名称，用于区分不同的业务
     */
    // 下单时订单检查topic
    public static final String TOPIC_DELAY_ORDER_CHECK = "seashop_order_check_delay_topic";
    // 下单失败topic
    public static final String TOPIC_ORDER_FAILURE = "seashop_order_failure_topic";
    // 订单ES构建topic
    public static final String TOPIC_ORDER_DTS = "seashop_order_dts_topic";
    // 下单成功删除购物车
    public static final String TOPIC_REMOVE_SHOPPING_CART = "seashop_remove_shopping_cart_topic";
    // 订单变更topic
    public static final String TOPIC_ORDER_CHANGE = "seashop_order_change_topic";
    public static final String GROUP_ORDER_ES_BUILD = "seashop_order_es_build_consumer";

    public static final String GROUP_ORDER_WDT_BUILD = "seashop_order_wdt_build_consumer";
    // 订单售后topic
    public static final String TOPIC_ORDER_REFUND = "seashop_order_refund_topic";
    public static final String GROUP_REFUND_ES_BUILD = "seashop_order_refund_es_build_consumer";

    // 订单售后构建到旺店通的消费者组
    public static final String GROUP_REFUND_WDT_BUILD = "seashop_order_refund_wdt_build_consumer";
    // 订单售后DTS topic
    public static final String TOPIC_ORDER_REFUND_DTS = "seashop_order_refund_dts_topic";

    /*
     * 消费者组名称，用于区分topic的不同用途，在kafka的实现中，
     * 通过消费者组实现发布订阅，不同的消费者组可以消费同一条消息；但同一条消息只会被同一个消费者组消费一次
     */
    // 交易商品构建到ES中的消费者组
    public static final String GROUP_ORDER_CHECK = "seashop_order_check_delay_consumer";
    // 店铺构建到ES中的消费者组
    public static final String GROUP_ORDER_FAILURE = "seashop_order_failure_consumer";
    // 下单成功删除购物车
    public static final String GROUP_ORDER_DTS = "seashop_order_dts_consumer";
    // 订单变更消费者组
    public static final String GROUP_ORDER_CHANGE = "seashop_order_change_consumer";
    public static final String GROUP_ORDER_CHANGE_FOR_REFUND = "seashop_order_change_for_refund_consumer";
    // 订单售后消费者组
    public static final String GROUP_ORDER_REFUND = "seashop_order_refund_consumer";
    // 订单售后DTS消费者组，用于构建售后ES
    public static final String GROUP_ORDER_REFUND_DTS = "seashop_order_refund_dts_consumer";

    // 订单支付回调
    public static final String TOPIC_ORDER_PAY_NOTIFY = "seashop_order_pay_status_change_topic";
    public static final String GROUP_ORDER_PAY_NOTIFY = "seashop_order_pay_status_change_consumer";

    // 订单退款回调
    public static final String TOPIC_ORDER_REFUND_NOTIFY = "seashop_order_reverse_topic";
    public static final String GROUP_ORDER_REFUND_NOTIFY = "seashop_order_reverse_consumer";

    /**
     * 内容审核topic
     */
    public static final String TOPIC_CONTENT_AUDIT = "canyinrc_virbius_waimai_content_audit_notifier";

    /**
     * 内容审核消费组
     */
    public static final String GROUP_CONTENT_AUDIT = "sea_shop_content_audit_order_consumer";


    // 延时时间，单位毫秒
    public static final long DELAY_TIME_ORDER_CHECK = 30 * 60 * 1000;

    /**
     * 构建商品评论es
     */
    public static final String TOPIC_PRODUCT_COMMENT = "seashop_order_product_comment_dts_topic";

    /**
     * 构建商品评论es消费者组
     */
    public static final String GROUP_PRODUCT_COMMENT_ES_BUILD = "seashop_order_comment_dts_es_build_consumer";

}
