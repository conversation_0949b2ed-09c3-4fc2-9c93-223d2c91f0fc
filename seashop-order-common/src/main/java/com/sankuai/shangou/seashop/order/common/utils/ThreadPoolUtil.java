package com.sankuai.shangou.seashop.order.common.utils;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.google.common.util.concurrent.ThreadFactoryBuilder;

/**
 * <AUTHOR>
 */
public class ThreadPoolUtil {

    private final static int PROCESSOR_NUM = Runtime.getRuntime().availableProcessors();

    public static ThreadPoolExecutor ASYNC_API_POOL = generateThreadPoolExecutor("trade-asyncApi");
//    public static ThreadPool ASYNC_API_POOL = generateAsyncApiThreadPool(ThreadPoolConst.RHINO_KEY_THREAD_POOL_ASYNC_API, "trade-asyncApi");

//    private static ThreadPool generateAsyncApiThreadPool(String rhinoKey, String threadPrefix) {
//        DefaultThreadPoolProperties.Setter setter = DefaultThreadPoolProperties.Setter()
//                .withCoreSize(PROCESSOR_NUM)
//                .withMaxSize(PROCESSOR_NUM * 3)
//                .withKeepAliveTimeMinutes(10)
//                .withKeepAliveTimeUnit(TimeUnit.MINUTES)
//                .withBlockingQueue(new LinkedBlockingQueue<>())
//                .withMaxQueueSize(2000)
//                .withThreadFactory(new ThreadFactoryBuilder().setNameFormat(threadPrefix).build());
//
//        return Rhino.newThreadPool(rhinoKey, setter);
//    }

    private static ThreadPoolExecutor generateThreadPoolExecutor(String threadPrefix) {
        return new ThreadPoolExecutor(PROCESSOR_NUM, PROCESSOR_NUM * 3, 10, TimeUnit.MINUTES, new LinkedBlockingQueue<>(2000),
            new ThreadFactoryBuilder().setNameFormat(threadPrefix).build());
    }

}
