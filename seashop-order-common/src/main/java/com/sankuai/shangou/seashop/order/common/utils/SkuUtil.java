package com.sankuai.shangou.seashop.order.common.utils;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;

/**
 * <AUTHOR>
 */
public class SkuUtil {

    /**
     * 组装sku名称
     * <p>历史原因，数据库的字段名是 color，size，version，但是规格的别名是可以自定义的，并不一定是颜色、尺寸、版本，
     * 且订单只保存的了具体的规格值，所以这里只拼接具体的值</p>
     *
     * @param color 规格1
     * @param size 规格2
     * @param version 规格3
     * @return 拼接的规格名称
     */
    public static String assembleSkuName(String color, String size, String version) {
        StringBuilder descBuilder = new StringBuilder();
        if (StrUtil.isNotBlank(color)) {
            descBuilder.append(color).append(CommonConst.SKU_SPLITTER);
        }
        if (StrUtil.isNotBlank(size)) {
            descBuilder.append(size).append(CommonConst.SKU_SPLITTER);
        }
        if (StrUtil.isNotBlank(version)) {
            descBuilder.append(version).append(CommonConst.SKU_SPLITTER);
        }
        String desc = descBuilder.toString();
        if (StrUtil.isNotBlank(desc)) {
            return desc.substring(0, desc.length() -1);
        }
        return desc;
    }

}
