package com.sankuai.shangou.seashop.order.common.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class SettingUtil {

    public static int getIntValueOrDefault(String bizDesc, String value, int defaultValue) {
        int v = defaultValue;
        if (StrUtil.isNotBlank(value)) {
            v = Integer.parseInt(value);
        }
        log.info("交易配置 {} 的值为: {}, 最终的取值为: {}", bizDesc, value, v);
        return v;
    }

    public static int getIntValueOrDefault(String bizDesc, Integer value, int defaultValue) {
        int v = ObjectUtil.defaultIfNull(value, defaultValue);
        log.info("交易配置 {} 的值为: {}, 最终的取值为: {}", bizDesc, value, v);
        return v;
    }

}
