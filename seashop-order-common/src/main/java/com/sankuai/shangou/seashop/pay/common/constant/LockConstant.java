package com.sankuai.shangou.seashop.pay.common.constant;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
public class LockConstant {

    /**
     * 分布式锁：支付创建的锁
     */
    public static final String PAYMENT_CREATE_LOCK_KEY = "pay.payment.create.orderId:%s";

    /**
     * 分布式锁：退款创建的锁
     */
    public static final String REVERSE_CREATE_LOCK_KEY = "pay.reverse.create.orderId:%s";

    /**
     * 分布式锁：支付确认的锁
     */
    public static final String PAYMENT_CONFIRM_LOCK_KEY = "pay.confirm.create.orderId:%s";

    /**
     * 分布式锁：支付配置修改的锁
     */
    public static final String PAYMENT_CONFIG_LOCK_KEY = "pay.config.update";

    /**
     * 分布式锁：支付创建的锁
     */
    public static final String PAYMENT_CALLBACK_LOCK_KEY = "pay.callback.orderId:%s";

    /**
     * 尝试获取锁时间
     */
    public static final Long LOCK_TIME = 2L;
}
