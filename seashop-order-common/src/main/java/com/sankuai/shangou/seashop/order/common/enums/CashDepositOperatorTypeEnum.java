package com.sankuai.shangou.seashop.order.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@ThriftEnum
public enum CashDepositOperatorTypeEnum {

    /**
     * 类型；1，付款；2，扣款；3，退款
     */
    PAY(1, "付款"),
    DEDUCTION(2, "扣款"),
    REFUND(3, "退款"),
    ;

    private Integer type;
    private String name;

    @ThriftEnumValue
    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    CashDepositOperatorTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
}
