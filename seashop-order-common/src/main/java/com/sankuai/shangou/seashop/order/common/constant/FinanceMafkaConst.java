package com.sankuai.shangou.seashop.order.common.constant;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
public class FinanceMafkaConst {

//    /**
//     * 默认的命名空间
//     */
//    public static final String DEFAULT_NAMESPACE = "waimai";

    /**
     * 保证金支付状态变更 topic
     */
    public static final String CASH_DEPOSIT_PAY_STATUS_CHANGE_TOPIC = "seashop_order_pay_status_change_topic";

    /**
     * 保证金支付状态变更 消费者
     */
    public static final String CASH_DEPOSIT_PAY_STATUS_CHANGE_CONSUMER = "seashop_category_pay_status_change_consumer";

    /**
     * 保证金退款状态变更 topic
     * （未结算，支付撤销）
     */
    public static final String CASH_DEPOSIT_REVERSE_STATUS_CHANGE_TOPIC = "seashop_order_reverse_topic";

    /**
     * 保证金退款状态变更 消费者
     */
    public static final String CASH_DEPOSIT_REVERSE_STATUS_CHANGE_CONSUMER = "seashop_cash_deposit_reverse_consumer";

    /**
     * 订单结算通知 topic
     * topic：seashop_order_settle_msg_topic（order）
     *
     * 消费组: seashop_order_settle_msg_consumer(erp)
     */
    public static final String ORDER_SETTLE_MSG_TOPIC = "seashop_order_settle_msg_topic";

}
