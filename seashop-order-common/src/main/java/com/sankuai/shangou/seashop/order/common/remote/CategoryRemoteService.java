package com.sankuai.shangou.seashop.order.common.remote;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.remote.model.product.RemoteCategoryBo;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryCategoryListReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryListResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class CategoryRemoteService {

    @Resource
    private CategoryQueryFeign categoryQueryFeign;

    public List<RemoteCategoryBo> getCategoryList(List<Long> categoryIdList) {
        QueryCategoryListReq req = new QueryCategoryListReq();
        req.setIds(categoryIdList);
        log.info("【商品】查询分类列表, 请求参数={}", req);
        CategoryListResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryQueryFeign.queryCategoryList(req));
        log.info("【商品】查询分类列表, 响应结果={}", resp);
        if (resp == null || resp.getCategoryRespList() == null) {
            return Collections.emptyList();
        }
        return JsonUtil.copyList(resp.getCategoryRespList(), RemoteCategoryBo.class);
    }

}
