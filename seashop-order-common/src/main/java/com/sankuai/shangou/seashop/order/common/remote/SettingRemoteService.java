package com.sankuai.shangou.seashop.order.common.remote;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.TradeSettingsQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.TradeSiteSettingsResp;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SettingRemoteService {

    @Resource
    private TradeSettingsQueryFeign tradeSettingsQueryFeign;

    /**
     * 获取平台配置的交易设置
     * <AUTHOR>
     *
     */
    public TradeSiteSettingBo getTradeSiteSetting() {
        TradeSiteSettingsResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeSettingsQueryFeign.queryTradeSiteSetting());
        log.info("获取平台配置的交易设置:{}", JsonUtil.toJsonString(resp));
        return JsonUtil.copy(resp, TradeSiteSettingBo.class);
    }

}
