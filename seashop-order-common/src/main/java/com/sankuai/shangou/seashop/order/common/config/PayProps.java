package com.sankuai.shangou.seashop.order.common.config;


import lombok.Getter;
import lombok.Setter;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 支付相关配置
 * config 说明文档 <a href=https://km.sankuai.com/page/550965600></a>
 * <AUTHOR>
 */
@Component
@Getter
@Setter
public class PayProps {

    /**
     * 订单后可撤销的超时天数，默认值178天
     */
    @Value("${seashop.order.pay.paidReverseTimeoutDays:178}")
    private Integer paidReverseTimeoutDays;

}
