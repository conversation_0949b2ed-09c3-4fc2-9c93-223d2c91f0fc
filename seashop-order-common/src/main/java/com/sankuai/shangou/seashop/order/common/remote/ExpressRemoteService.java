package com.sankuai.shangou.seashop.order.common.remote;

import com.dianping.sc.express.enums.ExpressPlatformEnum;
import com.sankuai.shangou.seashop.base.thrift.core.ExpressTrackQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.ExpressSubscribeReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
public class ExpressRemoteService {

    @Resource
    private ExpressTrackQueryFeign expressTrackQueryThriftService;


    public void subscribe(String expressCompanyCode, String shipOrderNumber) {
        ExpressSubscribeReq req = new ExpressSubscribeReq();
        req.setExpressNo(shipOrderNumber);
        req.setPlatform(ExpressPlatformEnum.KDNIAO.getCode());
        req.setBizId(UUID.randomUUID().toString());
        req.setCompanyCode(expressCompanyCode);
        expressTrackQueryThriftService.submitExpressSubscribe(req);
    }

}
