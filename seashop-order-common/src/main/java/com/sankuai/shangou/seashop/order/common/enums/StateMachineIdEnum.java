package com.sankuai.shangou.seashop.order.common.enums;

/**
 * 状态机ID枚举
 * <AUTHOR>
 */
public enum StateMachineIdEnum {

    ORDER_STATE_MACHINE("seashop-order-state-machine", "订单状态机")
    ;

    private final String stateMachineId;
    private final String desc;

    StateMachineIdEnum(String stateMachineId, String desc) {
        this.stateMachineId = stateMachineId;
        this.desc = desc;
    }

    public String getStateMachineId() {
        return stateMachineId;
    }

    public String getDesc() {
        return desc;
    }

}
