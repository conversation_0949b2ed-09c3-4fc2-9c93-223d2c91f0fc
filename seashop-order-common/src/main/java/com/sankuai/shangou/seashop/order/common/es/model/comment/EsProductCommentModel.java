package com.sankuai.shangou.seashop.order.common.es.model.comment;

import java.util.Date;

import com.sankuai.shangou.seashop.order.common.es.model.EsBase;

import lombok.Getter;
import lombok.Setter;

/**
 * 构建交易商品基本信息对象
 * <AUTHOR>
 */
@Getter
@Setter
public class EsProductCommentModel extends EsBase<Long> {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 商品评论id(美团id组件生成, 与其他表的关联使用该字段)
     */
    private Long productCommentId;

    /**
     * 订单id, 对应order.order_id
     */
    private String orderId;

    /**
     * 订单详细id，对应order_item.id
     */
    private Long subOrderId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格1别名
     */
    private String spec1Alias;

    /**
     * 规格2别名
     */
    private String spec2Alias;

    /**
     * 规格3别名
     */
    private String spec3Alias;

    /**
     * 规格1名称
     */
    private String spec1Value;

    /**
     * 规格2名称
     */
    private String spec2Value;

    /**
     * 规格3名称
     */
    private String spec3Value;

    /**
     * 缩略图
     */
    private String thumbnailsUrl;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 商家id
     */
    private Long userId;

    /**
     * 商家名称
     */
    private String userName;

    /**
     * 商家email
     */
    private String email;

    /**
     * 商家手机号
     */
    private String userMobile;

    /**
     * 评价内容
     */
    private String reviewContent;

    /**
     * 评价日期
     */
    private Date reviewDate;

    /**
     * 评分
     */
    private Integer reviewMark;

    /**
     * 回复内容
     */
    private String replyContent;

    /**
     * 回复日期
     */
    private Date replyDate;

    /**
     * 是否回复了图片
     */
    private Boolean hasImage;

    /**
     * 追加内容
     */
    private String appendContent;

    /**
     * 追加时间
     */
    private Date appendDate;

    /**
     * 是否追加了图片
     */
    private Boolean appendHasImage;

    /**
     * 追加评论回复
     */
    private String replyAppendContent;

    /**
     * 追加评论回复时间
     */
    private Date replyAppendDate;

    /**
     * 是否隐藏(前端不显示)
     */
    private Boolean hasHidden;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 规格ID
     */
    private String skuId;

    @Override
    public Long getPrimaryKey() {
        return productCommentId;
    }
}
