package com.sankuai.shangou.seashop.order.common.enums;

/**
 * <AUTHOR>
 */
public enum SwitchBizTypeEnum {

    ALLOW_CREATE_ORDER("allowCreateOrder", "系统当前不允许提交订单，如有疑问请联系系统管理员"),
    ALLOW_REFUND_PLATFORM_CONFIRM("allowRefundPlatformConfirm", "系统当前不允许平台确认退款，如有疑问请联系系统管理员"),
    ALLOW_INIT_PAY("allowInitPay", "系统当前不允许发起支付，如有疑问请联系系统管理员"),
    ;

    private final String key;
    private final String defaultMessage;

    SwitchBizTypeEnum(String key, String defaultMessage) {
        this.key = key;
        this.defaultMessage = defaultMessage;
    }

    public String getKey() {
        return key;
    }

    public String getDefaultMessage() {
        return defaultMessage;
    }
}
