package com.sankuai.shangou.seashop.order.common.config;

import cn.hutool.json.JSONUtil;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 系统开关配置
 * config 说明文档 <a href=https://km.sankuai.com/page/550965600></a>
 * <AUTHOR>
 */
@Component
@Getter
@Setter
@Slf4j
public class SystemSwitchProps {

    /**
     * 一些系统开关，可以为空，开关对象设置allowFlag为false时不允许进行业务
     */
    @Value("#{${seashop.order.switch.systemSwitch}}")
    private Map<String, Map<String,Object>> switchContentMap = Collections.emptyMap();

    private Map<String, SwitchContent> systemSwitch = new HashMap<>();
    public SwitchContent getSwitch(String key) {
        SwitchContent content = systemSwitch.get(key);
        log.info("获取系统开关, key={},content:{}", key, content);
        return content;
    }

    @PostConstruct
    public void initSystemSwitch() {
        switchContentMap.entrySet().forEach(e -> {
            this.systemSwitch.put(e.getKey(), JSONUtil.toBean(JSONUtil.toJsonStr(e.getValue()), SwitchContent.class));
        });
    }

    public static class SwitchContent {
        /**
         * 开关是否允许业务。明确定义为false才是禁止业务，即默认是 true
         */
        private Boolean allowFlag;
        /**
         * 禁止业务时的提示信息
         */
        private String forbiddenMessage;

        public Boolean getAllowFlag() {
            return allowFlag;
        }

        public void setAllowFlag(Boolean allowFlag) {
            this.allowFlag = allowFlag;
        }

        public String getForbiddenMessage() {
            return forbiddenMessage;
        }

        public void setForbiddenMessage(String forbiddenMessage) {
            this.forbiddenMessage = forbiddenMessage;
        }

        @Override
        public String toString() {
            return "SwitchContent{" +
                    "allowFlag=" + allowFlag +
                    ", forbiddenMessage='" + forbiddenMessage + '\'' +
                    '}';
        }
    }

}
