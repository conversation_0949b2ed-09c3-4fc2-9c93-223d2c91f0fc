package com.sankuai.shangou.seashop.order.common.remote;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.remote.model.promotion.CouponBo;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponRecordConsumeDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordCancelConsumeReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.CouponRecordConsumeReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleCancelConsumeReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleStockReturnReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponQueryFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.CouponRecordCmdFeign;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCmdFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PromotionRemoteService {

    @Resource
    private CouponQueryFeign couponQueryFeign;
    @Resource
    private CouponRecordCmdFeign couponRecordCmdFeign;
    @Resource
    private FlashSaleCmdFeign flashSaleCmdFeign;

    /**
     * 核销优惠券，下单时核销
     */
    public void chargeCoupon(Long userId, List<CouponRecordConsumeDto> couponRecordConsumeList, Date orderCreateTime) {
        CouponRecordConsumeReq req = new CouponRecordConsumeReq();
        req.setMemberId(userId);
        req.setConsumeList(couponRecordConsumeList);
        req.setConsumeTime(orderCreateTime);
        log.info("【营销接口】【优惠券】核销优惠券, 请求参数: {}", JsonUtil.toJsonString(req));
        BaseResp resp = ThriftResponseHelper.executeThriftCall(() -> couponRecordCmdFeign.consume(req));
        log.info("【营销接口】【优惠券】核销优惠券, 返回结果: {}", JsonUtil.toJsonString(resp));
    }

    /**
     * 回滚优惠券，下单失败时回滚、退款全部时回滚
     */
    public void rollbackCoupon(Long userId, List<String> orderIdList) {
        CouponRecordCancelConsumeReq req = new CouponRecordCancelConsumeReq();
        req.setOrderIdList(orderIdList);
        req.setMemberId(userId);
        log.info("【营销接口】【优惠券】回滚优惠券, 请求参数: {}", JsonUtil.toJsonString(req));
        BaseResp resp = ThriftResponseHelper.executeThriftCall(() -> couponRecordCmdFeign.cancelConsume(req));
        log.info("【营销接口】【优惠券】回滚优惠券, 返回结果: {}", JsonUtil.toJsonString(resp));
    }

    public CouponBo getCouponById(Long couponId) {
        log.info("【营销接口】【优惠券】根据优惠券活动ID查询优惠券, couponId: {}", couponId);
        BaseIdReq baseIdReq = new BaseIdReq();
        baseIdReq.setId(couponId);
        CouponResp couponResp = ThriftResponseHelper.executeThriftCall(() -> couponQueryFeign.getById(baseIdReq));
        log.info("【营销接口】【优惠券】根据优惠券活动ID查询优惠券, 返回结果: {}", JsonUtil.toJsonString(couponResp));
        if (couponResp == null) {
            return null;
        }
        return JsonUtil.copy(couponResp, CouponBo.class);
    }

    //public void decreaseFlashSaleStock(FlashSaleConsumeReq consumeReq) {
    //    log.info("【营销接口】【限时购】扣减限时购库存, 请求参数: {}", JsonUtil.toJsonString(consumeReq));
    //    BaseResp resp = ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.consume(consumeReq));
    //    log.info("【营销接口】【限时购】扣减限时购库存, 返回结果: {}", JsonUtil.toJsonString(resp));
    //}

    public void rollbackFlashSaleStock(String orderId) {
        FlashSaleCancelConsumeReq req = new FlashSaleCancelConsumeReq();
        req.setOrderId(orderId);
        log.info("【营销接口】【限时购】回滚限时购库存, 请求参数: {}", JsonUtil.toJsonString(req));
        BaseResp resp = ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.cancelConsume(req));
        log.info("【营销接口】【限时购】回滚限时购库存, 返回结果: {}", JsonUtil.toJsonString(resp));
    }

    public void rollbackFlashSaleStock(String orderId, Long flashSaleId, String skuId, Long relationId, Integer returnNum) {
        FlashSaleStockReturnReq req = new FlashSaleStockReturnReq();
        req.setOrderId(orderId);
        req.setFlashSaleId(flashSaleId);
        req.setSkuId(skuId);
        req.setRelationId(relationId);
        req.setReturnNum(returnNum);
        log.info("【营销接口】【限时购】回滚限时购库存, 请求参数: {}", JsonUtil.toJsonString(req));
        BaseResp resp = ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.stockReturn(req));
        log.info("【营销接口】【限时购】回滚限时购库存, 返回结果: {}", JsonUtil.toJsonString(resp));
    }

}
