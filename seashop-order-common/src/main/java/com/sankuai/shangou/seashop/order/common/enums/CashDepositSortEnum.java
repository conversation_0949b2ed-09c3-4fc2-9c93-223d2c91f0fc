package com.sankuai.shangou.seashop.order.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description: 保证金排序枚举
 */
@ThriftEnum
public enum CashDepositSortEnum {

    TOTAL_BALANCE(1, "totalBalance", "保证金总额"),
    CURRENT_BALANCE(2, "currentBalance", "当前保证金"),
    DATE(3, "date", "日期"),
    ;

    private Integer code;
    private String sort;
    private String name;

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getSort() {
        return sort;
    }

    public String getName() {
        return name;
    }

    CashDepositSortEnum(Integer code, String sort, String name) {
        this.code = code;
        this.sort = sort;
        this.name = name;
    }
}
