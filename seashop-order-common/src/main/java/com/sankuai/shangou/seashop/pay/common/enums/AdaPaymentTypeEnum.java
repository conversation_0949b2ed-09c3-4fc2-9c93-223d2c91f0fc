package com.sankuai.shangou.seashop.pay.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.AllArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description:
 */
@ThriftEnum
@AllArgsConstructor
public enum AdaPaymentTypeEnum {
    /**
     * .net对应的支付类型
     * [Description("支付宝扫码")]
     * AlipayScan = 1,
     * [Description("支付宝H5")]
     * AlipayH5 = 2,
     * [Description("微信小程序")]
     * WeixinApplet = 3,
     * [Description("微信H5")]
     * WeixinH5 = 4,
     * [Description("企业网银")]
     * CompanyBank = 5,
     * [Description("个人网银")]
     * PersonBank = 6,
     */

    // 支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银，

    ALIPAY_SCAN(1, "alipay_qr", "支付宝扫码"),
    ALIPAY_H5(2, "alipay_wap", "支付宝H5"),
    WECHAT_APPLET(3, "wx_lite", "微信小程序"),
    WECHAT_H5(4, "", "微信H5"),
    COMPANY_BANK(5, "b2b", "企业网银"),
    PERSON_BANK(6, "b2c", "个人网银"),
    WECHAT_NATIVE(7, "wx_scan", "微信反扫"),

    ;

    private Integer type;
    private String channel;
    private String name;

    @ThriftEnumValue
    public int getType() {
        return type;
    }

    public String getChannel() {
        return channel;
    }

    public String getName() {
        return name;
    }

    /**
     * 通过type获取枚举
     *
     * @param type
     */
    public static AdaPaymentTypeEnum getEnumByType(int type) {
        for (AdaPaymentTypeEnum value : AdaPaymentTypeEnum.values()) {
            if (value.getType() == type) {
                return value;
            }
        }
        return null;
    }

}
