package com.sankuai.shangou.seashop.order.common.remote.model.pay;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class AdaPayCreateExpandBo {

    /**
     * 用户openId，小程序支付必传
     */
    private String openId;
    /**
     *  个人网银支持的银行bank_code。b2b b2c 参数
     */
    private String acctIssrId;
    /**
     * 银行卡类型：debit-借记卡；credit-贷记卡(默认借记卡，)
     */
    private String cardType;
    /**
     * 发起支付的用户端ip
     */
    private String clientIp;
    /**
     * 商户前端页面地址，支付成功或失败时，会向该地址跳转
     */
    private String callbackUrl;

}
