package com.sankuai.shangou.seashop.pay.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;
import lombok.AllArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description:
 */
@ThriftEnum
@AllArgsConstructor
public enum AdaPayEnums {

    /**
     * 汇付请求结果支付翻
     */
    SUCCEEDED(1, "succeeded", "成功"),
    PENDING(2, "pending", "等待中"),
    FAILED(3, "failed", "支付失败");

    private Integer code;
    private String status;
    private String displayName;

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getStatus() {
        return status;
    }

    public String getDisplayName() {
        return displayName;
    }

}
