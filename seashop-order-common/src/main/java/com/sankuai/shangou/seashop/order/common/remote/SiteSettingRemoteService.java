package com.sankuai.shangou.seashop.order.common.remote;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseSitSettingRes;

import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SiteSettingRemoteService {

    @Resource
    private SiteSettingQueryFeign siteSettingQueryFeign;

    private static final String ORDER_COMMENT_OUT_TIME = "orderCommentTimeout";
    private static final Long DEFAULT_ORDER_COMMENT_OUT_TIME = 7l;

    ///**
    // * 获取站点设置
    // *
    // * @return 站点设置
    // */
    //public BaseSitSettingRes getSiteSetting() {
    //    return ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.getSetting());
    //}

    /**
     * 获取站点设置值
     *
     * @param key
     * @return
     */
    public String getSettingValueByKey(String key) {
        return ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.querySettingsValueByKey(key));
    }

    /**
     * 查询订单评价超时时间
     */
    public Long getCommentOutTime() {
        String value = getSettingValueByKey(ORDER_COMMENT_OUT_TIME);
        if (StringUtils.isBlank(value) || !NumberUtil.isLong(value)) {
            return DEFAULT_ORDER_COMMENT_OUT_TIME;
        }

        return Long.parseLong(value);
    }

}
