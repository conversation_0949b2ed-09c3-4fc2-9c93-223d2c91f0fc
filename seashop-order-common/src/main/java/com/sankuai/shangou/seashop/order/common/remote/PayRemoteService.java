package com.sankuai.shangou.seashop.order.common.remote;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.enums.TResultCode;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.remote.model.pay.CreatePayBo;
import com.sankuai.shangou.seashop.order.common.remote.model.pay.CreateRefundBo;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.OrderPayQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayPaymentCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayReverseCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaymentConfirmCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ReverseOrderQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayPaymentCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayReverseCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PaymentConfirmCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.ReverseOrderResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.PayCmdFeign;
import com.sankuai.shangou.seashop.pay.thrift.core.service.PayQueryFeign;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PayRemoteService {

    @Resource
    private PayCmdFeign payCmdThriftService;

    @Resource
    private PayQueryFeign payQueryThriftService;

    /**
     * 发起支付，创建支付单，支付服务会实际发起支付，订单服务等待回调
     *
     * @param createPayBo
     * @return
     */
    public PayPaymentCreateResp createPay(CreatePayBo createPayBo) {
        log.info("【支付接口调用】发起支付, 请求参数={}", createPayBo);
        PayPaymentCreateReq payPaymentCreateReq = JsonUtil.copy(createPayBo, PayPaymentCreateReq.class);
        PayPaymentCreateResp resp = ThriftResponseHelper.executeThriftCall(() -> payCmdThriftService.createPayment(payPaymentCreateReq));
        log.info("【支付接口调用】发起支付, 返回参数={}", JsonUtil.toJsonString(resp));
        return resp;
    }

    /**
     * 发起退款，创建退款单，支付服务会实际发起退款，订单服务等待回调
     *
     * @param createRefundBo
     * @return
     */
    public String createRefund(CreateRefundBo createRefundBo) {
        log.info("【支付接口调用】创建退款单, 请求参数={}", createRefundBo);
        PayReverseCreateReq reverseCreateReq = new PayReverseCreateReq();
        reverseCreateReq.setReverseAmount(createRefundBo.getRefundAmount());
        reverseCreateReq.setOrderId(createRefundBo.getOriginPayNo());
        reverseCreateReq.setReverseId(createRefundBo.getRefundNo());
        reverseCreateReq.setBusinessStatusType(createRefundBo.getBusinessStatusType());
        PayReverseCreateResp resp = ThriftResponseHelper.executeThriftCall(() -> payCmdThriftService.createPaymentReverse(reverseCreateReq));
        log.info("【支付接口调用】创建退款单, 返回参数={}", JsonUtil.toJsonString(resp));
        return resp.getChannelRefundId();
    }

    /**
     * 创建支付确认(分账)
     *
     * @param request
     * @return
     */
    public ResultDto<PaymentConfirmCreateResp> createPaymentConfirm(PaymentConfirmCreateReq request) {
        log.info("【支付接口调用】创建支付确认(分账), 请求参数={}", request);
        try {
            ResultDto<PaymentConfirmCreateResp> resp = payCmdThriftService.createPaymentConfirm(request);
            log.info("【支付接口调用】创建支付确认(分账), 返回参数={}", JsonUtil.toJsonString(resp));
            return resp;
        } catch (Exception e) {
            log.error("【支付接口调用】创建支付确认(分账), 参数={}, 异常", JsonUtil.toJsonString(request), e);
            ResultDto resultDto = new ResultDto();
            resultDto.fail(TResultCode.SERVER_ERROR.value(), TResultCode.SERVER_ERROR.desc());
            return resultDto;
        }
    }

    /**
     * 查询退款单
     *
     * @param request
     * @return
     */
    public ReverseOrderResp queryReverseOrderOne(ReverseOrderQueryReq request) {
        log.info("【支付接口调用】查询退款单, 请求参数={}", JsonUtil.toJsonString(request));
        ReverseOrderResp reverseOrderResp = ThriftResponseHelper.executeThriftCall(() -> payQueryThriftService.queryReverseOrderOne(request));
        log.info("【支付接口调用】查询退款单, 返回参数={}", JsonUtil.toJsonString(reverseOrderResp));
        return reverseOrderResp;
    }

    /**
     * 撤销支付（支付退款）
     *
     * @param request
     * @return
     */
    public PayReverseCreateResp createPaymentReverse(PayReverseCreateReq request) {
        log.info("【支付接口调用】撤销支付（支付退款）, 请求参数={}", JsonUtil.toJsonString(request));
        PayReverseCreateResp payReverseCreateResp = ThriftResponseHelper.executeThriftCall(() -> payCmdThriftService.createPaymentReverse(request));
        log.info("【支付接口调用】撤销支付（支付退款）, 返回参数={}", JsonUtil.toJsonString(payReverseCreateResp));
        return payReverseCreateResp;
    }

    /**
     * 创建支付单
     *
     * @param request
     * @return
     */
    public PayPaymentCreateResp createPayment(PayPaymentCreateReq request) {
        log.info("【支付接口调用】创建支付单, 请求参数={}", JsonUtil.toJsonString(request));
        request.setPaymentChannel(PaymentChannelEnum.ADAPAY.getCode());
        PayPaymentCreateResp payPaymentCreateResp = ThriftResponseHelper.executeThriftCall(() -> payCmdThriftService.createPayment(request));
        log.info("【支付接口调用】创建支付单, 返回参数={}", JsonUtil.toJsonString(payPaymentCreateResp));
        return payPaymentCreateResp;
    }

    /**
     * 查询支付单
     *
     * @param batchNo 支付批次号
     */
    public OrderPayResp queryPayResult(String batchNo) {
        OrderPayQueryReq request = new OrderPayQueryReq();
        request.setQueryChannel(Boolean.TRUE);
        // 支付接口的 orderId 是 订单服务的批次ID
        request.setOrderId(batchNo);
        log.info("【支付接口调用】查询退款单, 请求参数={}", JsonUtil.toJsonString(request));
        OrderPayResp payResp = ThriftResponseHelper.executeThriftCall(() -> payQueryThriftService.queryOrderPayOne(request));
        log.info("【支付接口调用】查询退款单, 返回参数={}", JsonUtil.toJsonString(payResp));
        return payResp;
    }

    /**
     * 批量查询支付单
     *
     * @param batchNoList 支付批次号
     */
    public Map<String,OrderPayResp> queryPayResult(List<String> batchNoList) {
        log.info("【支付接口调用】查询退款单, 请求参数={}", JsonUtil.toJsonString(batchNoList));
        Map<String,OrderPayResp> payResp = ThriftResponseHelper.executeThriftCall(() -> payQueryThriftService.queryCompletePay(batchNoList));
        log.info("【支付接口调用】查询退款单, 返回参数={}", JsonUtil.toJsonString(payResp));
        return payResp;
    }

    /**
     * 批量查询支付单
     *
     * @param batchNoList 支付批次号
     */
    public Map<String,OrderPayResp> queryPayResultNoOutTransId(List<String> batchNoList) {
        log.info("【支付接口调用】批量查询无第三方流水号支付结果, 请求参数={}", JsonUtil.toJsonString(batchNoList));
        if (CollUtil.isEmpty(batchNoList)) {
            return Collections.emptyMap();
        }
        Map<String,OrderPayResp> payResp = ThriftResponseHelper.executeThriftCall(() -> payQueryThriftService.queryPayOrderNoOutTransId(batchNoList));
        log.info("【支付接口调用】批量查询无第三方流水号支付结果, 返回参数={}", JsonUtil.toJsonString(payResp));
        if (payResp == null) {
            return Collections.emptyMap();
        }
        return payResp;
    }
}
