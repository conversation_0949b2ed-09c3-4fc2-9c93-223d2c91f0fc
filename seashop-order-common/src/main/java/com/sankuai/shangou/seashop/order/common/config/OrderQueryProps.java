package com.sankuai.shangou.seashop.order.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

/**
 * 订单服务查询相关的参数化配置
 * config 说明文档 <a href=https://km.sankuai.com/page/550965600></a>
 * <AUTHOR>
 */
@Component
@Getter
@Setter
public class OrderQueryProps {

    /**
     * 允许的最大分页数据量，超出数据量提示用户缩小查询范围
     */
    @Value("${seashop.order.query.maxPageTotal:10000}")
    private Integer maxPageTotal;

}
