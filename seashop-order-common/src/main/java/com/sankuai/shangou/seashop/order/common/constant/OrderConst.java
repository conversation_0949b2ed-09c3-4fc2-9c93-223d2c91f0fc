package com.sankuai.shangou.seashop.order.common.constant;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
public class OrderConst {

    // 平台汇付ID
    public static final String PLATFORM_ADAPAY_ID = "0";

    // 保证金扣款描述
    public static final String CASH_DEPOSIT_DEDUCTION_DESC = "，支付单号：";

    // 保证金退款失败描述
    public static final String CASH_DEPOSIT_REFUND_FAIL_DESC = "退款失败，失败原因：";
    // 保证金退款成功描述
    public static final String CASH_DEPOSIT_REFUND_SUCCESS_DESC = "退款成功，支付单号：";

    // 保证金支付商品描述
    public static final String CASH_DEPOSIT_PAY_GOODS_DESC = "缴纳保证金";

    // 保证金支付宝支付统一名称
    public static final String CASH_DEPOSIT_PAY_ALIPAY_NAME = "alipay";

    /**
     * 保证金生成的支付订单的前面长度
     * 1+19 = 20
     * 1 = CASH_DEPOSIT_PAY_ORDER_ID_PREFIX = "B";
     * 19 = 雪华算法的固定长度
     */
    public static final Integer CASH_DEPOSIT_PAY_ORDER_ID_PREFIX_LENGTH = 19;

    /**
     * 日期格式
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * 财务总览页面统计销售数据描述
     */
    public static final String FINANCE_ORDER_STATISTICS_DESC = "%s的销售额: %s元";

    /**
     * 确认收货订单锁前缀
     */
    public static final String CONFIRM_RECEIPT_ORDER_PREFIX = "confirm:receipt:order:";
}
