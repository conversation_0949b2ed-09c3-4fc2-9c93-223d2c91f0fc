package com.sankuai.shangou.seashop.order.common.remote.model.pay;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class CreatePayBo {

    /**
     * 来源订单id
     */
    private String orderId;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;
    /**
     * 支付类型
     */
    private Integer paymentType;
    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 商品标题
     */
    private String goodsTitle;
    /**
     * 商品描述信息，微信小程序和微信公众号该字段最大长度42个字符
     */
    private String goodsDesc;
    /**
     * 交易设备所在的公网 IP
     */
    private String deviceIp;
    /**
     * 支付渠道扩展参数
     */
    private AdaPayCreateExpandBo expend;

    /**
     * 支付渠道:1汇付天下支付 2微信支付 3支付宝支付
     */
    private Integer paymentChannel;

}
