package com.sankuai.shangou.seashop.order.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@ThriftEnum
public enum FinanceResultCodeEnum {

    /**
     * 业务异常定义：xx-xxx-xxx
     * <pre>
     * 一、业务系统区分：020-基础服务；030-交易服务；040-用户服务；050-营销服务；060商品服务；070：订单服务；080：支付服务；
     * 二、前面两位代表错误类型：40-参数校验错误；50-业务逻辑错误；60-系统错误；
     * 三、后面三位代表具体错误码
     * </pre>
     */


    // 保证金明细不存在
    CASH_DEPOSIT_DETAIL_NOT_EXIST(50071001, "保证金明细不存在"),
    CASH_DEPOSIT_BALANCE_NOT_ENOUGH(50071002, "选择的缴纳记录余额不足"),
    // 保证金退款不能存在
    CASH_DEPOSIT_REFUND_NOT_EXIST(50071003, "保证金退款不存在"),
    // 保证金退款状态不正确
    CASH_DEPOSIT_REFUND_STATUS_ERROR(50071004, "保证金退款状态不正确"),
    // 退款金额不足，不能申请退款
    CASH_DEPOSIT_REFUND_AMOUNT_NOT_ENOUGH(50071005, "退款金额不足，不能申请退款"),
    // 扣除金额不能多余店铺可用余额
    CASH_DEPOSIT_DEDUCT_AMOUNT_NOT_ENOUGH(50071006, "扣除金额不能多余店铺可用余额"),

    ;

    private Integer code;
    private String msg;

    FinanceResultCodeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
