package com.sankuai.shangou.seashop.order.common.remote;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/12/16 14:02
 */
@Service
@Slf4j
public class BaseRegionRemoteService {

    //@Resource
    //private RegionQueryThriftService regionQueryThriftService;
    //
    //public Map<String, AllPathRegionResp> getAllPathRegions(RegionIdsReq regionIdsReq){
    //    return ThriftResponseHelper.executeThriftCall(()->regionQueryThriftService.getAllPathRegions(regionIdsReq));
    //}
}
