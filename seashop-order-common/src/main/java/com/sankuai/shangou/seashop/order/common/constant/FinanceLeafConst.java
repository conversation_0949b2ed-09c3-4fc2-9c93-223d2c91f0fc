package com.sankuai.shangou.seashop.order.common.constant;

/**
 * <AUTHOR>
 */
public class FinanceLeafConst {

    /**
     * 保证金扣款雪华算法key
     */
    public static final String CASH_DEPOSIT_DEDUCTION_ORDER_ID = "com.sankuai.sgb2b.seashop.order.cash.deposit.deduction.order.id";

    /**
     * 保证金扣款前缀
     */
    public static final String CASH_DEPOSIT_DEDUCTION_ORDER_ID_PREFIX = "BPR";

    /**
     * 保证金支付雪华算法key
     */
    public static final String CASH_DEPOSIT_PAY_ORDER_ID = "com.sankuai.sgb2b.seashop.order.cash.deposit.pay.order.id";

    /**
     * 保证金扣款前缀
     */
    public static final String CASH_DEPOSIT_PAY_ORDER_ID_PREFIX = "B";

    /**
     * 保证金扣款前缀+雪花算法key的长度（雪花确定长度19位）
     */
    public static final int CASH_DEPOSIT_PAY_ORDER_ID_PREFIX_LENGTH = CASH_DEPOSIT_PAY_ORDER_ID_PREFIX.length() + 18;

}
