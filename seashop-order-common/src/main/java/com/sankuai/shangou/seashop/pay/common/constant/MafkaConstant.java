package com.sankuai.shangou.seashop.pay.common.constant;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description:
 */
public class MafkaConstant {

    /**
     * mafka命名空间，用于区分不同的业务，这里固定为waimai
     */
    public static final String DEFAULT_NAMESPACE = "waimai";

    /**
     * 订单支付状态变更topic
     * topic：seashop_order_pay_status_change_topic
     */
    public static final String ORDER_PAY_STATUS_CHANGE_TOPIC = "seashop_order_pay_status_change_topic";

    /**
     * 订单支付状态变更消费者组
     * group：seashop_order_pay_status_change_consumer
     */
    public static final String ORDER_PAY_STATUS_CHANGE_CONSUMER = "seashop_order_pay_status_change_consumer";

    /**
     * 企业账号申请回调topic
     * topic：seashop_corp_member_audit_topic
     */
    public static final String CORP_MEMBER_AUDIT_TOPIC = "seashop_corp_member_audit_topic";


    /**
     * 企业账号申请回调消费者组
     * group：seashop_corp_member_audit_consumer
     */
    public static final String CORP_MEMBER_AUDIT_CONSUMER = "seashop_corp_member_audit_consumer";

    /**
     * 订单退款(未分账订单退款)通知topic
     * seashop_order_reverse_topic
     */
    public static final String ORDER_REVERSE_TOPIC = "seashop_order_reverse_topic";

    /**
     * 订单退款(未分账订单退款)通知消费者组
     * seashop_order_reverse_consumer
     */
    public static final String ORDER_REVERSE_CONSUMER = "seashop_order_reverse_consumer";

    /**
     * 支付撤销(未分账退款)异常延迟消息 topic
     * seashop_order_reverse_exception_topic
     */
    public static final String ORDER_REVERSE_EXCEPTION_TOPIC = "seashop_order_reverse_exception_topic";

    /**
     * 支付撤销(未分账退款)异常延迟消息 消费者组
     * seashop_order_reverse_exception_consumer
     */
    public static final String ORDER_REVERSE_EXCEPTION_CONSUMER = "seashop_order_reverse_exception_consumer";
}
