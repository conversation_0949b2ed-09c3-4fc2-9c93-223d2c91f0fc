package com.sankuai.shangou.seashop.order.common.config;

import cn.hutool.json.JSONUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 消息通知配置：类型、短信模板等
 * config 说明文档 <a href=https://km.sankuai.com/page/550965600></a>
 * <AUTHOR>
 */
@Component
public class MessageProps {

    /**
     * 消息通知配置,key是枚举的name
     * {"MERCHANT_ORDER_DELIVER":{"messageType":3,"smsTemplateCode":2007583,"emailContentPattern":"尊敬的商家:%s,您的订单%s已发货，请注意查收。%s"},"SUPPLIER_ORDER_DELIVER":{"messageType":10,"smsTemplateCode":2009070,"emailContentPattern":"您的店铺有订单已支付，订单号：%s，请及时发货!%s"},"SUPPLIER_AFTER_SALE":{"messageType":11,"smsTemplateCode":2007565,"emailContentPattern":"您的店铺有订单申请售后，订单号：%s，请及时处理!%s"},"MERCHANT_WAIT_PAY":{"messageType":1,"smsTemplateCode":2007557,"emailContentPattern":"尊敬的商家:%s,您还有订单未成功付款，尽快支付别让好货错过！%s"},"MERCHANT_RETURN_AGREE":{"messageType":8,"smsTemplateCode":2007564,"emailContentPattern":"尊敬的商家:%s,您的退货已审核通过，请及时发货。%s"},"SUPPLIER_DELIVER_TIMEOUT":{"messageType":-1,"smsTemplateCode":2007559,"emailContentPattern":"%s店铺，您好，贵店铺有%s笔订单付款已超过%s小时没发货，请务必保证及时发出。!%s"},"MERCHANT_REFUND_REFUSE":{"messageType":6,"smsTemplateCode":2007562,"emailContentPattern":"尊敬的商家:%s,您的退款申请被拒绝，请知悉。%s"},"MERCHANT_PAY_SUCCESS":{"messageType":2,"smsTemplateCode":2007558,"emailContentPattern":"尊敬的商家:%s,您的订单%s已支付成功，我们会尽快为您发货。%s"},"MERCHANT_APPLY_RETURN":{"messageType":7,"smsTemplateCode":2007563,"emailContentPattern":"尊敬的商家:%s,您的退货申请正在受理中,请至个人中心查看。%s"},"MERCHANT_APPLY_REFUND":{"messageType":4,"smsTemplateCode":2007560,"emailContentPattern":"尊敬的商家:%s,您的退款申请正在受理中,请至个人中心查看。%s"},"MERCHANT_RETURN_REFUSE":{"messageType":9,"smsTemplateCode":2009071,"emailContentPattern":"尊敬的商家:%s,您的退货申请被拒绝，如有疑问请联系卖家。%s"},"MERCHANT_REFUND_SUCCESS":{"messageType":5,"smsTemplateCode":2007561,"emailContentPattern":"尊敬的商家:%s,您的订单已经完成退款，请留意查收。%s"}}
     */
    private Map<String, MessageConfig> messageConfigMap;

    @Value("#{${messageConfigMap}}")
    private Map<String, Map<String,Object>> messageConfig;

    public Map<String, MessageConfig> getMessageConfigMap() {
        return messageConfigMap;
    }

    public void setMessageConfigMap() {
        messageConfig.entrySet().forEach(e -> {
            MessageConfig msg = JSONUtil.toBean(JSONUtil.toJsonStr(e.getValue()), MessageConfig.class);
            this.messageConfigMap.put(e.getKey(), msg);
        });
    }

    public static class MessageConfig {
        private Integer messageType;
        private Integer smsTemplateCode;
        private String emailContentPattern;

        public Integer getMessageType() {
            return messageType;
        }

        public void setMessageType(Integer messageType) {
            this.messageType = messageType;
        }

        public Integer getSmsTemplateCode() {
            return smsTemplateCode;
        }

        public void setSmsTemplateCode(Integer smsTemplateCode) {
            this.smsTemplateCode = smsTemplateCode;
        }

        public String getEmailContentPattern() {
            return emailContentPattern;
        }

        public void setEmailContentPattern(String emailContentPattern) {
            this.emailContentPattern = emailContentPattern;
        }
    }

}
