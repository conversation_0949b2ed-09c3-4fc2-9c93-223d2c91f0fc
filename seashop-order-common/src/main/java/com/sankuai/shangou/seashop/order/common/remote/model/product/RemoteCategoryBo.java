package com.sankuai.shangou.seashop.order.common.remote.model.product;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/11 15:02
 */
@Getter
@Setter
@Builder
public class RemoteCategoryBo {

    /**
     * 类目Id
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 类目图标
     */
    private String icon;

    /**
     * 显示顺序
     */
    private Long displaySequence;

    /**
     * 父类目Id
     */
    private Long parentCategoryId;

    /**
     * 类目深度
     */
    private Integer depth;

    /**
     * 类目的路径（以|分离）
     */
    private String path;

    /**
     * 类目全路径名称，英文逗号分割
     */
    private String fullCategoryName;

    /**
     * 是否有下级
     */
    private Boolean hasChildren;

    /**
     * 保证金
     */
    private BigDecimal cashDeposit;

    /**
     * 允许七天无理由退货
     */
    private Boolean enableNoReasonReturn;

}
