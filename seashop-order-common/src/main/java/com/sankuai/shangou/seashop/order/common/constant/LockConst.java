package com.sankuai.shangou.seashop.order.common.constant;

/**
 * <AUTHOR>
 */
public class LockConst {

    private static final String LOCK_PREFIX = "seashop:order:lock:";

    // 订单ES构建相关的锁
    public static final String LOCK_ES_ORDER_UPDATE = LOCK_PREFIX + "es:order:build";
    public static final String LOCK_ES_ORDER_UPDATE_PATTERN = LOCK_ES_ORDER_UPDATE + ":{0}";
    public static final String SCENE_ES_ORDER_UPDATE = LOCK_PREFIX + "scene:" + "es:order:build";

    // 订单状态变更时使用的锁
    public static final String LOCK_ORDER_STATUS_CHANGE = LOCK_PREFIX + "order:status:change:";
    public static final String SCENE_ORDER_STATUS_CHANGE = LOCK_PREFIX + "scene:" + "order:status:change";

    public static final String LOCK_ORDER_COMMENT = LOCK_PREFIX + "order:comment:";
    public static final String SCENE_ORDER_COMMENT = LOCK_PREFIX + "scene:" + "order:comment";

    public static final String LOCK_ORDER_COMMENT_APPEND = LOCK_PREFIX + "order:comment:append:";
    public static final String SCENE_ORDER_COMMENT_APPEND = LOCK_PREFIX + "scene:" + "order:comment:append";

    // 订单售后ES的构建分两把锁进行部分更新，一把锁是售后基础信息；另一把锁是订单状态的变更
    public static final String LOCK_ES_REFUND_BASE = LOCK_PREFIX + "es:orderRefund:build:base:";
    public static final String SCENE_ES_REFUND_BASE = LOCK_PREFIX + "scene:" + "es:orderRefund:build:base";
    public static final String LOCK_ES_REFUND_ORDER_CHANGE = LOCK_PREFIX + "es:orderRefund:build:orderChange:";
    public static final String SCENE_ES_REFUND_ORDER_CHANGE = LOCK_PREFIX + "scene:" + "es:orderRefund:build:orderChange";

    public static final String LOCK_USER_PAY_ORDER = LOCK_PREFIX + "order:pay:";
    public static final String LOCK_USER_REFUND_ORDER = LOCK_PREFIX + "order:refund:";

    public static final String SCENE_USER_PAY_ORDER = LOCK_PREFIX + "scene:" + "order:pay";
    public static final String SCENE_USER_REFUND_ORDER = LOCK_PREFIX + "scene:" + "order:refund";

    /**
     * 订单退款（超支退款）分布式锁
     */
    public static final String LOCK_ORDER_REFUND_RECORD = LOCK_PREFIX + "order:refund:record:";

    public static final String LOCK_MQ_ERROR_DATA_SAVE_PATTERN = LOCK_PREFIX + "mq:error:save:%s:%s";
    public static final String SCENE_MQ_ERROR_DATA_SAVE = LOCK_PREFIX + "scene:" + "mq:errorData";

    // 商品评论es构建
    public static final String LOCK_ES_PRODUCT_COMMENT_PATTERN = LOCK_PREFIX + "es:product:audit:build:{0}";

    public static final String SCENE_ES_PRODUCT_COMMENT = LOCK_PREFIX + "scene:" + "es:product:audit:build";

    // 支付回调Lock
    public static final String LOCK_PAY_CALLBACK = LOCK_PREFIX + "pay:callback:";

    // 退款回调Lock
    public static final String LOCK_REFUND_CALLBACK = LOCK_PREFIX + "refund:callback:";

    // 同步支付配置Lock
    public static final String PAY_CONFIG_LOCK_PREFIX = LOCK_PREFIX + "pay:config:";
}
