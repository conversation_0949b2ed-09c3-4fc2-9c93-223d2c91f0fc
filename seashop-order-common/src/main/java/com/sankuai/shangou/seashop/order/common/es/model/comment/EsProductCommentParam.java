package com.sankuai.shangou.seashop.order.common.es.model.comment;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/04/19 16:59
 */
@Getter
@Setter
public class EsProductCommentParam {

    /**
     * 店铺id 平台查询时不传
     */
    private Long shopId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 评分 1-5分
     */
    private Integer reviewMark;

    /**
     * 是否追加了评论
     */
    private boolean hasAppend;

    /**
     * 回复状态 0-全部 1-未处理
     */
    private Integer replyStatus;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 是否过滤隐藏的评论(商城端需要设置为true)
     */
    private boolean filterHidden;

    /**
     * 是否有图片
     */
    private boolean hasImage;

    /**
     * 评分范围
     */
    private List<Integer> reviewMarkRange;

    /**
     * 规格ID
     */
    private String skuId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商家账号
     */
    private String userName;

    /**
     * 商家手机号
     */
    private String userMobile;

}
