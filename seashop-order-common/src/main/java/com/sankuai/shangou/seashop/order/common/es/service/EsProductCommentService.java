package com.sankuai.shangou.seashop.order.common.es.service;

import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.order.common.config.EsIndexProps;
import com.sankuai.shangou.seashop.order.common.constant.EsConst;
import com.sankuai.shangou.seashop.order.common.es.model.comment.EsProductCommentModel;
import com.sankuai.shangou.seashop.order.common.es.model.comment.EsProductCommentParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/26 14:08
 */
@Service
@Slf4j
public class EsProductCommentService extends AbsEsService<EsProductCommentParam, EsProductCommentModel> {

    private static Integer UN_HANDLE_STATUS = 1;

    @Resource
    private EsIndexProps esIndexProps;

    @Override
    public String getLogPrefix() {
        return "商品评价";
    }

    @Override
    public String getIndexName() {
        return esIndexProps.getIdxProductComment();
    }

    @Override
    public BoolQueryBuilder buildProductSearchCondition(EsProductCommentParam queryBo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (queryBo.getProductId() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("productId", queryBo.getProductId()));
        }
        if (StringUtils.isNotEmpty(queryBo.getSkuId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("skuId", queryBo.getSkuId()));
        }
        if (StringUtils.isNotEmpty(queryBo.getOrderId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("orderId", queryBo.getOrderId()));
        }
        if (StringUtils.isNotEmpty(queryBo.getUserName())) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("userName", "*" + queryBo.getUserName() + "*"));
        }
        if (StringUtils.isNotEmpty(queryBo.getUserMobile())) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("userMobile", "*" + queryBo.getUserMobile() + "*"));
        }
        if (queryBo.getShopId() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("shopId", queryBo.getShopId()));
        }
        if (StringUtils.isNotEmpty(queryBo.getProductName())) {
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("productName", "*" + queryBo.getProductName()));
        }
        if (queryBo.getReviewMark() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("reviewMark", queryBo.getReviewMark()));
        }
        if (queryBo.isHasAppend()) {
            boolQueryBuilder.must(QueryBuilders.existsQuery("appendDate"));
        }

        if (UN_HANDLE_STATUS.equals(queryBo.getReplyStatus())) {
            boolQueryBuilder.must(
                    QueryBuilders.boolQuery()
                            .should(QueryBuilders.boolQuery()
                                    .mustNot(QueryBuilders.existsQuery("replyDate"))
                            .should(QueryBuilders.boolQuery()
                                    .must(QueryBuilders.existsQuery("appendDate"))
                                    .mustNot(QueryBuilders.existsQuery("replyAppendDate"))))
            );
        }

        if (queryBo.isFilterHidden()) {
            boolQueryBuilder.must(QueryBuilders.termQuery("hasHidden", false));
        }

        if (CollectionUtils.isNotEmpty(queryBo.getReviewMarkRange())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("reviewMark", queryBo.getReviewMarkRange()));
        }
        if (queryBo.isHasImage()) {
            boolQueryBuilder.must(
                    QueryBuilders.boolQuery()
                            .should(QueryBuilders.matchQuery("hasImage", true))
                            .should(QueryBuilders.matchQuery("appendHasImage", true))
            );
        }
        return boolQueryBuilder;
    }

    @Override
    public List<FieldSortReq> defaultSortList() {
        FieldSortReq sort = new FieldSortReq();
        sort.setSort(EsConst.ProductComment.SEARCH_FIELD_PRODUCT_ID);
        sort.setIzAsc(Boolean.FALSE);
        return Arrays.asList(sort);
    }
}
