package com.sankuai.shangou.seashop.order.common.constant;

/**
 * <AUTHOR>
 */
public class ThreadPoolConst {

    // 多线程异步调用接口线程池
    public static final String RHINO_KEY_THREAD_POOL_ASYNC_API = "seashop.order.threadPool.asyncApi";

    // 订单和售后ES构建线程池
    public static final String RHINO_KEY_THREAD_POOL_ES_BUILD_ORDER = "seashop.order.threadPool.esBuild.order";
    public static final String RHINO_KEY_THREAD_POOL_ES_BUILD_REFUND = "seashop.order.threadPool.esBuild.refund";

}
