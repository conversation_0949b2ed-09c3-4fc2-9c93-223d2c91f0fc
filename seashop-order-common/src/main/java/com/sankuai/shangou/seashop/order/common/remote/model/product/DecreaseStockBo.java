package com.sankuai.shangou.seashop.order.common.remote.model.product;

import lombok.Getter;
import lombok.Setter;

/**
 * 扣减库存的参数对象
 * <AUTHOR>
 */
@Getter
@Setter
public class DecreaseStockBo {

    private String orderId;
    private Long shopId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 扣减数量
     */
    private Long quantity;
}
