package com.sankuai.shangou.seashop.pay.common.constant;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description: 汇付天下支付常量
 */
public class AdaPayConstant {

    /**
     * 汇付天下中出入参的一些命名
     */
    public static final String ID = "id";
    public static final String STATUS = "status";

    public static final String OUT_TRANS_ID = "out_trans_id";

    public static final String ORDER_NO = "order_no";

    public static final String REFUNDED_AMT = "reverse_amt";

    public static final String PAYMENTS = "payments";

    public static final String END_TIME = "end_time";

    public static final String PAY_AMT = "pay_amt";

    public static final String TIME_FORMAT = "yyyyMMddHHmmss";

    /**
     * 错误码
     */
    public static final String ERROR_CODE = "error_code";
    /**
     * 错误消息
     */
    public static final String ERROR_MSG = "error_msg";

    /**
     * leaf申请的汇付创建企业账号的订单号key
     */
    public static final String LEAF_ADAPAY_ORDER_NO_KEY = "com.sankuai.sgb2b.seashop.pay.adapay.order.no";

    /**
     * leaf申请的汇付创建支付时传入的payId的key
     */
    public static final String LEAF_ADAPAY_PAY_ID_KEY = "com.sankuai.sgb2b.seashop.pay.adapay.pay.id";

    /**
     * 传给汇付订单好的前缀
     */
    public static final String PAY_ID_PREFIX = "PO";

    /**
     * 默认使用的分账模式：延迟分账
     */
    public static final String PAY_MODE = "delay";


    public static final String PAY_CREATE_RESULT_KEY_ID = "id";
    public static final String PAY_CREATE_RESULT_KEY_EXPEND = "expend";

    /**
     * 单笔支付单最大退款次数
     */
    public static final int MAX_REFUND_TIMES = 50;
    public static final String MEMBER_ID = "member_id";
}
