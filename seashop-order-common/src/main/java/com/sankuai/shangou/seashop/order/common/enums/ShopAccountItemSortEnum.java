package com.sankuai.shangou.seashop.order.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@ThriftEnum
public enum ShopAccountItemSortEnum {

    SETTLEMENT_TIME(1, "settlementTime", "结算时间"),

    SETTLEMENT_AMOUNT(2, "amount", "结算金额"),

    ;

    private Integer code;
    private String sort;
    private String name;

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getSort() {
        return sort;
    }

    public String getName() {
        return name;
    }

    ShopAccountItemSortEnum(Integer code, String sort, String name) {
        this.code = code;
        this.sort = sort;
        this.name = name;
    }
}
