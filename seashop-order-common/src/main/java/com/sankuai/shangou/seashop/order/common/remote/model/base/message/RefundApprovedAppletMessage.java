package com.sankuai.shangou.seashop.order.common.remote.model.base.message;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RefundApprovedAppletMessage extends BaseAppletMessage {

    /**
     * 退款原因(20个以内字符,可汉字、数字、字母或符号组合)
     */
    private String thing6;

    /**
     * 退款时间(例如：2019年10月1日，或：2019年10月1日 15:01)
     */
    private String date4;

    /**
     * 支付金额(可带小数，结尾带元)
     */
    private String amount5;

}
