package com.sankuai.shangou.seashop.order.common.constant;

/**
 * @author: lhx
 * @date: 2023/12/21/021
 * @description:
 */
public class FinanceConstant {

    /**
     * 分账时，特殊处理的异常
     * （调用分账时，以下两个异常码，默认是正常处理）
     */
    public static final String ORDER_NO_USED = "order_no_used";
    public static final String CONFIRM_AMT_OVER_LIMIT = "confirm_amt_over_limit";

    /**
     * 错误码
     */
    public static final String ERROR_CODE = "error_code";
    /**
     * 错误消息
     */
    public static final String ERROR_MSG = "error_msg";

    /**
     * 结算成功描述
     */
    public static final String SETTLEMENT_SUCCESS_DESC = "successed";

    /**
     * 结算失败描述
     */
    public static final String SETTLEMENT_FAIL_DESC = "错误码：%s,分账失败：%s";

    /**
     * 平台ID
     */
    public static final Long PLATFORM_ID = 0L;

    /**
     * 系统定时任务结算
     */
    public static final String SYSTEM_SETTLEMENT = "系统定时任务结算";

    /**
     * 给定的一个默认的最小时间
     */
    public static final String DEFAULT_MIN_TIME_STR = "2025-07-20 00:00:00";

}
