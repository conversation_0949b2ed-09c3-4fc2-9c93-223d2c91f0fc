package com.sankuai.shangou.seashop.order.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description: 保证金退款排序枚举
 */
@ThriftEnum
public enum CashDepositRefundSortEnum {

    APPLY_DATE(1, "applyDate", "申请时间"),
    ;

    private Integer code;
    private String sort;
    private String name;

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    public String getSort() {
        return sort;
    }

    public String getName() {
        return name;
    }

    CashDepositRefundSortEnum(Integer code, String sort, String name) {
        this.code = code;
        this.sort = sort;
        this.name = name;
    }
}
