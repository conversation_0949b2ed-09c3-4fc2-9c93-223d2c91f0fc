package com.sankuai.shangou.seashop.order.common.enums;

/**
 * 订单评价状态
 * <AUTHOR>
 */
public enum OrderCommentStatusEnum {

    /**
     * 不可评价
     */
    NOT_COMMENTABLE(-1, "不可评价"),
    /**
     * 未评价
     */
    NOT_COMMENT(0, "未评价"),
    /**
     * 已评价
     */
    COMMENTED(1, "已评价"),
    /**
     * 已追评
     */
    ADDITIONAL_COMMENT(2, "已追评");

    private final Integer code;
    private final String desc;

    OrderCommentStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

}
