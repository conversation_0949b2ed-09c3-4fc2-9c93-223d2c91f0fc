package com.sankuai.shangou.seashop.order.common.constant;

/**
 * <AUTHOR>
 */
public class CacheConst {

    private static final String CACHE_PREFIX = "seashop:order:cache:";

    public static final String KEY_ORDER_CREATE_IDEMPOTENCE = CACHE_PREFIX + "order:create:token:";

    // 创建订单时幂等的有效期，单位是秒
    public static final int EXPIRE_ORDER_CREATE_IDEMPOTENCE = 30 * 60;

    // 结算配置缓存时间，单位是秒
    public static final int EXPIRE_SETTLE_CONFIG_CACHE = 60 * 60 * 24;

    public static final int HOUR_EXPIRE = 60;

    /**
     * 支付配置缓存
     */
    public static final String PAY_CONFIG_CACHE_PREFIX = CACHE_PREFIX + "pay:config:";
}
