package com.sankuai.shangou.seashop.order.common.enums;

/**
 * 平台消息模板枚举，主要是短信、邮件的
 * <AUTHOR>
 */
public enum PlatformMessageTemplateEnum {

    // 商家-待付款提醒
    MERCHANT_WAIT_PAY(1, "商家-待付款提醒", "尊敬的商家:%s,您还有订单未成功付款，尽快支付别让好货错过！", 2007557),
    // 商家-订单支付成功通知
    MERCHANT_PAY_SUCCESS(2, "商家-订单支付成功通知", "尊敬的商家:%s,您的订单%s已支付成功，我们会尽快为您发货。", 2007558),
    // 商家-订单发货提醒
    MERCHANT_ORDER_DELIVER(3, "商家-订单发货提醒", "尊敬的商家:%s,您的订单%s已发货，请注意查收。", 2007583),
    // 商家-申请退款
    MERCHANT_APPLY_REFUND(4, "商家-申请退款", "尊敬的商家:%s,您的退款申请正在受理中,请至个人中心查看。", 2007560),
    // 商家-退款成功
    MERCHANT_REFUND_SUCCESS(5, "商家-退款成功", "尊敬的商家:%s,您的订单已经完成退款，请留意查收。", 2007561),
    // 商家-供应商拒绝退款（供应商拒绝+平台驳回）
    MERCHANT_REFUND_REFUSE(6, "商家-供应商拒绝退款", "尊敬的商家:%s,您的退款申请被拒绝，请知悉。", 2007562),
    // 商家-申请退货
    MERCHANT_APPLY_RETURN(7, "商家-申请退货", "尊敬的商家:%s,您的退货申请正在受理中,请至个人中心查看。", 2007563),
    // 商家-供应商同意退货
    MERCHANT_RETURN_AGREE(8, "商家-供应商同意退货", "尊敬的商家:%s,您的退货已审核通过，请及时发货。", 2007564),
    // 商家-供应商拒绝退货（供应商拒绝+平台驳回）
    MERCHANT_RETURN_REFUSE(9, "商家-供应商拒绝退货", "尊敬的商家:%s,您的退货申请被拒绝，如有疑问请联系卖家。", 2009071),
    // 供应商-发货提醒
    SUPPLIER_ORDER_DELIVER(10, "供应商-发货提醒", "您的店铺有订单已支付，订单号：%s，请及时发货!", 2009070),
    // 供应商-售后处理提醒
    SUPPLIER_AFTER_SALE(11, "供应商-售后处理提醒", "您的店铺有订单申请售后，订单号：%s，请及时处理!", 2007565),
    // 供应商-超时未发货提醒，未在平台配置里，是在交易设置里面
    SUPPLIER_DELIVER_TIMEOUT(-1, "供应商-超时未发货提醒", "%s店铺，您好，贵店铺有%s笔订单付款已超过%s小时没发货，请务必保证及时发出!", 2007559),

    ;

    private final Integer code;
    private final String desc;
    private final String msgPattern;
    private final Integer smsTplCode;

    PlatformMessageTemplateEnum(Integer code, String desc, String msgPattern, Integer smsTplCode) {
        this.code = code;
        this.desc = desc;
        this.msgPattern = msgPattern;
        this.smsTplCode = smsTplCode;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getMsgPattern() {
        return msgPattern;
    }

    public Integer getSmsTplCode() {
        return smsTplCode;
    }
}
