package com.sankuai.shangou.seashop.order.common.enums;

/**
 * 订单投诉处理状态枚举
 * <AUTHOR>
 */
public enum OrderComplaintStatusEnum {

    WAIT_SUPPLIER_DEAL(1, "等待供应商处理"),
    SUPPLIER_COMPLETED(2, "供应商处理完成"),
    WAIT_PLATFORM_DEAL(3, "等待平台介入"),
    PLATFORM_COMPLETED(4, "已结束"),
    CANCELED(5, "已取消"),
    ;

    private final Integer code;
    private final String desc;

    OrderComplaintStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
