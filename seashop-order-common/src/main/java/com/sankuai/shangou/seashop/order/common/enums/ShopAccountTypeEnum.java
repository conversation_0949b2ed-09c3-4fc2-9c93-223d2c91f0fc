package com.sankuai.shangou.seashop.order.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@ThriftEnum
public enum ShopAccountTypeEnum {

    /**
     * /// <summary>
     * /// 结算入帐
     * /// </summary>
     * [Description("结算入帐")]
     * SettlementIncome = 1,
     * /// <summary>
     * /// 退款
     * /// </summary>
     * [Description("退款")]
     * Refund = 2,
     * /// <summary>
     * /// 平台佣金退还
     * /// </summary>
     * [Description("平台佣金退还")]
     * PlatCommissionRefund = 3,
     * /// <summary>
     * /// 分销佣金退还
     * /// </summary>
     * [Description("分销佣金退还")]
     * DistributorCommissionRefund = 4,
     * /// <summary>
     * /// 营销服务费
     * /// </summary>
     * [Description("营销服务费")]
     * MarketingServices = 5,
     * /// <summary>
     * /// 提现
     * /// </summary>
     * [Description("提现")]
     * WithDraw = 6,
     * /// <summary>
     * /// 充值
     * /// </summary>
     * [Description("充值")]
     * Recharge = 7,
     * /// <summary>
     * /// 续费当前套餐
     * /// </summary>
     * [Description("续费当前套餐")]
     * Renew = 8,
     * /// <summary>
     * /// 升级套餐
     * /// </summary>
     * [Description("升级套餐")]
     * Upgrade = 9,
     * /// <summary>
     * /// 保证金充值
     * /// </summary>
     * [Description("保证金充值")]
     * CashDeposit=10,
     */

    SETTLEMENT_INCOME(1, "结算入帐"),
    REFUND(2, "退款"),
    PLAT_COMMISSION_REFUND(3, "平台佣金退还"),
    DISTRIBUTOR_COMMISSION_REFUND(4, "分销佣金退还"),
    MARKETING_SERVICES(5, "营销服务费"),
    WITH_DRAW(6, "提现"),
    RECHARGE(7, "充值"),
    RENEW(8, "续费当前套餐"),
    UPGRADE(9, "升级套餐"),
    CASH_DEPOSIT(10, "保证金充值"),

    ;

    private Integer type;
    private String name;

    @ThriftEnumValue
    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    ShopAccountTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
}
