package com.sankuai.shangou.seashop.pay.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description 结合Hutool工具包，实现bean对象转map（支持驼峰转下划线）;
 *              主要是处理对象套对象时，子对象的key不会转换的问题
 * @Date 2023/3/2 19:19
 */
public class BeanUtilMap {

    public static Map<String, Object> beanMap(Object bean, boolean isUnderscore) {
        Map<String, Object> map = null;
        if (isUnderscore) {
            String s = JSONUtil.toJsonStr(bean);
            bean = JSONUtil.parseObj(s);
            try {
                map = formatKey((JSONObject) bean, false);
            } catch (Exception e) {
                throw new RuntimeException("bean转map失败");
            }
        }else {
            map = cn.hutool.core.bean.BeanUtil.beanToMap(bean, false, false);
        }
        return map;
    }


    private static JSONObject formatKey(final JSONObject json, boolean upper) {
        JSONObject real = new JSONObject();
        for (String it : json.keySet()) {
            Object objR = json.get(it);
            // 转换为驼峰格式
            String key = StrUtil.toUnderlineCase(it);
            // 首字母大写或者小写
            key = upper ? StrUtil.upperFirst(key) : StrUtil.lowerFirst(key);
            if (objR instanceof JSONObject) {
                real.set(key, formatKey((JSONObject) objR, upper));
            }else if (objR instanceof JSONArray) {
                JSONArray jsonA = new JSONArray();
                for (Object objA : (JSONArray) objR) {
                    jsonA.add(formatKey((JSONObject) objA, upper));
                }
                real.set(key, jsonA);
            }else {
                real.set(key, objR);
            }
        }
        return real;
    }
}
