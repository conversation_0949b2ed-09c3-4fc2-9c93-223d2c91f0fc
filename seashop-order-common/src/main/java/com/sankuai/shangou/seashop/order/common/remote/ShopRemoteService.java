package com.sankuai.shangou.seashop.order.common.remote;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;

import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.user.thrift.account.MemberContactQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberContactReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberContactRespList;
import com.sankuai.shangou.seashop.user.thrift.shop.BusinessCategoryQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopCmdFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopEsQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopEsQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.*;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.ShopEsCombinationResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.ShopEsResp;

import lombok.extern.slf4j.Slf4j;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/2/002
 * @description:
 */
@Service
@Slf4j
public class ShopRemoteService {

    @Resource
    private ShopQueryFeign shopQueryFeign;
    @Resource
    private ShopCmdFeign shopCmdFeign;
    @Resource
    private BusinessCategoryQueryFeign businessCategoryQueryFeign;
    @Resource
    private ShopEsQueryFeign shopEsQueryFeign;
    @Resource
    private MemberContactQueryFeign memberContactQueryFeign;

    public MemberContactRespList queryMemberContact(QueryMemberContactReq memberContactReq) {
        return ThriftResponseHelper.executeThriftCall(() -> memberContactQueryFeign.queryMemberContact(memberContactReq));
    }

    public ShopDetailResp queryDetail(Long shopId) {
        BaseIdReq baseIdReq = new BaseIdReq();
        baseIdReq.setId(shopId);
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.queryDetail(baseIdReq));
    }

    public ShopSimpleListResp querySimpleList(ShopSimpleQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.querySimpleList(request));
    }

    public List<BusinessCategoryResp> getCategoryByShopIdList(List<Long> shopIdList) {
        BaseBatchIdReq req = new BaseBatchIdReq();
        req.setId(shopIdList);
        log.info("【远程调用-店铺】获取店铺经营类目，shopIdList:{}", JsonUtil.toJsonString(shopIdList));
        BusinessCategoryRespList respList = ThriftResponseHelper.executeThriftCall(() -> businessCategoryQueryFeign.queryList(req));
        log.info("【远程调用-店铺】获取店铺经营类目结果，respList:{}", JsonUtil.toJsonString(respList));
        return respList.getRespList();
    }

    public ShopRespList getShopList(QueryShopPageReq queryShopPageReq) {
        ShopRespList shopRespList = new ShopRespList();
        List<ShopResp> shopList = new ArrayList<>(200);

        queryShopPageReq.setPageNo(CommonConst.DEFAULT_PAGE);
        queryShopPageReq.setPageSize(CommonConst.MAX_LIST_SIZE);
        List<Long> shopIds = queryShopPageReq.getShopIds();
        List<List<Long>> shopIdSplit = CollUtil.split(shopIds, CommonConst.MAX_LIST_SIZE);
        for (List<Long> shopIdList : shopIdSplit) {
            queryShopPageReq.setShopIds(shopIdList);
            ShopRespList shopResp = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.getShopList(queryShopPageReq));
            if (shopResp != null && CollUtil.isNotEmpty(shopResp.getShopRespList())) {
                shopList.addAll(shopResp.getShopRespList());
            }
        }

        shopRespList.setShopRespList(shopList);
        return shopRespList;
    }

    public Map<Long, ShopResp> getShopMap(List<Long> shopIdList) {
        log.info("【远程调用-店铺】获取店铺信息，shopIdList:{}", JsonUtil.toJsonString(shopIdList));
        QueryShopPageReq req = new QueryShopPageReq();
        req.setShopIds(shopIdList);
        ShopRespList listResp = getShopList(req);
        log.debug("【远程调用-店铺】获取店铺信息结果，listResp:{}", JsonUtil.toJsonString(listResp));
        if (listResp == null || CollUtil.isEmpty(listResp.getShopRespList())) {
            return MapUtil.empty();
        }
        return listResp.getShopRespList().stream()
                .collect(Collectors.toMap(ShopResp::getId, v -> v, (oldV, newV) -> newV));
    }

    public Long getYesterdayShopUV(Long shopId) {
        log.info("【远程调用-店铺】获取店铺昨日UV，shopId:{}", shopId);
        Long yesterdayShopUV = ThriftResponseHelper.executeThriftCall(() -> shopQueryFeign.getYesterdayShopUV(shopId));
        log.debug("【远程调用-店铺】获取店铺昨日UV结果，yesterdayShopUV:{}", yesterdayShopUV);
        return yesterdayShopUV;
    }

    public List<Long> searchShopIdByNameFromEs(String shopName) {
        ShopEsQueryReq req = new ShopEsQueryReq();
        req.setShopName(shopName);
        req.setPageNo(CommonConst.DEFAULT_PAGE);
        req.setPageSize(CommonConst.MAX_LIST_SIZE);
        ShopEsCombinationResp resp = ThriftResponseHelper.executeThriftCall(() -> shopEsQueryFeign.queryByShopEs(req));
        if (resp == null || resp.getShopList() == null || CollUtil.isEmpty(resp.getShopList().getData())) {
            return new ArrayList<>();
        }
        List<ShopEsResp> shopList = resp.getShopList().getData();
        return shopList.stream().map(ShopEsResp::getShopId).distinct().collect(Collectors.toList());
    }

    public void checkShopArrears(Long shopId) {
        log.info("【远程调用-店铺】校验店铺是否欠费，shopId:{}", shopId);
        try {
            ThriftResponseHelper.executeThriftCall(() -> shopCmdFeign.checkShopArrears(new BaseIdReq() {{
                setId(shopId);
            }}));
        }catch (Exception e){
            log.error("【远程调用-店铺】校验店铺是否欠费异常，shopId:{}", shopId, e);
        }
    }
}
