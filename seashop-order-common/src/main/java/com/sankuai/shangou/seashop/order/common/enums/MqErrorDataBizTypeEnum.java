package com.sankuai.shangou.seashop.order.common.enums;

/**
 * <AUTHOR>
 */
public enum MqErrorDataBizTypeEnum {

    ORDER_CHANGE(1, "订单状态变更"),
    PAY_NOTIFY(2, "支付回调"),
    REFUND_CHANGE(3, "售后状态变更"),
    REFUND_NOTIFY(4, "售后回调"),

    ;

    private final Integer code;
    private final String desc;
    MqErrorDataBizTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
