package com.sankuai.shangou.seashop.order.common.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import lombok.Getter;
import lombok.Setter;

/**
 * 定时任务相关配置
 * config 说明文档 <a href=https://km.sankuai.com/page/550965600></a>
 * <AUTHOR>
 */
@Component
@Getter
@Setter
public class TaskProps {

    /**
     * 订单不允许支付后N小时，订单自动关闭
     */
    @Value("${seashop.order.task.orderCloseAfterPayForbiddenHour:0}")
    private Integer orderCloseAfterPayForbiddenHour;
    /**
     * 订单关闭前N分钟，发送通知
     */
    @Value("${seashop.order.task.orderCloseNoticeMinute:0}")
    private Integer orderCloseNoticeMinute;
    /**
     * ES构建，时间为当前时间之前的分钟数
     * 比如当前时间为 9:00, esBuildEndBeforeMinute=10，esBuildGapMinute=20,
     * 则数据范围为：8:30-8:50
     */
    @Value("${seashop.order.task.esBuildEndBeforeMinute:10}")
    private Integer esBuildEndBeforeMinute;
    /**
     * ES构建，查询数据的间隔分钟数，这个值也是定时任务的执行间隔，否则会重复执行
     */
    @Value("${seashop.order.task.esBuildGapMinute:10}")
    private Integer esBuildGapMinute;

}
