package com.sankuai.shangou.seashop.order.common.enums;

import com.facebook.swift.codec.ThriftEnum;
import com.facebook.swift.codec.ThriftEnumValue;

/**
 * <AUTHOR>
 */
@ThriftEnum
public enum FeeFlagEnum {

    // 是否手续费承担方
    YES("Y", "是", 1),
    NO("N", "否", 2);


    private String flag;
    private String desc;
    private Integer code;

    public String getFlag() {
        return flag;
    }

    public String getDesc() {
        return desc;
    }

    @ThriftEnumValue
    public Integer getCode() {
        return code;
    }

    FeeFlagEnum(String flag, String desc, Integer code) {
        this.flag = flag;
        this.desc = desc;
        this.code = code;
    }
}
