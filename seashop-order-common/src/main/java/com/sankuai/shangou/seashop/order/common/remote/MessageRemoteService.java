package com.sankuai.shangou.seashop.order.common.remote;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MessageCMDFeign;
import com.sankuai.shangou.seashop.base.thrift.core.MsgTemplateCmdFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.order.common.enums.AppletMessageEventEnum;
import com.sankuai.shangou.seashop.order.common.remote.model.base.message.BaseAppletMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 消息发送远程接口
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageRemoteService {

    @Resource
    private MessageCMDFeign messageCMDThriftService;
    @Resource
    private MsgTemplateCmdFeign msgTemplateCmdThriftService;

    /**
     * 发送短信
     * <AUTHOR>
     * @param templateCode 短信模板ID
	 * @param param 短信模板的key-value参数对
	 * @param mobile 手机号码
	 * @param requestId 请求ID
     */
    public void sendSms(Integer templateCode, String param, String mobile, Long requestId) {
        // 发送短信
        SmsBodyReq smsBodyReq = new SmsBodyReq();
        smsBodyReq.setTemplateId(templateCode.longValue());
        smsBodyReq.setParam(param);
        smsBodyReq.setRequestId(requestId);
        // 设置收信人
        ContactReq contactReq = new ContactReq();
        contactReq.setMobile(mobile);
        smsBodyReq.setContactList(Collections.singletonList(contactReq));
        log.info("【消息】发送短信, 请求参数={}", JsonUtil.toJsonString(smsBodyReq));
        Boolean result = ThriftResponseHelper.executeThriftCall(() -> messageCMDThriftService.sendSms(smsBodyReq));
        log.info("【消息】发送短信, 返回结果={}", result);
    }

    public void sendEmail(String subject, String body, String email, Long requestId) {
        if (StrUtil.isBlank(email)) {
            return;
        }
        EmailBodyReq emailBodyReq = new EmailBodyReq();
        emailBodyReq.setBody(body);
        emailBodyReq.setSendFrom("test?");
        emailBodyReq.setSubject(subject);
        emailBodyReq.setRequestId(requestId);
        // 设置收信人
        ContactReq contactReq = new ContactReq();
        contactReq.setEmail(email);
        emailBodyReq.setContactList(Collections.singletonList(contactReq));
        log.info("【消息】发送邮件, 请求参数={}", JsonUtil.toJsonString(emailBodyReq));
        Boolean result = ThriftResponseHelper.executeThriftCall(() -> messageCMDThriftService.sendEmail(emailBodyReq));
        log.info("【消息】发送邮件, 返回结果={}", result);
    }

    /**
     * 发送小程序消息
     * @param userId 用户ID
     * @param messageEvent 消息事件
     * @param appletMessage 消息内容
     */
    public void sendAppletMessage(Long userId, AppletMessageEventEnum messageEvent, BaseAppletMessage appletMessage) {
        SendAppletReq sendAppletReq = new SendAppletReq();
        sendAppletReq.setUserId(userId);
        sendAppletReq.setTemplateNum(messageEvent.getCode());
        if (AppletMessageEventEnum.MEMBER_REFUND_APPROVED.equals(messageEvent)) {
            sendAppletReq.setSendRefundNoticeReq(JsonUtil.copy(appletMessage, SendRefundNoticeReq.class));
        } else if (AppletMessageEventEnum.MEMBER_ORDER_DELIVERED.equals(messageEvent)) {
            sendAppletReq.setSendOrderShippingNoticeReq(JsonUtil.copy(appletMessage, SendOrderShippingNoticeReq.class));
        }
        log.info("【消息】发送小程序消息, 请求参数={}", JsonUtil.toJsonString(sendAppletReq));
        ThriftResponseHelper.executeThriftCall(() -> msgTemplateCmdThriftService.sendAppletMessage(sendAppletReq));
        log.info("【消息】发送小程序消息, 结束");
    }

}
