package com.sankuai.shangou.seashop.order.common.remote;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.common.BatchQueryHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.remote.model.product.RemoteSkuBo;
import com.sankuai.shangou.seashop.product.thrift.core.ProductCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.ProductQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.SkuQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.AddSaleCountReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.QueryProductBasicReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.AddProductSaleCountDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.sku.SkuQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.ProductBasicResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.sku.SkuQueryResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/3 15:11
 */
@Service
@Slf4j
public class ProductRemoteService {

    @Resource
    private ProductQueryFeign productQueryFeign;
    @Resource
    private SkuQueryFeign skuQueryFeign;
    @Resource
    private ProductCmdFeign productCmdFeign;

    //public ProductSkuMergeQueryResp queryProductSkuMerge(ProductSkuQueryReq request) {
    //    return ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductSkuMerge(request));
    //}

    public ProductBasicDto queryProductById(Long productId) {
        QueryProductBasicReq request = new QueryProductBasicReq();
        request.setProductIds(Collections.singletonList(productId));
        ProductBasicResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductBasic(request));
        if (resp == null || CollUtil.isEmpty(resp.getProductList())) {
            return null;
        }
        return resp.getProductList().get(0);
    }

    /**
     * 查询商品基本信息
     *
     * @param productIds 商品id列表
     * @return 商品基本信息
     */
    public List<ProductBasicDto> queryProductList(List<Long> productIds) {
        QueryProductBasicReq request = new QueryProductBasicReq();
        request.setProductIds(productIds);
        log.info("【商品】查询商品基本信息, 请求参数={}", JsonUtil.toJsonString(request));
        return BatchQueryHelper.batchQuery(request, QueryProductBasicReq::setProductIds, QueryProductBasicReq::getProductIds, param -> {
            log.info("【商品】查询商品基本信息, 批次查询请求参数={}", JsonUtil.toJsonString(param));
            ProductBasicResp resp = ThriftResponseHelper.executeThriftCall(() -> productQueryFeign.queryProductBasic(param));
            log.info("【商品】查询商品基本信息, 批次查询响应结果={}", JsonUtil.toJsonString(resp));
            if (resp == null || CollUtil.isEmpty(resp.getProductList())) {
                return Collections.emptyList();
            }
            return resp.getProductList();
        });
    }

    public List<RemoteSkuBo> getSkuList(List<String> skuIdList) {
        SkuQueryReq req = new SkuQueryReq();
        req.setSkuIdList(skuIdList);
        log.info("【商品】查询sku列表, 请求参数={}", JsonUtil.toJsonString(req));
        List<SkuQueryResp> skuList = BatchQueryHelper.batchQuery(req, SkuQueryReq::setSkuIdList, SkuQueryReq::getSkuIdList, param -> {
            log.info("【商品】查询sku列表, 批次查询请求参数={}", JsonUtil.toJsonString(param));
            SkuListResp resp = ThriftResponseHelper.executeThriftCall(() -> skuQueryFeign.querySkuList(param));
            log.info("【商品】查询sku列表, 批次查询响应结果={}", JsonUtil.toJsonString(resp));
            if (resp == null || CollUtil.isEmpty(resp.getSkuList())) {
                return Collections.emptyList();
            }
            return resp.getSkuList();
        });
        return JsonUtil.copyList(skuList, RemoteSkuBo.class);
    }

    /**
     * 增加商品销量
     *
     * @param productList 商品销量列表
     */
    public void addProductSaleCount(List<AddProductSaleCountDto> productList) {
        AddSaleCountReq req = new AddSaleCountReq();
        req.setProductList(productList);
        log.debug("【商品】增加商品销量, 请求参数={}", JsonUtil.toJsonString(productList));
        ThriftResponseHelper.executeThriftCall(() -> productCmdFeign.addSales(req));
    }

}
