package com.sankuai.shangou.seashop.order.common.config;

import com.sankuai.shangou.seashop.order.common.constant.EsConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * ES索引配置，配置的是索引名称，默认值为生产正式的名称
 * config 说明文档 <a href=https://km.sankuai.com/page/550965600></a>
 *
 * <AUTHOR>
 */
@Component
@Slf4j
public class EsIndexProps {

    /**
     * 商品索引，如果为空，取常量的值，常量设置的生产环境的索引名称
     */
    @Value("${spring.profiles.active}")
    private String idxOrder;
    @Value("${spring.profiles.active}")
    private String idxRefund;
    @Value("${spring.profiles.active}")
    private String idxOrderItem;
    @Value("${spring.profiles.active}")
    private String idxProductComment;

    public String getIdxOrder() {
        String idx = idxOrder + "." + EsConst.INDEX_ORDER;
        // 去除索引_local前缀 使用线上es环境
        idx = idx.replace("_local", "");
        log.info("order索引：{}", idx);
        return idx;
    }

    public void setIdxOrder(String idxOrder) {
        this.idxOrder = idxOrder;
    }

    public String getIdxRefund() {
        String idx = idxRefund + "." + EsConst.INDEX_REFUND;
        // 去除索引_local前缀 使用线上es环境
        idx = idx.replace("_local", "");
        log.info("refund索引：{}", idx);
        return idx;
    }

    public void setIdxRefund(String idxRefund) {
        this.idxRefund = idxRefund;
    }

    public String getIdxOrderItem() {
        String idx = idxOrderItem + "." + EsConst.INDEX_ORDER_ITEM;
        // 去除索引_local前缀 使用线上es环境
        idx = idx.replace("_local", "");
        log.info("orderItem索引：{}", idx);
        return idx;
    }

    public void setIdxOrderItem(String idxOrderItem) {
        this.idxOrderItem = idxOrderItem;
    }

    public String getIdxProductComment() {
        String idx = idxProductComment + "." + EsConst.ProductComment.INDEX_PRODUCT_COMMENT;
        // 去除索引_local前缀 使用线上es环境
        idx = idx.replace("_local", "");
        log.info("productComment索引：{}", idx);
        return idx;
    }

    public void setIdxProductComment(String idxProductComment) {
        this.idxProductComment = idxProductComment;
    }
}
