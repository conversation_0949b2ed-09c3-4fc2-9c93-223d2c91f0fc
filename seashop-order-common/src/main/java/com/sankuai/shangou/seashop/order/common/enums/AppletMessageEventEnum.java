package com.sankuai.shangou.seashop.order.common.enums;

/**
 * 小程序消息发送来源事件枚举。需要与 base 服务的WxTemplatesEnum.TemplateNumEnum等价
 * <p>
 * <AUTHOR>
 */
public enum AppletMessageEventEnum {

    MEMBER_REFUND_APPROVED(30805, "用户-退款通知"),
    MEMBER_ORDER_DELIVERED(30766, "用户-订单发货通知"),

    ;

    private final Integer code;
    private final String desc;

    AppletMessageEventEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
