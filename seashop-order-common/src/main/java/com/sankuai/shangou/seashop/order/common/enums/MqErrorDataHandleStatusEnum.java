package com.sankuai.shangou.seashop.order.common.enums;

/**
 * <AUTHOR>
 */
public enum MqErrorDataHandleStatusEnum {

    UNDER_HANDLE(1, "待处理"),
    HANDLING(2, "处理中"),
    HANDLED(3, "已处理"),
    CANCELED(4, "已取消"),
    EXCEPTION(5, "处理异常"),

    ;

    private final Integer code;
    private final String desc;
    MqErrorDataHandleStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
