package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "交易商品品牌对象")
@ToString
@Data
public class TradeProductDto extends BaseThriftDto {

    @Schema(description = "商品编码")
    private String productCode;
    @Schema(description = "商品ID")
    private String productId;
    @Schema(description = "商品名称")
    private String productName;
    @Schema(description = "销量")
    private Long saleCount;
    @Schema(description = "店铺ID")
    private Long shopId;
    @Schema(description = "商品所有图片地址，多个图片地址用逗号分隔")
    private String allImagePath;
    @Schema(description = "品牌ID")
    private Long brandId;
    @Schema(description = "分类ID")
    private Long categoryId;
    @Schema(description = "分类全路径")
    private String categoryPath;
    @Schema(description = "市场价")
    private BigDecimal marketPrice;
    @Schema(description = "销售价")
    private BigDecimal salePrice;
    @Schema(description = "预估价")
    private BigDecimal estimatePrice;
    @Schema(description = "商品主图")
    private String mainImagePath;
    @Schema(description = "上架时间")
    private Long onsaleTime;
    @Schema(description = "店铺名称")
    private String shopName;
    /**
     * 是否单规格商品
     */
    @Schema(description = "是否单规格商品")
    private Boolean whetherSingleSku;
    /**
     * skuId
     */
    @Schema(description = "skuId")
    private String skuId;
    /**
     * 倍数起购量
     */
    @Schema(description = "倍数起购量")
    private Integer multipleCount;
    /**
     * 起购量，根据倍数和阶梯价数量计算得到的最小购买数量
     */
    @Schema(description = "最小购买基数")
    private Integer minBuyCount;
    /**
     * 购物车中商品维度数量
     */
    @Schema(description = "购物车中商品维度数量")
    private Long cartProductCount;

    @Schema(description = "总销量")
    private Long totalSaleCounts;

    @Schema(description = "浏览量")
    private Integer visitCounts;

    /**
     * 是否专享价商品
     */
    @Schema(description = "是否专享价商品")
    private Boolean whetherExclusive;
    /**
     * 单规格商品库存
     */
    @Schema(description = "单规格商品库存")
    private Long singleSkuStock;
    /**
     * 是否缺货（售罄）
     */
    @Schema(description = "是否缺货（售罄）")
    private Boolean whetherOutOfStock;
    /**
     * 原售价，不考虑任何营销前的，sku的最小售价
     */
    @Schema(description = "sku的最小售价")
    private BigDecimal minSalePrice;

    /**
     * 收藏数
     */
    @Schema(description = "收藏数")
    private Integer favoriteCount;
    /**
     * 是否限时购
     */
    @Schema(description = "是否限时购")
    private Boolean whetherFlashSale;
    /**
     * 评论数
     */
    @Schema(description = "评论数")
    private Integer evaluateNum;
    /**
     * 是否显示价格XX起
     */
    @Schema(description = "是否显示价格XX起")
    private Boolean showPriceStartAt;

    @Schema(description = "OE号")
    private String oeCode;

    @Schema(description = "品牌号")
    private String brandCode;
    @Schema(description = "品牌名称")
    private String brandName;

    public String getMarketPriceString() {
        return this.bigDecimal2String(this.marketPrice);
    }
    public String getSalePriceString() {
        return this.bigDecimal2String(this.salePrice);
    }
    public String getEstimatePriceString() {
        return this.bigDecimal2String(this.estimatePrice);
    }

}
