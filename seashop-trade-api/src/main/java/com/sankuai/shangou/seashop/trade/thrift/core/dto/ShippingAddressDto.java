package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "用户收货地址对象")
@ToString
@Data
public class ShippingAddressDto extends BaseThriftDto {

    @Schema(description = "主键ID")
    private Long id;
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "区域ID")
    private Integer regionId;
    @Schema(description = "收货人")
    private String shipTo;
    @Schema(description = "收货地址")
    private String address;
    @Schema(description = "详细地址")
    private String addressDetail;
    @Schema(description = "收货人电话")
    private String phone;
    @Schema(description = "是否为默认")
    private Boolean whetherDefault;
    @Schema(description = "收货地址坐标")
    private BigDecimal receiveLongitude;
    @Schema(description = "收货地址坐标")
    private BigDecimal receiveLatitude;

    @Schema(description = "省份名称")
    private String provinceName;
    @Schema(description = "城市名称")
    private String cityName;
    @Schema(description = "区县名称")
    private String districtName;
    @Schema(description = "街道名称")
    private String streetName;
    @Schema(description = "省市区街道全称")
    private String regionFullName;


}
