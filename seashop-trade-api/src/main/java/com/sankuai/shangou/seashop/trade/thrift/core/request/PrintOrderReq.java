package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 15:17
 */
@Data
@Schema(description = "打印订单入参")
public class PrintOrderReq extends BaseThriftDto {

    @Schema(description = "订单id集合")
    private List<String> orderIdList;


    public void checkParameter() {
        AssertUtil.throwIfNull(orderIdList, "订单id集合不能为空");
        AssertUtil.throwIfTrue(orderIdList.size() > 200, "订单id集合不能超过200个");
    }
}
