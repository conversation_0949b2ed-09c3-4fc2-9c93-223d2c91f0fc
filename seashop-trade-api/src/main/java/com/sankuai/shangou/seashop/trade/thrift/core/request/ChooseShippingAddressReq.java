package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.PreviewOrderSummaryDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.UserDto;
import lombok.Data;

import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "选择收货地址入参")
public class ChooseShippingAddressReq extends BaseParamReq {

    @Schema(description = "用户收货地址，可能为空", required = true)
    private Long shippingAddressId;
    @Schema(description = "按店铺分组的商品列表", required = true)
    private List<ShoppingCartShopProductDto> shopProductList;
    @Schema(description = "预览订单页所有商品总金额", required = true)
    private BigDecimal totalAmount;
    @Schema(description = "用户ID", required = true)
    private UserDto user;
    @Schema(description = "预览订单相关汇总信息", required = true)
    private PreviewOrderSummaryDto previewOrderSummaryDto;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.shippingAddressId == null || this.shippingAddressId <= 0) {
            throw new InvalidParamException("shippingAddressId不能为空");
        }
        if (this.shopProductList == null || this.shopProductList.isEmpty()) {
            throw new InvalidParamException("shopProductList不能为空");
        }
        /*if (this.totalAmount == null || this.totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new InvalidParamException("totalAmount不能为空");
        }*/
    }
}
