package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "预览订单请求入参")
public class SearchProductInShopReq extends BasePageReq {

    /**
     * 用户信息
     */
    @Schema(description = "用户信息")
    private UserDto user;
    /**
     * 店铺ID
     */
    @Schema(description = "店铺ID")
    private Long shopId;
    /**
     * 搜索关键字，目前是针对商品名称
     */
    @Schema(description = "搜索关键字，目前是针对商品名称")
    private String searchKey;
    /**
     * 金额区间开始，指商品原售价
     */
    @Schema(description = "金额区间开始，指商品原售价")
    private BigDecimal minPrice;
    /**
     * 金额区间结束，指商品原售价
     */
    @Schema(description = "金额区间结束，指商品原售价")
    private BigDecimal maxPrice;

    /**
     * 店铺分类id
     */
    @Schema(description = "店铺分类ID")
    private Long shopCategoryId;


}
