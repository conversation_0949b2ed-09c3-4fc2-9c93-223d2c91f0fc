package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "凑单汇总描述")
@ToString
@Data
public class AddonSummaryDto extends BaseThriftDto {

    /**
     * 凑单tab对应已勾选商品sku数，从购物车进入，勾选的商品如果是活动中的，就会计入
     */
    @Schema(description = "凑单tab对应已勾选商品sku数，从购物车进入，勾选的商品如果是活动中的，就会计入")
    private Integer selectedInActiveCount;
    /**
     * 凑单tab对应已勾选商品的实际金额，如果不满足活动，则是原总额；满足活动就是扣减玩活动后的
     */
    @Schema(description = "凑单tab对应已勾选商品的实际金额，如果不满足活动，则是原总额；满足活动就是扣减玩活动后的")
    private BigDecimal selectedProductAmount;
    /**
     * 截止时间描述
     */
    @Schema(description = "截止时间描述")
    private String deadlineDesc;
    /**
     * 活动描述
     */
    @Schema(description = "活动描述")
    private String activityDesc;
    /**
     * 满足的营销描述
     */
    @Schema(description = "满足的营销描述", example = "已购满X元，已减X元，再凑X元可减X元")
    private String matchPromotionDesc;

    /**
     * 截止时间时间戳
     */
    @Schema(description = "截止时间时间戳")
    private Long deadline;


}
