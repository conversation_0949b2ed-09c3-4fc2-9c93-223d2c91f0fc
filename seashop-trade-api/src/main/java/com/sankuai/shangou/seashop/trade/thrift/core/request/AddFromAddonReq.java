package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * 凑单页面加购入参
 *
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "凑单页面加购入参")
public class AddFromAddonReq extends BaseParamReq {

    @Schema(description = "商家用户ID", required = true)
    private Long userId;
    @Schema(description = "商品ID", required = true)
    private Long productId;
    @Schema(description = "skuId", required = true)
    private String skuId;
    @Schema(description = "加购数量。默认1", required = true)
    private Long quantity;
    /**
     * 凑单类型。1：折扣；2：满减
     */
    @Schema(description = "凑单类型。1：折扣；2：满减", required = true)
    private Integer type;
    /**
     * 活动ID
     */
    @Schema(description = "活动ID", required = true)
    private Long activityId;
    @Schema(description = "店铺信息", required = true)
    private ShoppingCartShopDto shop;
    @Schema(description = "商品列表，前端传入是保持用户页面跳转看到的数据一致。需要传入商品的productId, realSalePrice, quantity", required = true)
    private List<ShoppingCartProductDto> productList;

    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (userId == null || userId <= 0) {
            throw new InvalidParamException("userId is null");
        }
        if (productId == null || productId <= 0) {
            throw new InvalidParamException("productId is null");
        }
        if (skuId == null || skuId.isEmpty()) {
            throw new InvalidParamException("skuId is null");
        }
        if (quantity == null || quantity <= 0) {
            throw new InvalidParamException("quantity is null");
        }
        if (type == null || type <= 0) {
            throw new InvalidParamException("type is null");
        }
        if (activityId == null || activityId <= 0) {
            throw new InvalidParamException("activityId is null");
        }
    }


}
