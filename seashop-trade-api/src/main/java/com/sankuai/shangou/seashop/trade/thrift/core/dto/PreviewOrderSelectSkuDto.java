package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "预览订单请求入参")
public class PreviewOrderSelectSkuDto extends BaseParamReq {

    @Schema(description = "购物车主键ID", required = true)
    private Long id;
    @Schema(description = "商品ID", required = true)
    private Long productId;
    @Schema(description = "skuId", required = true)
    private String skuId;
    @Schema(description = "购买数量。参数是为了防止同一个账号多端操作看到的数量不一致", required = true)
    private Long quantity;
    @Schema(description = "实际售价。蚕食是为了防止后台调整后看到的价格前后不一致", required = true)
    private BigDecimal realSalePrice;


}
