package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/12 11:32
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询猜你喜欢入参")
public class QueryGuessYouLikeReq extends BaseParamReq {

    @Schema(description = "商品id")
    private Long productId;
    @Schema(description = "用户id")
    private Long userId;


}
