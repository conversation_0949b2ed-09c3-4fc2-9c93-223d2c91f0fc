package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/11 14:04
 */
@Data
@Schema(description = "商品id列表")
public class ProductIdsReq extends BaseThriftDto {

    @Schema(description = "商品id列表")
    private List<String> productIds;


    public void checkParameter() {
        if (productIds == null || productIds.isEmpty()) {
            throw new IllegalArgumentException("商品id列表不能为空");
        }
        if (productIds.size() > 200) {
            throw new IllegalArgumentException("商品id列表不能超过200个");
        }
    }
}
