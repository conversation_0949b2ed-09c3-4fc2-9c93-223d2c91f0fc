package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "凑单对应的营销描述")
@ToString
@Data
public class AddonDescDto {

    /**
     * 凑单类型。1：折扣；2：满减
     */
    @Schema(description = "凑单类型。1：折扣；2：满减")
    private Integer type;
    /**
     * 活动类型描述
     */
    @Schema(description = "活动类型描述")
    private String typeDesc;
    /**
     * 凑单对应的营销描述
     */
    @Schema(description = "凑单对应的营销描述")
    private String promotionDesc;


}
