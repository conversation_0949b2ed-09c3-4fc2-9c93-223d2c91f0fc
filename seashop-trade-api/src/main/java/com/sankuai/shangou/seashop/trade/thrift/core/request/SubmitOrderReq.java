package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "提交订单请求入参")
public class SubmitOrderReq extends BaseParamReq {

    @Schema(description = "用户信息，网关从上下文获取", required = true)
    private UserDto user;
    @Schema(description = "页面防重复提交token", required = true)
    private String submitToken;
    @Schema(description = "收货地址ID")
    private Long shippingAddressId;
    @Schema(description = "收货地址ID")
    private ShippingAddressDto shippingAddress;
    @Schema(description = "所有店铺总金额", required = true)
    private BigDecimal totalAmount;
    @Schema(description = "按店铺分组的商品列表", required = true)
    private List<ShoppingCartShopProductDto> shopProductList;
    @Schema(description = "限时购活动id，预览接口返回的回传")
    private Long flashSaleId;
    @Schema(description = "组合购活动id，预览接口返回的回传")
    private Long collocationId;
    /**
     * 订单平台，0：PC；2：小程序
     */
    @Schema(description = "订单平台，0：PC；2：小程序")
    private Integer platform;
    @Schema(description = "是否立即购买")
    private Boolean whetherBuyNow;


    @Override
    public void checkParameter() {
        if (this.user == null) {
            throw new InvalidParamException("用户信息不能为空");
        }
        this.user.checkParameter();
        if (shippingAddress == null) {
            throw new InvalidParamException("shippingAddress不能为空");
        }
        if (this.totalAmount == null || this.totalAmount.compareTo(BigDecimal.ZERO) < 0) {
            throw new InvalidParamException("totalAmount不能为空");
        }
        if (this.shopProductList == null || this.shopProductList.isEmpty()) {
            throw new InvalidParamException("shopProductList不能为空");
        }
        if (this.submitToken == null || this.submitToken.isEmpty()) {
            throw new InvalidParamException("submitToken不能为空");
        }
        if (this.flashSaleId != null && this.collocationId != null) {
            throw new InvalidParamException("限时购和组合购不能同时存在");
        }
    }


}
