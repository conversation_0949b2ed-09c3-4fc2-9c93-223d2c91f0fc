package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 勾选购物车sku入参
 *
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "勾选购物车sku入参")
@NoArgsConstructor
public class SelectShoppingCartSkuReq extends BaseParamReq {

    @Schema(description = "商家会员ID，用于校验是否操作的是自己的数据", required = true)
    private Long userId;
    @Schema(description = "购物车主键ID", required = true)
    private Long id;
    @Schema(description = "是否选中", required = true)
    private Boolean whetherSelect;
    @Schema(description = "店铺信息", required = true)
    private ShoppingCartShopDto shop;
    @Schema(description = "商品列表", required = true)
    private List<ShoppingCartProductDto> productList;

    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.id == null || this.id <= 0) {
            throw new InvalidParamException("请选择勾选的商品");
        }
        if (this.whetherSelect == null) {
            throw new InvalidParamException("whetherSelect不能为空");
        }
        if (this.shop == null) {
            throw new InvalidParamException("shop不能为空");
        }
        if (this.productList == null || this.productList.isEmpty()) {
            throw new InvalidParamException("productList不能为空");
        }
    }


}
