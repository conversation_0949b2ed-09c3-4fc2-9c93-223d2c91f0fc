package com.sankuai.shangou.seashop.trade.thrift.core.request.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "保证金退款查询条件对象")
public class ApiCashDepositRefundQueryReq extends BasePageReq {

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "店铺名称")
    private String shopName;

    /**
     * TO_AUDIT(0, "待审核"),
     * PASS(1, "通过"),
     * REFUSE(2, "拒绝"),
     * REFUNDING(3, "退款处理中"),
     * FAIL(4, "退款失败");
     */
    @Schema(description = "退款状态:0待审核,1通过,2拒绝,3退款处理中,4退款失败")
    private Integer status;


}
