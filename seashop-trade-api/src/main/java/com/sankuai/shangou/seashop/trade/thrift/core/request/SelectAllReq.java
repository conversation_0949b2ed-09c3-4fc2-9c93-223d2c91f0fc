package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopProductDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "全选购物车入参")
public class SelectAllReq extends BaseParamReq {

    @Schema(description = "商家会员ID，用于校验是否操作的是自己的数据", required = true)
    private Long userId;
    @Schema(description = "是否选中", required = true)
    private Boolean whetherSelect;
    @Schema(description = "购物车数据", required = true)
    private List<ShoppingCartShopProductDto> shopProductList;

    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.whetherSelect == null) {
            throw new InvalidParamException("whetherSelect不能为空");
        }
        if (CollUtil.isEmpty(shopProductList)) {
            throw new InvalidParamException("购物车数据不能为空");
        }
    }


}
