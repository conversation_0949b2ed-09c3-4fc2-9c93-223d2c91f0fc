package com.sankuai.shangou.seashop.product.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.CreateDefaultShopBrandReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 店铺品牌操作服务
 *
 * <AUTHOR>
 * @date 2023/11/13 11:05
 */
@FeignClient(name = "himall-trade", contextId = "ShopBrandCmdFeign", path = "/himall-trade/shopBrand", url = "${himall-trade.dev.url:}")
public interface ShopBrandCmdFeign {

    /**
     * 为新创建的店铺创建默认品牌关联
     */
    @PostMapping(value = "/createDefaultShopBrand", consumes = "application/json")
    ResultDto<BaseResp> createDefaultShopBrand(@RequestBody CreateDefaultShopBrandReq request) throws TException;

}
