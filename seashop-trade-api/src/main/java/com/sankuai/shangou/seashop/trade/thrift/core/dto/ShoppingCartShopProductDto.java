package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "购物车商品返回对象")
@ToString
@Data
@Getter
@Setter
public class ShoppingCartShopProductDto {

    @Schema(description = "店铺信息", required = true)
    private ShoppingCartShopDto shop;
    @Schema(description = "商品列表", required = true)
    private List<ShoppingCartProductDto> productList;
    @Schema(description = "附加信息")
    private OrderAdditionalDto additional;
    @Schema(description = "有效的优惠券信息")
    private List<UserCouponRecordDto> validCouponList;


}

