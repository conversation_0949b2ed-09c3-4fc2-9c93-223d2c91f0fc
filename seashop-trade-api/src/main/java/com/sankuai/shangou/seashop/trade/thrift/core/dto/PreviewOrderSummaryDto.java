package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "预览订单相关汇总信息")
@ToString
@Data
public class PreviewOrderSummaryDto extends BaseThriftDto {

    @Schema(description = "预览订单页总的应付金额")
    private BigDecimal totalPayAmount;
    @Schema(description = "预览订单页总的商品金额")
    private BigDecimal totalProductAmount;
    @Schema(description = "预览订单页总的商品数量")
    private Long totalSkuQuantity;
    @Schema(description = "预览订单页总的折扣金额")
    private BigDecimal totalDiscountAmount;
    @Schema(description = "预览订单页总的优惠券金额")
    private BigDecimal totalCouponAmount;
    @Schema(description = "预览订单页总的运费金额")
    private BigDecimal totalFreightAmount;
    @Schema(description = "预览订单页总的税费金额")
    private BigDecimal totalTaxAmount;
    @Schema(description = "预览订单页总的满减金额")
    private BigDecimal totalReductionAmount;


}
