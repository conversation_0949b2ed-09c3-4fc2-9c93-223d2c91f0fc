package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Schema(description = "店铺发票配置")
@ToString
@Data
public class ShopInvoiceConfigDto extends BaseThriftDto {

    /**
     * 是否提供发票
     */
    @Schema(description = "是否提供发票")
    private Boolean whetherInvoice;
    /**
     * 是否提供普通发票
     */
    @Schema(description = "是否提供普通发票")
    private Boolean whetherPlainInvoice;
    /**
     * 是否提供电子发票
     */
    @Schema(description = "是否提供电子发票")
    private Boolean whetherElectronicInvoice;
    /**
     * 是否提供增值税发票
     */
    @Schema(description = "是否提供增值税发票")
    private Boolean whetherVatInvoice;
    /**
     * 普通发票税率
     */
    @Schema(description = "普通发票税率")
    private BigDecimal plainInvoiceRate;
    /**
     * 增值税税率
     */
    @Schema(description = "增值税税率")
    private BigDecimal vatInvoiceRate;
    /**
     * 订单完成后多少天开具增值税发票
     */
    @Schema(description = "订单完成后多少天开具增值税发票")
    private Integer vatInvoiceDay;
    /**
     * 售后维权期天数
     */
    @Schema(description = "售后维权期天数")
    private Integer warrantyDays;


}
