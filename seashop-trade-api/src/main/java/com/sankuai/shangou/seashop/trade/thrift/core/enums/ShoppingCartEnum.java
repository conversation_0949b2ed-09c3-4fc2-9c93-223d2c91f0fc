package com.sankuai.shangou.seashop.trade.thrift.core.enums;

/**
 * <AUTHOR>
 */
public class ShoppingCartEnum {

    public enum SkuStatus {
        ON_SALE("销售中"),
        OFF_SALE("已下架"),
        INVALID("已失效"),
        SOLD_OUT("已售罄"),
        ;

        private final String desc;

        SkuStatus(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum OperateResult {

        LACK_OF_STOCK(4001001, "库存不足"),
        OVER_MAX_LIMIT(4001002, "超过最大限购数量"),
        NOT_MATCH_MULTIPLE(4001003, "不满足起批量"),
        LESS_THAN_MIN_LIMIT(4001004, "低于最小起批量"),
        ;

        private final Integer code;
        private final String desc;

        OperateResult(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

}
