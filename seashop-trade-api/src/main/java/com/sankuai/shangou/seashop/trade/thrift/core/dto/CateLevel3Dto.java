package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "第三级类目，thrift不支持嵌套对象，所以分开定义")
@ToString
@Data
public class CateLevel3Dto {

    @Schema(description = "类目id")
    private Long id;
    @Schema(description = "类目名称")
    private String name;
    @Schema(description = "类目路径")
    private String path;


}
