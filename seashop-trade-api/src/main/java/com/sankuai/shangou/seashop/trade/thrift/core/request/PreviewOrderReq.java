package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.PreviewOrderSelectSkuDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "预览订单请求入参")
@AllArgsConstructor
@NoArgsConstructor
public class PreviewOrderReq extends BaseParamReq {

    @Schema(description = "商家用户ID", required = true)
    private Long userId;
    @Schema(description = "选择提交订单的购物车sku", required = true)
    private List<PreviewOrderSelectSkuDto> selectedSkuList;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.selectedSkuList == null || this.selectedSkuList.isEmpty()) {
            throw new InvalidParamException("selectedSkuList不能为空");
        }
    }


}
