package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/12/25 14:14
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "查询商品详情入参")
public class QueryProductDetailReq extends BaseParamReq {
    @Schema(description = "商品Id", required = true)
    private Long productId;

    @Schema(description = "用户id")
    private Long userId;

    @Schema(description = "组合购id(传了该id 表示从组合购进去详情)")
    private Long collocationId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(productId == null || productId <= 0, "商品id不能为空");
    }


}
