package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2024/01/04 18:47
 */
@Data
@ToString
@Schema(description = "计算运费入参")
public class CalculateFreightReq extends BaseParamReq {

    @Schema(description = "规格id", required = true)
    private String skuId;

    @Schema(description = "数量", required = true)
    private Long quantity;

    @Schema(description = "金额", required = true)
    private BigDecimal salePrice;

    @Schema(description = "地址全路径", required = true)
    private String regionPath;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isEmpty(skuId), "规格id不能为空");
        AssertUtil.throwInvalidParamIfTrue(quantity == null || quantity <= 0, "数量不能为空");
        AssertUtil.throwInvalidParamIfTrue(salePrice == null || salePrice.compareTo(BigDecimal.ZERO) <= 0, "金额不能为空");
        AssertUtil.throwInvalidParamIfTrue(StringUtils.isEmpty(regionPath), "地址全路径不能为空");
        AssertUtil.throwInvalidParamIfTrue(regionPath.split(StrUtil.COMMA).length != 4, "地址全路径格式不正确");
    }


}
