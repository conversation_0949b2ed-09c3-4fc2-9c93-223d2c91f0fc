package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "立即购买请求入参")
@AllArgsConstructor
@NoArgsConstructor
public class PreviewBuyNowReq extends BaseParamReq {

    @Schema(description = "商家用户ID", required = true)
    private Long userId;
    @Schema(description = "商品ID", required = true)
    private Long productId;
    @Schema(description = "skuId", required = true)
    private String skuId;
    @Schema(description = "购买数量", required = true)
    private Long quantity;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.productId == null || this.productId <= 0) {
            throw new InvalidParamException("productId不能为空");
        }
        if (this.skuId == null || this.skuId.isEmpty()) {
            throw new InvalidParamException("skuId不能为空");
        }
        if (this.quantity == null || this.quantity <= 0) {
            throw new InvalidParamException("quantity不能为空");
        }
    }


}
