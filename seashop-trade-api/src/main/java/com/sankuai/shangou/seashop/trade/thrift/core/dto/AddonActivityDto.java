package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 凑单页面对应的活动对象
 *
 * <AUTHOR>
 */
@Schema(description = "凑单页面对应的活动对象")
@ToString
@Data
public class AddonActivityDto extends BaseThriftDto {

    /**
     * 活动类型。1：折扣；2：满减
     */
    @Schema(description = "活动类型。1：折扣；2：满减")
    private Integer type;
    /**
     * 活动类型描述
     */
    @Schema(description = "活动类型描述")
    private String typeDesc;
    /**
     * 活动ID
     */
    @Schema(description = "活动ID")
    private Long activityId;
    /**
     * 活动名称
     */
    @Schema(description = "活动名称")
    private String activityName;

    /**
     * 活动结束时间
     */
    @Schema(description = "活动结束时间")
    private Date endTime;


}
