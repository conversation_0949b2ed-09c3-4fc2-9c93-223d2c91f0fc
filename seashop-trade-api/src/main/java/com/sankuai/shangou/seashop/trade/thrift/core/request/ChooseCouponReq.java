package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.OrderAdditionalDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "选择优惠券入参")
public class ChooseCouponReq extends BaseParamReq {

    @Schema(description = "商家会员ID", required = true)
    private Long userId;
    @Schema(description = "店铺信息", required = true)
    private ShoppingCartShopDto shop;
    @Schema(description = "商品列表", required = true)
    private List<ShoppingCartProductDto> productList;
    @Schema(description = "订单附加信息。优惠券、运费、发票等")
    private OrderAdditionalDto additional;
    @Schema(description = "优惠券ID，这里是用户领用的记录ID，不是优惠券活动ID。如果为0，代表是去掉优惠券选择", required = true)
    private Long couponId;

    @Override
    public void checkParameter() {
        if (userId == null) {
            throw new InvalidParamException("userId不能为空");
        }
        if (shop == null) {
            throw new InvalidParamException("shop不能为空");
        }
        if (CollUtil.isEmpty(productList)) {
            throw new InvalidParamException("productList不能为空");
        }
        if (couponId == null) {
            throw new InvalidParamException("请选择优惠券");
        }
    }


}
