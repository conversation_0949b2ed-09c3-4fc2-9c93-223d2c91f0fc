package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "搜索交易商品请求入参")
@ToString
@Data
public class SearchTradeProductReq extends BasePageReq {

    @Schema(description = "搜索关键字，目前是针对商品名称")
    private String searchKey;
    @Schema(description = "类目ID")
    private Long categoryId;
    @Schema(description = "类目全路径")
    private String categoryPath;
    @Schema(description = "属性值Id")
    private Long attributeId;
    @Schema(description = "属性值")
    private String attributeValue;
    @Schema(description = "品牌ID")
    private Long brandId;
    @Schema(description = "用户ID", required = true)
    private Long userId;
    @Schema(description = "商品条码,sku维度")
    private String barcode;
    @Schema(description = "店铺ID")
    private Long shopId;
    @Schema(description = "商品ID集合")
    private List<String> productIds;
    @Schema(description = "选择的属性ID集合")
    private List<Long> attributeIdList;
    @Schema(description = "选择的属性值集合")
    private List<String> attributeValueList;
    @Schema(description = "优惠券ID")
    private Long couponId;

    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.getPageNo() == null || this.getPageNo() <= 0) {
            throw new InvalidParamException("pageNo不能为空");
        }
        if (this.getPageSize() == null || this.getPageSize() <= 0) {
            throw new InvalidParamException("pageSize不能为空");
        }
    }


}
