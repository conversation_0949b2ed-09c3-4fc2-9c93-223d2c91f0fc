package com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

/**
 * 创建默认店铺分类请求
 *
 * <AUTHOR>
 * @date 2023/11/13 11:06
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "创建默认店铺分类请求")
public class CreateDefaultShopCategoriesReq extends BaseParamReq {

    @Schema(description = "店铺ID", required = true)
    @PrimaryField(title = "店铺ID")
    @ExaminField(description = "店铺ID")
    private Long shopId;

    @Override
    public void checkParameter() {
        if (shopId == null || shopId <= 0) {
            throw new IllegalArgumentException("店铺ID不能为空");
        }
    }
}
