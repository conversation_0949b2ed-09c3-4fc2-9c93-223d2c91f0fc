package com.sankuai.shangou.seashop.trade.thrift.core.request.finance;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@Schema(description = "保证金明细查询条件对象")
public class ApiCashDepositDetailQueryReq extends BasePageReq {

    @Schema(description = "保证金ID", required = true)
    private Long cashDepositId;

    @Schema(description = "操作人")
    private String operator;

    @Schema(description = "开始时间")
    private Date startTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Override
    public void checkParameter() {
        if (null == this.cashDepositId) {
            throw new InvalidParamException("cashDepositId不能为空");
        }
    }


}
