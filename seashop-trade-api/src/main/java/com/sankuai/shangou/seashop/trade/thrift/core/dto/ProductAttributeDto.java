package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "交易商品品牌对象")
@ToString
@Data
public class ProductAttributeDto extends BaseThriftDto {

    @Schema(description = "属性ID")
    private Long attributeId;
    @Schema(description = "属性名称")
    private String attributeName;
    @Schema(description = "属性值")
    private List<String> attributeValues;


}
