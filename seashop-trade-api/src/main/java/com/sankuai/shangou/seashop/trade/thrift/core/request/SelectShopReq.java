package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "勾选购物车店铺入参")
public class SelectShopReq extends BaseParamReq {

    @Schema(description = "商家会员ID，用于校验是否操作的是自己的数据", required = true)
    private Long userId;
    @Schema(description = "是否选中", required = true)
    private Boolean whetherSelect;
    @Schema(description = "店铺信息", required = true)
    private ShoppingCartShopDto shop;
    @Schema(description = "商品列表", required = true)
    private List<ShoppingCartProductDto> productList;

    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.whetherSelect == null) {
            throw new InvalidParamException("whetherSelect不能为空");
        }
        if (this.shop == null) {
            throw new InvalidParamException("shop不能为空");
        }
        if (CollUtil.isEmpty(productList)) {
            throw new InvalidParamException("productList不能为空");
        }
    }


}
