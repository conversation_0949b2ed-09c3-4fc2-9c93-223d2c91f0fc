package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * 无论什么角色
 *
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "用户信息对象")
public class UserDto extends BaseParamReq {

    @Schema(description = "用户ID", required = true)
    private Long userId;
    @Schema(description = "用户名称", required = true)
    private String userName;
    @Schema(description = "手机号码", required = true)
    private String userPhone;

    @Override
    public void checkParameter() {
        if (userId == null || userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (userName == null || userName.isEmpty()) {
            throw new InvalidParamException("userName不能为空");
        }
    }


}
