package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "购物车-店铺信息对象")
@ToString
@Data
public class ShoppingCartShopDto extends BaseThriftDto {

    @Schema(description = "店铺ID")
    private Long shopId;
    @Schema(description = "店铺名称")
    private String shopName;
    @Schema(description = "勾选的sku的总金额，扣除优惠后的总金额")
    private BigDecimal selectedTotalAmount;
    /**
     * 是否需要显示凑单按钮
     */
    @Schema(description = "是否需要显示凑单按钮")
    private Boolean showAddonBtn;
    /**
     * 店铺当前满足的满减活动描述
     */
    @Schema(description = "店铺当前满足的满减活动描述")
    private List<AddonDescDto> promotionDescList;
    /**
     * 商品总金额
     */
    @Schema(description = "商品总金额，不含优惠")
    private BigDecimal productTotalAmount;
    @Schema(description = "店铺当前满足的满减活动描述")
    private List<String> reductionDescList;
    /**
     * 店铺是否是选中状态。如果店铺下的所有商品都是选中的，则店铺是选中状态
     */
    @Schema(description = "店铺是否是选中状态。如果店铺下的所有商品都是选中的，则店铺是选中状态 ")
    private Boolean whetherSelected;
    @Schema(description = "勾选的商品总数量")
    private Long selectedQuantity;
    /**
     * 是否有与订单和商品匹配的有效的优惠券
     */
    @Schema(description = "是否有与订单和商品匹配的有效的优惠券")
    private Boolean hasValidCoupon;


}
