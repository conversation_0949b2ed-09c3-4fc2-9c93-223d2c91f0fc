package com.sankuai.shangou.seashop.trade.thrift.core.request.mall;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Data
@Schema(description = "店铺ES查询")
public class ApiShopMallQueryReq extends BasePageReq {

    /**
     * 店铺名称
     */
    @Schema(description = "店铺名称")
    private String shopName;

    /**
     * 类目ID
     */
    @Schema(description = "类目ID")
    private Long categoryId;

    /**
     * 品牌id
     */
    @Schema(description = "品牌id")
    private Long brandId;
    //商家ID
    @Schema(description = "商家ID")
    private Long userId;

    /**
     * 是否检索最后一级类目
     */
    @Schema(description = "是否检索最后一级类目")
    private Boolean searchLastCategory;


}
