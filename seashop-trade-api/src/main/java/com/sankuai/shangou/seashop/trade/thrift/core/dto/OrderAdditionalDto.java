package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;


/**
 * 订单附加信息：留言、优惠券、发票等
 *
 * <AUTHOR>
 */
@Schema(description = "订单附加信息")
@ToString
@Data
public class OrderAdditionalDto extends BaseThriftDto {

    @Schema(description = "配送方式。目前是固定值。1：快递配送")
    private Integer deliveryType;
    @Schema(description = "优惠券ID")
    private Long couponRecordId;
    @Schema(description = "优惠券金额")
    private BigDecimal couponAmount;
    @Schema(description = "折扣总金额")
    private BigDecimal discountAmount;
    @Schema(description = "满减总金额")
    private BigDecimal reductionAmount;
    @Schema(description = "运费")
    private BigDecimal freightAmount;
    @Schema(description = "用户备注")
    private String remark;
    @Schema(description = "税费")
    private BigDecimal taxAmount;
    @Schema(description = "发票信息")
    private InvoiceDto invoice;
    @Schema(description = "商品总金额，仅商品")
    private BigDecimal productAmount;
    @Schema(description = "实付金额，计算各种营销")
    private BigDecimal payAmount;
    /**
     * 店铺发票配置，预览订单接口初始话一次，后续依赖前端传入后再返回或者前端保留
     */
    @Schema(description = "店铺发票配置，预览订单接口初始话一次，后续依赖前端传入后再返回或者前端保留")
    private ShopInvoiceConfigDto shopInvoiceConfig;

    @Schema(description = "第三方订单号(美团订单号)")
    private String sourceOrderId;


}
