package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.trade.thrift.core.enums.ShoppingCartEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Schema(description = "购物车商品返回对象")
@ToString
@Data
public class ShoppingCartProductDto extends BaseThriftDto {

    @Schema(description = "购物车主键ID")
    private String id;
    @Schema(description = "商家用户ID")
    private Long userId;
    @Schema(description = "商品ID")
    private String productId;
    @Schema(description = "商品名称")
    private String productName;
    @Schema(description = "商品SKUID")
    private String skuId;
    @Schema(description = "数量")
    private Long quantity;
    @Schema(description = "添加时间")
    private Date addTime;
    @Schema(description = "是否选中")
    private Boolean whetherSelected;

    @Schema(description = "商品主图")
    private String mainImagePath;
    @Schema(description = "sku库存")
    private Long skuStock;
    @Schema(description = "原售价")
    private BigDecimal originSalePrice;
    @Schema(description = "优惠售价，基于专享价和阶梯价得到")
    private BigDecimal discountSalePrice;
    @Schema(description = "实际售价(最终价格)，在优惠售价的基础上，还有折扣后的价格")
    private BigDecimal realSalePrice;
    /**
     * 最终售价。如果没有折扣，则=realSalePrice，如果有折扣，则=discountSalePrice
     */
    @Schema(description = "最终售价。如果没有折扣，则=realSalePrice，如果有折扣，则=discountSalePrice")
    private BigDecimal finalSalePrice;
    @Schema(description = "商品总金额")
    private BigDecimal totalAmount;
    @Schema(description = "规格描述")
    private List<String> skuNameList;
    @Schema(description = "起购量")
    private Integer minBuyCount;
    @Schema(description = "限购数")
    private Integer maxBuyCount;
    @Schema(description = "倍数起购量")
    private Integer multipleCount;
    @Schema(description = "是否专享价")
    private Boolean whetherExclusive;
    @Schema(description = "购物车sku状态")
    private ShoppingCartEnum.SkuStatus skuStatus;
    @Schema(description = "校验失败的描述")
    private String errorMsg;
    @Schema(description = "商品货号")
    private String productCode;
    /**
     * 是否允许7天无理由退货
     */
    @Schema(description = "是否允许7天无理由退货")
    private Boolean enableNoReasonReturn;
    /**
     * 是否显示保障(保证金)标志
     */
    @Schema(description = "是否显示保障(保证金)标志")
    private Boolean showGuaranteeFlag;
    /**
     * 是否显示闪电标识
     */
    @Schema(description = "是否显示闪电标识")
    private Boolean showThunderFlag;

    @Schema(description = "商品sku自增id")
    private Long skuAutoId;
}
