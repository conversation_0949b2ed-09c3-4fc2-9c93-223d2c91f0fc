package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * 购买的商品对象
 *
 * <AUTHOR>
 */
@Schema(description = "购买的商品对象")
@ToString
@Data
public class PurchaseProductDto extends BaseParamReq {

    /**
     * 商品ID
     */
    @Schema(description = "商品ID", required = true)
    private Long productId;
    /**
     * skuId
     */
    @Schema(description = "skuId", required = true)
    private String skuId;
    /**
     * 购买数量。默认单位是1，如果用户指定了购买套数，前端需要计算
     */
    @Schema(description = "购买数量。默认单位是1，如果用户指定了购买套数，前端需要计算", required = true)
    private Long quantity;

    @Override
    public void checkParameter() {
        if (this.productId == null || this.productId <= 0) {
            throw new InvalidParamException("productId不能为空");
        }
        if (StrUtil.isBlank(this.skuId)) {
            throw new InvalidParamException("skuId不能为空");
        }
        if (this.quantity == null || this.quantity <= 0) {
            throw new InvalidParamException("quantity不能为空");
        }
    }


}
