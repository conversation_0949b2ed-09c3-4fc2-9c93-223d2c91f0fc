package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "交易商品品牌对象")
@ToString
@Data
public class TradeProductBrandDto extends BaseThriftDto {

    @Schema(description = "品牌ID")
    private Long brandId;
    @Schema(description = "品牌名称")
    private String brandName;
    @Schema(description = "品牌logo")
    private String logo;


}
