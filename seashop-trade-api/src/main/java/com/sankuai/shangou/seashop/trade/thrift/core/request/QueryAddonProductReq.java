package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.UserDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "查询凑单商品请求入参")
public class QueryAddonProductReq extends BasePageReq {

    @Schema(description = "用户信息", required = true)
    private UserDto user;
    @Schema(description = "店铺信息", required = true)
    private ShoppingCartShopDto shop;
    @Schema(description = "商品列表，前端传入是保持用户页面跳转看到的数据一致。需要传入商品的productId, realSalePrice, quantity", required = true)
    private List<ShoppingCartProductDto> productList;
    /**
     * 凑单类型。1：折扣；2：满减
     */
    @Schema(description = "凑单类型。1：折扣；2：满减", required = true)
    private Integer type;
    /**
     * 活动ID
     */
    @Schema(description = "活动ID", required = true)
    private Long activityId;


}
