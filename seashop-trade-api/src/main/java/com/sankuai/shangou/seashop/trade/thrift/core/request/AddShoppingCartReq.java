package com.sankuai.shangou.seashop.trade.thrift.core.request;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * 商家添加商品到购物车请求入参
 *
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "商家添加商品到购物车请求入参")
public class AddShoppingCartReq extends BaseParamReq {

    @Schema(description = "商家用户ID", required = true)
    private Long userId;
    @Schema(description = "商品ID", required = true)
    private Long productId;
    @Schema(description = "skuId", required = true)
    private String skuId;
    @Schema(description = "加购数量", required = true)
    private Long quantity;

    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (userId == null || userId <= 0) {
            throw new InvalidParamException("userId is null");
        }
        if (productId == null || productId <= 0) {
            throw new InvalidParamException("productId is null");
        }
        if (skuId == null || skuId.isEmpty()) {
            throw new InvalidParamException("skuId is null");
        }
        if (quantity == null || quantity <= 0) {
            throw new InvalidParamException("quantity is null");
        }
    }


}
