package com.sankuai.shangou.seashop.trade.thrift.core.request;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import cn.hutool.core.collection.CollUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * 商家添加商品到购物车请求入参
 *
 * <AUTHOR>
 */
@Data
@ToString
@Schema(description = "商家添加商品到购物车请求入参")
public class AddShoppingCartBatchReq extends BaseParamReq {

    private List<AddShoppingCartReq> shoppingCartList;

    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(CollUtil.isEmpty(shoppingCartList), "shoppingCartList不能为空");
    }


}
