package com.sankuai.shangou.seashop.trade.thrift.core.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Schema(description = "发票信息")
@ToString
@Data
public class InvoiceDto extends BaseThriftDto {

    /**
     * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
     */
    @Schema(description = "发票类型（1:普通发票、2:电子发票、3:增值税发票）")
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    @Schema(description = "发票抬头")
    private String invoiceTitle;

    /**
     * 税号
     */
    @Schema(description = "税号")
    private String invoiceCode;

    /**
     * 发票内容(发票明细、商品类别)
     */
    @Schema(description = "发票内容(发票明细、商品类别)")
    private String invoiceContext;

    /**
     * 注册地址
     */
    @Schema(description = "注册地址")
    private String registerAddress;

    /**
     * 注册电话
     */
    @Schema(description = "注册电话")
    private String registerPhone;

    /**
     * 开户银行
     */
    @Schema(description = "开户银行")
    private String bankName;

    /**
     * 银行帐号
     */
    @Schema(description = "银行帐号")
    private String bankNo;

    /**
     * 收票人姓名
     */
    @Schema(description = "收票人姓名")
    private String realName;

    /**
     * 收票人手机号
     */
    @Schema(description = "收票人手机号")
    private String cellPhone;

    /**
     * 收票人邮箱
     */
    @Schema(description = "收票人邮箱")
    private String email;

    /**
     * 收票人地址区域id
     */
    @Schema(description = "收票人地址区域id")
    private Integer regionId;

    /**
     * 收票人详细地址
     */
    @Schema(description = "收票人详细地址")
    private String address;

    /**
     * 订单完成后多少天开具增值税发票
     */
    @Schema(description = "订单完成后多少天开具增值税发票")
    private Integer vatInvoiceDay;


}
