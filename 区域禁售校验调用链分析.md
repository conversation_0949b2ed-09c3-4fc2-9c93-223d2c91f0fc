# 区域禁售校验功能调用链分析

## 📋 概述

本文档详细分析区域禁售校验功能在整个系统中的调用链路，包括现有的调用方式和我们新增的校验逻辑。

## 🔄 完整调用链路图

```
用户提交订单请求
    ↓
PreOrderCmdController.submitOrder()
    ↓
PreOrderServiceImpl.submitOrder()
    ↓
┌─────────────────────────────────────────┐
│ 1. Token校验                              │
│ 2. 选择构建上下文 (chooseContext)           │
│ 3. 构建上下文数据 (buildContext)           │
│ 4. 获取构建器 (ShopProductBuilder)        │
│ 5. 构建购物车数据 (builder.build)          │
└─────────────────────────────────────────┘
    ↓
if (buildResult.isSuccess())
    ↓
┌─────────────────────────────────────────┐
│ 校验阶段                                  │
│ ├─ validateConfigAmountAndQuantity()    │
│ └─ validateAreaForbidden() ⭐ 新增       │
└─────────────────────────────────────────┘
    ↓
创建订单 (order2TradeRemoteService.createOrder)
```

## 🎯 现有的区域禁售校验调用

### 1. 运费计算时的校验

```java
// 在 FreightCalculateAssistant.calculateShopProductFreight() 中
public BigDecimal calculateShopProductFreight(String shippingRegionPath, 
                                            CalculateFreightShopBo shopBo, 
                                            Shop dbShop, 
                                            boolean ignoreForbiddenArea) {
    // ...
    // 先校验是否禁售区域，并根据标识处理是否过滤掉禁售区域的商品
    this.checkIfAreaForbiddenAndRemoveProductIfNecessary(
        shippingRegionPath, 
        productList, 
        templateIdList, 
        ignoreForbiddenArea  // 运费计算时可能为 true，允许过滤
    );
    // ...
}
```

**调用时机**：
- 预览订单页面加载时
- 选择收货地址时
- 计算运费时

**特点**：
- `ignoreForbiddenArea = true` 时，会移除禁售商品，不抛异常
- `ignoreForbiddenArea = false` 时，直接抛出异常

## 🆕 新增的提交订单校验调用

### 1. 调用入口

```java
// PreOrderServiceImpl.submitOrder() 中
if (buildResult.isSuccess()) {
    // 校验订单金额和订单商品数量
    validateConfigAmountAndQuantity(buildResult);

    // ⭐ 新增：校验区域禁售限制
    validateAreaForbidden(buildContext, buildResult);

    // 创建订单
    // ...
}
```

### 2. 校验方法实现

```java
private void validateAreaForbidden(SubmitOrderContext buildContext, BuildResult buildResult) {
    // 1. 获取收货地址
    ShippingAddressBo shippingAddress = buildContext.getShippingAddress();
    if (shippingAddress == null || shippingAddress.getRegionPath() == null) {
        return; // 地址为空，跳过校验
    }

    String shippingRegionPath = shippingAddress.getRegionPath();
    
    // 2. 遍历所有店铺的商品
    for (ShopProductListBo shopProduct : buildResult.getShopProductList()) {
        // 3. 转换商品格式
        List<CalculateFreightProductBo> productList = convertProducts(shopProduct);
        
        // 4. 获取运费模板ID列表
        List<Long> templateIdList = extractTemplateIds(productList);
        
        // 5. 调用校验方法 ⭐ 关键调用
        freightCalculateAssistant.validateAreaForbidden(
            shippingRegionPath, 
            productList, 
            templateIdList, 
            false  // 提交订单时严格校验，不允许忽略
        );
    }
}
```

### 3. FreightCalculateAssistant 中的公共方法

```java
// 新增的公共校验方法
public void validateAreaForbidden(String shippingRegionPath, 
                                List<CalculateFreightProductBo> productList,
                                List<Long> templateIdList, 
                                boolean ignoreForbidden) {
    // 调用原有的私有方法
    this.checkIfAreaForbiddenAndRemoveProductIfNecessary(
        shippingRegionPath, 
        productList, 
        templateIdList, 
        ignoreForbidden
    );
}
```

## 🔍 详细调用流程分析

### 阶段1：请求接收
```
HTTP POST /submitOrder
    ↓
PreOrderCmdController.submitOrder(@RequestBody SubmitOrderReq orderReq)
    ↓
preOrderService.submitOrder(submitOrderBo)
```

### 阶段2：上下文构建
```
PreOrderServiceImpl.submitOrder(SubmitOrderBo orderBo)
    ↓
chooseContext(orderBo) // 选择上下文类型
    ├─ FlashSaleSubmitOrderContext (限时购)
    ├─ CollocationSubmitOrderContext (组合购)  
    ├─ BuyNowSubmitOrderContext (立即购买)
    └─ SubmitOrderContext (普通购物车)
    ↓
buildContext(buildContext, orderBo) // 构建上下文数据
    ├─ 设置用户ID
    ├─ 设置商品列表
    ├─ 获取收货地址 ⭐ 关键数据
    └─ 设置其他属性
```

### 阶段3：数据构建与校验
```
ShopProductBuilder.build(buildContext)
    ↓ (构建成功)
validateConfigAmountAndQuantity(buildResult) // 金额数量校验
    ↓
validateAreaForbidden(buildContext, buildResult) // ⭐ 区域禁售校验
    ↓
    ├─ 获取收货地址区域路径
    ├─ 遍历店铺商品列表
    ├─ 转换商品格式
    ├─ 提取运费模板ID
    └─ 调用 FreightCalculateAssistant.validateAreaForbidden()
        ↓
        checkIfAreaForbiddenAndRemoveProductIfNecessary()
            ├─ 查询禁售区域配置
            ├─ 匹配收货地址与禁售区域
            └─ 抛出异常 (ignoreForbidden=false)
```

### 阶段4：订单创建
```
order2TradeRemoteService.createOrder(createOrderReq)
    ↓
返回订单创建结果
```

## 🎛️ 关键参数说明

### ignoreForbidden 参数的使用场景

| 调用场景 | ignoreForbidden 值 | 行为 |
|---------|-------------------|------|
| 预览订单页面加载 | `true` | 过滤禁售商品，不抛异常 |
| 选择收货地址 | `true` | 过滤禁售商品，不抛异常 |
| 运费计算 | `true` | 过滤禁售商品，不抛异常 |
| **提交订单** | `false` | **直接抛异常，阻止下单** |

### 数据转换过程

```java
// 从 ShoppingCartProductBo 转换为 CalculateFreightProductBo
ShoppingCartProductBo originalProduct = shopProduct.getProductList().get(0);
    ↓
CalculateFreightProductBo freightProduct = new CalculateFreightProductBo();
freightProduct.setProductId(originalProduct.getProductId());
freightProduct.setProductName(originalProduct.getProductName());
freightProduct.setTemplateId(originalProduct.getTemplateId()); // ⭐ 关键字段
freightProduct.setBuyCount(originalProduct.getBuyCount());
freightProduct.setProductAmount(originalProduct.getProductAmount());
```

## 🔧 异常处理流程

```java
try {
    freightCalculateAssistant.validateAreaForbidden(
        shippingRegionPath, 
        productList, 
        templateIdList, 
        false
    );
} catch (BusinessException e) {
    log.error("【区域禁售校验】店铺ID={}的商品未通过区域禁售校验，错误信息={}", 
             shopProduct.getShop().getShopId(), e.getMessage());
    throw e; // 重新抛出，阻止订单创建
}
```

**异常传播路径**：
```
FreightCalculateAssistant.checkIfAreaForbiddenAndRemoveProductIfNecessary()
    ↓ throw BusinessException
PreOrderServiceImpl.validateAreaForbidden()
    ↓ re-throw BusinessException  
PreOrderServiceImpl.submitOrder()
    ↓ 返回失败结果
PreOrderCmdController.submitOrder()
    ↓ 返回错误响应给前端
```

## 📊 性能考虑

### 数据库查询次数
1. **禁售区域查询**：`restrictedAreaRepository.getByTemplateId(templateIdList)`
2. **区域路径匹配**：内存中进行字符串匹配
3. **模板ID去重**：减少重复查询

### 优化策略
- 按店铺分组处理，避免重复查询相同模板
- 缓存禁售区域配置（可考虑）
- 提前过滤无模板商品

## 🚀 扩展性考虑

### 未来可能的扩展点
1. **缓存机制**：缓存禁售区域配置
2. **异步校验**：对于非关键路径的校验
3. **批量优化**：批量查询多个模板的禁售配置
4. **监控告警**：统计禁售校验失败率

### 配置化支持
```java
// 可以考虑添加配置开关
@Value("${trade.area.forbidden.check.enabled:true}")
private boolean areaForbiddenCheckEnabled;

if (areaForbiddenCheckEnabled) {
    validateAreaForbidden(buildContext, buildResult);
}
```

## 📝 总结

通过这个调用链分析，我们可以看到：

1. **现有系统**已经在运费计算环节有区域禁售校验
2. **新增功能**在订单提交环节增加了严格校验
3. **调用链路**清晰，异常处理完善
4. **参数控制**灵活，可以根据场景选择不同的处理策略

这个实现确保了业务规则的一致性，防止了禁售区域商品的成功下单。