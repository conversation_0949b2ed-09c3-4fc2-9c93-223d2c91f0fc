package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 9:50
 */
@Setter
@Getter
public class RemoteCollocationBo {

    /**
     * 组合购主表ID
     */
    private Long id;

    /**
     * 组合购标题
     */
    private String title;

    /**
     * 开始日期
     */
    private Date startTime;

    /**
     * 结束日期
     */
    private Date endTime;

    /**
     * 组合描述
     */
    private String shortDesc;

    /**
     * 组合购店铺ID
     */
    private Long shopId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 组合购商品集合
     */
    private List<RemoteCollocationProductBo> productRespList;

}
