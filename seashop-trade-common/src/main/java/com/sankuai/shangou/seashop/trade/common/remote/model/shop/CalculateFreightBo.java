package com.sankuai.shangou.seashop.trade.common.remote.model.shop;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 计算运费的业务对象
 * <AUTHOR>
 */
@Getter
@Setter
public class CalculateFreightBo {

    /**
     * 区域ID
     */
    private Integer regionId;
    /**
     * 区域全路径，逗号隔开
     * 需要区域数据返回的时候，也能拿到区域数据的完整路径，后续计算的时候，可以不用再查询，提高效率
     *
     */
    private String regionPath;
    /**
     * 是否忽略禁售区域商品
     * <p>这个标识的作用是：第一次进入订单预览页和后续其他操作都需要计算运费，但是第一次进入页面时，
     * 如果收货地址在禁售区域也应该显示数据，允许用户调整收货地址。所以第一次进入预览页这个标识设置为true，此时不报错而是忽略禁售区域的商品
     * 提交订单和其他接口要计算运费时，这个标识设置为false或者不设置，此时在禁售区域会抛出异常</p>
     */
    private Boolean ignoreForbiddenArea;
    /**
     * 店铺列表，店铺对象包含商品列表
     */
    private List<CalculateFreightShopBo> shopList;

}
