package com.sankuai.shangou.seashop.trade.common.remote.model.product;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RemoteLadderPriceBo {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 最小批量
     */
    private Integer minBath;

    /**
     * 最大批量
     */
    private Integer maxBath;

    /**
     * 价格
     */
    private BigDecimal price;

}
