package com.sankuai.shangou.seashop.product.common.es;

import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.product.common.es.model.EagleQueryResult;
import com.sankuai.shangou.seashop.trade.common.constant.EsConst;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.bulk.BulkItemResponse;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.search.ClearScrollRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.client.core.CountResponse;
import org.elasticsearch.client.indices.AnalyzeRequest;
import org.elasticsearch.client.indices.AnalyzeResponse;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.IdsQueryBuilder;
import org.elasticsearch.index.query.MatchAllQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EagleProductService {

    @Resource
    private RestHighLevelClient restHighLevelClient;
    private static final int EAGLE_SUCCESS_CODE = 200;

    public void partUpdate(String indexName, Map<String/*docId*/, Map<String/*字段名称*/, Object/*字段值*/>> paramMap) {
        BulkRequest request = new BulkRequest();
        for (Map.Entry<String, Map<String, Object>> entry : paramMap.entrySet()) {
            UpdateRequest updateRequest = new UpdateRequest(indexName, entry.getKey()).doc(entry.getValue(), XContentType.JSON).upsert(entry.getValue(), XContentType.JSON);
            request.add(updateRequest);
        }
        batchOperate(request);
    }

    /**
     * 批量操作建议数量最大建议在1000-5000个doc，如果单个文档大小很大，数量要更小。
     *
     * @param indexName
     * @param idAndDocs
     */
    public void batchUpdate(String indexName, Map<String/*文档ID*/, String/*文档内容*/> idAndDocs) {
        if (MapUtils.isEmpty(idAndDocs)) {
            return;
        }
        BulkRequest request = new BulkRequest();
        for (Map.Entry<String, String> entry : idAndDocs.entrySet()) {
            UpdateRequest updateRequest = new UpdateRequest(indexName, entry.getKey()).doc(entry.getValue(), XContentType.JSON).retryOnConflict(5).upsert(entry.getValue(), XContentType.JSON);
            request.add(updateRequest);
        }
        batchOperate(request);
    }

    /**
     * 批量删除文档
     *
     * @param indexName
     * @param ids
     */
    public void batchDelete(String indexName, List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        BulkRequest request = new BulkRequest();
        for (String id : ids) {
            request.add(new DeleteRequest(indexName, id));
        }
        batchOperate(request);
    }

    /**
     * 支持批量的插入、更新、删除操作
     *
     * @param request
     */
    private void batchOperate(BulkRequest request) {
        try {
            BulkResponse response = restHighLevelClient.bulk(request, RequestOptions.DEFAULT);
            // 获取保存失败的结果
            List<BulkItemResponse.Failure> failures = Arrays.stream(response.getItems()).filter(BulkItemResponse::isFailed).map(BulkItemResponse::getFailure).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(failures)) {
                String failureMsg = Joiner.on(",").join(failures.stream().map(BulkItemResponse.Failure::getMessage).collect(Collectors.toList()));
                log.error("save index failed", failureMsg);
                throw new BusinessException("save index failed: " + failureMsg);
            }
        } catch (Exception e) {
            log.error("eagle操作失败", e);
            throw new BusinessException("eagle操作失败");
        }
    }

    /**
     * 条件查询,可以分页查询,scroll查询等
     *
     * @param request
     * @return
     */
    public CountResponse countByCondition(CountRequest request) {
        log.info("es count param,{}", request.query());
        try {
            return restHighLevelClient.count(request, RequestOptions.DEFAULT);
        } catch (Exception e) {
            log.error("eagle查询异常,", e);
            throw new BusinessException("eagle查询异常");
        }
    }

    /**
     * 条件查询,可以分页查询,scroll查询等
     *
     * @param request
     * @return
     */
    public EagleQueryResult queryByCondition(SearchRequest request, String scrollId, Integer keepAliveMinutes) {
        log.info("es query param,{}", request.source());
        EagleQueryResult result = EagleQueryResult.defaultEagleQueryResult();
        try {
            SearchResponse response;

            if (StringUtils.isEmpty(scrollId)) {
                response = restHighLevelClient.search(request, RequestOptions.DEFAULT);
            } else {
                SearchScrollRequest searchScrollRequest = new SearchScrollRequest(scrollId);
                searchScrollRequest.scroll(TimeValue.timeValueMinutes(ObjectUtils.defaultIfNull(keepAliveMinutes, 1)));
                response = restHighLevelClient.scroll(searchScrollRequest, RequestOptions.DEFAULT);
            }

            if (response.status().getStatus() != EAGLE_SUCCESS_CODE) {
                log.error("query from eagle failed: {}, {}", response.status().getStatus(), response);
                throw new BusinessException("query from eagle failed: " + response.status().getStatus());
            }
            SearchHits hits = response.getHits();
            result.setTotalHit(hits.getTotalHits().value);
            result.setMaxScore(hits.getMaxScore());
            result.setScrollId(response.getScrollId());
            List<String> list = Lists.newArrayList();
            for (SearchHit next : hits) {
                list.add(next.getSourceAsString());
            }
            if (Objects.nonNull(response.getAggregations())) {
                result.setAggregations(response.getAggregations());
            }
            result.setHits(list);
            return result;
        } catch (IOException e) {
            log.error("eagle查询异常,", e);
            throw new BusinessException("eagle查询异常");
        }
    }

    public SearchResponse queryByTerms(String indexName, String propsName, Object matchValue) {
        SearchRequest request = new SearchRequest(indexName);

        SearchSourceBuilder builder = new SearchSourceBuilder();
        request.source(builder);
        builder.query(QueryBuilders.termQuery(propsName, matchValue));
        try {
            return restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("eagle查询异常,", e);
            throw new BusinessException("eagle查询异常");
        }
    }

    public SearchResponse queryById(String indexName, List<String> idList) {
        SearchRequest request = new SearchRequest(indexName);

        SearchSourceBuilder builder = new SearchSourceBuilder();
        IdsQueryBuilder idsQueryBuilder = QueryBuilders.idsQuery().addIds(String.join(",", idList));
        builder.query(idsQueryBuilder);
        request.source(builder);
        try {
            return restHighLevelClient.search(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("eagle查询异常,", e);
            throw new BusinessException("eagle查询异常");
        }
    }

    public void clearIndex(String indexName) {
        // 创建匹配所有文档的查询
        MatchAllQueryBuilder matchAllQuery = QueryBuilders.matchAllQuery();
        // 创建DeleteByQueryRequest
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(indexName).setQuery(matchAllQuery);
        // 异步执行请求并等待响应
        try {
            BulkByScrollResponse response = restHighLevelClient.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
            // 获取删除的文档数量
            long deleted = response.getDeleted();
            log.info("refund Deleted documents: " + deleted);
        } catch (Exception e) {
            log.error("refund delete error", e);
        }
    }

    public void clearScroll(String scrollId) {
        ClearScrollRequest clearScrollRequest = new ClearScrollRequest();
        clearScrollRequest.addScrollId(scrollId);
        try {
            restHighLevelClient.clearScroll(clearScrollRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("清除scroll异常,", e);
        }
    }

    /**
     * 关键字分词
     *
     * @param text 关键词
     * @return 拆分后的关键词
     */
    public List<String> analyze(String text) {
        if (StringUtils.isBlank(text)) {
            return Collections.emptyList();
        }
        if (text.length() == 1) {
            return Collections.singletonList(text);
        }
        AnalyzeRequest request = AnalyzeRequest.withGlobalAnalyzer(EsConst.IK_ANALYZER, text);
        try {
            AnalyzeResponse response = restHighLevelClient.indices().analyze(request, RequestOptions.DEFAULT);
            List<String> subTexts = response.getTokens().stream().map(AnalyzeResponse.AnalyzeToken::getTerm).distinct().collect(Collectors.toList());
            log.info("分词拆分:关键={},结果={}", text, subTexts);
            return subTexts;
        } catch (IOException e) {
            log.error("查询分词失败", e);
            return Collections.singletonList(text);
        }
    }
}
