package com.sankuai.shangou.seashop.trade.common.remote;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.common.BatchQueryHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.OrderCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.CreateOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentSummaryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.CountUserFlashSaleReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryProductBuyCountReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentSummaryResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.ProductBuyCountListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.ProductBuyCountResp;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class Order2TradeRemoteService {

    @Resource
    private OrderCmdFeign orderCmdFeign;
    @Resource
    private OrderQueryFeign orderQueryFeign;
    @Resource
    private ProductCommentQueryFeign productCommentQueryFeign;

    /**
     * 调用订单服务接口创建订单
     *
     * @param createOrderReq
     * <AUTHOR>
     */
    public CreateOrderResp createOrder(CreateOrderReq createOrderReq) {
        String uniqueId = UUID.randomUUID().toString();
        createOrderReq.setUniqueId(uniqueId);
        log.info("【订单】创建订单, 请求参数={}", JsonUtil.toJsonString(createOrderReq));
        CreateOrderResp resp = ThriftResponseHelper.executeThriftCall(() -> orderCmdFeign.createOrder(createOrderReq));
        return resp;
    }

    /**
     * 统计商品维度的用户限时购购买数量
     *
     * @param userId 用户ID
     */
    public Long countFlashSaleByProduct(Long userId, Long flashSaleId, Long productId) {
        log.info("【订单】统计商品维度的用户限时购购买数量, userId={}, flashSaleId={}, productId={}",
                userId, flashSaleId, productId);
        CountUserFlashSaleReq req = new CountUserFlashSaleReq();
        req.setProductId(productId);
        req.setFlashSaleId(flashSaleId);
        req.setUserId(userId);
        Long count = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.countFlashSaleByProduct(req));
        log.info("【订单】统计商品维度的用户限时购购买数量, userId={}, flashSaleId={}, productId={}, count={}",
                userId, flashSaleId, productId, count);
        return count;
    }

    /**
     * 统计SKU维度的用户限时购购买数量
     *
     * @param userId 用户ID
     */
    public Long countFlashSaleBySku(Long userId, Long flashSaleId, String skuId) {
        log.info("【订单】统计SKU维度的用户限时购购买数量, userId={}, flashSaleId={}, skuId={}",
                userId, flashSaleId, skuId);
        CountUserFlashSaleReq req = new CountUserFlashSaleReq();
        req.setSkuId(skuId);
        req.setFlashSaleId(flashSaleId);
        req.setUserId(userId);
        Long count = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.countFlashSaleBySku(req));
        log.info("【订单】统计SKU维度的用户限时购购买数量, userId={}, flashSaleId={}, skuId={}, count={}",
                userId, flashSaleId, skuId, count);
        return count;
    }

    /**
     * 查询商品评价汇总信息
     *
     * @param productId 商品ID
     * @return 商品评价汇总信息
     */
    public ProductCommentSummaryResp getProductCommentSummary(Long productId) {
        QueryProductCommentSummaryReq request = new QueryProductCommentSummaryReq();
        request.setProductId(productId);
        ProductCommentSummaryResp summaryResp = ThriftResponseHelper.executeThriftCall(() -> productCommentQueryFeign.queryProductCommentSummary(request));
        return summaryResp;
    }

    /**
     * 查询用户购买商品数量
     *
     * @param userId 用户ID
     * @param productIdList 商品ID列表
     * @return 商品购买数量
     */
    public List<ProductBuyCountResp> getUserProductBuyCount(Long userId, List<Long> productIdList) {
        QueryProductBuyCountReq req = new QueryProductBuyCountReq();
        req.setUserId(userId);
        req.setProductIdList(productIdList);
        return BatchQueryHelper.batchQuery(req, QueryProductBuyCountReq::setProductIdList, QueryProductBuyCountReq::getProductIdList, (request) -> {
            log.info("【订单】查询用户购买商品数量, 请求参数={}", JsonUtil.toJsonString(request));
            ProductBuyCountListResp resp = ThriftResponseHelper.executeThriftCall(() -> orderQueryFeign.getUserProductBuyCount(request));
            log.info("【订单】查询用户购买商品数量, 请求参数={}, resp={}", JsonUtil.toJsonString(request), JsonUtil.toJsonString(resp));
            return resp.getDataList();
        });
    }

    public Map<Long, Long> getUserProductBuyCountMap(Long userId, List<Long> productIdList) {
        List<ProductBuyCountResp> list = getUserProductBuyCount(userId, productIdList);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(ProductBuyCountResp::getProductId, ProductBuyCountResp::getQuantity));
    }


}
