package com.sankuai.shangou.seashop.trade.common.enums;

/**
 * <AUTHOR>
 */
public enum ShopTradeSettingValidTypeEnum {

    MATCH_BOTH(1, "起购数量和起购金额同时满足"),
    MATCH_ANY(2, "起购数量和起购金额满足其一"),

    ;

    private final Integer code;
    private final String desc;

    ShopTradeSettingValidTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
