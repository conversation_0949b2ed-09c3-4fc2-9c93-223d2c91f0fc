package com.sankuai.shangou.seashop.product.common.es.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.product.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.product.common.constant.EsConstant;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductModel;
import com.sankuai.shangou.seashop.product.common.es.model.product.EsProductParam;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.trade.common.config.EsIndexProps;
import com.sankuai.shangou.seashop.trade.common.constant.EsConst;
import com.sankuai.shangou.seashop.trade.common.es.EagleService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.MatchQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/12/26 14:08
 */
@Service
@Slf4j
public class EsProductService extends AbsEsService<EsProductParam, EsProductModel> {

    @Resource
    private RestHighLevelClient restHighLevelClient;

    @Value("${spring.profiles.active}")
    private String indexPrefix;

    @Override
    public String getLogPrefix() {
        return "商品";
    }

    @Override
    public String getIndexName() {
        return EsConstant.INDEX_TRADE_PRODUCT;
    }

    @Resource
    private EagleService eagleService;
    @Resource
    private EsIndexProps esIndexProps;

    @Override
    public BoolQueryBuilder buildProductSearchCondition(EsProductParam queryBo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank(queryBo.getSearchKey())) {
            String searchKey = queryBo.getSearchKey();

            // 构建模糊搜索查询，支持所有字段的模糊匹配
            BoolQueryBuilder searchQueryBuilder = QueryBuilders.boolQuery();

            // 添加通配符，支持模糊搜索（大小写不敏感）
            String wildcardKey = "*" + searchKey.toLowerCase() + "*";

            // OE号模糊搜索 - 权重最高(5) - 大小写不敏感
            searchQueryBuilder.should(QueryBuilders.wildcardQuery(EsConst.TradeProduct.SEARCH_FIELD_OE_CODE + ".wildcard", wildcardKey).caseInsensitive(true).boost(5f));

            // 替换号模糊搜索 - 权重4 - 大小写不敏感
            searchQueryBuilder.should(QueryBuilders.wildcardQuery(EsConst.TradeProduct.SEARCH_FIELD_REPLACE_NUMBER + ".keyword", wildcardKey).caseInsensitive(true).boost(4f));

            // 品牌号模糊搜索 - 权重3 - 大小写不敏感
            searchQueryBuilder.should(QueryBuilders.wildcardQuery(EsConst.TradeProduct.SEARCH_FIELD_BRAND_CODE + ".wildcard", wildcardKey).caseInsensitive(true).boost(3f));

            // 商品名称模糊搜索 - 权重2 - 大小写不敏感
            searchQueryBuilder.should(QueryBuilders.wildcardQuery(EsConst.TradeProduct.SEARCH_FIELD_PRODUCT_NAME + ".keyword", wildcardKey).caseInsensitive(true).boost(2f));

            // 品牌名称模糊搜索 - 权重1 - 大小写不敏感
            searchQueryBuilder.should(QueryBuilders.wildcardQuery(EsConst.TradeProduct.SEARCH_FIELD_BRAND_NAME + ".keyword", wildcardKey).caseInsensitive(true).boost(1f));

            // 适配车型模糊搜索 - 权重1 - 大小写不敏感
            searchQueryBuilder.should(QueryBuilders.wildcardQuery(EsConst.TradeProduct.SEARCH_FIELD_ADAPTABLE_CAR + ".keyword", wildcardKey).caseInsensitive(true).boost(1f));

            // 设置至少匹配一个条件
            searchQueryBuilder.minimumShouldMatch(1);

            boolQueryBuilder.must(searchQueryBuilder);
        }
        if (StringUtils.isNotEmpty(queryBo.getProductName())) {
            this.assembleSplitWordsSearch(boolQueryBuilder, queryBo.getProductName(), EsConstant.Product.SEARCH_FIELD_PRODUCT_NAME);
        }
        if (StringUtils.isNotBlank(queryBo.getBrandName())) {
            this.assembleSplitWordsSearch(boolQueryBuilder, queryBo.getBrandName(), EsConstant.Product.SEARCH_FIELD_BRAND_NAME);
        }
        if (StringUtils.isNotBlank(queryBo.getOeCode())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_OE_CODE, queryBo.getOeCode()));
        }
        if (StringUtils.isNotBlank(queryBo.getBrandCode())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_BRAND_CODE, queryBo.getBrandCode()));
        }
        if (queryBo.getProductId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_ID, queryBo.getProductId()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getProductIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_ID, queryBo.getProductIds()));
        }
        if (queryBo.getSkuAutoId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SKU_AUTO_IDS, queryBo.getSkuAutoId()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getSkuAutoIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SKU_AUTO_IDS, queryBo.getSkuAutoIds()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getSkuCodes())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SKU_CODES, queryBo.getSkuCodes()));
        }
        if (queryBo.getShopId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SHOP_ID, queryBo.getShopId()));
        }
        if (queryBo.getStartTime() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_ADDED_TIME).gte(DateUtil.beginOfDay(queryBo.getStartTime()).getTime()));
        }
        if (queryBo.getEndTime() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_ADDED_TIME).lte(DateUtil.endOfDay(queryBo.getEndTime()).getTime()));
        }
        if (StringUtils.isNotEmpty(queryBo.getProductCode())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_CODE, queryBo.getProductCode()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getBrandIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_BRAND_ID, queryBo.getBrandIds()));
        }
        if (StringUtils.isNotEmpty(queryBo.getCategoryPath())) {
            boolQueryBuilder.filter(QueryBuilders.prefixQuery(EsConstant.Product.SEARCH_FIELD_CATEGORY_PATH, queryBo.getCategoryPath()));
        }
        if (queryBo.getAuditStatusCode() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_AUDIT_STATUS, queryBo.getAuditStatusCode()));
        }
        if (queryBo.getSaleStatusCode() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SALE_STATUS, queryBo.getSaleStatusCode()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getAuditStatusCodeList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_AUDIT_STATUS, queryBo.getAuditStatusCodeList()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getSaleStatusCodeList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SALE_STATUS, queryBo.getSaleStatusCodeList()));
        }
        if (queryBo.getShopCategoryId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SHOP_CATEGORY_IDS, queryBo.getShopCategoryId()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getShopCategoryIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SHOP_CATEGORY_IDS, queryBo.getShopCategoryIds()));
        }
        if (queryBo.getFreightTemplateId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_FREIGHT_TEMPLATE_ID, queryBo.getFreightTemplateId()));
        }
        if (queryBo.getWhetherBelowSafeStock() != null && queryBo.getWhetherBelowSafeStock()) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_BELOW_SAFE_STOCK, queryBo.getWhetherBelowSafeStock()));
        }
        if (queryBo.getCreateTimeStart() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_CREATE_TIME).gte(DateUtil.beginOfDay(queryBo.getCreateTimeStart()).getTime()));
        }
        if (queryBo.getCreateTimeEnd() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_CREATE_TIME).lte(DateUtil.endOfDay(queryBo.getCreateTimeEnd()).getTime()));
        }
        if (queryBo.getUpdateTimeStart() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_UPDATE_TIME).gte(DateUtil.beginOfDay(queryBo.getUpdateTimeStart()).getTime()));
        }
        if (queryBo.getUpdateTimeEnd() != null) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_UPDATE_TIME).lte(DateUtil.endOfDay(queryBo.getUpdateTimeEnd()).getTime()));
        }
        if (queryBo.getSource() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SOURCE, queryBo.getSource()));
        }
        if (queryBo.getWhetherOpenLadder() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_OPEN_LADDER, queryBo.getWhetherOpenLadder()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getExcludeProductIds())) {
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_ID, queryBo.getExcludeProductIds()));
        }
        if (CollectionUtils.isNotEmpty(queryBo.getShopIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SHOP_ID, queryBo.getShopIds()));
        }
        if (queryBo.getCategoryId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_CATEGORY_ID, queryBo.getCategoryId()));
        }

        // 此处逻辑主要为了满足erp 同时查询草稿箱和违规下架的场景
        if (CollectionUtils.isNotEmpty(queryBo.getStatusList())) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            queryBo.getStatusList().forEach(status -> {
                BoolQueryBuilder subBoolQuery = QueryBuilders.boolQuery();
                if (status.getSaleStatus() != null) {
                    subBoolQuery.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SALE_STATUS, status.getSaleStatus()));
                }
                if (status.getAuditStatus() != null) {
                    subBoolQuery.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_AUDIT_STATUS, status.getAuditStatus()));
                }
                boolQuery.should(subBoolQuery);
            });
            boolQueryBuilder.filter(boolQuery);
        }

        // 商品id 和商品名称 or 查询
        if (StringUtils.isNotEmpty(queryBo.getProductIdOrName())) {
            BoolQueryBuilder orBoolQueryBuilder = QueryBuilders.boolQuery();
            if (NumberUtil.isLong(queryBo.getProductIdOrName())) {
                orBoolQueryBuilder.should(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_ID, queryBo.getProductIdOrName()));
            }

            String searchKey = queryBo.getProductIdOrName();
//            searchKey = searchKey.replaceAll("[,，]", " ");
//            // 根据空格拆分为并列多个关键词
//            String[] keys = searchKey.split(" ");
//            for (String key : keys) {
//                if (StringUtils.isEmpty(key)) {
//                    continue;
//                }
//                List<String> keywords = eagleService.analyze(key);
            MatchQueryBuilder matchProductNameQueryBuilder = QueryBuilders.matchQuery(EsConstant.Product.SEARCH_FIELD_PRODUCT_NAME, searchKey);
//                if (CollectionUtils.isNotEmpty(keywords)) {
//                    float minimumShouldMatch = keywords.size() * esIndexProps.getMinimumShouldProportion();
//                    log.info("product size={},minimumShouldMatch={}", keywords.size(), minimumShouldMatch);
//                    matchProductNameQueryBuilder.minimumShouldMatch(Math.round(minimumShouldMatch) + "");
//                }
            orBoolQueryBuilder.should(matchProductNameQueryBuilder);
//            }

            boolQueryBuilder.must(orBoolQueryBuilder);
        }

        // 规格名称id查询
        if (CollUtil.isNotEmpty(queryBo.getSpecNameIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SPEC_NAME_IDS, queryBo.getSpecNameIds()));
        }

        boolQueryBuilder.filter(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_WHETHER_DELETE, Boolean.FALSE));

        // 筛选出有库存的
        if (queryBo.getHasStock() != null && queryBo.getHasStock()) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery(EsConstant.Product.SEARCH_FIELD_STOCK).gt(0));
        }

        // 类目id 查询
        if (CollUtil.isNotEmpty(queryBo.getCategoryIds())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_CATEGORY_ID, queryBo.getCategoryIds()));
        }
        return boolQueryBuilder;
    }

    @Override
    public List<FieldSortReq> defaultSortList() {
        FieldSortReq sort = new FieldSortReq();
        sort.setSort(EsConstant.Product.SEARCH_FIELD_PRODUCT_ID);
        sort.setIzAsc(Boolean.FALSE);
        return Arrays.asList(sort);
    }

    public List<Long> getBrandIds(String categoryPath) {
        try {
            // 构建聚合查询
            SearchRequest searchRequest = new SearchRequest(getIndexName());
            SearchSourceBuilder sourceBuilder = new SearchSourceBuilder();

            // 过滤出 categoryId
            sourceBuilder.query(QueryBuilders.prefixQuery(EsConstant.Product.SEARCH_FIELD_CATEGORY_PATH, categoryPath));

            // 构建品牌的聚合
            sourceBuilder.aggregation(AggregationBuilders.terms(EsConstant.Product.AGG_BRAND_ID).field(EsConstant.Product.SEARCH_FIELD_BRAND_ID).size(CommonConstant.RECOMMEND_BRAND_SIZE));

            searchRequest.source(sourceBuilder);

            // 执行查询
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            // 解析聚合结果
            Aggregations aggregations = searchResponse.getAggregations();
            Terms brandIdAggregation = aggregations.get(EsConstant.Product.AGG_BRAND_ID);
            return getDirectKeyLong(brandIdAggregation);
        } catch (Exception e) {
            log.error("查询品牌聚合异常, categoryPath: {}", categoryPath, e);
            throw new BusinessException("品牌查询异常");
        }
    }

    private List<Long> getDirectKeyLong(Terms aggTerms) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(aggTerms.getBuckets())) {
            return Collections.emptyList();
        }
        List<Long> idList = new ArrayList<>(aggTerms.getBuckets().size());
        // 遍历品牌聚合桶
        for (Terms.Bucket bucket : aggTerms.getBuckets()) {
            String id = bucket.getKeyAsString();
            idList.add(Long.valueOf(id));
        }
        return idList;
    }

    public Map<Long, Long> countBySpecNameIds(List<Long> specNameIds) {
        try {
            SearchRequest searchRequest = new SearchRequest(getIndexName());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 构建过滤条件，只包含指定的specIds 同时过滤掉逻辑删除的
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_WHETHER_DELETE, Boolean.FALSE));
            boolQueryBuilder.must(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_SPEC_NAME_IDS, specNameIds));
            // 过滤掉审核状态为未审核和未通过的
            List<Integer> ignoreAuditStatus = Arrays.asList(ProductEnum.AuditStatusEnum.WAIT_AUDIT.getCode(), ProductEnum.AuditStatusEnum.NOT_PASS.getCode());
            boolQueryBuilder.mustNot(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_AUDIT_STATUS, ignoreAuditStatus));
            searchSourceBuilder.query(boolQueryBuilder);

            // 构建聚合，对specIds进行分组
            TermsAggregationBuilder aggregation = AggregationBuilders.terms(EsConstant.Product.AGG_SPEC_NAME_IDS).field(EsConstant.Product.SEARCH_FIELD_SPEC_NAME_IDS).size(specNameIds.size());

            searchSourceBuilder.aggregation(aggregation);

            searchRequest.source(searchSourceBuilder);

            // 执行搜索请求
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            // 解析聚合结果
            ParsedLongTerms terms = searchResponse.getAggregations().get(EsConstant.Product.AGG_SPEC_NAME_IDS);
            Map<Long, Long> result = new HashMap<>();
            for (Terms.Bucket bucket : terms.getBuckets()) {
                Long specId = bucket.getKeyAsNumber().longValue();
                Long docCount = bucket.getDocCount();
                result.put(specId, docCount);
            }
            return result;
        } catch (Exception e) {
            log.error("查询规格数量异常, specIds: {}", specNameIds, e);
            throw new BusinessException("规格数量查询异常");
        }
    }

    /**
     * 统计某一组类目下的商品数量
     * 入参: 类目id的集合, 示例: [1,2,3]
     * 返回值: Map<Long, Long>, key: 类目id, value: 该类目下的商品数量
     */
    public Map<Long, Long> countByCategoryIds(List<Long> categoryIds) {
        if (CollUtil.isEmpty(categoryIds)) {
            log.warn("Category IDs list is empty or null");
            return new HashMap<>();
        }

        try {
            SearchRequest searchRequest = new SearchRequest(getIndexName());
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();

            // 构建过滤条件，只包含指定的categoryIds 同时过滤掉逻辑删除的
            BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
            boolQueryBuilder.must(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_WHETHER_DELETE, Boolean.FALSE));
            boolQueryBuilder.must(QueryBuilders.termsQuery(EsConstant.Product.SEARCH_FIELD_CATEGORY_ID, categoryIds));
            // 只统计销售中的商品
            boolQueryBuilder.must(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_SALE_STATUS, ProductEnum.SaleStatusEnum.ON_SALE.getCode()));
            boolQueryBuilder.must(QueryBuilders.termQuery(EsConstant.Product.SEARCH_FIELD_AUDIT_STATUS, ProductEnum.AuditStatusEnum.ON_SALE.getCode()));

            searchSourceBuilder.query(boolQueryBuilder);

            // 构建聚合，对categoryIds进行分组
            TermsAggregationBuilder aggregation = AggregationBuilders.terms(EsConstant.Product.AGG_CATEGORY_ID).field(EsConstant.Product.SEARCH_FIELD_CATEGORY_ID).size(categoryIds.size());
            searchSourceBuilder.aggregation(aggregation);
            searchRequest.source(searchSourceBuilder);

            // 执行搜索请求
            SearchResponse searchResponse = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            // 解析聚合结果
            ParsedLongTerms terms = searchResponse.getAggregations().get(EsConstant.Product.AGG_CATEGORY_ID);
            Map<Long, Long> result = new HashMap<>();
            for (Terms.Bucket bucket : terms.getBuckets()) {
                Long categoryId = bucket.getKeyAsNumber().longValue();
                Long docCount = bucket.getDocCount();
                result.put(categoryId, docCount);
                log.debug("categoryId:{}, docCount:{}", categoryId, docCount);
            }
            return result;
        } catch (Exception e) {
            log.error("Error while searching Elasticsearch", e);
            throw new BusinessException("查询类目商品数量异常");
        }
    }
}
