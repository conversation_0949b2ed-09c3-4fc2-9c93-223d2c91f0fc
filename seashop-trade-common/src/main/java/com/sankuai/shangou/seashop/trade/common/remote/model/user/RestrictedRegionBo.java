package com.sankuai.shangou.seashop.trade.common.remote.model.user;

import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/01/04 15:50
 */
@Setter
@Getter
public class RestrictedRegionBo {

    /**
     * 运费模板id
     */
    private Long id;

    /**
     * 运费模板名称
     */
    private String name;

    /**
     * 宝贝发货地
     */
    private Integer sourceAddress;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 是否商家负责运费
     */
    private Integer whetherFree;

    /**
     * 定价方法(按体积、重量计算）
     */
    private Integer valuationMethod;

    /**
     * 运送类型（物流、快递）
     */
    private Integer shippingMethod;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 非销售区域是否隐藏
     */
    private Boolean nonSalesAreaHide;

    /**
     * 限制区域id的集合
     */
    private List<Long> restrictedRegionIds;

}
