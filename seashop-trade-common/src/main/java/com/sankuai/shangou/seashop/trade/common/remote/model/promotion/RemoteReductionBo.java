package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RemoteReductionBo {

    /**
     * 满减活动ID，对应满减表的主键ID
     */
    private Long activeId;
    /**
     * 满减活动名称
     */
    private String activeName;
    /**
     * 单笔订单满减金额门槛
     */
    private BigDecimal moneyOffCondition;
    /**
     * 单笔订单满减金额
     */
    private BigDecimal moneyOffFee;
    /**
     * 是否叠加优惠
     */
    private Boolean moneyOffOverLay;
    /**
     * 活动开始时间
     */
    private Date startTime;
    /**
     * 活动结束时间
     */
    private Date endTime;

}
