package com.sankuai.shangou.seashop.trade.common.constant;

import java.math.BigDecimal;

/**
 * 本服务公共的常量
 * <AUTHOR>
 */
public class CommonConst {

    // 默认的商品的起购量
    public static final int DEFAULT_BUY_COUNT = 1;
    // 代表所有商品的商品ID
    public final static long ALL_PRODUCT_ID = -1L;

    public static final String CURRENCY_SYMBOL_RMB = "¥";
    // 凑单页面的满减描述
    public static final String ADDON_REDUCTION_DESC_DEFAULT = "差%s元可减" + CURRENCY_SYMBOL_RMB + "%s元";
    public static final String ADDON_REDUCTION_DESC_NEXT = "已购满%s元，已减%s元";
    public static final String ADDON_REDUCTION_DESC_NEXT_OVERLAY = "已购满%s元，已减%s元，再凑%s元可减%s元";
    public static final String ADDON_DISCOUNT_DESC_LAST = "已购满%s元，享%s折优惠";
    public static final String ADDON_DISCOUNT_DESC_NEXT = "已购满%s元，享%s折优惠，再凑%s元可享%s折优惠，立减%s元";
    public static final String ADDON_DISCOUNT_DESC_DEFAULT= "差%s元可享%s折优惠，立减%s元";

    // 购物车修改数量相关提示
    public static final String MESSAGE_CART_CHANGE_CNT_MAX_BUY_COUNT = "限购%s%s，您已购买%s%s，还可购买%s%s";
    public static final String MESSAGE_CART_CHANGE_CNT_MULTIPLE = "购买数量必须是%s的倍数";
    public static final String MESSAGE_CART_CHANGE_CNT_MIN_BUY_COUNT = "%s%s起购";
    public static final String MESSAGE_CART_CHANGE_CNT_LACK_STOCK = "剩余库存%s%s";
    public static final String MESSAGE_CART_CHANGE_CNT_OFF_SALE = "%s商品已下架";
    public static final String MESSAGE_CART_CHANGE_CNT_INVALID = "%s商品已失效";
    public static final String MESSAGE_CART_CHANGE_CNT_GREAT_THAN_ZERO = "%s购买数量必须大于0";


    /**
     * 起始页
     */
    public static final Integer START_PAGE = 1;

    /**
     * 热门销售商品数量
     */
    public static final Integer HOT_SALE_PRODUCT_NUM = 5;

    /**
     * 热门关注商品数量
     */
    public static final Integer HOT_ATTENTION_PRODUCT_NUM = 5;

    /**
     * 最新上架数量
     */
    public static final Integer NEWEST_PRODUCT_NUM = 10;

    /**
     * 猜你喜欢数量
     */
    public static final Integer GUESS_YOU_LIKE_PRODUCT_NUM = 12;

    public static final BigDecimal HUNDRED = new BigDecimal("100");
    public static final BigDecimal TEN = new BigDecimal("10");
    public static final BigDecimal DEFAULT_MIN_AMOUNT = new BigDecimal("0.01");

    public static final int PAGE_ALL_PAGE_NO = 1;
    public static final int PAGE_ALL_PAGE_SIZE = 200;


    public static final int DEFAULT_TRADE_SETTING_WARRANTY_DAYS = 3;

    // 运费模板是否立即发货特殊标识
    public static final String FLAG_DELIVER_IMMEDIATELY = "0";

    public static final int CART_ERROR_MSG_PRODUCT_NAME_LENGTH = 9;
    public static final String CART_ERROR_MSG_PRODUCT_NAME_SHORT = "...";

    /**
     * 默认评论综合评分
     */
    public static final BigDecimal DEFAULT_COMMENT_SCORE = BigDecimal.valueOf(5);

    public static final BigDecimal DEFAULT_COMMENT_GOOD_RATE = BigDecimal.valueOf(100);

    public static final int CATE_DEPTH_3 = 3;
    public static final String CATE_SPLITTER = "|";

    public static final String NULL_STR = "null";

}
