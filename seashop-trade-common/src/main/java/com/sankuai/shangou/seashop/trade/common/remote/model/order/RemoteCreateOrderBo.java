package com.sankuai.shangou.seashop.trade.common.remote.model.order;

import com.sankuai.shangou.seashop.order.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopProductListDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RemoteCreateOrderBo {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 收货地址
     */
    private ShippingAddressDto shippingAddress;
    /**
     * 所有店铺总金额
     */
    private BigDecimal totalAmount;
    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListDto> shopProductList;

}
