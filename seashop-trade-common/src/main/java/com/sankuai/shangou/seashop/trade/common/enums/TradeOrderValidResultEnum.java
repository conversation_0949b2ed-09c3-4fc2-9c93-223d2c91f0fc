package com.sankuai.shangou.seashop.trade.common.enums;

/**
 * <AUTHOR>
 */
public enum TradeOrderValidResultEnum {

    SUCCESS(0, "订单校验通过"),
    ORDER_AMOUNT_NOT_MATCH(50030003, "有店铺活动发生变化，请刷新页面重试"),
    PRODUCT_FAIL(50030001, "商品不满足下单条件，请检查"),

    ;

    private final Integer code;
    private final String message;

    TradeOrderValidResultEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public static boolean anyError(TradeOrderValidResultEnum result) {
        if (result == null) {
            return false;
        }
        return result != SUCCESS;
    }
}
