package com.sankuai.shangou.seashop.trade.common.remote;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentSummaryReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentSummaryResp;
import com.sankuai.shangou.seashop.trade.common.remote.model.order.RemoteProductCommentSummaryBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/25 14:26
 */
@Service
@Slf4j
public class RemoteCommentService {

    @Resource
    private ProductCommentQueryFeign productCommentQueryFeign;


    /**
     * 获取商品评价汇总
     *
     * @param productId 商品id
     * @return 商品评价汇总
     */
    public RemoteProductCommentSummaryBo getProductCommentSummary(Long productId) {
        QueryProductCommentSummaryReq req = new QueryProductCommentSummaryReq();
        req.setProductId(productId);
        ProductCommentSummaryResp resp = ThriftResponseHelper.executeThriftCall(() -> productCommentQueryFeign.queryProductCommentSummary(req));
        return JsonUtil.copy(resp, RemoteProductCommentSummaryBo.class);
    }
}
