package com.sankuai.shangou.seashop.trade.common.constant;

/**
 * <AUTHOR>
 */
public class MessageConst {

    public static class PreOrder {

        public static final String LACK_OF_STOCK = "当前剩余库存：%s件";

        public static final String LESS_THAN_MIN_BUY_COUNT = "%s件起购";

        public static final String OVER_MAX_BUY_COUNT = "限购：%s件";

        public static final String NOT_MATCH_MULTI_BUY_COUNT = "购买数量必须是%s的倍数";

        public static final String PRODUCT_OR_PRICE_UPDATED = "商品或价格信息发生变化，请到购物车重新结算";

        public static final String PRODUCT_NOT_ON_SALE= "商品未上架";

    }

}
