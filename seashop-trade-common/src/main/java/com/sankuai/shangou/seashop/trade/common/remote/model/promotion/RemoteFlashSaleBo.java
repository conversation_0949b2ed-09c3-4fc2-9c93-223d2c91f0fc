package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 限时购bo
 *
 * @author: lhx
 * @date: 2023/12/12/012
 * @description:
 */
@Setter
@Getter
public class RemoteFlashSaleBo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动名称
     */
    private String title;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 活动开始日期
     */
    private Date beginDate;

    /**
     * 活动结束日期
     */
    private Date endDate;

    /**
     * 限购类型:1商品;2规格
     */
    private Integer limitType;

    /**
     * 限购数量
     */
    private Integer limitCount;

    /**
     * 活动id
     */
    private Long categoryId;

    /**
     * 活动分类：默认是限时购分类
     */
    private String categoryName;

    /**
     * 商品主图
     */
    private String imagePath;

    /**
     * 明细列表
     */
    private List<RemoteFlashSaleDetailBo> detailList;

    /**
     * 开始倒计时 为0表示活动已经开始
     */
    private Long startCountDown;

    /**
     * 结束倒计时 为0表示活动已经结束
     */
    private Long endCountDown;

    /**
     * 限时购活动id
     */
    private Long flashSaleActivityId;


}
