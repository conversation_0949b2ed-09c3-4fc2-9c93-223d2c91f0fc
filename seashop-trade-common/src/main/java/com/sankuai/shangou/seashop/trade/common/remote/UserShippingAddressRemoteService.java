package com.sankuai.shangou.seashop.trade.common.remote;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.user.thrift.account.ShippingAddressQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress.GetShippingAddressByIdReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.shippingAddress.DefaultShippingAddressResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class UserShippingAddressRemoteService {

    @Resource
    private ShippingAddressQueryFeign shippingAddressQueryFeign;

    /**
     * 获取用户默认收货地址
     * <AUTHOR>
     * @param userId 登录用户ID
     */
    public ShippingAddressBo getUserDefaultShippingAddress(Long userId) {
        log.info("【收货地址】获取用户默认收货地址, userId={}", userId);
        DefaultShippingAddressResp resp = ThriftResponseHelper.executeThriftCall(
                () -> shippingAddressQueryFeign.getDefaultAddress(userId)
        );
        log.info("【收货地址】获取用户默认收货地址, resp={}", JsonUtil.toJsonString(resp));
        if (resp == null || resp.getDefaultAddress() == null) {
            return null;
        }
        return JsonUtil.copy(resp.getDefaultAddress(), ShippingAddressBo.class);
    }

    /**
     * 获取用户默认收货地址
     * <AUTHOR>
     * @param userId 登录用户ID
     * @param shippingAddressId 收货地址ID
     */
    public ShippingAddressBo getShippingAddress(Long shippingAddressId, Long userId) {
        log.info("【收货地址】获取用户默认收货地址, userId={}, shippingAddressId={}", userId, shippingAddressId);
        GetShippingAddressByIdReq req = new GetShippingAddressByIdReq();
        req.setId(shippingAddressId);
        req.setUserId(userId);
        DefaultShippingAddressResp resp = ThriftResponseHelper.executeThriftCall(
                () -> shippingAddressQueryFeign.getById(req)
        );
        log.info("【收货地址】获取用户默认收货地址, resp={}", JsonUtil.toJsonString(resp));
        if (resp == null || resp.getDefaultAddress() == null) {
            return null;
        }
        return JsonUtil.copy(resp.getDefaultAddress(), ShippingAddressBo.class);
    }
}
