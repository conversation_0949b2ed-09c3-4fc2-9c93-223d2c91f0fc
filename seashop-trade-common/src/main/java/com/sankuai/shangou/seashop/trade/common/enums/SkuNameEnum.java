package com.sankuai.shangou.seashop.trade.common.enums;

/**
 * <AUTHOR>
 */
public enum SkuNameEnum {

    COLOR("spec1", "颜色"),
    SIZE("spec2", "尺寸"),
    VERSION("spec3", "版本"),
    ;

    private final String code;
    private final String desc;

    SkuNameEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }


    public static String appendDesc(SkuNameEnum skuNameEnum, String value) {
        return skuNameEnum.getDesc() + ":" + value;
    }

}
