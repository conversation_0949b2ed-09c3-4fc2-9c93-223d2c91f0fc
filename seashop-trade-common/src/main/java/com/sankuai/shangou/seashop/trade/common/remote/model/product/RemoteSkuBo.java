package com.sankuai.shangou.seashop.trade.common.remote.model.product;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RemoteSkuBo {

    /**
     * 自增主键Id
     */
    private Long id;

    /**
     * 商品ID_规格1ID_规格2ID_规格3ID
     */
    private String skuId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 规格1ID
     */
    private String spec1Value;

    /**
     * 规格2ID
     */
    private String spec2Value;

    /**
     * 规格3ID
     */
    private String spec3Value;

    /**
     * 货号
     */
    private String skuCode;

    /**
     * 成本价
     */
    private BigDecimal costPrice;

    /**
     * 市场价
     */
    private BigDecimal salePrice;

    /**
     * 显示图片
     */
    private String showPic;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 条码图片
     */
    private String barCodeImagePath;

    /**
     * 规格信息
     */
    private String specName;

    /**
     * 库存
     */
    private Long stock;

}
