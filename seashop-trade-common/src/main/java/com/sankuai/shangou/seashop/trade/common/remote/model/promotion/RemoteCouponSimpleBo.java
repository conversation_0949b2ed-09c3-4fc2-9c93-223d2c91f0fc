package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * 优惠券
 *
 * @author: lhx
 * @date: 2023/11/8/008
 * @description:
 */
@Setter
@Getter
public class RemoteCouponSimpleBo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 优惠劵金额
     */
    private BigDecimal price;

    /**
     * 最大可领取张数
     */
    private Integer perMax;

    /**
     * 订单金额（满足多少钱才能使用）
     */
    private BigDecimal orderAmount;

    /**
     * 发行张数
     */
    private Integer num;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 领用人数
     */
    private Integer receiveCount;

    /**
     * 领用张数
     */
    private Integer receiveNum;

    /**
     * 已使用数量
     */
    private Integer useCount;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态名称
     */
    private String statusDesc;

    /**
     * 商品数量
     */
    private Long productCount;

    /**
     * 使用范围：0=全场通用，1=部分商品可用
     */
    private Integer useArea;

    /**
     * 是否已领取
     */
    private boolean userReceived;

    /**
     * 用户领取的数量
     */
    private int userReceivedNum;

    /**
     * 用户已使用数量
     */
    private int userUsedUum;

    /**
     * 领取方式 0 店铺首页 1 积分兑换 2 主动发放
     */
    private Integer receiveType;

}
