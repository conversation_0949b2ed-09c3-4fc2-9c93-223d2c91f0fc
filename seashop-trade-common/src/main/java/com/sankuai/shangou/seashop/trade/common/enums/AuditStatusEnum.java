package com.sankuai.shangou.seashop.trade.common.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/11 14:39
 */
public enum AuditStatusEnum {

    AUDITING(1, "审核中"),
    AUDIT_PASS(2, "审核通过"),
    AUDIT_NOT_PASS(3, "审核不通过"),
    VIOLATION(4, "违规下架"),
    ;

    private final Integer code;
    private final String desc;

    AuditStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {
        for (AuditStatusEnum value : AuditStatusEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
