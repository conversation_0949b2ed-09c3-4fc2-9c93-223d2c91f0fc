package com.sankuai.shangou.seashop.trade.common.enums;

import com.sankuai.shangou.seashop.base.boot.enums.Code;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum TradeResultCode implements Code{


    /**
     * 业务异常定义：xx-xxx-xxx
     * <pre>
     * 一、业务系统区分：020-基础服务；030-交易服务；040-用户服务；050-营销服务；060商品服务；070：订单服务；080：支付服务；
     * 二、前面两位代表错误类型：40-参数校验错误；50-业务逻辑错误；60-系统错误；
     * 三、后面三位代表具体错误码
     * </pre>
     */

    BIZ_PRE_ORDER_VALIDATE_FAIL(50030001, "商品不满足下单条件，请检查"),
    BIZ_CHANGE_QUANTITY_FAIL(50030002, "修改数量不满足条件，前端需要重置数量"),
    BIZ_SUBMIT_ORDER_FAIL_OF_AMOUNT_NOT_MATCH(50030003, "有店铺活动发生变化，请刷新页面重试"),
    BIZ_CHANGE_QUANTITY_FAIL_HOLD(50030004, "修改数量不满足条件，前端保持修改前的数量不需要重置"),

    /***********************************提交订单校验*********************************************/
    BIZ_ORDER_SHOP_NOT_EXIST_ERROR(50030101, "店铺ID:{} 不存在"),

    BIZ_ORDER_SHOP_NOT_STATUS_ERROR(50030102, "店铺:{} 状态:{} 无法交易"),

    BIZ_ORDER_GOODS_NOT_EXIST_ERROR(50030103, "商品:{} 不存在"),

    BIZ_ORDER_GOODS_STATUS_ERROR(50030104, "商品:{} 状态:{} 异常"),

    BIZ_ORDER_GOODS_STOCK_LACK_ERROR(50030105, "商品:{} 库存不足"),

    BIZ_ORDER_GOODS_STOCK_LIMIT_MIN_ERROR(50030106, "商品:{} 起购数量：{}"),

    BIZ_ORDER_GOODS_STOCK_LIMIT_MAX_ERROR(50030107, "商品:{} 限购数量:{}"),

    BIZ_ORDER_GOODS_STOCK_MULTI_BUY_ERROR(50030108, "商品:{} 购买数量必须是{}的倍数"),

    BIZ_GOODS_LIMIT_AREA_ERROR(40303001, "商品在当前区域限购, 无权查看!"),

    BIZ_UPDATE_CART_SELECT_ERROR(40003001, "修改购物车选中状态失败"),

    PARAMS_IS_ILLEGAL(40003008, "参数非法"),
    ;


    private final Integer value;
    private final String desc;

    @Override
    public Integer value() {
        return value;
    }

    @Override
    public String desc() {
        return desc;
    }
}
