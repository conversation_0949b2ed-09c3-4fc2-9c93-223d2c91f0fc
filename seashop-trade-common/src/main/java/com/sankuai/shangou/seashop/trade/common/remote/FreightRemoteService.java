package com.sankuai.shangou.seashop.trade.common.remote;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.CalculateFreightBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.shop.ShopFreightBo;
import com.sankuai.shangou.seashop.user.thrift.account.FreightQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.CalculateFreightReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.freight.CalculateFreightResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FreightRemoteService {

    @Resource
    private FreightQueryFeign freightQueryFeign;

    /**
     * 计算运费，结果以店铺为维度
     * <AUTHOR>
     * @param calculateFreightParam 计算运费参数
     */
    public List<ShopFreightBo> calculateFreight(CalculateFreightBo calculateFreightParam) {
        CalculateFreightReq req = JsonUtil.copy(calculateFreightParam, CalculateFreightReq.class);
        log.info("【运费】计算运费, 请求参数={}", JsonUtil.toJsonString(req));
        CalculateFreightResp resp = ThriftResponseHelper.executeThriftCall(
                () -> freightQueryFeign.calculateFreight(req)
        );
        log.info("【运费】计算运费, 响应结果={}", JsonUtil.toJsonString(resp));
        return JsonUtil.copyList(resp.getShopFreightList(), ShopFreightBo.class);
    }

}
