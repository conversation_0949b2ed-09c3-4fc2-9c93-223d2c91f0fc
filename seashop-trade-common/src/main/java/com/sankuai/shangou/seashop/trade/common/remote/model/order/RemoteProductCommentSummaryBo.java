package com.sankuai.shangou.seashop.trade.common.remote.model.order;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * 评价汇总数据
 *
 * <AUTHOR>
 * @date 2023/12/25 11:01
 */
@Getter
@Setter
public class RemoteProductCommentSummaryBo {

    /**
     * 评价总数
     */
    private Integer totalCount;

    /**
     * 五分评价数
     */
    private Integer fiveStarCount;

    /**
     * 四分评价数
     */
    private Integer fourStarCount;

    /**
     * 三分评价数
     */
    private Integer threeStarCount;

    /**
     * 二分评价数
     */
    private Integer twoStarCount;

    /**
     * 一分评价数
     */
    private Integer oneStarCount;

    /**
     * 有图评价数
     */
    private Integer hasImageCount;

    /**
     * 追加评论数
     */
    private Integer appendCount;

    /**
     * 好评率
     */
    private BigDecimal goodRate;

    /**
     * 中评率
     */
    private BigDecimal middleRate;

    /**
     * 差评率
     */
    private BigDecimal badRate;

    /**
     * 综合评分
     */
    private BigDecimal score;

}
