package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * 专享价bo
 *
 * @author: lhx
 * @date: 2023/11/14/014
 * @description:
 */
@Setter
@Getter
public class ExclusivePriceBo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 活动名称
     */
    private String name;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 专享价商品列表
     */
    private List<ExclusivePriceProductBo> productList;
}
