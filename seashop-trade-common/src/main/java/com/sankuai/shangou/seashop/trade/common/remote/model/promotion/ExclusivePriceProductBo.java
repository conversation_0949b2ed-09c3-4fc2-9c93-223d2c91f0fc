package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * 专享价商品
 *
 * @author: lhx
 * @date: 2023/11/6/006
 * @description:
 */
@Setter
@Getter
public class ExclusivePriceProductBo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 活动ID
     */
    private Long activeId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * skuId
     */
    private String skuId;

    /**
     * sku自增id
     */
    private Long skuAutoId;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * 商城价格
     */
    private BigDecimal mallPrice;

    /**
     * 专享价格
     */
    private BigDecimal price;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 会员名称
     */
    private String userName;

}
