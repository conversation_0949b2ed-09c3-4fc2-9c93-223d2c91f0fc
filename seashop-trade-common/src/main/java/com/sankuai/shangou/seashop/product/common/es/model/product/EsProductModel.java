package com.sankuai.shangou.seashop.product.common.es.model.product;

import java.math.BigDecimal;
import java.util.List;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.product.common.es.model.EsBase;

import lombok.Getter;
import lombok.Setter;

/**
 * 构建交易商品基本信息对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class EsProductModel extends EsBase<Long> {

    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 销量
     */
    private Long saleCount;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 商品所有图片地址，多个图片地址用逗号分隔
     */
    private String allImagePath;
    /**
     * 品牌ID
     */
    private Long brandId;
    /**
     * 分类ID
     */
    private Long categoryId;
    /**
     * 分类全路径
     */
    private String categoryPath;
    /**
     * 市场价
     */
    private BigDecimal marketPrice;
    /**
     * 最小销售价
     */
    private BigDecimal minSalePrice;
    /**
     * 商品主图
     */
    private String mainImagePath;
    /**
     * 上架时间
     */
    private Long onsaleTime;

    /**
     * 添加时间
     */
    private Long addedTime;

    /**
     * 提交审核时间
     */
    private Long submitAuditTime;

    /**
     * 运费模板id
     */
    private Long freightTemplateId;

    /**
     * 是否低于警戒库存
     */
    private Boolean whetherBelowSafeStock;

    /**
     * 平台显示顺序
     */
    private Long displaySequence;

    /**
     * 店铺显示顺序
     */
    private Long shopDisplaySequence;

    /**
     * 是否删除
     */
    private Boolean whetherDelete;

    /**
     * 销售状态 1-销售中 2-仓库中 3-草稿箱
     */
    private Integer saleStatus;

    /**
     * 审核状态 1-审核中 2-审核通过 3-审核不通过 4-违规下架
     */
    private Integer auditStatus;

    /**
     * 收藏数
     */
    private Integer favoriteCount;

    /**
     * 浏览量
     */
    private Integer visitCounts;

    /**
     * 实际销量
     */
    private Long saleCounts;

    /**
     * 虚拟销量
     */
    private Long virtualSaleCounts;

    /**
     * 总销量 实际销量 + 虚拟销量
     */
    private Long totalSaleCounts;

    /**
     * 最大购买数
     */
    private Integer maxBuyCount;

    /**
     * 店铺分类id集合
     */
    private List<Long> shopCategoryIds;

    /**
     * 规格1 别名
     */
    private String spec1Alias;

    /**
     * 规格2 别名
     */
    private String spec2Alias;

    /**
     * 规格3 别名
     */
    private String spec3Alias;

    /**
     * 创建时间
     */
    private Long createTimeStamp;

    /**
     * 修改时间
     */
    private Long updateTimeStamp;

    /**
     * 商品单位
     */
    private String measureUnit;

    /**
     * 是否开启阶梯价
     */
    private Boolean whetherOpenLadder;

    /**
     * 倍数起购量
     */
    private Integer multipleCount;

    /**
     * 是否有sku
     */
    private Boolean hasSku;

    /**
     * 审核原因
     */
    private String auditReason;

    /**
     * 来源 1-商城 2-牵牛花 3-易久批
     */
    private Integer source;

    /**
     * sku自增id集合
     */
    private List<Long> skuAutoIds;

    /**
     * sku编码集合
     */
    private List<String> skuCodes;

    /**
     * 最大销售价
     */
    private BigDecimal maxSalePrice;

    /**
     * 商品总库存
     */
    private Long totalStock;

    /**
     * 规格id的集合
     */
    private List<String> skuIds;

    /**
     * 广告词
     */
    private String shortDescription;

    /**
     * 评价汇总信息
     */
    private EsProductCommentModel commentSummary;

    /**
     * 规格名称id集合
     */
    private List<Long> specNameIds;
    /**
     * OE号
     */
    private String oeCode;
    /**
     * 品牌号
     */
    private String brandCode;
    /**
     * 品牌名称
     */
    private String brandName;
    /**
     * 适配车型
     */
    private String adaptableCar;
    /**
     * 零件品质
     */
    private String partSpec;
    /**
     * 替换号
     */
    private String replaceNumber;
    @Override
    public Long getId() {
        return productId;
    }
}
