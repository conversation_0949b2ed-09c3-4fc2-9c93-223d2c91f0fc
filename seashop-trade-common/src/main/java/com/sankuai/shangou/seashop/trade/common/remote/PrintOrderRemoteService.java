package com.sankuai.shangou.seashop.trade.common.remote;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.thrift.core.PrintOrderDataQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.PrintOrderDataReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderDataResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderItemDataResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 17:10
 */
@Service
@Slf4j
public class PrintOrderRemoteService {

    @Resource
    private PrintOrderDataQueryFeign printOrderDataQueryFeign;

    public List<PrintOrderDataResp> batchQueryOrderByOrderIds(PrintOrderDataReq queryReq) {
        return ThriftResponseHelper.executeThriftCall(() -> printOrderDataQueryFeign.batchQueryOrderByOrderIds(queryReq));
    }

    public List<PrintOrderItemDataResp> batchQueryOrderItemByOrderIds(PrintOrderDataReq queryReq) {
        return ThriftResponseHelper.executeThriftCall(() -> printOrderDataQueryFeign.batchQueryOrderItemByOrderIds(queryReq));
    }
}
