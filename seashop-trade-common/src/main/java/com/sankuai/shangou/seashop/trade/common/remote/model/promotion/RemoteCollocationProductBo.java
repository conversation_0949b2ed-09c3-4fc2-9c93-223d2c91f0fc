package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 9:50
 */
@Setter
@Getter
public class RemoteCollocationProductBo {

    /**
     * 组合购商品ID
     */
    private Long id;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 商品主图
     */
    private String imagePath;

    /**
     * 组合购ID
     */
    private Long colloId;

    /**
     * 是否主商品
     */
    private Boolean mainFlag;

    /**
     * 排序
     */
    private Integer displaySequence;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 组合购商品SKU集合
     */
    private List<RemoteCollocationSkuBo> skuRespList;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 最低售价
     */
    private BigDecimal minSalePrice;

    /**
     * 原价
     */
    private BigDecimal originalPrice;

    /**
     * 是否有sku
     */
    private Boolean hasSku;

    /**
     * 规格id的集合
     */
    private List<String> skuIds;

}
