package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 优惠券活动响应体
 */
@Getter
@Setter
public class RemoteCouponBo {

    /**
     * 主键ID。领用记录ID
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 面值(价格)
     */
    private Long price;

    /**
     * 订单金额（满足多少钱才能使用）
     */
    private Long orderAmount;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 优惠券活动ID
     */
    private Long couponId;
    /**
     * 优惠券名称
     */
    private String couponName;
    /**
     * 优惠券优惠码
     */
    private String couponSn;

    /**
     * 使用范围：0=全场通用，1=部分商品可用
     */
    private Integer useArea;
    /**
     * 适用商品范围
     */
    private List<Long> productIdList;
    /**
     * 用户ID
     */
    private Long userId;
}
