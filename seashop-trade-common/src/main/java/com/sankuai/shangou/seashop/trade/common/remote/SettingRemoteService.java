package com.sankuai.shangou.seashop.trade.common.remote;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.TradeSettingsQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.TradeSiteSettingsResp;
import com.sankuai.shangou.seashop.trade.common.remote.model.base.ShopTradeSettingBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.user.thrift.shop.OrderSettingQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBatchOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryOrderSettingResp;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SettingRemoteService {

    @Resource
    private OrderSettingQueryFeign orderSettingQueryFeign;
    @Resource
    private TradeSettingsQueryFeign tradeSettingsQueryFeign;


    /**
     * 获取店铺配置的交易设置
     * @param shopIdList 店铺id
     */
    public Map<Long, ShopTradeSettingBo> getShopTradeSetting(List<Long> shopIdList) {
        QueryBatchOrderSettingReq req = new QueryBatchOrderSettingReq();
        req.setShopIdList(shopIdList);
        log.info("【设置】获取店铺配置的交易设置, 请求参数={}", JsonUtil.toJsonString(req));
        List<QueryOrderSettingResp> respList = ThriftResponseHelper.executeThriftCall(() -> orderSettingQueryFeign.queryBatchOrderSetting(req));
        log.info("【设置】获取店铺配置的交易设置, 响应参数={}", JsonUtil.toJsonString(respList));
        List<ShopTradeSettingBo> settingList = JsonUtil.copyList(respList, ShopTradeSettingBo.class);
        if (CollUtil.isEmpty(settingList)) {
            return null;
        }
        return settingList.stream().collect(Collectors.toMap(ShopTradeSettingBo::getShopId, setting -> setting, (k1, k2) -> k2));
    }

    /**
     * 获取平台配置的交易设置
     * <AUTHOR>
     *
     */
    public TradeSiteSettingBo getTradeSiteSetting() {
        TradeSiteSettingsResp resp = ThriftResponseHelper.executeThriftCall(() -> tradeSettingsQueryFeign.queryTradeSiteSetting());
        log.info("获取平台配置的交易设置:{}", JsonUtil.toJsonString(resp));
        return JsonUtil.copy(resp, TradeSiteSettingBo.class);
    }

}
