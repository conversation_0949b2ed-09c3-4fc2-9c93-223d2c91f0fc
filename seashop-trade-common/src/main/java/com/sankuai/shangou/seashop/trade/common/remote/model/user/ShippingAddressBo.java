package com.sankuai.shangou.seashop.trade.common.remote.model.user;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ShippingAddressBo {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 区域ID
     */
    private Integer regionId;
    /**
     * 收货人
     */
    private String shipTo;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 详细地址
     */
    private String addressDetail;
    /**
     * 收货人电话
     */
    private String phone;
    /**
     * 区域全路径
     */
    private String regionPath;
    /**
     * 区域全称
     */
    private String regionFullName;
    /**
     * 收货人坐标
     */
    private BigDecimal receiveLongitude;
    /**
     * 收货人坐标
     */
    private BigDecimal receiveLatitude;

    private Long provinceId;
    private String provinceName;
    private Long cityId;
    private String cityName;
    private String districtName;
    private String streetName;

}
