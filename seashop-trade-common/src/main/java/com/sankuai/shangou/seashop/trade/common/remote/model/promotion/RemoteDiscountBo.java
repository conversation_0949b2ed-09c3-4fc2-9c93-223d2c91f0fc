package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RemoteDiscountBo {

    /**
     * 折扣活动ID
     */
    private Long discountActId;
    /**
     * 折扣活动名称
     */
    private String discountActName;
    /**
     * 是否用于全部商品
     */
    private Boolean izAllProduct;
    /**
     * 折扣规则
     */
    private List<RemoteDiscountRuleBo> ruleList;
    /**
     * 活动适用的商品
     */
    private List<Long> productIdList;
    private Date startTime;
    private Date endTime;

}
