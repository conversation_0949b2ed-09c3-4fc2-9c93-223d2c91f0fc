package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class RemoteFlashSaleDetailBo {

    /**
     * 限时购活动ID
     */
    private Long flashSaleId;
    /**
     * 限时购名称
     */
    private String flashSaleName;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 限时购价格
     */
    private BigDecimal price;
    /**
     * 限时购库存，如果是商品维度的限时购，这个字段设置为空
     */
    private Integer totalCount;
    /**
     * 每个用户购买限制数量
     */
    private Integer limitCount;
    /**
     * 限时购类型。1：商品维度；2：sku维度
     */
    private Integer limitType;
    /**
     * 规格名称
     */
    private String skuName;

    /**
     * 规格1别名
     */
    private String spec1Alias;

    /**
     * 规格2别名
     */
    private String spec2Alias;

    /**
     * 规格3别名
     */
    private String spec3Alias;

    /**
     * 销售价
     */
    private BigDecimal salePrice;
    /**
     * 商品主图
     */
    private String imagePath;
    /**
     * 商品库存
     */
    private Long stock;

}
