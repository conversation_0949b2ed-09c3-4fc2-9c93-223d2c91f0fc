package com.sankuai.shangou.seashop.trade.common.util;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
public class SkuUtil {

    public static final String DEFAULT_SPEC_ALIAS = "规格";

    /**
     * 组装sku名称
     * <p>历史原因，数据库的字段名是 color，size，version，但是规格的别名是可以自定义的，并不一定是颜色、尺寸、版本，
     * 且订单只保存的了具体的规格值，所以这里只拼接具体的值</p>
     * @return 拼接的规格名称
     */
    public static String appendDesc(String specAlias, String value) {
        if (StrUtil.isBlank(specAlias) || "null".equals(specAlias)) {
            specAlias = DEFAULT_SPEC_ALIAS;
        }
        return specAlias + ":" + value;
    }

}
