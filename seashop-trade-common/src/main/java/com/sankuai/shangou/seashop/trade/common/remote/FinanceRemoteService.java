package com.sankuai.shangou.seashop.trade.common.remote;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayCreateResp;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositDetailCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositDetailQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositRefundQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.FinanceQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.PendingSettlementQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.SettledQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreatePaymentReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.OrderIdQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettlementDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceIndexResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementOrderResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledResp;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@Service
public class FinanceRemoteService {


    @Resource
    private CashDepositQueryFeign cashDepositQueryFeign;

    @Resource
    private CashDepositDetailQueryFeign cashDepositDetailQueryFeign;

    @Resource
    private CashDepositCmdFeign cashDepositCmdFeign;

    @Resource
    private CashDepositDetailCmdFeign cashDepositDetailCmdFeign;

    @Resource
    private CashDepositRefundQueryFeign cashDepositRefundQueryFeign;


    @Resource
    private FinanceQueryFeign financeQueryFeign;

    @Resource
    private PendingSettlementQueryFeign pendingSettlementQueryFeign;

    @Resource
    private SettledQueryFeign settledQueryFeign;

    public CashDepositResp queryOneByShopId(Long shopId) {
        ShopIdReq shopIdReq = new ShopIdReq();
        shopIdReq.setShopId(shopId);
        shopIdReq.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> cashDepositQueryFeign.queryOneByShopId(shopIdReq));
    }

    public BasePageResp<CashDepositDetailResp> pageList(CashDepositDetailQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> cashDepositDetailQueryFeign.pageList(request));
    }

    public OrderPayCreateResp createPayment(CreatePaymentReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> cashDepositCmdFeign.createPayment(request));
    }


    /**
     * 获取财务首页数据
     *
     * @return
     */
    public FinanceIndexResp getFinanceIndex(Long shopId) {
        BaseIdReq request = new BaseIdReq();
        request.setId(shopId);
        return ThriftResponseHelper.executeThriftCall(() -> financeQueryFeign.getFinanceIndex(request));
    }

    /**
     * 获取待结算列表
     *
     * @param request
     * @return
     */
    public BasePageResp<PendingSettlementResp> getPendingSettlementList(PendingSettlementQryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> pendingSettlementQueryFeign.getPendingSettlementList(request));
    }

    /**
     * 分页查询待结算订单列表
     *
     * @param request
     * @return
     */
    public BasePageResp<PendingSettlementOrderResp> pendingSettlementOrderPageList(PendingSettlementOrderQryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> pendingSettlementQueryFeign.pageList(request));
    }

    /**
     * 根据订单id查询订单详情
     *
     * @param orderId
     * @return
     */
    public PendingSettlementOrderResp getPendingSettlementOrderDetailByOrderId(String orderId) {
        OrderIdQryReq request = new OrderIdQryReq();
        request.setOrderId(orderId);
        return ThriftResponseHelper.executeThriftCall(() -> pendingSettlementQueryFeign.getDetailByOrderId(request));
    }

    /**
     * 已结算列表查询
     *
     * @param request
     * @return
     */
    public BasePageResp<SettledResp> settledPageList(SettledQryReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> settledQueryFeign.pageList(request));
    }

    /**
     * 已结算明细列表查询
     *
     * @param request
     * @return
     */
    public BasePageResp<SettledItemResp> settledItemPageList(SettledItemQryReq request) {
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> settledQueryFeign.itemPageList(request));
    }

    /**
     * 通过订单id查询已结算明细
     *
     * @param orderId
     * @return
     */
    public SettlementDetailResp getSettledDetailByOrderId(String orderId) {
        OrderIdQryReq request = new OrderIdQryReq();
        request.setOrderId(orderId);
        request.checkParameter();
        return ThriftResponseHelper.executeThriftCall(() -> settledQueryFeign.getDetailByOrderId(request));
    }

    /**
     * 保证金退款申请查询
     *
     * @param request
     * @return
     */
    public BasePageResp<CashDepositRefundResp> queryCashDepositRefundList(CashDepositRefundQueryReq request) {
        return ThriftResponseHelper.executeThriftCall(() -> cashDepositRefundQueryFeign.refundList(request));
    }

    public CashDepositRefundDetailResp refundDetail(Long id) {
        return ThriftResponseHelper.executeThriftCall(() -> cashDepositRefundQueryFeign.refundDetail(id));
    }
}
