package com.sankuai.shangou.seashop.trade.common.remote;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.user.thrift.shop.FavoriteProductQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFavoriteProductStatusReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryProductFavoriteCountReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFavoriteProductStatusResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryProductFavoriteCountResp;

/**
 * <AUTHOR>
 * @date 2023/12/12 10:07
 */
@Service
public class UserFavoriteRemoteService {

    @Resource
    private FavoriteProductQueryFeign favoriteProductQueryFeign;

    /**
     * 获取商品收藏数
     *
     * @param productId 商品id
     * @return 收藏数
     */
    public Integer getFavoriteCount(Long productId) {
        QueryProductFavoriteCountReq req = new QueryProductFavoriteCountReq();
        req.setProductId(productId);
        QueryProductFavoriteCountResp resp = ThriftResponseHelper.executeThriftCall(() -> favoriteProductQueryFeign.queryFavoriteProductCount(req));
        return resp.getFavoriteCount();
    }

    /**
     * 获取商品收藏状态
     *
     * @param productId 商品id
     * @param userId    用户id
     * @return true 已收藏，false 未收藏
     */
    public Boolean getFavoriteStatus(Long productId, Long userId) {
        if (userId == null) {
            return Boolean.FALSE;
        }

        QueryFavoriteProductStatusReq req = new QueryFavoriteProductStatusReq();
        req.setProductId(productId);
        req.setUserId(userId);
        QueryFavoriteProductStatusResp resp = ThriftResponseHelper.executeThriftCall(() -> favoriteProductQueryFeign.queryFavoriteProductStatus(req));
        return resp.getFavoriteStatus();
    }
}
