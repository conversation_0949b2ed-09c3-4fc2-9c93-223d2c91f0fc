package com.sankuai.shangou.seashop.trade.common.enums;

/**
 * 订单使用的优惠类型
 * LiGuoQiang
 */
public enum OrderPromotionType {

    EXCLUSIVE_PRICE("专享价"),
    LADDER_PRICE("阶梯价"),
    DISCOUNT("折扣"),
    REDUCTION("满减"),
    COUPON("优惠券"),
    FLASH_SALE("限时购"),
    COLLOCATION("组合购"),


    ;

    private final String desc;

    OrderPromotionType(String desc) {
        this.desc = desc;
    }

    public String getDesc() {
        return desc;
    }
}
