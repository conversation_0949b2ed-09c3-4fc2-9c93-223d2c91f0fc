package com.sankuai.shangou.seashop.trade.common.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
public enum ProductSortFieldMappingEnum {

    SALE_COUNT("saleCount", "totalSaleCounts", "成交量"),
    PRICE("minSalePrice", "minSalePrice", "价格"),
    COMMENT_COUNT("commentCount", "commentSummary.totalCount", "评论数"),
    ON_SALE_TIME("onsaleTime", "onsaleTime", "上架时间"),

    VISIT_COUNTS("visitCounts", "visitCounts", "访问量"),
    FAVORITE_COUNT("favoriteCount", "favoriteCount", "收藏数"),
    TOTAL_SALE_COUNTS("totalSaleCounts", "totalSaleCounts", "总成交量"),
    PRODUCT_ID("productId", "productId", "商品ID"),
    PRODUCT_SEQUENCE("displaySequence", "displaySequence", "商品序号"),
    SHOP_PRODUCT_SEQUENCE("shopDisplaySequence", "shopDisplaySequence", "店铺商品序号"),
    SCORE("_score", "_score", "得分");

    private final String paramName;
    private final String esFieldName;
    private final String desc;

    ProductSortFieldMappingEnum(String paramName, String esFieldName, String desc) {
        this.paramName = paramName;
        this.esFieldName = esFieldName;
        this.desc = desc;
    }

    public String getParamName() {
        return paramName;
    }

    public String getEsFieldName() {
        return esFieldName;
    }

    public String getDesc() {
        return desc;
    }

    public static String getEsFieldName(String paramName) {
        return Arrays.stream(values())
                .filter(e -> e.getParamName().equals(paramName))
                .findAny()
                .map(ProductSortFieldMappingEnum::getEsFieldName)
                .orElse("");
    }
}
