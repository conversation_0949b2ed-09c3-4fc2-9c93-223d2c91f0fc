package com.sankuai.shangou.seashop.trade.common.remote;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.RegionQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseRegionRes;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author： liweisong
 * @create： 2023/12/16 14:02
 */
@Service
@Slf4j
public class BaseRegionService {

    @Resource
    private RegionQueryFeign regionQueryFeign;

    public Map<String, AllPathRegionResp> getAllPathRegions(RegionIdsReq regionIdsReq){
        return ThriftResponseHelper.executeThriftCall(()->regionQueryFeign.getAllPathRegions(regionIdsReq));
    }

    /**
     * 根据区域id获取区域全名
     * @param regionId 区域id
     * @return 区域全名
     */
    public String getRegionFullName(Integer regionId){
        List<BaseRegionRes> regionRes = ThriftResponseHelper.executeThriftCall(()->regionQueryFeign.getParentRegions(regionId.longValue()));
        //判空
        if (CollUtil.isEmpty(regionRes)) {
            return "";
        }
        //排序 从小到大
        regionRes.sort(Comparator.comparingInt(BaseRegionRes::getRegionLevel));
        //获取区域全名
        StringBuilder regionFullName = new StringBuilder();
        for (int i = regionRes.size() - 1; i >= 0; i--) {
            regionFullName.append(regionRes.get(i).getName());
        }
        return regionFullName.toString();
    }

    /**
     * 根据区域id获取区域列表
     * @param regionId 区域id
     * @return 区域全名
     */
    public List<BaseRegionRes> getFullList(Integer regionId){
        List<BaseRegionRes> regionRes = ThriftResponseHelper.executeThriftCall(()->regionQueryFeign.getParentRegions(regionId.longValue()));
        //判空
        if (CollUtil.isEmpty(regionRes)) {
            return Collections.emptyList();
        }
        return regionRes;
    }
}
