package com.sankuai.shangou.seashop.trade.common.exception;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;

/**
 * 变更数量异常
 * <p>购物车与订单预览页，修改数量时的异常，返回的时候需要返回匹配的数量</p>
 * <AUTHOR>
 */
public class ChangeQuantityException extends BusinessException {

    private final String productNameDesc;
    private final Long suitQuantity;
    private final String errMsg;

    public ChangeQuantityException(int code, String productNameDesc, String errMsg, Long suitQuantity) {
        super(code, productNameDesc + errMsg);
        this.suitQuantity = suitQuantity;
        this.productNameDesc = productNameDesc;
        this.errMsg = errMsg;
    }

    public Long getSuitQuantity() {
        return suitQuantity;
    }

    public String getProductNameDesc() {
        return productNameDesc;
    }

    public String getErrMsg() {
        return errMsg;
    }
}
