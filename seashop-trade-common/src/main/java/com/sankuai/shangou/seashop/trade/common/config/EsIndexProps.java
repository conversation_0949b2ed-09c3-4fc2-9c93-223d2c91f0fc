package com.sankuai.shangou.seashop.trade.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class EsIndexProps {

    /**
     * 分词匹配比例
     */
    @Value("${es.index.minimum_should_proportion:0.8}")
    private Float minimumShouldProportion;


    public Float getMinimumShouldProportion() {
        return minimumShouldProportion;
    }

    public void setMinimumShouldProportion(Float minimumShouldProportion) {
        this.minimumShouldProportion = minimumShouldProportion;
    }
}
