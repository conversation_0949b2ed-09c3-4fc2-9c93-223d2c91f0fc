package com.sankuai.shangou.seashop.trade.common.remote.model.shop;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class CalculateFreightProductBo {

    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 运费模板ID
     */
    private Long templateId;
    /**
     * 重量
     */
    private BigDecimal weight;
    /**
     * 体积
     */
    private BigDecimal volume;
    /**
     * 商品总金额
     */
    private BigDecimal productAmount;
    /**
     * 购买数量
     */
    private Long buyCount;
    /**
     * 商品名称，用于异常提示
     */
    private String productName;

}
