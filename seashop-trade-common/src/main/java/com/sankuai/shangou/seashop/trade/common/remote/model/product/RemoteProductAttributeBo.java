package com.sankuai.shangou.seashop.trade.common.remote.model.product;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RemoteProductAttributeBo {

    /**
     * 属性id
     */
    private Long attributeId;

    /**
     * 属性名称
     */
    private String attributeName;

    /**
     * 属性值集合
     */
    private List<ProductAttributeValueBo> attributeValueList;

}
