package com.sankuai.shangou.seashop.trade.common.remote.model.promotion;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class RemoteShopUserPromotionBo {

    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺折扣对象
     * 同一个时间点，可以有多个折扣活动，每个活动配置不同的商品，
     */
    private List<RemoteDiscountBo> shopDiscountList;
    /**
     * 店铺满减活动对象
     */
    private RemoteReductionBo shopReduction;
    /**
     * 店铺专享价列表
     * 同一个时间点，可以有多个专享价活动，每个活动配置不同的商品，
     */
    private List<RemoteShopExclusivePriceBo> shopExclusivePriceList;

}
