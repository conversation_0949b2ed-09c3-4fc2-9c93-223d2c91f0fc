package com.sankuai.shangou.seashop.trade.common.remote;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdListReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositListResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositResp;
import com.sankuai.shangou.seashop.product.thrift.core.CategoryCashDepositQueryFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.category.QueryDepositConfigBySkuReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.SkuFitCategoryCashDepositListResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.SkuFitCategoryCashDepositResp;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 保证金相关远程接口
 * <AUTHOR>
 */
@Service
@Slf4j
public class CashDeposit2TradeRemoteService {

    @Resource
    private CategoryCashDepositQueryFeign categoryCashDepositQueryFeign;
    @Resource
    private CashDepositQueryFeign cashDepositQueryFeign;

    // todo 服务合并，不再使用，改为直接调用本地服务
    /**
     * 查询sku对应类目的保证金配置。保证金配置在一级类目上
     * @param skuIdList skuId列表
     */
    public List<SkuFitCategoryCashDepositResp> getSkuFitCateConfig(List<String> skuIdList) {
        QueryDepositConfigBySkuReq request = new QueryDepositConfigBySkuReq();
        request.setSkuIdList(skuIdList);
        log.info("【保证金】查询sku对应类目的保证金配置, 请求参数={}", JsonUtil.toJsonString(request));
        SkuFitCategoryCashDepositListResp resp = ThriftResponseHelper.executeThriftCall(() -> categoryCashDepositQueryFeign.queryBySkuList(request));
        log.info("【保证金】查询sku对应类目的保证金配置, 返回结果={}", JsonUtil.toJsonString(resp));
        if (resp == null) {
            return null;
        }
        return resp.getSkuCateDepositList();
    }

    /**
     * 查询sku对应类目的保证金配置。保证金配置在一级类目上
     * @param skuIdList skuId列表
     */
    public Map<String, SkuFitCategoryCashDepositResp> getSkuFitCateConfigMap(List<String> skuIdList) {
        List<SkuFitCategoryCashDepositResp> list = getSkuFitCateConfig(skuIdList);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(SkuFitCategoryCashDepositResp::getSkuId, v -> v, (v1, v2) -> v1));
    }

    /**
     * 查询店铺保证金
     * @param shopIdList 店铺id列表
     */
    public List<CashDepositResp> getShopDeposit(List<Long> shopIdList) {
        ShopIdListReq req = new ShopIdListReq();
        req.setShopIdList(shopIdList);
        log.info("【保证金】查询店铺保证金, 请求参数={}", JsonUtil.toJsonString(req));
        CashDepositListResp resp = ThriftResponseHelper.executeThriftCall(() -> cashDepositQueryFeign.queryByShopIdList(req));
        log.info("【保证金】查询店铺保证金, 返回结果={}", JsonUtil.toJsonString(resp));
        if (resp == null) {
            return null;
        }
        return resp.getList();
    }

    /**
     * 查询店铺保证金
     * @param shopIdList 店铺id列表
     */
    public Map<Long, CashDepositResp> getShopDepositMap(List<Long> shopIdList) {
        List<CashDepositResp> list = getShopDeposit(shopIdList);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(CashDepositResp::getShopId, v -> v, (v1, v2) -> v1));
    }

}
