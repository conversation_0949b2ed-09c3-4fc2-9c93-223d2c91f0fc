package com.sankuai.shangou.seashop.trade.common.remote;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.RestrictedRegionBo;
import com.sankuai.shangou.seashop.user.thrift.shop.FreightAreaQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryRestrictedRegionReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryRestrictedRegionResp;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/04 15:30
 */
@Service
@Slf4j
public class FreightTemplateService {

    @Resource
    private FreightAreaQueryFeign freightAreaQueryFeign;

    /**
     * 查询限售区域
     */
    public RestrictedRegionBo queryRestrictedRegion(Long freightTemplateId) {
        QueryRestrictedRegionReq req = new QueryRestrictedRegionReq();
        req.setFreightTemplateId(freightTemplateId);
        QueryRestrictedRegionResp resp = ThriftResponseHelper.executeThriftCall(() -> freightAreaQueryFeign.queryRestrictedRegion(req));
        return JsonUtil.copy(resp, RestrictedRegionBo.class);
    }

    /**
     * 是否在限购区域
     *
     * @param freightTemplateId 运费模板id
     * @param regionIds         区域id
     * @return true:在限购区域
     */
    public Boolean isRestrictedRegion(Long freightTemplateId, List<Long> regionIds) {
        if (CollectionUtils.isEmpty(regionIds)) {
            return Boolean.FALSE;
        }

        RestrictedRegionBo restrictedRegionBo = queryRestrictedRegion(freightTemplateId);
        if (restrictedRegionBo == null || CollectionUtils.isEmpty(restrictedRegionBo.getRestrictedRegionIds())) {
            return Boolean.FALSE;
        }
        return restrictedRegionBo.getRestrictedRegionIds().stream().filter(item -> regionIds.contains(item)).findFirst().isPresent();
    }

    /**
     * 根据模板ID查询运费模板列表
     */
    public List<QueryFreightTemplateDto> getFreightTplByIdList(List<Long> tplIdList) {
        BaseBatchIdReq req = new BaseBatchIdReq();
        req.setId(tplIdList);
        log.info("【查询运费模板】参数tplIdList={}", JsonUtil.toJsonString(req));
        QueryFreightTemplateResp resp = ThriftResponseHelper.executeThriftCall(() -> freightAreaQueryFeign.queryTplByTemplateIdList(req));
        log.info("【查询运费模板】结果resp={}", JsonUtil.toJsonString(resp));
        if (resp == null || CollUtil.isEmpty(resp.getResult())) {
            return Collections.emptyList();
        }
        return resp.getResult();
    }

    /**
     * 根据模板ID查询运费模板列表
     */
    public Map<Long, QueryFreightTemplateDto> getFreightTplByIdListMap(List<Long> tplIdList) {
        List<QueryFreightTemplateDto> list = getFreightTplByIdList(tplIdList);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(QueryFreightTemplateDto::getId, v -> v, (v1, v2) -> v2));
    }

}
