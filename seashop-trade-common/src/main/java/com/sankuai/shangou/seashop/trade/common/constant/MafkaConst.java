package com.sankuai.shangou.seashop.trade.common.constant;

/**
 * <AUTHOR>
 */
public class MafkaConst {

    // mafka命名空间，用于区分不同的业务，这里固定为waimai
    public static final String DEFAULT_NAMESPACE = "waimai";

    /*
     * topic名称，用于区分不同的业务
     */
    // 商品信息更新topic
    public static final String TOPIC_PRODUCT_UPDATE = "seashop_product_dts_topic";
    // 店铺信息更新topic
    //public static final String TOPIC_SHOP_UPDATE = "seashop_shop_dts_topic";
    // 下单成功删除购物车
    public static final String TOPIC_REMOVE_SHOPPING_CART = "seashop_remove_shopping_cart_topic";

    /*
     * 消费者组名称，用于区分topic的不同用途，在kafka的实现中，
     * 通过消费者组实现发布订阅，不同的消费者组可以消费同一条消息；但同一条消息只会被同一个消费者组消费一次
     */
    // 交易商品构建到ES中的消费者组
    public static final String GROUP_ES_PRODUCT_BUILD = "seashop_product_dts_consumer";
    // 店铺构建到ES中的消费者组
    //public static final String GROUP_ES_SHOP_BUILD = "seashop_shop_dts_consumer";
    // 下单成功删除购物车
    public static final String GROUP_REMOVE_SHOPPING_CART = "seashop_remove_shopping_cart_consumer";

    /**
     * 更新商品收藏数topic
     */
    public static final String TOPIC_PRODUCT_COLLECTION = "seashop_user_favorite_dts_topic";

    /**
     * 更新商品收藏数消费者组
     */
    public static final String GROUP_PRODUCT_COLLECTION = "seashop_user_favorite_dts_consumer";

    /**
     * 更新商品浏览数topic
     */
    public static final String TOPIC_PRODUCT_VISIT = "seashop_product_visit_topic";

    /**
     * 更新商品浏览数消费者组
     */
    public static final String GROUP_PRODUCT_VISIT = "seashop_product_visit_consumer";

    /**
     * 更新商品评论数topic
     */
    public static final String TOPIC_PRODUCT_COMMENT = "seashop_order_product_comment_dts_topic";

    /**
     * 更新商品评论数消费者组
     */
    public static final String GROUP_PRODUCT_COMMENT = "seashop_order_product_comment_dts_consumer";

}
