package com.sankuai.shangou.seashop.trade.common.constant;

/**
 * <AUTHOR>
 */
public class CacheConst {

    private static final String CACHE_PREFIX = "seashop:trade:cache:";

    public static final String CACHE_ORDER_SUBMIT_TOKEN = CACHE_PREFIX + "order:submit:token:";

    // 提交订单token的有效时间，先设置为30分钟，单位是秒
    public static final int EXPIRE_SUBMIT_ORDER_TOKEN_60_MIN = 60 * 60;

    /**
     * 商品访问量 messageId 标识
     */
    public static final String PRODUCT_VISIT_COUNT_MESSAGE_ID = "product_visit_count:";

    /**
     * 商品访问量 messageId 标识过期时间
     */
    public static final Integer PRODUCT_VISIT_COUNT_MESSAGE_ID_EXPIRE_TIME = 2 * 60 * 60;

}
