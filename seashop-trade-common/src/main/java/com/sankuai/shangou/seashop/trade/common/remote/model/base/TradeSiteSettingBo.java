package com.sankuai.shangou.seashop.trade.common.remote.model.base;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 交易参数设置返参
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class TradeSiteSettingBo {

    /**
     * 未付款超时
     */
    private String unpaidTimeout;

    /**
     * 确认收货超时
     */
    private String noReceivingTimeout;

    /**
     * 自动收货完成前时间
     */
    private String beforeReceivingDays;

    /**
     * 延迟收货时间
     */
    private String noReceivingDelayDays;

    /**
     * 关闭评价通道时限
     */
    private String orderCommentTimeout;

    /**
     * 供应商未发货自动短信提醒时限
     */
    private String orderWaitDeliveryRemindTime;

    /**
     * 企业网银限制金额
     */
    private String companyBankOrderAmount;

    /**
     * 订单退货期限
     */
    private String salesReturnTimeout;

    /**
     * 供应商自动确认售后时限
     */
    private String shopConfirmTimeout;

    /**
     * 用户发货限时
     */
    private String sendGoodsCloseTimeout;

    /**
     * 供应商确认到货时限
     */
    private String shopNoReceivingTimeout;

}
