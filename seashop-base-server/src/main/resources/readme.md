# 新环境部署

### 一、数据库初始化脚本

1. 库表结构sql,在目录/static/DML/*.sql
2. 初始化用户数据，在目录static/DML/初始化数据.sql
   - 微信和维护根据需求2选1. himall_order_pay_channel_config_微信支付初始化 | himall_order_pay_channel_config_汇付支付初始化 
3. ES 初始化脚本: 在目录static/ES_INDEX/*_index.txt
    - 注意事项： 环境变量.*_index 索引是意思是创建的索引名称： {env}.*_index, 例如 develop.user_index | test.user_index |
      prod.user_index
    - shop_index 无需区分环境
    - #### 注意生产环境：分片和副本参数调整
4. MQ topic|consumer 如果topic 需要手动创建，请参考MQ 的topic 列表
5. xxl-job 定时任务列表
6. base_site_setting表初始化注意事项
   - hi客服: site_url字段的值应该修改为接口地址 否则在客服中无法正常访问系统接口 会出现不能发送商品以及优惠券的问题
   
### 二、nacos 测试域名指向修改

1，注意himall-common.yml 是否存在此配置，会被测试路径映射修改。需要注释掉

```properties
# 测试服务路径
# hishop:
#   develop:
#     enabled: true
#     default-url: https://himall.cce.35hiw.com
```

2，更换小程序配置: himall-base.yml 中修改对应的值，然后在PC平台中小程序配置修改对应值

```properties
wx:
appKey:wx5a538cdb4b7b286d
appSecret:8517959f6e6dc5537995ae4a643d3649
```