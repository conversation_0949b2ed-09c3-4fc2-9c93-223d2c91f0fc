# himallWork部署运维指南

## 1. 环境准备

### 1.1 基础环境要求

#### 服务器配置
| 环境 | CPU | 内存 | 磁盘 | 数量 |
|------|-----|------|------|------|
| 开发环境 | 4核 | 8GB | 100GB | 1台 |
| 测试环境 | 8核 | 16GB | 200GB | 2台 |
| 生产环境 | 16核 | 32GB | 500GB | 4台+ |

#### 软件版本要求
| 软件 | 版本 | 说明 |
|------|------|------|
| JDK | 1.8+ | 推荐使用OpenJDK 11 |
| Maven | 3.6+ | 项目构建工具 |
| MySQL | 5.7+ | 主数据库 |
| Redis | 5.0+ | 缓存和分布式锁 |
| Elasticsearch | 7.x | 搜索引擎 |
| RocketMQ | 4.9+ | 消息队列 |
| Nginx | 1.18+ | 反向代理和负载均衡 |

### 1.2 网络规划

#### 端口规划
| 服务 | 端口 | 说明 |
|------|------|------|
| himall-gw | 8080 | 网关服务 |
| himall-base | 8081 | 基础服务 |
| himall-trade | 8082 | 交易服务 |
| himall-order | 8083 | 订单服务 |
| himall-report | 8084 | 报表服务 |
| MySQL | 3306 | 数据库 |
| Redis | 6379 | 缓存 |
| Elasticsearch | 9200 | 搜索引擎 |
| RocketMQ | 9876 | 消息队列 |

#### 网络安全组配置
```bash
# 开放应用端口（仅内网访问）
iptables -A INPUT -p tcp --dport 8080:8084 -s 10.0.0.0/8 -j ACCEPT

# 开放数据库端口（仅应用服务器访问）
iptables -A INPUT -p tcp --dport 3306 -s ********/24 -j ACCEPT
iptables -A INPUT -p tcp --dport 6379 -s ********/24 -j ACCEPT

# 开放HTTP/HTTPS端口（公网访问）
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

---

## 2. 基础组件部署

### 2.1 MySQL数据库部署

#### 主从架构部署
```bash
# 主库配置 /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
server-id = 1
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON
binlog-do-db = himall_base,himall_trade,himall_order,himall_report

# 从库配置
[mysqld]
server-id = 2
relay-log = mysql-relay-bin
read-only = 1
gtid-mode = ON
enforce-gtid-consistency = ON
```

#### 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE himall_base DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE himall_trade DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE himall_order DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE himall_report DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建应用用户
CREATE USER 'himall'@'%' IDENTIFIED BY 'HimallPass123!';
GRANT ALL PRIVILEGES ON himall_*.* TO 'himall'@'%';
FLUSH PRIVILEGES;

-- 创建只读用户（用于从库）
CREATE USER 'himall_read'@'%' IDENTIFIED BY 'HimallRead123!';
GRANT SELECT ON himall_*.* TO 'himall_read'@'%';
FLUSH PRIVILEGES;
```

#### 性能优化配置
```ini
# MySQL性能优化配置
[mysqld]
# 内存配置
innodb_buffer_pool_size = 16G
innodb_log_file_size = 1G
innodb_log_buffer_size = 64M
key_buffer_size = 256M
query_cache_size = 256M

# 连接配置
max_connections = 1000
max_connect_errors = 10000
wait_timeout = 28800
interactive_timeout = 28800

# InnoDB配置
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_thread_concurrency = 32
```

### 2.2 Redis集群部署

#### Redis Cluster配置
```bash
# 创建Redis集群目录
mkdir -p /opt/redis-cluster/{7001,7002,7003,7004,7005,7006}

# Redis节点配置文件 redis.conf
port 7001
cluster-enabled yes
cluster-config-file nodes-7001.conf
cluster-node-timeout 15000
appendonly yes
appendfilename "appendonly-7001.aof"
dbfilename "dump-7001.rdb"
logfile "/var/log/redis/redis-7001.log"
daemonize yes
protected-mode no
bind 0.0.0.0

# 启动Redis集群
redis-server /opt/redis-cluster/7001/redis.conf
redis-server /opt/redis-cluster/7002/redis.conf
redis-server /opt/redis-cluster/7003/redis.conf
redis-server /opt/redis-cluster/7004/redis.conf
redis-server /opt/redis-cluster/7005/redis.conf
redis-server /opt/redis-cluster/7006/redis.conf

# 创建集群
redis-cli --cluster create 127.0.0.1:7001 127.0.0.1:7002 127.0.0.1:7003 \
127.0.0.1:7004 127.0.0.1:7005 127.0.0.1:7006 --cluster-replicas 1
```

### 2.3 Elasticsearch集群部署

#### ES集群配置
```yaml
# elasticsearch.yml
cluster.name: himall-es-cluster
node.name: es-node-1
node.master: true
node.data: true
path.data: /opt/elasticsearch/data
path.logs: /opt/elasticsearch/logs
network.host: 0.0.0.0
http.port: 9200
transport.tcp.port: 9300
discovery.seed_hosts: ["es-node-1:9300", "es-node-2:9300", "es-node-3:9300"]
cluster.initial_master_nodes: ["es-node-1", "es-node-2", "es-node-3"]

# JVM配置 jvm.options
-Xms16g
-Xmx16g
-XX:+UseG1GC
-XX:G1HeapRegionSize=32m
-XX:+UseG1OldGCMixedGCCount=16
-XX:+UseStringDeduplication
```

#### 创建索引模板
```bash
# 商品索引模板
curl -X PUT "localhost:9200/_template/product_template" -H 'Content-Type: application/json' -d'
{
  "index_patterns": ["product_*"],
  "settings": {
    "number_of_shards": 3,
    "number_of_replicas": 1,
    "analysis": {
      "analyzer": {
        "ik_smart_analyzer": {
          "type": "ik_smart"
        },
        "ik_max_word_analyzer": {
          "type": "ik_max_word"
        }
      }
    }
  },
  "mappings": {
    "properties": {
      "productName": {
        "type": "text",
        "analyzer": "ik_max_word_analyzer",
        "search_analyzer": "ik_smart_analyzer"
      },
      "categoryName": {
        "type": "keyword"
      },
      "price": {
        "type": "double"
      },
      "createTime": {
        "type": "date"
      }
    }
  }
}'

# 店铺索引模板
curl -X PUT "localhost:9200/_template/shop_template" -H 'Content-Type: application/json' -d'
{
  "index_patterns": ["shop_*"],
  "settings": {
    "number_of_shards": 2,
    "number_of_replicas": 1
  },
  "mappings": {
    "properties": {
      "shopName": {
        "type": "text",
        "analyzer": "ik_max_word_analyzer"
      },
      "status": {
        "type": "integer"
      },
      "createTime": {
        "type": "date"
      }
    }
  }
}'
```

### 2.4 RocketMQ集群部署

#### NameServer部署
```bash
# 启动NameServer
nohup sh mqnamesrv &

# 检查启动状态
tail -f ~/logs/rocketmqlogs/namesrv.log
```

#### Broker集群部署
```properties
# broker-a.properties (主节点)
brokerClusterName=himall-cluster
brokerName=broker-a
brokerId=0
deleteWhen=04
fileReservedTime=48
brokerRole=SYNC_MASTER
flushDiskType=ASYNC_FLUSH
namesrvAddr=nameserver1:9876;nameserver2:9876
storePathRootDir=/opt/rocketmq/store-a
storePathCommitLog=/opt/rocketmq/store-a/commitlog
autoCreateTopicEnable=false

# broker-a-s.properties (从节点)
brokerClusterName=himall-cluster
brokerName=broker-a
brokerId=1
deleteWhen=04
fileReservedTime=48
brokerRole=SLAVE
flushDiskType=ASYNC_FLUSH
namesrvAddr=nameserver1:9876;nameserver2:9876
storePathRootDir=/opt/rocketmq/store-a-s
storePathCommitLog=/opt/rocketmq/store-a-s/commitlog
```

#### 启动Broker
```bash
# 启动主节点
nohup sh mqbroker -c broker-a.properties &

# 启动从节点
nohup sh mqbroker -c broker-a-s.properties &

# 创建Topic
sh mqadmin updateTopic -c himall-cluster -t ORDER_TOPIC -n nameserver1:9876
sh mqadmin updateTopic -c himall-cluster -t PAYMENT_TOPIC -n nameserver1:9876
sh mqadmin updateTopic -c himall-cluster -t REPORT_TOPIC -n nameserver1:9876
```

---

## 3. 应用服务部署

### 3.1 构建部署脚本

#### Maven构建脚本
```bash
#!/bin/bash
# build.sh - 项目构建脚本

set -e

PROJECT_HOME="/opt/himall"
MAVEN_HOME="/opt/maven"
JAVA_HOME="/opt/jdk"

echo "开始构建himallWork项目..."

# 设置环境变量
export PATH=$JAVA_HOME/bin:$MAVEN_HOME/bin:$PATH

# 进入项目目录
cd $PROJECT_HOME

# 清理并编译
mvn clean compile -Dmaven.test.skip=true

# 打包
mvn package -Dmaven.test.skip=true

echo "构建完成！"

# 复制jar包到部署目录
mkdir -p /opt/deploy/himall
cp himall-gw/seashop-gw-server/target/seashop-gw-server-*.jar /opt/deploy/himall/
cp himall-base/seashop-base-server/target/seashop-base-server-*.jar /opt/deploy/himall/
cp himall-trade/seashop-trade-server/target/seashop-trade-server-*.jar /opt/deploy/himall/
cp himall-order/seashop-order-server/target/seashop-order-server-*.jar /opt/deploy/himall/
cp himall-report/himall-report-server/target/himall-report-server-*.jar /opt/deploy/himall/

echo "jar包复制完成！"
```

#### 应用启动脚本
```bash
#!/bin/bash
# start.sh - 应用启动脚本

SERVICE_NAME=$1
JAR_NAME=$2
PROFILE=${3:-prod}

if [ -z "$SERVICE_NAME" ] || [ -z "$JAR_NAME" ]; then
    echo "用法: $0 <服务名> <jar包名> [环境]"
    echo "示例: $0 himall-gw seashop-gw-server-1.0.1-SNAPSHOT.jar prod"
    exit 1
fi

DEPLOY_DIR="/opt/deploy/himall"
LOG_DIR="/opt/logs/himall"
PID_DIR="/opt/pids"

# 创建必要目录
mkdir -p $LOG_DIR $PID_DIR

# JVM参数配置
JVM_OPTS="-server -Xms2g -Xmx4g -Xmn1g"
JVM_OPTS="$JVM_OPTS -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
JVM_OPTS="$JVM_OPTS -XX:+PrintGCDetails -XX:+PrintGCTimeStamps"
JVM_OPTS="$JVM_OPTS -Xloggc:$LOG_DIR/${SERVICE_NAME}-gc.log"
JVM_OPTS="$JVM_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JVM_OPTS="$JVM_OPTS -XX:HeapDumpPath=$LOG_DIR/${SERVICE_NAME}-heapdump.hprof"

# 应用参数
APP_OPTS="--spring.profiles.active=$PROFILE"
APP_OPTS="$APP_OPTS --logging.file.path=$LOG_DIR"
APP_OPTS="$APP_OPTS --server.port=808${SERVICE_NAME: -1}"

# 检查服务是否已启动
PID_FILE="$PID_DIR/${SERVICE_NAME}.pid"
if [ -f "$PID_FILE" ]; then
    PID=$(cat $PID_FILE)
    if ps -p $PID > /dev/null 2>&1; then
        echo "服务 $SERVICE_NAME 已在运行 (PID: $PID)"
        exit 1
    else
        rm -f $PID_FILE
    fi
fi

# 启动服务
echo "启动服务: $SERVICE_NAME"
cd $DEPLOY_DIR

nohup java $JVM_OPTS -jar $JAR_NAME $APP_OPTS > $LOG_DIR/${SERVICE_NAME}.out 2>&1 &
PID=$!

# 保存PID
echo $PID > $PID_FILE

echo "服务 $SERVICE_NAME 启动成功 (PID: $PID)"
echo "日志文件: $LOG_DIR/${SERVICE_NAME}.out"
```

#### 应用停止脚本
```bash
#!/bin/bash
# stop.sh - 应用停止脚本

SERVICE_NAME=$1
PID_DIR="/opt/pids"
PID_FILE="$PID_DIR/${SERVICE_NAME}.pid"

if [ -z "$SERVICE_NAME" ]; then
    echo "用法: $0 <服务名>"
    exit 1
fi

if [ ! -f "$PID_FILE" ]; then
    echo "服务 $SERVICE_NAME 未运行"
    exit 1
fi

PID=$(cat $PID_FILE)

if ! ps -p $PID > /dev/null 2>&1; then
    echo "服务 $SERVICE_NAME 未运行"
    rm -f $PID_FILE
    exit 1
fi

echo "停止服务: $SERVICE_NAME (PID: $PID)"

# 优雅停止
kill -TERM $PID

# 等待进程结束
for i in {1..30}; do
    if ! ps -p $PID > /dev/null 2>&1; then
        echo "服务 $SERVICE_NAME 已停止"
        rm -f $PID_FILE
        exit 0
    fi
    sleep 1
done

# 强制停止
echo "强制停止服务: $SERVICE_NAME"
kill -KILL $PID
rm -f $PID_FILE
echo "服务 $SERVICE_NAME 已强制停止"
```

### 3.2 配置文件管理

#### 生产环境配置
```yaml
# application-prod.yml
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
    master:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: **************************************************************************************************************
      username: himall
      password: ${DB_PASSWORD}
      hikari:
        maximum-pool-size: 20
        minimum-idle: 5
        connection-timeout: 30000
        idle-timeout: 600000
        max-lifetime: 1800000
    
    slave:
      driver-class-name: com.mysql.cj.jdbc.Driver
      url: *************************************************************************************************************
      username: himall_read
      password: ${DB_READ_PASSWORD}
      hikari:
        maximum-pool-size: 10
        minimum-idle: 2
  
  redis:
    cluster:
      nodes:
        - redis-1:7001
        - redis-1:7002
        - redis-2:7003
        - redis-2:7004
        - redis-3:7005
        - redis-3:7006
      max-redirects: 3
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms

  elasticsearch:
    rest:
      uris:
        - http://es-1:9200
        - http://es-2:9200
        - http://es-3:9200
      connection-timeout: 5s
      read-timeout: 30s

rocketmq:
  name-server: rocketmq-nameserver-1:9876;rocketmq-nameserver-2:9876
  producer:
    group: himall-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
  consumer:
    group: himall-consumer-group
    consume-timeout: 15

logging:
  level:
    com.sankuai.shangou.seashop: INFO
    org.springframework.web: WARN
    com.alibaba.druid: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"
  file:
    max-size: 100MB
    max-history: 30

# 业务配置
seashop:
  aes:
    secret: ${AES_SECRET}
  
  xxl-job:
    admin:
      addresses: http://xxl-job-admin:8080/xxl-job-admin
    executor:
      appname: himall-executor
      port: 9999
      logpath: /opt/logs/himall/xxl-job
      logretentiondays: 7
    accessToken: ${XXL_JOB_TOKEN}

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true
```

#### Docker Compose部署
```yaml
# docker-compose.yml
version: '3.8'

services:
  # 网关服务
  himall-gw:
    image: himall/himall-gw:1.0.1
    container_name: himall-gw
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_READ_PASSWORD=${DB_READ_PASSWORD}
      - AES_SECRET=${AES_SECRET}
      - XXL_JOB_TOKEN=${XXL_JOB_TOKEN}
    volumes:
      - /opt/logs/himall:/opt/logs/himall
      - /opt/config/himall:/opt/config
    depends_on:
      - mysql-master
      - redis-cluster
    networks:
      - himall-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 基础服务
  himall-base:
    image: himall/himall-base:1.0.1
    container_name: himall-base
    ports:
      - "8081:8081"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_READ_PASSWORD=${DB_READ_PASSWORD}
      - AES_SECRET=${AES_SECRET}
    volumes:
      - /opt/logs/himall:/opt/logs/himall
      - /opt/config/himall:/opt/config
    depends_on:
      - mysql-master
      - redis-cluster
    networks:
      - himall-network
    restart: unless-stopped

  # 交易服务
  himall-trade:
    image: himall/himall-trade:1.0.1
    container_name: himall-trade
    ports:
      - "8082:8082"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_READ_PASSWORD=${DB_READ_PASSWORD}
    volumes:
      - /opt/logs/himall:/opt/logs/himall
      - /opt/config/himall:/opt/config
    depends_on:
      - mysql-master
      - redis-cluster
      - elasticsearch
    networks:
      - himall-network
    restart: unless-stopped

  # 订单服务
  himall-order:
    image: himall/himall-order:1.0.1
    container_name: himall-order
    ports:
      - "8083:8083"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_READ_PASSWORD=${DB_READ_PASSWORD}
    volumes:
      - /opt/logs/himall:/opt/logs/himall
      - /opt/config/himall:/opt/config
    depends_on:
      - mysql-master
      - redis-cluster
      - rocketmq
    networks:
      - himall-network
    restart: unless-stopped

  # 报表服务
  himall-report:
    image: himall/himall-report:1.0.1
    container_name: himall-report
    ports:
      - "8084:8084"
    environment:
      - SPRING_PROFILES_ACTIVE=prod
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_READ_PASSWORD=${DB_READ_PASSWORD}
    volumes:
      - /opt/logs/himall:/opt/logs/himall
      - /opt/config/himall:/opt/config
    depends_on:
      - mysql-master
      - redis-cluster
    networks:
      - himall-network
    restart: unless-stopped

networks:
  himall-network:
    driver: bridge
```

---

## 4. 监控和日志

### 4.1 Prometheus监控配置

#### Prometheus配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "himall_rules.yml"

scrape_configs:
  - job_name: 'himall-services'
    static_configs:
      - targets: 
        - 'himall-gw:8080'
        - 'himall-base:8081'
        - 'himall-trade:8082'
        - 'himall-order:8083'
        - 'himall-report:8084'
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  - job_name: 'mysql-exporter'
    static_configs:
      - targets: ['mysql-exporter:9104']

  - job_name: 'redis-exporter'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'node-exporter'
    static_configs:
      - targets: 
        - 'node-exporter-1:9100'
        - 'node-exporter-2:9100'
        - 'node-exporter-3:9100'

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

#### 告警规则配置
```yaml
# himall_rules.yml
groups:
  - name: himall-application
    rules:
      - alert: ApplicationDown
        expr: up{job="himall-services"} == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "应用服务下线"
          description: "{{ $labels.instance }} 服务已下线超过1分钟"

      - alert: HighMemoryUsage
        expr: (jvm_memory_used_bytes / jvm_memory_max_bytes) * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "{{ $labels.instance }} 内存使用率超过80%"

      - alert: HighCPUUsage
        expr: system_cpu_usage * 100 > 80
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "CPU使用率过高"
          description: "{{ $labels.instance }} CPU使用率超过80%"

      - alert: DatabaseConnectionPoolHigh
        expr: hikaricp_connections_active / hikaricp_connections_max * 100 > 80
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "数据库连接池使用率过高"
          description: "{{ $labels.instance }} 数据库连接池使用率超过80%"

  - name: himall-business
    rules:
      - alert: HighOrderFailureRate
        expr: rate(order_create_failed_total[5m]) / rate(order_create_total[5m]) * 100 > 5
        for: 3m
        labels:
          severity: critical
        annotations:
          summary: "订单创建失败率过高"
          description: "订单创建失败率超过5%"

      - alert: PaymentFailureRate
        expr: rate(payment_failed_total[5m]) / rate(payment_total[5m]) * 100 > 3
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "支付失败率过高"
          description: "支付失败率超过3%"
```

### 4.2 Grafana仪表板配置

#### 应用监控仪表板
```json
{
  "dashboard": {
    "title": "himallWork应用监控",
    "panels": [
      {
        "title": "服务状态",
        "type": "stat",
        "targets": [
          {
            "expr": "up{job=\"himall-services\"}",
            "legendFormat": "{{instance}}"
          }
        ]
      },
      {
        "title": "QPS",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{instance}} - {{method}} {{uri}}"
          }
        ]
      },
      {
        "title": "响应时间",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          },
          {
            "expr": "histogram_quantile(0.50, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "50th percentile"
          }
        ]
      },
      {
        "title": "JVM内存使用",
        "type": "graph",
        "targets": [
          {
            "expr": "jvm_memory_used_bytes{area=\"heap\"}",
            "legendFormat": "{{instance}} - Heap Used"
          },
          {
            "expr": "jvm_memory_max_bytes{area=\"heap\"}",
            "legendFormat": "{{instance}} - Heap Max"
          }
        ]
      }
    ]
  }
}
```

### 4.3 ELK日志收集

#### Filebeat配置
```yaml
# filebeat.yml
filebeat.inputs:
  - type: log
    enabled: true
    paths:
      - /opt/logs/himall/*.log
    fields:
      service: himall
      environment: prod
    fields_under_root: true
    multiline.pattern: '^\d{4}-\d{2}-\d{2}'
    multiline.negate: true
    multiline.match: after

output.logstash:
  hosts: ["logstash:5044"]

processors:
  - add_host_metadata:
      when.not.contains.tags: forwarded
```

#### Logstash配置
```ruby
# logstash.conf
input {
  beats {
    port => 5044
  }
}

filter {
  if [service] == "himall" {
    grok {
      match => { 
        "message" => "%{TIMESTAMP_ISO8601:timestamp} \[%{DATA:thread}\] %{LOGLEVEL:level} %{DATA:logger} - %{GREEDYDATA:msg}" 
      }
    }
    
    date {
      match => [ "timestamp", "yyyy-MM-dd HH:mm:ss.SSS" ]
    }
    
    if [level] == "ERROR" {
      mutate {
        add_tag => [ "error" ]
      }
    }
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "himall-logs-%{+YYYY.MM.dd}"
  }
}
```

---

## 5. 备份和恢复

### 5.1 数据库备份策略

#### 自动备份脚本
```bash
#!/bin/bash
# mysql_backup.sh - MySQL自动备份脚本

BACKUP_DIR="/opt/backup/mysql"
MYSQL_USER="backup_user"
MYSQL_PASSWORD="backup_password"
MYSQL_HOST="mysql-master"
RETENTION_DAYS=7

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份文件名
BACKUP_DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="himall_backup_${BACKUP_DATE}.sql"

echo "开始备份数据库..."

# 执行备份
mysqldump -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  --all-databases \
  --master-data=2 \
  --flush-logs \
  --hex-blob > $BACKUP_DIR/$BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_DIR/$BACKUP_FILE

echo "数据库备份完成: $BACKUP_DIR/${BACKUP_FILE}.gz"

# 清理过期备份
find $BACKUP_DIR -name "himall_backup_*.sql.gz" -mtime +$RETENTION_DAYS -delete

echo "过期备份清理完成"

# 上传到云存储（可选）
# aws s3 cp $BACKUP_DIR/${BACKUP_FILE}.gz s3://himall-backup/mysql/
```

#### 数据恢复脚本
```bash
#!/bin/bash
# mysql_restore.sh - MySQL数据恢复脚本

BACKUP_FILE=$1
MYSQL_USER="root"
MYSQL_PASSWORD="root_password"
MYSQL_HOST="mysql-master"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <备份文件路径>"
    exit 1
fi

if [ ! -f "$BACKUP_FILE" ]; then
    echo "备份文件不存在: $BACKUP_FILE"
    exit 1
fi

echo "开始恢复数据库..."
echo "备份文件: $BACKUP_FILE"

# 停止应用服务
echo "停止应用服务..."
docker-compose stop himall-gw himall-base himall-trade himall-order himall-report

# 解压备份文件（如果是压缩的）
if [[ $BACKUP_FILE == *.gz ]]; then
    TEMP_FILE="/tmp/restore_temp.sql"
    gunzip -c $BACKUP_FILE > $TEMP_FILE
    BACKUP_FILE=$TEMP_FILE
fi

# 执行恢复
mysql -h$MYSQL_HOST -u$MYSQL_USER -p$MYSQL_PASSWORD < $BACKUP_FILE

if [ $? -eq 0 ]; then
    echo "数据库恢复成功"
    
    # 启动应用服务
    echo "启动应用服务..."
    docker-compose start himall-gw himall-base himall-trade himall-order himall-report
    
    # 清理临时文件
    if [ -f "/tmp/restore_temp.sql" ]; then
        rm -f /tmp/restore_temp.sql
    fi
else
    echo "数据库恢复失败"
    exit 1
fi
```

### 5.2 Redis备份策略

#### Redis备份脚本
```bash
#!/bin/bash
# redis_backup.sh - Redis备份脚本

BACKUP_DIR="/opt/backup/redis"
REDIS_NODES=("redis-1:7001" "redis-2:7003" "redis-3:7005")
RETENTION_DAYS=3

mkdir -p $BACKUP_DIR

BACKUP_DATE=$(date +%Y%m%d_%H%M%S)

echo "开始备份Redis集群..."

for node in "${REDIS_NODES[@]}"; do
    host=$(echo $node | cut -d: -f1)
    port=$(echo $node | cut -d: -f2)
    
    echo "备份节点: $node"
    
    # 执行BGSAVE
    redis-cli -h $host -p $port BGSAVE
    
    # 等待备份完成
    while [ $(redis-cli -h $host -p $port LASTSAVE) -eq $(redis-cli -h $host -p $port LASTSAVE) ]; do
        sleep 1
    done
    
    # 复制RDB文件
    scp $host:/opt/redis/dump.rdb $BACKUP_DIR/dump_${host}_${port}_${BACKUP_DATE}.rdb
done

echo "Redis备份完成"

# 清理过期备份
find $BACKUP_DIR -name "dump_*.rdb" -mtime +$RETENTION_DAYS -delete
```

---

## 6. 故障处理和运维

### 6.1 常见故障处理

#### 服务启动失败
```bash
# 检查服务状态
systemctl status himall-gw
docker ps -a | grep himall

# 查看启动日志
tail -f /opt/logs/himall/himall-gw.out
docker logs himall-gw

# 检查端口占用
netstat -tlnp | grep 8080
lsof -i :8080

# 检查磁盘空间
df -h
du -sh /opt/logs/himall/*

# 检查内存使用
free -h
ps aux --sort=-%mem | head -10
```

#### 数据库连接问题
```bash
# 检查数据库连接
mysql -h mysql-master -u himall -p -e "SELECT 1"

# 检查连接数
mysql -h mysql-master -u root -p -e "SHOW PROCESSLIST"
mysql -h mysql-master -u root -p -e "SHOW STATUS LIKE 'Threads_connected'"

# 检查慢查询
mysql -h mysql-master -u root -p -e "SHOW VARIABLES LIKE 'slow_query_log'"
tail -f /var/log/mysql/mysql-slow.log

# 重启数据库服务
systemctl restart mysql
docker restart mysql-master
```

#### Redis集群问题
```bash
# 检查集群状态
redis-cli -c -h redis-1 -p 7001 cluster nodes
redis-cli -c -h redis-1 -p 7001 cluster info

# 检查节点状态
for port in 7001 7002 7003 7004 7005 7006; do
    echo "检查端口 $port:"
    redis-cli -h redis-1 -p $port ping
done

# 修复集群
redis-cli --cluster fix redis-1:7001

# 重新分片
redis-cli --cluster reshard redis-1:7001
```

#### 应用性能问题
```bash
# 检查JVM状态
jstat -gc <pid> 5s
jstack <pid> > thread_dump.txt
jmap -dump:format=b,file=heap_dump.hprof <pid>

# 检查系统资源
top -p <pid>
iostat -x 1
sar -u 1 10

# 检查网络连接
netstat -an | grep :8080 | wc -l
ss -tuln | grep :8080
```

### 6.2 性能优化

#### JVM调优参数
```bash
# 生产环境JVM参数
JAVA_OPTS="-server"
JAVA_OPTS="$JAVA_OPTS -Xms4g -Xmx4g -Xmn2g"
JAVA_OPTS="$JAVA_OPTS -XX:+UseG1GC"
JAVA_OPTS="$JAVA_OPTS -XX:MaxGCPauseMillis=200"
JAVA_OPTS="$JAVA_OPTS -XX:G1HeapRegionSize=16m"
JAVA_OPTS="$JAVA_OPTS -XX:+UseStringDeduplication"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGC -XX:+PrintGCDetails"
JAVA_OPTS="$JAVA_OPTS -XX:+PrintGCTimeStamps -XX:+PrintGCApplicationStoppedTime"
JAVA_OPTS="$JAVA_OPTS -Xloggc:/opt/logs/himall/gc.log"
JAVA_OPTS="$JAVA_OPTS -XX:+UseGCLogFileRotation"
JAVA_OPTS="$JAVA_OPTS -XX:NumberOfGCLogFiles=5"
JAVA_OPTS="$JAVA_OPTS -XX:GCLogFileSize=100M"
JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError"
JAVA_OPTS="$JAVA_OPTS -XX:HeapDumpPath=/opt/logs/himall/"
```

#### 数据库优化
```sql
-- 慢查询优化
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;
SET GLOBAL log_queries_not_using_indexes = 'ON';

-- 连接池优化
SET GLOBAL max_connections = 1000;
SET GLOBAL max_connect_errors = 10000;
SET GLOBAL wait_timeout = 28800;

-- InnoDB优化
SET GLOBAL innodb_buffer_pool_size = 16*1024*1024*1024;
SET GLOBAL innodb_log_file_size = 1*1024*1024*1024;
SET GLOBAL innodb_flush_log_at_trx_commit = 2;
SET GLOBAL innodb_flush_method = 'O_DIRECT';
```

### 6.3 安全加固

#### 系统安全配置
```bash
# 防火墙配置
ufw enable
ufw default deny incoming
ufw default allow outgoing
ufw allow ssh
ufw allow 80/tcp
ufw allow 443/tcp

# SSH安全配置
sed -i 's/#PermitRootLogin yes/PermitRootLogin no/' /etc/ssh/sshd_config
sed -i 's/#PasswordAuthentication yes/PasswordAuthentication no/' /etc/ssh/sshd_config
systemctl restart sshd

# 系统更新
apt update && apt upgrade -y
yum update -y
```

#### 应用安全配置
```yaml
# application-prod.yml 安全配置
server:
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
    key-alias: himall

spring:
  security:
    require-ssl: true
  
management:
  endpoints:
    web:
      exposure:
        include: health,info
  endpoint:
    health:
      show-details: when-authorized
  security:
    enabled: true
```

---

## 7. 运维自动化

### 7.1 CI/CD流水线

#### Jenkins Pipeline
```groovy
pipeline {
    agent any
    
    environment {
        MAVEN_HOME = '/opt/maven'
        JAVA_HOME = '/opt/jdk'
        DOCKER_REGISTRY = 'registry.himall.com'
        K8S_NAMESPACE = 'himall-prod'
    }
    
    stages {
        stage('代码检出') {
            steps {
                git branch: 'master', 
                    url: 'https://git.himall.com/himall/himallWork.git',
                    credentialsId: 'git-credentials'
            }
        }
        
        stage('代码质量检查') {
            steps {
                script {
                    def scannerHome = tool 'SonarQube Scanner'
                    withSonarQubeEnv('SonarQube') {
                        sh "${scannerHome}/bin/sonar-scanner"
                    }
                }
            }
        }
        
        stage('编译构建') {
            steps {
                sh '''
                    export PATH=$JAVA_HOME/bin:$MAVEN_HOME/bin:$PATH
                    mvn clean compile -Dmaven.test.skip=true
                    mvn package -Dmaven.test.skip=true
                '''
            }
        }
        
        stage('单元测试') {
            steps {
                sh '''
                    export PATH=$JAVA_HOME/bin:$MAVEN_HOME/bin:$PATH
                    mvn test
                '''
            }
            post {
                always {
                    junit 'target/surefire-reports/*.xml'
                    publishHTML([
                        allowMissing: false,
                        alwaysLinkToLastBuild: true,
                        keepAll: true,
                        reportDir: 'target/site/jacoco',
                        reportFiles: 'index.html',
                        reportName: 'Coverage Report'
                    ])
                }
            }
        }
        
        stage('构建镜像') {
            steps {
                script {
                    def services = ['himall-gw', 'himall-base', 'himall-trade', 'himall-order', 'himall-report']
                    services.each { service ->
                        sh """
                            docker build -t ${DOCKER_REGISTRY}/${service}:${BUILD_NUMBER} \
                                -f ${service}/Dockerfile ${service}/
                            docker push ${DOCKER_REGISTRY}/${service}:${BUILD_NUMBER}
                        """
                    }
                }
            }
        }
        
        stage('部署到测试环境') {
            steps {
                sh '''
                    kubectl set image deployment/himall-gw himall-gw=${DOCKER_REGISTRY}/himall-gw:${BUILD_NUMBER} -n himall-test
                    kubectl set image deployment/himall-base himall-base=${DOCKER_REGISTRY}/himall-base:${BUILD_NUMBER} -n himall-test
                    kubectl set image deployment/himall-trade himall-trade=${DOCKER_REGISTRY}/himall-trade:${BUILD_NUMBER} -n himall-test
                    kubectl set image deployment/himall-order himall-order=${DOCKER_REGISTRY}/himall-order:${BUILD_NUMBER} -n himall-test
                    kubectl set image deployment/himall-report himall-report=${DOCKER_REGISTRY}/himall-report:${BUILD_NUMBER} -n himall-test
                    
                    kubectl rollout status deployment/himall-gw -n himall-test
                    kubectl rollout status deployment/himall-base -n himall-test
                    kubectl rollout status deployment/himall-trade -n himall-test
                    kubectl rollout status deployment/himall-order -n himall-test
                    kubectl rollout status deployment/himall-report -n himall-test
                '''
            }
        }
        
        stage('自动化测试') {
            steps {
                sh '''
                    # 等待服务启动
                    sleep 60
                    
                    # 执行接口测试
                    newman run tests/himall-api-tests.json \
                        --environment tests/test-environment.json \
                        --reporters cli,junit \
                        --reporter-junit-export target/newman-results.xml
                '''
            }
            post {
                always {
                    junit 'target/newman-results.xml'
                }
            }
        }
        
        stage('部署到生产环境') {
            when {
                branch 'master'
            }
            steps {
                input message: '确认部署到生产环境?', ok: '部署'
                sh '''
                    kubectl set image deployment/himall-gw himall-gw=${DOCKER_REGISTRY}/himall-gw:${BUILD_NUMBER} -n ${K8S_NAMESPACE}
                    kubectl set image deployment/himall-base himall-base=${DOCKER_REGISTRY}/himall-base:${BUILD_NUMBER} -n ${K8S_NAMESPACE}
                    kubectl set image deployment/himall-trade himall-trade=${DOCKER_REGISTRY}/himall-trade:${BUILD_NUMBER} -n ${K8S_NAMESPACE}
                    kubectl set image deployment/himall-order himall-order=${DOCKER_REGISTRY}/himall-order:${BUILD_NUMBER} -n ${K8S_NAMESPACE}
                    kubectl set image deployment/himall-report himall-report=${DOCKER_REGISTRY}/himall-report:${BUILD_NUMBER} -n ${K8S_NAMESPACE}
                    
                    kubectl rollout status deployment/himall-gw -n ${K8S_NAMESPACE}
                    kubectl rollout status deployment/himall-base -n ${K8S_NAMESPACE}
                    kubectl rollout status deployment/himall-trade -n ${K8S_NAMESPACE}
                    kubectl rollout status deployment/himall-order -n ${K8S_NAMESPACE}
                    kubectl rollout status deployment/himall-report -n ${K8S_NAMESPACE}
                '''
            }
        }
    }
    
    post {
        always {
            cleanWs()
        }
        success {
            dingtalk (
                robot: 'himall-robot',
                type: 'MARKDOWN',
                title: '部署成功',
                text: [
                    "### himallWork部署成功",
                    "- **项目**: himallWork",
                    "- **分支**: ${env.BRANCH_NAME}",
                    "- **构建号**: ${env.BUILD_NUMBER}",
                    "- **提交**: ${env.GIT_COMMIT}",
                    "- **时间**: ${new Date().format('yyyy-MM-dd HH:mm:ss')}"
                ]
            )
        }
        failure {
            dingtalk (
                robot: 'himall-robot',
                type: 'MARKDOWN',
                title: '部署失败',
                text: [
                    "### himallWork部署失败",
                    "- **项目**: himallWork",
                    "- **分支**: ${env.BRANCH_NAME}",
                    "- **构建号**: ${env.BUILD_NUMBER}",
                    "- **失败阶段**: ${env.STAGE_NAME}",
                    "- **时间**: ${new Date().format('yyyy-MM-dd HH:mm:ss')}"
                ]
            )
        }
    }
}
```

### 7.2 Kubernetes部署

#### Deployment配置
```yaml
# himall-gw-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: himall-gw
  namespace: himall-prod
  labels:
    app: himall-gw
spec:
  replicas: 3
  selector:
    matchLabels:
      app: himall-gw
  template:
    metadata:
      labels:
        app: himall-gw
    spec:
      containers:
      - name: himall-gw
        image: registry.himall.com/himall-gw:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "prod"
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: himall-secrets
              key: db-password
        - name: AES_SECRET
          valueFrom:
            secretKeyRef:
              name: himall-secrets
              key: aes-secret
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        volumeMounts:
        - name: logs
          mountPath: /opt/logs/himall
        - name: config
          mountPath: /opt/config
      volumes:
      - name: logs
        hostPath:
          path: /opt/logs/himall
      - name: config
        configMap:
          name: himall-config
---
apiVersion: v1
kind: Service
metadata:
  name: himall-gw-service
  namespace: himall-prod
spec:
  selector:
    app: himall-gw
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: himall-gw-ingress
  namespace: himall-prod
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
  - hosts:
    - api.himall.com
    secretName: himall-tls
  rules:
  - host: api.himall.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: himall-gw-service
            port:
              number: 80
```

### 7.3 自动化运维脚本

#### 健康检查脚本
```bash
#!/bin/bash
# health_check.sh - 服务健康检查脚本

SERVICES=("himall-gw:8080" "himall-base:8081" "himall-trade:8082" "himall-order:8083" "himall-report:8084")
ALERT_WEBHOOK="https://oapi.dingtalk.com/robot/send?access_token=xxx"

check_service() {
    local service=$1
    local host=$(echo $service | cut -d: -f1)
    local port=$(echo $service | cut -d: -f2)
    
    # 检查端口是否开放
    if ! nc -z $host $port; then
        echo "ERROR: $service 端口不可达"
        return 1
    fi
    
    # 检查健康检查接口
    local health_url="http://$service/actuator/health"
    local response=$(curl -s -o /dev/null -w "%{http_code}" $health_url)
    
    if [ "$response" != "200" ]; then
        echo "ERROR: $service 健康检查失败，HTTP状态码: $response"
        return 1
    fi
    
    echo "OK: $service 健康检查通过"
    return 0
}

send_alert() {
    local message=$1
    curl -X POST $ALERT_WEBHOOK \
        -H 'Content-Type: application/json' \
        -d "{
            \"msgtype\": \"text\",
            \"text\": {
                \"content\": \"【himallWork告警】$message\"
            }
        }"
}

main() {
    local failed_services=()
    
    echo "开始健康检查..."
    
    for service in "${SERVICES[@]}"; do
        if ! check_service $service; then
            failed_services+=($service)
        fi
    done
    
    if [ ${#failed_services[@]} -gt 0 ]; then
        local alert_msg="以下服务健康检查失败: ${failed_services[*]}"
        echo $alert_msg
        send_alert "$alert_msg"
        exit 1
    else
        echo "所有服务健康检查通过"
        exit 0
    fi
}

main
```

#### 自动扩缩容脚本
```bash
#!/bin/bash
# auto_scale.sh - 自动扩缩容脚本

NAMESPACE="himall-prod"
PROMETHEUS_URL="http://prometheus:9090"
SCALE_UP_THRESHOLD=80
SCALE_DOWN_THRESHOLD=30
MAX_REPLICAS=10
MIN_REPLICAS=2

get_cpu_usage() {
    local deployment=$1
    local query="avg(rate(container_cpu_usage_seconds_total{pod=~\"${deployment}-.*\"}[5m])) * 100"
    local result=$(curl -s "${PROMETHEUS_URL}/api/v1/query?query=${query}" | jq -r '.data.result[0].value[1]')
    echo ${result%.*}  # 取整数部分
}

get_current_replicas() {
    local deployment=$1
    kubectl get deployment $deployment -n $NAMESPACE -o jsonpath='{.spec.replicas}'
}

scale_deployment() {
    local deployment=$1
    local replicas=$2
    kubectl scale deployment $deployment --replicas=$replicas -n $NAMESPACE
    echo "已将 $deployment 扩缩容至 $replicas 个副本"
}

check_and_scale() {
    local deployment=$1
    local cpu_usage=$(get_cpu_usage $deployment)
    local current_replicas=$(get_current_replicas $deployment)
    
    echo "检查 $deployment: CPU使用率=${cpu_usage}%, 当前副本数=${current_replicas}"
    
    if [ $cpu_usage -gt $SCALE_UP_THRESHOLD ] && [ $current_replicas -lt $MAX_REPLICAS ]; then
        local new_replicas=$((current_replicas + 1))
        scale_deployment $deployment $new_replicas
    elif [ $cpu_usage -lt $SCALE_DOWN_THRESHOLD ] && [ $current_replicas -gt $MIN_REPLICAS ]; then
        local new_replicas=$((current_replicas - 1))
        scale_deployment $deployment $new_replicas
    else
        echo "$deployment 无需扩缩容"
    fi
}

main() {
    local deployments=("himall-gw" "himall-base" "himall-trade" "himall-order" "himall-report")
    
    for deployment in "${deployments[@]}"; do
        check_and_scale $deployment
    done
}

main
```

---

## 8. 总结

本部署运维指南涵盖了himallWork项目从环境准备到生产运维的完整流程：

### 8.1 部署要点

1. **环境规划**：合理规划服务器资源和网络架构
2. **基础组件**：正确部署MySQL、Redis、Elasticsearch等基础组件
3. **应用部署**：使用Docker或Kubernetes进行应用部署
4. **配置管理**：统一管理配置文件和敏感信息

### 8.2 运维要点

1. **监控告警**：建立完善的监控告警体系
2. **日志管理**：集中收集和分析应用日志
3. **备份恢复**：定期备份数据并验证恢复流程
4. **故障处理**：建立故障处理流程和应急预案

### 8.3 自动化运维

1. **CI/CD流水线**：实现代码到生产的自动化部署
2. **容器化部署**：使用Kubernetes实现弹性伸缩
3. **自动化脚本**：编写各种运维自动化脚本
4. **性能优化**：持续优化系统性能

### 8.4 安全加固

1. **系统安全**：加强操作系统和网络安全
2. **应用安全**：配置SSL证书和访问控制
3. **数据安全**：保护敏感数据和备份安全
4. **访问控制**：实施最小权限原则

通过遵循本指南，可以确保himallWork项目在生产环境中稳定、安全、高效地运行。
# himallWork部署运维指南

## 1. 环境准备

### 1.1 基础环境要求

#### 服务器配置
| 环境 | CPU | 内存 | 磁盘 | 数量 |
|------|-----|------|------|------|
| 开发环境 | 4核 | 8GB | 100GB | 1台 |
| 测试环境 | 8核 | 16GB | 200GB | 2台 |
| 生产环境 | 16核 | 32GB | 500GB | 4台+ |

#### 软件版本要求
| 软件 | 版本 | 说明 |
|------|------|------|
| JDK | 1.8+ | 推荐使用OpenJDK 11 |
| Maven | 3.6+ | 项目构建工具 |
| MySQL | 5.7+ | 主数据库 |
| Redis | 5.0+ | 缓存和分布式锁 |
| Elasticsearch | 7.x | 搜索引擎 |
| RocketMQ | 4.9+ | 消息队列 |
| Nginx | 1.18+ | 反向代理和负载均衡 |

### 1.2 网络规划

#### 端口规划
| 服务 | 端口 | 说明 |
|------|------|------|
| himall-gw | 8080 | 网关服务 |
| himall-base | 8081 | 基础服务 |
| himall-trade | 8082 | 交易服务 |
| himall-order | 8083 | 订单服务 |
| himall-report | 8084 | 报表服务 |
| MySQL | 3306 | 数据库 |
| Redis | 6379 | 缓存 |
| Elasticsearch | 9200 | 搜索引擎 |
| RocketMQ | 9876 | 消息队列 |

#### 网络安全组配置
```bash
# 开放应用端口（仅内网访问）
iptables -A INPUT -p tcp --dport 8080:8084 -s 10.0.0.0/8 -j ACCEPT

# 开放数据库端口（仅应用服务器访问）
iptables -A INPUT -p tcp --dport 3306 -s ********/24 -j ACCEPT
iptables -A INPUT -p tcp --dport 6379 -s ********/24 -j ACCEPT

# 开放HTTP/HTTPS端口（公网访问）
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT
```

---

## 2. 基础组件部署

### 2.1 MySQL数据库部署

#### 主从架构部署
```bash
# 主库配置 /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
server-id = 1
log-bin = mysql-bin
binlog-format = ROW
gtid-mode = ON
enforce-gtid-consistency = ON
binlog-do-db = himall_base,himall_trade,himall_order,himall_report

# 从库配置
[mysqld]
server-id = 2
relay-log = mysql-relay-bin
read-only = 1
gtid-mode = ON
enforce-gtid-consistency = ON
```

#### 数据库初始化
```sql
-- 创建数据库
CREATE DATABASE himall_base DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE himall_trade DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE himall_order DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE himall_report DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建应用用户
CREATE USER 'himall'@'%' IDENTIFIED BY 'HimallPass123!';
GRANT ALL PRIVILEGES ON himall_*.* TO 'himall'@'%';
FLUSH PRIVILEGES;

-- 创建只读用户（用于从库）
CREATE USER 'himall_read'@'%' IDENTIFIED BY 'HimallRead123!';
GRANT SELECT ON himall_*.* TO 'himall_read'@'%';
FLUSH PRIVILEGES;
```

#### 性能优化配置
```ini
# MySQL性能优化配置
[mysqld]
# 内存配置
innodb_buffer_pool_size = 16G
innodb_log_file_size = 1G
innodb_log_buffer_size = 64M
key_buffer_size = 256M
query_cache_size = 256M

# 连接配置
max_connections = 1000
max_connect_errors = 10000
wait_timeout = 28800
interactive_timeout = 28800

# InnoDB配置
innodb_file_per_table = 1
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_thread_concurrency = 32
```

### 2.2 Redis集群部署

#### Redis Cluster配置
```bash
# 创建Redis集群目录
mkdir -p /opt/redis-cluster/{7001,7002,7003,7004,7005,7006}

# Redis节点配置文件 redis.conf
port 7001
cluster-enabled yes
cluster-config-file nodes-7001.conf
cluster-node-timeout 15000
appendonly yes
appendfilename "appendonly-7001.aof"
dbfilename "dump-7001.rdb"
logfile "/var/log/redis/redis-7001.log"
daemonize yes
protected-mode no
bind 0.0.0.0

# 启动Redis集群
redis-server /opt/redis-cluster/7001/redis.conf
redis-server /opt/redis-cluster/7002/redis.conf
redis-server /opt/redis-cluster/7003/redis.conf
redis-server /opt/redis-cluster/7004/redis.conf
redis-server /opt/redis-cluster/7005/redis.conf
redis-server /opt/redis-cluster/7006/redis.conf

# 创建集群
redis-cli --cluster create 127.0.0.1:7001 127.0.0.1:7002 127.0.0.1:7003 \
127.0.0.1:7004 127.0.0.1:7005 127.0.0.1:7006 --cluster-replicas 1
```

### 2.3 Elasticsearch集群部署

#### ES集群配置
```yaml
# elasticsearch.yml
cluster.name: himall-es-cluster
node.name: es-node-1
node.master: true
node.data: true
path.data: /opt/elasticsearch/data
path.logs: /opt/elasticsearch/logs
network.host: 0.0.0.0
http.port: 9200
transport.tcp.port: 9300
discovery.seed_hosts: ["es-node-1:9300", "es-node-2:9300", "es-node-3:9300"]
cluster.initial_master_nodes: ["es-node-1", "es-node-2", "es-node-3"]

# JVM配置