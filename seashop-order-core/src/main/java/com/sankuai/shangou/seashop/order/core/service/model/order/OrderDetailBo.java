package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderDetailBo {

    /**
     * 状态变更的时间，前端根据数量来判断步骤
     * 前端总共显示四个步骤，所以这里最多四个时间
     */
    private List<String> statusChangeTimeList;
    /**
     * 订单基本信息
     */
    private OrderInfoBo orderInfo;
    /**
     * 订单发票信息
     */
    private OrderInvoiceBo orderInvoice;
    /**
     * 订单物流信息
     */
    private List<OrderExpressBo> expressList;
    /**
     * 微信发货开关
     */
    private Boolean wxSend;

}
