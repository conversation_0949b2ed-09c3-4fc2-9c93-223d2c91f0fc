package com.sankuai.shangou.seashop.order.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.OrderService;
import com.sankuai.shangou.seashop.order.core.service.model.order.ConfirmReceiveBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ErpOrderDetailBo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.ConfirmReceiptWxReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ErpOrderDetailResp;
import com.sankuai.shangou.seashop.pay.core.service.WxShippingBiz;
import com.sankuai.shangou.seashop.pay.thrift.core.request.GetOrderRequest;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname OrderController
 * Description //TODO
 * @date 2024/11/19 15:05
 */
@RestController
@RequestMapping
public class OrderController implements OrderFeign {
    @Resource
    private OrderService orderService;

    @Override
    public ResultDto<List<String>> generateOrderNo(Integer size) {
        List<String> orderNos = orderService.generateOrderNo(size);
        return ResultDto.newWithData(orderNos);
    }
}
