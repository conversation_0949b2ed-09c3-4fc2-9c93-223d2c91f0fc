package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class EsOrderRefundBo {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 退款ID，退款表主键ID
     */
    private Long refundId;
    /**
     * 明细售后对应订单明细ID
     */
    private Long orderItemId;
    /**
     * 退款类型。1：仅退款；2：退货退款
     */
    private Integer refundType;
    /**
     * 买家ID
     */
    private Long userId;
    /**
     * 买家账号
     */
    private String userName;
    /**
     * 供应商审核状态。1：待审核；2：审核通过；3：审核拒绝
     */
    private Integer sellerAuditStatus;
    /**
     * 平台审核状态
     */
    private Integer managerConfirmStatus;
    /**
     * 退款状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；5：待平台确认；6：退款成功；7：平台驳回；8：退款中；9：买家取消
     */
    private Integer refundStatus;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 退款模式。1：订单退款；2：货品退款；3：退货退款
     */
    private Integer refundMode;
    /**
     * 申请日期，时间戳格式
     */
    private Long applyDate;
    /**
     * 是否买家取消
     */
    private Boolean hasCancel;
    /**
     * 是否整单退
     */
    private Boolean hasAllReturn;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * sku id
     */
    private String skuId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 买家寄货物流公司
     */
    private String expressCompanyName;
    /**
     * 买家寄货物流单号
     */
    private String shipOrderNumber;
    /**
     * 买家寄货时间
     */
    private Long buyerDeliverDate;
    /**
     * 是否删除。0：否；1：是
     */
    private Integer isDelete;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 下单时间
     */
    private Long orderDate;

}
