package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * 店铺信息，包括店铺基本信息和店铺级别的金额
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
public class OrderShopBo {

    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 勾选的sku的总金额
     */
    private BigDecimal selectedTotalAmount;
    /**
     * 店铺商品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 店铺以及商品满足的营销信息。提交订单这个业务才会设置，用于保存订单的营销快照
     */
    private ShopAndProductPromotionBo shopAndProductPromotion;
    /**
     * 商品总数量
     */
    private Long productQuantity;
}
