package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.ThirdEventService;
import com.sankuai.shangou.seashop.order.core.service.model.ThirdPushEventBo;
import com.sankuai.shangou.seashop.order.thrift.core.ThirdEventCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPushEventReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.UpdatePushEventReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryPushEventResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.UpdatePushEventResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/thirdEvent")
public class ThirdEventCmdController implements ThirdEventCmdFeign {

    @Resource
    private ThirdEventService thirdEventService;

    @PostMapping(value = "/updateThirdPushEvent", consumes = "application/json")
    @Override
    public ResultDto<UpdatePushEventResp> updateThirdPushEvent(@RequestBody UpdatePushEventReq req) throws TException {
        log.info("[EVENT]事件推送, 请求参数={}", req);
        return ThriftResponseHelper.responseInvoke("updateThirdPushEvent", req, func -> {
            // 业务逻辑处理
            ThirdPushEventBo eventBo = new ThirdPushEventBo();
            eventBo.setSendCode(req.getSendCode());
            eventBo.setEventType(req.getEventType());
            eventBo.setSendState(req.getSendState());
            eventBo.setSendTarget(req.getSendTarget());
            eventBo.setEventBody(req.getEventBody());
            eventBo.setSendMsg(req.getSendMsg());
            eventBo.setId(req.getId());
            Long id = thirdEventService.saveOrUpdatePushEvent(eventBo);
            UpdatePushEventResp resp = new UpdatePushEventResp();
            resp.setId(id);
            return resp;
        });
    }

    @PostMapping(value = "/queryById", consumes = "application/json")
    @Override
    public ResultDto<QueryPushEventResp> queryById(@RequestBody QueryPushEventReq req) throws TException {
        log.info("[EVENT]事件查询, 请求参数={}", req);
        return ThriftResponseHelper.responseInvoke("queryById", req, func -> {
            // 业务逻辑处理
            ThirdPushEventBo thirdPushEventBo = thirdEventService.queryPushEventById(func.getId());
            return JsonUtil.copy(thirdPushEventBo, QueryPushEventResp.class);
        });
    }
}
