package com.sankuai.shangou.seashop.order.core.service.model.refund;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SellerApproveParamBo extends BaseParamReq {

    /**
     * 店铺信息
     */
    private ShopBo shop;
    private UserBo user;
    /**
     * 退款单号
     */
    @PrimaryField
    private Long refundId;
    /**
     * 供应商备注
     */
    private String sellerRemark;
    /**
     * 审核状态。2：待买家寄货(退货通过)；4：供应商拒绝；5：供应商通过审核(仅退款或者退货弃货)
     */
    @ExaminField(description = "审核状态")
    private RefundAuditStatusEnum auditStatus;
    /**
     * 是否退运费
     */
    private Boolean whetherRefundFreight;
    /**
     * 是否弃货，详情返回的refundType=2退货退款，才需要这个字段
     */
    private Boolean whetherAbandonGoods;

}
