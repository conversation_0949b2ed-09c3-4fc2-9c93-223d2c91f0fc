package com.sankuai.shangou.seashop.pay.core.bo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
@ToString
@TypeDoc(description = "查询渠道配置请求对象")
public class ChannelConfigQueryBo   {

    @FieldDoc(description = "支付渠道 1：汇付天下", requiredness = Requiredness.REQUIRED)
    private Integer paymentChannel;

    @FieldDoc(description = "配置Key值")
    private String configKey;

    @FieldDoc(description = "配置Key值列表")
    private List<String> configKeyList;
}
