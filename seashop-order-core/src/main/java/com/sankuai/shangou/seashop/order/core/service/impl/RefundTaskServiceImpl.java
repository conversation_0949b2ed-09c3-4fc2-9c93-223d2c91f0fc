package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundService;
import com.sankuai.shangou.seashop.order.core.service.RefundTaskService;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerApproveParamBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonRefundQueryParamPo;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RefundTaskServiceImpl implements RefundTaskService {

    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRefundService orderRefundService;

    @Override
    public void sellerAutoAudit() {
        log.info("【售后JOB】退款供应商自动审核通过");
        TradeSiteSettingBo tradeSiteSetting = settingRemoteService.getTradeSiteSetting();
        String shopConfirmTimeout = tradeSiteSetting.getShopConfirmTimeout();
        if (StrUtil.isBlank(shopConfirmTimeout)) {
            log.warn("【售后JOB】退款供应商自动审核通过, 交易设置中未配置shopConfirmTimeout");
            return;
        }
        Date today = new Date();
        Date expireApplyDate = DateUtils.addDays(today, -Integer.parseInt(shopConfirmTimeout));
        CommonRefundQueryParamPo param = CommonRefundQueryParamPo.builder()
                .sellerStatusEq(RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode())
                .isCancelEq(Boolean.FALSE)
                .applyDateLt(expireApplyDate)
                .build();
        log.info("【售后JOB】退款供应商自动审核通过, 查询条件:{}", JsonUtil.toJsonString(param));
        List<OrderRefund> orderRefunds = orderRefundRepository.getByCondition(param);
        if (CollUtil.isEmpty(orderRefunds)) {
            log.info("【售后JOB】退款供应商自动审核通过, 未查询到需要自动审核通过的退款单");
            return;
        }
        for (OrderRefund orderRefund : orderRefunds) {
            log.info("【售后JOB】退款供应商自动审核通过, orderRefund={}", JsonUtil.toJsonString(orderRefund));
            try {
                SellerApproveParamBo paramBo = new SellerApproveParamBo();
                paramBo.setRefundId(orderRefund.getId());
                paramBo.setSellerRemark(CommonConst.REFUND_SELLER_AUTO_AUDIT_REMARK);
                if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(orderRefund.getRefundMode())) {
                    paramBo.setAuditStatus(RefundAuditStatusEnum.WAIT_BUYER_SEND);
                } else {
                    paramBo.setAuditStatus(RefundAuditStatusEnum.SUPPLIER_PASS);
                }
                paramBo.setWhetherRefundFreight(false);
                paramBo.setWhetherAbandonGoods(false);
                ShopBo shop = new ShopBo();
                shop.setShopId(orderRefund.getShopId());
                paramBo.setShop(shop);
                UserBo user = new UserBo();
                user.setUserName(CommonConst.REFUND_SELLER_AUTO_AUDIT_USERNAME);
                paramBo.setUser(user);
                orderRefundService.sellerApprove(paramBo);
            } catch (Exception e) {
                log.error("【售后JOB】退款供应商自动审核通过, 异常, orderRefund={}", orderRefund, e);
            }
        }
    }

    @Override
    public void refundCloseWhenDeliveryExpire() {
        log.info("【售后JOB】买家超时未寄货，自动关闭售后");
        TradeSiteSettingBo tradeSiteSetting = settingRemoteService.getTradeSiteSetting();
        String closeTimeout = tradeSiteSetting.getSendGoodsCloseTimeout();
        if (StrUtil.isBlank(closeTimeout)) {
            log.warn("【售后JOB】买家超时未寄货，自动关闭售后");
            return;
        }
        Date today = new Date();
        Date expireApplyDate = DateUtils.addDays(today, -Integer.parseInt(closeTimeout));
        CommonRefundQueryParamPo param = CommonRefundQueryParamPo.builder()
                .sellerStatusEq(RefundAuditStatusEnum.WAIT_BUYER_SEND.getCode())
                .isCancelEq(Boolean.FALSE)
                .sellerAuditDateLt(expireApplyDate)
                .build();
        log.info("【售后】买家超时未寄货，自动关闭售后, 查询条件:{}", JsonUtil.toJsonString(param));
        List<OrderRefund> orderRefunds = orderRefundRepository.getByCondition(param);
        if (CollUtil.isEmpty(orderRefunds)) {
            log.info("【售后】买家超时未寄货，自动关闭售后, 未查询到需要自动审核通过的退款单");
            return;
        }
        for (OrderRefund orderRefund : orderRefunds) {
            log.info("【售后】买家超时未寄货，自动关闭售后, orderRefund={}", JsonUtil.toJsonString(orderRefund));
            try {
                SellerApproveParamBo paramBo = new SellerApproveParamBo();
                paramBo.setRefundId(orderRefund.getId());
                paramBo.setSellerRemark(CommonConst.REFUND_SELLER_AUTO_REJECT_REMARK);
                paramBo.setAuditStatus(RefundAuditStatusEnum.SUPPLIER_REFUSE);
                paramBo.setWhetherRefundFreight(false);
                paramBo.setWhetherAbandonGoods(false);
                ShopBo shop = new ShopBo();
                shop.setShopId(orderRefund.getShopId());
                paramBo.setShop(shop);
                UserBo user = new UserBo();
                user.setUserName(CommonConst.REFUND_SELLER_AUTO_AUDIT_USERNAME);
                paramBo.setUser(user);
                orderRefundService.sellerApprove(paramBo);
            } catch (Exception e) {
                log.error("【售后】买家超时未寄货，自动关闭售后, 异常, orderRefund={}", orderRefund, e);
            }
        }
    }

    @Override
    public void refundSellerAutoConfirmArrival() {
        log.info("【售后JOB】买家寄货后，供应商超时未确认收货，自动确认收货");
        TradeSiteSettingBo tradeSiteSetting = settingRemoteService.getTradeSiteSetting();
        String notReceivingTimeout = tradeSiteSetting.getShopNoReceivingTimeout();
        if (StrUtil.isBlank(notReceivingTimeout)) {
            log.warn("【售后JOB】供应商超时未确认收货，自动确认收货");
            return;
        }
        Date today = new Date();
        Date expireApplyDate = DateUtils.addDays(today, -Integer.parseInt(notReceivingTimeout));
        CommonRefundQueryParamPo param = CommonRefundQueryParamPo.builder()
                .sellerStatusEq(RefundAuditStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode())
                .isCancelEq(Boolean.FALSE)
                .buyerDeliveryDateLt(expireApplyDate)
                .build();
        log.info("【售后】供应商超时未确认收货，自动确认收货, 查询条件:{}", JsonUtil.toJsonString(param));
        List<OrderRefund> orderRefunds = orderRefundRepository.getByCondition(param);
        if (CollUtil.isEmpty(orderRefunds)) {
            log.info("【售后】供应商超时未确认收货，自动确认收货, 未查询到需要自动审核通过的退款单");
            return;
        }
        for (OrderRefund orderRefund : orderRefunds) {
            log.info("【售后】供应商超时未确认收货，自动确认收货, orderRefund={}", JsonUtil.toJsonString(orderRefund));
            try {
                SellerApproveParamBo paramBo = new SellerApproveParamBo();
                paramBo.setRefundId(orderRefund.getId());
                paramBo.setSellerRemark(CommonConst.REFUND_SELLER_AUTO_AUDIT_REMARK);
                paramBo.setAuditStatus(RefundAuditStatusEnum.SUPPLIER_PASS);
                paramBo.setWhetherRefundFreight(false);
                paramBo.setWhetherAbandonGoods(true);
                ShopBo shop = new ShopBo();
                shop.setShopId(orderRefund.getShopId());
                paramBo.setShop(shop);
                UserBo user = new UserBo();
                user.setUserName(CommonConst.REFUND_SELLER_AUTO_AUDIT_USERNAME);
                paramBo.setUser(user);
                orderRefundService.sellerApprove(paramBo);
            } catch (Exception e) {
                log.error("【售后】供应商超时未确认收货，自动确认收货, 异常, orderRefund={}", orderRefund, e);
            }
        }
    }



    //*********************************************************

}
