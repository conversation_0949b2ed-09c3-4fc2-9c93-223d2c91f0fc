package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.core.service.ThirdNoticeService;
import com.sankuai.shangou.seashop.order.thrift.core.ThirdNoticeCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.SaveThirdNoticeReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.SaveThirdNoticeResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/29 17:39
 */
@Slf4j
@RestController
@RequestMapping("/thirdNotice")
public class ThirdNoticeCmdController implements ThirdNoticeCmdFeign {

    @Resource
    private ThirdNoticeService thirdNoticeService;


    @PostMapping(value = "/saveThirdNotice", consumes = "application/json")
    @Override
    public ResultDto<SaveThirdNoticeResp> saveThirdNotice(@RequestBody SaveThirdNoticeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("saveThirdNotice", request, req -> {
            req.checkParameter();

            Long noticeId = thirdNoticeService.saveThirdNotice(req);
            SaveThirdNoticeResp resp = new SaveThirdNoticeResp();
            resp.setNoticeId(noticeId);
            return resp;
        });
    }
}
