package com.sankuai.shangou.seashop.order.core.mq.model.order;

import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderInvoice;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPromotionSnapshot;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * 订单数据
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderDataHolder {

    private List<Order> orderList;
    private List<OrderItem> orderItemList;
    private List<OrderInvoice> invoiceList;
    private List<OrderPromotionSnapshot> promotionSnapshotList;

    /**
     * 限时购活动ID
     */
    private Long flashSaleId;
    /**
     * 组合购活动ID
     */
    private Long collocationId;

    private List<String> orderIdList;
    // 需要支付的订单号，0元订单不需要支付
    private List<String> needPayOrderIdList;

}
