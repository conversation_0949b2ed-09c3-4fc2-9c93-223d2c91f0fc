package com.sankuai.shangou.seashop.pay.core.mq.publisher;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.pay.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.pay.core.mq.model.OrderReverseExceptionMsg;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description: 退款订单状态（未分账支付退款）异常订单通知
 */
@Slf4j
@Service
public class OrderReverseExceptionProducer {

//    @MafkaProducer(namespace = MafkaConstant.DEFAULT_NAMESPACE, topic = MafkaConstant.ORDER_REVERSE_EXCEPTION_TOPIC, delay = true)
//    private IProducerProcessor producerProcessor;

    @Resource
    private DefaultRocketMq defaultRocketMq;

    private static final Long DELAY_TIME = 1000 * 60 * 2L;

    public void sendMessage(OrderReverseExceptionMsg obj) {
        log.info("订单退款状态<未分账支付退款>异常订单通知生产者发送消息-obj：{}", JSONUtil.toJsonStr(obj));
        try {
//            ProducerResult producerResult = producerProcessor.sendDelayMessage(JSONUtil.toJsonStr(obj), DELAY_TIME);
            defaultRocketMq.convertAndSend(MafkaConstant.ORDER_REVERSE_EXCEPTION_TOPIC, obj);
            log.info("订单退款状态<未分账支付退款>异常订单通知生产者发送消息成功");
        } catch (Exception e) {
            log.error("订单退款状态<未分账支付退款>异常订单通知生产者发送消息失败-obj：{}", JSONUtil.toJsonStr(obj), e);
            throw new SystemException("订单退款状态<未分账支付退款>异常订单通知生产者发送消息失败");
        }
    }
}
