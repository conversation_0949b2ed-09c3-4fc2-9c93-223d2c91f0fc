package com.sankuai.shangou.seashop.order.core.service.model.order;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * 订单收货人信息
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class ErpOrderAddressBo {

    private String shipTo;

    private String cellPhone;

    private Integer regionId;

    private Integer topRegionId;

    private String address;

    private String regionFullName;

    private BigDecimal receiveLongitude;

    private BigDecimal receiveLatitude;

}
