package com.sankuai.shangou.seashop.order.core.service.model.stats;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

/**
 * 统计用户购买的sku请求入参
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class StatsUserPurchaseSkuParamBo extends BasePageReq {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 订单开始时间
     */
    private Date orderStartTime;
    /**
     * 订单结束时间
     */
    private Date orderEndTime;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * sku自增ID
     */
    private Long skuAutoId;
}
