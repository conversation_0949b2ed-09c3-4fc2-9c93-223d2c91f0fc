package com.sankuai.shangou.seashop.order.core.mq.listener;

import cn.hutool.crypto.digest.MD5;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.common.enums.MqErrorDataBizTypeEnum;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.service.assit.MqErrorDataAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.order.OrderChangeMessageAssist;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 消费订单状态变更的消息，处理订单相关数据
 *
 * <AUTHOR>
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_ORDER_CHANGE,
//        group = MafkaConst.GROUP_ORDER_CHANGE)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_CHANGE + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_CHANGE + "_${spring.profiles.active}")
public class OrderStatusChangeListener implements RocketMQListener<MessageExt> {

    @Resource
    private OrderChangeMessageAssist orderChangeMessageAssist;
    @Resource
    private MqErrorDataAssist mqErrorDataAssist;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【订单状态变更】【OrderStatusChangeListener】消息内容为: {}", body);

        OrderMessage orderMessage = null;
        try {
            orderMessage = JsonUtil.parseObject(body, OrderMessage.class);
        }
        catch (Exception e) {
            log.error("【mafka消费】【订单状态变更】消息转换失败", e);
            String traceId = message.getMsgId();
            mqErrorDataAssist.saveErrorData(MqErrorDataBizTypeEnum.ORDER_CHANGE, MD5.create().digestHex(body), message.getMsgId(), traceId,
                e.getMessage(), body);
            return;
        }
        // 事件处理
        try {
            orderChangeMessageAssist.handleRefundMessage(orderMessage);
        }
        catch (Exception e) {
            log.error("【mafka消费】【订单状态变更】处理失败", e);
            String traceId = message.getMsgId();
            mqErrorDataAssist.saveErrorData(MqErrorDataBizTypeEnum.ORDER_CHANGE, orderMessage.getOrderId(), message.getMsgId(), traceId,
                e.getMessage(), body);
            throw new RuntimeException(e);
        }
    }
}
