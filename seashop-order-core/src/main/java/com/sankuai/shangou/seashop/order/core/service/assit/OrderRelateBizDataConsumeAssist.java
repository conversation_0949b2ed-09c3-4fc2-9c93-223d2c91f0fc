package com.sankuai.shangou.seashop.order.core.service.assit;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.enums.TResultCode;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.common.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.StockRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.product.DecreaseStockBo;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderDataHolder;
import com.sankuai.shangou.seashop.order.core.service.model.order.CreateOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShopProductBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShopProductListBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.CouponRecordConsumeDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.flashsale.FlashSaleConsumeReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.service.FlashSaleCmdFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderRelateBizDataConsumeAssist {

    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private StockRemoteService stockRemoteService;
    @Resource
    private FlashSaleCmdFeign flashSaleCmdFeign;

    public void consumeOrderRelateBizData(OrderDataHolder orderDataHolder, CreateOrderBo createOrderBo) {
        // 扣减库存
        decreaseStock(orderDataHolder.getOrderItemList());
        // 核销优惠券
        consumePromotion(createOrderBo);
        // 扣减限时购库存，目前限时购没有预占商品库存，所以两个都需要扣
        decreaseFlashSaleStock(orderDataHolder, createOrderBo.getCreateTime());
    }


    //******************************************************************

    /**
     * 扣减库存
     * @param orderItemList
     */
    private void decreaseStock(List<OrderItem> orderItemList) {
        // 分配扣减库存
        try {
            stockRemoteService.decreaseStock(buildStockList(orderItemList));
        }
        catch (BusinessException be) {
            log.error("【订单创建】扣减库存失败", be);
            throw be;
        }
        catch (Exception e) {
            log.error("【订单创建】扣减库存失败", e);
            throw new BusinessException(TResultCode.SERVER_ERROR.value(), "创建订单失败", e);
        }
    }

    /**
     * 核销营销活动
     * @param createOrderBo
     */
    private void consumePromotion(CreateOrderBo createOrderBo) {
        // 核销营销活动
        try {
            List<CouponRecordConsumeDto> consumeList = buildCouponConsumeList(createOrderBo.getShopProductList());
            if (CollUtil.isEmpty(consumeList)) {
                return;
            }
            // 在构建订单数据时，createOrderBo 中的 对象会设置 生成的 orderId
            promotionRemoteService.chargeCoupon(createOrderBo.getUserInfo().getUserId(), consumeList, createOrderBo.getCreateTime());
        }
        catch (BusinessException be) {
            log.error("【订单创建】核销优惠券失败", be);
            throw be;
        }
        catch (Exception e) {
            log.error("【订单创建】核销优惠券失败", e);
            throw new BusinessException(TResultCode.SERVER_ERROR.value(), "创建订单失败", e);
        }
    }


    private void decreaseFlashSaleStock(OrderDataHolder orderDataHolder, Date orderCreateTime) {
        // 非限时购活动不需要扣减库存
        if (orderDataHolder.getFlashSaleId() == null) {
            return;
        }
        // 扣减限时购库存，
        try {
            FlashSaleConsumeReq consumeReq = buildFlashSaleConsumeReq(orderDataHolder, orderCreateTime);
            ThriftResponseHelper.executeThriftCall(() -> flashSaleCmdFeign.consume(consumeReq));
        }
        catch (BusinessException be) {
            log.error("【订单创建】扣减限时购库存失败", be);
            throw be;
        }
        catch (Exception e) {
            log.error("【订单创建】扣减限时购库存失败", e);
            throw new BusinessException(TResultCode.SERVER_ERROR.value(), "创建订单失败", e);
        }
    }


    private List<DecreaseStockBo> buildStockList(List<OrderItem> orderItemList) {
        return orderItemList.stream()
                .map(item -> {
                    DecreaseStockBo decreaseStockBo = new DecreaseStockBo();
                    decreaseStockBo.setOrderId(item.getOrderId());
                    decreaseStockBo.setShopId(item.getShopId());
                    decreaseStockBo.setProductId(item.getProductId());
                    decreaseStockBo.setSkuId(item.getSkuId());
                    decreaseStockBo.setQuantity(item.getQuantity());
                    return decreaseStockBo;
                })
                .collect(Collectors.toList());
    }

    private List<CouponRecordConsumeDto> buildCouponConsumeList(List<ShopProductListBo> shopProductList) {
        return shopProductList.stream()
                .filter(shopOrder -> shopOrder.getAdditional() != null && shopOrder.getAdditional().getCouponRecordId() != null)
                .map(shopOrder -> {
                    CouponRecordConsumeDto dto = new CouponRecordConsumeDto();
                    dto.setOrderId(shopOrder.getOrderId());
                    dto.setShopId(shopOrder.getShop().getShopId());
                    Long couponRecordId = shopOrder.getAdditional().getCouponRecordId();
                    if (couponRecordId == null || couponRecordId == 0) {
                        return null;
                    }
                    dto.setCouponRecordId(shopOrder.getAdditional().getCouponRecordId());
                    // 找出明细中有优惠券ID的，即代表当前订单适用的优惠券适用的商品
                    List<Long> productIdList = shopOrder.getProductList().stream()
                            .filter(p -> p.getCouponId() != null)
                            .map(ShopProductBo::getProductId)
                            .collect(Collectors.toList());
                    if (CollUtil.isEmpty(productIdList)) {
                        return null;
                    }
                    dto.setProductIds(productIdList);
                    return dto;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private FlashSaleConsumeReq buildFlashSaleConsumeReq(OrderDataHolder orderDataHolder, Date orderCreateTime) {
        // 限时购下单每次只会有一个订单，一个商品
        Order order = orderDataHolder.getOrderList().get(0);
        OrderItem orderItem = orderDataHolder.getOrderItemList().get(0);
        FlashSaleConsumeReq req = new FlashSaleConsumeReq();
        req.setFlashSaleId(orderDataHolder.getFlashSaleId());
        req.setMemberId(order.getUserId());
        req.setOrderId(order.getOrderId());
        req.setProductId(orderItem.getProductId());
        req.setSkuId(orderItem.getSkuId());
        req.setConsumeNum(orderItem.getQuantity().intValue());
        req.setConsumeTime(orderCreateTime);
        return req;
    }



}
