package com.sankuai.shangou.seashop.pay.core.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseResultDto;
import com.sankuai.shangou.seashop.pay.core.mq.publisher.OrderReverseProducer;
import com.sankuai.shangou.seashop.pay.core.service.ReverseOrderService;
import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ReverseOrder;
import com.sankuai.shangou.seashop.pay.dao.core.repository.OrderPayRepository;
import com.sankuai.shangou.seashop.pay.dao.core.repository.ReverseOrderRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayResultCodeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ReverseOrderQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.ReverseOrderResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
@Service
@Slf4j
public class ReverseOrderServiceImpl implements ReverseOrderService {

    @Resource
    private ReverseOrderRepository reverseOrderRepository;
    @Resource
    private OrderPayRepository orderPayRepository;
    @Resource
    private OrderReverseProducer orderReverseProducer;

    @Override
    public void updateAndSendSyncReverse(AdaPayReverseResultDto adaPayReverseResultDto) {
        log.info("更新并发送退款信息={}:", JsonUtil.toJsonString(adaPayReverseResultDto));
        boolean update = updateReverseOrder(ReverseOrder.builder()
                .reverseId(adaPayReverseResultDto.getRefundId())
                .channelRefundMsg(adaPayReverseResultDto.getErrorMessage())
                .reverseState(adaPayReverseResultDto.getPayStatus())
                .channelRefundId(adaPayReverseResultDto.getChannelRefundId())
                .build());
        if (update) {
            ReverseOrder reverseOrder = reverseOrderRepository.getByReverseId(adaPayReverseResultDto.getRefundId());
            OrderPay orderPay = orderPayRepository.getOne(Wrappers.<OrderPay>lambdaQuery().eq(OrderPay::getChannelPayId, reverseOrder.getChannelPayId()).eq(OrderPay::getPayState, PayStateEnums.PAID.getStatus()));
            adaPayReverseResultDto.setOrderNo(orderPay.getOrderId());
            adaPayReverseResultDto.setBusinessType(orderPay.getBusinessType());
            adaPayReverseResultDto.setChannelRefundId(reverseOrder.getChannelRefundId());
            adaPayReverseResultDto.setBusinessStatusType(reverseOrder.getBusinessStatusType());
            orderReverseProducer.sendMessage(adaPayReverseResultDto);
            log.info("退款结果通知={}:", adaPayReverseResultDto);
        }
    }

    @Override
    public ReverseOrderResp getOne(ReverseOrderQueryReq request) {
        ReverseOrder reverseOrder = reverseOrderRepository.getOne(
                Wrappers.<ReverseOrder>lambdaQuery()
                        .eq(StrUtil.isNotBlank(request.getReverseId()), ReverseOrder::getReverseId, request.getReverseId())
                        .eq(StrUtil.isNotBlank(request.getChannelRefundId()), ReverseOrder::getChannelRefundId, request.getChannelRefundId())
        );
        if (null == reverseOrder) {
            throw new BusinessException(PayResultCodeEnum.REVERSE_NOT_EXIST.getCode(), PayResultCodeEnum.REVERSE_NOT_EXIST.getMsg());
        }
        return JsonUtil.copy(reverseOrder, ReverseOrderResp.class);
    }

    private boolean updateReverseOrder(ReverseOrder reverseOrder) {
        return reverseOrderRepository.update(
                Wrappers.<ReverseOrder>lambdaUpdate().eq(ReverseOrder::getReverseId, reverseOrder.getReverseId())
                        .eq(ReverseOrder::getReverseState, ReverseStateEnums.REVERSE_ING.getStatus())
                        .set(ReverseOrder::getChannelRefundMsg, reverseOrder.getChannelRefundMsg())
                        .set(ReverseOrder::getReverseState, reverseOrder.getReverseState())
                        .set(StrUtil.isNotBlank(reverseOrder.getChannelRefundId()), ReverseOrder::getChannelRefundId, reverseOrder.getChannelRefundId()));
    }
}
