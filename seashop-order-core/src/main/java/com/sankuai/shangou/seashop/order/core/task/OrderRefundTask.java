package com.sankuai.shangou.seashop.order.core.task;

import javax.annotation.Resource;

import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import org.springframework.stereotype.Component;


import com.sankuai.shangou.seashop.order.core.service.RefundTaskService;
import com.xxl.job.core.handler.annotation.XxlJob;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class OrderRefundTask {

    @Resource
    private RefundTaskService refundTaskService;

    /**
     * 退款供应商自动审核通过
     * 根据交易设置，用户提交售后后超过N天，供应商未处理，视为自动审核通过
     */
//    @Crane("RefundSellerAutoAuditTask")
    @XxlJob("refundSellerAutoAuditTask")
    @XxlRegister(cron = "0 15,45 * * * ?",
            author = "snow",
            jobDesc = "退款供应商自动审核通过")
    public void refundSellerAutoAudit() {
        refundTaskService.sellerAutoAudit();
    }

    /**
     * 供应商审核通过后，需要买家寄货的，超过N天，退款自动关闭
     */
//    @Crane("RefundCloseWhenDeliveryExpireTask")
    @XxlJob("refundCloseWhenDeliveryExpireTask")
    @XxlRegister(cron = "0 15,45 * * * ?",
            author = "snow",
            jobDesc = "供应商审核通过后，需要买家寄货的，超过N天，退款自动关闭")
    public void refundCloseWhenDeliveryExpire() {
        refundTaskService.refundCloseWhenDeliveryExpire();
    }

    /**
     * 买家寄货后，供应商超过N天未处理，自动确认收货，进入下一步骤：待平台确认
     */
//    @Crane("RefundCloseWhenDeliveryExpireTask")
    @XxlJob("refundSellerAutoConfirmArrival")
    @XxlRegister(cron = "0 15,45 * * * ?",
            author = "snow",
            jobDesc = "买家寄货后，供应商超过N天未处理，自动确认收货，进入下一步骤：待平台确认")
    public void refundSellerAutoConfirmArrival() {
        refundTaskService.refundSellerAutoConfirmArrival();
    }


}
