package com.sankuai.shangou.seashop.order.core.service.model.refund;

import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPlatformQueryTabEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * 商家查询售后记录请求参数
 * <AUTHOR>
 */
@Getter
@Setter
public class PlatformQueryRefundBo extends QueryRefundBaseBo {

    /**
     * 用户信息
     */
    private UserBo user;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 买家账号。等价于EP账号
     */
    private String userName;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 商品ID/规格ID
     */
    private String productIdOrSkuId;
    /**
     * 退款状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；5：待平台确认；6：退款成功；7：平台驳回；8：退款中；9：买家取消
     */
    private RefundStatusEnum refundStatus;
    /**
     * 查询类型。1：退款-全部；2：退款-待处理；3：退货-全部；4：退货-待处理
     */
    private RefundPlatformQueryTabEnum tab;

}
