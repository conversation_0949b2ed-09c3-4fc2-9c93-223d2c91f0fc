package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.core.service.model.order.ExceptionOrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryExceptionOrderParamBo;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.ConfirmExceptionOrderReq;

/**
 * <AUTHOR>
 */
public interface ExceptionOrderService {


    /**
     * 分页获取异常订单列表
     * @param paramBo 查询参数
     * @return 异常订单列表
     */
    BasePageResp<ExceptionOrderInfoBo> pageList(BasePageParam pageParam, QueryExceptionOrderParamBo paramBo);

    /**
     * 确认退款
     * @param confirmReq 确认退款请求
     */
    void confirmRefund(ConfirmExceptionOrderReq confirmReq);

}
