package com.sankuai.shangou.seashop.order.core.service.assit.pay;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.core.mq.model.pay.PayResultBo;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderMessagePublisher;
import com.sankuai.shangou.seashop.order.core.service.assit.settlement.SettleAmountCalculator;
import com.sankuai.shangou.seashop.order.dao.core.domain.ExceptionOrder;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.ExceptionOrderRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Finance;
import com.sankuai.shangou.seashop.order.dao.finance.domain.FinanceItem;
import com.sankuai.shangou.seashop.order.dao.finance.domain.PendingSettlementOrder;
import com.sankuai.shangou.seashop.order.dao.finance.repository.AccountDetailRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceItemRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.PendingSettlementOrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.PlatAccountRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.ShopAccountRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.ExceptionOrderRefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.ExceptionOrderTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderDeliveryTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PaymentTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 对订单支付结果处理的辅助类
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderPayResultHandlerAssist {

    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private PendingSettlementOrderRepository pendingSettlementOrderRepository;
    @Resource
    private AccountDetailRepository accountDetailRepository;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private FinanceRepository financeRepository;
    @Resource
    private FinanceItemRepository financeItemRepository;
    @Resource
    private ExceptionOrderRepository exceptionOrderRepository;
    @Resource
    private ShopAccountRepository shopAccountRepository;
    @Resource
    private PlatAccountRepository platAccountRepository;
    @Resource
    private OrderMessagePublisher orderMessagePublisher;
    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private SettleAmountCalculator settleAmountCalculator;

    public void handlePayResult(PayResultBo payResultBo) {
        String batchNo = payResultBo.getOrderId();
        if (StrUtil.isBlank(batchNo)) {
            log.error("【支付回调】MQ消息中的batchNo为空，不处理");
            return;
        }
        // 通过batchNo查询支付记录，每个订单只有一条记录，但因为合并支付，一个batchNo可以对应多个订单
        List<OrderPayRecord> payRecordList = orderPayRecordRepository.getByBatchNo(batchNo);
        log.info("【支付回调】MQ消息中的batchNo:{}, 对应的支付记录:{}", batchNo, JsonUtil.toJsonString(payRecordList));
        if (CollUtil.isEmpty(payRecordList)) {
            log.error("【支付回调】MQ消息中的batchNo:{}在订单支付记录表中不存在，不处理", batchNo);
            return;
        }
        // 支付失败
        if (!PayStateEnums.PAID.getStatus().equals(payResultBo.getPayStatus())) {
            log.warn("【支付回调】MQ消息中的batchNo:{}支付状态不是支付成功", batchNo);
            // 支付失败处理
            handleFailure(payResultBo, payRecordList);
            return;
        }
        // 支付成功处理
        handleSuccess(payResultBo, payRecordList);
    }



    //**********************************************************************


    private void handleFailure(PayResultBo payResultBo, List<OrderPayRecord> payRecordList) {
        boolean anySuccess = PayStatusEnum.PAY_SUCCESS.getCode().equals(payRecordList.get(0).getPayStatus());
        if (anySuccess) {
            log.warn("【支付回调】MQ消息中的batchNo:{}支付状态不是支付成功，但是在订单支付记录表中存在支付成功的记录，不处理", payResultBo.getOrderId());
            return;
        }
        List<String> orderIdList = payRecordList.stream()
                .map(OrderPayRecord::getOrderId)
                .collect(Collectors.toList());

        List<LockKey> lockKeyList = orderIdList.stream()
                .map(orderId -> new LockKey(LockConst.SCENE_ORDER_STATUS_CHANGE, orderId))
                .collect(Collectors.toList());


        Date now = new Date();

        distributedLockService.tryMultiReentrantLock(lockKeyList, () -> {

            List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
            // 因为是支付回调，可能有延迟，所以需要判断订单状态是否是支付中，如果是支付中，则需要修改订单状态
            List<Order> payingOrderList = orderList.stream()
                    .filter(order -> OrderStatusEnum.PAYING.getCode().equals(order.getOrderStatus()))
                    .collect(Collectors.toList());

            // 支付记录是对应的都需要修改
            payRecordList.forEach(payRecord -> {
                payRecord.setPayStatus(PayStatusEnum.PAY_FAIL.getCode());
                payRecord.setPayTime(payResultBo.getPayTime());
                payRecord.setPayNo(payResultBo.getPayId());
                payRecord.setRemark(payResultBo.getErrorMsg());
                payRecord.setOutTransId(payResultBo.getOutTransId());
                payRecord.setUpdateTime(now);
            });
            // 待付款的订单才修改状态
            if (CollUtil.isNotEmpty(payingOrderList)) {
                payingOrderList.forEach(order -> {
                    order.setOrderStatus(OrderStatusEnum.UNDER_PAY.getCode());
                    order.setLastModifyTime(now);
                });
            }
            TransactionHelper.doInTransaction(() -> {
                orderPayRecordRepository.updateBatchById(payRecordList);
                if (CollUtil.isNotEmpty(payingOrderList)) {
                    orderRepository.updateBatchById(orderList);
                }
            });
        });
    }

     private void handleSuccess(PayResultBo payResultBo, List<OrderPayRecord> payRecordList) {
        List<String> orderIdList = payRecordList.stream()
                .map(OrderPayRecord::getOrderId)
                .distinct()
                .collect(Collectors.toList());
        if (payRecordList.size() != orderIdList.size()) {
            log.error("【支付回调】MQ消息中的batchNo:{}在订单支付记录表中存在重复的订单号，不处理", payResultBo.getOrderId());
            return;
        }

        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
        List<Long> shopIdList = orderList.stream()
                .map(Order::getShopId)
                .collect(Collectors.toList());
        Map<Long, ShopResp> shopMap = shopRemoteService.getShopMap(shopIdList);
        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(orderIdList);
        Map<String, List<OrderItem>> orderItemMap = orderItemList.stream()
                .collect(Collectors.groupingBy(OrderItem::getOrderId));
        // 锁的粒度统一用orderId，虽然可能同一笔订单同时发起单笔支付和合并支付，但锁到订单维度也可以，还能保证后续的锁的级别一致
        List<LockKey> lockKeyList = orderIdList.stream()
                .map(orderId -> new LockKey(LockConst.SCENE_ORDER_STATUS_CHANGE, orderId))
                .collect(Collectors.toList());

        distributedLockService.tryMultiReentrantLock(lockKeyList, () -> {
            // 锁里面重新查一次支付记录，可能其他线程拿到支付回调，所以需要重新查一次
            List<OrderPayRecord> currentPayRecordList = orderPayRecordRepository.getByBatchNo(payResultBo.getOrderId());
            Map<String, OrderPayRecord> payRecordMap = currentPayRecordList.stream()
                    .collect(Collectors.toMap(OrderPayRecord::getOrderId, v -> v, (oldV, newV) -> newV));
            // 遍历处理订单，待付款状态的订单，拿到支付回调，也可以支付成功，因为可能是用户发起支付且实际支付后，尚未拿到支付回调之前用户点击了取消支付
            for (Order order : orderList) {
                log.info("【支付回调】orderId={}, 订单信息:{}", order.getOrderId(), JsonUtil.toJsonString(order));
                OrderPayRecord orderPayRecord = payRecordMap.get(order.getOrderId());
                log.info("【支付回调】orderId={}, 订单支付记录信息:{}", order.getOrderId(), JsonUtil.toJsonString(orderPayRecord));
                if (orderPayRecord == null) {
                    log.error("【支付回调】订单号:{}, 在订单支付记录表中不存在支付记录", order.getOrderId());
                    continue;
                }
                // 已经有支付结果，则不需要继续处理
                if (PayStatusEnum.PAY_SUCCESS.getCode().equals(orderPayRecord.getPayStatus()) || PayStatusEnum.PAY_FAIL.getCode().equals(order.getOrderStatus())) {
                    log.info("【支付回调】订单号:{}, 订单状态:{}, 支付记录状态:{}, 不需要修改订单", order.getOrderId(), order.getOrderStatus(), orderPayRecord.getPayStatus());
                    continue;
                }
                // 有支付回调，且记录状态不是成功，则需要修改支付记录数据，但如果订单已经是支付的，则不再需要修改订单
                // 这个情况一般是重复支付
                boolean needUpdateOrder = OrderStatusEnum.PAYING.getCode().equals(order.getOrderStatus()) ||
                        OrderStatusEnum.UNDER_PAY.getCode().equals(order.getOrderStatus());
                // 下面的设置必须要设置，不需要更新的后面会判断是否更新，设置的目的是待结算数据的保存需要泳道
                // 设置支付时间
                order.setPayDate(payResultBo.getPayTime());
                order.setPaymentType(PaymentTypeEnum.ONLINE.getCode());
                order.setPayment(orderPayRecord.getPayMethod());
                order.setPayBankCode(payResultBo.getBankCode());
                // 支付成功进入下一步骤
                Integer orderStatus = this.judgeNextOrderStatus(order);
                order.setOrderStatus(orderStatus);
                // 设置实收金额=实付金额
                order.setActualPayAmount(order.getTotalAmount());
                order.setGatewayOrderId(payResultBo.getOutTransId());
                order.setPayRemark(payResultBo.getErrorMsg());
                order.setLastModifyTime(new Date());

                // 修改订单支付记录
                orderPayRecord.setPayStatus(PayStatusEnum.PAY_SUCCESS.getCode());
                orderPayRecord.setPayTime(payResultBo.getPayTime());
                orderPayRecord.setOutTransId(payResultBo.getOutTransId());
                orderPayRecord.setPayAmount(payResultBo.getPayAmount());
                orderPayRecord.setUpdateTime(new Date());

                // 更新order与order_pay_record
                TransactionHelper.doInTransaction(() -> {
                    if (needUpdateOrder) {
                        orderRepository.updateById(order);
                    }
                    orderPayRecordRepository.updateById(orderPayRecord);
                });

                // 发送通知，比如需要更新订单ES
                if (needUpdateOrder) {
                    orderMessagePublisher.sendOrderChangeMessage(order.getOrderId(), OrderMessageEventEnum.PAY_SUCCESS);
                }
            }

            // 保存待结算和财务中间表，不在前面事务一起，后续有补偿
            // 不能与单个订单放一起，结算的处理需要查询当前批次的订单
            try {
                // 重新查一次支付记录，且走主库，前面事务已经保存了最新的状态
                List<OrderPayRecord> newPayRecordList = orderPayRecordRepository.getByBatchNoForceMaster(payResultBo.getOrderId());
                saveSettlementAndFinance(orderList, orderItemMap, shopMap, payRecordMap, newPayRecordList, payResultBo.getPayId());
            } catch (Exception e) {
                log.error("【支付回调】保存待结算和财务中间表异常，订单号列表:{}", orderIdList, e);
            }

            // 异常订单处理
            handleExceptionOrder(orderIdList, orderList, payRecordMap, payResultBo.getPayId());
        });
    }

    private void saveSettlementAndFinance(List<Order> orderList, Map<String/*orderId*/, List<OrderItem>> orderItemMap,
                                          Map<Long/*shopId*/, ShopResp> shopMap,
                                          Map<String/*orderId*/, OrderPayRecord> payRecordMap,
                                          List<OrderPayRecord> batchNoRecordList, String payNo) {
        for (Order order : orderList) {
            TransactionHelper.doInTransaction(() -> {
                // 已经存在记录，则不需要再保存
                PendingSettlementOrder settlementOrder = pendingSettlementOrderRepository.getByOrderIdForceMaster(order.getOrderId());
                if (settlementOrder == null) {
                    settlementOrder = buildPendingSettlement(order, orderItemMap.get(order.getOrderId()),
                            shopMap.get(order.getShopId()), batchNoRecordList, orderList, payNo);
                    log.info("【支付回调】orderId={}, 待结算信息:{}", order.getOrderId(), JsonUtil.toJsonString(settlementOrder));
                    pendingSettlementOrderRepository.save(settlementOrder);
                    // 更新店铺资金账户、平台资金账户
                    shopAccountRepository.updatePendingSettlement(order.getShopId(), settlementOrder.getSettlementAmount());
                    platAccountRepository.updatePendingSettlement(settlementOrder.getSettlementAmount());
                }
                // 财务中间表，即使重复重复也保存记录，用于对账。
                // 用 payNo 判断是否是重试的，重复支付的，payNo 不同
                Finance existFinance = financeRepository.getByOrderIdAndPayNoForeMaster(order.getOrderId(), payNo);
                if (existFinance == null) {
                    saveFinanceInfo(order, orderItemMap.get(order.getOrderId()), payRecordMap.get(order.getOrderId()), settlementOrder);
                }
            });
        }
    }

    /**
     * java 版，对于重复支付，需要重复保存待结算数据
     * <AUTHOR>
     */
    private PendingSettlementOrder buildPendingSettlement(Order order, List<OrderItem> orderItemList,
                                                          ShopResp shop, List<OrderPayRecord> batchNoRecordList,
                                                          List<Order> orderList, String payNo) {

        // 获取店铺信息，用于设置是否自营
        boolean isSelf = Boolean.TRUE.equals(shop.getWhetherSelf());
        PendingSettlementOrder settlementOrder = new PendingSettlementOrder();
        settlementOrder.setSelfFlag(isSelf);
        settlementOrder.setShopId(order.getShopId());
        settlementOrder.setShopName(order.getShopName());
        settlementOrder.setOrderId(order.getOrderId());
        settlementOrder.setFreightAmount(order.getFreight());
        settlementOrder.setTaxAmount(order.getTax());
        settlementOrder.setOrderType(order.getOrderType());
        // order表的payDate也是回调的时候先设置并保存到数据库的，来源都是回调返回的
        settlementOrder.setPayDate(order.getPayDate());
        settlementOrder.setRefundAmount(BigDecimal.ZERO);
        settlementOrder.setPlatCommissionReturn(BigDecimal.ZERO);
        settlementOrder.setDiscountAmount(BigDecimal.ZERO);
        // 渠道金额，等价于.net的 adaPayAmount
        BigDecimal channelAmount = settleAmountCalculator.calculate(order, batchNoRecordList, orderList);
        if (order.getActualPayAmount().compareTo(BigDecimal.ZERO) <= 0) {
            log.info("【支付回调】orderId={}, 订单金额为0，渠道金额设置为0", order.getOrderId());
            channelAmount = BigDecimal.ZERO;
        }
        log.info("【支付回调】orderId={}, 渠道金额:{}", order.getOrderId(), channelAmount);
        settlementOrder.setChannelAmount(channelAmount);

        // 计算平台佣金
        BigDecimal commissionAmount = orderItemList.stream()
                .map(item -> NumberUtil.mul(item.getRealTotalPrice(), item.getCommisRate()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("【支付回调】orderId={}, 平台佣金:{}", order.getOrderId(), commissionAmount);
        // 产生了汇付手续费
        if (channelAmount.compareTo(BigDecimal.ZERO) > 0) {
            log.info("【支付回调】orderId={}, 产生了汇付手续费", order.getOrderId());
            BigDecimal channelCommission = order.getActualPayAmount().subtract(channelAmount);
            if (commissionAmount.compareTo(channelCommission) >= 0) {
                commissionAmount = channelCommission;
            }
        }
        commissionAmount = commissionAmount.setScale(2, RoundingMode.HALF_UP);
        log.info("【支付回调】orderId={}, 最终平台佣金:{}", order.getOrderId(), commissionAmount);
        settlementOrder.setPlatCommission(commissionAmount);

        // 订单商品实付金额，productTotalAmount - discountAmount - fullDiscount - moneyOff
        BigDecimal productTotalAmount = order.getProductTotalAmount()
                .subtract(order.getCouponAmount())
                .subtract(order.getDiscountAmount())
                .subtract(order.getMoneyOffAmount())
                .setScale(2, RoundingMode.HALF_UP);
        settlementOrder.setProductsAmount(productTotalAmount);

        settlementOrder.setPaymentType(order.getPayment());
        settlementOrder.setPaymentTypeName(PayMethodEnum.getDesc(order.getPayment()));

        // 结算金额：settlementAmount = o.ActualPayAmount - item.PlatCommission - refundAmount + item.PlatCommissionReturn - item.AdpayAmount;
        BigDecimal settlementAmount = order.getActualPayAmount()
                .subtract(commissionAmount)
                .subtract(settlementOrder.getRefundAmount())
                .add(settlementOrder.getPlatCommissionReturn())
                .subtract(settlementOrder.getChannelAmount())
                .setScale(2, RoundingMode.HALF_UP);
        log.info("【支付回调】orderId={}, 实付金额={}, 平台佣金={}, 结算中的退款金额={}, 结算中的平台佣金返回={}, 结算中的渠道金额={}, 结算金额={}",
                order.getOrderId(), order.getActualPayAmount(), commissionAmount, settlementOrder.getRefundAmount(),
                settlementOrder.getPlatCommissionReturn(), settlementOrder.getChannelAmount(), settlementAmount);
        settlementOrder.setSettlementAmount(settlementAmount);
        settlementOrder.setCreateTime(new Date());
        settlementOrder.setOrderAmount(order.getTotalAmount());
        if (order.getFinishDate() != null) {
            settlementOrder.setOrderFinishTime(order.getFinishDate());
        }
        return settlementOrder;
    }

    private void saveFinanceInfo(Order order, List<OrderItem> orderItemList, OrderPayRecord payRecord, PendingSettlementOrder settlementOrder) {
        log.info("【支付回调】保存财务中间表，订单号:{}", order.getOrderId());
        Finance finance = new Finance();
        finance.setOrderId(order.getOrderId());
        finance.setAdapayId(payRecord.getPayNo());
        finance.setPayId(payRecord.getBatchNo());
        finance.setType(TransactionTypesEnum.PAY.getCode());
        finance.setCreateDate(order.getPayDate());
        finance.setShopId(order.getShopId());
        finance.setShopName(order.getShopName());
        finance.setUserId(order.getUserId());
        finance.setUserName(order.getUserName());
        finance.setTransactionId(StrUtil.nullToDefault(order.getGatewayOrderId(), ""));
        finance.setTotalAmount(order.getTotalAmount());
        finance.setFreight(order.getFreight());
        finance.setProductAmount(order.getProductTotalAmount());
        finance.setDiscountAmount(order.getCouponAmount());
        finance.setPlatDiscountAmount(BigDecimal.ZERO);
        finance.setFullDiscount(order.getDiscountAmount());
        finance.setMoneyOff(order.getMoneyOffAmount());
        // 字段值确认
        finance.setServiceAmount(NumberUtil.nullToZero(settlementOrder.getChannelAmount()));
        finance.setSettlementAmount(NumberUtil.nullToZero(settlementOrder.getSettlementAmount()));
        finance.setCommissionAmount(NumberUtil.nullToZero(settlementOrder.getPlatCommission()));
        finance.setActualPayAmount(payRecord.getPayAmount());

        financeRepository.save(finance);

        List<FinanceItem> financeItemList = orderItemList.stream()
                .map(oi -> {
                    FinanceItem item = new FinanceItem();
                    // 需要先保存财务中间表，才能获取到id
                    item.setFinanceId(finance.getId());
                    item.setOrderId(order.getOrderId());
                    item.setProductId(oi.getProductId());
                    item.setProductName(oi.getProductName());
                    item.setSku(oi.getSku());
                    item.setQuantity(oi.getQuantity());
                    item.setOriginalPrice(oi.getCostPrice());
                    item.setSalePrice(oi.getSalePrice());
                    item.setTotalAmount(oi.getRealTotalPrice());
                    item.setDiscountPrice(oi.getDiscountAmount());
                    item.setFullDiscount(oi.getFullDiscount());
                    item.setMoneyOff(oi.getMoneyOff());
                    item.setCouponDiscount(oi.getCouponDiscount());
                    // 没有平台优惠券业务了，字段设置为0
                    item.setPlatCouponDiscount(BigDecimal.ZERO);
                    item.setCommisRate(oi.getCommisRate());
                    return item;
                })
                .collect(Collectors.toList());

        financeItemRepository.saveBatch(financeItemList);

    }


    private void handleExceptionOrder(List<String> orderIdList, List<Order> orderList, Map<String/*orderId*/, OrderPayRecord> payRecordMap, String payNo) {
        // 一个订单可能会有多条支付关系，这里异常订单的处理要以订单为维度判断
        List<OrderPayRecord> payRecordList = orderPayRecordRepository.getByOrderIdListAndPayStatusForceMaster(orderIdList, Collections.singletonList(PayStatusEnum.PAY_SUCCESS.getCode()));
        log.info("【支付回调】异常订单处理，订单号列表:{}, 支付成功的记录:{}", JsonUtil.toJsonString(orderIdList), JsonUtil.toJsonString(payRecordList));
        // 前面已经处理更新了支付记录，如果此时没有支付成功的，说明没有支付成功的记录，就不存在要处理异常订单的情况
        if (CollUtil.isEmpty(payRecordList)) {
            log.warn("【支付回调】异常订单处理，订单号列表:{}在订单支付记录表中不存在支付成功的记录", orderIdList);
            return;
        }
        Map<String, Order> orderMap = orderList.stream()
                .collect(Collectors.toMap(Order::getOrderId, v -> v, (oldV, newV) -> newV));
        // 根据订单号分组，value设置为分组后的数量，如果数量大于1，说明不止一笔支付成功的，需要保存异常订单
        Map<String, Integer> payRecordCountMap = payRecordList.stream()
                .filter(op -> PayStatusEnum.PAY_SUCCESS.getCode().equals(op.getPayStatus()))
                .collect(Collectors.groupingBy(OrderPayRecord::getOrderId, Collectors.summingInt(v -> 1)));
        // 获取订单已经存在的异常订单数据，如果相同支付号的异常订单已经存在，则不再保存，主要是考虑重试的场景
        List<ExceptionOrder> existList = exceptionOrderRepository.getByOrderIdListForceMaster(orderIdList);
        // 已存在的异常订单，过滤出支付号相同的异常订单的数量
        Map<String, List<ExceptionOrder>> existMap = existList.stream()
                .collect(Collectors.groupingBy(ExceptionOrder::getOrderId));
        List<ExceptionOrder> exceptionOrderList = orderIdList.stream()
                .map(orderId -> {
                    ExceptionOrder exceptionOrder = null;
                    // 相同订单号的异常订单数量
                    List<ExceptionOrder> orderExistList = existMap.get(orderId);
                    // 已经存在的异常订单数量
                    int existCnt = 0;
                    boolean payNoExist = false;
                    if (CollUtil.isNotEmpty(orderExistList)) {
                        existCnt = orderExistList.size();
                        payNoExist = orderExistList.stream().anyMatch(eo -> StrUtil.equals(payNo, eo.getPayNo()));
                    }
                    // 对于重复支付，支付成功的记录数是否大于已经存在的异常订单数量+1，且对应支付单号还没保存，比如支付成功两笔，则记录一笔异常订单
                    boolean recordMoreThanExist = payRecordCountMap.containsKey(orderId) && payRecordCountMap.get(orderId) > existCnt + 1 && !payNoExist;
                    // 对于订单关闭，支付成功的记录数是否大于已经存在的异常订单数量，且对应支付单号还没保存，比如支付成功一笔，则记录一笔异常订单
                    boolean recordThanOne = payRecordCountMap.containsKey(orderId) && payRecordCountMap.get(orderId) > existCnt && !payNoExist;
                    if (recordMoreThanExist) {
                        exceptionOrder = new ExceptionOrder();
                        exceptionOrder.setErrorType(ExceptionOrderTypeEnum.REPEAT_PAY.getCode());
                    } else if (orderMap.containsKey(orderId) && recordThanOne) {
                        Order order = orderMap.get(orderId);
                        if (OrderStatusEnum.CLOSED.getCode().equals(order.getOrderStatus())) {
                            exceptionOrder = new ExceptionOrder();
                            exceptionOrder.setErrorType(ExceptionOrderTypeEnum.TIMEOUT_CLOSE.getCode());
                        }
                    }
                    OrderPayRecord orderPayRecord = payRecordMap.get(orderId);
                    if (exceptionOrder != null) {
                        exceptionOrder.setOrderId(orderId);
                        exceptionOrder.setPayNo(orderPayRecord.getPayNo());
                        exceptionOrder.setPayAmount(orderPayRecord.getOrderAmount());
                        exceptionOrder.setPayTime(orderPayRecord.getPayTime());
                        exceptionOrder.setRefundStatus(ExceptionOrderRefundStatusEnum.WAIT_REFUND.getCode());
                        exceptionOrder.setCreateTime(new Date());
                        exceptionOrder.setUpdateTime(new Date());
                        exceptionOrder.setBatchNo(orderPayRecord.getBatchNo());
                    }
                    return exceptionOrder;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(exceptionOrderList)) {
            exceptionOrderRepository.saveBatch(exceptionOrderList);
        }
    }

    /**
     * 判断订单的下一个状态
     *
     * @param order 订单
     * @return 订单下一个状态
     */
    private Integer judgeNextOrderStatus(Order order) {
        Integer orderStatus = OrderStatusEnum.UNDER_SEND.getCode();
        log.info("【支付回调】订单{}，进入下一状态{}", order.getOrderId(), OrderStatusEnum.getDesc(orderStatus));
        return orderStatus;
    }
}
