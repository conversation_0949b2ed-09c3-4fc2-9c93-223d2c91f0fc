package com.sankuai.shangou.seashop.order.core.task;

import java.util.Date;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.apache.commons.lang3.ObjectUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.starter.util.json.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.remote.SiteSettingRemoteService;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import com.sankuai.shangou.seashop.order.core.service.OrderCommentService;
import com.sankuai.shangou.seashop.order.core.service.ProductCommentService;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderItemInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryPlatformOrderBo;
import com.sankuai.shangou.seashop.order.core.task.param.ProductCommentEsParam;
import com.sankuai.shangou.seashop.order.dao.core.domain.ProductComment;
import com.sankuai.shangou.seashop.order.dao.core.repository.ProductCommentRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/02 15:12
 */
@Slf4j
@Component
public class CommentTask {

    @Resource
    private EsOrderService esOrderService;
    @Resource
    private OrderCommentService orderCommentService;
    @Resource
    private SiteSettingRemoteService siteSettingRemoteService;
    @Resource
    private ProductCommentRepository productCommentRepository;
    @Resource
    private ProductCommentService productCommentService;

    private static final Integer DEFAULT_PAGE_NO = 1;
    private static final Integer DEFAULT_PAGE_SIZE = 100;
    private static final Integer DEFAULT_MARK = 5;
    private static final String DEFAULT_REVIEW_CONTENT = "好评";


    /**
     * 自动评价
     * corn 表达式 0 15,45 * * * ?
     */
//    @Crane("OrderAutoCommentTask")
    @XxlJob("orderAutoCommentTask")
    @XxlRegister(cron = "0 15,45 * * * ?",
            author = "snow",
            jobDesc = "自动评价")
    public void autoComment() {
        Long outTime = siteSettingRemoteService.getCommentOutTime();
        // Long outTime = 1L;

        // 2. 去es中查询出待评价的订单
        boolean isEnd = Boolean.FALSE;
        Integer pageNo = DEFAULT_PAGE_NO;
        while (!isEnd) {
            QueryPlatformOrderBo request = new QueryPlatformOrderBo();
            request.setOrderStatus(OrderStatusEnum.FINISHED.getCode());
            request.setHasComment(Boolean.FALSE);
            request.setPageNo(pageNo);
            request.setPageSize(DEFAULT_PAGE_SIZE);

            // 计算完成结束时间
            request.setFinishEndTime(DateUtil.offsetDay(new Date(), outTime.intValue() * -1));

            BasePageResp<OrderInfoBo> pageResult = esOrderService.searchForPlatform(request);
            List<OrderInfoBo> orderList = pageResult.getData();

            // 3. 调用评价服务进行评价
            orderList.forEach(order -> {
                saveOrderComment(order);
            });

            if (CollectionUtils.isEmpty(orderList) || orderList.size() < DEFAULT_PAGE_SIZE) {
                isEnd = Boolean.TRUE;
            }
        }
    }

    private void saveOrderComment(OrderInfoBo order) {
        try {
            log.info("自动订单评价, orderId:{}", order.getOrderId());
            OrderCommentBo orderCommentBo = orderCommentBuild(order);
            orderCommentService.saveOrderComment(orderCommentBo);
            log.info("自动订单评价成功, orderId:{}", order.getOrderId());
        }
        catch (Exception e) {
            log.error("自动订单评价失败,orderId:{}", order.getOrderId(), e);
        }
    }

    private OrderCommentBo orderCommentBuild(OrderInfoBo order) {
        OrderCommentBo comment = new OrderCommentBo();
        comment.setOrderId(order.getOrderId());
        comment.setUserId(order.getUserId());
        comment.setPackMark(DEFAULT_MARK);
        comment.setDeliveryMark(DEFAULT_MARK);
        comment.setServiceMark(DEFAULT_MARK);

        List<OrderItemInfoBo> orderItemList = order.getItemList();
        List<ProductCommentBo> productCommentList = orderItemList.stream().map(orderItem -> {
            ProductCommentBo productComment = new ProductCommentBo();
            productComment.setSubOrderId(orderItem.getOrderItemId());
            productComment.setReviewMark(DEFAULT_MARK);
            productComment.setReviewContent(DEFAULT_REVIEW_CONTENT);
            return productComment;
        }).collect(Collectors.toList());
        comment.setProductCommentList(productCommentList);
        return comment;
    }

    @XxlJob("refreshProductCommentEs")
    @XxlRegister(cron = "0 0 3 * * ?",
            author = "snow",
            jobDesc = "刷新商品评论")
    public void refreshProductCommentEs(String paramStr) {
        String jobParam = XxlJobHelper.getJobParam();
        ProductCommentEsParam param = JsonUtil.parseObject(jobParam,ProductCommentEsParam.class);
        Long lastId = ObjectUtils.defaultIfNull(param.getLastId(), 0L);
        Integer limit = CommonConst.QUERY_LIMIT;

        // 因为只用一次 直接使用这种方式创建了线程池
        ExecutorService executorService = Executors.newFixedThreadPool(10);

        while (lastId != null) {
            log.info("refreshProductCommentEs, lastId:{}, limit:{}", lastId, limit);
            List<ProductComment> productCommentList = productCommentRepository
                    .list(new LambdaQueryWrapper<ProductComment>().gt(ProductComment::getId, lastId).last("limit " + limit));

            CountDownLatch latch = new CountDownLatch(productCommentList.size());
            productCommentList.forEach(productComment -> {
                executorService.execute(() -> {
                    productCommentService.buildEsProductComment(productComment.getProductCommentId());
                    latch.countDown();
                });
            });

            lastId = productCommentList.size() < limit ? null : productCommentList.get(limit - 1).getId();
            try {
                latch.await();
            } catch (Exception e) {
                log.error("refreshProductCommentEs error", e);
            }
        }

        executorService.shutdown();
        log.info("refreshProductCommentEs end");
    }
}
