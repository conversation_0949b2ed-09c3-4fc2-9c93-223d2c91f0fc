package com.sankuai.shangou.seashop.order.core.statemachine.action;

import cn.hutool.core.date.DateUtil;
import com.alibaba.cola.statemachine.Action;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.common.utils.SettingUtil;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderBizAssist;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderEvent;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 延迟收货事件发生时的动作
 * <p>待收货才能延迟收货</p>
 * <P>虽然延迟收货不涉及状态变更，但可以认为是一种内部状态流转，且能公用状态机的一些逻辑，所以放到状态机实现</P>
 * <AUTHOR>
 */
@Service
@Slf4j
public class DelayReceiveAction extends BaseAction implements Action<OrderStatusEnum, OrderEvent, OrderContext> {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderBizAssist orderBizAssist;
    @Resource
    private SettingRemoteService settingRemoteService;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void executeStatusChange(OrderContext context, Order dbOrder) {
        Long userId = context.getUserId();
        String orderId = context.getOrderId();
        log.info("【订单状态机】商家延长收货,userId={},orderId={}", userId, orderId);
        TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
        log.info("【订单状态机】获取交易设置,setting={}", JsonUtil.toJsonString(setting));
        // 获取交易设置中的延迟收货时间，涉及两个时间，一个是收货后多长时间自动确认收货，一个是自动完成前多长时间可以延迟收货
        int autoConfirmReceiveDays = SettingUtil.getIntValueOrDefault("收货后自动确认收货天数", setting.getNoReceivingTimeout(), CommonConst.DEFAULT_AUTO_FINISH_DAYS);
        int allowApplyDelayReceiveDays = SettingUtil.getIntValueOrDefault("自动完成前允许延迟收货天数", setting.getBeforeReceivingDays(), CommonConst.DEFAULT_BEFORE_RECEIVING_DAYS);
        // 根据订单的发货时间计算自动确认收货时间
        Date autoConfirmReceiveDate = DateUtil.offsetDay(dbOrder.getShippingDate(), autoConfirmReceiveDays);
        // 自动完成前允许开始申请延长收货的时间
        Date allowApplyDelayReceiveDate = DateUtil.offsetDay(autoConfirmReceiveDate, -allowApplyDelayReceiveDays);
        Date now = new Date();
        log.info("订单延长收货, 发货日期={}, 自动确认收货日期={}, 允许延长收货开始日期={}", dbOrder.getShippingDate(), autoConfirmReceiveDate, allowApplyDelayReceiveDate);
        if (now.compareTo(allowApplyDelayReceiveDate) < 0) {
            throw new BusinessException("该订单还未到允许延长收货时间");
        }
        if (autoConfirmReceiveDate.compareTo(now) < 0) {
            throw new BusinessException("该订单已经超过延迟收货时间");
        }
        // 保存延迟收货时间
        // 获取交易设置中的延长收货时间
        int delayReceiveDays = SettingUtil.getIntValueOrDefault("延长收货天数", setting.getNoReceivingDelayDays(), CommonConst.DEFAULT_DELAY_DAYS);
        int cnt = orderRepository.updateReceiveDelay(dbOrder.getOrderId(), delayReceiveDays);
        if (cnt != 1) {
            throw new BusinessException("订单状态发生改变，请重新刷页面操作");
        }
        // 保存订单操作日志
        orderBizAssist.addOrderOperationLog(dbOrder.getOrderId(), context.getUserName(),
                context.getUserName() + "延迟收货" + delayReceiveDays + "天");
    }

    @Override
    protected void validateBizData(OrderContext context, Order dbOrder) {
        if (!OrderStatusEnum.UNDER_RECEIVE.getCode().equals(dbOrder.getOrderStatus())) {
            throw new BusinessException("订单状态发生改变，请重新刷页面操作");
        }
        if (dbOrder.getReceiveDelay() != null && dbOrder.getReceiveDelay() > 0) {
            throw new BusinessException("该订单已经操作过延迟收货");
        }
    }

    @Override
    protected void dealExecuteResult(OrderContext context, ActionResult executeResult) {
        if (!executeResult.isSuccess()) {
            throw new BusinessException("订单状态发生改变，请重新刷页面操作");
        }
    }

}
