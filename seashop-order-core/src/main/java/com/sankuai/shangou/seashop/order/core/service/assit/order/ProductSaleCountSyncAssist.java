package com.sankuai.shangou.seashop.order.core.service.assit.order;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.order.common.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.product.thrift.core.request.product.dto.AddProductSaleCountDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ProductSaleCountSyncAssist {

    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private ProductRemoteService productRemoteService;

    public void addProductSaleCount(String orderId) {
        List<OrderItem> itemList = orderItemRepository.getByOrderId(orderId);
        if (CollUtil.isEmpty(itemList)) {
            log.warn("【订单状态变更-创建】创建订单, orderId: {} 订单项为空", orderId);
            return;
        }
        try {
            List<AddProductSaleCountDto> productList = itemList.stream().map(item -> {
                AddProductSaleCountDto dto = new AddProductSaleCountDto();
                dto.setProductId(item.getProductId());
                dto.setAddSaleCount(item.getQuantity().intValue());
                return dto;
            }).collect(Collectors.toList());
            productRemoteService.addProductSaleCount(productList);
        } catch (Exception e) {
            // 更新商品销量失败不影响主流程
            log.error("【订单状态变更-创建】创建订单, orderId: {} 更新商品销量失败", orderId, e);
        }
    }

}
