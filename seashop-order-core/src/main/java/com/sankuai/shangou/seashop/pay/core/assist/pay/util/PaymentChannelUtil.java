package com.sankuai.shangou.seashop.pay.core.assist.pay.util;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayPlatformEnum;
import com.sankuai.shangou.seashop.pay.dao.core.model.PayOpenStateModel;
import com.sankuai.shangou.seashop.pay.dao.core.repository.ChannelConfigRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/09/03 10:30
 */
@Component
@Slf4j
public class PaymentChannelUtil {

    @Resource
    private ChannelConfigRepository channelConfigRepository;

    /**
     * 根据支付类型自动匹配支付渠道
     *
     * @param paymentType 支付类型
     * @return 支付渠道
     */
    public PaymentChannelEnum autoMatchPaymentChannel(Integer paymentType) {
        log.info("【支付】自动匹配支付渠道, paymentType={}", paymentType);
        PaymentTypeEnum paymentTypeEnum = PaymentTypeEnum.getByType(paymentType);
        AssertUtil.throwIfNull(paymentTypeEnum, "支付类型不存在");

        PayOpenStateModel payOpenState = channelConfigRepository.getPayOpenState();
        log.info("【支付】支付渠道开关状态, payOpenState={}", JsonUtil.toJsonString(payOpenState));

        switch (paymentTypeEnum) {
            // 支付宝支付
            case ALIPAY_SCAN:
            case ALIPAY_H5:
                if (payOpenState.getOpenAliPay()) {
                    return PaymentChannelEnum.ALIPAY;
                }
                break;
            case WECHAT_APPLET:
                if (payOpenState.getOpenWxPay()) {
                    return PaymentChannelEnum.WXPAY;
                }
                break;
            case WECHAT_H5:
                if (payOpenState.getOpenWxPay()) {
                    return PaymentChannelEnum.WXPAY;
                }
                break;
            case WECHAT_NATIVE:
                if (payOpenState.getOpenWxPay()) {
                    return PaymentChannelEnum.WXPAY;
                }
                break;
            case WECHAT_JS:
                if (payOpenState.getOpenWxPay()) {
                    return PaymentChannelEnum.WXPAY;
                }
                break;
            default:
                break;
        }

        if (payOpenState.getOpenAdaPay()) {
            return PaymentChannelEnum.ADAPAY;
        }

        throw new BusinessException("支付渠道未开启");
    }

    public PaymentChannelEnum checkPaymentChannel(Integer paymentChannel) {
        PaymentChannelEnum paymentChannelEnum = PaymentChannelEnum.getByCode(paymentChannel);
        AssertUtil.throwIfNull(paymentChannelEnum, "支付渠道不存在");

        PayOpenStateModel payOpenState = channelConfigRepository.getPayOpenState();
        log.info("【支付】支付渠道开关状态, payOpenState={}", JsonUtil.toJsonString(payOpenState));
        switch (paymentChannelEnum) {
            case ALIPAY:
                AssertUtil.throwIfTrue(!payOpenState.getOpenAliPay(), "支付宝支付未开启");
                break;
            case WXPAY:
                AssertUtil.throwIfTrue(!payOpenState.getOpenWxPay(), "微信支付未开启");
                break;
            case ADAPAY:
                AssertUtil.throwIfTrue(!payOpenState.getOpenAdaPay(), "汇付天下支付未开启");
                break;
            default:
                break;
        }

        return paymentChannelEnum;
    }


    public List<PayMethodEnum> getEnablePayMethods(PayPlatformEnum platform) {
        List<PayMethodEnum> supportMethods = PayMethodEnum.getSupportMethods(platform);
        if (CollUtil.isEmpty(supportMethods)) {
            return supportMethods;
        }

        PayOpenStateModel payOpenState = channelConfigRepository.getPayOpenState();
        // 如果开启了汇付 则所有支付方式都可以使用
        if (payOpenState.getOpenAdaPay()) {
            supportMethods.remove(PayMethodEnum.WECHAT_NATIVE);
            return supportMethods;
        }
        // 如果关闭了汇付 不能使用网银支付
        else {
            supportMethods.remove(PayMethodEnum.COMPANY_BANK);
            supportMethods.remove(PayMethodEnum.PERSON_BANK);
        }

        // 如果关掉了支付宝 则把支付宝相关的支付方式过滤掉
        if (!payOpenState.getOpenAliPay()) {
            supportMethods.remove(PayMethodEnum.ALIPAY_SCAN);
            supportMethods.remove(PayMethodEnum.ALIPAY_H5);
        }

        // 如果关掉了微信 则把微信相关的支付方式过滤掉
        if (!payOpenState.getOpenWxPay()) {
            supportMethods.remove(PayMethodEnum.WECHAT_APPLET);
            supportMethods.remove(PayMethodEnum.WECHAT_H5);
            supportMethods.remove(PayMethodEnum.WECHAT_NATIVE);
        }
        return supportMethods;
    }
}
