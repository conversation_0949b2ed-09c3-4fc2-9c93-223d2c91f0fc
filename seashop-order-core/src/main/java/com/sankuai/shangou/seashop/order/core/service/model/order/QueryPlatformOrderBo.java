package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 商家查询自己的订单列表查询条件
 * <p>分业务端是为了防止底层业务逻辑过于通用导致不可预知的水平越权</p>
 * <AUTHOR>
 */
@Getter
@Setter
public class QueryPlatformOrderBo extends QueryOrderCommonBo {
    /**
     * 支付单号，本系统生成的支付单号
     */
    private String payId;
    /**
     * 交易单号，汇付返回的支付渠道号
     */
    private String tradeNo;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称，从店铺ES中查询
     */
    private String shopName;

    /**
     * 订单ID集合
     */
    private List<String> orderIdList;

}
