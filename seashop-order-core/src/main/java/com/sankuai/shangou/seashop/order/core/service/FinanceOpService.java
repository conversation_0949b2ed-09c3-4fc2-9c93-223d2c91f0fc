package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.order.dao.core.domain.ExceptionOrder;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2024/1/10/010
 * @description:
 */
public interface FinanceOpService {

    /**
     * 正常退款写入财务表
     * （退款成功的）
     *
     * @param order
     * @param orderPayRecord
     * @param orderRefund
     * @param refundMoney
     * @param serviceAmount 汇付手续费：(待结算表)原待结算金额-退款成功之后的待结算金额
     * @param settlementAmount 供应商结算金额：(待结算表)原待结算金额-退款成功之后的待结算金额
     */
    void writeFinanceOrderRefund(Order order, OrderPayRecord orderPayRecord, OrderRefund orderRefund,
                                 BigDecimal refundMoney,BigDecimal serviceAmount,BigDecimal settlementAmount);

    /**
     * 异常订单退款写入财务表
     * （退款成功的）
     *
     * @param exceptionOrder
     */
    void writeFinanceExceptionOrderRefund(ExceptionOrder exceptionOrder);
}
