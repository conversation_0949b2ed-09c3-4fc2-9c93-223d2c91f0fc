package com.sankuai.shangou.seashop.order.core.mq.model.refund;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class OrderRefundTableMessage {

    /**
     * 主键ID
     */
    @JsonProperty("id")
    private Long id;

    /**
     * 订单id
     */
    @JsonProperty("order_id")
    private String orderId;

    /**
     * 订单详情id
     */
    @JsonProperty("order_item_id")
    private Long orderItemId;

    /**
     * 店铺id
     */
    @JsonProperty("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @JsonProperty("shop_name")
    private String shopName;

    /**
     * 商家id
     */
    @JsonProperty("user_id")
    private Long userId;

    /**
     * 申请人
     */
    @JsonProperty("applicant")
    private String applicant;

    /**
     * 联系人
     */
    @JsonProperty("contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @JsonProperty("contact_cell_phone")
    private String contactCellPhone;

    /**
     * 申请时间
     */
    @JsonProperty("apply_date")
    private Date applyDate;

    /**
     * 金额
     */
    @JsonProperty("amount")
    private BigDecimal amount;

    /**
     * 退款原因
     */
    @JsonProperty("reason")
    private String reason;

    /**
     * 退款详情
     */
    @JsonProperty("reason_detail")
    private String reasonDetail;

    /**
     * 供应商审核状态(1:待供应商审核,2:待买家寄货,3:待供应商收货,4:供应商拒绝,5:供应商通过审核)
     */
    @JsonProperty("seller_audit_status")
    private Integer sellerAuditStatus;

    /**
     * 供应商审核时间
     */
    @JsonProperty("seller_audit_date")
    private Date sellerAuditDate;

    /**
     * 供应商注释
     */
    @JsonProperty("seller_remark")
    private String sellerRemark;

    /**
     * 平台审核状态(6:待平台确认,7:退款成功,8:平台驳回)
     */
    @JsonProperty("manager_confirm_status")
    private Integer managerConfirmStatus;

    /**
     * 平台审核时间
     */
    @JsonProperty("manager_confirm_date")
    private Date managerConfirmDate;

    /**
     * 平台注释
     */
    @JsonProperty("manager_remark")
    private String managerRemark;

    /**
     * 快递公司
     */
    @JsonProperty("express_company_name")
    private String expressCompanyName;

    /**
     * 快递单号
     */
    @JsonProperty("ship_order_number")
    private String shipOrderNumber;

    /**
     * 退款方式(1:订单退款,2:货品退款,3:退货退款)
     */
    @JsonProperty("refund_mode")
    private Integer refundMode;

    /**
     * 退款支付状态
     */
    @JsonProperty("refund_pay_status")
    private Integer refundPayStatus;

    /**
     * 退款支付类型
     */
    @JsonProperty("refund_pay_type")
    private Integer refundPayType;

    /**
     * 买家发货时间
     */
    @JsonProperty("buyer_deliver_date")
    private Date buyerDeliverDate;

    /**
     * 卖家确认到货时间
     */
    @JsonProperty("seller_confirm_arrival_date")
    private Date sellerConfirmArrivalDate;

    /**
     * 退款批次号
     */
    @JsonProperty("refund_batch_no")
    private String refundBatchNo;

    /**
     * 退款异步提交时间
     */
    @JsonProperty("refund_post_time")
    private Date refundPostTime;

    /**
     * 退货数量
     */
    @JsonProperty("return_quantity")
    private Long returnQuantity;

    /**
     * 平台佣金退还
     */
    @JsonProperty("return_plat_commission")
    private BigDecimal returnPlatCommission;

    /**
     * 申请次数
     */
    @JsonProperty("apply_number")
    private Integer applyNumber;

    /**
     * 凭证图片1
     */
    @JsonProperty("cert_pic1")
    private String certPic1;

    /**
     * 凭证图片2
     */
    @JsonProperty("cert_pic2")
    private String certPic2;

    /**
     * 凭证图片3
     */
    @JsonProperty("cert_pic3")
    private String certPic3;

    /**
     * 退运费
     */
    @JsonProperty("return_freight")
    private BigDecimal returnFreight;

    /**
     * 最后修改时间
     */
    @JsonProperty("last_modify_time")
    private Date lastModifyTime;

    /**
     * 取消申请时间
     */
    @JsonProperty("cancel_date")
    private Date cancelDate;

    /**
     * 创建时间
     */
    @JsonProperty("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @JsonProperty("update_time")
    private Date updateTime;

}
