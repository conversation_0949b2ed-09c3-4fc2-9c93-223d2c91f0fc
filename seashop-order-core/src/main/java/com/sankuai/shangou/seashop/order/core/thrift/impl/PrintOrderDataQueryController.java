package com.sankuai.shangou.seashop.order.core.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.core.service.PrintOrderDataQueryService;
import com.sankuai.shangou.seashop.order.thrift.core.PrintOrderDataQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.PrintOrderDataReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderDataResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderItemDataResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 16:10
 */
@Slf4j
@RestController
@RequestMapping("/printOrderData")
public class PrintOrderDataQueryController implements PrintOrderDataQueryFeign {

    @Resource
    private PrintOrderDataQueryService printOrderDataQueryService;

    @PostMapping(value = "/batchQueryOrderByOrderIds", consumes = "application/json")
    @Override
    public ResultDto<List<PrintOrderDataResp>> batchQueryOrderByOrderIds(@RequestBody PrintOrderDataReq queryReq) throws TException {
        queryReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("batchQueryOrderByOrderIds", queryReq, req -> printOrderDataQueryService.batchQueryOrderByOrderIds(req));
    }

    @PostMapping(value = "/batchQueryOrderItemByOrderIds", consumes = "application/json")
    @Override
    public ResultDto<List<PrintOrderItemDataResp>> batchQueryOrderItemByOrderIds(@RequestBody PrintOrderDataReq queryReq) throws TException {
        queryReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("batchQueryOrderItemByOrderIds", queryReq, req -> printOrderDataQueryService.batchQueryOrderItemByOrderIds(req));
    }
}
