package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class CancelOrderBo extends BaseParamReq {

    /**
     * 用户ID
     */
    private Long userId;
    private Long shopId;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 取消原因
     */
    private String cancelReason;

}
