package com.sankuai.shangou.seashop.order.core.service.model.refund;

import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.order.thrift.core.enums.SearchTimeTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class QueryErpRefundBo {
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 售后类型
     */
    private List<Integer> refundModes;
    /**
     * 时间类型
     */
    private SearchTimeTypeEnum timeType;
    /**
     * 开始时间
     */
    private Date startTime;
    /**
     * 结束时间
     */
    private Date endTime;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 订单id
     */
    private String orderId;
    /**
     * 综合状态集合
     */
    private List<RefundStatusEnum> statusList;

}
