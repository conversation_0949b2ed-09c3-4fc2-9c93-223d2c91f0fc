package com.sankuai.shangou.seashop.order.core.thrift.impl;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.OrderPayService;
import com.sankuai.shangou.seashop.order.core.service.model.pay.PcOrderPayInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.pay.QueryOrderPayBo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderPayQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.PayMethodDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.PcPrePayOrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPayChannelReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayMethodResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/orderPayQuery")
public class OrderPayQueryController implements OrderPayQueryFeign {

    @Resource
    private OrderPayService orderPayService;

    @PostMapping(value = "/queryPcPayChannel", consumes = "application/json")
    @Override
    public ResultDto<OrderPayMethodResp> queryPcPayChannel(@RequestBody QueryPayChannelReq queryReq) {
        log.info("【支付】PC端获取支付方式, 请求参数={}", queryReq);
        return ThriftResponseHelper.responseInvoke("queryPcPayChannel", queryReq, func -> {
            QueryOrderPayBo queryOrderPayBo = JsonUtil.copy(queryReq, QueryOrderPayBo.class);
            // 业务逻辑处理
            PcOrderPayInfoBo detail = orderPayService.queryPcOrderPayInfo(queryOrderPayBo);
            List<PayMethodDto> methodList = detail.getPayMethodList().stream()
                .map(method -> {
                    PayMethodDto dto = new PayMethodDto();
                    dto.setType(method.getCode());
                    dto.setDesc(method.getDesc());
                    return dto;
                }).collect(Collectors.toList());
            OrderPayMethodResp resp = new OrderPayMethodResp();
            resp.setPayMethodList(methodList);
            resp.setOrderInfo(JsonUtil.copy(detail.getOrderInfo(), PcPrePayOrderInfoDto.class));
            return resp;
        });
    }

    @PostMapping(value = "/confirmPayResult", consumes = "application/json")
    @Override
    public ResultDto<Boolean> confirmPayResult(@RequestBody QueryPayChannelReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【支付】确认支付结果", queryReq, func -> {
            return orderPayService.confirmPayResult(queryReq);
        });
    }
}
