package com.sankuai.shangou.seashop.pay.core.service;

import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.GetOrderRequest;
import com.sankuai.shangou.seashop.pay.thrift.core.request.UploadShippingRequest;
import com.sankuai.shangou.seashop.pay.thrift.core.response.DeliveryListResponse;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description:
 */
public interface WxShippingBiz {

    Boolean isTradeManaged(Integer paymentType);

    Boolean uploadShipping(UploadShippingRequest request);

    Boolean getOrder(GetOrderRequest request);

    DeliveryListResponse getDeliveryList(Integer paymentType);
}
