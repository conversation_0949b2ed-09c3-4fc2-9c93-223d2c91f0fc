package com.sankuai.shangou.seashop.order.core.service.assit.refund;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.StockRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.product.DecreaseStockBo;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.RefundEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderTypeEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 供应商确认收货处理
 * <AUTHOR>
 */
@Service
@Slf4j
public class SellerConfirmReceiveRefundMessageHandler implements RefundMessageHandler{

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private StockRemoteService stockRemoteService;

    @Override
    public RefundEventEnum getEvent() {
        return RefundEventEnum.SELLER_CONFIRM_RECEIVE;
    }

    @Override
    public void handle(OrderRefundMessage message) {
        log.info("【售后消息处理】供应商确认收货处理，message: {}", JsonUtil.toJsonString(message));
        OrderRefund orderRefund = orderRefundRepository.getById(message.getRefundId());
        log.info("【售后消息处理】供应商确认收货处理，orderRefund: {}", JsonUtil.toJsonString(orderRefund));
        if (orderRefund == null) {
            log.warn("【售后消息处理】申请售后消息处理失败，订单售后不存在，message: {}", message);
            return;
        }
        // 退库存
        Order order = orderRepository.getByOrderIdForceMaster(orderRefund.getOrderId());
        log.info("【售后消息处理】供应商确认收货处理，order: {}", JsonUtil.toJsonString(order));
        // 限时购的订单明细只有一条记录
        if (OrderTypeEnum.FLASH_SALE.getCode().equals(order.getOrderType())){
            log.info("限时购订单，回滚库存");
            OrderItem orderItem = orderItemRepository.getByOrderId(order.getOrderId()).get(0);
            promotionRemoteService.rollbackFlashSaleStock(order.getOrderId(), orderItem.getFlashSaleId(), orderItem.getSkuId(),
                    orderRefund.getId(), orderRefund.getReturnQuantity().intValue());
        }
        // 退库存，这里是退发货后的库存
        // 整单退
        if (Boolean.TRUE.equals(orderRefund.getHasAllReturn()) && orderRefund.getReturnQuantity() > 0){
            log.info("整单退，回滚库存");
            List<OrderItem> orderItemList = orderItemRepository.getByOrderId(order.getOrderId());
            List<DecreaseStockBo> returnList = orderItemList.stream()
                    .map(item -> {
                        DecreaseStockBo bo = new DecreaseStockBo();
                        bo.setProductId(item.getProductId());
                        bo.setSkuId(item.getSkuId());
                        bo.setQuantity(item.getQuantity());
                        bo.setShopId(item.getShopId());
                        bo.setOrderId(item.getOrderId());
                        return bo;
                    })
                    .collect(Collectors.toList());
            stockRemoteService.rollbackStockBySku(StockUpdateTypeEnum.RETURN_STOCK, String.valueOf(orderRefund.getId()), returnList);
        }
        // 明细退
        else if (orderRefund.getOrderItemId() > 0 && orderRefund.getReturnQuantity() > 0){
            log.info("单品退，回滚库存");
            OrderItem orderItem = orderItemRepository.getById(orderRefund.getOrderItemId());
            DecreaseStockBo bo = new DecreaseStockBo();
            bo.setProductId(orderItem.getProductId());
            bo.setSkuId(orderItem.getSkuId());
            bo.setQuantity(orderRefund.getReturnQuantity());
            bo.setShopId(orderItem.getShopId());
            bo.setOrderId(orderItem.getOrderId());
            stockRemoteService.rollbackStockBySku(StockUpdateTypeEnum.RETURN_STOCK, String.valueOf(orderRefund.getId()), Collections.singletonList(bo));
        }
    }
}
