package com.sankuai.shangou.seashop.order.core.statemachine;

import com.sankuai.shangou.seashop.order.dao.core.domain.Order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.SuperBuilder;

/**
 * <AUTHOR>
 */
@SuperBuilder
@Getter
@Setter
@ToString
@NoArgsConstructor
@AllArgsConstructor
public class OrderContext {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 当前用户id
     */
    private Long userId;
    /**
     * 当前用户名
     */
    private String userName;
    /**
     * 取消原因
     */
    private String cancelReason;


    /**
     * 上下文中的order是逻辑处理过程中的实际订单对象，从数据库获取并逻辑中更新
     */
    private Order order;

}
