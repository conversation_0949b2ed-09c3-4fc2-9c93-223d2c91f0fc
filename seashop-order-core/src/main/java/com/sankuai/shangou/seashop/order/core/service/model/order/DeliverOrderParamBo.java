package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import lombok.Getter;
import lombok.Setter;

/**
 * 订单发货请求参数业务对象
 * <AUTHOR>
 */
@Getter
@Setter
public class DeliverOrderParamBo extends BaseParamReq {

    /**
     * 当前登录操作的店铺信息
     */
    private ShopBo shop;
    /**
     * 当前登录操作的用户信息
     */
    private UserBo user;
    /**
     * 是否需要物流
     */
    private Boolean needExpress;
    /**
     * 订单发货信息
     */
    private OrderDeliveryBo delivery;

}
