package com.sankuai.shangou.seashop.order.core.service.model.refund;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundSellerQueryTabEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 供应商查询售后记录请求参数
 * <AUTHOR>
 */
@Getter
@Setter
public class SellerQueryRefundBo extends QueryRefundBaseBo {

    /**
     * 用户信息
     */
    private UserBo user;
    /**
     * 店铺信息
     */
    private ShopBo shop;
    /**
     * 订单号
     */
    private String orderId;

    /**
     * 订单号集合
     */
    private List<String> orderIds;
    /**
     * 买家账号。等价于EP账号
     */
    private String userName;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品名称或商品ID
     */
    private String productIdOrSkuId;
    /**
     * 查询类型。1：退款-全部；2：退款-待处理；3：退货-全部；4：退货-待处理；5：买家取消
     */
    private RefundSellerQueryTabEnum tab;

    @FieldDoc(description = "退款状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；5：待平台确认；6：退款成功；7：平台驳回；8：退款中；9：买家取消")
    private Integer refundStatus;
}
