package com.sankuai.shangou.seashop.order.core.statemachine.action;

import com.alibaba.cola.statemachine.Action;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderEvent;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import org.springframework.stereotype.Service;

/**
 * 订单发起支付执行器
 * <AUTHOR>
 */
@Service
public class InitiatePayAction extends BaseAction implements Action<OrderStatusEnum, OrderEvent, OrderContext> {

    @Override
    protected void executeStatusChange(OrderContext context, Order dbOrder) {

    }

    @Override
    protected void validateBizData(OrderContext context, Order dbOrder) {

    }

    @Override
    protected void dealExecuteResult(OrderContext context, ActionResult executeResult) {

    }
}
