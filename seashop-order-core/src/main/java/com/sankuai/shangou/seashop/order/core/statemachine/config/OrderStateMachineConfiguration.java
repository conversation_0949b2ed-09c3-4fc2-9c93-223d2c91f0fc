package com.sankuai.shangou.seashop.order.core.statemachine.config;

import com.alibaba.cola.statemachine.StateMachine;
import com.alibaba.cola.statemachine.builder.StateMachineBuilder;
import com.alibaba.cola.statemachine.builder.StateMachineBuilderFactory;
import com.sankuai.shangou.seashop.order.common.enums.StateMachineIdEnum;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderEvent;
import com.sankuai.shangou.seashop.order.core.statemachine.action.*;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Configuration
@Slf4j
public class OrderStateMachineConfiguration {

    @Resource
    private InitiatePayAction initiatePayAction;
    @Resource
    private PayNotifyAction payNotifyAction;
    @Resource
    private CancelPayAction cancelPayAction;
    @Resource
    private CancelOrderAction cancelOrderAction;
    @Resource
    private DeliveryAction deliveryAction;
    @Resource
    private ConfirmReceiveAction confirmReceiveAction;
    @Resource
    private DelayReceiveAction delayReceiveAction;

    /**
     * 构建状态机实例
     */
    @Bean
    public StateMachine<OrderStatusEnum, OrderEvent, OrderContext> orderStateMachine() {
        // 对状态机开始构建，并在StateMachineFactory中注册
        StateMachineBuilder<OrderStatusEnum, OrderEvent, OrderContext> stateMachineBuilder = StateMachineBuilderFactory.create();
        /*
         * 发起支付: 待付款 -> 支付中
         * externalTransition : 不同状态的流转
         */
        stateMachineBuilder.externalTransition()
                .from(OrderStatusEnum.UNDER_PAY)
                .to(OrderStatusEnum.PAYING)
                .on(OrderEvent.INITIATE_PAY)
                //.when(checkIfPass())
                .perform(initiatePayAction);
        /*
         * 支付回调成功: 支付中 -> 待发货
         * externalTransition : 不同状态的流转
         */
        stateMachineBuilder.externalTransition()
                .from(OrderStatusEnum.PAYING)
                .to(OrderStatusEnum.UNDER_SEND)
                .on(OrderEvent.PAY_NOTIFY)
                //.when(checkIfNotPass())
                .perform(payNotifyAction);
        /*
         * 卖家发货: 待发货 -> 待收货
         * externalTransition : 不同状态的流转
         */
        stateMachineBuilder.externalTransition()
                .from(OrderStatusEnum.UNDER_SEND)
                .to(OrderStatusEnum.UNDER_RECEIVE)
                .on(OrderEvent.DELIVERY)
                //.when(checkIfPass())
                .perform(deliveryAction);
        /*
         * 买家延迟收货: 待收货 -> 待收货
         */
        stateMachineBuilder.internalTransition()
                .within(OrderStatusEnum.UNDER_RECEIVE)
                .on(OrderEvent.DELAY_RECEIVE)
                .perform(delayReceiveAction);
        /*
         * 买家收货: 待收货 -> 已完成
         * externalTransition : 不同状态的流转
         */
        stateMachineBuilder.externalTransition()
                .from(OrderStatusEnum.UNDER_RECEIVE)
                .to(OrderStatusEnum.FINISHED)
                .on(OrderEvent.CONFIRM_RECEIVE)
                //.when(checkIfNotPass())
                .perform(confirmReceiveAction);
        /*
         * 取消支付: 支付中 -> 待付款
         * externalTransition : 不同状态的流转
         */
        stateMachineBuilder.externalTransition()
                .from(OrderStatusEnum.PAYING)
                .to(OrderStatusEnum.UNDER_PAY)
                .on(OrderEvent.CANCEL_PAY)
                //.when(checkIfNotPass())
                .perform(cancelPayAction);
        /*
         * 取消订单: 待付款 -> 已关闭
         * externalTransition : 不同状态的流转
         */
        stateMachineBuilder.externalTransition()
                .from(OrderStatusEnum.UNDER_PAY)
                .to(OrderStatusEnum.CLOSED)
                .on(OrderEvent.CANCEL_ORDER)
                //.when(checkIfNotPass())
                .perform(cancelOrderAction);

        /*
         * 自动关闭: 待付款 -> 已关闭
         * externalTransition : 不同状态的流转
         */
        stateMachineBuilder.externalTransition()
                .from(OrderStatusEnum.UNDER_PAY)
                .to(OrderStatusEnum.CLOSED)
                .on(OrderEvent.CLOSE)
                //.when(checkIfNotPass())
                .perform(cancelOrderAction);

        StateMachine<OrderStatusEnum, OrderEvent, OrderContext> stateMachine = stateMachineBuilder.build(StateMachineIdEnum.ORDER_STATE_MACHINE.getStateMachineId());
        String uml = stateMachine.generatePlantUML();
        log.info("{}", uml);

        return stateMachine;

    }

}
