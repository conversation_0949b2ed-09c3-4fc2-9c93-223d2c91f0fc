package com.sankuai.shangou.seashop.order.core.service.assit.order;

import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderMessagePublisher;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * 创建订单的消息处理器
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderCreateMessageHandler implements OrderChangeMessageHandler {
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderMessagePublisher orderMessagePublisher;
    
    @Override
    public OrderMessageEventEnum getEvent() {
        return OrderMessageEventEnum.CREATE_ORDER;
    }

    @Override
    public void handle(OrderMessage message) {
        String orderId = message.getOrderId();
        log.info("【订单状态变更】创建订单, orderId: {}",orderId);
        // 创建订单成功，且支付金额为0时，因为不需要支付，直接算支付成功
        Order order = orderRepository.getByOrderIdForceMaster(orderId);
        if (order == null) {
            log.error("【订单状态变更】创建订单失败，找不到订单, orderId: {}",orderId);
            return;
        }
        // 0元订单，执行支付成功逻辑
        if (BigDecimal.ZERO.compareTo(order.getTotalAmount()) == 0) {
            orderMessagePublisher.sendOrderChangeMessage(orderId, OrderMessageEventEnum.PAY_SUCCESS);
        }
    }
}
