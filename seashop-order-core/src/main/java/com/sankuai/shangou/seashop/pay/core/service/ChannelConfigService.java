package com.sankuai.shangou.seashop.pay.core.service;

import com.sankuai.shangou.seashop.pay.core.bo.ChannelConfigQueryBo;
import com.sankuai.shangou.seashop.pay.core.bo.ChannelConfigSaveBo;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ChannelConfig;
import com.sankuai.shangou.seashop.pay.dao.core.model.AdaPayConfigModel;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
public interface ChannelConfigService {

    /**
     * 保存渠道配置
     *
     * @param saveBo
     */
    void update(ChannelConfigSaveBo saveBo);

    /**
     * 条件查询渠道配置
     *
     * @param queryBo
     * @return
     */
    List<ChannelConfig> queryList(ChannelConfigQueryBo queryBo);


    /**
     * 获取汇付天下配置
     *
     * @return
     */
    AdaPayConfigModel getAdaPayConfigModel();

}
