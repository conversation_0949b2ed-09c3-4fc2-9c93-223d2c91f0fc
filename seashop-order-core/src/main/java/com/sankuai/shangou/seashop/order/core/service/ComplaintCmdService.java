package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.order.thrift.core.request.*;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/21 19:16
 */
public interface ComplaintCmdService {

    /**
     * 发起投诉（我要投诉）
     * @param cmdOrderComplaintReq
     */
    void startComplaint(StartComplaintReq cmdOrderComplaintReq);

    /**
     * 商家取消投诉
     * @param cancelComplaintReq
     */
    void cancelComplaint(CancelComplaintReq cancelComplaintReq);

    /**
     * 供应商审核
     * @param dealSellerComplaintReq
     */
    void dealSellerComplaint(DealSellerComplaintReq dealSellerComplaintReq);

    /**
     * 供应商申请仲裁
     * @param applyArbitrateComplaintReq
     */
    void applyArbitrateComplaint(ApplyArbitrateComplaintReq applyArbitrateComplaintReq);

    /**
     * 商家申请仲裁
     * @param applyArbitrateComplaintReq
     */
    void applyMallArbitrateComplaint(ApplyArbitrateComplaintReq applyArbitrateComplaintReq);

    /**
     * 平台审核
     * @param dealMComplaintReq
     */
    void dealMComplaint(DealMComplaintReq dealMComplaintReq);

}
