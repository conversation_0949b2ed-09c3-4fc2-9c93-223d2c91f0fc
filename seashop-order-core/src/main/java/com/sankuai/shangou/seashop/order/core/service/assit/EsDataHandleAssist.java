package com.sankuai.shangou.seashop.order.core.service.assit;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.order.core.service.model.stats.IntegerFieldStatsCountBo;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ES的数据处理辅助类
 * <AUTHOR>
 */
@Service
@Slf4j
public class EsDataHandleAssist {

    public SearchRequest buildNonDocRequest(String index, BoolQueryBuilder boolQueryBuilder, List<AggregationBuilder> aggregationBuilders) {
        // 整合查询条件，不需要返回文档，所以设置size为0
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .size(0);
        // 设置聚合
        if (CollUtil.isNotEmpty(aggregationBuilders)) {
            aggregationBuilders.forEach(sourceBuilder::aggregation);
        }
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(index);
        searchRequest.source(sourceBuilder);
        return searchRequest;
    }


    /**
     * 解析Count类型的聚合桶中的数据
     * @param bucketList 聚合桶
     * @return 聚合桶中的数据
     */
    public Map<Integer, IntegerFieldStatsCountBo> resolveCountAggToMap(List<? extends Terms.Bucket> bucketList) {
        // 遍历聚合桶设置数据
        if (bucketList == null || bucketList.isEmpty()) {
            return null;
        }
        return bucketList.stream()
                .map(bucket -> {
                    IntegerFieldStatsCountBo countBo = new IntegerFieldStatsCountBo();
                    countBo.setFieldValue(bucket.getKeyAsNumber().intValue());
                    countBo.setCount(bucket.getDocCount());
                    return countBo;
                })
                .collect(Collectors.toMap(IntegerFieldStatsCountBo::getFieldValue, countBo -> countBo));
    }

}
