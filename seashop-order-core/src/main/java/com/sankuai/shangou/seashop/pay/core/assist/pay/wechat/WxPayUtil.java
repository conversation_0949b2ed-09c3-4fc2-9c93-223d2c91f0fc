package com.sankuai.shangou.seashop.pay.core.assist.pay.wechat;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.binarywang.wxpay.bean.notify.OriginNotifyResponse;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2024/09/02 15:09
 */
@Component
public class WxPayUtil {

    @Resource
    private HttpServletRequest httpServletRequest;

    /**
     * 提取回调请求头
     *
     * @return 回调请求头
     */
    public Map<String, String> getNotifyPaySignatureHeader() {
        Map<String, String> header = new LinkedHashMap<>();
        Object timeStamp = httpServletRequest.getHeader("wechatpay-timestamp") == null ? httpServletRequest.getAttribute("wechatpay-timestamp") : httpServletRequest.getHeader("wechatpay-timestamp");
        header.put("timeStamp", String.valueOf(timeStamp));
        Object nonce = httpServletRequest.getHeader("wechatpay-nonce") == null ? httpServletRequest.getAttribute("wechatpay-nonce") : httpServletRequest.getHeader("wechatpay-nonce");
        header.put("nonce", String.valueOf(nonce));
        Object signature = httpServletRequest.getHeader("wechatpay-signature") == null ? httpServletRequest.getAttribute("wechatpay-signature") : httpServletRequest.getHeader("wechatpay-signature");
        header.put("signature", String.valueOf(signature));
        Object serial = httpServletRequest.getHeader("wechatpay-serial") == null ? httpServletRequest.getAttribute("wechatpay-serial") : httpServletRequest.getHeader("wechatpay-serial");
        header.put("serial", String.valueOf(serial));
        return header;
    }

    /**
     * 请求报文：按官方接口示例键值 --- 排序(必须)
     * 官方文档：https://pay.weixin.qq.com/wiki/doc/apiv3/apis/chapter3_1_5.shtml，
     * https://pay.weixin.qq.com/wiki/doc/apiv3/wechatpay/wechatpay4_1.shtml
     * 《微信支付API v3签名验证》 中注意：应答主体（response Body），需要按照接口返回的顺序进行验签，错误的顺序将导致验签失败。
     *
     * @param originNotifyResponse OriginNotifyResponse
     * @return String
     */
    public String jsonStrSort(OriginNotifyResponse originNotifyResponse) {
        Map<String, Object> jsonSort = new LinkedHashMap<>();
        jsonSort.put("id", originNotifyResponse.getId());
        jsonSort.put("create_time", originNotifyResponse.getCreateTime());
        jsonSort.put("resource_type", originNotifyResponse.getResourceType());
        jsonSort.put("event_type", originNotifyResponse.getEventType());
        jsonSort.put("summary", originNotifyResponse.getSummary());
        Map<String, Object> resource = new LinkedHashMap();
        resource.put("original_type", originNotifyResponse.getResource().getOriginalType());
        resource.put("algorithm", originNotifyResponse.getResource().getAlgorithm());
        resource.put("ciphertext", originNotifyResponse.getResource().getCiphertext());
        resource.put("associated_data", originNotifyResponse.getResource().getAssociatedData());
        resource.put("nonce", originNotifyResponse.getResource().getNonce());
        jsonSort.put("resource", resource);
        return JSONUtil.toJsonStr(jsonSort);
    }

    /**
     * 转换微信的支付状态 true-支付成功 false-支付失败 null-待确定
     * <p>
     * SUCCESS：支付成功
     * REFUND：转入退款
     * NOTPAY：未支付
     * CLOSED：已关闭
     * REVOKED：已撤销（付款码支付）
     * USERPAYING：用户支付中（付款码支付）
     * PAYERROR：支付失败(其他原因，如银行返回失败)
     *
     * @param tradeState
     * @return
     */
    public Boolean transTradeState(String tradeState) {
        switch (tradeState) {
            // 支付成功
            case "SUCCESS":
                return true;
            case "CLOSED":
            case "REVOKED":
            case "PAYERROR":
            // 转入退款 表示已经支付成功 并且已经退款, 这里暂时标记为支付失败
            case "REFUND":
                return false;
            default:
                break;
        }
        return null;
    }

    /**
     * 退款状态，枚举值：
     * SUCCESS：退款成功
     * CLOSE：退款关闭
     * ABNORMAL：退款异常，退款到银行发现用户的卡作废或者冻结了，导致原路退款银行卡失败，可前往【商户平台—>交易中心】，手动处理此笔退款
     * @param refundState
     * @return
     */
    public Boolean transRefundState(String refundState) {
        switch (refundState) {
            case "SUCCESS":
                return true;
            case "CLOSE": case "ABNORMAL":
                return false;
            default:
                break;
        }
        return null;
    }

    /**
     * 解析微信返回的时间字符串
     *
     * @param wxDate 微信返回的时间字符串
     * @return 解析后的时间
     */
    public Date parseWxDate(String wxDate) {
        if (StrUtil.isEmpty(wxDate)) {
            return null;
        }

        return DateUtil.parse(wxDate, "yyyy-MM-dd'T'HH:mm:ss+08:00");
    }
}
