package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderCheckBo {

    /**
     * 需要检查的订单ID
     */
    private List<String> orderIdList;
    /**
     * 用户ID
     */
    private Long userId;

}
