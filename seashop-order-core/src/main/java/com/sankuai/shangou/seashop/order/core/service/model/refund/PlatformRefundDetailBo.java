package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class PlatformRefundDetailBo extends RefundDetailBo {

    /**
     * 退款完成时间
     */
    private Date refundFinishDate;
    /**
     * 退款明细，如果为null，前端显示【订单所有商品】
     */
    private RefundItemBo item;

}
