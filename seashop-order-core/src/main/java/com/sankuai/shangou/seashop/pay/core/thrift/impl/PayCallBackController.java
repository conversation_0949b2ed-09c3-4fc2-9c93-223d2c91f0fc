package com.sankuai.shangou.seashop.pay.core.thrift.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.huifu.adapay.core.AdapayCore;
import com.huifu.adapay.core.util.AdapaySign;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.order.core.service.assit.BizNoGenerator;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.pay.common.constant.AdaPayConstant;
import com.sankuai.shangou.seashop.pay.core.assist.pay.AbstractPayHandler;
import com.sankuai.shangou.seashop.pay.core.assist.pay.PayFactory;
import com.sankuai.shangou.seashop.pay.core.service.ExchangeLogService;
import com.sankuai.shangou.seashop.pay.core.service.adapay.CallBackStrategyFactory;
import com.sankuai.shangou.seashop.pay.core.service.adapay.CallBackStrategyService;
import com.sankuai.shangou.seashop.pay.core.service.adapay.impl.PaymentCallBackServiceImpl;
import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import com.sankuai.shangou.seashop.pay.dao.core.repository.OrderPayRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.dto.MockPayCallBack;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ExchangeLogReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayCallBackNotifyPayReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayCallBackReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.PayCallBackFeign;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.UnsupportedEncodingException;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("/payCallBack")
public class PayCallBackController implements PayCallBackFeign {

    @Resource
    private CallBackStrategyFactory callBackStrategyFactory;
    @Resource
    private ExchangeLogService exchangeLogService;
    @Resource
    private PayFactory payFactory;
    @Resource
    private HttpServletRequest httpServletRequest;


    @PostMapping(value = "/adaPayCallback", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> adaPayCallback(@RequestBody PayCallBackReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("callback", request, req -> {
            log.info("支付回调接口请求全入参.adaPayCallback,request={}", JsonUtil.toJsonString(request));
            log.info("支付回调通知处理接受参数data={},通知具体类型type={}", req.getData(), req.getType());
            try {
                // 入参、返参插表记录
                ExchangeLogReq exchangeLogReq = new ExchangeLogReq();
                exchangeLogReq.setType(2);
                exchangeLogReq.setMethod("PayCallBackController.adaPayCallback");
                exchangeLogReq.setParam(JsonUtil.toJsonString(request));
                exchangeLogReq.setResult(null);
                exchangeLogService.insetExchangeLog(exchangeLogReq);
            } catch (Exception e) {
                log.info("汇付交互表写数失败");
            }
            //验签请参data
            String data = req.getData();
            //验签请参sign
            String sign = req.getSign();
            //验签标记
            boolean checkSign;
            try {
                checkSign = AdapaySign.verifySign(data, sign, AdapayCore.PUBLIC_KEY);
                if (!checkSign) {
                    log.info("支付回调通知处理验签失败={}:", sign);
                    throw new BusinessException("验签失败");
                }
                List<String> types = StrUtil.split(req.getType(), ".");
                CallBackStrategyService resource = callBackStrategyFactory.getCallBackStrategyService(types.get(0));
                resource.callback(data);
            } catch (Exception e) {
                log.error("支付回调通知处理失败", e);
                throw new BusinessException(e.getMessage());
            }
            return BaseResp.of();
        });
    }

    @Override
    @PostMapping(value = "/notifyPay/{channel}", consumes = "application/json")
    public Object notifyPay(@PathVariable("channel") String channel, @RequestBody PayCallBackNotifyPayReq notifyPayReq) {
        log.info("【支付回调】通知处理接受参数channel:{}, notifyPayReq:{}", channel, JsonUtil.toJsonString(notifyPayReq));
        String notifyData = notifyPayReq.getNotifyData();
        this.putNotifyPaySignatureHeader(notifyPayReq);
        log.info("【支付回调】通知处理接受参数channel={},notifyData={}", channel, notifyData);

        /*// 转义的jsonStr还原为string - 使用Jackson ObjectMapper处理
        String unescapedNotifyData = JsonUtil.unescapeJsonString(notifyData);
        log.info("【支付回调】反转义后的notifyData={}", unescapedNotifyData);*/

        AssertUtil.throwIfNull(channel, "支付渠道不存在");

        PaymentChannelEnum paymentChannel = PaymentChannelEnum.getByName(channel);
        AssertUtil.throwIfNull(paymentChannel, "支付渠道不存在");
        AbstractPayHandler handler = payFactory.getPayHandler(paymentChannel);
        return handler.commonNotifyPay(notifyData);
    }

    @Override
    @PostMapping("/aliPayCallback")
    public Object aliPayCallback(HttpServletRequest request)  {

        Map<String, String> params = new HashMap<String, String>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用
            try {
                valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            params.put(name, valueStr);
        }

        String payLoad = JsonUtil.toJsonString(params);
        AbstractPayHandler handler = payFactory.getPayHandler(PaymentChannelEnum.ALIPAY);
        return handler.commonNotifyPay(payLoad);
    }


    @Override
    public Object notifyRefund(String channel, @RequestBody PayCallBackNotifyPayReq notifyPayReq) {
        log.info("【退款回调】通知处理接受参数channel:{}, notifyPayReq:{}", channel, JsonUtil.toJsonString(notifyPayReq));
        String notifyData = notifyPayReq.getNotifyData();
        this.putNotifyPaySignatureHeader(notifyPayReq);
        log.info("【退款回调】通知处理接受参数channel={},notifyData={}", channel, notifyData);

        AssertUtil.throwIfNull(channel, "支付渠道不存在");

        PaymentChannelEnum paymentChannel = PaymentChannelEnum.getByName(channel);
        AssertUtil.throwIfNull(paymentChannel, "支付渠道不存在");
        AbstractPayHandler handler = payFactory.getPayHandler(paymentChannel);
        return handler.commonNotifyRefund(notifyData);
    }

    @Override
    public Object aliNotifyRefund(HttpServletRequest request) {
        Map<String, String> params = new HashMap<String, String>();
        Map<String, String[]> requestParams = request.getParameterMap();
        for (Iterator<String> iter = requestParams.keySet().iterator(); iter.hasNext(); ) {
            String name = (String) iter.next();
            String[] values = (String[]) requestParams.get(name);
            String valueStr = "";
            for (int i = 0; i < values.length; i++) {
                valueStr = (i == values.length - 1) ? valueStr + values[i]
                        : valueStr + values[i] + ",";
            }
            //乱码解决，这段代码在出现乱码时使用
            try {
                valueStr = new String(valueStr.getBytes("ISO-8859-1"), "utf-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }
            params.put(name, valueStr);
        }

        String payLoad = JsonUtil.toJsonString(params);
        AbstractPayHandler handler = payFactory.getPayHandler(PaymentChannelEnum.ALIPAY);
        return handler.commonNotifyRefund(payLoad);
    }

    /**
     * 设置请求参数
     *
     * @param notifyPayReq 通知参数
     */
    private void putNotifyPaySignatureHeader(PayCallBackNotifyPayReq notifyPayReq) {
        Map<String, Object> notifySignHeaders = notifyPayReq.getNotifySignHeaders();
        if (notifySignHeaders != null && !notifySignHeaders.isEmpty()) {
            notifySignHeaders.forEach((key, value) -> {
                httpServletRequest.setAttribute(key, value);
            });
        }
    }

    @Resource
    private OrderPayRepository orderPayRepository;
    @Resource
    private LeafService leafService;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private BizNoGenerator bizNoGenerator;
    /**
     * <p>
     * {
     * "orderId": "orderId_e4c548faf55c", // 订单id
     * "payAmount": 0.00, // 金额
     * "channelPayId": "channelPayId_5ce326b85831" //支付渠道id，用于模拟支付回调中Id字段
     * }
     * </p>
     *
     * @param request
     * @return
     */
    @PostMapping(value = "/mock/pay", consumes = "application/json")
    public ResultDto<String> mockCreatePay(@RequestBody OrderPayResp request) {
        // 查询是否保存过订单支付信息
        OrderPay oldPay = orderPayRepository.lambdaQuery()
                .eq(OrderPay::getOrderId, request.getOrderId())
                .last("limit 1")
                .one();
        String payId;
        if (oldPay == null) {
            // 保存订单支付信息
            payId = AdaPayConstant.PAY_ID_PREFIX + leafService.generateNoBySnowFlake(AdaPayConstant.LEAF_ADAPAY_PAY_ID_KEY);
            OrderPay orderPay = new OrderPay();
            orderPay.setOrderId(request.getOrderId());
            orderPay.setPayId(payId);
            orderPay.setPayAmount(request.getPayAmount());
            orderPay.setPayState(PayStateEnums.UNPAID.getStatus());
            orderPay.setChannelPayId(request.getChannelPayId());
            orderPay.setPaymentType(6);
            orderPayRepository.save(orderPay);
            // orderPayRecordRepository.updatePayNoByBatchNo(batchNo, request.getChannelPayId());
        }else {
            payId = oldPay.getPayId();
            oldPay.setChannelPayId(request.getChannelPayId());
            orderPayRepository.updateById(oldPay);
            log.info("订单支付信息已存在,payId={}, 更新channelPayId={}", payId, request.getChannelPayId());
        }
        // 查询是否保存过支付记录
        OrderPayRecord oldRecord = orderPayRecordRepository.lambdaQuery()
                .eq(OrderPayRecord::getOrderId, request.getOrderId())
                .last("limit 1")
                .one();
        if (oldRecord == null) {
            // 保存支付记录
            OrderPayRecord record = new OrderPayRecord();
            // 生成批次号
            String batchNo = bizNoGenerator.generatePayNo();
            record.setOrderId(request.getOrderId());
            record.setBatchNo(batchNo);
            record.setOrderAmount(request.getPayAmount());
            record.setPayChannel(PaymentChannelEnum.MOCKPAY.getCode());
            record.setPayMethod(9);
            record.setPayStatus(PayStatusEnum.PAYING.getCode());
            Date now = DateUtil.date();
            record.setCreateTime(now);
            record.setUpdateTime(now);
            orderPayRecordRepository.save(record);
        }else {
            log.info("支付记录已存在,batchNo={}", oldRecord.getBatchNo());
        }

        return ResultDto.newWithData(payId);
    }

    @Resource
    private PaymentCallBackServiceImpl paymentCallBackService;

    @PostMapping(value = "/mock/collBack", consumes = "application/json")
    public ResultDto<BaseResp> mockCallBack(@RequestBody MockPayCallBack callBack) {
        return ThriftResponseHelper.responseInvoke("mockCallBack", callBack, parma -> {
            paymentCallBackService.callback(JsonUtil.toJsonString(callBack));
            return BaseResp.of();
        });
    }
}
