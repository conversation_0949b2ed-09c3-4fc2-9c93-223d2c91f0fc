package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderInvoice;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname OrderCreateDTO
 * Description
 * @date 2024/11/7 16:27
 */
@Data
public class OrderCreateDTO implements Serializable {
    /**
     * 订单
     */
    private Order order;
    /**
     * 订单项
     */
    private List<OrderItem> orderItemList;
    /**
     * 发票
     */
    private OrderInvoice invoice;

}
