package com.sankuai.shangou.seashop.order.core.thrift.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import com.sankuai.shangou.seashop.order.core.service.OrderExportService;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundSearchService;
import com.sankuai.shangou.seashop.order.core.service.OrderService;
import com.sankuai.shangou.seashop.order.core.service.OrderStatsService;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ErpOrderDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderAndItemScrollBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderExportBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderOperationLogBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderWayBillBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderWayBillListBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryPlatformOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QuerySellerOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryUserOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserQueryRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserRefundBo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderAndItemInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderOperationLogResp;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundUserQueryTabEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollClearReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollQueryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.OrderStatisticsMemberReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.OrderStatisticsReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryBatchErpOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryErpOrderDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryErpPageOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderDistributionReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderWayBillReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPlatformOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QuerySellerOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryUserOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.CountUserFlashSaleReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryProductBuyCountReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryUserOrderWayBillReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.CountFlashOrderAndProductResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.EachStatusCountResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ErpBatchOrderListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ErpOrderDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderDistributionFormResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderProductDistributionFormResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderStatisticsResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderWayBillListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderWayBillResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.OrderAndItemFlatListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.ProductBuyCountListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.ProductBuyCountResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdReq;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.thrift.TException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/orderQuery")
@Tag(name = "订单查询接口", description = "订单相关查询操作接口")
public class OrderQueryController implements OrderQueryFeign {

    @Resource
    private OrderService orderService;
    @Resource
    private EsOrderService esOrderService;
    @Resource
    private OrderStatsService orderStatsService;
    @Resource
    private OrderRefundSearchService orderRefundSearchService;
    @Resource
    private OrderExportService orderExportService;
    @Value("${wx.send: false}")
    private Boolean wxSend;

    @PostMapping(value = "/pageQueryUserOrder", consumes = "application/json")
    @Override
    @Operation(summary = "买家订单分页列表", description = "分页查询买家订单列表")
    public ResultDto<BasePageResp<OrderInfoDto>> pageQueryUserOrder(@RequestBody QueryUserOrderReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【订单】商家订单分页列表", queryReq, func -> {
            // 业务逻辑处理
            QueryUserOrderBo queryUserOrderBo = JsonUtil.copy(queryReq, QueryUserOrderBo.class);
            BasePageResp<OrderInfoBo> resp = esOrderService.searchForUser(queryUserOrderBo);
            if(CollectionUtils.isNotEmpty(resp.getData())) {
                resp.getData().forEach(f->{
                    f.setWxSend(wxSend);
                });
            }
            return JsonUtil.copy(resp, new TypeReference<BasePageResp<OrderInfoDto>>() {
            });
        });
    }

    @PostMapping(value = "/pageQuerySellerOrder", consumes = "application/json")
    @Override
    @Operation(summary = "卖家订单分页列表", description = "分页查询卖家订单列表")
    public ResultDto<BasePageResp<OrderInfoDto>> pageQuerySellerOrder(@RequestBody QuerySellerOrderReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【订单】卖家订单分页列表", queryReq, func -> {
            // 业务逻辑处理
            QuerySellerOrderBo queryUserOrderBo = JsonUtil.copy(queryReq, QuerySellerOrderBo.class);
            BasePageResp<OrderInfoBo> resp = esOrderService.searchForSeller(queryUserOrderBo);
            return JsonUtil.copy(resp, new TypeReference<BasePageResp<OrderInfoDto>>() {
            });
        });
    }

    @PostMapping(value = "/pageQueryPlatformOrder", consumes = "application/json")
    @Override
    @Operation(summary = "平台订单分页列表", description = "分页查询平台订单列表")
    public ResultDto<BasePageResp<OrderInfoDto>> pageQueryPlatformOrder(@RequestBody QueryPlatformOrderReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【订单】平台订单分页列表", queryReq, func -> {
            // 业务逻辑处理
            QueryPlatformOrderBo queryUserOrderBo = JsonUtil.copy(queryReq, QueryPlatformOrderBo.class);
            BasePageResp<OrderInfoBo> resp = esOrderService.searchForPlatform(queryUserOrderBo);
            return JsonUtil.copy(resp, new TypeReference<BasePageResp<OrderInfoDto>>() {
            });
        });
    }

    @PostMapping(value = "/pageQueryErpOrder", consumes = "application/json")
    @Override
    @Operation(summary = "ERP订单分页列表", description = "分页查询ERP订单列表")
    public ResultDto<BasePageResp<ErpOrderDetailResp>> pageQueryErpOrder(@RequestBody QueryErpPageOrderReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】ERP订单分页列表", req, func -> {
            // 业务逻辑处理
            BasePageResp<ErpOrderDetailBo> resp = orderService.queryOrderPageForErp(req);
            return JsonUtil.copy(resp, new TypeReference<BasePageResp<ErpOrderDetailResp>>() {
            });
        });
    }

    @PostMapping(value = "/queryDetail", consumes = "application/json")
    @Override
    @Operation(summary = "查询订单详情", description = "根据订单ID查询订单详情")
    public ResultDto<OrderDetailResp> queryDetail(@RequestBody QueryOrderDetailReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【订单】查询订单详情", queryReq, func -> {
            // 业务逻辑处理
            OrderDetailBo detail = orderService.getDetail(queryReq.getOrderId(), queryReq.getQueryFrom());
            return JsonUtil.copy(detail, OrderDetailResp.class);
        });
    }

    @PostMapping(value = "/queryDetailForPlatform", consumes = "application/json")
    @Override
    @Operation(summary = "平台查询订单详情", description = "平台端根据订单ID查询订单详情")
    public ResultDto<OrderDetailResp> queryDetailForPlatform(@RequestBody QueryOrderDetailReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】平台查询订单详情", queryReq, func -> {
            // 业务逻辑处理
            OrderDetailBo detail = orderService.getDetail(queryReq.getOrderId(), OrderQueryFromEnum.PLATFORM_PC);
            return JsonUtil.copy(detail, OrderDetailResp.class);
        });
    }

    @GetMapping(value = "/queryOrderLog")
    @Override
    @Operation(summary = "查询订单操作日志", description = "根据订单ID查询订单操作日志")
    public ResultDto<List<OrderOperationLogResp>> queryOrderLog(@RequestParam String orderId) {
        return ThriftResponseHelper.responseInvoke("【订单】查询订单操作日志", orderId, func -> {
            // 业务逻辑处理
            List<OrderOperationLogBo> logList = orderService.getOrderLog(orderId);
            return JsonUtil.copyList(logList, OrderOperationLogResp.class);
        });
    }

    @PostMapping(value = "/queryOrderWayBill", consumes = "application/json")
    @Override
    @Operation(summary = "查询订单收货地址", description = "根据订单ID查询订单收货地址")
    public ResultDto<OrderWayBillListResp> queryOrderWayBill(@RequestBody QueryOrderWayBillReq queryReq) {
        return ThriftResponseHelper.responseInvoke("【订单】查询订单收货地址", queryReq, func -> {
            queryReq.checkParameter();
            // 业务逻辑处理
            OrderWayBillListBo detail = orderService.getOrderWayBill(queryReq.getShopId(), queryReq.getOrderIdList());
            return JsonUtil.copy(detail, OrderWayBillListResp.class);
        });
    }

    @PostMapping(value = "/getOrderStatistics", consumes = "application/json")
    @Override
    @Operation(summary = "获取订单统计", description = "获取订单统计信息")
    public ResultDto<OrderStatisticsResp> getOrderStatistics(@RequestBody OrderStatisticsReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getOrderStatistics", request, req -> {
            req.checkParameter();
            return orderService.getOrderStatistics(req);
        });
    }

    @PostMapping(value = "/getOrderStatisticsByMember", consumes = "application/json")
    @Override
    @Operation(summary = "获取会员订单统计", description = "根据会员ID获取订单统计信息")
    public ResultDto<OrderStatisticsResp> getOrderStatisticsByMember(@RequestBody OrderStatisticsMemberReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getOrderStatistics", request, req -> {
            req.checkParameter();
            return orderService.getOrderStatisticsByMember(req);
        });
    }

    @PostMapping(value = "/getOrderStatisticsList", consumes = "application/json")
    @Override
    @Operation(summary = "获取订单统计列表", description = "获取订单统计信息列表")
    public ResultDto<OrderStatisticsListResp> getOrderStatisticsList(@RequestBody OrderStatisticsReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getOrderStatisticsList", request, req -> {
            req.checkParameter();
            return orderService.getOrderStatisticsList(req);
        });
    }

    @PostMapping(value = "/queryErpDetail", consumes = "application/json")
    @Override
    @Operation(summary = "查询ERP订单详情", description = "根据ERP订单ID查询订单详情")
    public ResultDto<ErpOrderDetailResp> queryErpDetail(@RequestBody QueryErpOrderDetailReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】查询ERP订单详情", req, func -> {
            func.checkParameter();

            // 业务逻辑处理
            ErpOrderDetailBo detail = orderService.getErpOrderDetail(req.getOrderId(), req.getSourceOrderId());
            return JsonUtil.copy(detail, ErpOrderDetailResp.class);
        });
    }

    @PostMapping(value = "/queryEachStatusCount", consumes = "application/json")
    @Override
    @Operation(summary = "ERP批量查询订单", description = "ERP批量查询订单信息")
    public ResultDto<ErpBatchOrderListResp> queryBatchErpOrder(@RequestBody QueryBatchErpOrderReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】ERP批量查询订单", req, func -> {
            // 业务逻辑处理
            List<ErpOrderDetailBo> list = orderService.queryBatchOrderForErp(req);
            ErpBatchOrderListResp resp = new ErpBatchOrderListResp();
            resp.setOrders(JsonUtil.copyList(list, ErpOrderDetailResp.class));
            return resp;
        });
    }

    @RequestMapping("/queryEachStatusCount")
    @Override
    @Operation(summary = "查询各状态订单数量", description = "根据用户ID查询各状态订单数量")
    public ResultDto<EachStatusCountResp> queryEachStatusCount(@RequestParam Long userId) throws TException {
        if (userId == null) {
            return ResultDto.newWithData(new EachStatusCountResp());
        }
        EachStatusCountResp result = new EachStatusCountResp();
        // 统计全部订单数量
        QueryUserOrderBo allOrderReq = new QueryUserOrderBo();
        allOrderReq.setUserId(userId);
        allOrderReq.setOrderStatus(OrderStatusEnum.UNKNOWN.getCode());
        allOrderReq.setQueryFrom(OrderQueryFromEnum.SHOP_PC);
        allOrderReq.setQueryUnCommented(false);
        BasePageResp<OrderInfoBo> allOrderResp = esOrderService.searchForUser(allOrderReq);
        result.setAllOrderCount(allOrderResp.getTotalCount());

        // 统计待支付订单数量
        QueryUserOrderBo waitingForPayReq = new QueryUserOrderBo();
        waitingForPayReq.setUserId(userId);
        waitingForPayReq.setOrderStatus(OrderStatusEnum.UNDER_PAY.getCode());
        waitingForPayReq.setQueryFrom(OrderQueryFromEnum.SHOP_PC);
        waitingForPayReq.setQueryUnCommented(false);
        BasePageResp<OrderInfoBo> waitingForPayResp = esOrderService.searchForUser(waitingForPayReq);
        result.setWaitingForPay(waitingForPayResp.getTotalCount());

        // 待发货订单数量
        QueryUserOrderBo waitingForDeliveryReq = new QueryUserOrderBo();
        waitingForDeliveryReq.setUserId(userId);
        waitingForDeliveryReq.setOrderStatus(OrderStatusEnum.UNDER_SEND.getCode());
        waitingForDeliveryReq.setQueryFrom(OrderQueryFromEnum.SHOP_PC);
        waitingForDeliveryReq.setQueryUnCommented(false);
        BasePageResp<OrderInfoBo> waitingForDeliveryResp = esOrderService.searchForUser(waitingForDeliveryReq);
        result.setWaitingForDelivery(waitingForDeliveryResp.getTotalCount());

        // 待收货订单数量
        QueryUserOrderBo waitingForRecieveReq = new QueryUserOrderBo();
        waitingForRecieveReq.setUserId(userId);
        waitingForRecieveReq.setOrderStatus(OrderStatusEnum.UNDER_RECEIVE.getCode());
        waitingForRecieveReq.setQueryFrom(OrderQueryFromEnum.SHOP_PC);
        waitingForRecieveReq.setQueryUnCommented(false);
        BasePageResp<OrderInfoBo> waitingForRecieveResp = esOrderService.searchForUser(waitingForRecieveReq);
        result.setWaitingForRecieve(waitingForRecieveResp.getTotalCount());

        // 待处理售后订单数量
        UserQueryRefundBo refundBo = new UserQueryRefundBo();
        UserBo user = new UserBo();
        user.setUserId(userId);
        refundBo.setUser(user);
        refundBo.setTab(RefundUserQueryTabEnum.ALL_APPLY);
        refundBo.setRefundStatusList(Arrays.asList(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode(),
                RefundStatusEnum.WAIT_BUYER_SEND.getCode(),
                RefundStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode(),
                RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode()));
        BasePageResp<UserRefundBo> refundResp = orderRefundSearchService.userQueryPage(refundBo);
        result.setRefundCount(refundResp.getTotalCount());

        // 待评论订单数量
        QueryUserOrderBo waitingForCommentsReq = new QueryUserOrderBo();
        waitingForCommentsReq.setUserId(userId);
        waitingForCommentsReq.setOrderStatus(OrderStatusEnum.UNKNOWN.getCode());
        waitingForCommentsReq.setQueryFrom(OrderQueryFromEnum.SHOP_PC);
        waitingForCommentsReq.setQueryUnCommented(true);
        BasePageResp<OrderInfoBo> waitingForCommentsResp = esOrderService.searchForUser(waitingForCommentsReq);
        result.setWaitingForComments(waitingForCommentsResp.getTotalCount());

        return ResultDto.newWithData(result);
//        return ThriftResponseHelper.responseInvoke("queryEachStatusCount", userId, req -> orderService.queryEachStatusCount(req));
    }

    @GetMapping(value = "/queryLastOrderInfo")
    @Override
    @Operation(summary = "查询最近订单信息", description = "根据用户ID查询最近下单的订单信息")
    public ResultDto<OrderInfoDto> queryLastOrderInfo(@RequestParam Long userId) throws TException {
        if (userId == null) {
            return ResultDto.newWithData(new OrderInfoDto());
        }
        return ThriftResponseHelper.responseInvoke("queryLastOrderInfo", userId, req -> orderService.queryLastOrderInfo(req));
    }

    @PostMapping(value = "/countFlashSaleByProduct", consumes = "application/json")
    @Override
    @Operation(summary = "统计商品维度限时购购买数量", description = "统计用户在商品维度的限时购购买数量")
    public ResultDto<Long> countFlashSaleByProduct(@RequestBody CountUserFlashSaleReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】统计商品维度的用户限时购购买数量", req, func -> {
            // 业务逻辑处理
            Long userId = req.getUserId();
            Long flashSaleId = req.getFlashSaleId();
            Long productId = req.getProductId();
            return esOrderService.countFlashSaleByProduct(userId, flashSaleId, productId);
        });
    }

    @PostMapping(value = "/countFlashSaleBySku", consumes = "application/json")
    @Override
    @Operation(summary = "统计SKU维度限时购购买数量", description = "统计用户在SKU维度的限时购购买数量")
    public ResultDto<Long> countFlashSaleBySku(@RequestBody CountUserFlashSaleReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】统计SKU维度的用户限时购购买数量", req, func -> {
            // 业务逻辑处理
            Long userId = req.getUserId();
            Long flashSaleId = req.getFlashSaleId();
            String skuId = req.getSkuId();
            return esOrderService.countFlashSaleBySku(userId, flashSaleId, skuId);
        });
    }

    /**
     * 导出单独提供接口，因为按订单维度导出再内存平铺的话，可能一个订单的商品很多，导致批次处理的数据太多
     * <AUTHOR>
     * @param queryReq
     *
     */
    @PostMapping(value = "/exportForUser", consumes = "application/json")
    @Override
    @Operation(summary = "买家导出订单分页列表", description = "买家端导出订单分页列表")
    public ResultDto<BasePageResp<OrderAndItemInfoDto>> exportForUser(@RequestBody QueryUserOrderReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】买家-导出订单分页列表", queryReq, func -> {
            queryReq.checkParameter();
            // 业务逻辑处理
            QueryUserOrderBo queryUserOrderBo = JsonUtil.copy(queryReq, QueryUserOrderBo.class);
            BasePageResp<OrderExportBo> resp = orderExportService.searchExportForUser(queryUserOrderBo);
            return JsonUtil.copy(resp, new TypeReference<BasePageResp<OrderAndItemInfoDto>>() {
            });
        });
    }

    /**
     * 导出单独提供接口，因为按订单维度导出再内存平铺的话，可能一个订单的商品很多，导致批次处理的数据太多
     * <AUTHOR>
     * @param queryReq
     *
     */
    @PostMapping(value = "/exportForSeller", consumes = "application/json")
    @Override
    @Operation(summary = "卖家导出订单分页列表", description = "卖家端导出订单分页列表")
    public ResultDto<BasePageResp<OrderAndItemInfoDto>> exportForSeller(@RequestBody QuerySellerOrderReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】卖家-导出订单分页列表", queryReq, func -> {
            queryReq.checkParameter();
            // 业务逻辑处理
            QuerySellerOrderBo queryUserOrderBo = JsonUtil.copy(queryReq, QuerySellerOrderBo.class);
            BasePageResp<OrderExportBo> resp = orderExportService.searchExportForSeller(queryUserOrderBo);
            return JsonUtil.copy(resp, new TypeReference<BasePageResp<OrderAndItemInfoDto>>() {
            });
        });
    }

    /**
     * 导出单独提供接口，因为按订单维度导出再内存平铺的话，可能一个订单的商品很多，导致批次处理的数据太多
     * <AUTHOR>
     * @param queryReq
     *
     */
    @PostMapping(value = "/exportForPlatform", consumes = "application/json")
    @Override
    @Operation(summary = "平台导出订单分页列表", description = "平台端导出订单分页列表")
    public ResultDto<BasePageResp<OrderAndItemInfoDto>> exportForPlatform(@RequestBody QueryPlatformOrderReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】平台-导出订单分页列表", queryReq, func -> {
            // 业务逻辑处理
            QueryPlatformOrderBo queryUserOrderBo = JsonUtil.copy(queryReq, QueryPlatformOrderBo.class);
            BasePageResp<OrderExportBo> resp = orderExportService.searchExportForPlatform(queryUserOrderBo);
            return JsonUtil.copy(resp, new TypeReference<BasePageResp<OrderAndItemInfoDto>>() {
            });
        });
    }

    @PostMapping(value = "/getScrollIdForUserExport", consumes = "application/json")
    @Override
    @Operation(summary = "获取买家导出订单scrollId", description = "获取买家导出订单scrollId")
    public ResultDto<OrderAndItemFlatListResp> getScrollIdForUserExport(@RequestBody QueryUserOrderReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【商家导出订单】获取订单查询scroll", queryReq, func -> {
            queryReq.checkParameter();
            // 业务逻辑处理
            QueryUserOrderBo queryUserOrderBo = JsonUtil.copy(queryReq, QueryUserOrderBo.class);
            OrderAndItemScrollBo scroll = orderExportService.getScrollIdForUserExport(queryUserOrderBo);
            return JsonUtil.copy(scroll, OrderAndItemFlatListResp.class);
        });
    }

    @PostMapping(value = "/getScrollIdForSellerExport", consumes = "application/json")
    @Override
    @Operation(summary = "获取卖家导出订单scrollId", description = "获取卖家导出订单scrollId")
    public ResultDto<OrderAndItemFlatListResp> getScrollIdForSellerExport(@RequestBody QuerySellerOrderReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【供应商导出订单】获取订单查询scroll", queryReq, func -> {
            queryReq.checkParameter();
            // 业务逻辑处理
            QuerySellerOrderBo queryUserOrderBo = JsonUtil.copy(queryReq, QuerySellerOrderBo.class);
            OrderAndItemScrollBo scroll = orderExportService.getScrollIdForSellerExport(queryUserOrderBo);
            return JsonUtil.copy(scroll, OrderAndItemFlatListResp.class);
        });
    }

    @PostMapping(value = "/getScrollIdForPlatformExport", consumes = "application/json")
    @Override
    @Operation(summary = "获取平台导出订单scrollId", description = "获取平台导出订单scrollId")
    public ResultDto<OrderAndItemFlatListResp> getScrollIdForPlatformExport(@RequestBody QueryPlatformOrderReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【平台导出订单】获取订单查询scroll", queryReq, func -> {
            // 业务逻辑处理
            QueryPlatformOrderBo queryUserOrderBo = JsonUtil.copy(queryReq, QueryPlatformOrderBo.class);
            OrderAndItemScrollBo scroll = orderExportService.getScrollIdForPlatformExport(queryUserOrderBo);
            return JsonUtil.copy(scroll, OrderAndItemFlatListResp.class);
        });
    }

    @PostMapping(value = "/clearScrollId", consumes = "application/json")
    @Override
    @Operation(summary = "清除scroll数据", description = "清除订单导出scrollId相关数据")
    public ResultDto<BaseResp> clearScrollId(@RequestBody EsScrollClearReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】清除scroll数据", queryReq, func -> {
            queryReq.checkParameter();
            orderExportService.clearScrollId(queryReq.getScrollId());
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/listOrderAndItemFlatByScroll", consumes = "application/json")
    @Override
    @Operation(summary = "根据scrollId查询订单和明细平铺数据", description = "根据scrollId查询订单和明细平铺数据")
    public ResultDto<OrderAndItemFlatListResp> listOrderAndItemFlatByScroll(@RequestBody EsScrollQueryReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】根据scrollId查询订单和明细平铺数据", queryReq, func -> {
            queryReq.checkParameter();
            List<OrderExportBo> dataList = orderExportService.searchByScrollId(queryReq);
            List<OrderAndItemInfoDto> resultList = JsonUtil.copyList(dataList, OrderAndItemInfoDto.class);
            OrderAndItemFlatListResp resp = new OrderAndItemFlatListResp();
            resp.setDataList(resultList);
            return resp;
        });
    }

    @PostMapping(value = "/exportOrderDistribution", consumes = "application/json")
    @Override
    @Operation(summary = "导出订单分布", description = "导出订单分布数据")
    public ResultDto<List<OrderDistributionFormResp>> exportOrderDistribution(@RequestBody QueryOrderDistributionReq queryReq) throws TException {
        queryReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("exportOrderDistribution", queryReq, req -> orderService.exportOrderDistribution(req));
    }

    @PostMapping(value = "/exportOrderProductDistribution", consumes = "application/json")
    @Override
    @Operation(summary = "导出订单商品分布", description = "导出订单商品分布数据")
    public ResultDto<List<OrderProductDistributionFormResp>> exportOrderProductDistribution(@RequestBody QueryOrderDistributionReq queryReq) throws TException {
        queryReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("exportOrderProductDistribution", queryReq, req -> orderService.exportOrderProductDistribution(req));
    }

    @PostMapping(value = "/countFlashOrderAndProduct", consumes = "application/json")
    @Override
    @Operation(summary = "统计限时购订单和商品", description = "统计限时购订单和商品数量")
    public ResultDto<CountFlashOrderAndProductResp> countFlashOrderAndProduct(@RequestBody ShopIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("countFlashOrderAndProduct", request, req -> {
            req.checkParameter();
            return orderService.countFlashOrderAndProduct(req);
        });
    }

    @PostMapping(value = "/getUserOrderWayBill", consumes = "application/json")
    @Override
    @Operation(summary = "获取用户订单物流信息", description = "获取用户订单物流信息")
    public ResultDto<OrderWayBillResp> getUserOrderWayBill(@RequestBody QueryUserOrderWayBillReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】获取用户订单物流信息", queryReq, func -> {
            // 业务逻辑处理
            OrderWayBillBo bill = orderService.getUserOrderWayBill(queryReq.getUserId(), queryReq.getOrderId());
            return JsonUtil.copy(bill, OrderWayBillResp.class);
        });
    }

    @PostMapping(value = "/getUserProductBuyCount", consumes = "application/json")
    @Override
    @Operation(summary = "获取用户商品购买数量", description = "获取用户购买商品的数量")
    public ResultDto<ProductBuyCountListResp> getUserProductBuyCount(@RequestBody QueryProductBuyCountReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】获取用户订单物流信息", queryReq, func -> {
            // 调用ES获取用户购买商品数量，虽然有延迟，但是用户的购买行为间隔理论上不会太短
            List<ProductBuyCountResp> list = orderStatsService.searchUserProductBuyCount(queryReq);
            ProductBuyCountListResp resp = new ProductBuyCountListResp();
            resp.setDataList(list);
            return resp;
        });
    }

    @PostMapping(value = "/getNearOrderAddress", consumes = "application/json")
    @Override
    @Operation(summary = "查询最近下单收货地址", description = "查询用户最近下单的收货地址")
    public ResultDto<ShippingAddressDto> getNearOrderAddress(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单】查询最近下单的收货地址", request, req -> {
            req.checkParameter();

            return orderStatsService.getNearOrderAddress(req.getId());
        });
    }
}
