package com.sankuai.shangou.seashop.order.core.thrift.impl;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundSearchService;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundService;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.OrderItemRefundPreviewBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.OrderRefundPreviewBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformQueryRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformRefundDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.QueryErpRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundLogListBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerQueryRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerRefundDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserDeliveryBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserQueryRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserRefundDetailBo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ErpQueryRefundPageReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ErpQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformQueryRefundDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.QueryOrderItemRefundPreviewReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.QueryOrderRefundPreviewReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.UserQueryRefundDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.UserQueryRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.OrderItemRefundPreviewResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.OrderRefundPreviewResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.PlatformRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.PlatformRefundDto;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundLogListResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.RefundUserDeliverExpressResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.SellerRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.SellerRefundDto;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.UserRefundDetailExtResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.UserRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.UserRefundDto;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/orderRefundQuery")
public class OrderRefundQueryController implements OrderRefundQueryFeign {

    @Resource
    private OrderRefundService orderRefundService;
    @Resource
    private OrderRefundSearchService orderRefundSearchService;

    private final static String[] IGNORE_PROPERTIES = {"refundStatus"};

    @PostMapping(value = "/getOrderRefundPreview", consumes = "application/json")
    @Override
    public ResultDto<OrderRefundPreviewResp> getOrderRefundPreview(@RequestBody QueryOrderRefundPreviewReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】商家获取订单退款预览", queryReq, func -> {
            UserBo user = JsonUtil.copy(queryReq.getUser(), UserBo.class);
            // 业务逻辑处理
            OrderRefundPreviewBo queryBo = orderRefundService.getOrderRefundPreview(queryReq.getOrderId(), queryReq.getRefundId(), user);
            return JsonUtil.copy(queryBo, OrderRefundPreviewResp.class);
        });
    }

    @PostMapping(value = "/getOrderItemRefundPreview", consumes = "application/json")
    @Override
    public ResultDto<OrderItemRefundPreviewResp> getOrderItemRefundPreview(@RequestBody QueryOrderItemRefundPreviewReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】商家获取订单明细退款预览", queryReq, func -> {
            UserBo user = JsonUtil.copy(queryReq.getUser(), UserBo.class);
            // 业务逻辑处理
            OrderItemRefundPreviewBo queryBo = orderRefundService.getOrderItemRefundPreview(queryReq.getOrderId(), queryReq.getOrderItemId(), queryReq.getRefundId(), user);
            return JsonUtil.copy(queryBo, OrderItemRefundPreviewResp.class);
        });
    }

    @PostMapping(value = "/userQueryDetail", consumes = "application/json")
    @Override
    public ResultDto<UserRefundDetailResp> userQueryDetail(@RequestBody UserQueryRefundDetailReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】商家获取售后详情", queryReq, func -> {
            UserBo user = JsonUtil.copy(queryReq.getUser(), UserBo.class);
            // 业务逻辑处理
            UserRefundDetailBo detailBo = orderRefundService.userQueryDetail(queryReq.getRefundId(), user);
            return JsonUtil.copy(detailBo, UserRefundDetailResp.class);
        });
    }

    @PostMapping(value = "/userQueryDetailExt", consumes = "application/json")
    @Override
    public ResultDto<UserRefundDetailExtResp> userQueryDetailExt(@RequestBody UserQueryRefundDetailReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】商家微信获取售后详情", queryReq, func -> {
            UserBo user = JsonUtil.copy(queryReq.getUser(), UserBo.class);
            // 业务逻辑处理
            UserRefundDetailExtResp detailBo = orderRefundService.userQueryDetailExt(queryReq.getRefundId(), user);
            return detailBo;
        });
    }

    @PostMapping(value = "/sellerQueryDetail", consumes = "application/json")
    @Override
    public ResultDto<SellerRefundDetailResp> sellerQueryDetail(@RequestBody SellerQueryRefundDetailReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】供应商获取售后详情", queryReq, func -> {
            ShopBo shop = JsonUtil.copy(queryReq.getShop(), ShopBo.class);
            // 业务逻辑处理
            SellerRefundDetailBo detailBo = orderRefundService.sellerQueryDetail(queryReq.getRefundId(), shop);
            return JsonUtil.copy(detailBo, SellerRefundDetailResp.class);
        });
    }

    @PostMapping(value = "/platformQueryDetail", consumes = "application/json")
    @Override
    public ResultDto<PlatformRefundDetailResp> platformQueryDetail(@RequestBody PlatformQueryRefundDetailReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】平台获取售后详情", queryReq, func -> {
            // 业务逻辑处理
            PlatformRefundDetailBo detailBo = orderRefundService.platformQueryDetail(queryReq.getRefundId());
            return JsonUtil.copy(detailBo, PlatformRefundDetailResp.class);
        });
    }

    @PostMapping(value = "/queryRefundLog", consumes = "application/json")
    @Override
    public ResultDto<RefundLogListResp> queryRefundLog(@RequestBody BaseIdReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】获取售后操作日志", queryReq, func -> {
            // 业务逻辑处理
            RefundLogListBo logResult = orderRefundService.queryRefundLog(queryReq.getId());
            return JsonUtil.copy(logResult, RefundLogListResp.class);
        });
    }

    @PostMapping(value = "/userQueryRefundPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<UserRefundDto>> userQueryRefundPage(@RequestBody UserQueryRefundReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】商家分页查询售后列表", queryReq, func -> {
            UserQueryRefundBo queryBo = JsonUtil.copy(queryReq, UserQueryRefundBo.class, IGNORE_PROPERTIES);
            // 枚举转换
            queryBo.setRefundStatus(RefundStatusEnum.valueOf(queryReq.getRefundStatus()));
            // 业务逻辑处理
            BasePageResp<UserRefundBo> pageResp = orderRefundSearchService.userQueryPage(queryBo);
            return JsonUtil.copy(pageResp, new TypeReference<BasePageResp<UserRefundDto>>() {
            });
        });
    }

    @PostMapping(value = "/sellerQueryRefundPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<SellerRefundDto>> sellerQueryRefundPage(@RequestBody SellerQueryRefundReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】供应商分页查询售后列表", queryReq, func -> {
            SellerQueryRefundBo queryBo = JsonUtil.copy(queryReq, SellerQueryRefundBo.class);
            // 业务逻辑处理
            BasePageResp<SellerRefundBo> pageResp = orderRefundSearchService.sellerQueryPage(queryBo);
            return JsonUtil.copy(pageResp, new TypeReference<BasePageResp<SellerRefundDto>>() {
            });
        });
    }

    @PostMapping(value = "/platformQueryRefundPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<PlatformRefundDto>> platformQueryRefundPage(@RequestBody PlatformQueryRefundReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】平台分页查询售后列表", queryReq, func -> {
            PlatformQueryRefundBo queryBo = JsonUtil.copy(queryReq, PlatformQueryRefundBo.class, IGNORE_PROPERTIES);

            // 枚举转换
            queryBo.setRefundStatus(RefundStatusEnum.valueOf(queryReq.getRefundStatus()));
            // 业务逻辑处理
            BasePageResp<PlatformRefundBo> pageResp = orderRefundSearchService.platformQueryPage(queryBo);
            return JsonUtil.copy(pageResp, new TypeReference<BasePageResp<PlatformRefundDto>>() {
            });
        });
    }

    @PostMapping(value = "/queryUserDeliverExpress", consumes = "application/json")
    @Override
    public ResultDto<RefundUserDeliverExpressResp> queryUserDeliverExpress(@RequestBody BaseIdReq queryReq) throws TException {
        return ThriftResponseHelper.responseInvoke("【售后】查询买家寄货物流信息", queryReq, func -> {
            // 业务逻辑处理
            UserDeliveryBo deliveryBo = orderRefundService.getUserDelivery(queryReq.getId());
            return JsonUtil.copy(deliveryBo, RefundUserDeliverExpressResp.class);
        });
    }

    @PostMapping(value = "/queryErpRefundList", consumes = "application/json")
    @Override
    public ResultDto<List<PlatformRefundDetailResp>> queryErpRefundList(@RequestBody ErpQueryRefundReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【ERP】获取售后列表", req, func -> {
            // 业务逻辑处理
            if (req.getRefundId() != null) {
                return Optional.ofNullable(orderRefundService.queryRefundDetailByRefundId(req.getRefundId()))
                        .map(refundDetailBo -> Collections.singletonList(JsonUtil.copy(refundDetailBo, PlatformRefundDetailResp.class)))
                        .orElse(Collections.emptyList());
            }
            if (StrUtil.isNotBlank(req.getSourceRefundId())) {
                return Optional.ofNullable(orderRefundService.queryRefundDetailBySourceRefundId(req.getSourceRefundId()))
                        .map(refundDetailBo -> JsonUtil.copyList(refundDetailBo, PlatformRefundDetailResp.class))
                        .orElse(Collections.emptyList());
            }
            List<RefundDetailBo> detailBos = orderRefundService.queryRefundListByOrderIds(req.getOrderIds());
            return JsonUtil.copyList(detailBos, PlatformRefundDetailResp.class);
        });
    }

    @PostMapping(value = "/queryErpRefundPage", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<PlatformRefundDetailResp>> queryErpRefundPage(@RequestBody ErpQueryRefundPageReq req) throws TException {
        req.checkParameter();
        log.info("【ERP】获取售后分页列表, 请求参数={}", req);
        QueryErpRefundBo bo = new QueryErpRefundBo();
        bo.setShopId(req.getShopId());
        bo.setAuditStatus(req.getAuditStatus());
        bo.setRefundModes(req.getRefundModes());
        bo.setTimeType(req.getTimeType());
        bo.setStartTime(req.getStartTime());
        bo.setEndTime(req.getEndTime());
        bo.setUserId(req.getUserId());
        bo.setOrderId(req.getOrderId());
        return ThriftResponseHelper.responseInvoke("queryErpRefundPage", req, func -> {
            // 业务逻辑处理
            BasePageResp<PlatformRefundDetailBo> page = orderRefundService.queryErpRefundPage(req.buildPage(), bo);
            return JsonUtil.copy(page, new TypeReference<BasePageResp<PlatformRefundDetailResp>>() {
            });
        });
    }

}
