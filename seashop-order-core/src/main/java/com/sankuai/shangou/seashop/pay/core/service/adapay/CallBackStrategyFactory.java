package com.sankuai.shangou.seashop.pay.core.service.adapay;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description:
 */
@Component
public class CallBackStrategyFactory {

    @Autowired
    private Map<String, CallBackStrategyService> callBackStrategyServices;

    public CallBackStrategyService getCallBackStrategyService(String type) {
        return callBackStrategyServices.get(type);
    }

}
