package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RefundBo {

    /**
     * 退款ID，退款表主键ID
     */
    private Long refundId;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 订单项ID
     */
    private Long orderItemId;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 买家ID
     */
    private Long userId;
    /**
     * 买家账号
     */
    private String userName;
    /**
     * 售后类型。1：仅退款；2：退货退款
     */
    private Integer refundType;
    /**
     * 订单实付金额
     */
    private BigDecimal orderPayAmount;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 退款数量
     */
    private Long refundQuantity;
    /**
     * 申请日期
     */
    private Date applyDate;
    /**
     * 退款状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；5：待平台确认；6：退款成功；7：平台驳回；8：退款中；9：买家取消
     */
    private Integer refundStatus;
    /**
     * 退款状态描述
     */
    private String refundStatusDesc;
    /**
     * 退款商品明细列表
     */
    private List<RefundItemBo> itemList;
    /**
     * 是否订单全部退.店铺后台和平台后台需要根据这个字段区分显示商品信息。如果是true，显示 【订单所有商品】，否则从itemList取第一个商品显示
     */
    private Boolean hasAllReturn;

    /**
     * 退款原因
     */
    private String reason;
    /**
     * 联系人
     */
    private String contactPerson;
    /**
     * 联系电话
     */
    private String contactCellPhone;
    /**
     * 供应商备注
     */
    private String sellerRemark;
    /**
     * 退款方式。1：原路退回
     */
    private Integer refundPayType;
    /**
     * 退款方式描述
     */
    private String refundPayTypeDesc;
    /**
     * 退款原因详情
     */
    private String reasonDetail;
    /**
     * 平台备注
     */
    private String managerRemark;
    /**
     * 平台确认退款时间
     */
    private Date managerConfirmDate;
    /**
     * 凭证图片1
     */
    private String certPic1;
    /**
     * 凭证图片2
     */
    private String certPic2;

    /**
     * 凭证图片3
     */
    private String certPic3;
    /**
     * 联系人姓名
     */
    private String contactUserName;
    /**
     * 联系人电话
     */
    private String contactUserPhone;
    private Integer refundMode;

}
