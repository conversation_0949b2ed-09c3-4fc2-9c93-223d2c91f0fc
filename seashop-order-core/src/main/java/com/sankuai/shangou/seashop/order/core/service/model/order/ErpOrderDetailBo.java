package com.sankuai.shangou.seashop.order.core.service.model.order;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ErpOrderDetailBo {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 牵牛花订单号
     */
    private String sourceOrderId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 下单时间
     */
    private Date orderDate;
    /**
     * 商品总金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 订单实付金额
     */
    private BigDecimal totalAmount;
    /**
     * 订单实收金额
     */
    private BigDecimal actualPayAmount;
    /**
     * 订单状态
     */
    private OrderStatusEnum orderStatus;
    /**
     * 订单商品列表
     */
    private List<OrderItemInfoBo> itemList;

    /**
     * 预计完成时间。待发货状态才有，显示自动确认收货时间
     */
    private String estimateCompleteTime;
    /**
     * 优惠券金额
     */
    private BigDecimal couponAmount;
    /**
     * 运费金额
     */
    private BigDecimal freightAmount;
    /**
     * 改运费时备份的运费
     */
    private BigDecimal backupFreight;
    /**
     * 运费修改描述
     */
    private String freightUpdateDesc;
    /**
     * 税费金额
     */
    private BigDecimal taxAmount;
    /**
     * 买家留言
     */
    private String userRemark;
    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;
    /**
     * 满减金额
     */
    private BigDecimal reductionAmount;
    /**
     * 卖家备注
     */
    private String sellerRemark;
    /**
     * 支付渠道
     */
    private String payChannelName;
    /**
     * 付款日期
     */
    private Date payDate;
    /**
     * 发货日期
     */
    private Date shippingDate;
    /**
     * 完成日期
     */
    private Date finishDate;
    /**
     * 是否有售后
     */
    private Boolean hasRefund;
    /**
     * 售后ID
     */
    private Long refundId;
    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 地址
     */
    private ErpOrderAddressBo orderAddress;

    /**
     * 订单物流信息
     */
    private List<OrderExpressBo> expressList;

    /**
     * 订单发票信息
     */
    private OrderInvoiceBo orderInvoice;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 支付类型
     */
    private Integer paymentType;
    /**
     * 订单来源
     */
    //private Integer orderSource;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 卖家备注标记。一次1-4，4面小旗
     */
    private Integer sellerRemarkFlag;
    /**
     * 支付ID
     */
    private String gatewayOrderId;
    /**
     * 关闭原因
     */
    private String closeReason;
}
