package com.sankuai.shangou.seashop.order.core.service.model.refund;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SellerConfirmReceiveParamBo extends BaseParamReq {

    /**
     * 店铺信息
     */
    private ShopBo shop;
    /**
     * 用户信息
     */
    private UserBo user;
    /**
     * 退款单号
     */
    @PrimaryField
    private Long refundId;
    /**
     * 确认收货数量
     */
    @ExaminField(description = "确认收货数量")
    private Long confirmQuantity;

}
