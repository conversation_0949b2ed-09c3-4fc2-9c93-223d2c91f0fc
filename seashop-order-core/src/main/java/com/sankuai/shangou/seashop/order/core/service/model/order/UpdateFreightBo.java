package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class UpdateFreightBo extends BaseParamReq {

    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 订单ID
     */
    @PrimaryField
    private String orderId;
    /**
     * 修改后的价格
     */
    @ExaminField(description = "运费调整")
    private BigDecimal updatedFreight;

}
