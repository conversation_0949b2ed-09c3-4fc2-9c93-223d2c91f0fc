package com.sankuai.shangou.seashop.order.core.service.assit.refund;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.RefundEventEnum;

/**
 * <AUTHOR>
 */
@Service
public class OrderRefundMessageAssist {

    @Resource
    private RefundMessageHandlerFactory refundMessageHandlerFactory;

    public void handleRefundMessage(OrderRefundMessage message) {
        RefundMessageHandler handler = refundMessageHandlerFactory.getHandler(RefundEventEnum.valueOf(message.getRefundEventName()));
        if (handler == null) {
            return;
        }
        handler.handle(message);
    }

}
