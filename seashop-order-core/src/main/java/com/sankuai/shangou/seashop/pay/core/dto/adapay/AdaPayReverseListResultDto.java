package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/30/030
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class AdaPayReverseListResultDto {

    @JsonProperty("prod_mode")
    private boolean prodMode;
    @JsonProperty("page_index")
    private int pageIndex;
    @JsonProperty("has_more")
    private boolean hasMore;
    @JsonProperty("app_id")
    private String appId;
    @JsonProperty("object")
    private String object;
    @JsonProperty("status")
    private String status;
    @JsonProperty("page_size")
    private int pageSize;
    @JsonProperty("payment_reverses")
    private List<AdaPaymentReverseDto> paymentReverses;
}
