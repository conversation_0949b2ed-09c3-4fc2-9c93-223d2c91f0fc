package com.sankuai.shangou.seashop.order.core.task;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.StrUtil;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.config.TaskProps;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.constant.ThreadPoolConst;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundSearchService;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonOrderQueryParamBo;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonRefundQueryParamPo;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;

/**
 * 订单和售后ES构建相关任务，ES构建首先是监听数据库的binlog生成
 * <pre>
 *     此任务的作用
 *     1. 系统上线时，第一次初始化历史数据到ES，后续可能的话增量初始化，手动执行，不配置循环执行
 *     2. 基于更新时间查询昨日变更的数据，补偿同步一次到ES，防止binlog处理异常导致的数据不一致
 * </pre>
 * <AUTHOR>
 */
@Slf4j
@Component
public class OrderAndRefundEsDataBuildTask {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private EsOrderService esOrderService;
    @Resource
    private TaskProps taskProps;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRefundSearchService orderRefundSearchService;

    /**
     * 构建订单ES数据，正常数据通过binlog构建
     * 入参是 json 字符串
     * <AUTHOR>
     * @param taskParam 执行参数
     */
    @XxlJob("BuildOrderEsDataTask")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "构建订单ES数据--手动执行")
    public void buildOrderEsDataTask(EsInitParam taskParam) {
        log.info("【定时任务】【构建订单ES数据】...start...taskParam={}", JsonUtil.toJsonString(taskParam));
        // 构建订单查询参数，或者取默认参数进行补偿矫正
        CommonOrderQueryParamBo param = buildOrderQueryParamOrDefault(taskParam);
        log.info("【定时任务】【构建订单ES数据】查询参数为：{}", JsonUtil.toJsonString(param));
        List<Order> orderList = orderRepository.getByCondition(param);
        log.info("【定时任务】【构建订单ES数据】, size={}", orderList.size());
        CountDownLatch countDownLatch = new CountDownLatch(orderList.size());
        int i = 1;
        StopWatch stopWatch = StopWatch.create("【定时任务】【构建订单ES数据】");
        stopWatch.start();
        for (Order order : orderList) {
            log.info("【定时任务】【构建订单ES数据】， orderId={}， 第 {} 条", order.getOrderId(), i++);
            ES_BUILD_ORDER_POOL.submit(() -> {
                try {
                    esOrderService.buildEsOrder(order.getOrderId());
                } catch (Exception e) {
                    log.error("【定时任务】【构建订单ES数据】, orderId={}， 异常", order.getOrderId(), e);
                }
                countDownLatch.countDown();
            });
        }
        try {
            countDownLatch.await();
            stopWatch.stop();
            log.info("【定时任务】【构建订单ES数据】, 构建数据量={}条, cost={}s", i - 1, stopWatch.prettyPrint(TimeUnit.SECONDS));
        } catch (Exception e) {
            log.error("【定时任务】【构建订单ES数据】结束异常", e);
        }
    }

    /**
     * 构建售后ES数据，正常数据通过binlog构建
     * 入参是 json 字符串
     * <AUTHOR>
     * @param taskParam 执行参数
     */
    @XxlJob("BuildRefundEsDataTask")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "构建售后ES数据--手动执行")
    public void buildRefundEsDataTask(EsInitParam taskParam) {
        log.info("【定时任务】【构建售后ES数据】...start...taskParam={}", JsonUtil.toJsonString(taskParam));
        // 构建订单查询参数，或者取默认参数进行补偿矫正
        CommonRefundQueryParamPo param = buildRefundQueryParamOrDefault(taskParam);
        log.info("【定时任务】【构建售后ES数据】查询参数为：{}", JsonUtil.toJsonString(param));
        List<OrderRefund> refundList = orderRefundRepository.getByCondition(param);
        log.info("【定时任务】【构建售后ES数据】, size={}", refundList.size());
        CountDownLatch countDownLatch = new CountDownLatch(refundList.size());
        int i = 1;
        StopWatch stopWatch = StopWatch.create("【定时任务】【构建售后ES数据】");
        stopWatch.start();
        for (OrderRefund refund : refundList) {
            log.info("【定时任务】【构建售后ES数据】， refundId={}， 第 {} 条", refund.getId(), i++);
            ES_BUILD_REFUND_POOL.submit(() -> {
                try {
                    orderRefundSearchService.buildEsRefund(refund.getId());
                } catch (Exception e) {
                    log.error("【定时任务】【构建售后ES数据】, refundId={}， 异常", refund.getId(), e);
                }
                countDownLatch.countDown();
            });
        }
        try {
            countDownLatch.await();
            stopWatch.stop();
            log.info("【定时任务】【构建售后ES数据】, 构建数据量={}条, cost={}s", i - 1, stopWatch.prettyPrint(TimeUnit.SECONDS));
        } catch (Exception e) {
            log.error("【定时任务】【构建售后ES数据】结束异常", e);
        }
    }

    private static final ThreadPoolExecutor ES_BUILD_ORDER_POOL = getOrderThreadPool();
    private static final ThreadPoolExecutor ES_BUILD_REFUND_POOL = getRefundThreadPool();
    /**
     * 获取线程池，默认设置为第一次初始化时的配置，后续依赖管理平台修改相关参数
     * <AUTHOR>
     */
    private static ThreadPoolExecutor getOrderThreadPool() {
        return new ThreadPoolExecutor(10, 20, 10, TimeUnit.MINUTES, new LinkedBlockingQueue<>(500000),
                new ThreadFactoryBuilder().setNameFormat("es-build-order-").build());
        //DefaultThreadPoolProperties.Setter setter = DefaultThreadPoolProperties.Setter()
        //        .withCoreSize(10)
        //        .withMaxSize(20)
        //        .withKeepAliveTimeMinutes(10)
        //        .withKeepAliveTimeUnit(TimeUnit.SECONDS)
        //        .withBlockingQueue(new ArrayBlockingQueue<>(500000))
        //        .withMaxQueueSize(500000)
        //        .withThreadFactory(new ThreadFactoryBuilder().setNameFormat("es-build-order-").build());
        //return Rhino.newThreadPool(ThreadPoolConst.RHINO_KEY_THREAD_POOL_ES_BUILD_ORDER, setter);
    }
    private static ThreadPoolExecutor getRefundThreadPool() {
        return new ThreadPoolExecutor(10, 20, 10, TimeUnit.MINUTES, new LinkedBlockingQueue<>(100000),
                new ThreadFactoryBuilder().setNameFormat("es-build-refund-").build());
        //DefaultThreadPoolProperties.Setter setter = DefaultThreadPoolProperties.Setter()
        //        .withCoreSize(10)
        //        .withMaxSize(20)
        //        .withKeepAliveTimeMinutes(10)
        //        .withKeepAliveTimeUnit(TimeUnit.SECONDS)
        //        .withBlockingQueue(new ArrayBlockingQueue<>(100000))
        //        .withMaxQueueSize(100000)
        //        .withThreadFactory(new ThreadFactoryBuilder().setNameFormat("es-build-refund-").build());
        //return Rhino.newThreadPool(ThreadPoolConst.RHINO_KEY_THREAD_POOL_ES_BUILD_REFUND, setter);
    }



    /**
     * 构建订单查询参数，如果为空取默认值为更新时间为当前时间前 20-10分钟，认为任务每10分钟执行一次，
     * 这个时间可以通过 seashop.order.task.esBuildEndBeforeMinute 和 seashop.order.task.esBuildGapMinute 调整，但需要同步调整定时任务执行间隔
     * <AUTHOR>
     * @param taskParam 执行参数
     */
    private CommonOrderQueryParamBo buildOrderQueryParamOrDefault(EsInitParam taskParam) {
        CommonOrderQueryParamBo.CommonOrderQueryParamBoBuilder builder = CommonOrderQueryParamBo.builder();
        if (taskParam != null && Boolean.TRUE.equals(taskParam.getInitAll())) {
            return builder.build();
        }

        // 如果任务参数为空，则默认，
        if (taskParam != null && taskParam.hasValue()) {
            if (taskParam.getMinId() != null) {
                builder.idGe(taskParam.getMinId());
            }
            if (taskParam.getMaxId() != null) {
                builder.idLe(taskParam.getMaxId());
            }
            if (StrUtil.isNotBlank(taskParam.getMinUpdateTime())) {
                builder.updateTimeGe(DateUtil.parse(taskParam.getMinUpdateTime(), CommonConst.DATE_TIME_PATTERN_DEFAULT));
            }
            if (StrUtil.isNotBlank(taskParam.getMaxUpdateTime())) {
                builder.updateTimeLe(DateUtil.parse(taskParam.getMaxUpdateTime(), CommonConst.DATE_TIME_PATTERN_DEFAULT));
            }
        } else {
            // 默认值
            Date now = new Date();
            Date endTime = DateUtil.offsetMinute(now, -getEndBeforeMinute());
            Date startTime = DateUtil.offsetMinute(endTime, -getGapMinute());
            builder.updateTimeGe(startTime);
            builder.updateTimeLe(endTime);
        }
        return builder.build();
    }

    /**
     * 构建订单查询参数，如果为空取默认值为更新时间为当前时间前 20-10分钟，认为任务每10分钟执行一次，
     * 这个时间可以通过 seashop.order.task.esBuildEndBeforeMinute 和 seashop.order.task.esBuildGapMinute 调整，但需要同步调整定时任务执行间隔
     * <AUTHOR>
     * @param taskParam 执行参数
     */
    private CommonRefundQueryParamPo buildRefundQueryParamOrDefault(EsInitParam taskParam) {
        CommonRefundQueryParamPo.CommonRefundQueryParamPoBuilder builder = CommonRefundQueryParamPo.builder();
        if (taskParam != null && Boolean.TRUE.equals(taskParam.getInitAll())) {
            return builder.build();
        }
        // 如果任务参数为空，则默认查询前一天的修改数据构建ES，
        if (taskParam != null && taskParam.hasValue()) {
            if (taskParam.getMinId() != null) {
                builder.idGe(taskParam.getMinId());
            }
            if (taskParam.getMaxId() != null) {
                builder.idLe(taskParam.getMaxId());
            }
            if (StrUtil.isNotBlank(taskParam.getMinUpdateTime())) {
                builder.updateTimeGe(DateUtil.parse(taskParam.getMinUpdateTime(), CommonConst.DATE_TIME_PATTERN_DEFAULT));
            }
            if (StrUtil.isNotBlank(taskParam.getMaxUpdateTime())) {
                builder.updateTimeLe(DateUtil.parse(taskParam.getMaxUpdateTime(), CommonConst.DATE_TIME_PATTERN_DEFAULT));
            }
        } else {
            // 默认值
            Date now = new Date();
            Date endTime = DateUtil.offsetMinute(now, -getEndBeforeMinute());
            Date startTime = DateUtil.offsetMinute(endTime, -getGapMinute());
            builder.updateTimeGe(startTime);
            builder.updateTimeLe(endTime);
        }
        return builder.build();
    }
    
    private int getEndBeforeMinute() {
        if (taskProps.getEsBuildEndBeforeMinute() == null) {
            return 10;
        }
        return taskProps.getEsBuildEndBeforeMinute();
    }

    private int getGapMinute() {
        if (taskProps.getEsBuildGapMinute() == null) {
            return 10;
        }
        return taskProps.getEsBuildGapMinute();
    }


    static class EsInitParam {
        private Long minId;
        private Long maxId;
        private String minUpdateTime;
        private String maxUpdateTime;
        private Boolean initAll;


        public Long getMinId() {
            return minId;
        }

        public void setMinId(Long minId) {
            this.minId = minId;
        }

        public Long getMaxId() {
            return maxId;
        }

        public void setMaxId(Long maxId) {
            this.maxId = maxId;
        }

        public String getMinUpdateTime() {
            return minUpdateTime;
        }

        public void setMinUpdateTime(String minUpdateTime) {
            this.minUpdateTime = minUpdateTime;
        }

        public String getMaxUpdateTime() {
            return maxUpdateTime;
        }

        public void setMaxUpdateTime(String maxUpdateTime) {
            this.maxUpdateTime = maxUpdateTime;
        }

        public Boolean getInitAll() {
            return initAll;
        }

        public void setInitAll(Boolean initAll) {
            this.initAll = initAll;
        }

        public boolean hasValue() {
            return minId != null || maxId != null || StrUtil.isNotBlank(minUpdateTime) || StrUtil.isNotBlank(maxUpdateTime);
        }
    }

}
