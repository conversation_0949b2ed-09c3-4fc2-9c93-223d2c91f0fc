package com.sankuai.shangou.seashop.order.core.service.assit;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonPayRecordQueryParamBo;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Finance;
import com.sankuai.shangou.seashop.order.dao.finance.domain.FinanceItem;
import com.sankuai.shangou.seashop.order.dao.finance.domain.PendingSettlementOrder;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceItemRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.PendingSettlementOrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 订单完成业务逻辑
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderFinishForFinanceBizAssist {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private PendingSettlementOrderRepository pendingSettlementOrderRepository;
    @Resource
    private FinanceRepository financeRepository;
    @Resource
    private FinanceItemRepository financeItemRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;

    public void finishOrder(Order order, Date finishDate) {
        log.info("【订单完成-财务数据更新】finishDate={}", finishDate);
        if (order == null) {
            return;
        }
        // 查询支付成功的记录，因为可能重复支付，支付记录表的数据可能不唯一，所以加上订单表上关联的支付单号查询
        OrderPayRecord payRecord = null;
        CommonPayRecordQueryParamBo param = CommonPayRecordQueryParamBo.builder()
                .payStatusEq(PayStatusEnum.PAY_SUCCESS.getCode())
                .orderIdListIn(Collections.singletonList(order.getOrderId()))
                .outTransIdList(Collections.singletonList(order.getGatewayOrderId()))
                .build();
        log.info("【订单完成-财务数据更新】查询支付成功的记录，param: {}", JsonUtil.toJsonString(param));
        List<OrderPayRecord> paySuccessList = orderPayRecordRepository.getByCondition(param);
        log.info("【订单完成-财务数据更新】查询支付成功的记录，paySuccessList: {}", JsonUtil.toJsonString(paySuccessList));
        if (CollUtil.isNotEmpty(paySuccessList)) {
            // 如果有多个支付成功的记录，理论上匹配的只会有一条记录
            payRecord = paySuccessList.get(0);
        }
        // 理论上明细一定不为空
        List<OrderItem> orderItemList = orderItemRepository.getByOrderId(order.getOrderId());
        this.finishOrder(order, finishDate, orderItemList, payRecord);
    }


    public void finishOrder(Order order, Date finishDate, List<OrderItem> orderItemList, OrderPayRecord payRecord) {
        TransactionHelper.doInTransaction(() -> {
            // 如果是不需要支付的订单，比如订单金额是0元，不需要支付，也就不需要结算
            if (payRecord != null) {
                // 更新待结算订单时间
                boolean flag = pendingSettlementOrderRepository.updateOrderFinishTime(order.getOrderId(), finishDate);
                log.info("【订单完成-财务数据更新】订单完成，更新待结算订单时间，订单ID: {}, 更新结果: {}", order.getOrderId(), flag);
                // 财务中间表的查询没有批量，是也需要放到乐观锁里面
                int financeCnt = financeRepository.countByOrderIdAndType(order.getOrderId(), TransactionTypesEnum.FINISH.getCode());
                log.info("【订单完成-财务数据更新】订单完成，查询财务中间表，订单ID: {}, 记录数: {}", order.getOrderId(), financeCnt);
                // 没有对应类型的才保存
                if (financeCnt == 0) {
                    // 写入财务中间表
                    buildAndSaveFinance(order, orderItemList, payRecord, finishDate);
                }
            }
        });
    }





    private void buildAndSaveFinance(Order order, List<OrderItem> orderItemList, OrderPayRecord payRecord, Date now) {
        Finance finance = new Finance();
        finance.setOrderId(order.getOrderId());
        String adaPayId = payRecord != null && StrUtil.isNotBlank(payRecord.getPayNo()) ? payRecord.getPayNo() : "";
        finance.setAdapayId(adaPayId);
        String payNo = payRecord != null && StrUtil.isNotBlank(payRecord.getPayNo()) ? payRecord.getPayNo() : "";
        finance.setPayId(payNo);
        finance.setType(TransactionTypesEnum.FINISH.getCode());
        Date finishDate = order.getFinishDate() != null ? order.getFinishDate() : now;
        finance.setCreateDate(finishDate);
        finance.setShopId(order.getShopId());
        finance.setShopName(order.getShopName());
        finance.setUserId(order.getUserId());
        finance.setUserName(order.getUserName());
        String gatewayOrderId = order.getGatewayOrderId() != null ? order.getGatewayOrderId() : "";
        finance.setTransactionId(gatewayOrderId);
        finance.setTotalAmount(order.getTotalAmount());
        finance.setFreight(order.getFreight());
        finance.setProductAmount(order.getProductTotalAmount());
        finance.setDiscountAmount(order.getCouponAmount());
        finance.setPlatDiscountAmount(BigDecimal.ZERO);
        finance.setFullDiscount(order.getDiscountAmount());
        finance.setMoneyOff(order.getMoneyOffAmount());
        finance.setIntegralDiscount(BigDecimal.ZERO);
        finance.setActualPayAmount(payRecord.getPayAmount());

        PendingSettlementOrder settlementOrder = pendingSettlementOrderRepository.getByOrderId(order.getOrderId());
        if (settlementOrder != null) {
            finance.setSettlementAmount(settlementOrder.getSettlementAmount());
            finance.setCommissionAmount(settlementOrder.getPlatCommission());
            finance.setServiceAmount(BigDecimal.ZERO);
        } else {
            finance.setSettlementAmount(BigDecimal.ZERO);
            finance.setServiceAmount(BigDecimal.ZERO);
            finance.setCommissionAmount(BigDecimal.ZERO);
        }
        log.info("【订单完成-财务数据更新】订单完成，写入财务中间表，finance: {}", JsonUtil.toJsonString(finance));
        financeRepository.save(finance);

        List<FinanceItem> financeItemList = orderItemList.stream()
                .map(item -> {
                    FinanceItem financeItem = new FinanceItem();
                    financeItem.setFinanceId(finance.getId());
                    financeItem.setOrderId(item.getOrderId());
                    financeItem.setProductId(item.getProductId());
                    financeItem.setProductName(item.getProductName());
                    financeItem.setSku(item.getSku());
                    financeItem.setQuantity(item.getQuantity());
                    financeItem.setOriginalPrice(item.getCostPrice());
                    financeItem.setSalePrice(item.getSalePrice());
                    financeItem.setTotalAmount(NumberUtil.mul(item.getSalePrice(), item.getQuantity()));
                    financeItem.setDiscountPrice(item.getDiscountAmount());
                    financeItem.setFullDiscount(item.getFullDiscount());
                    financeItem.setMoneyOff(item.getMoneyOff());
                    financeItem.setCouponDiscount(item.getCouponDiscount());
                    financeItem.setPlatCouponDiscount(BigDecimal.ZERO);
                    financeItem.setCommisRate(item.getCommisRate());
                    return financeItem;
                })
                .collect(Collectors.toList());
        log.info("【订单完成-财务数据更新】订单完成，写入财务中间表，financeItemList: {}", JsonUtil.toJsonString(financeItemList));
        financeItemRepository.saveBatch(financeItemList);
    }

}
