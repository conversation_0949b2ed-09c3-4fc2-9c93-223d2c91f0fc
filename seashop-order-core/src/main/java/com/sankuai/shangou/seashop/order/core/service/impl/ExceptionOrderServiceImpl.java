package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.dto.ChangeFiled;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.remote.PayRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.pay.CreateRefundBo;
import com.sankuai.shangou.seashop.order.core.service.ExceptionOrderService;
import com.sankuai.shangou.seashop.order.core.service.assit.opLog.ChangeFieldDesc;
import com.sankuai.shangou.seashop.order.core.service.assit.opLog.OperationLogAssist;
import com.sankuai.shangou.seashop.order.core.service.model.order.ExceptionOrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryExceptionOrderParamBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.ExceptionOrder;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.po.QueryExceptionOrderPo;
import com.sankuai.shangou.seashop.order.dao.core.repository.ExceptionOrderRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.ExceptionOrderRefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.ExceptionOrderTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.ConfirmExceptionOrderReq;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessStatusTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ExceptionOrderServiceImpl implements ExceptionOrderService {

    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private ExceptionOrderRepository exceptionOrderRepository;
    @Resource
    private PayRemoteService payRemoteService;
    @Resource
    private OperationLogAssist operationLogAssist;


    @Override
    public BasePageResp<ExceptionOrderInfoBo> pageList(BasePageParam pageParam, QueryExceptionOrderParamBo paramBo) {

        Page<ExceptionOrder> pageResult = PageHelper.startPage(pageParam);

        QueryExceptionOrderPo param = new QueryExceptionOrderPo();
        param.setOrderId(paramBo.getOrderId());
        param.setRefundStatus(paramBo.getRefundStatus());
        exceptionOrderRepository.getList(param);

        return PageResultHelper.transfer(pageResult, ExceptionOrderInfoBo.class, (db, bo) -> {
            bo.setErrTypeDesc(ExceptionOrderTypeEnum.getDesc(bo.getErrorType()));
            bo.setRefundStatusDesc(ExceptionOrderRefundStatusEnum.getDesc(bo.getRefundStatus()));
        });
    }

    @Override
    public void confirmRefund(ConfirmExceptionOrderReq confirmReq) {
        Long exceptionOrderId = confirmReq.getExceptionOrderId();
        UserDto user = confirmReq.getUser();
        // 这个操作是由平台操作的，基本不会出现并发，所以没加锁
        ExceptionOrder exceptionOrder = exceptionOrderRepository.getById(exceptionOrderId);
        AssertUtil.throwIfNull(exceptionOrder, "异常订单不存在");
        AssertUtil.throwIfTrue(!exceptionOrder.getRefundStatus().equals(ExceptionOrderRefundStatusEnum.WAIT_REFUND.getCode()),
                "异常订单已经退款");
        long days = DateUtil.between(new Date(), exceptionOrder.getPayTime(), DateUnit.DAY) + 1;
        AssertUtil.throwIfTrue(days > 178, "订单付款时间超过178天，无法退款");

        // 规则与.net保持一致
        String batchNo = "EOPR" + DateUtil.format(new Date(), "yyyyMMddhhmmss");
        TransactionHelper.doInTransaction(() -> {
            ExceptionOrder updateOrder = new ExceptionOrder();
            updateOrder.setId(exceptionOrder.getId());
            updateOrder.setRefundStatus(ExceptionOrderRefundStatusEnum.REFUNDING.getCode());
            updateOrder.setRefundTime(new Date());
            updateOrder.setRefundBatchNo(batchNo);
            updateOrder.setRefundManager(user.getUserName());
            exceptionOrderRepository.updateById(exceptionOrder.getId(), updateOrder);

            try {
                updateOrder.setOrderId(exceptionOrder.getOrderId());
                List<ChangeFiled> orderChanged = operationLogAssist.buildChangeList(exceptionOrder, updateOrder, ChangeFieldDesc.EXCEPTION_ORDER_PLATFORM_CONFIRM);
                operationLogAssist.log(ExaminModelEnum.ORDER, ExaProEnum.MODIFY, "异常订单确认退款", JsonUtil.toJsonString(orderChanged), confirmReq, user.getUserName());
            } catch (Exception e) {
                log.error("【异常订单】确认退款, 记录接口操作日志失败", e);
            }
        });

        // 调用支付撤销接口，等待回调修改
        // 这里要根据异常订单保存时关联的支付记录的批次号查询，否则可能与正常支付的订单冲突
        OrderPayRecord orderPayRecord = orderPayRecordRepository.getBydOrderIdAndBatchNoAndStatus(exceptionOrder.getOrderId(), exceptionOrder.getBatchNo());
        log.info("【异常订单】确认退款, 对应的支付记录为: {}", JsonUtil.toJsonString(orderPayRecord));
        // 调用支付接口发起退款
        CreateRefundBo createRefundBo = new CreateRefundBo();
        createRefundBo.setRefundNo(batchNo);
        createRefundBo.setOriginPayNo(orderPayRecord.getBatchNo());
        createRefundBo.setRefundAmount(exceptionOrder.getPayAmount());
        createRefundBo.setBusinessStatusType(BusinessStatusTypeEnum.ABNORMAL.getType());
        payRemoteService.createRefund(createRefundBo);
    }

}
