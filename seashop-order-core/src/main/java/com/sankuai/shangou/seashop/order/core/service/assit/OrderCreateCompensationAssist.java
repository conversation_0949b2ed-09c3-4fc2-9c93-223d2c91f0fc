package com.sankuai.shangou.seashop.order.core.service.assit;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.StockRemoteService;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderCreateCompensationAssist {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private StockRemoteService stockRemoteService;

    /**
     * 检查订单是否创建成功，如果没有创建成功，执行回滚操作
     * <p>当前的业务逻辑中，会先执行库存、营销的处理 {@link com.sankuai.shangou.seashop.order.core.service.impl.OrderServiceImpl#createOrder(com.sankuai.shangou.seashop.order.core.service.model.order.CreateOrderBo)
     * 成功后，才会本地保存订单相关数据，所以，理论上如果能查到订单，说明订单已经创建成功了。否则就需要回滚业务数据了</p>
     * <p>如果这里回滚失败了，后续还有一个延迟消息，回再次检查，然后处理回滚</p>
     * <AUTHOR>
     * @param orderIdList 当前可能创建订单的订单号，创建订单逻辑开始就先生成了
     * void
     */
    public void checkAndRollback(Long userId, List<String> orderIdList) {
        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
        // 这里只判断是否有订单，没有校验数量是否一致，是因为目前的逻辑订单数据是批量保存的，且在同一个本地事务，所以只要有就代表都保存成功了
        if (CollectionUtils.isNotEmpty(orderList)) {
            log.warn("【订单创建】订单已经创建成功，不需要回滚, orderIdList: {}", JsonUtil.toJsonString(orderIdList));
            return;
        }
        if (userId == null) {
            log.warn("【订单创建】用户id为空，无法回滚, orderIdList: {}", JsonUtil.toJsonString(orderIdList));
            return;
        }
        // 回滚库存
        stockRemoteService.rollbackStock(orderIdList, StockUpdateTypeEnum.CREATE_ORDER);
        // 回滚优惠券
        promotionRemoteService.rollbackCoupon(userId, orderIdList);
        // 回滚限时购库存，订单不一定有限时购库存，但此时这里也判断不出，因为有可能订单完全没有保存，只有orderId，所以直接调用接口
        // 限时购只有一个订单
        promotionRemoteService.rollbackFlashSaleStock(orderIdList.get(0));
    }

}
