package com.sankuai.shangou.seashop.pay.core.assist.pay.wechat;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

import com.github.binarywang.wxpay.bean.notify.OriginNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.SignatureHeader;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Response;
import com.github.binarywang.wxpay.bean.notify.WxPayOrderNotifyV3Result;
import com.github.binarywang.wxpay.bean.notify.WxPayRefundNotifyV3Result;
import com.github.binarywang.wxpay.bean.request.WxPayRefundV3Request;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderV3Request;
import com.github.binarywang.wxpay.bean.result.WxPayOrderQueryV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayRefundQueryV3Result;
import com.github.binarywang.wxpay.bean.result.WxPayRefundV3Result;
import com.github.binarywang.wxpay.bean.result.enums.TradeTypeEnum;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.mode.AbstractWxPayModeProxy;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.PayPaymentCreateExpendDto;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/09/02 9:33
 */
@Slf4j
public class WxPayNormalHandler extends AbstractWxPayModeProxy {

    @Override
    public PayResult pay(PayParam payParam) {
        log.info("【发起支付-微信支付-普通模式】调用微信支付请求参数{}", payParam);
        PayResult payResult = new PayResult();

        // 创建支付参数
        WxPayUnifiedOrderV3Request request = new WxPayUnifiedOrderV3Request();
        request.setAppid(appId);
        request.setMchid(payConfig.getMchId());
        request.setOutTradeNo(String.valueOf(payParam.getPayId()));
        request.setDescription(payParam.getGoodsTitle());
        request.setNotifyUrl(payConfig.getCallBackUrl());

        // 设置支付金额
        WxPayUnifiedOrderV3Request.Amount amount = new WxPayUnifiedOrderV3Request.Amount();
        amount.setTotal(payParam.getPayAmount().multiply(new BigDecimal(100)).intValue());
        request.setAmount(amount);

        // 设置支付人
        Optional<PayPaymentCreateExpendDto> expend = Optional.ofNullable(payParam.getExpend());
        WxPayUnifiedOrderV3Request.Payer payer = new WxPayUnifiedOrderV3Request.Payer();
        payer.setOpenid(expend.map(PayPaymentCreateExpendDto::getOpenId).orElse(null));
        if(!PaymentTypeEnum.WECHAT_H5.equals(paymentType)) {
            request.setPayer(payer);
        }

        if (PaymentTypeEnum.WECHAT_H5.equals(paymentType)) {
            // 场景 h5必填
            WxPayUnifiedOrderV3Request.SceneInfo sceneInfo = new WxPayUnifiedOrderV3Request.SceneInfo();
            sceneInfo.setPayerClientIp(expend.map(PayPaymentCreateExpendDto::getClientIp).orElse("127.0.0.1"));
            WxPayUnifiedOrderV3Request.H5Info h5Info = new WxPayUnifiedOrderV3Request.H5Info();
            h5Info.setType("Wap");
            sceneInfo.setH5Info(h5Info);
            request.setSceneInfo(sceneInfo);
        }

        try {
            log.info("【发起支付-微信支付-普通模式】调用微信支付请求参数{}", JsonUtil.toJsonString(request));
            Object resp = wxPayService.createOrderV3(getTradeType(), request);

            payResult.setRemoteReq(request);
            payResult.setRemoteResp(resp);
            payResult.setSuccess(true);
        }
        catch (Exception e) {
            log.error("【发起支付-微信支付-普通模式】调用微信支付失败", e);
            payResult.setErrorMsg(e.getMessage());
            payResult.setSuccess(false);
        }
        return payResult;
    }

    @Override
    public PayNotifyResult notifyPay(String payData) {
        try {
            // 解密回调参数
            OriginNotifyResponse notifyResponse = JSONUtil.toBean(payData, OriginNotifyResponse.class);
            Map<String, String> header = wxPayUtil.getNotifyPaySignatureHeader();
            WxPayOrderNotifyV3Result wxPayRefundNotifyV3Result =
                    wxPayService.parseOrderNotifyV3Result(wxPayUtil.jsonStrSort(notifyResponse), JsonUtil.copy(header, SignatureHeader.class));
            WxPayOrderNotifyV3Result.DecryptNotifyResult result = wxPayRefundNotifyV3Result.getResult();
            log.info("【微信支付回调-普通模式】{}", JsonUtil.toJsonString(result));

            PayNotifyResult payNotifyResult = new PayNotifyResult();
            payNotifyResult.setChannelPayId(result.getTransactionId());
            payNotifyResult.setTradeState(result.getTradeState());
            payNotifyResult.setPayId(result.getOutTradeNo());
            payNotifyResult.setPayTime(wxPayUtil.parseWxDate(result.getSuccessTime()));
            payNotifyResult.setSuccess(wxPayUtil.transTradeState(result.getTradeState()));
            payNotifyResult.setThirdResult(WxPayNotifyV3Response.success("回调成功"));

            return payNotifyResult;
        }
        catch (Exception e) {
            log.error("【微信支付回调-普通模式】解析微信支付回调异常", e);
            throw new BusinessException("解析微信支付回调异常");
        }
    }

    @Override
    public PayConfirmResult payConfirm(PayConfirmParam confirmParam) {
        try {
            log.info("【发起支付-微信支付-普通模式】支付确认, param: {}", confirmParam);
            WxPayOrderQueryV3Result result = wxPayService.queryOrderV3(confirmParam.getChannelPayId(), confirmParam.getPayId());
            PayConfirmResult confirmResult = new PayConfirmResult();
            confirmResult.setPayId(result.getOutTradeNo());
            confirmResult.setChannelPayId(result.getTransactionId());
            confirmResult.setTradeState(result.getTradeState());
            confirmResult.setPayTime(wxPayUtil.parseWxDate(result.getSuccessTime()));
            confirmResult.setSuccess(wxPayUtil.transTradeState(confirmResult.getTradeState()));
            return confirmResult;
        }
        catch (Exception e) {
            log.error("【发起支付-微信支付-普通模式】支付确认失败", e);
            throw new BusinessException("支付确认失败");
        }
    }

    @Override
    public RefundResult refund(RefundParam refundParam) {
        log.info("【发起退款-微信支付-普通模式】, param： {}", JsonUtil.toJsonString(refundParam));

        RefundResult refundResult = new RefundResult();

        try {
            WxPayRefundV3Request request = new WxPayRefundV3Request();
            request.setTransactionId(refundParam.getChannelPayId());
            request.setOutRefundNo(refundParam.getReverseId());
            request.setNotifyUrl(payConfig.getRefundCallBackUrl());

            WxPayRefundV3Request.Amount amount = new WxPayRefundV3Request.Amount();
            amount.setRefund(refundParam.getReverseAmount().multiply(new BigDecimal(100)).intValue());
            amount.setTotal(refundParam.getOrderAmount().multiply(new BigDecimal(100)).intValue());
            amount.setCurrency("CNY");
            request.setAmount(amount);

            refundResult.setRemoteReq(request);

            WxPayRefundV3Result refunds = wxPayService.refundV3(request);
            refundResult.setChannelRefundId(refunds.getRefundId());
            refundResult.setRemoteResp(refunds);
            refundResult.setSuccess(true);
        }
        catch (Exception e) {
            log.error("【发起退款-微信支付-普通模式】 发起退款失败", e);
            refundResult.setSuccess(false);
            refundResult.setErrorMsg(e.getMessage());
        }

        return refundResult;
    }

    @Override
    public RefundConfirmResult refundConfirm(RefundConfirmParam refundConfirmParam) {
        log.info("【发起退款-微信支付-普通模式】退款确认, param: {}", refundConfirmParam);
        try {
            WxPayRefundQueryV3Result refundQueryResult = wxPayService.refundQueryV3(refundConfirmParam.getReverseId());
            log.info("【发起退款-微信支付-普通模式】退款确认结果: {}", JsonUtil.toJsonString(refundQueryResult));

            RefundConfirmResult refundConfirmResult = new RefundConfirmResult();
            refundConfirmResult.setChannelRefundId(refundQueryResult.getRefundId());
            refundConfirmResult.setReverseId(refundQueryResult.getOutRefundNo());
            refundConfirmResult.setRefundStatus(refundQueryResult.getStatus());
            refundConfirmResult.setRefundDate(wxPayUtil.parseWxDate(refundQueryResult.getSuccessTime()));
            refundConfirmResult.setSuccess(wxPayUtil.transRefundState(refundQueryResult.getStatus()));
            return refundConfirmResult;
        }
        catch (Exception e) {
            log.info("【发起退款-微信支付-普通模式】退款确认失败", e);
            throw new BusinessException("退款确认失败");
        }
    }

    @Override
    public RefundNotifyResult notifyRefund(String refundData) {
        try {
            // 解密回调参数
            OriginNotifyResponse notifyResponse = JSONUtil.toBean(refundData, OriginNotifyResponse.class);
            Map<String, String> header = wxPayUtil.getNotifyPaySignatureHeader();
            WxPayRefundNotifyV3Result wxRefundNotifyV3Result =
                    wxPayService.parseRefundNotifyV3Result(wxPayUtil.jsonStrSort(notifyResponse), JsonUtil.copy(header, SignatureHeader.class));
            WxPayRefundNotifyV3Result.DecryptNotifyResult result = wxRefundNotifyV3Result.getResult();
            log.info("【微信退款回调-普通模式】{}", JsonUtil.toJsonString(result));

            RefundNotifyResult refundNotifyResult = new RefundNotifyResult();
            refundNotifyResult.setChannelRefundId(result.getRefundId());
            refundNotifyResult.setReverseId(result.getOutRefundNo());
            refundNotifyResult.setRefundStatus(result.getRefundStatus());
            refundNotifyResult.setRefundDate(wxPayUtil.parseWxDate(result.getSuccessTime()));
            refundNotifyResult.setSuccess(wxPayUtil.transRefundState(result.getRefundStatus()));
            refundNotifyResult.setThirdResult(WxPayNotifyV3Response.success("回调成功"));

            return refundNotifyResult;
        }
        catch (Exception e) {
            log.error("【微信退款回调-普通模式】解析微信支付回调异常", e);
            throw new BusinessException("解析微信退款回调异常");
        }
    }

    private TradeTypeEnum getTradeType() {
        switch (paymentType) {
            case WECHAT_APPLET:
                return TradeTypeEnum.JSAPI;
            case WECHAT_H5:
                return TradeTypeEnum.H5;
            case WECHAT_NATIVE:
                return TradeTypeEnum.NATIVE;
            case WECHAT_JS:
                return TradeTypeEnum.JSAPI;
            default:
                throw new BusinessException("不支持的支付类型");
        }
    }

}
