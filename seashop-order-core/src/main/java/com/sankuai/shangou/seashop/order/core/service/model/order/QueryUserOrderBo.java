package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 商家查询自己的订单列表查询条件
 * <p>分业务端是为了防止底层业务逻辑过于通用导致不可预知的水平越权</p>
 * <AUTHOR>
 */
@Getter
@Setter
public class QueryUserOrderBo extends QueryOrderBaseBo {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 搜索关键字，支持 商品名称，订单编号，规格ID，商品ID
     */
    private String searchKey;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    private List<Integer> orderStatusList;

    private String orderId;
    private String productName;

    /**
     * 是否查询待评价订单
     */
    private Boolean queryUnCommented;


}
