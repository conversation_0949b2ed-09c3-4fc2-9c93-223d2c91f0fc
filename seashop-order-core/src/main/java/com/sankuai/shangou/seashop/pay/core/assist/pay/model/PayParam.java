package com.sankuai.shangou.seashop.pay.core.assist.pay.model;

import java.math.BigDecimal;

import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.PayPaymentCreateExpendDto;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/02 11:17
 */
@Data
public class PayParam {

    /**
     * 支付渠道:1汇付天下支付 2微信支付 3支付宝支付
     */
    private Integer paymentChannel;

    /**
     * 来源订单id
     */
    private String orderId;

    /**
     * 支付金额（单元：元）
     */
    private BigDecimal payAmount;

    /**
     * 支付类型
     */
    private Integer paymentType;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 商品标题
     */
    private String goodsTitle;

    /**
     * 商品描述信息，微信小程序和微信公众号该字段最大长度42个字符
     */
    private String goodsDesc;

    /**
     * 交易设备所在的公网 IP
     */
    private String deviceIp;

    /**
     * 支付渠道扩展参数
     */
    private PayPaymentCreateExpendDto expend;

    /**
     * 支付流水ID, 支付服务生成, 用于第三方的支付流水ID
     */
    private String payId;

    /**
     * 支付配置
     */
    private Object payConfig;

}
