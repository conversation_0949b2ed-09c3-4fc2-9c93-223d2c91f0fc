package com.sankuai.shangou.seashop.order.core.mq.model.pay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 支付结果返回对象，与支付服务MQ消息保持一致
 * <AUTHOR>
 */
@Data
public class PayResultBo {

    /**
     * 来源订单ID，在支付服务，这个字段不是订单的订单号，而是订单服务与支付服务的关联ID，对应 order_pay_record 表的 batch_no
     * 因为可以多个订单合并一次支付，所以是batch_no
     */
    private String orderId;
    /**
     * 支付金额
     */
    private BigDecimal payAmount;
    /**
     * 支付状态 1 成功 2 失败
     */
    private Integer payStatus;
    /**
     * 支付时间
     */
    private Date payTime;
    /**
     * 错误信息
     */
    private String errorMsg;
    /**
     * 外部交易ID
     */
    private String outTransId;
    /**
     * 汇付支付ID
     */
    private String payId;
    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 银行编码
     */
    private String bankCode;

}
