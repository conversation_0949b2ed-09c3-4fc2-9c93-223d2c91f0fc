package com.sankuai.shangou.seashop.order.core.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.order.common.enums.YesOrNoEnum;
import com.sankuai.shangou.seashop.order.common.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.RemoteMemberService;
import com.sankuai.shangou.seashop.order.common.remote.model.user.RemoteMemberBo;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderMessagePublisher;
import com.sankuai.shangou.seashop.order.core.service.OrderCommentService;
import com.sankuai.shangou.seashop.order.core.service.ProductCommentService;
import com.sankuai.shangou.seashop.order.core.service.assit.BizNoGenerator;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryOrderCommentBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderComment;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.ProductComment;
import com.sankuai.shangou.seashop.order.dao.core.domain.ProductCommentImage;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderCommentRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.ProductCommentImageRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.ProductCommentRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.CommentEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.DeleteOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ShopMarkResp;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/04 10:18
 */
@Service
@Slf4j
public class OrderCommentServiceImpl implements OrderCommentService {

    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderCommentRepository orderCommentRepository;
    @Resource
    private ProductCommentRepository productCommentRepository;
    @Resource
    private ProductCommentImageRepository productCommentImageRepository;
    @Resource
    private ProductCommentService productCommentService;
    @Resource
    private BizNoGenerator bizNoGenerator;
    @Resource
    private RemoteMemberService remoteMemberService;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private OrderMessagePublisher orderMessagePublisher;


    @Override
    public void saveOrderComment(OrderCommentBo commentBo) {
        String lockKey = LockConst.LOCK_ORDER_COMMENT + commentBo.getOrderId();
        distributedLockService.tryLock(new LockKey(LockConst.SCENE_ORDER_COMMENT, lockKey), () -> {
            // 检查并填充订单评价数据
            checkAndFillOrderComment(commentBo);

            // 提前构建保存数据
            OrderComment orderComment = JsonUtil.copy(commentBo, OrderComment.class);
            List<ProductCommentBo> productCommentBoList = commentBo.getProductCommentList();
            List<ProductComment> productCommentList = JsonUtil.copyList(productCommentBoList, ProductComment.class);
            List<ProductCommentImage> productCommentImageList = commentImageBuild(productCommentBoList, CommentEnum.CommentType.FIRST);

            TransactionHelper.doInTransaction(() -> {
                // 保存订单评价
                orderCommentRepository.save(orderComment);

                // 保存商品评价
                if (CollectionUtils.isNotEmpty(productCommentList)) {
                    productCommentRepository.saveBatch(productCommentList);
                }

                // 保存商品评价图片
                if (CollectionUtils.isNotEmpty(productCommentImageList)) {
                    productCommentImageRepository.saveBatch(productCommentImageList);
                }

                // 设置订单的评论状态
                orderRepository.updateOrderCommented(commentBo.getOrderId(), YesOrNoEnum.YES);
            });

        }, 10L, TimeUnit.SECONDS);

        // 发送订单评价事件
        try {
            orderMessagePublisher.sendOrderChangeMessage(commentBo.getOrderId(), OrderMessageEventEnum.COMMENT_ORDER);
        } catch (Exception e) {
            log.error("发送订单评价事件失败, orderId: {}", commentBo.getOrderId(), e);
        }
    }

    @Override
    public void appendOrderComment(OrderCommentBo commentBo) {
        String lockKey = LockConst.LOCK_ORDER_COMMENT_APPEND + commentBo.getId();
        distributedLockService.tryLock(new LockKey(LockConst.SCENE_ORDER_COMMENT_APPEND, lockKey), () -> {
            OrderComment orderComment = checkAndFillAppendOrderCommentBo(commentBo);

            // 提前构建保存数据
            List<ProductCommentBo> productCommentBoList = commentBo.getProductCommentList();
            List<ProductComment> productCommentList = JsonUtil.copyList(productCommentBoList, ProductComment.class);
            List<ProductCommentImage> productCommentImageList = commentImageBuild(productCommentBoList, CommentEnum.CommentType.APPEND);

            TransactionHelper.doInTransaction(() -> {
                // 保存商品评价
                if (CollectionUtils.isNotEmpty(productCommentList)) {
                    productCommentRepository.updateBatchById(productCommentList);
                }

                // 保存商品追加评价图片
                if (CollectionUtils.isNotEmpty(productCommentImageList)) {
                    productCommentImageRepository.saveBatch(productCommentImageList);
                }
            });
        }, 10L, TimeUnit.SECONDS);

        // 发送订单追评事件
        try {
            orderMessagePublisher.sendOrderChangeMessage(commentBo.getOrderId(), OrderMessageEventEnum.APPEND_COMMENT);
        } catch (Exception e) {
            log.error("发送订单追评事件失败, orderId: {}", commentBo.getOrderId(), e);
        }
    }

    @Override
    public BasePageResp<OrderCommentBo> queryOrderCommentForBuyer(BasePageParam pageParam, QueryOrderCommentBo queryBo) {
        Page<OrderComment> pageResult = PageHelper.startPage(pageParam);

        LambdaQueryWrapper<OrderComment> wrapper = orderCommentWrapperBuilder(queryBo);
        orderCommentRepository.list(wrapper);
        return PageResultHelper.transfer(pageResult, OrderCommentBo.class);
    }

    @Override
    public BasePageResp<OrderCommentBo> queryOrderCommentForPlatform(BasePageParam pageParam, QueryOrderCommentBo queryBo) {
        return queryOrderCommentForBuyer(pageParam, queryBo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteOrderCommentForPlatForm(Long id) {
        OrderComment orderComment = orderCommentRepository.getById(id);
        AssertUtil.throwIfNull(orderComment, "订单评价不存在");

        OrderComment updOderComment = new OrderComment();
        updOderComment.setId(id);
        updOderComment.setDeleted(Boolean.TRUE);
        orderCommentRepository.updateById(updOderComment);

        ProductComment productComment = new ProductComment();
        productComment.setOrderId(orderComment.getOrderId());
        productComment.setDeleted(Boolean.TRUE);
        productCommentRepository.update(productComment, new LambdaQueryWrapper<ProductComment>().eq(ProductComment::getOrderId, orderComment.getOrderId()));
    }

    @Override
    public OrderCommentBo queryOrderCommentDetailForBuyer(QueryOrderCommentBo queryBo) {
        OrderComment orderComment = checkAndGetOrderComment(queryBo.getOrderId(), queryBo.getUserId());
        if (orderComment == null) {
            // 查询初始化的评论数据
            return getInitComment(queryBo.getOrderId());
        }

        Order order = orderRepository.getByOrderId(queryBo.getOrderId());
        AssertUtil.throwIfNull(order, "订单不存在");
        OrderCommentBo orderCommentBo = JsonUtil.copy(orderComment, OrderCommentBo.class);

        // 查询商品评价数据
        List<ProductCommentBo> productCommentBoList = productCommentService.queryProductCommentByOrderId(order.getOrderId());
        productCommentBoList.forEach(bo -> bo.setBuyDate(order.getOrderDate()));
        orderCommentBo.setProductCommentList(productCommentBoList);
        return orderCommentBo;
    }

    @Override
    public ShopMarkResp queryShopMarkByShopId(Long shopId) {
        return JsonUtil.copy(orderCommentRepository.queryShopMarkByShopId(shopId), ShopMarkResp.class);
    }

    @Override
    public void deleteOrderCommentForBuyer(DeleteOrderCommentReq req) {
        // 这个查询接口里面 已经校验的越权
        OrderComment dbComment = orderCommentRepository.getById(req.getId());
        AssertUtil.throwIfNull(dbComment, "订单评价不存在");

        QueryOrderCommentBo orderCommentQueryBo = JsonUtil.copy(req, QueryOrderCommentBo.class);
        orderCommentQueryBo.setOrderId(dbComment.getOrderId());
        OrderCommentBo orderCommentBo = queryOrderCommentDetailForBuyer(orderCommentQueryBo);

        // 如果评价和追评风控都过了 则不允许删除
        /*if (!CommentEnum.RiskStatus.REFUSE.getCode().equals(orderCommentBo.getRiskStatus())
                && !CommentEnum.RiskStatus.REFUSE.getCode().equals(orderCommentBo.getAppendRiskStatus())) {
            throw new BusinessException("当前评论不允许删除");
        }*/

        String orderId = orderCommentBo.getOrderId();
        Long commentId = orderCommentBo.getId();
        List<Long> productCommentIds = orderCommentBo.getProductCommentList().stream().map(ProductCommentBo::getProductCommentId).collect(Collectors.toList());

        // 如果是追评风控未通过 则清空追评
        /*if (CommentEnum.RiskStatus.REFUSE.getCode().equals(orderCommentBo.getAppendRiskStatus())) {
            clearAppendComment(productCommentIds);
            return;
        }*/

        // 直接删除订单评价
        removeOrderComment(orderId, commentId, productCommentIds);
    }

    /**
     * 清空追评
     *
     * @param productCommentIds 商品评价Id的集合
     */
    private void clearAppendComment(List<Long> productCommentIds) {
        if (CollectionUtils.isEmpty(productCommentIds)) {
            return;
        }

        TransactionHelper.doInTransaction(() -> {
            // 清空掉所有的追评
            productCommentRepository.update(
                    new LambdaUpdateWrapper<ProductComment>()
                            .in(ProductComment::getProductCommentId, productCommentIds)
                            .set(ProductComment::getAppendDate, null)
                            .set(ProductComment::getAppendContent, null)
                            .set(ProductComment::getAppendHasImage, false)
            );

            // 清理追评的图片
            productCommentImageRepository.deleteProductImage(productCommentIds, CommentEnum.CommentType.APPEND.getCode());
        });
    }

    /**
     * 删除订单评价
     *
     * @param orderId           订单Id
     * @param commentId         订单评价Id
     * @param productCommentIds 商品评价Id集合
     */
    private void removeOrderComment(String orderId, Long commentId, List<Long> productCommentIds) {
        TransactionHelper.doInTransaction(() -> {
            orderCommentRepository.removeById(commentId);

            // 删除商品评价
            productCommentRepository.removeByProductCommentIds(productCommentIds);

            // 删除评论图片
            productCommentImageRepository.deleteProductImage(productCommentIds, null);

            // 重置订单表的评价状态
            orderRepository.updateOrderCommented(orderId, YesOrNoEnum.NO);
        });
    }

    /**
     * 获取初始化订单评价数据(获取未评价订单的初始化页面数据)
     *
     * @param orderId 订单id
     * @return 评价数据
     */
    private OrderCommentBo getInitComment(String orderId) {
        Order order = orderRepository.getByOrderId(orderId);
        AssertUtil.throwIfNull(order, "订单不存在");

        OrderCommentBo orderCommentBo = JsonUtil.copy(order, OrderCommentBo.class);
        orderCommentBo.setId(null);
        List<OrderItem> orderItemList = orderItemRepository.getByOrderId(orderId);
        List<ProductCommentBo> productCommentList = JsonUtil.copyList(orderItemList, ProductCommentBo.class, (db, bo) -> {
            bo.setShopName(order.getShopName());
            bo.setUserId(order.getUserId());
            bo.setUserName(order.getUserName());
            bo.setBuyDate(order.getOrderDate());
            bo.setSubOrderId(db.getId());
        });
        orderCommentBo.setProductCommentList(productCommentList);
        return orderCommentBo;
    }

    /**
     * 检查并填充订单评价数据
     *
     * @param commentBo 评价数据
     */
    private void checkAndFillOrderComment(OrderCommentBo commentBo) {
        Order order = orderRepository.getByOrderId(commentBo.getOrderId());
        AssertUtil.throwIfNull(order, "订单不存在");
        AssertUtil.throwIfTrue(!order.getUserId().equals(commentBo.getUserId()), "无权操作");

        Boolean existComment = orderCommentRepository.existByOrderId(commentBo.getOrderId());
        AssertUtil.throwIfTrue(existComment, "订单已经评价过");

        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(Arrays.asList(commentBo.getOrderId()));
        Map<Long, OrderItem> orderItemMap = orderItemList.stream()
                .collect(Collectors.toMap(OrderItem::getId, Function.identity(), (k1, k2) -> k2));

        RemoteMemberBo member = remoteMemberService.getByMemberId(order.getUserId());
        List<Long> productIds = orderItemList.stream().map(OrderItem::getProductId).collect(Collectors.toList());
        Map<Long, ProductBasicDto> productMap = productRemoteService.queryProductList(productIds)
                .stream().collect(Collectors.toMap(ProductBasicDto::getProductId, Function.identity(), (k1, k2) -> k2));

        // 商品评论去重 订单明细是根据订单id获取的, 所以无需再进行水平越权校验
        List<ProductCommentBo> productCommentList = distinctProductComment(commentBo.getProductCommentList());
        productCommentList.forEach(productComment -> {
            OrderItem orderItem = orderItemMap.get(productComment.getSubOrderId());
            AssertUtil.throwIfNull(orderItem, "子订单不存在");

            ProductBasicDto product = productMap.get(orderItem.getProductId());

            // 商品评论赋值
            productComment.setProductId(orderItem.getProductId());
            productComment.setProductName(orderItem.getProductName());
            productComment.setShopId(orderItem.getShopId());
            productComment.setShopName(order.getShopName());
            productComment.setUserId(order.getUserId());
            productComment.setUserName(order.getUserName());
            productComment.setOrderId(order.getOrderId());
            productComment.setSpec1Value(orderItem.getColor());
            productComment.setSpec2Value(orderItem.getSize());
            productComment.setSpec3Value(orderItem.getVersion());
            productComment.setThumbnailsUrl(orderItem.getThumbnailsUrl());
            productComment.setSkuId(String.valueOf(orderItem.getSkuAutoId()));
            if (member != null) {
                productComment.setUserMobile(member.getCellPhone());
                productComment.setEmail(member.getEmail());
            }
            if (product != null) {
                productComment.setSpec1Alias(product.getSpec1Alias());
                productComment.setSpec2Alias(product.getSpec2Alias());
                productComment.setSpec3Alias(product.getSpec3Alias());
            }

            productComment.setReviewDate(new Date());
            productComment.setHasImage(CollectionUtils.isNotEmpty(productComment.getCommentImageList()));
        });
        // 填充id
        fillProductComment(productCommentList);
        commentBo.setProductCommentList(productCommentList);

        // 主订单赋值
        commentBo.setCommentDate(new Date());
        commentBo.setShopId(order.getShopId());
        commentBo.setShopName(order.getShopName());
        commentBo.setUserId(order.getUserId());
        commentBo.setUserName(order.getUserName());
    }

    /**
     * 检查并填充追加订单评价数据
     *
     * @param commentBo 评价数据
     */
    private OrderComment checkAndFillAppendOrderCommentBo(OrderCommentBo commentBo) {
        OrderComment orderComment = orderCommentRepository.getById(commentBo.getId());
        AssertUtil.throwIfTrue(orderComment == null || orderComment.getDeleted(), "订单评价不存在");
        AssertUtil.throwIfTrue(!orderComment.getUserId().equals(commentBo.getUserId()), "无权操作");

        List<ProductCommentBo> productCommentBoList = commentBo.getProductCommentList();
        if (CollectionUtils.isEmpty(productCommentBoList)) {
            return orderComment;
        }
        List<Long> productCommentIdList = productCommentBoList.stream().map(ProductCommentBo::getProductCommentId).collect(Collectors.toList());
        List<ProductComment> productCommentList = productCommentRepository.listByProductCommentIds(productCommentIdList);
        long count = productCommentList.stream().filter(item -> !item.getOrderId().equals(orderComment.getOrderId())).count();
        AssertUtil.throwIfTrue(count > 0, "无权操作");
        Map<Long, ProductComment> productCommentMap = productCommentList.stream().collect(Collectors.toMap(ProductComment::getProductCommentId, Function.identity(), (k1, k2) -> k2));
        productCommentBoList.forEach(productCommentBo -> {
            ProductComment dbProductComment = productCommentMap.get(productCommentBo.getProductCommentId());
            AssertUtil.throwIfNull(dbProductComment, "商品评价不存在");

            productCommentBo.setId(dbProductComment.getId());
            productCommentBo.setAppendDate(new Date());
            productCommentBo.setAppendHasImage(CollectionUtils.isNotEmpty(productCommentBo.getAppendCommentImageList()));
        });
        commentBo.setOrderId(orderComment.getOrderId());
        return orderComment;
    }

    /**
     * 检查并获取订单评价
     *
     * @param orderId 订单id
     * @param userId  用户id
     * @return 评价数据
     */
    private OrderComment checkAndGetOrderComment(String orderId, Long userId) {
        OrderComment orderComment = orderCommentRepository.getByOrderId(orderId);
        if (orderComment == null) {
            return null;
        }
        AssertUtil.throwIfTrue(!orderComment.getUserId().equals(userId), "无权操作");
        return orderComment;
    }

    /***
     * 商品评论去重
     */
    private List<ProductCommentBo> distinctProductComment(List<ProductCommentBo> commentBoList) {
        if (CollectionUtils.isEmpty(commentBoList)) {
            return Collections.EMPTY_LIST;
        }

        Set<Long> orderItemIdSet = new HashSet<>();
        return commentBoList.stream().filter(item -> orderItemIdSet.add(item.getSubOrderId())).collect(Collectors.toList());
    }

    /**
     * 构建商品评价图片
     *
     * @param productCommentList 商品评价bo集合
     * @param commentType        评价类型
     * @return 评价图片
     */
    private List<ProductCommentImage> commentImageBuild(List<ProductCommentBo> productCommentList, CommentEnum.CommentType commentType) {
        if (CollectionUtils.isEmpty(productCommentList)) {
            return Collections.EMPTY_LIST;
        }

        List<ProductCommentImage> commentImageList = new ArrayList<>();
        productCommentList.forEach(productComment -> {
            List<ProductCommentImage> images = commentImageBuild(productComment.getProductCommentId(), commentType, productComment.getCommentImageList());
            commentImageList.addAll(images);
        });
        return commentImageList;
    }

    /**
     * 构建订单商品评价图片
     *
     * @param productCommentId 商品评价id
     * @param commentType      评价类型
     * @param images           评价图片
     * @return 评价图片
     */
    private List<ProductCommentImage> commentImageBuild(Long productCommentId, CommentEnum.CommentType commentType, List<String> images) {
        if (CollectionUtils.isEmpty(images) || productCommentId == null || commentType == null) {
            return Collections.EMPTY_LIST;
        }

        return images.stream().map(image -> {
            ProductCommentImage commentImage = new ProductCommentImage();
            commentImage.setProductCommentId(productCommentId);
            commentImage.setCommentType(commentType.getCode());
            commentImage.setCommentImage(image);
            return commentImage;
        }).collect(Collectors.toList());
    }

    /**
     * 填充商品评价id 和是否有图片
     * 使用美团id组件生成
     *
     * @param productCommentList 商品评价bo集合
     */
    private void fillProductComment(List<ProductCommentBo> productCommentList) {
        if (CollectionUtils.isEmpty(productCommentList)) {
            return;
        }

        List<Long> productCommentIdList = bizNoGenerator.generateProductCommentId(productCommentList.size());
        for (int i = 0; i < productCommentList.size(); i++) {
            productCommentList.get(i).setProductCommentId(productCommentIdList.get(i));
            productCommentList.get(i).setHasImage(CollectionUtils.isNotEmpty(productCommentList.get(i).getCommentImageList()));
        }
    }

    /**
     * 订单评论构建筛选wrapper
     *
     * @param queryBo 查询条件
     * @return wrapper
     */
    private LambdaQueryWrapper<OrderComment> orderCommentWrapperBuilder(QueryOrderCommentBo queryBo) {
        LambdaQueryWrapper<OrderComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(queryBo.getStartTime() != null, OrderComment::getCommentDate, queryBo.getStartTime());
        wrapper.le(queryBo.getEndTime() != null, OrderComment::getCommentDate, queryBo.getEndTime());
        wrapper.eq(StringUtils.isNotEmpty(queryBo.getOrderId()), OrderComment::getOrderId, queryBo.getOrderId());
        wrapper.eq(queryBo.getUserId() != null, OrderComment::getUserId, queryBo.getUserId());
        wrapper.eq(queryBo.getShopId() != null, OrderComment::getShopId, queryBo.getShopId());
        wrapper.likeRight(StringUtils.isNotEmpty(queryBo.getShopName()), OrderComment::getShopName, queryBo.getShopName());
        wrapper.likeRight(StringUtils.isNotEmpty(queryBo.getUserName()), OrderComment::getUserName, queryBo.getUserName());
        wrapper.eq(OrderComment::getDeleted, Boolean.FALSE);
        wrapper.orderByDesc(OrderComment::getId);
        return wrapper;
    }
}
