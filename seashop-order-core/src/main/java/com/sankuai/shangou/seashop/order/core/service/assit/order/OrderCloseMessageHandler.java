package com.sankuai.shangou.seashop.order.core.service.assit.order;

import com.sankuai.shangou.seashop.order.common.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.StockRemoteService;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderCloseMessageHandler implements OrderChangeMessageHandler {

    @Resource
    private StockRemoteService stockRemoteService;
    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public OrderMessageEventEnum getEvent() {
        return OrderMessageEventEnum.CANCEL_ORDER;
    }

    @Override
    public void handle(OrderMessage message) {
        log.info("【订单状态变更-关闭】订单关闭, orderId: {}", message.getOrderId());
        String orderId = message.getOrderId();
        Order order = orderRepository.getByOrderId(orderId);
        if (order == null) {
            log.warn("【订单状态变更-关闭】订单关闭, orderId: {} 订单不存在", orderId);
            return;
        }
        // 释放库存，退还营销，对应业务保证重试幂等
        stockRemoteService.rollbackStock(Collections.singletonList(orderId), StockUpdateTypeEnum.CREATE_ORDER);
        log.info("【订单状态变更-关闭】订单关闭, orderId: {} 回滚库存成功", orderId);
        promotionRemoteService.rollbackCoupon(order.getUserId(), Collections.singletonList(orderId));
        log.info("【订单状态变更-关闭】订单关闭, orderId: {} 回滚优惠券成功", orderId);
        promotionRemoteService.rollbackFlashSaleStock(orderId);
        log.info("【订单状态变更-关闭】订单关闭, orderId: {} 回滚限时购库存成功", orderId);
    }
}
