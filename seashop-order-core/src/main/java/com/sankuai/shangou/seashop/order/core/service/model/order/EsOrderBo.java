package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class EsOrderBo {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
     */
    private Integer invoiceType;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 收货人手机号
     */
    private String cellPhone;
    /**
     * 订单状态。1：待付款，2：待发货，3：待收货，4：已关闭，5：已完成，6：支付中
     */
    private Integer orderStatus;
    /**
     * 下单时间
     */
    private Long orderDate;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 商品总数量
     */
    private Integer productCount;
    /**
     * 商品总金额，纯商品金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 订单实付金额，支付金额
     */
    private BigDecimal totalAmount;
    /**
     * 订单实收金额(订单实付-退款金额)
     */
    private BigDecimal actualPayAmount;
    /**
     * 订单完成时间
     */
    private Long finishDate;
    /**
     * 订单来源 0表示本系统，1表示牵牛花
     */
    //private Integer orderSource;
    /**
     * 下单平台(端口)，0：pc端，2：小程序
     */
    private Integer platform;
    /**
     * 支付类型。1：线上支付
     */
    private Integer paymentType;

    /**
     * 支付类型。1：线上支付
     */
    private Integer payment;
    /**
     * 支付时间
     */
    private Long payDate;
    /**
     * 支付方式。1：支付宝扫码，3：微信小程序，5：企业网银，6：个人网银
     */
    private Integer payMethod;
    /**
     * 交易单号，支付返回的流水号
     */
    private String gatewayOrderId;
    /**
     * 支付单号
     */
    private String payNo;
    /**
     * 订单明细
     */
    private List<EsOrderItemBo> orderItems;
    /**
     * 发票信息
     */
    private EsOrderInvoiceBo orderInvoice;
    /**
     * 订单是否已评价。0：否；1：是
     */
    private Integer hasCommented;
    /**
     * 订单发票地址
     */
    private String invoiceUrl;

    /**
     * 门店ID
     */
    private Long storeId;

    /**
     * 订单类型列表（0:正常购买; 1:组合购; 2:限时购; 3:虚拟商品订单; 4:实物自提订单;）
     */
    private Integer orderType;

}
