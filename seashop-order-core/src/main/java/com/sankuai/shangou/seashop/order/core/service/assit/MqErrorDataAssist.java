package com.sankuai.shangou.seashop.order.core.service.assit;

import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.order.common.enums.MqErrorDataBizTypeEnum;
import com.sankuai.shangou.seashop.order.common.enums.MqErrorDataHandleStatusEnum;
import com.sankuai.shangou.seashop.order.dao.core.domain.MqErrorData;
import com.sankuai.shangou.seashop.order.dao.core.repository.MqErrorDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class MqErrorDataAssist {

    @Resource
    private MqErrorDataRepository mqErrorDataRepository;
    @Resource
    private DistributedLockService distributedLockService;

    public void saveErrorData(MqErrorDataBizTypeEnum bizTypeEnum, String uniqueNo, String messageId, String traceId, String errMsg, String dataContent) {
        log.info("MQ消息消费异常保存, bizType={}, uniqueNo={}, messageId={}, traceId={}", bizTypeEnum, uniqueNo, messageId, traceId);
        // 加锁，查询最新的数据
        String lockKey = String.format(LockConst.LOCK_MQ_ERROR_DATA_SAVE_PATTERN, bizTypeEnum.getCode(), uniqueNo);
        // 分布式锁+唯一ID，判断是否已经存在，存在了就不需要保存了
        distributedLockService.tryLock(new LockKey(LockConst.SCENE_MQ_ERROR_DATA_SAVE, lockKey), () -> {
            MqErrorData mqData = mqErrorDataRepository.getByBizTypeAndBizNo(bizTypeEnum.getCode(), uniqueNo);
            if (mqData != null) {
                log.info("MQ消息消费异常保存, 数据已存在无需保存");
                return;
            }
            String msg = null;
            if (errMsg == null) {
                msg = "";
            } else if (errMsg.length() > 500) {
                msg = errMsg.substring(0, 500);
            } else {
                msg = errMsg;
            }
            String realTraceId = null;
            if (traceId == null) {
                realTraceId = getTraceId();
            } else {
                realTraceId = traceId;
            }
            mqData = new MqErrorData();
            mqData.setBizType(bizTypeEnum.getCode());
            mqData.setBizTypeDesc(bizTypeEnum.getDesc());
            mqData.setBizNo(uniqueNo);
            mqData.setMessageId(messageId);
            mqData.setTraceId(realTraceId);
            mqData.setErrorMessage(msg);
            mqData.setDataContent(dataContent);
            mqData.setHandleStatus(MqErrorDataHandleStatusEnum.UNDER_HANDLE.getCode());
            mqErrorDataRepository.save(mqData);
            log.info("MQ消息消费异常保存成功");
        });
    }

    public String getTraceId() {
        //Span serverSpan = Tracer.getServerTracer().getSpan();
        //if (serverSpan != null) {
        //    log.info("server traceId:{}", serverSpan.getTraceId());
        //    return serverSpan.getTraceId();
        //}
        //Span clientSpan = Tracer.getClientTracer().getSpan();
        //if (clientSpan != null) {
        //    log.info("client traceId:{}", clientSpan.getTraceId());
        //    return clientSpan.getTraceId();
        //}
        String traceId = MDC.get("X-B3-TraceId");
        log.info("X-B3-TraceId:{}", traceId);
        if (traceId == null) {
            traceId = MDC.get("traceId");
        }
        return traceId;
    }

}
