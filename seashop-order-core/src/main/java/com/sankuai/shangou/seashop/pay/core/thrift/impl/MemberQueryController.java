package com.sankuai.shangou.seashop.pay.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.core.service.PayService;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaySettleAccountQryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPaySettleAccountQryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.service.MemberQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/21/021
 * @description:
 */
@RestController
@RequestMapping("/member")
public class MemberQueryController implements MemberQueryFeign {

    @Resource
    private PayService payService;

    @PostMapping(value = "/hasSettleAccount", consumes = "application/json")
    @Override
    public ResultDto<Boolean> hasSettleAccount(@RequestBody PaySettleAccountQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("hasSettleAccount", request, req -> {
            req.checkParameter();
            if (req.getPaymentChannel().equals(PaymentChannelEnum.ADAPAY.getCode())) {
                return payService.hasSettleAccount(JsonUtil.copy(req, AdaPaySettleAccountQryReq.class));
            } else {
                // req.checkParameter() 已经校验类型，这里不会执行
                return null;
            }

        });
    }
}
