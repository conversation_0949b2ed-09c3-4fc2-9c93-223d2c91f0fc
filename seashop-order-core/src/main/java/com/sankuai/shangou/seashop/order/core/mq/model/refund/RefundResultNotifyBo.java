package com.sankuai.shangou.seashop.order.core.mq.model.refund;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RefundResultNotifyBo {

    /**
     * 售后单，对应发起售后时传的 batchNo，但后续其实重置掉了
     */
    private String refundId;

    /**
     * 订单ID
     */
    private String orderNo;

    /**
     * 渠道退款单号，发起售后后，会用这个字段重置 batchNo
     */
    private String channelRefundId;

    /**
     * 订单金额
     */
    private String refundedAmt;
    /**
     * 支付状态 1 成功 2 失败
     */
    private Integer payStatus;

    /**
     * 错误描述
     */
    private String errorMessage;

    /**
     * 退款类型
     */
    private Integer type;

    /**
     * 业务类型: 1：订单 4：保证金
     */
    private Integer businessType;

    /**
     * 业务状态类型：1：正常订单退款；2：异常订单退款；3：订单补偿退款（支付金额大于订单金额）
     */
    private Integer businessStatusType;

}
