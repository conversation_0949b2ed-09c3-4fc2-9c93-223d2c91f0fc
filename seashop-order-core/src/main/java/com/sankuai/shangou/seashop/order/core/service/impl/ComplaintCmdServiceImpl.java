package com.sankuai.shangou.seashop.order.core.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.enums.OrderComplaintStatusEnum;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.core.service.ComplaintCmdService;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderComplaint;
import com.sankuai.shangou.seashop.order.dao.core.repository.ComplaintRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.request.*;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberContactReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberContactResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberContactRespList;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.Objects;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/21 19:28
 */
@Service
@Slf4j
public class ComplaintCmdServiceImpl implements ComplaintCmdService {

    @Resource
    private ComplaintRepository complaintRepository;

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private ShopRemoteService shopRemoteService;

    @Override
    public void startComplaint(StartComplaintReq cmdOrderComplaintReq) {
        // 先查询订单表，取出需要的字段，再组装参数，插投诉表
        Order order = orderRepository.getByOrderId(cmdOrderComplaintReq.getOrderId());
        OrderComplaint orderComplaint = JsonUtil.copy(order, OrderComplaint.class);
        ShopDetailResp shopDetailResp = shopRemoteService.queryDetail(order.getShopId());
        orderComplaint.setShopPhone(StringUtils.isEmpty(shopDetailResp.getContactsPhone())?shopDetailResp.getMemberPhone():shopDetailResp.getContactsPhone());
        if(StringUtils.isEmpty(orderComplaint.getShopPhone())){
            QueryMemberContactReq queryMemberContactReq = new QueryMemberContactReq();
            queryMemberContactReq.setShopIds(Arrays.asList(order.getShopId()));
            MemberContactRespList memberContactRespList = shopRemoteService.queryMemberContact(queryMemberContactReq);
            if(Objects.isNull(memberContactRespList) || CollectionUtils.isEmpty(memberContactRespList.getMemberContactRespList())){
                throw new RuntimeException("供应商数据有误，联系方式为空。");
            }
            orderComplaint.setShopPhone(memberContactRespList.getMemberContactRespList().get(0).getContact());
        }
        // 审核状态(1:等待供应商处理,2:供应商处理完成,3:等待平台介入,4:已结束)
        orderComplaint.setStatus(OrderComplaintStatusEnum.WAIT_SUPPLIER_DEAL.getCode());
        orderComplaint.setOrderId(cmdOrderComplaintReq.getOrderId());
        orderComplaint.setUserPhone(cmdOrderComplaintReq.getUserPhone());
        orderComplaint.setComplaintReason(cmdOrderComplaintReq.getComplaintReason());
        orderComplaint.setComplaintDate(new Date());
        orderComplaint.setCreateTime(new Date());
        orderComplaint.setUpdateTime(new Date());
        complaintRepository.insert(orderComplaint);
    }

    @Override
    public void cancelComplaint(CancelComplaintReq cancelComplaintReq) {
        OrderComplaint orderComplaint = new OrderComplaint();
        orderComplaint.setId(cancelComplaintReq.getId());
        orderComplaint.setStatus(cancelComplaintReq.getStatus());
        orderComplaint.setUpdateTime(new Date());
        complaintRepository.updateById(orderComplaint);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" ,actionName = "投诉维权供应商处理",
            processModel = ExaminModelEnum.ORDER, processType = ExaProEnum.MODIFY,
            repository = "complaintRepository", serviceMethod = "dealSellerComplaint",
            dto = DealSellerComplaintReq.class, entity = OrderComplaint.class)
    public void dealSellerComplaint(DealSellerComplaintReq dealSellerComplaintReq) {
        OrderComplaint orderComplaint = new OrderComplaint();
        orderComplaint.setId(dealSellerComplaintReq.getId());
        orderComplaint.setStatus(OrderComplaintStatusEnum.SUPPLIER_COMPLETED.getCode());
        orderComplaint.setSellerReply(dealSellerComplaintReq.getSellerReply());
        orderComplaint.setUpdateTime(new Date());
        complaintRepository.updateById(orderComplaint);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" ,actionName = "投诉维权申请仲裁",
            processModel = ExaminModelEnum.ORDER, processType = ExaProEnum.MODIFY,
            repository = "complaintRepository", serviceMethod = "applyArbitrateComplaint",
            dto = ApplyArbitrateComplaintReq.class, entity = OrderComplaint.class)
    public void applyArbitrateComplaint(ApplyArbitrateComplaintReq applyArbitrateComplaintReq) {
        OrderComplaint orderComplaint = new OrderComplaint();
        orderComplaint.setId(applyArbitrateComplaintReq.getId());
        orderComplaint.setStatus(applyArbitrateComplaintReq.getStatus());
        orderComplaint.setUpdateTime(new Date());
        complaintRepository.updateById(orderComplaint);
    }

    @Override
    public void applyMallArbitrateComplaint(ApplyArbitrateComplaintReq applyArbitrateComplaintReq) {
        OrderComplaint orderComplaint = new OrderComplaint();
        orderComplaint.setId(applyArbitrateComplaintReq.getId());
        orderComplaint.setStatus(applyArbitrateComplaintReq.getStatus());
        orderComplaint.setUpdateTime(new Date());
        complaintRepository.updateById(orderComplaint);
    }

    @Override
    @ExaminProcess(operationUserId = "operationUserId",shopId = "shopId" ,actionName = "投诉维权平台处理",
            processModel = ExaminModelEnum.ORDER, processType = ExaProEnum.MODIFY,
            repository = "complaintRepository", serviceMethod = "dealMComplaint",
            dto = DealMComplaintReq.class, entity = OrderComplaint.class)
    public void dealMComplaint(DealMComplaintReq dealMComplaintReq) {
        OrderComplaint orderComplaint = new OrderComplaint();
        orderComplaint.setId(dealMComplaintReq.getId());
        orderComplaint.setStatus(OrderComplaintStatusEnum.PLATFORM_COMPLETED.getCode());
        orderComplaint.setPlatRemark(dealMComplaintReq.getPlatRemark());
        orderComplaint.setUpdateTime(new Date());
        complaintRepository.updateById(orderComplaint);
    }

}
