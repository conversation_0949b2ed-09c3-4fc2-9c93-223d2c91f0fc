package com.sankuai.shangou.seashop.order.core.statemachine.action;

import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.statemachine.Action;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderMessagePublisher;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderBizAssist;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderEvent;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 取消订单事件发生时的动作:包括用户手动取消以及定时任务扫描超时关闭
 * <p>待付款状态才能取消</p>
 * <AUTHOR>
 */
@Service
@Slf4j
public class CancelOrderAction extends BaseAction implements Action<OrderStatusEnum, OrderEvent, OrderContext> {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderMessagePublisher orderMessagePublisher;
    @Resource
    private OrderBizAssist orderBizAssist;

    /**
     * <AUTHOR>
     */
    @Override
    public void executeStatusChange(OrderContext context, Order dbOrder) {
        // 如果用户ID为0，说明是定时任务扫描超时关闭
        Long userId = context.getUserId();
        String orderId = context.getOrderId();
        log.info("【订单状态机】用户取消订单,userId={},orderId={}", userId, orderId);
        // 取消订单状态设置为关闭
        dbOrder.setOrderStatus(OrderStatusEnum.CLOSED.getCode());
        TransactionHelper.doInTransaction(() -> {
            // 更新订单状态
            int cnt = orderRepository.updateOrderClose(Collections.singletonList(dbOrder.getOrderId()),
                    OrderStatusEnum.UNDER_PAY.getCode(), OrderStatusEnum.CLOSED.getCode(), context.getCancelReason());
            if (cnt != 1) {
                throw new BusinessException("订单状态已经变更，不能关闭");
            }
            // 保存操作日志
            String cancelReason = StrUtil.nullToDefault(context.getCancelReason(), "");
            String userName = StrUtil.nullToDefault(context.getUserName(), "");
            orderBizAssist.addOrderOperationLog(orderId, userName, cancelReason);
        });
        // 发送消息，异步回滚库存和营销
        orderMessagePublisher.sendOrderChangeMessage(orderId, OrderMessageEventEnum.CANCEL_ORDER);
    }

    @Override
    protected void validateBizData(OrderContext context, Order dbOrder) {
        if (!dbOrder.getOrderStatus().equals(OrderStatusEnum.UNDER_PAY.getCode())) {
            throw new BusinessException("待付款状态的订单才能取消");
        }
    }

    @Override
    protected void dealExecuteResult(OrderContext context, ActionResult executeResult) {
        if (!executeResult.isSuccess()) {
            throw new BusinessException("订单状态已经变更，不需要再次处理");
        }
    }
}
