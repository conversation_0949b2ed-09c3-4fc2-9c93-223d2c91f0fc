package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * 核对订单是否异常参数，如果异常则添加异常订单
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
public class CheckExceptionOrderBo {

    /**
     * 订单号，因为多笔订单可以同时支付，所以支持数组传入
     */
    private List<String> orderIdList;
    /**
     * 支付批次号，多笔订单同时支付时批次号相同。与 order_pay_record 表的 batch_no 字段对应
     */
    private String batchNo;
    /**
     * 支付渠道对应的唯一标识。与 order_pay_record 表的 pay_no 字段对应
     */
    private String payNo;
    /**
     * 支付时间。与 order 表的 pay_time 字段对应
     */
    private Date payTime;
    /**
     * 支付金额。与 order 表的 pay_amount 字段对应
     */
    //private BigDecimal payAmount;
    /**
     * 异常类型。1，重复支付；2，超时关闭；
     */
    //private ExceptionOrderTypeEnum errorType;

}
