package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.core.service.model.refund.*;

/**
 * <AUTHOR>
 */
public interface OrderRefundSearchService {

    /**
     * 构建售后索引。基于数据表变更
     *
     * @param refundId 售后ID
     */
    void buildEsRefund(Long refundId);

    /**
     * 更新订单状态。业务代码驱动修改
     *
     * @param orderId 订单ID
     */
    void updateOrderStatus(String orderId);

    /**
     * 用户查询售后记录
     *
     * @param queryBo 查询参数
     * @return 售后记录
     */
    BasePageResp<UserRefundBo> userQueryPage(UserQueryRefundBo queryBo);

    /**
     * 商家查询售后记录
     *
     * @param queryBo 查询参数
     * @return 售后记录
     */
    BasePageResp<SellerRefundBo> sellerQueryPage(SellerQueryRefundBo queryBo);

    /**
     * 平台查询售后记录
     *
     * @param queryBo 查询参数
     * @return 售后记录
     */
    BasePageResp<PlatformRefundBo> platformQueryPage(PlatformQueryRefundBo queryBo);

}
