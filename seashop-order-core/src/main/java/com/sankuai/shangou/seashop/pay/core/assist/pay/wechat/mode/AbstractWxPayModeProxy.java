package com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.mode;

import com.github.binarywang.wxpay.service.WxPayService;
import com.sankuai.shangou.seashop.pay.core.assist.pay.PayModeProxy;
import com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.WxPayUtil;
import com.sankuai.shangou.seashop.pay.dao.core.model.WxPayConfigModel;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/02 13:55
 */
@Data
public abstract class AbstractWxPayModeProxy implements PayModeProxy {

    protected WxPayConfigModel payConfig;
    protected WxPayService wxPayService;

    protected String appId;
    protected PaymentTypeEnum paymentType;
    protected WxPayUtil wxPayUtil;

}
