package com.sankuai.shangou.seashop.order.core.service.assit.refund;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.StockRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.product.DecreaseStockBo;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.RefundResultNotifyBo;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderMessagePublisher;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderRefundMessagePublisher;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundRecordService;
import com.sankuai.shangou.seashop.order.dao.core.domain.ExceptionOrder;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefundRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.ExceptionOrderRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.domain.AccountDetail;
import com.sankuai.shangou.seashop.order.dao.finance.domain.PendingSettlementOrder;
import com.sankuai.shangou.seashop.order.dao.finance.repository.AccountDetailRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.PendingSettlementOrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.RefundEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.ExceptionOrderRefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.OrderRefundRecordStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessStatusTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseStateEnums;
import com.sankuai.shangou.seashop.product.thrift.core.enums.StockUpdateTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * 退款结果回调处理
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RefundResultNotifyAssist {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private PendingSettlementOrderRepository pendingSettlementOrderRepository;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private AccountDetailRepository accountDetailRepository;
    @Resource
    private ExceptionOrderRepository exceptionOrderRepository;
    @Resource
    private OrderRefundRecordRepository orderRefundRecordRepository;
    @Resource
    private OrderRefundRecordService orderRefundRecordService;
    @Resource
    private StockRemoteService stockRemoteService;
    @Resource
    private OrderRefundMessagePublisher orderRefundMessagePublisher;
    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private OrderMessagePublisher orderMessagePublisher;

    /**
     * 这里的是支付撤销(退款)的回调，理论上不会有并发
     */
    public void handleRefundResult(RefundResultNotifyBo refundResult) {
        log.info("退款结果回调,{}", JsonUtil.toJsonString(refundResult));
        // 根据业务类型区分处理不同的退款：正常订单、异常订单
        if (BusinessStatusTypeEnum.NORMAL.getType().equals(refundResult.getBusinessStatusType())) {
            handleRefundNotifyForNormalOrder(refundResult);
        } else if (BusinessStatusTypeEnum.ABNORMAL.getType().equals(refundResult.getBusinessStatusType())) {
            handleRefundForExceptionOrder(refundResult);
        } else if (BusinessStatusTypeEnum.COMPENSATION.getType().equals(refundResult.getBusinessStatusType())) {
            // 超支退款的情况
            handleRefundForCompensationOrder(refundResult);
        } else {
            log.warn("退款结果回调,业务类型不正确,{}", refundResult);
        }
    }

    private void handleRefundNotifyForNormalOrder(RefundResultNotifyBo refundResult) {
        // 理论上这个退款一定会成功，如果失败了，不改变任何结果，保持状态为退款中，后续人工介入处理
        if (!ReverseStateEnums.REVERSE_SUCCESS.getStatus().equals(refundResult.getPayStatus())) {
            log.warn("退款结果回调,退款状态不是成功,{}", refundResult);
            return;
        }
        // 售后的批次，理论上是唯一的，不会有重复的
        OrderRefund refund = orderRefundRepository.getByBatchNo(refundResult.getChannelRefundId());
        if (refund == null) {
            log.warn("退款结果回调,退款记录不存在,{}", refundResult);
            return;
        }
        // 不是退款中，不处理。正常流程是审核通过后就是退款中，然后等待回调。不是退款中可能是超时重试等情况
        if (!RefundAuditStatusEnum.PLATFORM_PASS.getCode().equals(refund.getManagerConfirmStatus())) {
            log.warn("退款结果回调,退款记录状态不是平台审核通过,{}", refundResult);
            return;
        }

        Order order = orderRepository.getByOrderId(refund.getOrderId());
        if (order == null) {
            log.error("退款结果回调,订单不存在,{}", refundResult);
            return;
        }
        handleOrderRefundSuccess(order, refund);
    }

    public void handleOrderRefundSuccess(Order order, OrderRefund refund) {
        log.info("处理订单退款成功,{}", JsonUtil.toJsonString(refund));
        Date now = new Date();
        // 订单已退总金额
        BigDecimal newRefundTotalAmount = NumberUtil.nullToZero(order.getRefundTotalAmount());
        log.info("订单已退总金额:{}", newRefundTotalAmount);
        // 订单退款是发货前的，是整单退
        List<OrderItem> itemList;
        OrderItem orderItem;
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(refund.getRefundMode())) {
            orderItem = null;
            itemList = orderItemRepository.getByOrderId(order.getOrderId());
            itemList.forEach(item -> {
                item.setReturnQuantity(item.getQuantity());
                item.setRefundPrice(item.getEnabledRefundAmount());
            });
            // 订单退款，金额时不可变更的，用订单实付金额重置
            newRefundTotalAmount = order.getTotalAmount();
        }
        // 如果为true，说明是整单退，否则是部分退
        else if (refund.getHasAllReturn()) {
            orderItem = null;
            itemList = orderItemRepository.getByOrderId(order.getOrderId());
            boolean onlyRefund = RefundModeEnum.GOODS_REFUND.getCode().equals(refund.getRefundMode());
            // 发货后的退货退款，在供应商确认收货时，已经退还了库存
            itemList.forEach(item -> {
                // 整单，仅退款时，不需要退数量，所以数量是0
                if (onlyRefund) {
                    item.setReturnQuantity(0L);
                } else {
                    item.setReturnQuantity(item.getQuantity());
                }
                item.setRefundPrice(item.getEnabledRefundAmount());
            });
            // 发货后的整单退，申请时可以修改金额，所以用申请的金额重置
            newRefundTotalAmount = refund.getAmount();
        }
        // 剩下的是单品售后
        else {
            itemList = null;
            orderItem = orderItemRepository.getById(refund.getOrderItemId());
            orderItem.setRefundPrice(orderItem.getRefundPrice().add(refund.getAmount()));
            orderItem.setReturnQuantity(orderItem.getReturnQuantity() + refund.getReturnQuantity());

            BigDecimal enableRefund = orderItem.getEnabledRefundAmount().subtract(refund.getAmount());
            if (enableRefund.compareTo(BigDecimal.ZERO) < 0) {
                enableRefund = BigDecimal.ZERO;
            }
            orderItem.setEnabledRefundAmount(enableRefund);

            // 单品售后，订单的已退金额累加
            newRefundTotalAmount = newRefundTotalAmount.add(refund.getAmount());
        }

        // 修改实收金额
        BigDecimal newActualPayAmount = order.getActualPayAmount().subtract(refund.getAmount());
        if (newActualPayAmount.compareTo(BigDecimal.ZERO) < 0) {
            newActualPayAmount = BigDecimal.ZERO;
        }
        order.setActualPayAmount(newActualPayAmount);
        order.setRefundTotalAmount(newRefundTotalAmount);
        order.setLastModifyTime(now);

        // 先保存相关数据，后续重新查询判断是否需要关闭订单，否则涉及当前条与其他数据，且有些需要累计，容易搞错
        // 先保存数据也可能存在一个问题是，并发时，当前订单的其他数据也已经修改了，导致逻辑的后续判断不正确，但一则基本不会有并发，二则不影响最终结果
        AtomicBoolean orderClosed = new AtomicBoolean(false);
        // 退款中是平台确认通过；退款成功是0元订单的情况，都需要执行逻辑
        if (RefundAuditStatusEnum.PLATFORM_PASS.getCode().equals(refund.getManagerConfirmStatus()) ||
                RefundAuditStatusEnum.REFUND_SUCCESS.getCode().equals(refund.getManagerConfirmStatus())) {
            TransactionHelper.doInTransaction(() -> {
                // 修改订单表
                orderRepository.updateById(order);
                log.info("修改订单表");
                // 订单子项根据售后的情况，可能改多条，可能改一条
                if (CollUtil.isNotEmpty(itemList)) {
                    orderItemRepository.updateBatchById(itemList);
                    log.info("批量修改订单子项表");
                }
                if (orderItem != null) {
                    orderItemRepository.updateById(orderItem);
                    log.info("修改订单子项表");
                }
                // 修改售后表
                refund.setManagerConfirmStatus(RefundAuditStatusEnum.REFUND_SUCCESS.getCode());
                refund.setManagerConfirmDate(now);
                refund.setRefundPayStatus(RefundPayStatusEnum.PAY_SUCCESS.getCode());
                refund.setStatus(RefundStatusEnum.REFUND_SUCCESS.getCode());
                orderRefundRepository.updateById(refund);
                log.info("修改售后表");
                // 关闭订单，先保存数据的原因是，关闭订单逻辑里面需要做一些判断，需要用到当前的数据，如果传入容易造成错误
                boolean closed = closeOrderIfNecessary(refund, order);
                log.info("是否关闭订单, closed={}", closed);
                orderClosed.set(closed);

                // Notice: 退款成功的逻辑，这里没有幂等，历史设计没有幂等字段
                try {
                    refundSettlement(order, refund);
                } catch (Exception e) {
                    log.warn("退款结果回调，重新计算结算金额异常, orderId={}, refundId={}", refund.getOrderId(), refund.getOrderId(), e);
                }
            });
        }

        // 回滚相关数据：库存、优惠券
        rollbackRelated(refund, order, itemList, orderItem, orderClosed.get());
        // 如果售后是关闭订单，则发送消息
        if (Boolean.TRUE.equals(orderClosed.get())) {
            // 发送消息
            orderMessagePublisher.sendOrderChangeMessage(order.getOrderId(), OrderMessageEventEnum.CLOSE_ORDER);
        }
        // 发送MQ消息，下游消费
        try {
            orderRefundMessagePublisher.sendOrderRefund(refund.getOrderId(), refund.getId(), RefundEventEnum.REFUND_SUCCESS);
        }
        catch (Exception e) {
            log.warn("售后成功发送MQ消息失败, refundId: {}", refund.getId(), e);
        }
    }

    /**
     * 如果订单已经全部退款，包括订单子项，关闭订单
     * <p>因为要支持同一个订单明细可以多次发起售后，所以子项是通过申请数量判断的是否全部退完</p>
     * <p>整单售后，如果供应商选择不退运费，或者单品发起的售后，对于订单而言，运费是无法退掉的，但订单状态还是设置为已关闭，但是不删除待结算数据，运费同样还是进行结算</p>
     *
     * @param order 需要处理的订单
     * <AUTHOR>
     */
    private boolean closeOrderIfNecessary(OrderRefund refund, Order order) {
        // 需求：Java版开始的时候，就确认了订单还有钱或者货没退完订单不关闭
        boolean shouldCloseOrder = orderHasAllRefund(refund);
        log.info("订单是否全部退款, shouldCloseOrder={}", shouldCloseOrder);
        // 订单的运费和税费也要没有才能关闭
        if (!shouldCloseOrder || order.getRefundTotalAmount().compareTo(order.getTotalAmount()) != 0) {
            log.info("订单未全部退款，orderId: {}, refundId: {}, totalAmount: {}, refundAmount: {}",
                    refund.getOrderId(), refund.getId(), order.getTotalAmount(), order.getRefundTotalAmount());
            return false;
        }
        order.setCloseReason("已退货/退款，订单自动关闭");
        order.setOrderStatus(OrderStatusEnum.CLOSED.getCode());
        order.setLastModifyTime(new Date());
        return orderRepository.updateById(order);
    }

    private boolean orderHasAllRefund(OrderRefund currentRefund) {
        // 发货前订单退款与整单退款，需要关闭订单
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(currentRefund.getRefundMode()) || Boolean.TRUE.equals(currentRefund.getHasAllReturn())) {
            log.info("发货前订单退款与整单退款，需要关闭订单");
            return true;
        }
        List<OrderRefund> refundList = orderRefundRepository.getByOrderId(currentRefund.getOrderId());
        boolean anyNotFinished = refundList.stream()
                .anyMatch(item -> !RefundAuditStatusEnum.SUPPLIER_REFUSE.getCode().equals(item.getSellerAuditStatus())
                        && !RefundAuditStatusEnum.PLATFORM_REFUSE.getCode().equals(item.getManagerConfirmStatus())
                        && !RefundAuditStatusEnum.REFUND_SUCCESS.getCode().equals(item.getManagerConfirmStatus())
                        && Boolean.FALSE.equals(item.getHasCancel()));
        log.info("订单是否已经全部退款, anyNotFinished={}", anyNotFinished);
        if (anyNotFinished) {
            return false;
        }
        // 单品售后，判断订单是否已经全部退款
        List<OrderItem> orderItemList = orderItemRepository.getByOrderId(currentRefund.getOrderId());
        // 一个明细可以多次售后
        /*Map<Long, List<OrderRefund>> orderItemRefundMap = refundList.stream()
                .collect(Collectors.groupingBy(OrderRefund::getOrderItemId));*/
        int finishOrderItemCount = 0;
        for (OrderItem orderItem : orderItemList) {
            // 钱和货都退完了，才算退完
            if (orderItem.getReturnQuantity() >= orderItem.getQuantity() &&
                    orderItem.getRefundPrice().compareTo(orderItem.getRealTotalPrice()) >= 0) {
                finishOrderItemCount = finishOrderItemCount + 1;
            } else {
                log.info("订单明细未退完，orderId: {}, orderItemId: {}, itemQuantity:{}, refundedQuantity: {}, realToalPrice:{}. refundPrice: {}",
                        currentRefund.getOrderId(), orderItem.getId(), orderItem.getQuantity(), orderItem.getReturnQuantity(),
                        orderItem.getRealTotalPrice(), orderItem.getRefundPrice());
            }
        }
        // 所有明细的售后都已完成，说明订单已经全部退款退货
        return finishOrderItemCount == orderItemList.size();
    }


    private void rollbackRelated(OrderRefund refund, Order order, List<OrderItem> itemList, OrderItem orderItem, boolean orderClosed) {
        log.info("售后回滚相关数据，售后数据: {}", JsonUtil.toJsonString(refund));
        // 发货前的订单退款、发货后的整单售后，退库存
        boolean orderRefund = RefundModeEnum.ORDER_REFUND.getCode().equals(refund.getRefundMode());
        // 发货前订单退款、发货后整单退货退款、发货后单品退货退款，都需要退库存，发货后退货退款的退后数量一定大于0
        // 发货后的退货退款，需要供应商确认收货，在确认收货阶段，已经退了库存，所以这里只需要退发货前订单退款的库存
        if (orderRefund && CollUtil.isNotEmpty(itemList)) {
            log.info("发货前订单退款回滚库存");
            // 如果是整单退，则订单所有明细都要回滚库存
            // 需要回滚的库存
            List<DecreaseStockBo> returnList = itemList.stream()
                    .map(item -> {
                        DecreaseStockBo bo = new DecreaseStockBo();
                        bo.setProductId(item.getProductId());
                        bo.setSkuId(item.getSkuId());
                        bo.setQuantity(item.getQuantity());
                        bo.setShopId(item.getShopId());
                        bo.setOrderId(item.getOrderId());
                        return bo;
                    })
                    .collect(Collectors.toList());
            stockRemoteService.rollbackStockBySku(StockUpdateTypeEnum.RETURN_STOCK, String.valueOf(refund.getId()), returnList);

            // 限时购的订单明细只有一条记录
            if (OrderTypeEnum.FLASH_SALE.getCode().equals(order.getOrderType())){
                log.info("回滚限时购库存");
                // 前面因为orderItem单品售后时需要设置更改数据，所以发货前订单退款没有赋值，这里重新查询
                OrderItem item = orderItemRepository.getByOrderId(order.getOrderId()).get(0);
                promotionRemoteService.rollbackFlashSaleStock(order.getOrderId(), item.getFlashSaleId(), item.getSkuId(),
                        refund.getId(), item.getQuantity().intValue());
            }
        }

        // 需求描述：【如果订单使用了优惠券则订单款和货全部退完订单关闭后优惠券退回，否则优惠券不退】
        // 全部退完，包括运费和税费
        if (orderClosed) {
            log.info("回滚优惠券");
            promotionRemoteService.rollbackCoupon(order.getUserId(), Collections.singletonList(order.getOrderId()));
        }
    }


    /**
     * 退款时，重新计算结算金额
     *
     * @param order
     * @param currentRefund
     * <AUTHOR>
     */
    private void refundSettlement(Order order, OrderRefund currentRefund) {
        String orderId = order.getOrderId();
        Date now = new Date();
        PendingSettlementOrder pendingSettlementOrder = pendingSettlementOrderRepository.getByOrderId(orderId);
        if (pendingSettlementOrder == null) {
            log.warn("订单结算，订单待结算数据不存在, orderId: {}", orderId);
            return;
        }
        BigDecimal returnPlatformCommission = BigDecimal.ZERO;
        BigDecimal refundTotalAmount = BigDecimal.ZERO;
        // 订单的所有退款成功的，前面先修改了数据，这里查询能查到本次的记录
        List<OrderRefund> refundList = getRefundSuccessList(orderId);
        for (OrderRefund refund : refundList) {
            returnPlatformCommission = returnPlatformCommission.add(refund.getReturnPlatCommission());
            refundTotalAmount = refundTotalAmount.add(refund.getAmount());
        }
        if (OrderStatusEnum.CLOSED.getCode().equals(order.getOrderStatus())) {
            pendingSettlementOrder.setOrderFinishTime(now);
        }
        pendingSettlementOrder.setPlatCommissionReturn(returnPlatformCommission);
        pendingSettlementOrder.setRefundAmount(refundTotalAmount);
        pendingSettlementOrder.setRefundDate(now);

        // 平台佣金=平台佣金-退的平台佣金
        BigDecimal realReturnPlatformCommission = currentRefund.getReturnPlatCommission();
        // 防止溢出，多次退款四舍五入的影响
        if (currentRefund.getReturnPlatCommission().compareTo(pendingSettlementOrder.getPlatCommission()) > 0) {
            realReturnPlatformCommission = pendingSettlementOrder.getPlatCommission();
            pendingSettlementOrder.setPlatCommission(BigDecimal.ZERO);
        } else {
            BigDecimal newPlatCommission = pendingSettlementOrder.getPlatCommission().subtract(currentRefund.getReturnPlatCommission());
            pendingSettlementOrder.setPlatCommission(newPlatCommission);
        }
        if (order.getRefundTotalAmount().compareTo(order.getTotalAmount()) == 0 &&
                order.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 总的退款金额
            BigDecimal amount = refundList.stream().map(OrderRefund::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            if (amount.compareTo(order.getTotalAmount()) == 0) {
                // 说明可退金额已全退了，防止溢出，多次退款四舍五入的影响,这里设置为0
                pendingSettlementOrder.setPlatCommission(BigDecimal.ZERO);
            }
        }

        BigDecimal adpayAmountBefore = pendingSettlementOrder.getChannelAmount();
        BigDecimal adapayAmount = calculateAdpayAmount(order);
        pendingSettlementOrder.setChannelAmount(adapayAmount);

        //本次退还汇付手续费
        BigDecimal adpayAmountReturn = adpayAmountBefore.subtract(adapayAmount);
        //计算剩余可分账的金额
        BigDecimal adpayCommission = order.getActualPayAmount().subtract(pendingSettlementOrder.getChannelAmount());

        if (pendingSettlementOrder.getPlatCommission().compareTo(adpayCommission) >= 0) {
            pendingSettlementOrder.setPlatCommission(adpayCommission);
        }

        //结算金额-本次退的金额+本次返回的平台佣金
        BigDecimal settlementAmount = pendingSettlementOrder.getSettlementAmount()
                .subtract(currentRefund.getAmount())
                .add(realReturnPlatformCommission)
                .add(adpayAmountReturn);
        if (settlementAmount.compareTo(BigDecimal.ZERO) < 0) {
            settlementAmount = BigDecimal.ZERO;
        }
        pendingSettlementOrder.setSettlementAmount(settlementAmount);
        pendingSettlementOrderRepository.updateById(pendingSettlementOrder);
    }

    private BigDecimal calculateAdpayAmount(Order order) {
        if (order.getActualPayAmount().compareTo(BigDecimal.ZERO) <= 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal adpyCharge = BigDecimal.ZERO;
        // 是否部分分账
        OrderPayRecord successPayRecord = orderPayRecordRepository.getByOrderIdAndOutTransIdAndStatus(order.getOrderId(), order.getGatewayOrderId(), PayStatusEnum.PAY_SUCCESS.getCode());
        String batchNo = successPayRecord.getBatchNo();
        // 合并支付可能会有多条记录
        List<OrderPayRecord> allPays = orderPayRecordRepository.getByBatchNoAndStatus(batchNo, PayStatusEnum.PAY_SUCCESS.getCode());
        if (order.getPayment() != null && PayMethodEnum.COMPANY_BANK.getCode().equals(order.getPayment())) {
            return calculateAdpayAmountForCompany(order, allPays);
        }
        if (allPays.size() <= 0) {
            BigDecimal baseCharge = NumberUtil.mul(order.getTotalAmount(), order.getSettlementCharge());
            return NumberUtil.div(order.getActualPayAmount(), order.getTotalAmount()).multiply(baseCharge);
        }
        List<String> orderIds = allPays.stream().map(OrderPayRecord::getOrderId).collect(Collectors.toList());
        List<Order> orders = orderRepository.getByOrderIdList(orderIds);
        // 原交易额
        BigDecimal totalAmount = orders.stream().map(Order::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        // 手续费总额
        BigDecimal totalCharge = NumberUtil.mul(totalAmount, order.getSettlementCharge());
        return NumberUtil.div(order.getActualPayAmount(), totalAmount).multiply(totalCharge);
    }

    private BigDecimal calculateAdpayAmountForCompany(Order order, List<OrderPayRecord> allPays) {
        log.info("企业网银支付订单计算渠道金额");
        if (allPays.size() == 1) {
            return BigDecimal.TEN;
        }
        // batchSuccessList 根据时间降序排序
        allPays.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
        if (!allPays.get(0).getOrderId().equals(order.getOrderId())) {
            List<String> orderIds = allPays.stream().map(OrderPayRecord::getOrderId).collect(Collectors.toList());
            List<Order> orders = orderRepository.getByOrderIdList(orderIds);
            // 原交易金额
            BigDecimal totalAmount = orders.stream().map(Order::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal adpyCharge = NumberUtil.div(order.getActualPayAmount(), totalAmount).multiply(BigDecimal.TEN);
            log.info("订单{}分账金额{}", order.getOrderId(), adpyCharge);
            return adpyCharge;
        }
        List<String> otherOrderIdList = allPays.stream()
                .map(OrderPayRecord::getOrderId)
                .filter(orderId -> !orderId.equals(order.getOrderId()))
                .collect(Collectors.toList());
        BigDecimal penAdadyAmount = BigDecimal.ZERO;
        List<String> penOrderIds = new ArrayList<>();
        List<PendingSettlementOrder> pends = pendingSettlementOrderRepository.listByOrderIdList(otherOrderIdList);
        if (CollUtil.isNotEmpty(pends)) {
            for (PendingSettlementOrder pend : pends) {
                // 待结算订单手续费
                penAdadyAmount = penAdadyAmount.add(pend.getChannelAmount());
                penOrderIds.add(pend.getOrderId());
            }
        }
        // 已结算订单
        List<String> detailOrderIds = otherOrderIdList.stream().filter(orderId -> !penOrderIds.contains(orderId)).collect(Collectors.toList());
        if (CollUtil.isEmpty(detailOrderIds)) {
            return BigDecimal.TEN.subtract(penAdadyAmount);
        }
        List<AccountDetail> accountDetailList = accountDetailRepository.listByOrderIdList(detailOrderIds);
        if (CollUtil.isEmpty(accountDetailList)) {
            return BigDecimal.TEN.subtract(penAdadyAmount);
        }
        BigDecimal acctDetailSum = accountDetailList.stream().map(AccountDetail::getChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        penAdadyAmount = penAdadyAmount.add(acctDetailSum);
        return BigDecimal.TEN.subtract(penAdadyAmount);
    }

    private void handleRefundForExceptionOrder(RefundResultNotifyBo refundResult) {
        ExceptionOrder updateOrder = new ExceptionOrder();
        if (ReverseStateEnums.REVERSE_SUCCESS.getStatus().equals(refundResult.getPayStatus())) {
            updateOrder.setRefundStatus(ExceptionOrderRefundStatusEnum.REFUNDED.getCode());
        } else {
            updateOrder.setRefundStatus(ExceptionOrderRefundStatusEnum.REFUND_FAIL.getCode());
            updateOrder.setRefundFailReason(refundResult.getErrorMessage());
        }
        exceptionOrderRepository.updateRefundInfoByBatchNo(refundResult.getRefundId(), updateOrder);
    }

    private void handleRefundForCompensationOrder(RefundResultNotifyBo refundResult) {
        String refundId = refundResult.getRefundId();
        OrderRefundRecord orderRefundRecord = orderRefundRecordRepository.getByRefundNo(refundId);
        if (null == orderRefundRecord && !OrderRefundRecordStatusEnum.REFUNDING.getCode().equals(orderRefundRecord.getRefundStatus())) {
            log.info("超支退款结果回调,退款记录不存在或状态已经非退款中,{}", refundResult);
            return;
        }
        if (ReverseStateEnums.REVERSE_SUCCESS.getStatus().equals(refundResult.getPayStatus())) {
            // 如果成功了
            orderRefundRecord.setRefundStatus(OrderRefundRecordStatusEnum.REFUND_SUCCESS.getCode());
        } else if (ReverseStateEnums.REVERSE_ERROR.getStatus().equals(refundResult.getPayStatus())) {
            // 如果失败了
            orderRefundRecord.setRefundStatus(OrderRefundRecordStatusEnum.REFUND_FAIL.getCode());
            orderRefundRecord.setErrMsg(refundResult.getErrorMessage());
        }
        orderRefundRecordService.updateObj(orderRefundRecord);
    }

    public List<OrderRefund> getRefundSuccessList(String orderId) {
        List<OrderRefund> refundList = orderRefundRepository.getByOrderId(orderId);
        return Optional.ofNullable(refundList).map(list ->
                        list.stream()
                                // 未取消
                                .filter(refund -> RefundStatusEnum.REFUND_SUCCESS.getCode().equals(refund.getStatus()))
                                .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

}
