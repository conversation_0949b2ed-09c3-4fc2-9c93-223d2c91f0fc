package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description: 汇付创建企业用户对象DTO
 */
@Data
public class AdaPayCorpMemberDto {

    /**
     * 汇付支付 app_id
     */
    private String appId;
    /**
     * 用户ID
     */
    private String memberId;

    /**
     * 请求订单号
     */
    private String orderNo;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 省份编码
     */
    private String provCode;

    /**
     * Adapay接口功能号，该接口填固定值：corp_members.update
     */
    private String adapayFuncCode;

    /**
     * 地区编码
     */
    private String areaCode;

    /**
     * 统一社会信用码
     */
    private String socialCreditCode;

    /**
     * 统一社会信用证有效期
     */
    private String socialCreditCodeExpires;

    /**
     * 经营范围
     */
    private String businessScope;
    /**
     * 法人姓名
     */
    private String legalPerson;
    /**
     * 法人身份证号码
     */
    private String legalCertId;
    /**
     * 法人身份证有效期
     */
    private String legalCertIdExpires;

    /**
     * 法人手机号
     */
    private String legalMp;
    /**
     * 企业地址
     */
    private String address;
    /**
     * 邮编
     */
    private String zipCode;
    /**
     * 企业电话
     */
    private String telphone;
    /**
     * 企业邮箱
     */
    private String email;

    /**
     * 银行编码，详见附录 银行代码，银行账户类型对公时，必填
     */
    private String bankCode;

    /**
     * 银行账户类型：1-对公；2-对私，如果需要自动开结算账户，本字段必填
     */
    private String bankAcctType;

    /**
     * 银行卡号，如果需要自动开结算账户，本字段必填
     */
    private String cardNo;

    /**
     * 银行卡对应的户名，如果需要自动开结算账户，本字段必填
     */
    private String cardName;

    /**
     * 异步通知地址，url为http/https路径，服务器POST回调，URL 上请勿附带参数
     */
    private String notifyUrl;
}
