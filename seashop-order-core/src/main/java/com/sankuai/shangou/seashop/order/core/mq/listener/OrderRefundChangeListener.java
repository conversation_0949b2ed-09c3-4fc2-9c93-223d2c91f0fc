package com.sankuai.shangou.seashop.order.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.common.enums.MqErrorDataBizTypeEnum;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.core.service.assit.MqErrorDataAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.OrderRefundMessageAssist;

import cn.hutool.crypto.digest.MD5;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_ORDER_REFUND,
//        group = MafkaConst.GROUP_ORDER_REFUND)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_REFUND + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_REFUND + "_${spring.profiles.active}")
public class OrderRefundChangeListener implements RocketMQListener<MessageExt> {

    @Resource
    private OrderRefundMessageAssist orderRefundMessageAssist;
    @Resource
    private MqErrorDataAssist mqErrorDataAssist;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
//        String body = (String) message.getBody();
//        log.info("【mafka消费】【订单售后】消息内容为: {}", body);
//        OrderRefundMessage refundMessage = JsonUtil.parseObject(body, OrderRefundMessage.class);
//        // 售后消息处理，不同的事件会有不同的处理器
//        orderRefundMessageAssist.handleRefundMessage(refundMessage);
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【订单售后】消息内容为: {}", body);
        // 售后消息处理，不同的事件会有不同的处理器
        OrderRefundMessage refundMessage = null;
        try {
            refundMessage = JsonUtil.parseObject(body, OrderRefundMessage.class);
        }
        catch (Exception e) {
            log.error("【mafka消费】【订单售后】消息转换失败", e);
            String traceId = message.getMsgId();
            mqErrorDataAssist.saveErrorData(MqErrorDataBizTypeEnum.REFUND_CHANGE, MD5.create().digestHex(body), message.getMsgId(), traceId,
                e.getMessage(), body);
            return;
        }
        try {
            orderRefundMessageAssist.handleRefundMessage(refundMessage);
        }
        catch (Exception e) {
            log.error("【mafka消费】【订单售后】处理失败", e);
            String traceId = message.getMsgId();
            mqErrorDataAssist.saveErrorData(MqErrorDataBizTypeEnum.REFUND_CHANGE, String.valueOf(refundMessage.getRefundId()), message.getMsgId(),
                traceId
                , e.getMessage(), body);
            throw new RuntimeException(e);
        }
    }
}
