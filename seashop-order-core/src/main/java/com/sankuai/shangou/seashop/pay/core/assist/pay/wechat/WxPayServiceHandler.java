package com.sankuai.shangou.seashop.pay.core.assist.pay.wechat;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Optional;

import com.github.binarywang.wxpay.bean.ecommerce.PartnerTransactionsNotifyResult;
import com.github.binarywang.wxpay.bean.ecommerce.PartnerTransactionsQueryRequest;
import com.github.binarywang.wxpay.bean.ecommerce.PartnerTransactionsRequest;
import com.github.binarywang.wxpay.bean.ecommerce.PartnerTransactionsResult;
import com.github.binarywang.wxpay.bean.ecommerce.RefundQueryResult;
import com.github.binarywang.wxpay.bean.ecommerce.RefundsRequest;
import com.github.binarywang.wxpay.bean.ecommerce.RefundsResult;
import com.github.binarywang.wxpay.bean.ecommerce.SignatureHeader;
import com.github.binarywang.wxpay.bean.ecommerce.TransactionsResult;
import com.github.binarywang.wxpay.bean.ecommerce.enums.TradeTypeEnum;
import com.github.binarywang.wxpay.bean.notify.OriginNotifyResponse;
import com.github.binarywang.wxpay.bean.notify.WxPayNotifyV3Response;
import com.github.binarywang.wxpay.service.EcommerceService;
import com.github.binarywang.wxpay.service.impl.EcommerceServiceImpl;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.mode.AbstractWxPayModeProxy;
import com.sankuai.shangou.seashop.pay.dao.core.model.WxPayConfigModel;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.PayPaymentCreateExpendDto;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/09/02 9:33
 */
@Slf4j
public class WxPayServiceHandler extends AbstractWxPayModeProxy {

    @Override
    public PayResult pay(PayParam payParam) {
        log.info("【发起支付-微信支付-服务商模式】调用微信支付请求参数{}", payParam);
        PayResult payResult = new PayResult();
        WxPayConfigModel payConfig = (WxPayConfigModel) payParam.getPayConfig();

        EcommerceService ecommerceService = new EcommerceServiceImpl(wxPayService);

        // 创建支付参数
        PartnerTransactionsRequest request = new PartnerTransactionsRequest();
        request.setSpAppid(payConfig.getServiceAppId());
        request.setSpMchid(payConfig.getServiceMchId());
        request.setSubAppid(appId);
        request.setSubMchid(payConfig.getMchId());
        request.setDescription(payParam.getGoodsTitle());
        request.setOutTradeNo(String.valueOf(payParam.getPayId()));

        request.setDescription(payParam.getGoodsTitle());
        request.setNotifyUrl(payConfig.getCallBackUrl());

        // 设置支付金额
        PartnerTransactionsRequest.Amount amount = new PartnerTransactionsRequest.Amount();
        amount.setTotal(payParam.getPayAmount().multiply(new BigDecimal(100)).intValue());
        request.setAmount(amount);

        // 设置支付人
        Optional<PayPaymentCreateExpendDto> expend = Optional.ofNullable(payParam.getExpend());
        PartnerTransactionsRequest.Payer payer = new PartnerTransactionsRequest.Payer();
        payer.setSubOpenid(expend.map(PayPaymentCreateExpendDto::getOpenId).orElse(null));
        request.setPayer(payer);

        if (PaymentTypeEnum.WECHAT_H5.equals(paymentType)) {
            // 场景 h5必填
            PartnerTransactionsRequest.SceneInfo sceneInfo = new PartnerTransactionsRequest.SceneInfo();
            sceneInfo.setPayerClientIp(expend.map(PayPaymentCreateExpendDto::getClientIp).orElse("127.0.0.1"));
            PartnerTransactionsRequest.H5Info h5Info = new PartnerTransactionsRequest.H5Info();
            h5Info.setType("Wap");
            sceneInfo.setH5Info(h5Info);
            request.setSceneInfo(sceneInfo);
        }

        try {
            log.info("【发起支付-微信支付-服务商模式】调用微信支付请求参数{}", JsonUtil.toJsonString(request));
            TransactionsResult resp = ecommerceService.partner(getTradeType(), request);

            payResult.setRemoteReq(request);
            payResult.setRemoteResp(resp);
            payResult.setSuccess(true);
        } catch (Exception e) {
            log.error("【发起支付-微信支付-服务商模式】调用微信支付失败", e);
            payResult.setErrorMsg(e.getMessage());
            payResult.setSuccess(false);
        }
        return payResult;
    }

    @Override
    public PayNotifyResult notifyPay(String payData) {
        try {
            EcommerceService ecommerceService = new EcommerceServiceImpl(wxPayService);

            // 解密回调参数
            OriginNotifyResponse notifyResponse = JSONUtil.toBean(payData, OriginNotifyResponse.class);
            Map<String, String> header = wxPayUtil.getNotifyPaySignatureHeader();
            PartnerTransactionsNotifyResult wxPayRefundNotifyV3Result =
                    ecommerceService.parsePartnerNotifyResult(wxPayUtil.jsonStrSort(notifyResponse), JsonUtil.copy(header, SignatureHeader.class));
            PartnerTransactionsResult result = wxPayRefundNotifyV3Result.getResult();
            log.info("【微信支付回调-服务商模式】{}", JsonUtil.toJsonString(result));

            PayNotifyResult payNotifyResult = new PayNotifyResult();
            payNotifyResult.setChannelPayId(result.getTransactionId());
            payNotifyResult.setTradeState(result.getTradeState());
            payNotifyResult.setPayId(result.getOutTradeNo());
            payNotifyResult.setPayTime(wxPayUtil.parseWxDate(result.getSuccessTime()));
            payNotifyResult.setSuccess(wxPayUtil.transTradeState(result.getTradeState()));
            payNotifyResult.setThirdResult(WxPayNotifyV3Response.success("回调成功"));

            return payNotifyResult;
        } catch (Exception e) {
            log.error("【微信支付回调-普通模式】解析微信支付回调异常", e);
            throw new BusinessException("解析微信支付回调异常");
        }
    }

    @Override
    public PayConfirmResult payConfirm(PayConfirmParam confirmParam) {
        try {
            EcommerceService ecommerceService = new EcommerceServiceImpl(wxPayService);

            log.info("【发起支付-微信支付-服务商模式】支付确认, param: {}", confirmParam);

            PartnerTransactionsQueryRequest request = new PartnerTransactionsQueryRequest();
            request.setSpMchid(payConfig.getServiceMchId());
            request.setSubMchid(payConfig.getMchId());
            request.setTransactionId(confirmParam.getChannelPayId());
            request.setOutTradeNo(confirmParam.getPayId());

            PartnerTransactionsResult result = ecommerceService.queryPartnerTransactions(request);

            PayConfirmResult confirmResult = new PayConfirmResult();
            confirmResult.setPayId(result.getOutTradeNo());
            confirmResult.setChannelPayId(result.getTransactionId());
            confirmResult.setTradeState(result.getTradeState());
            confirmResult.setPayTime(wxPayUtil.parseWxDate(result.getSuccessTime()));
            confirmResult.setSuccess(wxPayUtil.transTradeState(confirmResult.getTradeState()));
            return confirmResult;
        } catch (Exception e) {
            log.error("【发起支付-微信支付-服务商模式】支付确认失败", e);
            throw new BusinessException("支付确认失败");
        }
    }

    @Override
    public RefundResult refund(RefundParam refundParam) {
        log.info("【发起退款-微信支付-服务商模式】, param： {}", JsonUtil.toJsonString(refundParam));

        EcommerceService ecommerceService = new EcommerceServiceImpl(wxPayService);

        RefundResult refundResult = new RefundResult();

        try {

            RefundsRequest request = new RefundsRequest();
            request.setSubMchid(payConfig.getMchId());
            request.setSpAppid(payConfig.getServiceAppId());
            request.setSubAppid(appId);
            request.setTransactionId(refundParam.getChannelPayId());
            request.setOutRefundNo(refundParam.getReverseId());
            request.setNotifyUrl(payConfig.getRefundCallBackUrl());
            // request.setReason(refundParam.getReason());

            RefundsRequest.Amount amount = RefundsRequest.Amount.builder().build();
            amount.setRefund(refundParam.getReverseAmount().multiply(new BigDecimal(100)).intValue());
            amount.setTotal(refundParam.getOrderAmount().multiply(new BigDecimal(100)).intValue());
            amount.setCurrency("CNY");
            request.setAmount(amount);

            RefundsResult refunds = ecommerceService.refunds(request);

            refundResult.setChannelRefundId(refunds.getRefundId());
            refundResult.setRemoteResp(refunds);
            refundResult.setSuccess(true);
        } catch (Exception e) {
            log.error("【发起退款-微信支付-服务商模式】 发起退款失败", e);
            refundResult.setSuccess(false);
            refundResult.setErrorMsg(e.getMessage());
        }

        return refundResult;
    }

    @Override
    public RefundConfirmResult refundConfirm(RefundConfirmParam refundConfirmParam) {
        log.info("【发起退款-微信支付-服务商模式】退款确认, param: {}", refundConfirmParam);
        try {
            EcommerceService ecommerceService = new EcommerceServiceImpl(wxPayService);

            RefundQueryResult refundQueryResult = ecommerceService.queryRefundByOutRefundNo(payConfig.getMchId(), refundConfirmParam.getReverseId());
            log.info("【发起退款-微信支付-服务商模式】退款确认结果: {}", JsonUtil.toJsonString(refundQueryResult));

            RefundConfirmResult refundConfirmResult = new RefundConfirmResult();
            refundConfirmResult.setChannelRefundId(refundQueryResult.getRefundId());
            refundConfirmResult.setReverseId(refundQueryResult.getOutRefundNo());
            refundConfirmResult.setRefundStatus(refundQueryResult.getStatus());
            refundConfirmResult.setRefundDate(wxPayUtil.parseWxDate(refundQueryResult.getSuccessTime()));
            refundConfirmResult.setSuccess(wxPayUtil.transRefundState(refundQueryResult.getStatus()));
            return refundConfirmResult;
        }
        catch (Exception e) {
            log.info("【发起退款-微信支付-服务商模式】退款确认失败", e);
            throw new BusinessException("退款确认失败");
        }
    }

    @Override
    public RefundNotifyResult notifyRefund(String refundData) {
        try {
            EcommerceService ecommerceService = new EcommerceServiceImpl(wxPayService);

            // 解密回调参数
            OriginNotifyResponse notifyResponse = JSONUtil.toBean(refundData, OriginNotifyResponse.class);
            Map<String, String> header = wxPayUtil.getNotifyPaySignatureHeader();
            com.github.binarywang.wxpay.bean.ecommerce.RefundNotifyResult result =
                    ecommerceService.parseRefundNotifyResult(wxPayUtil.jsonStrSort(notifyResponse), JsonUtil.copy(header, SignatureHeader.class));
            log.info("【微信退款回调-服务商模式】{}", JsonUtil.toJsonString(result));

            RefundNotifyResult refundNotifyResult = new RefundNotifyResult();
            refundNotifyResult.setChannelRefundId(result.getRefundId());
            refundNotifyResult.setReverseId(result.getOutRefundNo());
            refundNotifyResult.setRefundStatus(result.getRefundStatus());
            refundNotifyResult.setRefundDate(wxPayUtil.parseWxDate(result.getSuccessTime()));
            refundNotifyResult.setSuccess(wxPayUtil.transRefundState(result.getRefundStatus()));
            refundNotifyResult.setThirdResult(WxPayNotifyV3Response.success("回调成功"));

            return refundNotifyResult;
        } catch (Exception e) {
            log.error("【微信退款回调-服务商模式】解析微信支付回调异常", e);
            throw new BusinessException("解析微信退款回调异常");
        }
    }

    private TradeTypeEnum getTradeType() {
        switch (paymentType) {
            case WECHAT_APPLET:
                return TradeTypeEnum.JSAPI;
            case WECHAT_H5:
                return TradeTypeEnum.MWEB;
            case WECHAT_NATIVE:
                return TradeTypeEnum.NATIVE;
            default:
                throw new BusinessException("不支持的支付类型");
        }
    }

}
