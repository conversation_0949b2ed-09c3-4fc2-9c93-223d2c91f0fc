package com.sankuai.shangou.seashop.order.core.statemachine.action;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.cola.statemachine.Action;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.dto.ChangeFiled;
import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderMessagePublisher;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundService;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderBizAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.opLog.ChangeFieldDesc;
import com.sankuai.shangou.seashop.order.core.service.assit.opLog.OperationLogAssist;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderExpressBo;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderEvent;
import com.sankuai.shangou.seashop.order.core.statemachine.context.OrderDeliveryContext;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderWayBill;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderWayBillRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.pay.core.service.WxShippingBiz;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.UploadShippingRequest;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.Contact;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.OrderKey;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.Payer;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.ShippingItem;
import com.sankuai.shangou.seashop.user.thrift.auth.AuthUserFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopShipperQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryShopShipperRespDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 订单发货
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class DeliveryAction extends BaseAction implements Action<OrderStatusEnum, OrderEvent, OrderContext> {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderWayBillRepository orderWayBillRepository;
    @Resource
    private OrderBizAssist orderBizAssist;
    @Resource
    private OrderMessagePublisher orderMessagePublisher;
    @Resource
    private OrderRefundService orderRefundService;
    @Resource
    private OperationLogAssist operationLogAssist;
    @Value("${wx.send:false}")
    private Boolean wxSend;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private WxShippingBiz wxShippingBiz;
    @Resource
    private AuthUserFeign authUserFeign;
    @Resource
    private ShopShipperQueryFeign shopShipperQueryFeign;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void executeStatusChange(OrderContext context, Order dbOrder) {
        OrderDeliveryContext deliveryContext = (OrderDeliveryContext) context;
        String orderId = deliveryContext.getOrderId();
        // 保存物流信息，需要物流才保存
        List<OrderWayBill> wayBillList = null;
        if (Boolean.TRUE.equals(deliveryContext.getNeedExpress())) {
            Date now = new Date();
            wayBillList = deliveryContext.getExpressList().stream().map(express -> {
                OrderWayBill wayBill = new OrderWayBill();
                wayBill.setOrderId(orderId);
                wayBill.setExpressCompanyName(express.getExpressCompanyName());
                wayBill.setShipOrderNumber(express.getShipOrderNumber());
                wayBill.setExpressCompanyCode(express.getExpressCompanyCode());
                wayBill.setCreateTime(now);
                wayBill.setUpdateTime(now);
                return wayBill;
            }).collect(Collectors.toList());
            log.info("【订单】发货, orderId={}, 保存物流信息, wayBillList={}", orderId, JsonUtil.toJsonString(wayBillList));
        }
        // 修改订单状态
        Order updateOrder = new Order();
        updateOrder.setOrderId(orderId);
        updateOrder.setOrderStatus(OrderStatusEnum.UNDER_RECEIVE.getCode());
        updateOrder.setShippingDate(new Date());
        log.info("【订单】发货, orderId={}, 待更新的数据为: {}", orderId, JsonUtil.toJsonString(updateOrder));

        final List<OrderWayBill> finalWayBillList = wayBillList;
        TransactionHelper.doInTransaction(() -> {
            if (CollUtil.isNotEmpty(finalWayBillList)) {
                orderWayBillRepository.saveBatch(finalWayBillList);
            }
            orderRepository.updateByOrderId(orderId, updateOrder);
            // 保存订单操作日志
            orderBizAssist.addOrderOperationLog(orderId, context.getUserName(), "供应商发货");
        });
        // 判断微信发货开关
        log.info("【订单】发货, orderId={}, 微信发货开关: {}", orderId, wxSend);
        if (wxSend) {
            // 查询订单是否是微信支付的
            Order order = orderRepository.getById(orderId);
            Long shopId = order.getShopId();
            Map<Long, List<QueryShopShipperRespDto>> map = ThriftResponseHelper.executeThriftCall(() -> shopShipperQueryFeign.queryBatchShopShipperList(Collections.singletonList(shopId)));
            List<QueryShopShipperRespDto> shipperList = map.get(shopId);
            if (CollectionUtils.isEmpty(shipperList)) {
                throw new BusinessException("店铺没有配置发货地址");
            }
            List<QueryShopShipperRespDto> thisShopDefaultSendShipper = shipperList.stream().filter(QueryShopShipperRespDto::getDefaultSendGoodsFlag).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(thisShopDefaultSendShipper)) {
                throw new BusinessException("店铺没有配置默认发货地址");
            }
            QueryShopShipperRespDto shopShipperSendShipper = thisShopDefaultSendShipper.get(0);
            OrderPayRecord orderPayRecord = orderPayRecordRepository.getLatestByOrderIdAndStatus(orderId, PayStatusEnum.PAY_SUCCESS.getCode());
            List<OrderExpressBo> expressList = deliveryContext.getExpressList();
            if (orderPayRecord != null && (orderPayRecord.getPayMethod().equals(PayMethodEnum.WECHAT_APPLET.getCode()) || orderPayRecord.getPayMethod().equals(PayMethodEnum.WECHAT_H5.getCode()) || orderPayRecord.getPayMethod().equals(PayMethodEnum.WECHAT_NATIVE.getCode()) || orderPayRecord.getPayMethod().equals(PayMethodEnum.WECHAT_JS.getCode()))) {
                // 发送微信消息
                LoginBaseDto loginBaseDto = new LoginBaseDto();
                loginBaseDto.setId(order.getUserId());
                LoginMemberDto memberDto = ThriftResponseHelper.executeThriftCall(() -> authUserFeign.getMemberUser(loginBaseDto));
                UploadShippingRequest request = new UploadShippingRequest();
                OrderKey orderKey = new OrderKey();
                orderKey.setOut_trade_no(order.getGatewayOrderId() + "");
                request.setDelivery_mode(1);
                request.setOrder_key(orderKey);
                // 判断订单是什么情况 需要物流发快递  无需物流发虚拟
                if (Boolean.TRUE.equals(deliveryContext.getNeedExpress())) {
                    request.setLogistics_type(1);
                    List<ShippingItem> shippingList = expressList.stream().map(express -> {
                        ShippingItem shippingItem = new ShippingItem();
                        Contact contact = new Contact();
                        contact.setConsignor_contact(shopShipperSendShipper.getTelPhone());
                        contact.setReceiver_contact(express.getCellPhone());
                        shippingItem.setContact(contact);
                        shippingItem.setTracking_no(express.getShipOrderNumber());
                        shippingItem.setExpress_company(express.getExpressCompanyCode());
                        shippingItem.setItem_desc("订单号【" + orderId + "】发货");
                        return shippingItem;
                    }).collect(Collectors.toList());
                    request.setShipping_list(shippingList);
                } else {
                    request.setLogistics_type(3);
                    List<ShippingItem> shippingList = Lists.newArrayList();
                    ShippingItem sitem = new ShippingItem();
                    sitem.setItem_desc("订单号【" + orderId + "】发货");
                    shippingList.add(sitem);
                    request.setShipping_list(shippingList);
                }
                Payer payer = new Payer();
                switch (PaymentTypeEnum.getByType(orderPayRecord.getPayMethod())) {
                    case WECHAT_APPLET:
                        payer.setOpenid(memberDto.getWeiXinOpenId());
                        break;
                    case WECHAT_H5:
                        payer.setOpenid(memberDto.getWxmpOpenId());
                        break;
                    case WECHAT_JS:
                        payer.setOpenid(memberDto.getWxmpOpenId());
                        break;
                    case WECHAT_NATIVE:
                        payer.setOpenid(memberDto.getWeiXinOpenId());
                        break;
                }
                request.setPayer(payer);
                wxShippingBiz.uploadShipping(request);
            }
        }
        // 发送通知
        orderMessagePublisher.sendOrderChangeMessage(dbOrder.getOrderId(), OrderMessageEventEnum.DELIVERY_ORDER);
        // 记录接口操作日志，这个目前的aop日志切面不支持
        try {
            List<ChangeFiled> orderChanged = operationLogAssist.buildChangeList(dbOrder, updateOrder, ChangeFieldDesc.ORDER_SELLER_DELIVERY_ORDER);
            List<ChangeFiled> billChanged = operationLogAssist.buildChangeListForUpdateExpress(null, wayBillList, ChangeFieldDesc.ORDER_SELLER_UPDATE_WAYBILL_BILL, ChangeFieldDesc.ORDER_SELLER_UPDATE_WAYBILL_ORDER, orderId);
            orderChanged.addAll(billChanged);
            operationLogAssist.log(ExaminModelEnum.ORDER, ExaProEnum.MODIFY, "供应商发货", JsonUtil.toJsonString(orderChanged), deliveryContext.getUserId(), deliveryContext.getShopId(), context.getUserName());
        } catch (Exception e) {
            log.error("【供应商改价】记录接口操作日志失败", e);
        }
    }

    @Override
    protected void validateBizData(OrderContext context, Order dbOrder) {
        if (!OrderStatusEnum.UNDER_SEND.getCode().equals(dbOrder.getOrderStatus())) {
            throw new BusinessException("只有待发货状态的订单才能发货");
        }
        OrderDeliveryContext deliveryContext = (OrderDeliveryContext) context;
        if (!dbOrder.getShopId().equals(deliveryContext.getShopId())) {
            throw new BusinessException("店铺ID不匹配");
        }
        List<OrderRefund> refundList = orderRefundService.getRefundingByOrderId(dbOrder.getOrderId());
        if (CollUtil.isNotEmpty(refundList)) {
            throw new BusinessException("售后中的订单不支持发货操作");
        }
    }

    @Override
    protected void dealExecuteResult(OrderContext context, ActionResult executeResult) {

    }
}
