package com.sankuai.shangou.seashop.order.core.convert;

import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.OrderItemReq;
import org.mapstruct.Mapper;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname OrderItemConvert
 * Description //TODO
 * @date 2024/11/7 16:34
 */
@Mapper(componentModel = "spring")
public interface OrderItemConvert {
    OrderItem convert(OrderItemReq itemReq);
}
