package com.sankuai.shangou.seashop.order.core.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.enums.MqErrorDataBizTypeEnum;
import com.sankuai.shangou.seashop.order.common.enums.MqErrorDataHandleStatusEnum;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.mq.model.pay.PayResultBo;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.RefundResultNotifyBo;
import com.sankuai.shangou.seashop.order.core.service.assit.order.OrderChangeMessageAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.pay.OrderPayResultHandlerAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.OrderRefundMessageAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.RefundResultNotifyAssist;
import com.sankuai.shangou.seashop.order.dao.core.domain.MqErrorData;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonMqErrorDataQueryParamPo;
import com.sankuai.shangou.seashop.order.dao.core.repository.MqErrorDataRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessTypeEnum;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * MQ异常数据相关任务
 * 留口子处理MQ异常数据，一般异常后需要先排查解决异常，然后可以通过这个口子重新执行补偿
 * <AUTHOR>
 */
@Slf4j
@Component
public class MqErrorDataTask {

    @Resource
    private MqErrorDataRepository mqErrorDataRepository;

    @Resource
    private OrderRefundMessageAssist orderRefundMessageAssist;
    @Resource
    private OrderChangeMessageAssist orderChangeMessageAssist;
    @Resource
    private RefundResultNotifyAssist refundResultNotifyAssist;
    @Resource
    private OrderPayResultHandlerAssist orderPayResultHandlerAssist;

    @XxlJob("ExecuteMqErrorDataTask")
    public void executeMqErrorDataTask(ExecuteParam taskParam) {
        log.info("【定时任务】【执行MQ异常数据】...start...taskParam={}", JsonUtil.toJsonString(taskParam));
        CommonMqErrorDataQueryParamPo param = buildQueryParam(taskParam);
        log.info("【定时任务】【执行MQ异常数据】查询参数为：{}", JsonUtil.toJsonString(param));
        List<MqErrorData> dataList = mqErrorDataRepository.getByCondition(param);
        log.info("【定时任务】【执行MQ异常数据】, size={}", dataList.size());
        for (MqErrorData data : dataList) {
            log.info("【定时任务】【执行MQ异常数据】, data={}", JsonUtil.toJsonString(data));
            // 设置为处理中
            mqErrorDataRepository.updateHandleStatusById(data.getId(), MqErrorDataHandleStatusEnum.HANDLING.getCode(), null);
            log.info("【定时任务】【执行MQ异常数据】状态设置为处理中");
            try {
                String body = data.getDataContent();
                if (MqErrorDataBizTypeEnum.ORDER_CHANGE.getCode().equals(data.getBizType())) {
                    log.info("【定时任务】【执行MQ异常数据】, bizType=ORDER_CHANGE");
                    OrderMessage orderMessage = JsonUtil.parseObject(body, OrderMessage.class);
                    orderChangeMessageAssist.handleRefundMessage(orderMessage);
                } else if (MqErrorDataBizTypeEnum.PAY_NOTIFY.getCode().equals(data.getBizType())) {
                    log.info("【定时任务】【执行MQ异常数据】, bizType=PAY_NOTIFY");
                    PayResultBo payResultBo = JsonUtil.parseObject(body, PayResultBo.class);
                    // 目前支付服务的回调是同一个MQ，包括了保证金的，所以判断是否是订单的，不是订单的不处理
                    if (BusinessTypeEnum.ORDER.getType().equals(payResultBo.getBusinessType())) {
                        orderPayResultHandlerAssist.handlePayResult(payResultBo);
                    }
                } else if (MqErrorDataBizTypeEnum.REFUND_CHANGE.getCode().equals(data.getBizType())) {
                    log.info("【定时任务】【执行MQ异常数据】, bizType=REFUND_CHANGE");
                    OrderRefundMessage refundMessage = JsonUtil.parseObject(body, OrderRefundMessage.class);
                    orderRefundMessageAssist.handleRefundMessage(refundMessage);
                } else if (MqErrorDataBizTypeEnum.REFUND_NOTIFY.getCode().equals(data.getBizType())) {
                    log.info("【定时任务】【执行MQ异常数据】, bizType=REFUND_NOTIFY");
                    RefundResultNotifyBo payResultBo = JsonUtil.parseObject(body, RefundResultNotifyBo.class);
                    Integer businessType = payResultBo.getBusinessType();
                    // 只消费订单的消息
                    if (BusinessTypeEnum.ORDER.getType().equals(businessType)) {
                        refundResultNotifyAssist.handleRefundResult(payResultBo);
                    }
                } else {
                    log.warn("【定时任务】【执行MQ异常数据】无法识别的业务类型");
                }
                // 设置为已处理
                mqErrorDataRepository.updateHandleStatusById(data.getId(), MqErrorDataHandleStatusEnum.HANDLED.getCode(), null);
                log.info("【定时任务】【执行MQ异常数据】状态设置为已处理");
            } catch (Exception e) {
                log.error("【定时任务】【执行MQ异常数据】执行异常", e);
                // 设置为处理异常
                String msg = e.getMessage();
                if (msg == null) {
                    msg = "";
                } else if (msg.length() > 500) {
                    msg = msg.substring(0, 500);
                }
                mqErrorDataRepository.updateHandleStatusById(data.getId(), MqErrorDataHandleStatusEnum.EXCEPTION.getCode(), msg);
                log.info("【定时任务】【执行MQ异常数据】状态设置为处理异常");
            }
        }
    }

    private CommonMqErrorDataQueryParamPo buildQueryParam(ExecuteParam taskParam) {
        CommonMqErrorDataQueryParamPo.CommonMqErrorDataQueryParamPoBuilder builder = CommonMqErrorDataQueryParamPo.builder();
        // 状态设置为默认值，可以重置
        builder.statusIn(Arrays.asList(MqErrorDataHandleStatusEnum.UNDER_HANDLE.getCode(), MqErrorDataHandleStatusEnum.EXCEPTION.getCode()));
        if (taskParam == null) {
            return builder.build();
        }
        if (taskParam.getId() != null) {
            builder.idEq(taskParam.getId());
        }
        if (taskParam.getBizType() != null) {
            builder.bizTypeEq(taskParam.getBizType());
        }
        if (StrUtil.isNotBlank(taskParam.getBizNo())) {
            builder.bizNoEq(taskParam.getBizNo());
        }
        if (CollUtil.isNotEmpty(taskParam.getStatusList())) {
            builder.statusIn(taskParam.getStatusList());
        }
        return builder.build();
    }

    static class ExecuteParam {
        private Long id;
        private Integer bizType;
        private String bizNo;
        private List<Integer> statusList;

        public Long getId() {
            return id;
        }

        public void setId(Long id) {
            this.id = id;
        }

        public Integer getBizType() {
            return bizType;
        }

        public void setBizType(Integer bizType) {
            this.bizType = bizType;
        }

        public String getBizNo() {
            return bizNo;
        }

        public void setBizNo(String bizNo) {
            this.bizNo = bizNo;
        }

        public List<Integer> getStatusList() {
            return statusList;
        }

        public void setStatusList(List<Integer> statusList) {
            this.statusList = statusList;
        }
    }

}
