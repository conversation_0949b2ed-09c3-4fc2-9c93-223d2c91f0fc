package com.sankuai.shangou.seashop.order.core.service.impl;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.order.common.constant.LeafConst;
import com.sankuai.shangou.seashop.order.core.service.ThirdEventService;
import com.sankuai.shangou.seashop.order.core.service.model.ThirdPushEventBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.ThirdPushEvent;
import com.sankuai.shangou.seashop.order.dao.core.repository.ThirdPushEventRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.ThirdEvent;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class ThirdEventServiceImpl implements ThirdEventService {

    @Resource
    private ThirdPushEventRepository thirdPushEventRepository;
    @Resource
    private LeafService leafService;

    @Override
    public Long saveOrUpdatePushEvent(ThirdPushEventBo bo) {
        LambdaQueryWrapper<ThirdPushEvent> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ThirdPushEvent::getSendCode, bo.getSendCode())
                .eq(ThirdPushEvent::getEventType, bo.getEventType().getCode())
                .eq(ThirdPushEvent::getSendTarget, bo.getSendTarget().getCode());
        DateTime date = DateUtil.date();
        Long id = bo.getId();
        ThirdPushEvent event = new ThirdPushEvent();
        event.setSendCode(bo.getSendCode());
        event.setEventType(bo.getEventType().getCode());
        event.setSendTarget(bo.getSendTarget().getCode());
        event.setSendState(bo.getSendState().getCode());
        event.setSendMsg(bo.getSendMsg());
        event.setEventBody(bo.getEventBody());
        event.setUpdateTime(date);
        event.setId(id);
        if (id == null) {
            //新增
            id = leafService.generateNoBySnowFlake(LeafConst.KEY_PUSH_EVENT_ID);
            event.setId(id);
            event.setCreateTime(date);
            thirdPushEventRepository.save(event);
            return id;
        }
        thirdPushEventRepository.updateById(event);
        return id;
    }

    @Override
    public ThirdPushEventBo queryPushEventById(Long id) {
        ThirdPushEvent event = thirdPushEventRepository.getById(id);
        if (event == null) {
            return null;
        }
        ThirdPushEventBo bo = new ThirdPushEventBo();
        bo.setId(id);
        bo.setSendCode(event.getSendCode());
        bo.setEventType(ThirdEvent.EventTypeEnum.valueOf(event.getEventType()));
        bo.setSendTarget(ThirdEvent.SendTargetEnum.valueOf(event.getSendTarget()));
        bo.setSendState(ThirdEvent.SendStateEnum.valueOf(event.getSendState()));
        bo.setEventBody(event.getEventBody());
        bo.setSendMsg(event.getSendMsg());
        return bo;
    }
}
