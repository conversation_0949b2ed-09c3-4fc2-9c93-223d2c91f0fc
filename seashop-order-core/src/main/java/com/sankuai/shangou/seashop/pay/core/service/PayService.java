package com.sankuai.shangou.seashop.pay.core.service;

import java.util.List;
import java.util.Map;

import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import com.sankuai.shangou.seashop.pay.thrift.core.request.BillDownloadReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayBaseReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayCorpMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayMemberAndAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayPaymentCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayReverseCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPaySettleAccountQryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPaymentConfirmCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.BillDownloadResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPayPaymentCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPayReverseCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPaySettleAccountResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPaymentConfirmCreateResp;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
public interface PayService {

    void queryMember(AdaPayBaseReq request);

    /**
     * 创建个人账号
     *
     * @param request
     */
    void createMember(AdaPayMemberReq request);

    /**
     * 创建结算账号
     *
     * @param request
     * @return
     */
    AdaPaySettleAccountResp createSettleAccount(AdaPaySettleAccountReq request);

    /**
     * 删除结算账号
     * @param request 入参
     */
    void deleteSettleAccount(AdaPaySettleAccountQryReq request);

    /**
     * 创建个人账号和结算账号
     *
     * @param request
     * @return
     */
    AdaPaySettleAccountResp createMemberAndSettleAccount(AdaPayMemberAndAccountReq request);

    /**
     * 创建企业账号
     *
     * @param request
     */
    void createCompanyMember(AdaPayCorpMemberReq request);

    /**
     * 更新企业账号
     *
     * @param request
     */
    boolean updateCompanyMember(AdaPayCorpMemberReq request);

    /**
     * 判断结算账号是否存在
     *
     * @param request
     * @return
     */
    Boolean hasSettleAccount(AdaPaySettleAccountQryReq request);

    /**
     * 创建支付
     *
     * @param req
     * @return
     */
    AdaPayPaymentCreateResp createPayment(AdaPayPaymentCreateReq req);

    /**
     * 创建退款订单（发起退款请求）
     *
     * @param req
     * @return
     */
    AdaPayReverseCreateResp createPaymentReverse(AdaPayReverseCreateReq req);

    /**
     * 创建支付确认对象（分账）
     *
     * @param req
     * @return
     */
    AdaPaymentConfirmCreateResp createPaymentConfirm(AdaPaymentConfirmCreateReq req);

    /**
     * 对账单下载
     *
     * @param request
     * @return
     */
    BillDownloadResp billDownload(BillDownloadReq request);

    /**
     * 我已完成支付
     * @param batchNos
     */
    Map<String,OrderPayResp> queryCompletePay(List<String> batchNos);

    /**
     * 补偿处理待支付的支付单
     * @param unPaidList
     * @param flag  true发送MQ，false不发送MQ
     */
    void opOrderPayStatus(List<OrderPay> unPaidList, boolean flag);

    /**
     * 根据批次号集合查汇付支付单记录，不调第三方接口
     * @param batchNos
     * @return
     */
    Map<String, OrderPayResp> queryPayOrderPayMap(List<String> batchNos);
}
