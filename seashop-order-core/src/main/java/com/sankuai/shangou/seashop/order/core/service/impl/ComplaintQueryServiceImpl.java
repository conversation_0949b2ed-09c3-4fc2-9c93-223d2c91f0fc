package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.github.pagehelper.Page;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.ComplaintQueryService;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderComplaint;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.model.OrderComplaintExt;
import com.sankuai.shangou.seashop.order.dao.core.model.QueryOrderRightsReqModel;
import com.sankuai.shangou.seashop.order.dao.core.model.QueryOrderRightsRespModel;
import com.sankuai.shangou.seashop.order.dao.core.repository.ComplaintRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryMComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryMallComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QuerySellerComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderRightsReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderRightImagePathResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintRespPage;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderRightsResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/21 19:28
 */

@Service
@Slf4j
public class ComplaintQueryServiceImpl implements ComplaintQueryService {

    @Resource
    private ComplaintRepository complaintRepository;

    @Resource
    private OrderItemRepository orderItemRepository;

    @Resource
    private OrderRepository orderRepository;

    @Override
    public QueryOrderComplaintRespPage pageMComplaint(QueryMComplaintReq queryMComplaintReq) {
        Page<OrderComplaint> complaintPageInfo = complaintRepository.pageQueryOrderComplaint(JsonUtil.copy(queryMComplaintReq,OrderComplaintExt.class), queryMComplaintReq.getPageNo(), queryMComplaintReq.getPageSize());
        return new QueryOrderComplaintRespPage(PageResultHelper.transfer(complaintPageInfo, QueryOrderComplaintResp.class));
    }

    @Override
    public BasePageResp<QueryOrderComplaintResp> pageSellerComplaint(QuerySellerComplaintReq querySellerComplaintReq) {
        Page<OrderComplaint> complaintPageInfo = complaintRepository.pageQueryOrderComplaint(JsonUtil.copy(querySellerComplaintReq,OrderComplaintExt.class),
                querySellerComplaintReq.getPageNo(), querySellerComplaintReq.getPageSize());
        List<OrderComplaint> list = complaintPageInfo.getResult();
        if(CollectionUtil.isEmpty(list)){
            return PageResultHelper.transfer(complaintPageInfo, QueryOrderComplaintResp.class);
        }
        List<String> orderIdList = list.stream().map(OrderComplaint::getOrderId).collect(Collectors.toList());
        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
        if(CollectionUtil.isEmpty(orderList)){
            return PageResultHelper.transfer(complaintPageInfo, QueryOrderComplaintResp.class);
        }
        Map<String, Order> orderMap = orderList.stream().collect(Collectors.toMap(Order::getOrderId, Function.identity(), (k1, k2) -> k2));
        BasePageResp<QueryOrderComplaintResp> result = PageResultHelper.transfer(complaintPageInfo, QueryOrderComplaintResp.class);
        result.getData().forEach(v -> {
            if(Objects.isNull(orderMap.get(v.getOrderId()))){
                return;
            }
            v.setPayment(orderMap.get(v.getOrderId()).getPayment());
            v.setPayMethod(PayMethodEnum.getDesc(v.getPayment()));
            v.setTotalAmount(orderMap.get(v.getOrderId()).getTotalAmount());
            if(StringUtils.isEmpty(v.getPayMethod()) || "".equals(v.getPayMethod())){
                v.setPayMethod("0元订单无须支付");
            }
        });
        return result;
    }

    @Override
    public BasePageResp<QueryOrderComplaintResp> pageMallComplaint(QueryMallComplaintReq queryMallComplaintReq) {
        Page<OrderComplaint> complaintPageInfo = complaintRepository.pageQueryOrderComplaint(JsonUtil.copy(queryMallComplaintReq,OrderComplaintExt.class), queryMallComplaintReq.getPageNo(), queryMallComplaintReq.getPageSize());
        return PageResultHelper.transfer(complaintPageInfo, QueryOrderComplaintResp.class);
    }

    @Override
    public BasePageResp<QueryOrderRightsResp> pageQueryOrderRights(QueryOrderRightsReq queryOrderRightsReq) {
        QueryOrderRightsReqModel reqDto = new QueryOrderRightsReqModel();
        reqDto.setUserId(queryOrderRightsReq.getUserId());
        Page<QueryOrderRightsRespModel> rightsPageInfo = complaintRepository.pageQueryOrderRights(reqDto, queryOrderRightsReq.getPageNo(), queryOrderRightsReq.getPageSize());
        if(CollectionUtil.isEmpty(rightsPageInfo.getResult())){
            return PageResultHelper.defaultEmpty(queryOrderRightsReq.buildPage());
        }
        List<String> orderIdList = rightsPageInfo.getResult().stream().map(QueryOrderRightsRespModel::getOrderId).map(String::valueOf).collect(Collectors.toList());
        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(orderIdList);
        if(CollectionUtil.isEmpty(orderItemList)){
            return PageResultHelper.transfer(rightsPageInfo, QueryOrderRightsResp.class);
        }
        // 通过订单ID分组
        Map<String, List<OrderItem>> orderItemMap = orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getOrderId));
        rightsPageInfo.getResult().forEach(v -> {
            List<OrderItem> filter = orderItemMap.get(v.getOrderId().toString());
            if(CollectionUtil.isEmpty(filter)){
                return;
            }
            List<Map<String,String>> itemList = Lists.newArrayList();
            for(OrderItem item : filter){
                Map<String,String> rightItem = new HashMap<>();
                rightItem.put("productId", item.getProductId().toString());
                rightItem.put("imageUrl", item.getThumbnailsUrl());
                itemList.add(rightItem);
            }
            v.setImagePath(itemList);
        });
        return PageResultHelper.transfer(rightsPageInfo, QueryOrderRightsResp.class);
    }
}
