package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayRecordResp;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/5 15:48
 */
public interface OrderPayRecordService {

    List<OrderPayRecordResp> queryOrderPayRecordList(List<String> batchNoList);

    List<OrderPayRecordResp> queryOrderPayRecordByOrderIds(List<String> orderIds);
}
