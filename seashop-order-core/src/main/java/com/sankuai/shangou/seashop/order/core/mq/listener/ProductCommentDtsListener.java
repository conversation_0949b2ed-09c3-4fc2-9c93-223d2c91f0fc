package com.sankuai.shangou.seashop.order.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.mq.model.DbTableDataChangeMessage;
import com.sankuai.shangou.seashop.order.core.mq.model.order.ProductCommentTableMessage;
import com.sankuai.shangou.seashop.order.core.service.ProductCommentService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/04/19 17:13
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_PRODUCT_COMMENT,
//        group = MafkaConst.GROUP_PRODUCT_COMMENT_ES_BUILD)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_PRODUCT_COMMENT + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_PRODUCT_COMMENT_ES_BUILD + "_${spring.profiles.active}")
public class ProductCommentDtsListener implements RocketMQListener<MessageExt> {

    @Resource
    private ProductCommentService productCommentService;


    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【商品评价表变更】消息内容为: {}", body);

        try {
            DbTableDataChangeMessage<ProductCommentTableMessage> messageWrapper = JsonUtil.parseObject(body,
                new TypeReference<DbTableDataChangeMessage<ProductCommentTableMessage>>() {
                });
            productCommentService.buildEsProductComment(messageWrapper.getData().getProductCommentId());
        }
        catch (Exception e) {
            log.error("【mafka消费】【商品评价表变更】处理失败", e);
            // 失败后，重试默认3次，监控发现问题，然后依赖定时任务补偿
            throw new RuntimeException(e);
        }
    }
}
