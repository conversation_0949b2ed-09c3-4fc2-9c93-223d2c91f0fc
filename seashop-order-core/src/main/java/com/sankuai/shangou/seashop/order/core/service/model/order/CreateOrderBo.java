package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderPlatformEnum;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class CreateOrderBo {

    /**
     * 用户ID
     */
    private CreateOrderUserBo userInfo;
    /**
     * 订单提交唯一标识，用于幂等校验
     */
    private String uniqueId;
    /**
     * 收货地址
     */
    private ShippingAddressBo shippingAddress;
    /**
     * 所有店铺总金额
     */
    private BigDecimal totalAmount;
    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;


    /**
     * 限时购活动id，限时购活动提交时字段有值
     */
    private Long flashSaleId;
    /**
     * 组合购活动id，组合购活动提交时字段有值
     */
    private Long collocationId;
    /**
     * 订单来源
     */
    //private OrderSourceEnum orderSource;
    /**
     * 订单平台
     */
    private OrderPlatformEnum platform;
    /**
     * 是否立即购买
     */
    private Boolean whetherBuyNow;
    /**
     * 创建时间，用接口入口时间。交易服务设置传入
     */
    private Date createTime;

    /**
     * 订单商品类型，1：实物商品, 2：虚拟商品
     */
    private Integer productType;

    /**
     * 订单配送类型，1：快递配送，2：上门自提
     */
    private Integer deliveryType;

    public boolean isNotFromCart() {
        return (flashSaleId != null && flashSaleId > 0) || (collocationId != null && collocationId > 0) ||
                Boolean.TRUE.equals(whetherBuyNow);
    }

}
