package com.sankuai.shangou.seashop.pay.core.assist.pay.model;

import java.math.BigDecimal;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/03 12:42
 */
@Data
public class RefundParam {

    /**
     * 来源订单id
     */
    private String orderId;

    /**
     * 退款id
     */
    private String reverseId;

    /**
     * 退款金额（单位：元）
     */
    private BigDecimal reverseAmount;

    /**
     * 业务状态类型：1：正常订单退款；2：异常订单退款；3：订单补偿退款（支付金额大于订单金额）
     */
    private Integer businessStatusType;

    /**
     * 支付渠道支付id
     */
    private String channelPayId;

    /**
     * 支付渠道
     */
    private Integer paymentChannel;

    /**
     * 支付配置
     */
    private Object payConfig;

    /**
     * 订单总金额
     */
    private BigDecimal orderAmount;
}
