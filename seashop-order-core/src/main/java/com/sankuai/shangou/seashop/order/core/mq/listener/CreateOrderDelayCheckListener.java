package com.sankuai.shangou.seashop.order.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderCreateCompensationAssist;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderCheckBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 创建订单时，先发送一条延时检查消息，用于一定时间后检查订单是否已经创建成功。
 * <p>目前延时消息30分钟，如果这么久还未创建订单成功，则代表订单创建失败，但创建过程中，可能关联的服务调用成功了，所以需要尝试回滚</p>
 * <AUTHOR>
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_DELAY_ORDER_CHECK,
//        group = MafkaConst.GROUP_ORDER_CHECK)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_DELAY_ORDER_CHECK + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_CHECK + "_${spring.profiles.active}")
public class CreateOrderDelayCheckListener implements RocketMQListener<MessageExt> {

    @Resource
    private OrderCreateCompensationAssist orderCreateCompensationAssist;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext context) {
//        String body = (String) mafkaMessage.getBody();
//        log.info("【mafka消费】【订单创建时延迟检查】消息内容为: {}", body);
//        OrderCheckBo messageWrapper = JsonUtil.parseObject(body, OrderCheckBo.class);
//        // TODO BCP接入，验证消息是否发送成功
//        orderCreateCompensationAssist.checkAndRollback(messageWrapper.getUserId(), messageWrapper.getOrderIdList());
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(MessageExt message) {
        log.info("【mafka消费】【订单创建时延迟检查】消息内容为: {}", message);
//        String body = (String) mafkaMessage.getBody();
        String body  = new String(message.getBody(), StandardCharsets.UTF_8);
        try {
            OrderCheckBo messageWrapper = JsonUtil.parseObject(body, OrderCheckBo.class);
            // TODO BCP接入，验证消息是否发送成功
            orderCreateCompensationAssist.checkAndRollback(messageWrapper.getUserId(), messageWrapper.getOrderIdList());
        } catch (Exception e) {
            log.error("【mafka消费】【订单创建时延迟检查】处理失败", e);
            throw new RuntimeException(e);
        }
    }

}
