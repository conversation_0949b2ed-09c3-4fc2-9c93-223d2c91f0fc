package com.sankuai.shangou.seashop.order.core.mq.publisher;

import java.util.List;

import javax.annotation.Resource;

import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderCheckBo;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;

import lombok.extern.slf4j.Slf4j;

/**
 * 订单检查辅助类
 * <p>订单创建过程，库存、优惠券等的处理是保证最终一致性</p>
 * <p>mafka生产者参考文档：https://km.sankuai.com/custom/onecloud/page/686486875</p>
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderMessagePublisher {

//    @MafkaProducer(namespace = "waimai", topic = MafkaConst.TOPIC_DELAY_ORDER_CHECK, delay = true)
//    private IProducerProcessor orderCheckProducer;
//    @MafkaProducer(namespace = "waimai", topic = MafkaConst.TOPIC_ORDER_FAILURE)
//    private IProducerProcessor orderFailureProducer;
//    @MafkaProducer(namespace = "waimai", topic = MafkaConst.TOPIC_ORDER_CHANGE)
//    private IProducerProcessor orderChangeProducer;

    @Resource
    private DefaultRocketMq defaultRocketMq;

    /**
     * 创建订单时，先发送一条延时检查消息，用于一定时间后检查订单是否已经创建成功。
     * <p>如果创建失败，需要检查关联的数据，并执行回滚</p>
     *
     * <AUTHOR>
     */
    public void sendOrderCheckMessage(Long userId, List<String> orderIdList) {
        log.info("【mafka生产】【订单检查】发送订单检查消息, orderIdList: {}", JsonUtil.toJsonString(orderIdList));
        // 发送订单检查消息
        SendResult producerResult = null;
        try {
            producerResult = defaultRocketMq.syncSendDelayTimeMills(MafkaConst.TOPIC_DELAY_ORDER_CHECK,
                    JsonUtil.toJsonString(new OrderCheckBo(orderIdList, userId)), MafkaConst.DELAY_TIME_ORDER_CHECK);
//           producerResult = orderCheckProducer.sendDelayMessage(JsonUtil.toJsonString(new OrderCheckBo(orderIdList, userId)), MafkaConst.DELAY_TIME_ORDER_CHECK);
            log.info("【mafka生产】【订单检查】发送订单检查消息结果: {}", producerResult.toString());
        }
        catch (Exception e) {
            log.error("【mafka生产】【订单检查】发送订单检查消息异常", e);
            throw new SystemException();
        }
        if (!SendStatus.SEND_OK.equals(producerResult.getSendStatus())) {
            throw new BusinessException("发送消息失败");
        }
    }

    /**
     * 创建订单时，先发送一条延时检查消息，用于一定时间后检查订单是否已经创建成功。
     * <p>如果创建失败，需要检查关联的数据，并执行回滚</p>
     *
     * <AUTHOR>
     */
    public void sendOrderFailureMessage(Long userId, List<String> orderIdList) {
        log.info("【mafka生产】【订单失败】发送订单失败消息, orderIdList: {}", JsonUtil.toJsonString(orderIdList));
        // 发送订单检查消息
        SendResult producerResult = null;
        try {
//            producerResult = orderFailureProducer.sendMessage(JsonUtil.toJsonString(new OrderCheckBo(orderIdList, userId)));
            producerResult = defaultRocketMq.syncSend(MafkaConst.TOPIC_ORDER_FAILURE, JsonUtil.toJsonString(new OrderCheckBo(orderIdList
                    , userId)));
            log.info("【mafka生产】【订单失败】发送订单失败消息结果: {}", producerResult.toString());
        }
        catch (Exception e) {
            log.error("【mafka生产】【订单失败】发送订单失败消息异常", e);
            throw new SystemException();
        }
        if (!SendStatus.SEND_OK.equals(producerResult.getSendStatus())) {
            throw new BusinessException("发送消息失败");
        }
    }

    /**
     * 发送订单变更消息，其他业务按需消费
     *
     * <AUTHOR>
     */
    public void sendOrderChangeMessage(String orderId, OrderMessageEventEnum messageEvent) {
        // 判断当前是否存在事务，如果存在事务，则在事务提交后发送消息，否则直接发送, 发送消息的方法: actualSendOrderChangeMessage
        if (TransactionSynchronizationManager.isSynchronizationActive()) {
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                @Override
                public void afterCommit() {
                    actualSendOrderChangeMessage(orderId, messageEvent);
                }
            });
        }
        else {
            actualSendOrderChangeMessage(orderId, messageEvent);
        }
    }

    private void actualSendOrderChangeMessage(String orderId, OrderMessageEventEnum messageEvent) {
        log.info("【mafka生产】【订单变更】发送订单变更消息, orderId: {}, messageEvent={}", orderId, messageEvent);
        // 发送订单检查消息
        SendResult producerResult = null;
        String message = JsonUtil.toJsonString(new OrderMessage(orderId, messageEvent.name()));
        try {
            producerResult = defaultRocketMq.syncSend(MafkaConst.TOPIC_ORDER_CHANGE, message);
            log.info("【mafka生产】【订单变更】发送订单变更消息结果: {}", JsonUtil.toJsonString(producerResult));
            if (!SendStatus.SEND_OK.equals(producerResult.getSendStatus())) {
                throw new BusinessException("发送消息失败");
            }
        }
        catch (Exception e) {
            log.error("【mafka生产】【订单变更】发送订单变更消息异常: {}", message, e);
        }
    }

}
