package com.sankuai.shangou.seashop.order.core.mq.listener;

import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.erp.thrift.biz.request.wdt.WdtOrderApiReq;
import com.sankuai.shangou.seashop.erp.thrift.biz.request.wdt.WdtOrderDto;
import com.sankuai.shangou.seashop.erp.thrift.biz.request.wdt.WdtOrderItemDto;
import com.sankuai.shangou.seashop.erp.thrift.channel.ErpWdtOrderCmdThriftService;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.service.OrderService;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderItemInfoBo;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopErpQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopErpReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopErpResp;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

/**
 * 暂时先这么用，后续有了dts再改
 *
 * <AUTHOR>
 * @date 2024/07/24 14:59
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_CHANGE + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_WDT_BUILD + "_${spring.profiles.active}")
public class OrderWdtBuildListener implements RocketMQListener<MessageExt> {


    @Resource
    private ShopErpQueryFeign shopErpQueryFeign;
    @Resource
    private OrderService orderService;

    @Resource
    private ErpWdtOrderCmdThriftService erpWdtOrderCmdThriftService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【订单ES构建】【OrderWdtBuildListener】消息内容为: {}", body);

        OrderMessage orderMessage = null;
        try {
            orderMessage = JsonUtil.parseObject(body, OrderMessage.class);

            OrderDetailBo orderDetailBo = orderService.getDetail(orderMessage.getOrderId(), OrderQueryFromEnum.PLATFORM_PC);
            if (orderDetailBo == null) {
                log.info("【mafka消费】【同步旺店通erp】消息内容为: body: {}, 订单不存在", body);
                return;
            }

            QueryShopErpReq req = new QueryShopErpReq();
            req.setShopId(orderDetailBo.getOrderInfo().getShopId());
            QueryShopErpResp queryShopErpResp = ThriftResponseHelper.executeThriftCall(() -> shopErpQueryFeign.queryShopErp(req));
            if (queryShopErpResp == null) {
                log.info("【mafka消费】【同步旺店通erp】消息内容为: body: {}, 找不到erp配置", body);
                return;
            }
            if (StringUtil.isNullOrEmpty(queryShopErpResp.getWdtAppKey())) {
                log.info("【mafka消费】【同步旺店通erp】消息内容为: body: {}, 店铺没有配置erp", body);
                return;
            }
            WdtOrderApiReq apiReq = buildOrderApiReq(orderDetailBo, queryShopErpResp);

            ThriftResponseHelper.executeThriftCall(() -> erpWdtOrderCmdThriftService.createWdtOrder(apiReq));
        } catch (Exception e) {
            log.error("【mafka消费】【订单ES 构建失败】处理失败, msg: {}", body, e);
            throw new RuntimeException(e);
        }
    }

    private WdtOrderApiReq buildOrderApiReq(OrderDetailBo orderDetailBo, QueryShopErpResp shopErpResp) {
        WdtOrderApiReq wdtOrderApiReq = new WdtOrderApiReq();
        wdtOrderApiReq.setSwitchType(1);//固定1  严选模式
        wdtOrderApiReq.setShopNo(shopErpResp.getWdtShopNo());
        wdtOrderApiReq.setAppKey(shopErpResp.getWdtAppKey());
        wdtOrderApiReq.setAppSecret(shopErpResp.getWdtAppSecret());
        wdtOrderApiReq.setSid(shopErpResp.getWdtSid());
        wdtOrderApiReq.setBaseUrl("https://sandbox.wangdian.cn/openapi2");
        List<WdtOrderDto> tradeList = new ArrayList<>();

        WdtOrderDto wdtOrderDto = new WdtOrderDto();
        wdtOrderDto.setTid(orderDetailBo.getOrderInfo().getOrderId());
        int status = getTradeStatus(OrderStatusEnum.getByCode(orderDetailBo.getOrderInfo().getOrderStatus()), RefundStatusEnum.valueOf(orderDetailBo.getOrderInfo().getOrderStatus()));
        wdtOrderDto.setTradeStatus(status == 90 ? status == 90 && orderDetailBo.getOrderInfo().getPayDate() != null ? 80 : 90 : status);
        wdtOrderDto.setPayStatus(getPayStatus(OrderStatusEnum.getByCode(orderDetailBo.getOrderInfo().getOrderStatus())));
        wdtOrderDto.setDeliveryTerm(1);//固定 款到发货
        wdtOrderDto.setTradeTime(DateUtil.formatDateTime(orderDetailBo.getOrderInfo().getOrderDate()));
        wdtOrderDto.setPayTime(orderDetailBo.getOrderInfo().getPayDate() != null ? DateUtil.formatDateTime(orderDetailBo.getOrderInfo().getPayDate()) : "0000-00-00 00:00:00");
        wdtOrderDto.setBuyerNick(orderDetailBo.getOrderInfo().getNick());
        wdtOrderDto.setBuyerEmail("");
        wdtOrderDto.setPayId(orderDetailBo.getOrderInfo().getGatewayOrderId());
        wdtOrderDto.setPayAccount("");
        wdtOrderDto.setReceiverName(orderDetailBo.getOrderInfo().getShipTo());
        String address = orderDetailBo.getOrderInfo().getRegionFullName();
        String[] addressArray = address.split(" ");
        wdtOrderDto.setReceiverProvince(addressArray[0]);
        wdtOrderDto.setReceiverCity(addressArray[1]);
        wdtOrderDto.setReceiverDistrict(addressArray[2]);
        wdtOrderDto.setReceiverAddress(orderDetailBo.getOrderInfo().getAddress());
        wdtOrderDto.setReceiverMobile(orderDetailBo.getOrderInfo().getCellPhone());
        wdtOrderDto.setReceiverTelno("");
        wdtOrderDto.setReceiverZip("");
        wdtOrderDto.setLogisticsType("-1");
        wdtOrderDto.setInvoiceKind(orderDetailBo.getOrderInvoice() != null ? orderDetailBo.getOrderInvoice().getInvoiceType() : 0);
        wdtOrderDto.setInvoiceTitle(orderDetailBo.getOrderInvoice() != null ? orderDetailBo.getOrderInvoice().getInvoiceTitle() : "");
        wdtOrderDto.setInvoiceContent(orderDetailBo.getOrderInvoice() != null ? orderDetailBo.getOrderInvoice().getInvoiceContext() : "");
        wdtOrderDto.setBuyerMessage(orderDetailBo.getOrderInfo().getUserRemark());
        wdtOrderDto.setSellerMemo(orderDetailBo.getOrderInfo().getSellerRemark());
        wdtOrderDto.setPostAmount(orderDetailBo.getOrderInfo().getFreight().doubleValue());
        wdtOrderDto.setCodAmount(0);
        wdtOrderDto.setExtCodFee(0);
        wdtOrderDto.setOtherAmount(0);
        wdtOrderDto.setPaid(orderDetailBo.getOrderInfo().getTotalAmount().doubleValue());

        List<WdtOrderItemDto> orderList = new ArrayList<>();
        for (OrderItemInfoBo orderItemBo : orderDetailBo.getOrderInfo().getItemList()) {
            WdtOrderItemDto wdtOrderItemDto = new WdtOrderItemDto();
            wdtOrderItemDto.setOid(orderItemBo.getOrderItemId().toString());
            wdtOrderItemDto.setNum(orderItemBo.getQuantity().intValue());

            wdtOrderItemDto.setStatus(status == 90 ? status == 90 && orderDetailBo.getOrderInfo().getPayDate() != null ? 80 : 90 : status);
            wdtOrderItemDto.setRefundStatus(getRefundStatus(RefundAuditStatusEnum.valueOf(orderItemBo.getRefundStatus())));
            wdtOrderItemDto.setGoodsId(orderItemBo.getProductId().toString());
            wdtOrderItemDto.setSpecId(orderItemBo.getSkuId());
            wdtOrderItemDto.setGoodsNo("");
            wdtOrderItemDto.setSpecNo(orderItemBo.getSku());
            wdtOrderItemDto.setGoodsName(orderItemBo.getProductName());
            wdtOrderItemDto.setSpecName(orderItemBo.getSkuName());
            wdtOrderItemDto.setDiscount(orderItemBo.getDiscountAmount().doubleValue());
            orderList.add(wdtOrderItemDto);
        }
        wdtOrderDto.setOrderList(orderList);
        tradeList.add(wdtOrderDto);
        wdtOrderApiReq.setTradeList(tradeList);
        return wdtOrderApiReq;
    }

    private static int getTradeStatus(OrderStatusEnum status, RefundStatusEnum refundStatus) {
        int tradeStatus = -1;
        if (status == OrderStatusEnum.UNDER_PAY) {
            tradeStatus = 10;
        } else if (status == OrderStatusEnum.UNDER_RECEIVE) {
            tradeStatus = 50;
        } else if (status == OrderStatusEnum.CLOSED) {
            tradeStatus = 90;
        } else if (status == OrderStatusEnum.FINISHED) {
            tradeStatus = 70;
        } else if (status == OrderStatusEnum.UNDER_SEND) {
            tradeStatus = 30;
        } else if (refundStatus == RefundStatusEnum.REFUND_SUCCESS) {
            tradeStatus = 80;
        }
        return tradeStatus;
    }

    private int getPayStatus(OrderStatusEnum status) {
        int pay_status = 0;
        if (status == OrderStatusEnum.UNDER_PAY) {
            return 0;
        } else {
            return 2;
        }
    }


    public int getRefundStatus(RefundAuditStatusEnum orderRefund) {
        switch (orderRefund) {
            case PLATFORM_REFUSE:
            case SUPPLIER_REFUSE:
                return 1;
            case WAIT_SUPPLIER_AUDIT:
            case WAIT_PLATFORM_CONFIRM:
            case PLATFORM_PASS:
            case SUPPLIER_PASS:
                return 2;
            case WAIT_SUPPLIER_RECEIVE:
                return 4;
            case WAIT_BUYER_SEND:
                return 3;
            case REFUND_SUCCESS:
                return 5;
            default:
                return 0;
        }
    }

}
