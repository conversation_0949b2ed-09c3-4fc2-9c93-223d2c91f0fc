package com.sankuai.shangou.seashop.order.core.service.model.refund;

import java.math.BigDecimal;
import java.util.List;

import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundTypeEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class CreateOrderRefundBo {

    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 三方售后单号
     */
    private String sourceRefundId;

    /**
     * 支付前 订单退款  仅退钱
     * 支付后 货品退款  仅退钱
     * 支付后 退货退款  退钱退货
     */
    private RefundModeEnum refundMode;

    /**
     * 售后类型。1：仅退款；2：退货退款
     */
    private RefundTypeEnum refundType;

    /**
     * 指定状态 @link RefundStatusEnum
     */
    private RefundStatusEnum status;
    /**
     * 售后单列表
     */
    private List<CreateOrderRefundItemBo> refundItems;
    /**
     * 运费
     */
    private BigDecimal refundFreight;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 退货原因
     */
    private String refundReason;
    /**
     * 退款说明
     */
    private String remark;
    /**
     * 退款凭证，最多3张图片
     */
    private List<String> pics;
    /**
     * 快递公司编码
     */
    private String expressCompanyCode;
    /**
     * 快递公司名称
     */
    private String expressCompanyName;
    /**
     * 快递单号
     */
    private String shipOrderNumber;
}
