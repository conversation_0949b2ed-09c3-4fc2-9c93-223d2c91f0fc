package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundRecordService;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundRecordCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ExcessPaymentRefundReq;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2024/1/8/008
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("/orderRefundRecord")
public class OrderRefundRecordCmdController implements OrderRefundRecordCmdFeign {

    @Resource
    private OrderRefundRecordService orderRefundRecordService;

    @PostMapping(value = "/excessPaymentRefund", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> excessPaymentRefund(@RequestBody ExcessPaymentRefundReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("excessPaymentRefund", request, req -> {
            req.checkParameter();
            LockHelper.lock(LockConst.LOCK_ORDER_REFUND_RECORD + req.getOrderId(), () -> {
                orderRefundRecordService.excessPaymentRefund(req);
            });
            return BaseResp.of();
        });
    }

}
