package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderRefundPreviewBo extends RefundDetailBo {

    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 订单创建时间
     */
    private Date orderDate;
    /**
     * 订单总金额
     */
    private BigDecimal orderTotalAmount;
    /**
     * 订单可退款金额
     */
    private BigDecimal remainRefundAmount;
    /**
     * 可退金额描述(最多X元，包含运费Y元)
     */
    private String remainRefundAmountDesc;
    /**
     * 订单可退款数量
     */
    private Long remainRefundQuantity;
    /**
     * 订单状态。1：待付款，2：待发货，3：待收货，4：已关闭，5：已完成，6：支付中
     */
    private Integer orderStatus;
    /**
     * 订单状态描述
     */
    private String orderStatusDesc;
    /**
     * 订单类型。 {@link com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderTypeEnum}
     */
    private Integer orderType;
    /**
     * 订单类型描述
     */
    private String orderTypeDesc;

}
