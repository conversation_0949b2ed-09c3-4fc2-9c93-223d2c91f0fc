package com.sankuai.shangou.seashop.pay.core.bo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
@ToString
@TypeDoc(description = "保存渠道配置请求对象")
public class ChannelConfigBo {

    @FieldDoc(description = "配置Key", requiredness = Requiredness.REQUIRED)
    private String configKey;

    @FieldDoc(description = "配置Value", requiredness = Requiredness.REQUIRED)
    private String configValue;


}
