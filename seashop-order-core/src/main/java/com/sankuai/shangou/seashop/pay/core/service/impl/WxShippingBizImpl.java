package com.sankuai.shangou.seashop.pay.core.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Maps;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.common.util.RedisUtil;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.pay.core.service.WxShippingBiz;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.GetOrderRequest;
import com.sankuai.shangou.seashop.pay.thrift.core.request.IsTradeManagedRequest;
import com.sankuai.shangou.seashop.pay.thrift.core.request.UploadShippingRequest;
import com.sankuai.shangou.seashop.pay.thrift.core.response.DeliveryListResponse;
import com.sankuai.shangou.seashop.pay.thrift.core.response.GetOrderResponse;
import com.sankuai.shangou.seashop.pay.thrift.core.response.IsTradeManagedResponse;
import com.sankuai.shangou.seashop.pay.thrift.core.response.UploadShippingResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description:
 */
@Service
@Slf4j
public class WxShippingBizImpl implements WxShippingBiz {

    @Resource
    private WxMaShippingService wxMaShippingService;
    private static final String WX_SHIPPING_IS_TRADED_MANAGED = "wx_shipping_is_traded_managed:";
    @Autowired
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private SiteSettingQueryFeign siteSettingQueryFeign;

    @Override
    public Boolean isTradeManaged(Integer paymentType) {
        Map<String, String> map = this.queryToken(PaymentTypeEnum.getByType(paymentType));
        IsTradeManagedRequest requet = new IsTradeManagedRequest();
        requet.setAccessToken(map.get("token"));
        requet.setAppid(map.get("appId"));
        try {
            IsTradeManagedResponse response = wxMaShippingService.isTradeManaged(requet);
            RedisUtil.set(WX_SHIPPING_IS_TRADED_MANAGED + map.get("appId"), response.isTradeManaged(), 60 * 60 * 24);
            return response.isTradeManaged();
        } catch (Exception e) {
            log.error("查询小程序是否开通发货信息管理服务失败", e);
            throw new BusinessException("查询小程序是否开通发货信息管理服务失败");
        }
    }

    @Override
    public Boolean uploadShipping(UploadShippingRequest request) {
        log.info("发货信息录入接口request：{}", JSON.toJSONString(request));
        if (request.getOrder_key() == null) {
            throw new BusinessException("订单信息不能为空");
        }
        if (StringUtils.isEmpty(request.getOrder_key().getOut_trade_no())) {
            throw new BusinessException("订单号不能为空");
        }
        if (CollectionUtils.isEmpty(request.getShipping_list())) {
            throw new BusinessException("物流信息不能为空");
        }
        List<Integer> payMethodList = Lists.newArrayList();
        payMethodList.add(PayMethodEnum.WECHAT_APPLET.getCode());
        payMethodList.add(PayMethodEnum.WECHAT_H5.getCode());
        payMethodList.add(PayMethodEnum.WECHAT_JS.getCode());
        payMethodList.add(PayMethodEnum.WECHAT_NATIVE.getCode());
        OrderPayRecord orderPayRecord = orderPayRecordRepository.getOne(new LambdaQueryWrapper<OrderPayRecord>()
                .eq(OrderPayRecord::getOutTransId, request.getOrder_key().getOut_trade_no())
                .eq(OrderPayRecord::getPayStatus, PayStatusEnum.PAY_SUCCESS.getCode())
                .in(OrderPayRecord::getPayMethod, payMethodList)
                .last("limit 1"));
        if (orderPayRecord == null) {
            throw new BusinessException("支付记录不存在");
        }
        Map<String, String> map = this.queryToken(PaymentTypeEnum.getByType(orderPayRecord.getPayMethod()));
        request.setAccessToken(map.get("token"));
        // 走微信支付单号
        request.getOrder_key().setTransaction_id(orderPayRecord.getOutTransId());
        request.getOrder_key().setOrder_number_type(2);

        // 在上层获取openId
        /*Payer payer = new Payer();
        payer.setOpenid(transaction.getOpenId());
        request.setPayer(payer);*/

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")); // 显式设置时区
        String rfc3339String = sdf.format(new Date());
        request.setUpload_time(rfc3339String);
        try {
            UploadShippingResponse response = wxMaShippingService.uploadShipping(request);
            log.info("发货信息录入接口调用成功:{}", JSON.toJSONString(response));
            if (response.getErrCode() == 0) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("发货信息录入接口调用失败:{}", e.getMessage());
            throw new BusinessException("发货信息录入接口调用失败");
        }
    }

    @Override
    public Boolean getOrder(GetOrderRequest request) {
        if (StringUtils.isEmpty(request.getTransactionId())) {
            throw new BusinessException("支付记录id不能为空");
        }
        List<Integer> payMethodList = Lists.newArrayList();
        payMethodList.add(PayMethodEnum.WECHAT_APPLET.getCode());
        payMethodList.add(PayMethodEnum.WECHAT_H5.getCode());
        payMethodList.add(PayMethodEnum.WECHAT_JS.getCode());
        payMethodList.add(PayMethodEnum.WECHAT_NATIVE.getCode());
        OrderPayRecord orderPayRecord = orderPayRecordRepository.getOne(new LambdaQueryWrapper<OrderPayRecord>()
                .eq(OrderPayRecord::getOutTransId, request.getTransactionId())
                .eq(OrderPayRecord::getPayStatus, PayStatusEnum.PAY_SUCCESS.getCode())
                .in(OrderPayRecord::getPayMethod, payMethodList)
                .last("limit 1"));
        if (orderPayRecord == null) {
            throw new BusinessException("支付记录不存在");
        }
        Map<String, String> map = this.queryToken(PaymentTypeEnum.getByType(orderPayRecord.getPayMethod()));
        request.setAccessToken(map.get("token"));
        try {
            GetOrderResponse response = wxMaShippingService.getOrder(request);
            if (response.getErrCode() == 0 && response.getOrder() != null) {
                if (response.getOrder().getOrderState() == 3) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("查询订单接口调用失败:{}", e.getMessage());
            throw new BusinessException("查询订单接口调用失败");
        }
    }

    @Override
    public DeliveryListResponse getDeliveryList(Integer paymentType) {
        Map<String, String> map = this.queryToken(PaymentTypeEnum.getByType(paymentType));
        try {
            DeliveryListResponse deliveryListResponse = wxMaShippingService.getDeliveryList(map.get("token"));
            return deliveryListResponse;
        } catch (Exception e) {
            log.error("获取运力id列表接口调用失败:{}", e.getMessage());
            throw new BusinessException("获取运力id列表接口调用失败");
        }
    }

    private Map<String, String> queryToken(PaymentTypeEnum paymentType) {
        Map<String, String> map = Maps.newHashMap();
        String appId = "";
        String secret = "";
        switch (paymentType) {
            case WECHAT_APPLET:
                appId = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.querySettingsValueByKey("weixinAppletId"));
                secret = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.querySettingsValueByKey("weixinAppletSecret"));
                map.put("appId", appId);
                break;
            case WECHAT_H5:
                appId = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.querySettingsValueByKey("weixinMpAppId"));
                secret = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.querySettingsValueByKey("weixinMpAppSecret"));
                map.put("appId", appId);
                break;
            case WECHAT_JS:
                appId = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.querySettingsValueByKey("weixinMpAppId"));
                secret = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.querySettingsValueByKey("weixinMpAppSecret"));
                map.put("appId", appId);
                break;
            case WECHAT_NATIVE:
                appId = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.querySettingsValueByKey("weixinAppletId"));
                secret = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.querySettingsValueByKey("weixinAppletSecret"));
                map.put("appId", appId);
                break;
            default:
                AssertUtil.throwIfTrue(true, "不支持的支付类型");
                return null;
        }
        log.info("微信小程序appId:{},微信小程序secret:{}", appId, secret);
        String token = wxMaShippingService.getToken(appId, secret);
        log.info("微信小程序token:{}", token);
        map.put("token", token);
        if (StringUtils.isBlank(token)) {
            throw new BusinessException("小程序token获取失败");
        }
        return map;
    }
}
