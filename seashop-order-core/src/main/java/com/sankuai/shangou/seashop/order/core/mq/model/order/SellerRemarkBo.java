package com.sankuai.shangou.seashop.order.core.mq.model.order;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class SellerRemarkBo extends BaseParamReq {

    /**
     * 订单id
     */
    @PrimaryField
    private String orderId;
    /**
     * 当前登录店铺信息
     */
    private ShopBo shop;
    /**
     * 备注
     */
    @ExaminField(description = "备注")
    private String sellerRemark;
    /**
     * 备注标记。一次1-4，4面小旗
     */
    @ExaminField(description = "备注标记")
    private Integer sellerRemarkFlag;

}
