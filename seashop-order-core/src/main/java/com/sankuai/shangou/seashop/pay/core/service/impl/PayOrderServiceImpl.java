package com.sankuai.shangou.seashop.pay.core.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.huifu.adapay.model.Payment;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.pay.common.constant.AdaPayConstant;
import com.sankuai.shangou.seashop.pay.common.enums.AdaPayEnums;
import com.sankuai.shangou.seashop.pay.core.config.AdaPayInitWithMerConfig;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayPaymentListQueryDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayPaymentResultDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentResultDto;
import com.sankuai.shangou.seashop.pay.core.mq.publisher.OrderPayStatusChangeProducer;
import com.sankuai.shangou.seashop.pay.core.service.PayOrderService;
import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import com.sankuai.shangou.seashop.pay.dao.core.repository.OrderPayRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayResultCodeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.request.OrderPayQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
@Service
@Slf4j
public class PayOrderServiceImpl implements PayOrderService {

    @Resource
    private OrderPayRepository orderPayRepository;
    @Resource
    private OrderPayStatusChangeProducer orderPayStatusChangeProducer;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;

    @Override
    public OrderPayResp queryOrderPayResp(String orderId) {
        OrderPay orderPay = orderPayRepository.getByOrderId(orderId);
        return JsonUtil.copy(orderPay, OrderPayResp.class);
    }

    @Override
    public OrderPayResp getOne(OrderPayQueryReq request) {
        OrderPay orderPay = orderPayRepository.queryListByNo(
                OrderPay.builder()
                        .payId(request.getPayId())
                        .orderId(request.getOrderId())
                        .channelPayId(request.getChannelPayId())
                        .build()
        );
        if (orderPay == null) {
            throw new BusinessException(PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getCode(), PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getMsg());
        }

        String outTransId = "";

        // 增加查询汇付支付状态
        if (request.getQueryChannel() != null && request.getQueryChannel()) {
            // 判断是否要查询汇付
            // 未支付的订单，查询汇付
            AdaPayPaymentListQueryDto adaPayPaymentListQueryDto = AdaPayPaymentListQueryDto.builder()
                    .appId(AdaPayInitWithMerConfig.appId)
                    .orderNo(orderPay.getPayId()).build();
            Map<String, Object> adaPayReverseMap = BeanUtil.beanToMap(adaPayPaymentListQueryDto, true, true);
            Map<String, Object> returnMap;
            try {
                log.info("待支付查询参数【getOne】 adaPayReverseMap={}:", adaPayReverseMap);
                returnMap = Payment.queryList(adaPayReverseMap);
                log.info("待支付查询结果【getOne】 returnMap={}:", returnMap);
                if (returnMap != null) {
                    Object o = returnMap.get(AdaPayConstant.PAYMENTS);
                    List<AdaPayPaymentResultDto> resultDtoList = JSONUtil.toList(new JSONArray(o), AdaPayPaymentResultDto.class);
                    if (CollUtil.isNotEmpty(resultDtoList)) {
                        AdaPayPaymentResultDto adaPayPaymentResultDto = resultDtoList.get(0);
                        if (AdaPayEnums.SUCCEEDED.getStatus().equals(adaPayPaymentResultDto.getStatus())) {
                            orderPay.setPayState(PayStateEnums.PAID.getStatus());
                            outTransId = adaPayPaymentResultDto.getOutTransId();

                            // 指定支付时间
                            String payTime = adaPayPaymentResultDto.getEndTime();
                            if (StrUtil.isNotBlank(payTime)) {
                                orderPay.setPayTime(DateUtil.parse(payTime, AdaPayConstant.TIME_FORMAT));
                            } else {
                                orderPay.setPayTime(new Date());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                log.error("查询汇付支付状态失败", e);
            }
        }
        OrderPayResp orderPayResp = JsonUtil.copy(orderPay, OrderPayResp.class);
        orderPayResp.setOutTransId(outTransId);
        return orderPayResp;
    }

    @Override
    public List<OrderPay> getUnPaidList() {
        LambdaQueryWrapper<OrderPay> queryWrapper = Wrappers.<OrderPay>lambdaQuery()
                .eq(OrderPay::getPayState, PayStateEnums.UNPAID.getStatus())
                .isNotNull(OrderPay::getChannelPayId);
        return orderPayRepository.list(queryWrapper);
    }

    @Override
    public void updateUnPaid(List<String> updateOrderIdList, Integer status) {
        orderPayRepository.updateUnPaid(updateOrderIdList, status);
    }

//    @Override
//    public List<OrderPay> getUnPaidListByTime(Date startTime, Date endTime) {
//        LambdaQueryWrapper<OrderPay> queryWrapper = Wrappers.<OrderPay>lambdaQuery()
//                .eq(OrderPay::getPayState, PayStateEnums.UNPAID.getStatus())
//                .isNotNull(OrderPay::getChannelPayId)
//                .ge(OrderPay::getCreateTime, startTime)
//                .le(OrderPay::getCreateTime, endTime);
//        return orderPayRepository.list(queryWrapper);
//    }

    @Override
    public void updateAndSendSyncPayment(AdaPaymentResultDto adaPaymentResultDto, boolean flag) {
        OrderPay orderPay = OrderPay.builder()
                .channelPayMsg(adaPaymentResultDto.getErrorMsg())
                .payState(adaPaymentResultDto.getPayStatus())
                .orderId(adaPaymentResultDto.getOrderId())
                .build();
        if (adaPaymentResultDto.getPayTime() != null) {
            orderPay.setPayTime(adaPaymentResultDto.getPayTime());
        }
        boolean update = update(orderPay);
        if (update && flag) {
            OrderPay byOrderId = orderPayRepository.getByOrderId(adaPaymentResultDto.getOrderId());
            adaPaymentResultDto.setBusinessType(byOrderId.getBusinessType());
//            List<OrderPayRecord> orderPayRecords = orderPayRecordRepository.queryOrderPayRecordByOrderIds(Arrays.asList(byOrderId.getOrderId()));
//            for(OrderPayRecord orderPayRecord : orderPayRecords) {
//                adaPaymentResultDto.setOrderId(orderPayRecord.getBatchNo());
                orderPayStatusChangeProducer.sendMessage(adaPaymentResultDto);
//            }
        }
    }

    public boolean update(OrderPay orderPay) {
        return orderPayRepository.update(
                Wrappers.<OrderPay>lambdaUpdate().eq(OrderPay::getOrderId, orderPay.getOrderId())
                        .ne(OrderPay::getPayState, PayStateEnums.PAID.getStatus())
                        .set(orderPay.getPayTime() != null, OrderPay::getPayTime, orderPay.getPayTime())
//                        .set(StrUtil.isNotBlank(orderPay.getConfirmPayId()), OrderPay::getConfirmPayId, orderPay.getConfirmPayId())
                        .set(OrderPay::getChannelPayMsg, orderPay.getChannelPayMsg())
                        .set(OrderPay::getPayState, orderPay.getPayState()));
    }
}
