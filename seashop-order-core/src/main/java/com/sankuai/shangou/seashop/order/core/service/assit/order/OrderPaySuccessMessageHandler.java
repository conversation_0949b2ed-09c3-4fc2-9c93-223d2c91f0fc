package com.sankuai.shangou.seashop.order.core.service.assit.order;

import com.sankuai.shangou.seashop.order.common.enums.PlatformMessageTemplateEnum;
import com.sankuai.shangou.seashop.order.common.remote.MemberRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.user.MemberContactBo;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import com.sankuai.shangou.seashop.order.core.service.assit.MessageNoticeHelper;
import com.sankuai.shangou.seashop.order.core.service.model.sms.ShopAndOrderMsgBo;
import com.sankuai.shangou.seashop.order.core.service.model.sms.UserNameAndOrderMsgBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderPaySuccessMessageHandler implements OrderChangeMessageHandler {

    @Resource
    private EsOrderService esOrderService;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private ProductSaleCountSyncAssist productSaleCountSyncAssist;
    @Resource
    private MessageNoticeHelper messageNoticeHelper;
    @Resource
    private MemberRemoteService memberRemoteService;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public OrderMessageEventEnum getEvent() {
        return OrderMessageEventEnum.PAY_SUCCESS;
    }

    @Override
    public void handle(OrderMessage message) {
        String orderId = message.getOrderId();
        log.info("【订单状态变更】支付成功, orderId: {}", orderId);
        Order dbOrder = orderRepository.getByOrderIdForceMaster(orderId);
        // 0元订单，没有支付记录
        if (dbOrder.getTotalAmount().compareTo(BigDecimal.ZERO) > 0) {
            OrderPayRecord payRecord = orderPayRecordRepository.getByOrderIdAndOutTransIdAndStatus(orderId, dbOrder.getGatewayOrderId(), PayStatusEnum.PAY_SUCCESS.getCode());
            if (payRecord == null) {
                log.warn("【订单状态变更】支付成功, 支付记录不存在, orderId: {}", orderId);
                return;
            }
            // 更新订单ES
            esOrderService.updateEsOrderPayData(orderId, payRecord.getPayNo(), payRecord.getOutTransId());
        }

        // 更新商品销量
        // productSaleCountSyncAssist.addProductSaleCount(orderId);

        // 发送短信通知，不影响主流程
        String siteName = messageNoticeHelper.getSiteName();
        try {
            MemberContactBo contact = memberRemoteService.getMemberContactByUserId(dbOrder.getUserId());
            // 构建消息体，邮箱和短信内容不一定一样
            String msgBody = messageNoticeHelper.buildEmailBody(PlatformMessageTemplateEnum.MERCHANT_PAY_SUCCESS, siteName,
                    contact.getUserName(), dbOrder.getOrderId());
            UserNameAndOrderMsgBo msgBo = new UserNameAndOrderMsgBo(contact.getUserName(), dbOrder.getOrderId());
            // 发送消息
            messageNoticeHelper.noticeForTemplate(contact, PlatformMessageTemplateEnum.MERCHANT_PAY_SUCCESS, msgBo, msgBody);
        } catch (Exception e) {
            log.error("【订单状态变更】支付成功, 给商家发送通知异常, orderId: {}", orderId, e);
        }
        // 给供应商发通知
        try {
            MemberContactBo shopContact = memberRemoteService.getMemberContactByShopId(dbOrder.getShopId());
            if (shopContact != null) {
                PlatformMessageTemplateEnum template = PlatformMessageTemplateEnum.SUPPLIER_ORDER_DELIVER;
                // 构建消息体，邮箱和短信内容不一定一样
                String msgBody = messageNoticeHelper.buildEmailBody(template, siteName, dbOrder.getOrderId());
                ShopAndOrderMsgBo msgBo = new ShopAndOrderMsgBo(dbOrder.getShopName(), dbOrder.getOrderId());
                // 发送消息
                messageNoticeHelper.noticeForTemplate(shopContact, template, msgBo, msgBody);
            }
        } catch (Exception e) {
            log.error("【订单状态变更】支付成功, 给供应商发送通知异常", e);
        }
    }
}
