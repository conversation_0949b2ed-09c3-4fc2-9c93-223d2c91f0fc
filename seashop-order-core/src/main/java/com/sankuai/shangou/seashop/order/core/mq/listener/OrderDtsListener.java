package com.sankuai.shangou.seashop.order.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.mq.model.DbTableDataChangeMessage;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderTableMessage;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;

import lombok.extern.slf4j.Slf4j;

/**
 * 订单表更新监听器，用于构建订单ES索引，提高查询性能
 * <AUTHOR>
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_ORDER_DTS,
//        group = MafkaConst.GROUP_ORDER_DTS)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_DTS + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_DTS + "_${spring.profiles.active}")
public class OrderDtsListener implements RocketMQListener<MessageExt> {

    @Resource
    private EsOrderService esOrderService;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext context) {
//        String body = (String) mafkaMessage.getBody();
//        log.info("【mafka消费】【订单表变更】消息内容为: {}", body);
//        DbTableDataChangeMessage<OrderTableMessage> messageWrapper = JsonUtil.parseObject(body,
//                new TypeReference<DbTableDataChangeMessage<OrderTableMessage>>() {});
//        esOrderService.buildEsOrder(messageWrapper.getData().getOrderId());
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【订单表变更】消息内容为: {}", body);
//        String body = (String) mafkaMessage.getBody();
        try {
            DbTableDataChangeMessage<OrderTableMessage> messageWrapper = JsonUtil.parseObject(body,
                    new TypeReference<DbTableDataChangeMessage<OrderTableMessage>>() {});
            esOrderService.buildEsOrder(messageWrapper.getData().getOrderId());
        } catch (Exception e) {
            log.error("【mafka消费】【订单表变更】处理失败", e);
            // 失败后，重试默认3次，监控发现问题，然后依赖定时任务补偿
            throw new RuntimeException(e);
        }

    }
}
