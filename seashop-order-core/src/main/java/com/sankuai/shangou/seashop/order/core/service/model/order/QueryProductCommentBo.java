package com.sankuai.shangou.seashop.order.core.service.model.order;

import java.util.List;

import com.sankuai.shangou.seashop.order.thrift.core.enums.CommentEnum;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/04 15:54
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class QueryProductCommentBo {

    /**
     * 店铺id 平台查询时不传
     */
    private Long shopId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 评分 1-5分
     */
    private Integer reviewMark;

    /**
     * 是否追加了评论
     */
    private boolean hasAppend;

    /**
     * 回复状态 0-全部 1-未处理
     */
    private Integer replyStatus;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 评论状态
     */
    private CommentEnum.MallCommentStatus status;

    /**
     * 是否过滤隐藏的评论(商城端需要设置为true)
     */
    private boolean filterHidden;

    /**
     * 是否有图片
     */
    private boolean hasImage;

    /**
     * 评分范围
     */
    private List<Integer> reviewMarkRange;

    /**
     * 规格ID
     */
    private String skuId;

    /**
     * 订单号
     */
    private String orderId;

    /**
     * 商家账号
     */
    private String userName;

    /**
     * 商家手机号
     */
    private String userMobile;

    /**
     * 是否从缓存中读取
     */
    private Boolean useCache = true;
}
