package com.sankuai.shangou.seashop.pay.core.assist.pay;


import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Value;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.pay.common.constant.AdaPayConstant;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.util.ObjectToMapConverter;
import com.sankuai.shangou.seashop.pay.core.assist.pay.util.PaymentCacheUtil;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseResultDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentResultDto;
import com.sankuai.shangou.seashop.pay.core.mq.publisher.OrderPayStatusChangeProducer;
import com.sankuai.shangou.seashop.pay.core.mq.publisher.OrderReverseProducer;
import com.sankuai.shangou.seashop.pay.core.service.ExchangeLogService;
import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ReverseOrder;
import com.sankuai.shangou.seashop.pay.dao.core.model.BasePayConfigModel;
import com.sankuai.shangou.seashop.pay.dao.core.repository.OrderPayRepository;
import com.sankuai.shangou.seashop.pay.dao.core.repository.ReverseOrderRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayResultCodeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseTypeEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ExchangeLogReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayPaymentCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayReverseCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayPaymentCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayReverseCreateResp;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/09/02 9:26
 */
@Slf4j
public abstract class AbstractPayHandler<Config extends BasePayConfigModel> implements PayHandler {

    @Resource
    private OrderPayRepository orderPayRepository;
    @Resource
    private LeafService leafService;
    @Resource
    private ExchangeLogService exchangeLogService;
    @Resource
    private ObjectToMapConverter objectToMapConverter;
    @Resource
    private ReverseOrderRepository reverseOrderRepository;
    @Resource
    private OrderPayStatusChangeProducer orderPayStatusChangeProducer;
    @Resource
    private OrderReverseProducer orderReverseProducer;
    @Resource
    private PaymentCacheUtil paymentCacheUtil;


    @Value("${wx.callback.url}")
    private String callBackUrl;
    @Value("${wx.refundCallBack.url}")
    private String refundCallBackUrl;

    /**
     * 发起支付
     *
     * @param payParam 支付参数
     * @return 支付结果
     */
    @SuppressWarnings("all")
    public PayPaymentCreateResp commonPay(PayPaymentCreateReq req) {
        PayParam payParam = JsonUtil.copy(req, PayParam.class);

        // 获取支付配置
        Config payConfig = getPayConfigCache();
        payParam.setPayConfig(payConfig);

        OrderPay orderPayCheck = orderPayRepository.getByOrderId(payParam.getOrderId());
        if (null != orderPayCheck) {
            throw new BusinessException(PayResultCodeEnum.ORDER_ID_EXIST.getCode(), PayResultCodeEnum.ORDER_ID_EXIST.getMsg());
        }

        // 支付渠道
        PaymentChannelEnum paymentChannel = paymentChannel();

        // 保存订单支付信息
        String payId = AdaPayConstant.PAY_ID_PREFIX + leafService.generateNoBySnowFlake(AdaPayConstant.LEAF_ADAPAY_PAY_ID_KEY);
        OrderPay orderPay = new OrderPay();
        orderPay.setOrderId(payParam.getOrderId());
        orderPay.setPayId(payId);
        orderPay.setPayAmount(payParam.getPayAmount());
        orderPay.setPayState(PayStateEnums.UNPAID.getStatus());
        // 默认的支付渠道
        orderPay.setPaymentChannel(paymentChannel.getCode());
        orderPay.setPaymentType(payParam.getPaymentType());
        orderPay.setBusinessType(payParam.getBusinessType());

        // 银行卡支付时，需要传入银行编码
        if (PaymentTypeEnum.COMPANY_BANK.getType().equals(payParam.getPaymentType())
                || PaymentTypeEnum.PERSON_BANK.getType().equals(payParam.getPaymentType())) {
            orderPay.setBankCode(payParam.getExpend().getAcctIssrId());
        }
        orderPayRepository.save(orderPay);

        try {
            // 指定支付id
            payParam.setPayId(payId);
            // 实际发起支付
            PayResult result = pay(payParam);

            try {
                // 入参、返参插表记录
                ExchangeLogReq exchangeLogReq = new ExchangeLogReq();
                exchangeLogReq.setType(paymentChannel.getPayLogType());
                exchangeLogReq.setMethod("com.sankuai.shangou.seashop.pay.core.assist.pay.AbstractPayHandler.commonPay");
                exchangeLogReq.setParam(JsonUtil.toJsonString(result.getRemoteReq()));
                exchangeLogReq.setResult(JsonUtil.toJsonString(result.getRemoteResp()));
                exchangeLogService.insetExchangeLog(exchangeLogReq);
            }
            catch (Exception e) {
                log.info("[支付] 记录第三方支付日志异常");
            }

            log.info("创建支付对象返回参数 returnMap={}", JsonUtil.toJsonString(result));

            // 支付没成功抛出异常
            if (!result.isSuccess()) {
                throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_CREATE_ERROR.getCode(), result.getErrorMsg());
            }

            // 保存第三方支付流水
            String channelPayId = result.getChannelPayId();
            if (StrUtil.isNotEmpty(channelPayId)) {
                orderPay.setChannelPayId(channelPayId);
                orderPayRepository.updateById(orderPay);
            }

            PayPaymentCreateResp resp = new PayPaymentCreateResp();
            resp.setChannelPayId(channelPayId);
            resp.setExpend(objectToMapConverter.objectToStringMap(result.getRemoteResp()));
            // 构建支付参数
            return resp;

        }
        catch (BusinessException e) {
            log.error("创建支付对象异常 code={}, message={}", e.getCode(), e.getMessage(), e);
            throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_CREATE_ERROR.getCode(), e.getMessage());
        }
    }

    /**
     * 公共支付回调处理
     *
     * @param notifyData 回调参数
     * @return 回调结果
     */
    public Object commonNotifyPay(String notifyData) {
        // 支付渠道
        PaymentChannelEnum paymentChannel = paymentChannel();

        // 入参、返参插表记录
        ExchangeLogReq exchangeLogReq = new ExchangeLogReq();
        exchangeLogReq.setType(paymentChannel.getPayNotifyLogType());
        exchangeLogReq.setMethod("com.sankuai.shangou.seashop.pay.core.assist.pay.AbstractPayHandler.commonNotify");
        exchangeLogReq.setParam(notifyData);

        PayNotifyResult result = null;
        try {
            result = notifyPay(notifyData);
            exchangeLogReq.setResult(JsonUtil.toJsonString(result));
        }
        catch (Exception e) {
            log.info("支付回调交互表写数失败");
        }
        finally {
            exchangeLogService.insetExchangeLog(exchangeLogReq);
        }

        // 处理解析出来的支付回调结果
        dealPayNotifyResult(result);

        return Optional.ofNullable(result).map(PayNotifyResult::getThirdResult).orElse(null);
    }

    /**
     * 处理解密后的支付回调结果
     * 更新支付状态 并发送mq通知
     */
    private void dealPayNotifyResult(PayNotifyResult result) {
        if (result == null) {
            return;
        }

        LockHelper.lock(LockConst.LOCK_PAY_CALLBACK + result.getPayId(), () -> {
            // 更新支付成功状态 并且发送通知消息
            OrderPay orderPay = orderPayRepository.getByPayId(result.getPayId());
            if (orderPay == null || !PayStateEnums.UNPAID.getStatus().equals(orderPay.getPayState())) {
                return;
            }

            // 更新支付状态
            if (result.getSuccess() != null) {
                orderPay.setPayState(result.getSuccess() ? PayStateEnums.PAID.getStatus() : PayStateEnums.PAY_FAILED.getStatus());
                orderPay.setPayTime(result.getPayTime());
                orderPay.setChannelPayId(result.getChannelPayId());
                orderPayRepository.updateById(orderPay);

                // 发送通知消息
                AdaPaymentResultDto paymentResultDto = new AdaPaymentResultDto();
                paymentResultDto.setOrderId(orderPay.getOrderId());
                paymentResultDto.setPayAmount(orderPay.getPayAmount());
                paymentResultDto.setPayStatus(orderPay.getPayState());
                paymentResultDto.setPayTime(orderPay.getPayTime());
                paymentResultDto.setPayId(orderPay.getPayId());
                paymentResultDto.setBusinessType(orderPay.getBusinessType());
                paymentResultDto.setOutTransId(orderPay.getChannelPayId());
                paymentResultDto.setBankCode(orderPay.getBankCode());

                orderPayStatusChangeProducer.sendMessage(paymentResultDto);
            }
        });
    }

    /**
     * 公共退款申请
     *
     * @param req 退款参数
     * @return 退款结果
     */
    public PayReverseCreateResp commonRefund(PayReverseCreateReq req) {
        RefundParam refundParam = JsonUtil.copy(req, RefundParam.class);

        OrderPay orderPay = orderPayRepository.getByOrderId(refundParam.getOrderId());
        if (orderPay == null) {
            throw new BusinessException(PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getCode(),
                    PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getMsg());
        }
        ReverseOrder reverseOrderTemp = reverseOrderRepository.getByReverseId(refundParam.getReverseId());
        if (reverseOrderTemp != null) {
            throw new BusinessException(PayResultCodeEnum.REVERSE_ID_EXIST.getCode(), PayResultCodeEnum.REVERSE_ID_EXIST.getMsg());
        }

        // 查询退款次数，单笔支付退款如果超过50次，不允许退款(查询退款中和退款成功的)
        Integer reverseCount = reverseOrderRepository.countByChannelPayIdAndStatus(orderPay.getChannelPayId(),
                Arrays.asList(ReverseStateEnums.REVERSE_ING.getStatus(), ReverseStateEnums.REVERSE_SUCCESS.getStatus()));
        if (reverseCount >= AdaPayConstant.MAX_REFUND_TIMES) {
            throw new BusinessException(PayResultCodeEnum.REVERSE_COUNT_OVER_LIMIT.getCode(), PayResultCodeEnum.REVERSE_COUNT_OVER_LIMIT.getMsg());
        }

        // 保存退款订单信息
        ReverseOrder reverseOrder = ReverseOrder.builder()
                .channelPayId(orderPay.getChannelPayId())
                .reverseAmount(req.getReverseAmount())
                .reverseId(req.getReverseId())
                .reverseState(ReverseStateEnums.REVERSE_ING.getStatus())
                .reverseType(ReverseTypeEnums.REVERSE_NO_SETTLEMENT.getType())
                .reverseTime(new Date())
                .businessType(orderPay.getBusinessType())
                .businessStatusType(req.getBusinessStatusType())
                .build();
        reverseOrderRepository.save(reverseOrder);

        try {
            // 发起实际的退款操作
            refundParam.setChannelPayId(orderPay.getChannelPayId());
            refundParam.setPaymentChannel(orderPay.getPaymentChannel());
            refundParam.setPayConfig(getPayConfigCache());
            refundParam.setOrderAmount(orderPay.getPayAmount());
            RefundResult refundResult = refund(refundParam);

            if (!refundResult.isSuccess()) {
                throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_REVERSE_CREATE_ERROR.getCode(), refundResult.getErrorMsg());
            }

            if (StrUtil.isNotEmpty(refundResult.getChannelRefundId())) {
                reverseOrder.setChannelRefundId(refundResult.getChannelRefundId());
                reverseOrderRepository.updateById(reverseOrder);
            }

            PayReverseCreateResp resp = new PayReverseCreateResp();
            resp.setChannelRefundId(refundResult.getChannelRefundId());
            return resp;
        }
        catch (BusinessException e) {
            reverseOrder.setReverseState(ReverseStateEnums.REVERSE_ERROR.getStatus());
            reverseOrder.setChannelRefundMsg(e.getMessage());
            reverseOrderRepository.updateById(reverseOrder);
            throw e;
        }
        catch (Exception e) {
            log.error("申请退款异常", e);
            throw new BusinessException(PayResultCodeEnum.PAY_PAYMENT_REVERSE_CREATE_ERROR.getCode(), PayResultCodeEnum.PAY_PAYMENT_REVERSE_CREATE_ERROR.getMsg());
        }
    }

    /**
     * 公共退款回调处理
     *
     * @param notifyData 回调参数
     * @return 回调结果
     */
    public Object commonNotifyRefund(String notifyData) {
        // 支付渠道
        PaymentChannelEnum paymentChannel = paymentChannel();

        // 入参、返参插表记录
        ExchangeLogReq exchangeLogReq = new ExchangeLogReq();
        exchangeLogReq.setType(paymentChannel.getPayNotifyLogType());
        exchangeLogReq.setMethod("com.sankuai.shangou.seashop.pay.core.assist.pay.AbstractPayHandler.commonNotifyRefund");
        exchangeLogReq.setParam(notifyData);

        RefundNotifyResult result = null;
        try {
            result = notifyRefund(notifyData);
            exchangeLogReq.setResult(JsonUtil.toJsonString(result));
        }
        catch (Exception e) {
            log.info("退款回调交互表写数失败");
        }
        finally {
            exchangeLogService.insetExchangeLog(exchangeLogReq);
        }

        // 处理退款通知结果
        dealRefundNotifyResult(result);

        return Optional.ofNullable(result).map(RefundNotifyResult::getThirdResult).orElse(null);
    }

    /**
     * 处理解密后的退款回调结果
     * 更新退款状态 并发送mq通知
     */
    private void dealRefundNotifyResult(RefundNotifyResult result) {
        if (result == null) {
            return;
        }

        // 更新退款成功状态 并且发送通知消息
        LockHelper.lock(LockConst.LOCK_REFUND_CALLBACK + result.getReverseId(), () -> {
            ReverseOrder reverseOrder = reverseOrderRepository.getByReverseId(result.getReverseId());
            if (reverseOrder == null || !ReverseStateEnums.REVERSE_ING.getStatus().equals(reverseOrder.getReverseState())) {
                return;
            }

            // 更新支付状态
            if (result.getSuccess() != null) {
                reverseOrder.setReverseState(result.getSuccess() ? ReverseStateEnums.REVERSE_SUCCESS.getStatus() : ReverseStateEnums.REVERSE_ERROR.getStatus());
                reverseOrder.setReverseTime(result.getRefundDate());
                reverseOrder.setChannelRefundId(result.getChannelRefundId());

                reverseOrderRepository.updateById(reverseOrder);

                // 发送通知消息
                AdaPayReverseResultDto paymentResultDto = new AdaPayReverseResultDto();
                paymentResultDto.setRefundId(reverseOrder.getReverseId());
                paymentResultDto.setChannelRefundId(reverseOrder.getChannelRefundId());
                paymentResultDto.setRefundedAmt(reverseOrder.getReverseAmount().toString());
                paymentResultDto.setPayStatus(reverseOrder.getReverseState());

                OrderPay orderPay = orderPayRepository.getByChannelPayId(reverseOrder.getChannelPayId());
                paymentResultDto.setOrderNo(orderPay.getOrderId());
                paymentResultDto.setBusinessType(orderPay.getBusinessType());
                paymentResultDto.setChannelRefundId(reverseOrder.getChannelRefundId());
                paymentResultDto.setBusinessStatusType(reverseOrder.getBusinessStatusType());

                orderReverseProducer.sendMessage(paymentResultDto);
            }
        });
    }

    /**
     * 公共支付确认
     *
     * @param confirmParam 支付确认参数
     * @return 支付确认结果
     */
    public PayConfirmResult commonPayConfirm(PayConfirmParam confirmParam) {
        // 查询支付记录
        OrderPay orderPay = null;
        if (StrUtil.isNotEmpty(confirmParam.getChannelPayId())) {
            orderPay = orderPayRepository.getByChannelPayId(confirmParam.getChannelPayId());
        }
        else {
            orderPay = orderPayRepository.getByPayId(confirmParam.getPayId());
        }

        if (orderPay == null) {
            log.error("【支付确认】 支付记录不存在, param: {}", JsonUtil.toJsonString(confirmParam));
            throw new BusinessException(PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getCode(), PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getMsg());
        }

        PayConfirmResult confirmResult = new PayConfirmResult();
        confirmResult.setPayId(orderPay.getPayId());
        confirmResult.setChannelPayId(orderPay.getChannelPayId());
        confirmResult.setPayTime(orderPay.getPayTime());

        // 如果不是待支付状态 表示支付结果已经确认 无需处理
        if (!PayStateEnums.UNPAID.getStatus().equals(orderPay.getPayState())) {
            log.info("【支付确认】 订单支付不是未支付状态, 无需处理, param: {}", JsonUtil.toJsonString(confirmParam));
            confirmResult.setSuccess(PayStateEnums.PAID.getStatus().equals(orderPay.getPayState()));
            return confirmResult;
        }

        // 调用第三方确认
        confirmResult = payConfirm(confirmParam);
        // 复用支付回调逻辑
        dealPayNotifyResult(JsonUtil.copy(confirmResult, PayNotifyResult.class));
        return confirmResult;
    }

    /**
     * 公共退款确认
     *
     * @param confirmParam 退款确认参数
     * @return 退款确认结果
     */
    public RefundConfirmResult commonRefundConfirm(RefundConfirmParam confirmParam) {
        // 查询退款记录
        ReverseOrder reverseOrder = reverseOrderRepository.getByReverseId(confirmParam.getReverseId());
        if (reverseOrder == null) {
            log.error("【退款确认】 退款记录不存在, param: {}", JsonUtil.toJsonString(confirmParam));
            throw new BusinessException(PayResultCodeEnum.REVERSE_NOT_EXIST.getCode(), PayResultCodeEnum.REVERSE_NOT_EXIST.getMsg());
        }

        RefundConfirmResult confirmResult = new RefundConfirmResult();
        confirmResult.setChannelRefundId(reverseOrder.getChannelRefundId());
        confirmResult.setReverseId(reverseOrder.getReverseId());
        confirmResult.setRefundDate(reverseOrder.getReverseTime());

        if (!ReverseStateEnums.REVERSE_ING.getStatus().equals(reverseOrder.getReverseState())) {
            log.info("【退款确认】 退款记录不是待处理状态,  无需处理, param: {}", JsonUtil.toJsonString(confirmParam));
            confirmResult.setSuccess(ReverseStateEnums.REVERSE_SUCCESS.getStatus().equals(reverseOrder.getReverseState()));
            return confirmResult;
        }

        // 调用第三方确认
        confirmParam.setChannelPayId(reverseOrder.getChannelPayId());
        confirmResult = refundConfirm(confirmParam);
        // 复用退款回调逻辑
        dealRefundNotifyResult(JsonUtil.copy(confirmResult, RefundNotifyResult.class));
        return confirmResult;
    }

    /**
     * 支付渠道
     *
     * @return 支付渠道
     */
    protected abstract PaymentChannelEnum paymentChannel();

    /**
     * 获取初始化支付配置
     *
     * @return 支付配置
     */
    protected abstract Config getInitPayConfig();

    /**
     * 获取支付配置
     *
     * @return 支付配置
     */
    protected Config getPayConfig() {
        Config payConfig = getInitPayConfig();
        payConfig.setCallBackUrl(callBackUrl + "/" + paymentChannel().getName());
        payConfig.setRefundCallBackUrl(refundCallBackUrl + "/" + paymentChannel().getName());
        return payConfig;
    }

    /**
     * 获取支付配置(使用缓存)
     *
     * @return 支付配置
     */
    protected Config getPayConfigCache() {
        return (Config) paymentCacheUtil.getPayConfig(paymentChannel(), this::getPayConfig);
    }
}
