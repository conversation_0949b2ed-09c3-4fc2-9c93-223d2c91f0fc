package com.sankuai.shangou.seashop.order.core.service.model.pay;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class InitiatePayBo {

    /**
     * 订单ID
     */
    private List<String> orderIdList;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 选择的支付方式
     */
    private Integer payMethod;
    /**
     * openId.小程序支付必传
     */
    private String openId;
    /**
     * 银行编号
     */
    private String bankCode;
    /**
     * 发起支付的客户端IP
     */
    private String clientIp;
    /**
     * 支付成功后的回调地址
     */
    private String callbackUrl;

    /**
     * 支付渠道:1汇付天下支付 2微信支付 3支付宝支付
     */
    private Integer paymentChannel;

}
