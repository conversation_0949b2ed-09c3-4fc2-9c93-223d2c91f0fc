package com.sankuai.shangou.seashop.pay.core.service.adapay.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.pay.common.constant.AdaPayConstant;
import com.sankuai.shangou.seashop.pay.common.enums.AdaPayEnums;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseResultDto;
import com.sankuai.shangou.seashop.pay.core.service.ReverseOrderService;
import com.sankuai.shangou.seashop.pay.core.service.adapay.CallBackStrategyService;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseTypeEnums;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description: 退款状态回调（未分账退款）
 */
@Component("payment_reverse")
@Slf4j
public class PaymentReverseCallBackImpl implements CallBackStrategyService {

    @Resource
    private ReverseOrderService reverseOrderService;

    @Override
    public void callback(String data) {
        log.info("支付撤销回调(未分账前)处理data={}", data);
        JSONObject jsonObject = JSONUtil.parseObj(data);

        String status = jsonObject.get(AdaPayConstant.STATUS).toString();
        String orderNo = jsonObject.get(AdaPayConstant.ORDER_NO).toString();
        String refundedAmt = jsonObject.get(AdaPayConstant.REFUNDED_AMT).toString();
        String errorMessage = "";
        if (jsonObject.get(AdaPayConstant.ERROR_MSG) != null) {
            errorMessage = jsonObject.get(AdaPayConstant.ERROR_MSG).toString();
        }

        AdaPayReverseResultDto adaPayReverseResultDto = new AdaPayReverseResultDto();
        adaPayReverseResultDto.setType(ReverseTypeEnums.REVERSE_NO_SETTLEMENT.getType());
        adaPayReverseResultDto.setRefundId(orderNo);
        adaPayReverseResultDto.setRefundedAmt(refundedAmt);
        adaPayReverseResultDto.setErrorMessage(errorMessage);
        if (AdaPayEnums.SUCCEEDED.getStatus().equals(status)) {
            adaPayReverseResultDto.setPayStatus(PayStateEnums.PAID.getStatus());
        } else {
            adaPayReverseResultDto.setPayStatus(PayStateEnums.PAY_FAILED.getStatus());
        }
        reverseOrderService.updateAndSendSyncReverse(adaPayReverseResultDto);
    }
}
