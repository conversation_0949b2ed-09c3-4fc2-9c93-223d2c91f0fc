package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.core.service.ExceptionOrderService;
import com.sankuai.shangou.seashop.order.thrift.core.ExceptionOrderCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.ConfirmExceptionOrderReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/exceptionOrderCmd")
public class ExceptionOrderCmdController implements ExceptionOrderCmdFeign {

    @Resource
    private ExceptionOrderService exceptionOrderService;

    @PostMapping(value = "/confirm", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> confirm(@RequestBody ConfirmExceptionOrderReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【异常订单】确认退款", req, func -> {
            // 业务逻辑处理
            exceptionOrderService.confirmRefund(req);
            return BaseResp.of();
        });
    }
}
