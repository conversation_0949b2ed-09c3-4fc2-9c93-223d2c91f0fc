package com.sankuai.shangou.seashop.order.core.service.model.stats;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 平台首页统计交易数据返回对象
 * <AUTHOR>
 */
@Getter
@Setter
public class PlatformStatsTradeDataBo {

    /**
     * 交易笔数=[已完成]订单数量
     */
    private Long completedOrderCount;
    /**
     * 待付款订单数量
     */
    private Long underPayOrderCount;
    /**
     * 待发货订单数量
     */
    private Long underDeliveryOrderCount;
    /**
     * 待处理 退款单数量
     */
    private Long underDealRefundCount;
    /**
     * 待处理 退货单数量
     */
    private Long underDealReturnCount;
    /**
     * 待处理 投诉单数量
     */
    private Long underDealComplaintCount;
    /**
     * 今日有效交易金额
     */
    private BigDecimal todayEffectiveAmount;

    public static PlatformStatsTradeDataBo defaultZero() {
        PlatformStatsTradeDataBo bo = new PlatformStatsTradeDataBo();
        bo.setUnderDealReturnCount(0L);
        bo.setUnderDealComplaintCount(0L);
        bo.setUnderDealRefundCount(0L);
        bo.setUnderDeliveryOrderCount(0L);
        bo.setUnderPayOrderCount(0L);
        bo.setCompletedOrderCount(0L);
        bo.setTodayEffectiveAmount(BigDecimal.ZERO);
        return bo;
    }
}
