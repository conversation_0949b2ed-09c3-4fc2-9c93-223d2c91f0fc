package com.sankuai.shangou.seashop.order.core.statemachine.action;

import com.alibaba.cola.statemachine.Action;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.remote.PayRemoteService;
import com.sankuai.shangou.seashop.order.core.mq.model.pay.PayResultBo;
import com.sankuai.shangou.seashop.order.core.service.assit.pay.OrderPayResultHandlerAssist;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderEvent;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 取消支付事件发生时的动作
 * <p>支付中才能取消</p>
 * <AUTHOR>
 */
@Service
@Slf4j
public class CancelPayAction extends BaseAction implements Action<OrderStatusEnum, OrderEvent, OrderContext> {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private PayRemoteService payRemoteService;
    @Resource
    private OrderPayResultHandlerAssist orderPayResultHandlerAssist;

    @Override
    public void executeStatusChange(OrderContext context, Order dbOrder) {
        Long userId = context.getUserId();
        String orderId = context.getOrderId();
        log.info("【订单状态机】用户取消支付,userId={},orderId={}", userId, orderId);
        // 支付中才能取消，所以取最新的支付中的记录
        OrderPayRecord latestPayRecord = orderPayRecordRepository.getLatestByOrderIdAndStatus(orderId, PayStatusEnum.PAYING.getCode());
        if (latestPayRecord == null) {
            throw new BusinessException("没有支付中的支付记录");
        }
        // 支付中的状态可能是支付回调有问题，所以保险起见，再次调用支付服务的查询接口，确认一下
        OrderPayResp orderPayResp = payRemoteService.queryPayResult(latestPayRecord.getBatchNo());
        if (orderPayResp == null || PayStateEnums.UNPAID.getStatus().equals(orderPayResp.getPayState())) {
            log.warn("【订单状态机】用户取消支付,支付中的支付记录没有查询到支付信息或还是支付中,userId={},orderId={}", userId, orderId);
            // 修改订单状态
            // 订单发起支付时，就变成了支付中，此时，可能用户确实没有实际支付，可能尚未拿到支付回调
            // 用户取消支付时，直接将状态改为待付款，如果是没有实际支付，没有问题；如果是尚未拿到支付回调，等拿到支付回调时，如果状态是待付款或者支付中，都改为已支付(待发货)
            // 如果是支付中，代表用户可能再次发起了支付，可能导致重复支付，这个时候，支付回调的处理中，重复支付的会进入异常订单，有平台处理退款
            TransactionHelper.doInTransaction(() -> {
                orderRepository.updateOrderStatus(Collections.singletonList(dbOrder.getOrderId()),
                        OrderStatusEnum.PAYING.getCode(), OrderStatusEnum.UNDER_PAY.getCode());
                // 支付记录状态改为关闭
                latestPayRecord.setPayStatus(PayStatusEnum.CLOSE.getCode());
                orderPayRecordRepository.updateById(latestPayRecord);
            });
            return;
        }
        // 此时，支付记录有支付结果，则需要处理支付结果
        // 对于支付失败的，orderPayResultHandlerAssist 会将订单设置成 待付款，而取消支付也是这个目的，所以不需要额外处理
        PayResultBo payResultBo = JsonUtil.copy(orderPayResp, PayResultBo.class);
        payResultBo.setErrorMsg(orderPayResp.getChannelPayMsg());
        orderPayResultHandlerAssist.handlePayResult(payResultBo);
        // 如果是已支付，说明支付成功，此时，订单状态已经变更，不需要再次处理
        if (PayStateEnums.PAID.getStatus().equals(orderPayResp.getPayState())) {
            // 抛出异常是为了提示用户不能取消，数据的修改在orderPayResultHandlerAssist中事务处理，这里抛出异常没关系
            throw new BusinessException("当前订单已经支付成功，不能取消支付");
        }
    }

    @Override
    protected void validateBizData(OrderContext context, Order dbOrder) {
        if (!dbOrder.getOrderStatus().equals(OrderStatusEnum.PAYING.getCode())) {
            throw new BusinessException("支付中的订单才能取消支付");
        }
    }

    @Override
    protected void dealExecuteResult(OrderContext context, ActionResult executeResult) {
        if (!executeResult.isSuccess()) {
            throw new BusinessException("订单状态已经变更，不需要再次处理");
        }
    }
}
