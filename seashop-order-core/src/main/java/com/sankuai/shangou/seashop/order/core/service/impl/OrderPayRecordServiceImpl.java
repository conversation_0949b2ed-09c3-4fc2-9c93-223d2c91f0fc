package com.sankuai.shangou.seashop.order.core.service.impl;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.OrderPayRecordService;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayRecordResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/5 15:48
 */
@Slf4j
@Service
public class OrderPayRecordServiceImpl implements OrderPayRecordService {

    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;

    @Override
    public List<OrderPayRecordResp> queryOrderPayRecordList(List<String> batchNoList) {
        List<OrderPayRecord> orderPayRecords = orderPayRecordRepository.queryOrderPayRecordList(batchNoList);
        return JsonUtil.copyList(orderPayRecords, OrderPayRecordResp.class);
    }

    @Override
    public List<OrderPayRecordResp> queryOrderPayRecordByOrderIds(List<String> orderIds) {
        List<OrderPayRecord> orderPayRecords = orderPayRecordRepository.queryOrderPayRecordByOrderIds(orderIds);
        return JsonUtil.copyList(orderPayRecords, OrderPayRecordResp.class);
    }
}
