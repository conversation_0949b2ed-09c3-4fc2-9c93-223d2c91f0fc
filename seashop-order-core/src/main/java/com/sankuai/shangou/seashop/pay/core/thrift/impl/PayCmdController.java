package com.sankuai.shangou.seashop.pay.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.pay.common.constant.LockConstant;
import com.sankuai.shangou.seashop.pay.core.assist.pay.AbstractPayHandler;
import com.sankuai.shangou.seashop.pay.core.assist.pay.PayFactory;
import com.sankuai.shangou.seashop.pay.core.assist.pay.util.PaymentChannelUtil;
import com.sankuai.shangou.seashop.pay.core.service.PayService;
import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import com.sankuai.shangou.seashop.pay.dao.core.repository.OrderPayRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayResultCodeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayPaymentCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayReverseCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaymentConfirmCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayPaymentCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayReverseCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPaymentConfirmCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayPaymentCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayReverseCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PaymentConfirmCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPayPaymentCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPayReverseCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPaymentConfirmCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.PayCmdFeign;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
@RestController
@RequestMapping("/pay")
public class PayCmdController implements PayCmdFeign {

    @Resource
    private PayService payService;
    @Resource
    private PayFactory payFactory;
    @Resource
    private PaymentChannelUtil paymentChannelUtil;
    @Resource
    private OrderPayRepository orderPayRepository;

    @PostMapping(value = "/createPayment", consumes = "application/json")
    @Override
    public ResultDto<PayPaymentCreateResp>  createPayment(@RequestBody PayPaymentCreateReq request)  {
        return ThriftResponseHelper.responseInvoke("paymentCreate", request, req -> {
            req.checkParameter();

            // 如果没有指定支付渠道
            if (request.getPaymentChannel() == null) {
                PaymentChannelEnum paymentChannelEnum = paymentChannelUtil.autoMatchPaymentChannel(req.getPaymentType());
                request.setPaymentChannel(paymentChannelEnum.getCode());
            }

            String lockKey = String.format(LockConstant.PAYMENT_CREATE_LOCK_KEY, req.getOrderId());
            PayPaymentCreateResp payPaymentCreateResp = LockHelper.lock(lockKey, LockConstant.LOCK_TIME,
                    () -> {
                        if (request.getPaymentChannel().equals(PaymentChannelEnum.ADAPAY.getCode())) {
                            AdaPayPaymentCreateResp payment = payService.createPayment(JsonUtil.copy(req, AdaPayPaymentCreateReq.class));
                            return JsonUtil.copy(payment, PayPaymentCreateResp.class);
                        } else {
                            PaymentChannelEnum payChannel = PaymentChannelEnum.getByCode(request.getPaymentChannel());
                            AbstractPayHandler payHandler = payFactory.getPayHandler(payChannel);
                            return payHandler.commonPay(req);
                        }
                    });
            return payPaymentCreateResp;
        });
    }

    @PostMapping(value = "/createPaymentReverse", consumes = "application/json")
    @Override
    public ResultDto<PayReverseCreateResp> createPaymentReverse(@RequestBody PayReverseCreateReq request)  {
        return ThriftResponseHelper.responseInvoke("paymentReverseCreate", request, req -> {
            req.checkParameter();

            OrderPay orderPay = orderPayRepository.getByOrderId(req.getOrderId());
            if (orderPay == null) {
                throw new BusinessException(PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getCode(),
                        PayResultCodeEnum.ORDER_PAY_NOT_EXIST.getMsg());
            }
            PaymentChannelEnum paymentChannelEnum = paymentChannelUtil.checkPaymentChannel(orderPay.getPaymentChannel());

            String lockKey = String.format(LockConstant.REVERSE_CREATE_LOCK_KEY, req.getOrderId());
            PayReverseCreateResp payReverseCreateResp = LockHelper.lock(lockKey, LockConstant.LOCK_TIME,
                    () -> {
                        if (PaymentChannelEnum.ADAPAY.equals(paymentChannelEnum)) {
                            // 需要先查询，判断哪个支付渠道(目前只支持一个支付渠道，暂未处理)
                            AdaPayReverseCreateResp paymentReverse = payService.createPaymentReverse(JsonUtil.copy(req, AdaPayReverseCreateReq.class));
                            return JsonUtil.copy(paymentReverse, PayReverseCreateResp.class);
                        } else {
                            return payFactory.getPayHandler(paymentChannelEnum).commonRefund(req);
                        }
                    });
            return payReverseCreateResp;
        });
    }

    @PostMapping(value = "/createPaymentConfirm", consumes = "application/json")
    @Override
    public ResultDto<PaymentConfirmCreateResp> createPaymentConfirm(@RequestBody PaymentConfirmCreateReq request)  {
        return ThriftResponseHelper.responseInvoke("paymentConfirmCreate", request, req -> {
            req.checkParameter();

            String lockKey = String.format(LockConstant.PAYMENT_CONFIRM_LOCK_KEY, req.getSourceOrderId());
            PaymentConfirmCreateResp paymentConfirmCreateResp = LockHelper.lock(lockKey, LockConstant.LOCK_TIME,
                    () -> {
                        // 需要先查询，判断哪个支付渠道(目前只支持一个支付渠道，暂未处理)
                        AdaPaymentConfirmCreateResp paymentConfirm = payService.createPaymentConfirm(JsonUtil.copy(req, AdaPaymentConfirmCreateReq.class));
                        return JsonUtil.copy(paymentConfirm, PaymentConfirmCreateResp.class);
                    });
            return paymentConfirmCreateResp;
        });
    }
}
