package com.sankuai.shangou.seashop.pay.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.core.bo.ChannelConfigQueryBo;
import com.sankuai.shangou.seashop.pay.core.service.ChannelConfigService;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ChannelConfig;
import com.sankuai.shangou.seashop.pay.thrift.core.dto.AdaPayConfigModelDto;
import com.sankuai.shangou.seashop.pay.thrift.core.dto.ChannelConfigInfoDto;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ChannelConfigQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.ChannelConfigListResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.ChannelConfigQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@RestController
@RequestMapping("/channelConfig")
public class ChannelConfigQueryController implements ChannelConfigQueryFeign {

    @Resource
    private ChannelConfigService channelConfigService;

    @PostMapping(value = "/queryList", consumes = "application/json")
    @Override
    public ResultDto<ChannelConfigListResp> queryList(@RequestBody ChannelConfigQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryList", request, req -> {
            req.checkParameter();
            ChannelConfigQueryBo saveBo = JsonUtil.copy(req, ChannelConfigQueryBo.class);
            List<ChannelConfig> channelConfigModels = channelConfigService.queryList(saveBo);
            ChannelConfigListResp resp = new ChannelConfigListResp();
            resp.setConfigList(JsonUtil.copyList(channelConfigModels, ChannelConfigInfoDto.class));
            return resp;
        });
    }

    @GetMapping(value = "/getAdaPayConfig")
    @Override
    public ResultDto<AdaPayConfigModelDto> getAdaPayConfigModel() {
        return ThriftResponseHelper.responseInvoke("getAdaPayConfigModel", null, req -> {
            return JsonUtil.copy(channelConfigService.getAdaPayConfigModel(), AdaPayConfigModelDto.class);
        });
    }
}
