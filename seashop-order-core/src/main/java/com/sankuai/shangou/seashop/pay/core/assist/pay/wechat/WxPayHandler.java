package com.sankuai.shangou.seashop.pay.core.assist.pay.wechat;

import java.util.HashMap;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.hishop.starter.storage.client.StorageClient;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.core.assist.pay.AbstractPayHandler;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.mode.AbstractWxPayModeProxy;
import com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.mode.WxPayModeProxyFactory;
import com.sankuai.shangou.seashop.pay.dao.core.model.WxPayConfigModel;
import com.sankuai.shangou.seashop.pay.dao.core.repository.ChannelConfigRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/09/02 9:33
 */
@Component
@Slf4j
public class WxPayHandler extends AbstractPayHandler<WxPayConfigModel> {

    @Resource
    private ChannelConfigRepository channelConfigRepository;
    @Resource
    private WxPayModeProxyFactory wxPayModeProxyFactory;
    @Resource
    private StorageClient storageClient;

    @Override
    public PayResult pay(PayParam payParam) {
        log.info("【发起支付-微信支付】调用微信支付请求参数{}", payParam);
        WxPayConfigModel payConfig = (WxPayConfigModel) payParam.getPayConfig();

        // 获取微信支付的类型-此方法校验了支付类型是否开启
        PaymentTypeEnum paymentType = PaymentTypeEnum.getByType(payParam.getPaymentType());
        String appId = getTradeAppIdIfEnable(payConfig, paymentType);

        // 获取支付模式代理
        AbstractWxPayModeProxy modeProxy = wxPayModeProxyFactory.buildWxPayModeProxy(payConfig);
        modeProxy.setAppId(appId);
        modeProxy.setPaymentType(paymentType);

        PayResult payResult = modeProxy.pay(payParam);
        if (!payResult.isSuccess()) {
            return payResult;
        }

        // 兼容旧的结构
        switch (paymentType) {
            case WECHAT_APPLET:
                Map<String, Object> orderMap = BeanUtil.beanToMap(payResult.getRemoteResp());
                orderMap.put("package", orderMap.get("packageValue"));
                orderMap.remove("packageValue");

                Map<String, Object> appResult = new HashMap<>();
                appResult.put("pay_info", orderMap);
                payResult.setRemoteResp(appResult);
                break;
            case WECHAT_H5:
                Map<String, Object> h5Result = new HashMap<>();
                h5Result.put("h5_url", payResult.getRemoteResp());
                Map<String, Object> appResult2 = new HashMap<>();
                appResult2.put("pay_info", h5Result);
                payResult.setRemoteResp(appResult2);
                break;
            case WECHAT_NATIVE:
                Map<String, Object> nativeResult = new HashMap<>();
                nativeResult.put("qrcode_url", payResult.getRemoteResp());
                payResult.setRemoteResp(nativeResult);
                break;
            case WECHAT_JS:
                Map<String, Object> orderMap1 = BeanUtil.beanToMap(payResult.getRemoteResp());
                orderMap1.put("package", orderMap1.get("packageValue"));
                orderMap1.remove("packageValue");

                Map<String, Object> appResult1 = new HashMap<>();
                appResult1.put("pay_info", orderMap1);
                payResult.setRemoteResp(appResult1);
                break;
            default:
                break;
        }
        return payResult;
    }

    @Override
    public PayNotifyResult notifyPay(String payData) {
        log.info("【微信支付回调】{}", payData);
        WxPayConfigModel payConfig = getPayConfigCache();
        AbstractWxPayModeProxy modeProxy = wxPayModeProxyFactory.buildWxPayModeProxy(payConfig);
        PayNotifyResult result = modeProxy.notifyPay(payData);
        return result;
    }

    @Override
    public PayConfirmResult payConfirm(PayConfirmParam confirmParam) {
        WxPayConfigModel payConfig = getPayConfigCache();

        // 获取支付模式代理
        AbstractWxPayModeProxy modeProxy = wxPayModeProxyFactory.buildWxPayModeProxy(payConfig);
        PayConfirmResult confirmResult = modeProxy.payConfirm(confirmParam);
        return confirmResult;
    }

    @Override
    public RefundResult refund(RefundParam refundParam) {
        log.info("【发起微信退款】 param: {}", JsonUtil.toJsonString(refundParam));
        WxPayConfigModel payConfig = (WxPayConfigModel) refundParam.getPayConfig();
        AbstractWxPayModeProxy modeProxy = wxPayModeProxyFactory.buildWxPayModeProxy(payConfig);
        return modeProxy.refund(refundParam);
    }

    @Override
    public RefundConfirmResult refundConfirm(RefundConfirmParam refundConfirmParam) {

        return null;
    }

    @Override
    public RefundNotifyResult notifyRefund(String refundData) {
        log.info("【微信退款回调】{}", refundData);
        WxPayConfigModel payConfig = getPayConfigCache();
        AbstractWxPayModeProxy modeProxy = wxPayModeProxyFactory.buildWxPayModeProxy(payConfig);
        RefundNotifyResult result = modeProxy.notifyRefund(refundData);
        return result;
    }

    @Override
    protected PaymentChannelEnum paymentChannel() {
        return PaymentChannelEnum.WXPAY;
    }

    @Override
    protected WxPayConfigModel getInitPayConfig() {
        WxPayConfigModel wxPayConfig = channelConfigRepository.getWxPayConfig();
        if (StrUtil.isNotEmpty(wxPayConfig.getServiceP12KeyPath()) && wxPayConfig.getServiceMode()) {
            try {
                String serviceRemoteUrl = storageClient.formatUrl(wxPayConfig.getServiceP12KeyPath());
                wxPayConfig.setServiceP12KeyContent(HttpUtil.downloadBytes(serviceRemoteUrl));
            } catch (Exception e) {
                log.error("【微信支付】下载服务商证书失败", e);
            }
        }

        if (StrUtil.isNotEmpty(wxPayConfig.getP12KeyPath())) {
            try {
                String remoteUrl = storageClient.formatUrl(wxPayConfig.getP12KeyPath());
                wxPayConfig.setP12KeyContent(HttpUtil.downloadBytes(remoteUrl));
            } catch (Exception e) {
                log.error("【微信支付】下载商户证书失败", e);
            }
        }
        return wxPayConfig;
    }

    private String getTradeAppIdIfEnable(WxPayConfigModel payConfig, PaymentTypeEnum paymentType) {
        switch (paymentType) {
            case WECHAT_APPLET:
                AssertUtil.throwIfTrue(!payConfig.getEnableApplet(), "小程序支付未开启");
                return payConfig.getMiniProgramAppId();
            case WECHAT_H5:
                AssertUtil.throwIfTrue(!payConfig.getEnableH5(), "H5支付未开启");
                return payConfig.getOfficialAccountAppId();
            case WECHAT_JS:
                AssertUtil.throwIfTrue(!payConfig.getEnableH5(), "微商城支付未开启");
                return payConfig.getOfficialAccountAppId();
            case WECHAT_NATIVE:
                AssertUtil.throwIfTrue(!payConfig.getEnableNative(), "Native支付未开启");
                return payConfig.getNativeAppId();
            default:
                AssertUtil.throwIfTrue(true, "不支持的支付类型");
                return null;
        }
    }
}
