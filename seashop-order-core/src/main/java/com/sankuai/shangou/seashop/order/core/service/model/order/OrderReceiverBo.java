package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderReceiverBo extends BaseParamReq {

    /**
     * 订单ID
     */
    @PrimaryField
    private String orderId;
    @ExaminField(description = "顶级区域ID")
    private Integer topRegionId;
    /**
     * 区域ID
     */
    @ExaminField(description = "区域ID")
    private Integer regionId;
    /**
     * 收货人
     */
    @ExaminField(description = "收货人")
    private String shipTo;
    /**
     * 收货地址
     */
    @ExaminField(description = "收货地址")
    private String address;
    /**
     * 详细地址
     */
    private String addressDetail;
    /**
     * 收货人电话
     */
    @ExaminField(description = "收货人电话")
    private String cellPhone;

}
