package com.sankuai.shangou.seashop.order.core.mq.publisher;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.RefundEventEnum;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 订单售后消息发布者
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderRefundMessagePublisher {

//    @MafkaProducer(namespace = "waimai", topic = MafkaConst.TOPIC_ORDER_REFUND)
//    private IProducerProcessor orderRefundProducer;

    @Resource
    private DefaultRocketMq defaultRocketMq;

    /**
     * 发送售后消息，售后内部使用时，用同一个消费组，消费时通过区分事件类型来处理。其他业务新定义消费组
     * <AUTHOR>
     * @param orderId
	 * @param refundId
     * @param refundEventEnum
     * void
     */
    public void sendOrderRefund(String orderId, Long refundId, RefundEventEnum refundEventEnum) {
        log.info("【mafka生产】【订单售后】发送订单售后消息, orderId: {}, refundId: {}, refundEvent: {}",
                orderId, refundId, refundEventEnum);
        // 发送订单检查消息
        SendResult producerResult = null;
        try {
            OrderRefundMessage message = OrderRefundMessage.builder()
                    .orderId(orderId)
                    .refundId(refundId)
                    .refundEventName(refundEventEnum.name())
                    .build();
//            producerResult = orderRefundProducer.sendMessage(JsonUtil.toJsonString(message));
            producerResult = defaultRocketMq.syncSend(MafkaConst.TOPIC_ORDER_REFUND, JsonUtil.toJsonString(message));
            log.info("【mafka生产】【订单售后】发送订单售后消息结果: {}", JsonUtil.toJsonString(producerResult));
        } catch (Exception e) {
            throw new BusinessException("发送订单售后消息失败");
        }
        if (!SendStatus.SEND_OK.equals(producerResult.getSendStatus())) {
            throw new BusinessException("发送订单售后消息失败");
        }
    }

}
