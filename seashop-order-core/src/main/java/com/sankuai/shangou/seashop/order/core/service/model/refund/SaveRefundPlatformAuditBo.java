package com.sankuai.shangou.seashop.order.core.service.model.refund;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
public class SaveRefundPlatformAuditBo extends BaseParamReq {

    private String orderId;
    private Long refundId;
    private Integer applyNumber;
    private RefundAuditStatusEnum platformAuditStatus;
    private RefundStatusEnum stepStatus;
    private String remark;
    private String userName;

}
