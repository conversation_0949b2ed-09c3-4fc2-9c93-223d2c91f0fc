package com.sankuai.shangou.seashop.order.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.ApplyRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.CancelOrderRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.CreateOrderRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.OrderItemRefundPreviewBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.OrderRefundPreviewBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformApproveBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformRefundDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.QueryErpRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.ReapplyRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundLogBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundLogListBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerApproveParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerConfirmReceiveParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerRefundDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserDeliverParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserDeliveryBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserRefundDetailBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.thrift.core.request.PlatformApproveBatchReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderRefundResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.PlatformApproveBatchResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.UserRefundDetailExtResp;

/**
 * <AUTHOR>
 */
public interface OrderRefundService {

    /**
     * 获取订单退款预览
     *
     * @param orderId 订单id
     * @param userBo  用户信息
     * @return 预览信息。包括订单基本信息、金额，和联系人
     */
    OrderRefundPreviewBo getOrderRefundPreview(String orderId, Long refundId, UserBo userBo);

    /**
     * 获取订单明细退款预览
     *
     * @param orderId     订单id
     * @param userBo      用户信息
     * @param orderItemId 订单明细id
     * @return 预览信息。包括订单基本信息、金额，和联系人
     */
    OrderItemRefundPreviewBo getOrderItemRefundPreview(String orderId, Long orderItemId, Long refundId, UserBo userBo);

    /**
     * 发货前-申请订单退款
     *
     * @param applyBo 申请退款信息
     */
    Long applyOrderRefund(ApplyRefundBo applyBo);

    /**
     * 发货后-申请整单退款/退货退款
     *
     * @param applyBo 申请退款信息
     */
    Long applyWholeOrderRefund(ApplyRefundBo applyBo);

    /**
     * 申请订单明细退款
     *
     * @param applyBo 申请退款信息
     */
    Long applyOrderItemRefund(ApplyRefundBo applyBo);

    /**
     * 取消订单退款
     *
     * @param cancelBo 取消退款信息
     */
    void cancelOrderRefund(CancelOrderRefundBo cancelBo);

    /**
     * 重新申请订单退款
     *
     * @param applyBo 申请退款信息
     */
    void reapplyRefund(ReapplyRefundBo applyBo);

    /**
     * 商家查询售后记录详情，返回的数据有区别，接口拆分
     *
     * @param refundId 退款id
     */
    UserRefundDetailBo userQueryDetail(Long refundId, UserBo user);

    /**
     * 商家查询售后记录详情，微信接口，返回的数据有区别，接口拆分
     *
     * @param refundId 退款id
     * @param user     用户信息
     */
    UserRefundDetailExtResp userQueryDetailExt(Long refundId, UserBo user);

    /**
     * 供应商查询售后记录详情，返回的数据有区别，接口拆分
     *
     * @param refundId 退款id
     */
    SellerRefundDetailBo sellerQueryDetail(Long refundId, ShopBo shop);

    /**
     * 平台查询售后记录详情，返回的数据有区别，接口拆分
     *
     * @param refundId 退款id
     */
    PlatformRefundDetailBo platformQueryDetail(Long refundId);

    /**
     * 根据订单ID列表批量查询
     *
     * @param orderIds 订单ID列表
     * @return 退款详情列表
     */
    List<RefundDetailBo> queryRefundListByOrderIds(List<String> orderIds);

    /**
     * 根据售后单号查询售后详情
     *
     * @param refundId 售后单号
     * @return 售后详情
     */
    PlatformRefundDetailBo queryRefundDetailByRefundId(Long refundId);

    /**
     * 根据三方售后单号查询售后详情
     *
     * @param sourceRefundId 三方售后单号
     * @return 售后详情
     */
    List<PlatformRefundDetailBo> queryRefundDetailBySourceRefundId(String sourceRefundId);
    /**
     * 分页查询erp退款列表
     *
     * @param pageParam 分页参数
     * @param bo        动态查询条件
     * @return 分页数据
     */
    BasePageResp<PlatformRefundDetailBo> queryErpRefundPage(BasePageParam pageParam, QueryErpRefundBo bo);


    /**
     * 查询退款日志
     *
     * @param refundId 退款id
     */
    RefundLogListBo queryRefundLog(Long refundId);

    /**
     * 买家寄货
     *
     * @param paramBo 入参数据
     */
    void userDeliver(UserDeliverParamBo paramBo);

    /**
     * 供应商审核
     *
     * @param paramBo 入参数据
     */
    void sellerApprove(SellerApproveParamBo paramBo);

    /**
     * 供应商确认收货
     *
     * @param paramBO 入参数据
     */
    void sellerConfirmReceive(SellerConfirmReceiveParamBo paramBO);

    /**
     * 平台审核通过
     *
     * @param paramBo 入参数据
     */
    void platformConfirm(PlatformApproveBo paramBo);

    /**
     * 平台驳回
     *
     * @param paramBo 入参数据
     */
    void platformReject(PlatformApproveBo paramBo);

    /**
     * 获取买家寄货的物流信息
     *
     * @param refundId 退款记录ID
     */
    UserDeliveryBo getUserDelivery(Long refundId);

    /**
     * 批量确认
     *
     * @param batchReq 入参
     * @return 批量结果
     */
    PlatformApproveBatchResp platformBatchConfirm(PlatformApproveBatchReq batchReq);

    /**
     * 创建售后单 可以指定状态
     *
     * @param createBo 创建售后单入参
     * @return 售后单信息
     */
    CreateOrderRefundResp createOrderRefund(CreateOrderRefundBo createBo);

    /**
     * 获取订单的有效退款记录,所谓有效，是指商家未取消、供应商未拒绝的记录
     *
     * @param orderId 订单ID
     * @return 有效退款记录
     */
    List<OrderRefund> getValidByOrderId(String orderId);

    /**
     * 获取进行中的售后记录，进行中是指待供应商审核、待平台确认的记录
     *
     * @param orderId 订单ID
     * @return 有效退款记录
     */
    List<OrderRefund> getRefundingByOrderId(String orderId);

}
