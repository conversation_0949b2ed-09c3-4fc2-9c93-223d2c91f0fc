package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class AdaPaymentConfirmCreateResultDto {

    /**
     * 汇付支付流水号
     */
    private String id;

    /**
     * 支付确认手续费金额
     */
    private String feeAmt;

}
