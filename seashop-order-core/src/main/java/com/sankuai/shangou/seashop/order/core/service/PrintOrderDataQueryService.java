package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.order.thrift.core.request.PrintOrderDataReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderDataResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderItemDataResp;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 16:23
 */
public interface PrintOrderDataQueryService {

    List<PrintOrderDataResp> batchQueryOrderByOrderIds(PrintOrderDataReq queryReq);

    List<PrintOrderItemDataResp> batchQueryOrderItemByOrderIds(PrintOrderDataReq queryReq);
}
