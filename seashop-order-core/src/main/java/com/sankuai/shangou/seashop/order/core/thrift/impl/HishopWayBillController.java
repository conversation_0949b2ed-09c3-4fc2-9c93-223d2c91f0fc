package com.sankuai.shangou.seashop.order.core.thrift.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.OrderService;
import com.sankuai.shangou.seashop.order.core.service.model.order.CreateOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.DeliverOrderParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderInfoBo;
import com.sankuai.shangou.seashop.order.thrift.core.HishopWayBillFegin;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.OrderGetSoldTradesReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description：电子面单接口实现
 * @author： chenpeng
 * @create： 2023/11/21 19:14
 */
@RestController
@RequestMapping("/wayBill")
public class HishopWayBillController implements HishopWayBillFegin {

    @Resource
    private OrderService orderService;

    @PostMapping(value = "/getSoldTrades")
    @Override
    public ResultDto<BasePageResp<OrderInfoDto>> getSoldTrades(@RequestBody OrderGetSoldTradesReq param) throws TException {
        return ThriftResponseHelper.responseInvoke("查询订单列表", param, func -> {
            BasePageResp<OrderInfoDto> resp = orderService.getSoldTrades(param);
            return JsonUtil.copy(resp, new TypeReference<BasePageResp<OrderInfoDto>>() {
            });
        });
    }

    @PostMapping(value = "/getIncrementSoldTrades")
    @Override
    public ResultDto<BasePageResp<OrderInfoDto>> getIncrementSoldTrades(@RequestBody OrderGetSoldTradesReq param) throws TException {
        return ThriftResponseHelper.responseInvoke("查询增量订单列表", param, func -> {
            BasePageResp<OrderInfoDto> resp = orderService.getIncrementSoldTrades(param);
            return JsonUtil.copy(resp, new TypeReference<BasePageResp<OrderInfoDto>>() {
            });
        });
    }

    @GetMapping(value = "/getTradeByOrderId")
    @Override
    public ResultDto<OrderInfoDto> getTradeByOrderId(@RequestParam String orderId) throws TException {
        return ThriftResponseHelper.responseInvoke("查询订单", orderId, func -> {
            OrderInfoDto resp = orderService.getTradeByOrderId(orderId);
            return resp;
        });
    }

    @Override
    public ResultDto<Void> deliverOrder(OrderGetSoldTradesReq param) throws TException {
        return ThriftResponseHelper.responseInvoke("订单发货", param, func -> {
            DeliverOrderParamBo deliverOrderParamBo = JsonUtil.copy(param, DeliverOrderParamBo.class);
            orderService.deliverOrder(deliverOrderParamBo);
            return null;
        });
    }
}
