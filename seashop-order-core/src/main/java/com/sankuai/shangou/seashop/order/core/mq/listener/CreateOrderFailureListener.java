package com.sankuai.shangou.seashop.order.core.mq.listener;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderCreateCompensationAssist;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderCheckBo;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 创建订单时，如果执行失败，尝试回滚关联数据
 * <p>如果执行失败，说明订单数据是没有保存的</p>
 * <p>引入美团BCP机制，确保消息发送成功，小概率事件不额外引入其他方式保证数据一致</p>>
 * <AUTHOR>
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_ORDER_FAILURE,
//        group = MafkaConst.GROUP_ORDER_FAILURE)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_FAILURE + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_FAILURE + "_${spring.profiles.active}")
public class CreateOrderFailureListener implements RocketMQListener<MessageExt> {

    @Resource
    private OrderCreateCompensationAssist orderCreateCompensationAssist;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext context) {
//        String body = (String) mafkaMessage.getBody();
//        log.info("【mafka消费】【订单创建失败】消息内容为: {}", body);
//        if (body == null) {
//            log.error("【mafka消费】【订单创建失败】消息内容为空");
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//        OrderCheckBo messageWrapper = JsonUtil.parseObject(body, OrderCheckBo.class);
//        // TODO BCP接入，验证消息是否发送成功
//        orderCreateCompensationAssist.checkAndRollback(messageWrapper.getUserId(), messageWrapper.getOrderIdList());
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(MessageExt message) {
        log.info("【mafka消费】【订单创建失败】消息内容为: {}", message);
//        String body = (String) mafkaMessage.getBody();
        if (message.getBody() == null) {
            log.error("【mafka消费】【订单创建失败】消息内容为空");
            return;
        }
        try {
            OrderCheckBo messageWrapper = JsonUtil.parseObject(message.getBody(), OrderCheckBo.class);
            // TODO BCP接入，验证消息是否发送成功
            orderCreateCompensationAssist.checkAndRollback(messageWrapper.getUserId(), messageWrapper.getOrderIdList());
        } catch (Exception e) {
            log.error("【mafka消费】【订单创建失败】处理失败", e);
            throw new RuntimeException(e);
        }
    }

}
