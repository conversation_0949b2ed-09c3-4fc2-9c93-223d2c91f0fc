package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.sankuai.shangou.seashop.order.core.service.PrintOrderDataQueryService;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.request.PrintOrderDataReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderDataResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderItemDataResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 16:23
 */
@Service
public class PrintOrderDataQueryServiceImpl implements PrintOrderDataQueryService {

    @Resource
    private OrderRepository orderRepository;

    @Resource
    private OrderItemRepository orderItemRepository;

    @Override
    public List<PrintOrderDataResp> batchQueryOrderByOrderIds(PrintOrderDataReq queryReq) {
        List<PrintOrderDataResp> result = new ArrayList<>();
        List<Order> orderList = orderRepository.getByOrderIdList(queryReq.getOrderIdList());
        if(CollectionUtil.isEmpty(orderList)){
            return result;
        }
        for(Order order : orderList){
            PrintOrderDataResp resp = new PrintOrderDataResp();
            resp.setShopName(order.getShopName());
            resp.setOrderId(order.getOrderId());
            resp.setOrderDate(order.getOrderDate());
            resp.setShipTo(order.getShipTo());
            resp.setCellPhone(order.getCellPhone());
            resp.setAddress(order.getRegionFullName()+" "+order.getAddress());
            resp.setProductTotalAmount(order.getProductTotalAmount());
            resp.setFreight(order.getFreight());
            resp.setTotalAmount(order.getTotalAmount());
            resp.setOrderRemarks(order.getOrderRemarks());
            result.add(resp);
        }
        return result;
    }

    @Override
    public List<PrintOrderItemDataResp> batchQueryOrderItemByOrderIds(PrintOrderDataReq queryReq) {
        List<PrintOrderItemDataResp> result = new ArrayList<>();
        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(queryReq.getOrderIdList());
        if(CollectionUtil.isEmpty(orderItemList)){
            return result;
        }
        for(int i=0;i<orderItemList.size();i++){
            OrderItem orderItem = orderItemList.get(i);
            PrintOrderItemDataResp resp = new PrintOrderItemDataResp();
            resp.setNum(i);
            resp.setOrderId(orderItem.getOrderId());
            resp.setProductName(orderItem.getProductName());
            resp.setSpecName(orderItem.getColor()+orderItem.getSize()+orderItem.getVersion());
            resp.setQuantity(orderItem.getQuantity());
            resp.setSalePrice(orderItem.getSalePrice());
            resp.setRealTotalPrice(orderItem.getRealTotalPrice());
            result.add(resp);
        }
        return result;
    }
}
