package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RefundStepBo {

    /**
     * 退款步骤
     */
    private Integer stepCode;
    /**
     * 退款步骤描述
     */
    private String stepDesc;
    /**
     * 是否需要高亮。如果为true，当前颜色显示橘黄色，否则显示灰色
     */
    private Boolean highlight = false;
    /**
     * 是否是当前步骤，橘黄色高亮
     */
    private Boolean currentStep = false;

    public RefundStepBo(Integer stepCode, String stepDesc) {
        this.stepCode = stepCode;
        this.stepDesc = stepDesc;
    }

    public RefundStepBo(Integer stepCode, String stepDesc, Boolean highlight) {
        this.stepCode = stepCode;
        this.stepDesc = stepDesc;
        this.highlight = highlight;
    }
    public RefundStepBo(Integer stepCode, String stepDesc, Boolean highlight, Boolean currentStep) {
        this.stepCode = stepCode;
        this.stepDesc = stepDesc;
        this.highlight = highlight;
        this.currentStep = currentStep;
    }
}
