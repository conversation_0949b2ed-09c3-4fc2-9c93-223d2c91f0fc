package com.sankuai.shangou.seashop.pay.core.mq.publisher;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.pay.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseResultDto;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description: 退款订单状态（未分账支付退款）通知
 */
@Slf4j
@Service
public class OrderReverseProducer {

    /**
     * topic：seashop_order_reverse_topic
     * <p>
     * group：seashop_order_reverse_consumer
     * <p>
     * 发送端： com.sankuai.sgb2b.seashop.pay
     * <p>
     * 消费端：com.sankuai.sgb2b.seashop.trade
     */

//    @MafkaProducer(namespace = MafkaConstant.DEFAULT_NAMESPACE, topic = MafkaConstant.ORDER_REVERSE_TOPIC)
//    private IProducerProcessor producerProcessor;

    @Resource
    private DefaultRocketMq defaultRocketMq;

    public void sendMessage(AdaPayReverseResultDto adaPayReverseResultDto) {
        log.info("订单退款状态<未分账支付退款>变更生产者发送消息-adaPayReverseResultDto：{}", JSONUtil.toJsonStr(adaPayReverseResultDto));
        try {
//            ProducerResult producerResult = producerProcessor.sendMessage(JSONUtil.toJsonStr(adaPayReverseResultDto));
            defaultRocketMq.convertAndSend(MafkaConstant.ORDER_REVERSE_TOPIC, adaPayReverseResultDto);
            log.info("订单退款状态<未分账支付退款>变更生产者发送消息成功");
        } catch (Exception e) {
            log.error("订单退款状态<未分账支付退款>变更生产者发送消息失败-adaPayReverseResultDto：{}", JSONUtil.toJsonStr(adaPayReverseResultDto), e);
            throw new SystemException("订单退款状态<未分账支付退款>变更生产者发送消息失败");
        }
    }
}
