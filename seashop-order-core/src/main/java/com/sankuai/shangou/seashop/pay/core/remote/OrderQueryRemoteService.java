package com.sankuai.shangou.seashop.pay.core.remote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import com.sankuai.shangou.seashop.order.core.service.OrderPayRecordService;
import com.sankuai.shangou.seashop.order.core.service.OrderService;
import com.sankuai.shangou.seashop.order.core.service.OrderStatsService;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryPlatformOrderBo;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositPay;
import com.sankuai.shangou.seashop.order.dao.finance.repository.CashDepositPayRepository;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPlatformOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayRecordResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositPayReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositPayResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OrderQueryRemoteService {
    @Resource
    private OrderService orderService;
    @Resource
    private EsOrderService esOrderService;
    @Resource
    private OrderStatsService orderStatsService;

    @Resource
    private OrderPayRecordService orderPayRecordService;

    @Resource
    private CashDepositPayRepository cashDepositPayRepository;
    public BasePageResp<OrderInfoDto> pageQueryPlatformOrder(QueryPlatformOrderReq queryReq) {
        // 业务逻辑处理
        QueryPlatformOrderBo queryUserOrderBo = JsonUtil.copy(queryReq, QueryPlatformOrderBo.class);
        BasePageResp<OrderInfoBo> resp = esOrderService.searchForPlatform(queryUserOrderBo);
        return JsonUtil.copy(resp, new TypeReference<BasePageResp<OrderInfoDto>>() {
        });
    }

    public List<CashDepositPayResp> selectCashDepositPay(CashDepositPayReq request) {
        List<CashDepositPay> payList = cashDepositPayRepository.queryByPayIdList(request.getPayIdList());
        return JsonUtil.copyList(payList, CashDepositPayResp.class);
    }

    public List<OrderPayRecordResp> queryOrderPayRecordList(List<String> batchNoList) {
        return orderPayRecordService.queryOrderPayRecordList(batchNoList);
    }

    public List<OrderPayRecordResp> queryOrderPayRecordByOrderIds(List<String> orderIdList) {
        return orderPayRecordService.queryOrderPayRecordByOrderIds(orderIdList);
    }
}
