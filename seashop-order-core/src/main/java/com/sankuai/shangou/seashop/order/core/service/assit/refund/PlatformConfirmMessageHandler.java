package com.sankuai.shangou.seashop.order.core.service.assit.refund;

import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.enums.AppletMessageEventEnum;
import com.sankuai.shangou.seashop.order.common.remote.MessageRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.PayRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.message.RefundApprovedAppletMessage;
import com.sankuai.shangou.seashop.order.common.remote.model.pay.CreateRefundBo;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.core.service.assit.BizNoGenerator;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.RefundEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayStatusEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessStatusTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PlatformConfirmMessageHandler implements RefundMessageHandler {

    @Resource
    private PayRemoteService payRemoteService;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private BizNoGenerator bizNoGenerator;
    @Resource
    private MessageRemoteService messageRemoteService;
    @Resource
    private RefundResultNotifyAssist refundResultNotifyAssist;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public RefundEventEnum getEvent() {
        return RefundEventEnum.PLATFORM_CONFIRM;
    }

    /**
     * 逻辑能走到这，退款记录已经修改成退款中状态了，这里是继续异步调用支付接口实际退款，不做额外校验
     * <AUTHOR>
     * @param message
     * void
     */
    @Override
    public void handle(OrderRefundMessage message) {
        log.info("【售后】平台确认退款，退款记录: {}", JsonUtil.toJsonString(message));
        // 这里不需要走主库，后续用到的数据都是供应商审核就能确定的
        OrderRefund refund = orderRefundRepository.getByIdForceMaster(message.getRefundId());
        log.info("【售后】平台确认退款，退款记录: {}", JsonUtil.toJsonString(refund));
        if (refund == null) {
            log.error("【售后】平台确认退款，退款记录不存在, refundId: {}", message.getRefundId());
            return;
        }
        // 如果退款已经处理过了，不再处理
        if (!RefundPayStatusEnum.UN_PAY.getCode().equals(refund.getRefundPayStatus())) {
            log.info("【售后】平台确认退款，退款已经处理过了, refundId: {}", message.getRefundId());
            return;
        }

        // 修改退款支付相关的数据
        // 查看.net代码以及比对汇付单号，这边生成的batchNo，发起退款后，需要重置
        String batchNo = bizNoGenerator.generateRefundBatchNo();
        log.info("【售后】平台确认退款 生成退款批次号: {}", batchNo);
        Order order = orderRepository.getByOrderId(refund.getOrderId());
        // 能退款需要获取的是支付成功的，一笔订单支付成功的，正常只会有一笔
        if (refund.getAmount().compareTo(BigDecimal.ZERO) > 0) {
            // 获取支付记录，用于发起退款，这里要用订单上的外部支付流水号一直，否则可能与异常订单冲突
            OrderPayRecord orderPayRecord = orderPayRecordRepository.getByOrderIdAndOutTransIdAndStatus(message.getOrderId(), order.getGatewayOrderId(), PayStatusEnum.PAY_SUCCESS.getCode());
            log.info("【售后】平台确认退款，查询订单支付记录: {}", JsonUtil.toJsonString(orderPayRecord));
            if (orderPayRecord == null) {
                log.error("【售后】平台确认退款，订单支付记录不存在, orderId: {}", message.getOrderId());
                // 理论上一定要存在记录的，没有抛出异常，后续手动处理
                throw new BusinessException("订单支付记录不存在");
            }
            // 调用支付接口发起退款
            CreateRefundBo createRefundBo = new CreateRefundBo();
            createRefundBo.setRefundNo(batchNo);
            createRefundBo.setOriginPayNo(orderPayRecord.getBatchNo());
            createRefundBo.setRefundAmount(refund.getAmount());
            createRefundBo.setBusinessStatusType(BusinessStatusTypeEnum.NORMAL.getType());
            // 如果发起了退款，则重置批次号，保存第三方支付渠道的单号
            batchNo = payRemoteService.createRefund(createRefundBo);
            log.info("【售后】平台确认退款，调用支付接口发起退款，退款批次号: {}", batchNo);
            // 需要发起售后退款的，才需要改状态
            orderRefundRepository.saveRefundPayStatus(message.getRefundId(), batchNo, RefundPayStatusEnum.UN_PAY.getCode());
        }
        // 不需要退款的，直接退款成功
        else if (RefundAuditStatusEnum.REFUND_SUCCESS.getCode().equals(refund.getManagerConfirmStatus())) {
            refundResultNotifyAssist.handleOrderRefundSuccess(order, refund);
        }

        try {
            RefundApprovedAppletMessage appletMessage = new RefundApprovedAppletMessage();
            String subReason = StringUtils.abbreviate(refund.getReason(), CommonConst.WECHAT_MESSAGE_LENGTH);
            appletMessage.setThing6(subReason);
            appletMessage.setDate4(DateUtil.format(refund.getApplyDate(), "yyyy年MM月dd日 HH:mm"));
            appletMessage.setAmount5(refund.getAmount().toPlainString() + "元");
            messageRemoteService.sendAppletMessage(refund.getUserId(),
                    AppletMessageEventEnum.MEMBER_REFUND_APPROVED, appletMessage);
        } catch (Exception e) {
            log.error("【售后】平台确认退款，发送小程序消息异常, refundId: {}", message.getRefundId(), e);
        }
    }
}
