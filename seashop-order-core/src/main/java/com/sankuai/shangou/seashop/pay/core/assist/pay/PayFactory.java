package com.sankuai.shangou.seashop.pay.core.assist.pay;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;

import cn.hutool.extra.spring.SpringUtil;
import lombok.NonNull;

/**
 * <AUTHOR>
 * @date 2024/09/02 9:34
 */
@Component
public class PayFactory {

    public AbstractPayHandler getPayHandler(@NonNull PaymentChannelEnum channel) {
        return SpringUtil.getBeansOfType(AbstractPayHandler.class)
                .values().stream()
                .filter(payHandler -> channel.equals(payHandler.paymentChannel()))
                .findFirst()
                .orElseThrow(() -> new BusinessException("支付渠道不存在"));
    }

}
