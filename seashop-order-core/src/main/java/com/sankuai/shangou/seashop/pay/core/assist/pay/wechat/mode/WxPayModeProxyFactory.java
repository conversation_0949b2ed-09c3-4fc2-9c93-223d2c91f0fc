package com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.mode;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.WxPayNormalHandler;
import com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.WxPayServiceHandler;
import com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.WxPayUtil;
import com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.config.CustomWxPayConfig;
import com.sankuai.shangou.seashop.pay.dao.core.model.WxPayConfigModel;

/**
 * <AUTHOR>
 * @date 2024/09/02 14:10
 */
@Component
public class WxPayModeProxyFactory {

    @Resource
    private WxPayUtil wxPayUtil;

    public AbstractWxPayModeProxy buildWxPayModeProxy(WxPayConfigModel payConfig) {
        AbstractWxPayModeProxy modeProxy;
        if (payConfig.getServiceMode()) {
            modeProxy = buildWxPayServiceModeProxy(payConfig);
        } else {
            modeProxy = buildWxPayNormalModeProxy(payConfig);
        }

        modeProxy.setWxPayUtil(wxPayUtil);
        modeProxy.setPayConfig(payConfig);
        return modeProxy;
    }

    private AbstractWxPayModeProxy buildWxPayNormalModeProxy(WxPayConfigModel payConfig) {
        WxPayNormalHandler modeProxy = new WxPayNormalHandler();

        WxPayServiceImpl wxPayService = new WxPayServiceImpl();
        CustomWxPayConfig config = new CustomWxPayConfig();
        config.setMchId(payConfig.getMchId());
        config.setApiV3Key(payConfig.getApiV3Key());
        config.setKeyContent(payConfig.getP12KeyContent());
        wxPayService.setConfig(config);
        modeProxy.setWxPayService(wxPayService);
        return modeProxy;
    }

    private AbstractWxPayModeProxy buildWxPayServiceModeProxy(WxPayConfigModel payConfig) {
        WxPayServiceHandler modeProxy = new WxPayServiceHandler();

        WxPayServiceImpl wxPayService = new WxPayServiceImpl();
        CustomWxPayConfig config = new CustomWxPayConfig();
        config.setAppId(payConfig.getServiceAppId());
        // config.setSubAppId();
        config.setMchId(payConfig.getServiceMchId());
        config.setSubMchId(payConfig.getMchId());
        config.setApiV3Key(payConfig.getApiV3Key());
        config.setKeyContent(payConfig.getServiceP12KeyContent());
        wxPayService.setConfig(config);
        modeProxy.setWxPayService(wxPayService);
        return modeProxy;
    }
}
