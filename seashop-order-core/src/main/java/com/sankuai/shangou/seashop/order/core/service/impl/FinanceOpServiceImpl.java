package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.enums.FinanceEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.core.service.FinanceOpService;
import com.sankuai.shangou.seashop.order.dao.core.domain.*;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Finance;
import com.sankuai.shangou.seashop.order.dao.finance.domain.FinanceItem;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceItemRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceRepository;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/10/010
 * @description:
 */
@Service
@Slf4j
public class FinanceOpServiceImpl implements FinanceOpService {

    @Resource
    private FinanceRepository financeRepository;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private FinanceItemRepository financeItemRepository;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public void writeFinanceOrderRefund(Order order, OrderPayRecord orderPayRecord, OrderRefund orderRefund,
                                        BigDecimal refundMoney, BigDecimal serviceAmount, BigDecimal settlementAmount) {
        log.info("【写入财务】订单退款，订单号: {}, 退款金额: {}", order.getOrderId(), refundMoney);
        Finance oldFinance = financeRepository.getByRefundId(orderRefund.getId(), TransactionTypesEnum.REFUND.getCode());
        if (null != oldFinance) {
            log.info("【写入财务】订单退款，订单号: {}, 退款金额: {}, 财务已存在，不再写入", order.getOrderId(), refundMoney);
            return;
        }
        ShopDetailResp shopDetailResp = shopRemoteService.queryDetail(order.getShopId());

        List<OrderItem> orderItems = orderItemRepository.getByOrderId(order.getOrderId());

        Finance finance = new Finance();
        finance.setOrderId(order.getOrderId());
        finance.setAdapayId(null != orderPayRecord ? orderPayRecord.getPayNo() : "");
        finance.setPayId(null != orderPayRecord ? orderPayRecord.getBatchNo() : "");
        finance.setType(TransactionTypesEnum.REFUND.getCode());
        finance.setCreateDate(orderRefund.getManagerConfirmDate());
        finance.setShopId(order.getShopId());
        finance.setShopName(shopDetailResp.getShopName());
        finance.setUserId(order.getUserId());
        finance.setUserName(order.getUserName());
        finance.setTransactionId(order.getGatewayOrderId());
        finance.setTotalAmount(refundMoney.compareTo(BigDecimal.ZERO) > 0 ? refundMoney : order.getRefundTotalAmount());
        finance.setRefundType(order.getFinishDate() != null ? FinanceEnum.RefundTypes.AFTER_FINISH.getCode() : FinanceEnum.RefundTypes.BEFORE_FINISH.getCode());
        finance.setFreight(order.getFreight());
        finance.setProductAmount(order.getProductTotalAmount());
        finance.setDiscountAmount(order.getCouponAmount());
        finance.setPlatDiscountAmount(BigDecimal.ZERO);
        finance.setFullDiscount(order.getDiscountAmount());
        finance.setMoneyOff(order.getMoneyOffAmount());
        finance.setIntegralDiscount(BigDecimal.ZERO);

        finance.setServiceAmount(serviceAmount);
        finance.setServiceAmount(settlementAmount);

        finance.setCommissionAmount(orderRefund.getReturnPlatCommission());
        finance.setOrderRefundId(orderRefund.getId());
        finance.setActualPayAmount(orderPayRecord.getPayAmount());

        financeRepository.save(finance);

        if (CollUtil.isNotEmpty(orderItems)) {
            List<FinanceItem> financeItems = CollUtil.newArrayList();
            for (OrderItem orderItem : orderItems) {
                FinanceItem financeItem = new FinanceItem();
                financeItem.setFinanceId(finance.getId());
                financeItem.setOrderId(order.getOrderId());
                financeItem.setProductId(orderItem.getProductId());
                financeItem.setProductName(orderItem.getProductName());
                financeItem.setSku(orderItem.getSku());
                financeItem.setQuantity(orderItem.getReturnQuantity());
                financeItem.setOriginalPrice(orderItem.getCostPrice());
                financeItem.setSalePrice(orderItem.getSalePrice());
                financeItem.setTotalAmount(orderItem.getRefundPrice());
                financeItem.setDiscountPrice(orderItem.getDiscountAmount());
                financeItem.setFullDiscount(orderItem.getFullDiscount());
                financeItem.setMoneyOff(orderItem.getMoneyOff());
                financeItem.setCouponDiscount(orderItem.getCouponDiscount());
                financeItem.setCommisRate(orderItem.getCommisRate());
                financeItems.add(financeItem);
            }
            financeItemRepository.saveBatch(financeItems);
        }
    }

    @Override
    public void writeFinanceExceptionOrderRefund(ExceptionOrder exceptionOrder) {
        log.info("【写入财务】异常订单退款，exceptionOrder: {}", exceptionOrder);
        Finance oldFinance = financeRepository.getByRefundId(exceptionOrder.getId(), TransactionTypesEnum.ERROR_ORDER_REFUND.getCode());
        if (null != oldFinance) {
            log.info("【写入财务】异常订单退款，exceptionOrder: {}, 财务已存在，不再写入", exceptionOrder);
            return;
        }

        Order order = orderRepository.getByOrderId(exceptionOrder.getOrderId());

        Finance finance = new Finance();
        finance.setOrderId(exceptionOrder.getOrderId());
        finance.setPayId(exceptionOrder.getRefundBatchNo());
        finance.setType(TransactionTypesEnum.ERROR_ORDER_REFUND.getCode());
        finance.setCreateDate(new Date());
        finance.setShopId(order.getShopId());
        finance.setShopName(order.getShopName());
        finance.setUserId(CommonConst.USER_ID_DEFAULT_ZERO);
        finance.setUserName("异常订单退款");
        finance.setTransactionId("");
        finance.setTotalAmount(exceptionOrder.getPayAmount());
        finance.setOrderRefundId(exceptionOrder.getId());
        finance.setActualPayAmount(exceptionOrder.getPayAmount());

        financeRepository.save(finance);
    }
}
