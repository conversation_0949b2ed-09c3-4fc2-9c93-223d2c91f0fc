package com.sankuai.shangou.seashop.order.core.service.assit;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sankuai.shangou.seashop.base.boot.dto.ChangeFiled;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.core.service.assit.opLog.ChangeFieldDesc;
import com.sankuai.shangou.seashop.order.core.service.assit.opLog.OperationLogAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.RefundStatusHelper;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.ApplyRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.CreateOrderRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.CreateOrderRefundItemBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.ItemRemainRefundInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SaveRefundPlatformAuditBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerApproveParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserDeliverParamBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefundLog;
import com.sankuai.shangou.seashop.order.dao.core.po.UserDeliverRefundItemParamPo;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderOperationLogRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundLogRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundLogStepEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundTypeEnum;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderRefundBizAssist {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRefundLogRepository orderRefundLogRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private BizNoGenerator bizNoGenerator;
    @Resource
    private OrderOperationLogRepository orderOperationLogRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OperationLogAssist operationLogAssist;

    @Transactional(rollbackFor = Exception.class)
    public Long saveOrderRefundData(ApplyRefundBo applyBo, Order order) {
        // 构建并退款对象
        OrderRefund orderRefund = buildOrderRefund(applyBo, order);
        Long refundId = bizNoGenerator.generateRefundId();
        orderRefund.setId(refundId);
        //指定状态
        RefundStatusEnum status = applyBo.getStatus();
        orderRefund.setSellerAuditStatus(RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode());
        orderRefund.setStatus(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode());

        if (status != null) {
            //指定了状态
            orderRefund.setSellerAuditStatus(status.getCode());
            if (status.getCode() >= RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode()) {
                //买家审核，平台固定
                orderRefund.setSellerAuditStatus(RefundAuditStatusEnum.SUPPLIER_PASS.getCode());
                orderRefund.setManagerConfirmStatus(status.getCode());
            }
            orderRefund.setStatus(status.getCode());
            //状态大于 WAIT_BUYER_SEND  商品退货
            if (RefundModeEnum.GOODS_REFUND.equals(applyBo.getRefundMode()) && status.getCode() >= RefundStatusEnum.WAIT_BUYER_SEND.getCode()) {
                orderRefund.setExpressCompanyCode(applyBo.getExpressCompanyCode());
                orderRefund.setExpressCompanyName(applyBo.getExpressCompanyName());
                orderRefund.setShipOrderNumber(applyBo.getShipOrderNumber());
            }
        }

        orderRefundRepository.save(orderRefund);
        // 如果是明细退，订单明细同样保存申请退款数量
        if (orderRefund.getOrderItemId() > 0) {
            int cnt = orderItemRepository.increaseApplyRefundQuantity(orderRefund.getOrderItemId(), applyBo.getApplyQuantity());
            if (cnt <= 0) {
                throw new BusinessException("申请售后数量不能超过商品购买数量");
            }
        }
        Integer refundStatus = orderRefund.getStatus();
        // 保存退款日志
        saveRefundLog(orderRefund.getId(), orderRefund.getApplyNumber(), RefundLogStepEnum.WAIT_SUPPLIER_AUDIT,
                RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT, applyBo.getUser().getUserName(), applyBo.getRefundReasonDesc(), orderRefund.getReasonDetail());
        return orderRefund.getId();
    }


    @NotNull
    private static String getLogContent(SellerApproveParamBo auditPo, OrderRefund orderRefund) {
        String desc = RefundAuditStatusEnum.SUPPLIER_REFUSE.equals(auditPo.getAuditStatus()) ? CommonConst.DESC_REFUND_AUDIT_REJECT : CommonConst.DESC_REFUND_AUDIT_PASS;
        String logContent = CommonConst.DESC_REFUND_LOG_SELLER_AUDIT_PREFIX + desc;
        // 整单退可能会退运费，处理日志内容
        if (orderRefund.getHasAllReturn()) {
            if (Boolean.TRUE.equals(auditPo.getWhetherRefundFreight())) {
                logContent += CommonConst.DESC_REFUND_FREIGHT;
            }
            else {
                logContent += CommonConst.DESC_REFUND_NOT_FREIGHT;
            }
        }
        return logContent;
    }

    /**
     * 整单退
     *
     * @param createBo 售后单创建对象
     * @param order    订单
     * @return 退款对象
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderRefund saveOrderRefund(CreateOrderRefundBo createBo, Order order, UserBo userBo) {

        // 构建并退款对象
        DateTime date = DateUtil.date();
        OrderRefund refund = new OrderRefund();
        Long refundId = bizNoGenerator.generateRefundId();
        refund.setId(refundId);
        refund.setOrderId(createBo.getOrderId());
        refund.setSourceRefundId(createBo.getSourceRefundId());
        refund.setOrderItemId(0L);
        refund.setAmount(order.getTotalAmount());
        //整单退 判断是否退运费  发货后，传入的退运费
        BigDecimal refundFreight = (createBo.getRefundFreight() == null ? BigDecimal.ZERO : createBo.getRefundFreight());
        refund.setReturnFreight(refundFreight);
        refund.setHasAllReturn(true);
        refund.setReturnQuantity(0L);
        refund.setRefundMode(createBo.getRefundMode().getCode());

        refund.setReason(createBo.getRefundReason());
        refund.setReasonDetail(createBo.getRemark());

        refund.setHasCancel(false);

        refund.setReturnPlatCommission(order.getCommissionTotalAmount());
        refund.setApplyNumber(1);
        refund.setApplyDate(date);
        refund.setCreateTime(date);
        refund.setUpdateTime(date);
        refund.setSellerAuditDate(date);
        refund.setManagerConfirmDate(date);
        refund.setLastModifyTime(date);

        refund.setRefundPayStatus(RefundPayStatusEnum.UN_PAY.getCode());
        refund.setRefundPayType(RefundPayTypeEnum.ORIGINAL.getCode());

        refund.setShopId(order.getShopId());
        refund.setShopName(order.getShopName());
        refund.setUserId(order.getUserId());
        if (userBo != null) {
            refund.setApplicant(userBo.getUserName());
        }
        refund.setContactPerson(order.getUserName());
        refund.setContactCellPhone(order.getCellPhone());
        refund.setManagerConfirmStatus(RefundAuditStatusEnum.WAIT_PLATFORM_CONFIRM.getCode());
        RefundStatusEnum status = createBo.getStatus();
        if (status != null) {
            //指定了状态
            refund.setSellerAuditStatus(status.getCode());
            if (status.getCode() >= RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode()) {
                //买家审核，平台固定
                refund.setSellerAuditStatus(RefundAuditStatusEnum.SUPPLIER_PASS.getCode());
                refund.setManagerConfirmStatus(status.getCode());
            }
            refund.setStatus(status.getCode());
        }
        else {
            refund.setSellerAuditStatus(RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode());
            refund.setStatus(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode());
        }
        refund.setHasReturn(RefundModeEnum.RETURN_AND_REFUND.equals(createBo.getRefundMode()));
        orderRefundRepository.save(refund);

        // 保存退款日志
        saveRefundLog(refund.getId(), refund.getApplyNumber(),
                RefundLogStepEnum.WAIT_SUPPLIER_AUDIT,
                RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT, userBo != null ? userBo.getUserName() :
                        StrUtil.EMPTY,
                refund.getReason(), null);
        return refund;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveCancelOrderRefundData(OrderRefund orderRefund, UserBo user) {
        Date now = new Date();
        // 修改退款的取消状态
        boolean flag = orderRefundRepository.cancelOrderRefund(orderRefund.getId(), now, RefundStatusEnum.BUYER_CANCEL.getCode());
        if (flag) {
            // 修改订单的最后修改时间
            orderRepository.updateLastModifyTime(orderRefund.getOrderId(), now);
            // 如果是明细退，订单明细同样减去申请退款数量，兼容.net之前数据，需要额外加上判断
            if (orderRefund.getOrderItemId() > 0 && Boolean.FALSE.equals(orderRefund.getHasAllReturn())) {
                int cnt = orderItemRepository.decreaseApplyRefundQuantity(orderRefund.getOrderItemId(), orderRefund.getApplyQuantity());
                log.info("取消退款，修改订单明细申请退款数量，orderItemId:{}, returnQuantity:{}, cnt:{}", orderRefund.getOrderItemId(), orderRefund.getApplyQuantity(), cnt);
            }
            // 保存退款日志
            RefundAuditStatusEnum auditStatus = RefundAuditStatusEnum.valueOf(orderRefund.getSellerAuditStatus(), orderRefund.getManagerConfirmStatus());
            saveRefundLog(orderRefund.getId(), orderRefund.getApplyNumber(), RefundLogStepEnum.BUYER_CANCEL,
                    auditStatus, user.getUserName(), CommonConst.DESC_REFUND_LOG_CANCEL_ORDER_REFUND, null);
        }
    }

    /**
     * 保存重新申请的数据
     * <p>重新申请，会新增一条售后记录，之前的售后记录设置为逻辑删除，且售后日志关联的ID重置</p>
     *
     * @param originRefundData
     * @param applyBo
     * @param order            java.lang.Long
     * <AUTHOR>
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveReRefundData(OrderRefund originRefundData, ApplyRefundBo applyBo, Order order) {
        // 构建并退款对象，sql只会改部分字段，但还是复用之前的构建方法
        OrderRefund newRefund = buildOrderRefund(applyBo, order);
        // 重新申请时，申请次数在原有基础上累加
        newRefund.setApplyNumber(originRefundData.getApplyNumber() + 1);
        newRefund.setSellerRemark("");
        newRefund.setManagerRemark("");
        newRefund.setHasCancel(false);
        newRefund.setReapplyOriginId(originRefundData.getId());
        newRefund.setOrderItemId(originRefundData.getOrderItemId());

        orderRefundRepository.removeById(originRefundData.getId());
        boolean updated = orderRefundRepository.save(newRefund);
        if (!updated) {
            throw new BusinessException("更新退款数据失败");
        }
        // 退款日志的ID要重新关联
        orderRefundLogRepository.updateRefundId(originRefundData.getId(), newRefund.getId());
        // 保存退款日志
        saveRefundLog(newRefund.getId(), newRefund.getApplyNumber(), RefundLogStepEnum.WAIT_SUPPLIER_AUDIT,
                RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT, applyBo.getUser().getUserName(), applyBo.getRefundReasonDesc(), null);
        return newRefund.getId();
    }


    public Integer getRefundStatus(OrderRefund orderRefund) {
        if (orderRefund.getHasCancel()) {
            return RefundStatusEnum.BUYER_CANCEL.getCode();
        }
        Integer sellerAuditStatus = orderRefund.getSellerAuditStatus();
        Integer platformAuditStatus = orderRefund.getManagerConfirmStatus();

        RefundAuditStatusEnum auditStatus = RefundAuditStatusEnum.valueOf(sellerAuditStatus, platformAuditStatus);

        return auditStatus.getCode();
    }

    public RefundTypeEnum getRefundType(OrderRefund orderRefund) {
        RefundTypeEnum refundType;
        // 发货前订单退款，只能是仅退款；发货后，不同类型，mode不一样
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode())) {
            refundType = RefundTypeEnum.ONLY_REFUND;
        }
        else if (RefundModeEnum.GOODS_REFUND.getCode().equals(orderRefund.getRefundMode())) {
            refundType = RefundTypeEnum.ONLY_REFUND;
        }
        else {
            refundType = RefundTypeEnum.RETURN_AND_REFUND;
        }
        return refundType;
    }

    /**
     * 是否可以重新申请
     * 1. 当前条售后的状态允许，即是被取消或者被审核拒绝时可以重新申请
     * 2. 订单状态允许
     * 3. 其他售后单允许，比如是否有其他整单退
     *
     * @param order          发起售后的订单
     * @param orderRefund    当前需要重新发起售后的售后单
     * @param orderValidList 发起售后的订单所有有效的售后单
     *                       boolean
     * <AUTHOR>
     */
    public boolean canReapply(Order order, OrderRefund orderRefund, List<OrderRefund> orderValidList, boolean hasOverAfterSale) {
        if (hasOverAfterSale) {
            return false;
        }
        // 商家取消，或者供应商拒绝，或者平台拒绝，都可以重新申请
        boolean statusCanReapply = RefundStatusHelper.refundInvalid(orderRefund.getHasCancel(), orderRefund.getSellerAuditStatus(), orderRefund.getManagerConfirmStatus());
        log.info("售后状态是否可以重新申请:{}", statusCanReapply);
        if (!statusCanReapply) {
            return false;
        }
        boolean orderStatusCanReapply = true;
        // 发货前订单退款，只有订单状态为 待发货 才可能可以重新申请
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) &&
                !OrderStatusEnum.UNDER_SEND.getCode().equals(order.getOrderStatus())) {
            log.info("发货前订单退款，只有订单状态为 待发货 才可能可以重新申请");
            // 订单退款，订单状态为待支付，或者已支付，都可以重新申请
            orderStatusCanReapply = false;
        }
        // 其余情况，订单不是关闭状态，都可以重新申请
        else if (OrderStatusEnum.CLOSED.getCode().equals(order.getOrderStatus())) {
            log.info("订单是关闭状态，不可以重新申请");
            // 发货后，不同类型，mode不一样
            orderStatusCanReapply = false;
        }
        if (!orderStatusCanReapply) {
            return false;
        }
        boolean isOrderRefund = RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) || Boolean.TRUE.equals(orderRefund.getHasAllReturn());
        // 整单退，且有任意有效的售后，不能重新申请
        if (isOrderRefund) {
            return CollUtil.isEmpty(orderValidList);
        }
        // 单品退，则看是否有对应明细的有效售后
        boolean hasOrderItem = false;
        if (CollUtil.isNotEmpty(orderValidList)) {
            hasOrderItem = orderValidList.stream()
                    .anyMatch(r -> r.getOrderItemId().equals(orderRefund.getOrderItemId()));
        }
        return !hasOrderItem;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveUserDeliverData(UserDeliverParamBo paramBo, OrderRefund orderRefund) {
        UserDeliverRefundItemParamPo paramPo = UserDeliverRefundItemParamPo.builder()
                .refundId(orderRefund.getId())
                .sellerAuditStatus(RefundAuditStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode())
                .expressCompanyName(paramBo.getExpressCompanyName())
                .shipOrderNumber(paramBo.getShipOrderNumber())
                .expressCompanyCode(paramBo.getExpressCompanyCode())
                .deliveryDate(new Date())
                .build();
        orderRefundRepository.saveUserDeliveryData(paramPo);
        // 保存退款日志
        String reason = "";
        if (StrUtil.isNotBlank(paramBo.getExpressCompanyName()) && StrUtil.isNotBlank(paramBo.getShipOrderNumber())) {
            reason = paramBo.getExpressCompanyName() + ":" + paramBo.getShipOrderNumber();
        }
        saveRefundLog(paramBo.getRefundId(), orderRefund.getApplyNumber(), RefundLogStepEnum.WAIT_SUPPLIER_RECEIVE,
                RefundAuditStatusEnum.WAIT_SUPPLIER_RECEIVE, paramBo.getUser().getUserName(), reason, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePlatformAuditPassData(OrderRefund orderRefund, SaveRefundPlatformAuditBo auditPo) {
        // 审核通过，设置为退款中
        orderRefundRepository.updatePlatformAuditStatus(auditPo.getRefundId(), auditPo.getPlatformAuditStatus().getCode(), auditPo.getRemark());
        // 保存退款日志
        saveRefundLog(auditPo.getRefundId(), auditPo.getApplyNumber(), RefundLogStepEnum.REFUND_SUCCESS,
                RefundAuditStatusEnum.REFUND_SUCCESS, auditPo.getUserName(), auditPo.getRemark(), null);
        // 记录接口操作日志，这个目前的aop日志切面不支持
        try {
            OrderRefund updateRefund = new OrderRefund();
            updateRefund.setId(orderRefund.getId());
            updateRefund.setManagerConfirmStatus(auditPo.getPlatformAuditStatus().getCode());
            updateRefund.setManagerRemark(auditPo.getRemark());
            updateRefund.setManagerConfirmDate(new Date());
            updateRefund.setStatus(auditPo.getPlatformAuditStatus().getCode());
            List<ChangeFiled> orderChanged = operationLogAssist.buildChangeList(orderRefund, updateRefund, ChangeFieldDesc.REFUND_MANAGER_APPROVE_REFUND);
            operationLogAssist.log(ExaminModelEnum.ORDER, ExaProEnum.MODIFY, "售后-平台审核通过",
                    JsonUtil.toJsonString(orderChanged), auditPo, auditPo.getUserName());
        }
        catch (Exception e) {
            log.error("【平台审核通过】记录接口操作日志失败", e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void savePlatformAuditRejectData(OrderRefund orderRefund, SaveRefundPlatformAuditBo auditPo) {
        // 修改售后状态
        orderRefundRepository.updatePlatformAuditStatus(auditPo.getRefundId(), auditPo.getPlatformAuditStatus().getCode(), auditPo.getRemark());
        // 保存退款日志
        saveRefundLog(auditPo.getRefundId(), auditPo.getApplyNumber(), RefundLogStepEnum.PLATFORM_REFUSE,
                auditPo.getPlatformAuditStatus(), auditPo.getUserName(), auditPo.getRemark(), null);
        // 订单操作日志
        orderOperationLogRepository.save(auditPo.getOrderId(), auditPo.getUserName(), CommonConst.DESC_REFUND_LOG_PLATFORM_REJECT);
        // 如果是明细退，订单明细同样减去申请退款数量，兼容.net之前数据，需要额外加上判断
        OrderItem orderItem = null;
        OrderItem updateItem = null;
        if (orderRefund.getOrderItemId() > 0 && Boolean.FALSE.equals(orderRefund.getHasAllReturn())) {
            orderItem = orderItemRepository.getById(orderRefund.getOrderItemId());
            updateItem = new OrderItem();
            updateItem.setId(orderItem.getId());
            updateItem.setApplyRefundQuantity(orderItem.getApplyRefundQuantity() - orderRefund.getApplyQuantity());
            int cnt = orderItemRepository.decreaseApplyRefundQuantity(orderRefund.getOrderItemId(), orderRefund.getApplyQuantity());
            log.info("取消退款，修改订单明细申请退款数量，orderItemId:{}, returnQuantity:{}, cnt:{}", orderRefund.getOrderItemId(), orderRefund.getApplyQuantity(), cnt);
        }
        // 记录接口操作日志，这个目前的aop日志切面不支持
        try {
            OrderRefund updateRefund = new OrderRefund();
            updateRefund.setId(orderRefund.getId());
            updateRefund.setManagerConfirmStatus(auditPo.getPlatformAuditStatus().getCode());
            updateRefund.setManagerRemark(auditPo.getRemark());
            updateRefund.setManagerConfirmDate(new Date());
            updateRefund.setStatus(auditPo.getPlatformAuditStatus().getCode());
            List<ChangeFiled> orderChanged = operationLogAssist.buildChangeList(orderRefund, updateRefund, ChangeFieldDesc.REFUND_MANAGER_APPROVE_REFUND);
            if (orderItem != null) {
                List<ChangeFiled> itemChanged = operationLogAssist.buildChangeList(orderItem, updateItem, ChangeFieldDesc.REFUND_MANAGER_APPROVE_ORDER_ITEM);
                orderChanged.addAll(itemChanged);
            }
            operationLogAssist.log(ExaminModelEnum.ORDER, ExaProEnum.MODIFY, "售后-平台审核驳回",
                    JsonUtil.toJsonString(orderChanged), auditPo, auditPo.getUserName());
        }
        catch (Exception e) {
            log.error("【平台审核驳回】记录接口操作日志失败", e);
        }
    }

    /**
     * 整单退
     *
     * @param createBo 售后单创建对象
     * @param order    订单
     * @return 退款对象
     */
    @Transactional(rollbackFor = Exception.class)
    public OrderRefund saveProductRefund(CreateOrderRefundBo createBo, Order order, UserBo userBo) {
        //TODO 暂时只支持一个
        CreateOrderRefundItemBo bo = createBo.getRefundItems().get(0);
        // 构建并退款对象
        DateTime date = DateUtil.date();
        OrderRefund refund = new OrderRefund();
        Long refundId = bizNoGenerator.generateRefundId();
        refund.setId(refundId);
        refund.setOrderId(createBo.getOrderId());
        refund.setSourceRefundId(createBo.getSourceRefundId());
        refund.setOrderItemId(bo.getOrderItemId());
        refund.setAmount(bo.getRefundAmount());
        //不退运费
        refund.setReturnFreight(createBo.getRefundFreight());
        refund.setHasAllReturn(false);
        refund.setReturnQuantity(bo.getQuantity());
        refund.setRefundMode(createBo.getRefundMode().getCode());
        refund.setReason(createBo.getRefundReason());
        refund.setReasonDetail(createBo.getRemark());
        refund.setHasCancel(false);

        refund.setReturnPlatCommission(order.getCommissionTotalAmount());
        refund.setApplyNumber(1);
        refund.setApplyDate(date);
        refund.setCreateTime(date);
        refund.setUpdateTime(date);
        refund.setSellerAuditDate(date);
        refund.setManagerConfirmDate(date);
        refund.setLastModifyTime(date);

        refund.setRefundPayStatus(RefundPayStatusEnum.UN_PAY.getCode());
        refund.setRefundPayType(RefundPayTypeEnum.ORIGINAL.getCode());

        refund.setShopId(order.getShopId());
        refund.setShopName(order.getShopName());
        refund.setUserId(order.getUserId());
        if (userBo != null) {
            refund.setApplicant(userBo.getUserName());
        }
        refund.setContactPerson(order.getUserName());
        refund.setContactCellPhone(order.getCellPhone());
        refund.setManagerConfirmStatus(RefundAuditStatusEnum.WAIT_PLATFORM_CONFIRM.getCode());
        RefundStatusEnum status = createBo.getStatus();
        if (status != null) {
            //指定了状态
            refund.setSellerAuditStatus(status.getCode());
            if (status.getCode() >= RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode()) {
                //买家审核，平台固定
                refund.setSellerAuditStatus(RefundAuditStatusEnum.SUPPLIER_PASS.getCode());
                refund.setManagerConfirmStatus(status.getCode());
            }
            refund.setStatus(status.getCode());
        }
        else {
            refund.setSellerAuditStatus(RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode());
            refund.setStatus(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode());
        }
        List<String> pics = createBo.getPics();
        if (CollectionUtil.isNotEmpty(pics)) {
            refund.setCertPic1(pics.get(0));
            if (pics.size() > 1) {
                refund.setCertPic2(pics.get(1));
            }
            if (pics.size() > 2) {
                refund.setCertPic3(pics.get(2));
            }
        }
        refund.setReturnFreight(createBo.getRefundFreight() == null ? BigDecimal.ZERO : createBo.getRefundFreight());
        if (StrUtil.isNotBlank(createBo.getExpressCompanyCode())) {
            refund.setBuyerDeliverDate(date);
        }
        refund.setExpressCompanyCode(createBo.getExpressCompanyCode());
        refund.setExpressCompanyName(createBo.getExpressCompanyName());
        refund.setShipOrderNumber(createBo.getShipOrderNumber());
        refund.setHasReturn(RefundModeEnum.RETURN_AND_REFUND.equals(createBo.getRefundMode()));

        orderRefundRepository.save(refund);
        // 保存退款日志
        saveRefundLog(refund.getId(), refund.getApplyNumber(),
                RefundLogStepEnum.WAIT_SUPPLIER_AUDIT,
                RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT, userBo != null ? userBo.getUserName() :
                        StrUtil.EMPTY,
                refund.getReason(), null);
        return refund;
    }

    public ItemRemainRefundInfoBo getItemRemainRefund(OrderItem orderItem, List<OrderRefund> validRefundList) {
        ItemRemainRefundInfoBo remain = new ItemRemainRefundInfoBo();
        remain.setOrderItemId(orderItem.getId());
        remain.setOrderId(orderItem.getOrderId());
        if (CollUtil.isEmpty(validRefundList)) {
            remain.setRemainAmount(orderItem.getRealTotalPrice());
            remain.setRemainQuantity(orderItem.getQuantity());
            log.info("订单明细退款剩余数量取订单明细数据：{}，剩余金额：{}", orderItem.getQuantity(), orderItem.getRealTotalPrice());
            return remain;
        }
        // 当前申请的明细，已经发起的有效的数量和金额
        Long orderItemId = orderItem.getId();
        Long returnedQuantity = 0L;
        BigDecimal appliedAmount = BigDecimal.ZERO;
        for (OrderRefund refund : validRefundList) {
            if (orderItemId.equals(refund.getOrderItemId())) {
                // 与.net兼容，returnQuantity只有退货的时候设置了值，且弃货时会重置为0，所以剩余可退数量的计算，如果该值是0，取applyQuantity
                long quantity = refund.getReturnQuantity() > 0 ? refund.getReturnQuantity() : refund.getApplyQuantity();
                returnedQuantity += quantity;
                appliedAmount = appliedAmount.add(refund.getAmount());
            }
        }
        long remainQuantity = orderItem.getQuantity() - returnedQuantity;
        BigDecimal remainAmount = cn.hutool.core.util.NumberUtil.sub(orderItem.getRealTotalPrice(), appliedAmount);
        remain.setRemainAmount(remainAmount);
        remain.setRemainQuantity(remainQuantity);
        log.info("订单明细退款剩余数量：{}，剩余金额：{}", remainQuantity, remainAmount);
        return remain;
    }


    //*****************************************************************

    @Transactional(rollbackFor = Exception.class)
    public void saveSellerAuditData(SellerApproveParamBo auditPo, OrderRefund orderRefund) {
        // 修改售后状态
        BigDecimal amount = orderRefund.getAmount();
        BigDecimal freight = NumberUtil.nullToZero(orderRefund.getReturnFreight());
        // 默认整单整单退款是带了运费的，如果不退运费，变更金额
        if (RefundAuditStatusEnum.SUPPLIER_PASS.equals(auditPo.getAuditStatus()) &&
                !Boolean.TRUE.equals(auditPo.getWhetherRefundFreight())) {
            amount = amount.subtract(freight);
            freight = BigDecimal.ZERO;
        }

        OrderRefund updateRefund = new OrderRefund();
        updateRefund.setSellerAuditStatus(auditPo.getAuditStatus().getCode());
        // 供应商审核通过，相当于待平台确认
        if (RefundAuditStatusEnum.SUPPLIER_PASS.equals(auditPo.getAuditStatus())) {
            updateRefund.setStatus(RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode());
        }
        else {
            updateRefund.setStatus(auditPo.getAuditStatus().getCode());
        }
        updateRefund.setSellerRemark(StrUtil.blankToDefault(auditPo.getSellerRemark(), StrUtil.EMPTY));
        updateRefund.setAmount(amount);
        updateRefund.setReturnFreight(freight);
        // 同意弃货，则不用退数量
        if (Boolean.TRUE.equals(auditPo.getWhetherAbandonGoods())) {
            updateRefund.setReturnQuantity(0L);
        }
        else {
            updateRefund.setReturnQuantity(orderRefund.getReturnQuantity());
        }
        // 如果是供应商审核通过，则同步修改平台为待审核
        // 平台审核状态发起的时候默认就是待审核，所以这里只会更新时间
        if (RefundAuditStatusEnum.SUPPLIER_PASS.equals(auditPo.getAuditStatus())) {
            updateRefund.setManagerConfirmDate(new Date());
        }
        else {
            updateRefund.setManagerConfirmDate(orderRefund.getManagerConfirmDate());
        }
        // 部分字段修改，保存售后数据
        orderRefundRepository.updatePartialById(orderRefund.getId(), updateRefund);
        // 保存退款日志
        saveRefundLog(auditPo.getRefundId(), orderRefund.getApplyNumber(), RefundLogStepEnum.valueBySellerStatus(auditPo.getAuditStatus().getCode()),
                auditPo.getAuditStatus(), auditPo.getUser().getUserName(), auditPo.getSellerRemark(), null);
        // 订单操作日志
        String logContent = getLogContent(auditPo, orderRefund);
        orderOperationLogRepository.save(orderRefund.getOrderId(), auditPo.getUser().getUserName(), logContent);
        // 审核拒绝，且是明细，订单明细同样减去申请退款数量
        OrderItem orderItem = null;
        OrderItem updateItem = null;
        if (RefundAuditStatusEnum.SUPPLIER_REFUSE.equals(auditPo.getAuditStatus()) &&
                orderRefund.getOrderItemId() > 0 && Boolean.FALSE.equals(orderRefund.getHasAllReturn())) {

            orderItem = orderItemRepository.getById(orderRefund.getOrderItemId());
            updateItem = new OrderItem();
            updateItem.setId(orderItem.getId());
            updateItem.setApplyRefundQuantity(orderItem.getApplyRefundQuantity() - orderRefund.getApplyQuantity());

            int cnt = orderItemRepository.decreaseApplyRefundQuantity(orderRefund.getOrderItemId(), orderRefund.getApplyQuantity());
            log.info("审核拒绝，修改订单明细申请退款数量，orderItemId:{}, returnQuantity:{}, cnt:{}", orderRefund.getOrderItemId(), orderRefund.getApplyQuantity(), cnt);
        }
        // 记录接口操作日志，这个目前的aop日志切面不支持
        try {
            updateRefund.setId(orderRefund.getId());
            List<ChangeFiled> orderChanged = operationLogAssist.buildChangeList(orderRefund, updateRefund, ChangeFieldDesc.REFUND_SELLER_APPROVE_REFUND);
            if (orderItem != null) {
                List<ChangeFiled> itemChanged = operationLogAssist.buildChangeList(orderItem, updateItem, ChangeFieldDesc.REFUND_MANAGER_APPROVE_ORDER_ITEM);
                orderChanged.addAll(itemChanged);
            }
            operationLogAssist.log(ExaminModelEnum.ORDER, ExaProEnum.MODIFY, "售后-供应商审核",
                    JsonUtil.toJsonString(orderChanged), auditPo, auditPo.getUser().getUserName());
        }
        catch (Exception e) {
            log.error("【供应商审核】记录接口操作日志失败", e);
        }
    }

    private void saveRefundLog(Long refundId, int applyNumber, RefundLogStepEnum logStepEnum,
                               RefundAuditStatusEnum auditStatus, String userName, String reasonDesc, String remark) {
        // 保存退款日志
        OrderRefundLog refundLog = buildRefundLog(refundId, applyNumber, logStepEnum, auditStatus, userName, reasonDesc, remark);
        orderRefundLogRepository.save(refundLog);
    }

    private OrderRefundLog buildRefundLog(Long refundId, int applyNumber, RefundLogStepEnum logStepEnum,
                                          RefundAuditStatusEnum auditStatus, String userName, String operateDesc, String remark) {
        Date now = new Date();
        OrderRefundLog refundLog = new OrderRefundLog();
        refundLog.setRefundId(refundId);
        refundLog.setOperator(userName);
        refundLog.setOperateDate(now);

        // 根据需求，操作说明，要保存说明
        if (operateDesc == null) {
            operateDesc = "";
        }
        if (StrUtil.isNotBlank(remark)) {
            operateDesc += "：" + remark;
        }
        String operateContent = String.format("【%s】%s", auditStatus.getDesc(), operateDesc);
        refundLog.setOperateContent(operateContent);
        refundLog.setApplyNumber(applyNumber);
        refundLog.setStep(logStepEnum.getCode());
        refundLog.setRemark(operateDesc);
        refundLog.setCreateTime(now);
        refundLog.setUpdateTime(now);
        return refundLog;
    }

    /**
     * 构建订单退款对象
     *
     * @param applyBo 申请数据对象
     * @param order   申请关联的订单
     * <AUTHOR>
     */
    private OrderRefund buildOrderRefund(ApplyRefundBo applyBo, Order order) {
        UserBo userBo = applyBo.getUser();
        Date now = new Date();

        OrderRefund orderRefund = new OrderRefund();
        orderRefund.setOrderId(order.getOrderId());
        orderRefund.setOrderItemId(applyBo.getOrderItemId());
        orderRefund.setShopId(order.getShopId());
        orderRefund.setShopName(order.getShopName());
        orderRefund.setUserId(order.getUserId());
        orderRefund.setApplicant(userBo.getUserName());
        orderRefund.setContactPerson(applyBo.getContactUserName());
        orderRefund.setContactCellPhone(applyBo.getContactUserPhone());
        orderRefund.setApplyDate(now);
        orderRefund.setLastModifyTime(now);
        orderRefund.setAmount(applyBo.getRefundAmount());
        orderRefund.setReason(applyBo.getRefundReasonDesc());
        orderRefund.setReasonDetail(applyBo.getRefundRemark());

        orderRefund.setSellerAuditDate(now);
        // 注意业务逻辑处理好退货数量
        orderRefund.setHasReturn(applyBo.getRefundQuantity() != null && applyBo.getRefundQuantity() > 0);
        orderRefund.setHasAllReturn(applyBo.getHasAllReturn());
        orderRefund.setHasCancel(false);
        orderRefund.setRefundMode(applyBo.getRefundMode().getCode());
        orderRefund.setRefundPayStatus(RefundPayStatusEnum.UN_PAY.getCode());

        orderRefund.setRefundPayType(applyBo.getRefundPayType().getCode());
        orderRefund.setReturnQuantity(applyBo.getRefundQuantity());
        orderRefund.setApplyQuantity(applyBo.getApplyQuantity());
        // 订单退款，退下单时计算设置的平台佣金
        orderRefund.setReturnPlatCommission(applyBo.getReturnCommissionAmount());
        // 新发起时，申请次数默认为1
        orderRefund.setApplyNumber(1);
        orderRefund.setCertPic1(applyBo.getCertPic1());
        orderRefund.setCertPic2(applyBo.getCertPic2());
        orderRefund.setCertPic3(applyBo.getCertPic3());
        orderRefund.setReturnFreight(applyBo.getReturnFreightAmount());
        orderRefund.setManagerConfirmStatus(RefundAuditStatusEnum.WAIT_PLATFORM_CONFIRM.getCode());
        orderRefund.setManagerConfirmDate(now);

        orderRefund.setSourceRefundId(applyBo.getSourceRefundId());


        // 提交申请时，总状态默认为待供应商审核
        orderRefund.setSellerAuditStatus(RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode());
        orderRefund.setStatus(RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode());

        orderRefund.setCreateTime(now);
        orderRefund.setUpdateTime(now);
        return orderRefund;
    }


    @Transactional(rollbackFor = Exception.class)
    public void saveConfirmReceiveData(OrderRefund updateRefund, OrderRefund originRefund, UserBo user, Long refundQuantity, Long confirmQuantity) {
        // 修改售后数据，只修改部分字段
        orderRefundRepository.updatePartialById(originRefund.getId(), updateRefund);

        // 订单表修改最后修改时间。与.net逻辑保持一直
        orderRepository.updateLastModifyTime(originRefund.getOrderId(), updateRefund.getUpdateTime());

        // 订单操作日志
        orderOperationLogRepository.save(originRefund.getOrderId(), user.getUserName(), CommonConst.ORDER_OPERATION_LOG_REFUND_CONFIRM_RECEIVE);
        // 不相等代表修改了
        boolean receiveDiff = !refundQuantity.equals(confirmQuantity);
        // 非整单，有修改，更新订单明细的申请数量
        if (Boolean.FALSE.equals(originRefund.getHasAllReturn()) && receiveDiff) {
            long diffQuantity = refundQuantity - confirmQuantity;
            log.info("订单退款退货数量修改，订单明细id：{}，修改前退货数量：{}，修改后退货数量：{}, diff={}",
                    originRefund.getOrderItemId(), refundQuantity, confirmQuantity, diffQuantity);
            orderItemRepository.decreaseApplyRefundQuantity(originRefund.getOrderItemId(), diffQuantity);
        }

        // 保存退款日志
        String logDesc = CommonConst.DESC_REFUND_LOG_SELLER_CONFIRM_RECEIVE;
        if (receiveDiff) {
            logDesc = String.format(CommonConst.DESC_REFUND_LOG_SELLER_CONFIRM_RECEIVE_CHANGE, refundQuantity, confirmQuantity);
        }
        saveRefundLog(originRefund.getId(), originRefund.getApplyNumber(), RefundLogStepEnum.WAIT_PLATFORM_CONFIRM,
                RefundAuditStatusEnum.WAIT_PLATFORM_CONFIRM, user.getUserName(), logDesc, null);
    }

}
