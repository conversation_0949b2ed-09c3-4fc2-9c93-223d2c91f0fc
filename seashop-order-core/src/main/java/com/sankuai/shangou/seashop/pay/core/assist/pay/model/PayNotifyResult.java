package com.sankuai.shangou.seashop.pay.core.assist.pay.model;

import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/02 16:00
 */
@Data
public class PayNotifyResult {

    /**
     * 支付流水ID, 这个是系统内部生成的支付流水号, 第三方支付会返回
     */
    private String payId;

    /**
     * 交易状态 可以不返回
     */
    private String tradeState;

    /**
     * 第三方支付id
     */
    private String channelPayId;

    /**
     * 返回给第三方支付系统的返回值, 第三方支付的回调结果
     */
    private Object thirdResult;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 有值才更新 true-表示支付成功 false-表示支付失败 null-表示不更新
     */
    private Boolean success;

}
