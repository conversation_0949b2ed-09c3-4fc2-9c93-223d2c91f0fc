package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryOrderCommentBo;
import com.sankuai.shangou.seashop.order.thrift.core.request.DeleteOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ShopMarkResp;

/**
 * <AUTHOR>
 * @date 2023/12/04 10:18
 */
public interface OrderCommentService {

    /**
     * 保存订单评价(买家端)
     *
     * @param commentBo 评价数据
     */
    void saveOrderComment(OrderCommentBo commentBo);

    /**
     * 追加订单评价(买家端)
     *
     * @param commentBo 评价数据
     */
    void appendOrderComment(OrderCommentBo commentBo);

    /**
     * 查询订单评价（买家端）
     *
     * @param pageParam 分页参数
     * @param queryBo   查询条件
     * @return 评价数据
     */
    BasePageResp<OrderCommentBo> queryOrderCommentForBuyer(BasePageParam pageParam, QueryOrderCommentBo queryBo);

    /**
     * 查询订单评论(平台端)
     *
     * @param pageParam 分页参数
     * @param queryBo   查询条件
     * @return 评价数据
     */
    BasePageResp<OrderCommentBo> queryOrderCommentForPlatform(BasePageParam pageParam, QueryOrderCommentBo queryBo);

    /**
     * 删除订单评价（平台端独有接口）
     *
     * @param id 评价id
     */
    void deleteOrderCommentForPlatForm(Long id);

    /**
     * 查询订单评价详情（买家端）
     *
     * @param queryBo 查询条件
     * @return 评价数据
     */
    OrderCommentBo queryOrderCommentDetailForBuyer(QueryOrderCommentBo queryBo);

    /**
     * 查询店铺评分
     *
     * @param shopId
     * @return
     */
    ShopMarkResp queryShopMarkByShopId(Long shopId);

    /**
     * 用户删除订单评价(跟平台的评价删除 所区别的时，用户是自己删除风控未通过的评价，删除完后，可以再次评价)
     *
     * @param req 删除评价请求
     */
    void deleteOrderCommentForBuyer(DeleteOrderCommentReq req);
}
