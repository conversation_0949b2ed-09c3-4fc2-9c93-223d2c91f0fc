package com.sankuai.shangou.seashop.order.core.service.model.order;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/04 11:55
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class QueryOrderCommentBo {

    /**
     * 订单评价id
     */
    private Long id;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 用户id(外观传入)
     */
    private Long userId;

    /**
     * 店铺id(外观传入)
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 评价人名称
     */
    private String userName;


}
