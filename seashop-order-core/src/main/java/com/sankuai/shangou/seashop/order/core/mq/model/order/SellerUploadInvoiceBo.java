package com.sankuai.shangou.seashop.order.core.mq.model.order;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

@Getter
@Setter
@ToString
public class SellerUploadInvoiceBo extends BaseParamReq {

    /**
     * 订单id
     */
    @PrimaryField
    private String orderId;
    /**
     * 当前登录店铺信息
     */
    private ShopBo shop;

    @ExaminField(description = "发票地址")
    private String invoiceUrl;
}
