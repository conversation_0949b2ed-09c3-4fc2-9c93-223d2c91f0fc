package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description: 创建确认订单请求对象(分账请求对象)
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdaPaymentConfirmCreateDto {

    /**
     * Adapt生成的支付对象id
     */
    private String paymentId;

    /**
     * 请求订单号，只能为英文、数字或者下划线的一种或多种组合，保证在app_id下唯一
     */
    private String orderNo;

    /**
     * 确认金额，必须大于0，保留两位小数点，如0.10、100.05等。必须小于等于原支付金额-已确认金额-已撤销金额。
     */
    private String confirmAmt;

    /**
     * 附加说明
     */
    private String description;

    /**
     * 分账对象信息列表，一次请求最多仅支持7个分账方。json对象 形式，详见 分账对象信息列表
     */
    private List<AdaPaymentConfirmCreateDivMembersDto> divMembers;

    /**
     * 手续费收取模式：O-商户手续费账户扣取手续费，I-交易金额中扣取手续费；值为空时，默认值为I；若为O时，分账对象列表中不支持传入手续费承担方
     */
    private String feeMode;

}
