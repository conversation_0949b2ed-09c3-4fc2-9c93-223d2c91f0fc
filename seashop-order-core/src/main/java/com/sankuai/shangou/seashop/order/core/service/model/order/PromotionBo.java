package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

/**
 * 营销对象
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class PromotionBo {

    /**
     * 营销活动ID
     */
    private Long promotionId;
    /**
     * 营销活动名称
     */
    private String promotionName;
    /**
     * 营销活动类型
     */
    private String promotionType;
    /**
     * 营销活动类型描述
     */
    private String promotionTypeDesc;
    /**
     * 满足营销活动的条件描述
     */
    private String matchConditionDesc;
    /**
     * 满足营销活动的值描述
     */
    private String promotionValueDesc;

}
