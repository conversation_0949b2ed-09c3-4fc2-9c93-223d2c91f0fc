package com.sankuai.shangou.seashop.pay.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.pay.common.constant.LockConstant;
import com.sankuai.shangou.seashop.pay.core.bo.ChannelConfigSaveBo;
import com.sankuai.shangou.seashop.pay.core.service.ChannelConfigService;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ChannelConfigSaveReq;
import com.sankuai.shangou.seashop.pay.thrift.core.service.ChannelConfigCmdFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */

@RestController
@RequestMapping("/channelConfig")
public class ChannelConfigCmdController implements ChannelConfigCmdFeign {

    @Resource
    private ChannelConfigService channelConfigService;

    @PostMapping(value = "/update", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> update(@RequestBody ChannelConfigSaveReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("update", request, req -> {
            req.checkParameter();
            ChannelConfigSaveBo saveBo = JsonUtil.copy(req, ChannelConfigSaveBo.class);
            // 修改渠道配置，需要加锁，修改很少，直接加全局锁
            String lockKey = LockConstant.PAYMENT_CONFIG_LOCK_KEY;
            LockHelper.lock(lockKey, LockConstant.LOCK_TIME, () -> {
                channelConfigService.update(saveBo);
            });
            return BaseResp.of();
        });
    }
}
