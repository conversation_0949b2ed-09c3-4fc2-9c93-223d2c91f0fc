package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class UserRefundBo extends RefundBo {

    /**
     * 是否可以重新申请
     */
    private Boolean canReapply = false;
    /**
     * 是否显示【请退货】按钮
     * 商家登录，退货售后单状态为：待买家寄货
     */
    private Boolean showReturnGoodsBtn = false;
    /**
     * 是否明细退，如果为true，重新申请的时候调用明细预览接口，否则调用订单预览接口
     */
    private Boolean whetherItemRefund;

    /**
     * 是否已过售后维权期，简单的按钮，前端需要根据状态和这个字段综合判断是否显示
     */
    private Boolean hasOverAfterSales;

    /**
     * 是否可以取消售后。用于控制前端【取消售后】按钮显示
     * 1. 发起了售后，订单退款，或仅退款，且是待供应商审核或待平台审核时，显示
     * 2. 发起了售后，退货退款时，待供应商审核或待买家寄货时，显示
     */
    private Boolean showCancelRefundBtn = false;

}
