package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefundRecord;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ExcessPaymentRefundReq;

/**
 * @author: lhx
 * @date: 2024/1/8/008
 * @description:
 */
public interface OrderRefundRecordService {

    /**
     * 订单进行超支退款
     *
     * @param request
     */
    void excessPaymentRefund(ExcessPaymentRefundReq request);

    /**
     * 更新订单退款记录对象
     *
     * @param orderRefundRecord
     */
    void updateObj(OrderRefundRecord orderRefundRecord);
}
