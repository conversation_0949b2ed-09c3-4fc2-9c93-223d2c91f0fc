package com.sankuai.shangou.seashop.order.core.service.assit.opLog;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ChangeFieldDesc {

    private String fieldName;
    private String fieldDesc;


    // 常量维护各个业务需要变更的字段，这里用到时是从DB对象取的数据，注解不想加到DB对象，所以采用常量的方式维护
    // 订单业务-供应商改价-订单表
    public final static List<ChangeFieldDesc> ORDER_UPDATE_ITEM_AMOUNT_ORDER = Arrays.asList(new ChangeFieldDesc("orderId", "订单号"),
            new ChangeFieldDesc("totalAmount", "订单总金额"),
            new ChangeFieldDesc("actualPayAmount", "订单实付金额"),
            new ChangeFieldDesc("commissionTotalAmount", "订单佣金金额"));
    // 订单业务-供应商改价-订单明细表
    public final static List<ChangeFieldDesc> ORDER_UPDATE_ITEM_AMOUNT_ITEM = Arrays.asList(new ChangeFieldDesc("id", "订单明细ID"),
            new ChangeFieldDesc("realTotalPrice", "商品支付金额"),
            new ChangeFieldDesc("enabledRefundAmount", "商品可退金额"),
            new ChangeFieldDesc("discountAmount", "商品改价变更金额"));
    // 订单业务-供应商修改运费-订单表
    public final static List<ChangeFieldDesc> ORDER_UPDATE_FREIGHT_ORDER = Arrays.asList(new ChangeFieldDesc("orderId", "订单号"),
            new ChangeFieldDesc("freight", "订单运费"),
            new ChangeFieldDesc("backupFreight", "提交订单时的运费"),
            new ChangeFieldDesc("totalAmount", "订单总金额"),
            new ChangeFieldDesc("actualPayAmount", "订单实付金额"));
    // 订单业务-供应商发货-订单表
    public final static List<ChangeFieldDesc> ORDER_SELLER_DELIVERY_ORDER = Arrays.asList(new ChangeFieldDesc("orderStatus", "订单状态(1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中)"),
            new ChangeFieldDesc("shippingDate", "发货日期"));
    // 订单业务-供应商取消订单-订单表
    public final static List<ChangeFieldDesc> ORDER_SELLER_CANCEL_ORDER_ORDER = Arrays.asList(new ChangeFieldDesc("orderId", "订单号"),
            new ChangeFieldDesc("orderStatus", "订单状态(1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中)"),
            new ChangeFieldDesc("closeReason", "关闭原因"));
    // 订单业务-供应商修改运单号-订单表
    public final static ChangeFieldDesc ORDER_SELLER_UPDATE_WAYBILL_ORDER = new ChangeFieldDesc("orderId", "订单号");
    public final static ChangeFieldDesc ORDER_SELLER_UPDATE_WAYBILL_BILL = new ChangeFieldDesc("expressList", "物流信息");
    public final static List<ChangeFieldDesc> ORDER_SELLER_UPDATE_WAYBILL_CHANGE_FIELD = Arrays.asList(
            new ChangeFieldDesc("expressCompanyCode", "快递公司编码"),
            new ChangeFieldDesc("expressCompanyName", "快递公司名称"),
            new ChangeFieldDesc("shipOrderNumber", "物流单号"));
    // 售后业务-供应商审核-售后表
    public final static List<ChangeFieldDesc> REFUND_SELLER_APPROVE_REFUND = Arrays.asList(new ChangeFieldDesc("id", "售后单号"),
            new ChangeFieldDesc("sellerAuditStatus", "供应商审核状态(1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；5：供应商通过审核)"),
            new ChangeFieldDesc("status", "综合状态(1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；6：待平台确认；7：退款成功；8：平台驳回；9：退款中；-1：买家取消)"),
            new ChangeFieldDesc("sellerRemark", "供应商备注"),
            new ChangeFieldDesc("amount", "退款金额"),
            new ChangeFieldDesc("returnFreight", "退运费金额"),
            new ChangeFieldDesc("returnQuantity", "退货数量"),
            new ChangeFieldDesc("managerConfirmDate", "待平台确认时间"));
    // 售后业务-供应商确认收货-售后表
    public final static List<ChangeFieldDesc> REFUND_SELLER_RECEIVE_REFUND = Arrays.asList(new ChangeFieldDesc("id", "售后单号"),
            new ChangeFieldDesc("sellerAuditStatus", "供应商审核状态(1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；5：供应商通过审核)"),
            new ChangeFieldDesc("status", "综合状态(1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；6：待平台确认；7：退款成功；8：平台驳回；9：退款中；-1：买家取消)"),
            new ChangeFieldDesc("managerConfirmStatus", "平台审核状态(6：待平台确认；7：退款成功；8：平台驳回；9：退款中)"),
            new ChangeFieldDesc("sellerConfirmArrivalDate", "供应商确认到货时间"),
            new ChangeFieldDesc("amount", "退款金额"),
            new ChangeFieldDesc("returnQuantity", "退货数量"),
            new ChangeFieldDesc("managerConfirmDate", "待平台确认时间"));
    // 售后业务-平台审核-售后表
    public final static List<ChangeFieldDesc> REFUND_MANAGER_APPROVE_REFUND = Arrays.asList(new ChangeFieldDesc("id", "售后单号"),
            new ChangeFieldDesc("managerConfirmStatus", "平台审核状态(6：待平台确认；7：退款成功；8：平台驳回；9：退款中)"),
            new ChangeFieldDesc("managerRemark", "平台备注"),
            new ChangeFieldDesc("managerConfirmDate", "平台审核时间"),
            new ChangeFieldDesc("status", "综合状态(1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；6：待平台确认；7：退款成功；8：平台驳回；9：退款中；-1：买家取消)"));
    // 售后业务-平台审核驳回-订单明细
    public final static List<ChangeFieldDesc> REFUND_MANAGER_APPROVE_ORDER_ITEM = Arrays.asList(new ChangeFieldDesc("id", "订单明细ID"),
            new ChangeFieldDesc("applyRefundQuantity", "已申请的退货数量"));
    // 订单业务-异常订单平台确认
    public final static List<ChangeFieldDesc> EXCEPTION_ORDER_PLATFORM_CONFIRM = Arrays.asList(new ChangeFieldDesc("id", "异常订单ID"),
            new ChangeFieldDesc("orderId", "订单号"),
            new ChangeFieldDesc("refundStatus", "退款状态(0：待退款；1：退款中；2：退款完成；3：退款失败)"),
            new ChangeFieldDesc("refundTime", "退款时间"),
            new ChangeFieldDesc("refundBatchNo", "退款批次号"),
            new ChangeFieldDesc("refundManager", "退款操作人"));
}
