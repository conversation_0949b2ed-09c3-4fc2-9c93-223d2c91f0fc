package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.StopWatch;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.PageUtil;
import com.sankuai.shangou.seashop.order.common.config.EsIndexProps;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.constant.EsConst;
import com.sankuai.shangou.seashop.order.common.es.EagleService;
import com.sankuai.shangou.seashop.order.common.es.model.EagleQueryResult;
import com.sankuai.shangou.seashop.order.common.remote.CategoryRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.product.RemoteCategoryBo;
import com.sankuai.shangou.seashop.order.common.remote.model.product.RemoteSkuBo;
import com.sankuai.shangou.seashop.order.core.service.OrderExportService;
import com.sankuai.shangou.seashop.order.core.service.assit.EsDataHandleAssist;
import com.sankuai.shangou.seashop.order.core.service.model.order.*;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderInvoice;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderInvoiceRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderPlatformEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollQueryReq;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.user.thrift.account.enums.InvoiceTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.PipelineAggregatorBuilders;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedNested;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.aggregations.metrics.ParsedValueCount;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderExportServiceImpl extends OrderSearchParamBuilder implements OrderExportService {

    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private EagleService eagleService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private OrderInvoiceRepository orderInvoiceRepository;
    @Resource
    private EsDataHandleAssist esDataHandleAssist;
    @Resource
    private CategoryRemoteService categoryRemoteService;
    @Resource
    private EsIndexProps esIndexProps;

    @Override
    public OrderAndItemScrollBo getScrollIdForUserExport(QueryUserOrderBo searchBo) {
        OrderAndItemScrollBo result = new OrderAndItemScrollBo();
        BoolQueryBuilder boolQueryBuilder = buildUserSearchCondition(searchBo);
        if (boolQueryBuilder == null) {
            return result;
        }
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList();
        // 整合查询条件
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .size(searchBo.getPageSize());
        if (CollUtil.isNotEmpty(sortBuilders)) {
            sortBuilders.forEach(sourceBuilder::sort);
        }
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(esIndexProps.getIdxOrder());
        searchRequest.source(sourceBuilder);
        long timeValueMinutes = ObjectUtil.defaultIfNull(searchBo.getTimeValueMinutes(), EsConst.DEFAULT_SCROLL_TIME_VALUE_MINUTES);
        searchRequest.scroll(TimeValue.timeValueMinutes(timeValueMinutes));
        // 获取返回结果scrollId，source的首页信息
        StopWatch stopWatch = StopWatch.create("商家订单获取导出scrollId");
        stopWatch.start("查询ES");
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        stopWatch.stop();
        // 获取scrollId
        String scrollId = searchResult.getScrollId();
        // 平铺订单成sku维度
        stopWatch.start("订单与明细平铺展开成sku维度");
        List<OrderExportBo> resultList = resolveAndFlatToSku(searchResult);
        stopWatch.stop();
        // 数据填充
        stopWatch.start("填充数据");
        appendExportExtInfo(resultList);
        stopWatch.stop();

        log.info("订单导出耗时：{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));

        result.setScrollId(scrollId);
        result.setDataList(resultList);
        return result;
    }

    @Override
    public OrderAndItemScrollBo getScrollIdForSellerExport(QuerySellerOrderBo searchBo) {
        OrderAndItemScrollBo result = new OrderAndItemScrollBo();
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", searchBo.getShopId()));
        BoolQueryBuilder condBuilder = buildCommonSearchCondition(boolQueryBuilder, searchBo);
        // 约定：条件为null，代表入参条件没有符合的数据
        if (condBuilder == null) {
            return result;
        }
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList();
        // 整合查询条件
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .size(searchBo.getPageSize());
        if (CollUtil.isNotEmpty(sortBuilders)) {
            sortBuilders.forEach(sourceBuilder::sort);
        }
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(esIndexProps.getIdxOrder());
        searchRequest.source(sourceBuilder);
        long timeValueMinutes = ObjectUtil.defaultIfNull(searchBo.getTimeValueMinutes(), EsConst.DEFAULT_SCROLL_TIME_VALUE_MINUTES);
        searchRequest.scroll(TimeValue.timeValueMinutes(timeValueMinutes));
        // 获取返回结果scrollId，source的首页信息
        StopWatch stopWatch = StopWatch.create("供应商订单获取导出scrollId");
        stopWatch.start("查询ES");
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        stopWatch.stop();
        // 获取scrollId
        String scrollId = searchResult.getScrollId();
        // 平铺订单成sku维度
        stopWatch.start("订单与明细平铺展开成sku维度");
        List<OrderExportBo> resultList = resolveAndFlatToSku(searchResult);
        stopWatch.stop();
        // 数据填充
        stopWatch.start("填充数据");
        appendExportExtInfo(resultList);
        stopWatch.stop();

        log.info("订单导出耗时：{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));

        result.setScrollId(scrollId);
        result.setDataList(resultList);
        return result;
    }

    @Override
    public OrderAndItemScrollBo getScrollIdForPlatformExport(QueryPlatformOrderBo searchBo) {
        OrderAndItemScrollBo result = new OrderAndItemScrollBo();
        BoolQueryBuilder boolQueryBuilder = buildPlatformExportQueryBuilder(searchBo);
        // 约定：条件为null，代表入参条件没有符合的数据
        if (boolQueryBuilder == null) {
            return result;
        }
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList();
        // 整合查询条件
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .size(searchBo.getPageSize());
        if (CollUtil.isNotEmpty(sortBuilders)) {
            sortBuilders.forEach(sourceBuilder::sort);
        }
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(esIndexProps.getIdxOrder());
        searchRequest.source(sourceBuilder);
        long timeValueMinutes = ObjectUtil.defaultIfNull(searchBo.getTimeValueMinutes(), EsConst.DEFAULT_SCROLL_TIME_VALUE_MINUTES);
        searchRequest.scroll(TimeValue.timeValueMinutes(timeValueMinutes));
        // 获取返回结果scrollId，source的首页信息
        StopWatch stopWatch = StopWatch.create("平台订单获取导出scrollId");
        stopWatch.start("查询ES");
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        stopWatch.stop();
        // 获取scrollId
        String scrollId = searchResult.getScrollId();
        // 平铺订单成sku维度
        stopWatch.start("订单与明细平铺展开成sku维度");
        List<OrderExportBo> resultList = resolveAndFlatToSku(searchResult);
        stopWatch.stop();
        // 数据填充
        stopWatch.start("填充数据");
        appendExportExtInfo(resultList);
        stopWatch.stop();

        log.info("订单导出耗时：{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));

        result.setScrollId(scrollId);
        result.setDataList(resultList);
        return result;
    }

    @Override
    public BasePageResp<OrderExportBo> searchExportForPlatform(QueryPlatformOrderBo searchBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = buildPlatformExportQueryBuilder(searchBo);
        // 约定：条件为null，代表入参条件没有符合的数据
        if (boolQueryBuilder == null) {
            return PageResultHelper.defaultEmpty(searchBo);
        }
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList();

        // 构建搜索请求
        SearchRequest searchRequest = buildExportRequest(searchBo, boolQueryBuilder, sortBuilders);
        log.info("【订单搜索】卖家端搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        log.debug("【订单搜索】入参为: {}, 搜索结果为: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(searchResult));
        // 解析搜索结果
        BasePageResp<OrderExportBo> resultBo = resolveExportData(searchResult, searchBo);
        log.debug("【订单搜索】入参为: {}, 订单结果: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(resultBo));
        appendExportExtInfo(resultBo);
        return resultBo;
    }

    @Override
    public BasePageResp<OrderExportBo> searchExportForSeller(QuerySellerOrderBo searchBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", searchBo.getShopId()));
        BoolQueryBuilder condBuilder = buildCommonSearchCondition(boolQueryBuilder, searchBo);
        // 约定：条件为null，代表入参条件没有符合的数据
        if (condBuilder == null) {
            return PageResultHelper.defaultEmpty(searchBo);
        }
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList();
        // 构建搜索请求
        SearchRequest searchRequest = buildExportRequest(searchBo, boolQueryBuilder, sortBuilders);
        log.info("【订单搜索】卖家端搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        log.debug("【订单搜索】入参为: {}, 搜索结果为: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(searchResult));
        // 解析搜索结果
        BasePageResp<OrderExportBo> resultBo = resolveExportData(searchResult, searchBo);
        log.debug("【订单搜索】入参为: {}, 订单结果: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(resultBo));
        appendExportExtInfo(resultBo);
        return resultBo;
    }

    @Override
    public BasePageResp<OrderExportBo> searchExportForUser(QueryUserOrderBo searchBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = buildUserSearchCondition(searchBo);
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList();
        // 构建搜索请求
        SearchRequest searchRequest = buildExportRequest(searchBo, boolQueryBuilder, sortBuilders);
        log.info("【订单搜索】卖家端搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        log.debug("【订单搜索】入参为: {}, 搜索结果为: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(searchResult));
        // 解析搜索结果
        BasePageResp<OrderExportBo> resultBo = resolveExportData(searchResult, searchBo);
        log.debug("【订单搜索】入参为: {}, 订单结果: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(resultBo));
        appendExportExtInfo(resultBo);
        return resultBo;
    }

    @Override
    public List<OrderExportBo> searchByScrollId(EsScrollQueryReq queryReq) {
        // 导出基于 scroll 搜索，接口是无状态的，但是scroll需要之前的scrollId
        SearchScrollRequest scrollRequest = new SearchScrollRequest(queryReq.getScrollId());
        //6.指定scrollId的生存时间
        scrollRequest.scroll(TimeValue.timeValueMinutes(queryReq.getTimeValueMinutes()));
        EagleQueryResult searchResult = eagleService.scroll(scrollRequest);
        // 平铺订单成sku维度
        List<OrderExportBo> resultList = resolveAndFlatToSku(searchResult);
        // 数据填充
        appendExportExtInfo(resultList);
        return resultList;
    }

    @Override
    public void clearScrollId(String scrollId) {
        eagleService.clearScroll(scrollId);
    }


    //*****************************************************************

    private BoolQueryBuilder buildPlatformExportQueryBuilder(QueryPlatformOrderBo searchBo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        BoolQueryBuilder condBuilder = buildCommonSearchCondition(boolQueryBuilder, searchBo);
        // 约定：条件为null，代表入参条件没有符合的数据
        if (condBuilder == null) {
            return null;
        }
        if (StrUtil.isNotBlank(searchBo.getPayId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("payId", searchBo.getPayId()));
        }
        if (StrUtil.isNotBlank(searchBo.getTradeNo())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tradeNo", searchBo.getTradeNo()));
        }
        if (searchBo.getShopId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", searchBo.getShopId()));
        }
        // 从店铺ES获取店铺ID
        if (StrUtil.isNotBlank(searchBo.getShopName())) {
            List<Long> shopIdList = shopRemoteService.searchShopIdByNameFromEs(searchBo.getShopName());
            // 如果店铺ID为空，直接返回空
            if (CollUtil.isEmpty(shopIdList)) {
                return null;
            }
            boolQueryBuilder.filter(QueryBuilders.termsQuery("shopId", shopIdList));
        }
        return boolQueryBuilder;
    }

    private void appendExportExtInfo(BasePageResp<OrderExportBo> resultBo) {
        // 前置处理，分页对象不会为空，这里没有考虑为空的情况，直接取值
        List<OrderExportBo> dataList = resultBo.getData();
        appendExportExtInfo(dataList);
    }

    private void appendExportExtInfo(List<OrderExportBo> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return;
        }
        int size = dataList.size();
        Set<String> orderIdSet = new HashSet<>(size);
        List<Long> itemIdList = new ArrayList<>(size);
        Set<String> skuIdSet = new HashSet<>(size);
        Set<Long> productIdSet = new HashSet<>(size);
        dataList.forEach(skuBo -> {
            orderIdSet.add(skuBo.getOrderId());
            itemIdList.add(skuBo.getItemId());
            skuIdSet.add(skuBo.getSkuId());
            productIdSet.add(skuBo.getProductId());
        });

        List<String> orderIdList = new ArrayList<>(orderIdSet);
        List<String> skuIdList = new ArrayList<>(skuIdSet);
        List<Long> productIdList = new ArrayList<>(productIdSet);
        log.info("【订单搜索】orderIdList.size={}, itemIdList.size={}, skuIdList.size={}, productIdList.size={}",
                orderIdList.size(), itemIdList.size(), skuIdList.size(), productIdList.size());

        StopWatch stopWatch = new StopWatch("填充订单数据");
        stopWatch.start("查询订单");
        // 订单信息
        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
        Map<String, Order> orderMap = orderList.stream().collect(Collectors.toMap(Order::getOrderId, order -> order, (oldV, newV) -> newV));
        stopWatch.stop();
        // 订单明细
        stopWatch.start("查询订单明细");
        List<OrderItem> itemList = orderItemRepository.listByIds(itemIdList);
        Map<Long, OrderItem> itemMap = itemList.stream().collect(Collectors.toMap(OrderItem::getId, item -> item, (oldV, newV) -> newV));
        stopWatch.stop();
        // sku信息
        stopWatch.start("查询商品");
        List<RemoteSkuBo> skuList = productRemoteService.getSkuList(skuIdList);
        Map<String, RemoteSkuBo> skuMap = skuList.stream().collect(Collectors.toMap(RemoteSkuBo::getSkuId, sku -> sku, (oldV, newV) -> newV));
        stopWatch.stop();
        // 发票信息
        stopWatch.start("查询发票");
        List<OrderInvoice> invoiceList = orderInvoiceRepository.getByOrderIdList(orderIdList);
        Map<String, OrderInvoice> invoiceMap = invoiceList.stream().collect(Collectors.toMap(OrderInvoice::getOrderId, invoice -> invoice, (oldV, newV) -> newV));
        stopWatch.stop();
        // 类目信息，需要先查询出商品，才能拿到类目ID
        stopWatch.start("查询类目");
        Map<String/*skuId*/, String/*categoryFullName*/> cateFullNameMap = getProductCateAndMapToSkuWithFullName(productIdList, itemList);
        stopWatch.stop();

        log.info("【订单搜索】订单数据填充耗时：{}", stopWatch.prettyPrint(TimeUnit.MILLISECONDS));

        dataList.forEach(data -> {
            Order dbOrder = orderMap.get(data.getOrderId());
            if (dbOrder != null) {
                data.setUserId(dbOrder.getUserId());
                data.setShopName(dbOrder.getShopName());
                data.setPlatformDesc(OrderPlatformEnum.getDesc(dbOrder.getPlatform()));
                data.setOrderDateStr(DateUtil.format(dbOrder.getOrderDate(), DatePattern.NORM_DATETIME_PATTERN));
                data.setOrderType(dbOrder.getOrderType());
                data.setOrderTypeDesc(OrderTypeEnum.getDesc(dbOrder.getOrderType()));
                data.setUserName(dbOrder.getUserName());
                if (dbOrder.getPayDate() != null) {
                    data.setPayDateStr(DateUtil.format(dbOrder.getPayDate(), DatePattern.NORM_DATETIME_PATTERN));
                }
                if (dbOrder.getFinishDate() != null) {
                    data.setFinishDateStr(DateUtil.format(dbOrder.getFinishDate(), DatePattern.NORM_DATETIME_PATTERN));
                }
                data.setPayMethodDesc(PayMethodEnum.getDesc(dbOrder.getPayment()));
                data.setGatewayOrderId(dbOrder.getGatewayOrderId());
                data.setProductTotalAmount(dbOrder.getProductTotalAmount());
                data.setFreight(dbOrder.getFreight());
                data.setTax(dbOrder.getTax());
                data.setCouponAmount(dbOrder.getCouponAmount());
                data.setMoneyOffAmount(dbOrder.getMoneyOffAmount());
                data.setTotalAmount(dbOrder.getTotalAmount());
                data.setCommissionTotalAmount(dbOrder.getCommissionTotalAmount());
                data.setOrderStatus(dbOrder.getOrderStatus());
                data.setOrderStatusDesc(OrderStatusEnum.getDesc(dbOrder.getOrderStatus()));
                data.setUserRemark(dbOrder.getUserRemark());
                data.setShipTo(dbOrder.getShipTo());
                data.setCellPhone(dbOrder.getCellPhone());
                String regionFullName = StrUtil.blankToDefault(dbOrder.getRegionFullName(), "");
                String shipAddress = StrUtil.blankToDefault(dbOrder.getAddress(), "");
                data.setAddress(regionFullName + " " + shipAddress);
            }


            OrderItem item = itemMap.get(data.getItemId());
            if (item != null) {
                data.setUpdateAmount(item.getDiscountAmount());
                data.setSkuAutoId(item.getSkuAutoId());
                data.setSalePrice(item.getSalePrice());
            }

            RemoteSkuBo sku = skuMap.get(data.getSkuId());
            if (sku != null) {
                data.setBarCode(sku.getBarCode());
                data.setSkuCode(sku.getSkuCode());
            }

            OrderInvoice invoice = invoiceMap.get(data.getOrderId());
            if (invoice != null) {
                InvoiceTypeEnum typeEnum = InvoiceTypeEnum.valueOf(invoice.getInvoiceType());
                if (typeEnum != null) {
                    data.setInvoiceType(invoice.getInvoiceType());
                    data.setInvoiceTypeDesc(typeEnum.getName());
                }
                data.setInvoiceTitle(invoice.getInvoiceTitle());
                data.setInvoiceCode(invoice.getInvoiceCode());
                data.setInvoiceContext(invoice.getInvoiceContext());
                data.setRegisterAddress(invoice.getRegisterAddress());
                data.setRegisterPhone(invoice.getRegisterPhone());
                data.setBankName(invoice.getBankName());
                data.setBankNo(invoice.getBankNo());
                data.setRealName(invoice.getRealName());
                data.setInvoiceCellPhone(invoice.getCellPhone());
                data.setEmail(invoice.getEmail());
            }

            String cateFullName = cateFullNameMap.get(data.getSkuId());
            if (StrUtil.isNotBlank(cateFullName)) {
                String[] cateNameArr = cateFullName.split(CommonConst.PATTERN_CATEGORY_FULL_NAME);
                if (cateNameArr.length > 0) {
                    data.setCateLevel1Name(cateNameArr[0]);
                }
                if (cateNameArr.length > 1) {
                    data.setCateLevel2Name(cateNameArr[1]);
                }
                if (cateNameArr.length > 2) {
                    data.setCateLevel3Name(cateNameArr[2]);
                }
            }
        });
    }

    /**
     * 用户的采购统计需要返回订单+sku维度，即对订单明细平铺后分页返回，目前采用的方案是通过聚合函数实现平铺
     * 虽然是使用聚合实现的平铺，但因为列表还需要订单数据，所以同步返回了文档
     * <p>不确定性能是否会有问题</p>
     * <AUTHOR>
     * org.elasticsearch.action.search.SearchRequest
     */
    private SearchRequest buildExportRequest(BasePageReq pageReq, BoolQueryBuilder boolQueryBuilder,
                                             List<SortBuilder<FieldSortBuilder>> sortBuilders) {

        int from = PageUtil.getStart(pageReq.getPageNo(), pageReq.getPageSize());
        int total = pageReq.getPageNo() * pageReq.getPageSize();
        log.info("【订单搜索】商家采购统计搜索条件为: from: {}, pageSize: {}, total: {}", from, pageReq.getPageSize(), total);
        // 构建聚合查询
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        // 聚合sku
        AggregationBuilder productAgg = AggregationBuilders.nested("itemsAgg", "orderItems")
                .subAggregation(
                        // 平铺后分页聚合
                        AggregationBuilders.terms("pageItems")
                                .field("orderItems.itemId")
                                // topHits 返回sku文档
                                .subAggregation(AggregationBuilders.topHits("topHits").size(1))
                                // 通过bucketSort实现分页
                                .subAggregation(
                                        PipelineAggregatorBuilders.bucketSort("bucketSort", Lists.newArrayList())
                                                // from和to指定分页参数
                                                .from(from)
                                                .size(pageReq.getPageSize()))
                                // 这里指定size是返回所有的桶数据，在桶内聚合和排序
                                .size(total)
                                // 基于明细ID排序，相当于根据订单创建时间降序
                                .order(BucketOrder.key(false))
                ).subAggregation(
                        // 符合条件的明细总数，代表分页的总记录数，用于分页
                        AggregationBuilders.count("totalCount").field("orderItems.itemId")
                ).subAggregation(
                        // 符合条件的明细总数，代表分页的总记录数，用于分页
                        AggregationBuilders.sum("quantitySum").field("orderItems.quantity")
                );
        aggregationBuilders.add(productAgg);

        // 聚合用户订单数量：订单数量（售后导致订单关闭不扣减）
        AggregationBuilder orderCountAgg = AggregationBuilders.count("totalOrderCount").field("orderId");
        aggregationBuilders.add(orderCountAgg);
        // 聚合订单金额：采购金额之和
        AggregationBuilder orderAmountAgg = AggregationBuilders.sum("totalOrderAmount").field("totalAmount");
        aggregationBuilders.add(orderAmountAgg);

        // 使用聚合实现明细的平铺
        return esDataHandleAssist.buildNonDocRequest(esIndexProps.getIdxOrder(), boolQueryBuilder, aggregationBuilders);
    }

    private BasePageResp<OrderExportBo> resolveExportData(EagleQueryResult searchResult, BasePageReq pageReq) {
        // 定义默认返回值
        BasePageResp<OrderExportBo> pageData = PageResultHelper.defaultEmpty(pageReq);
        if (searchResult.getTotalHit() != null && searchResult.getTotalHit() == 0) {
            return PageResultHelper.defaultEmpty(pageReq);
        }
        // 解析平铺的明细聚合
        ParsedNested itemsAgg = searchResult.getAggregations().get("itemsAgg");
        if (itemsAgg != null) {
            // 解析符合条件的明细总记录数，这个明细总数就是总记录数，用于计算分页相关数据
            ParsedValueCount totalCountAgg = itemsAgg.getAggregations().get("totalCount");
            long totalCount = 0L;
            if (totalCountAgg != null) {
                totalCount = totalCountAgg.getValue();
                // 分页数据
                pageData.setTotalCount(totalCount);
                pageData.setPages(PageUtil.totalPage(totalCount, pageReq.getPageSize()));
            }
            // 解析明细列表
            ParsedLongTerms pageItemsAgg = itemsAgg.getAggregations().get("pageItems");
            List<? extends Terms.Bucket> buckets = pageItemsAgg.getBuckets();
            if (CollUtil.isEmpty(buckets)) {
                return PageResultHelper.defaultEmpty(pageReq);
            }
            List<OrderExportBo> dataList = new ArrayList<>(buckets.size());
            for (Terms.Bucket bucket : buckets) {
                // 解析明细文档
                ParsedTopHits topHits = bucket.getAggregations().get("topHits");
                if (topHits != null) {
                    SearchHits searchHits = topHits.getHits();
                    SearchHit hit = searchHits.getAt(0);
                    OrderExportBo skuBO = JsonUtil.parseObject(hit.getSourceAsString(), OrderExportBo.class);
                    // 订单的索引ID设置的是订单号，所以这里可以设置，如果有变更，这里需要调整
                    skuBO.setOrderId(hit.getId());

                    dataList.add(skuBO);
                }
            }
            pageData.setData(dataList);
        }
        return pageData;
    }

    private Map<String/*skuId*/, String/*categoryFullName*/> getProductCateAndMapToSkuWithFullName(List<Long> productIdList, List<OrderItem> itemList) {
        if (CollUtil.isEmpty(productIdList)) {
            return Collections.emptyMap();
        }
        if (CollUtil.isEmpty(itemList)) {
            return Collections.emptyMap();
        }
        List<ProductBasicDto> productList = productRemoteService.queryProductList(productIdList);
        Map<Long, Long> productCateIdMap = productList.stream()
                .collect(Collectors.toMap(ProductBasicDto::getProductId, ProductBasicDto::getCategoryId, (oldV, newV) -> newV));
        List<Long> cateIdList = productList.stream().map(ProductBasicDto::getCategoryId).distinct().collect(Collectors.toList());
        List<RemoteCategoryBo> categoryList = categoryRemoteService.getCategoryList(cateIdList);
        Map<Long, String> cateFullNameMap = categoryList.stream()
                .collect(Collectors.toMap(RemoteCategoryBo::getId, RemoteCategoryBo::getFullCategoryName, (oldV, newV) -> newV));
        return itemList.stream().collect(Collectors.toMap(OrderItem::getSkuId, item -> {
            Long cateId = productCateIdMap.get(item.getProductId());
            if (cateId == null) {
                return "";
            }
            String cateFullName = cateFullNameMap.get(cateId);
            if (StrUtil.isBlank(cateFullName)) {
                return "";
            }
            return cateFullName;
        }, (oldV, newV) -> newV));
    }

    private List<OrderExportBo> resolveAndFlatToSku(EagleQueryResult searchResult) {
        // 解析商品列表
        List<EsOrderBo> orderList = searchResult.getHits().stream()
                .map(hit -> JsonUtil.parseObject(hit, EsOrderBo.class))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(orderList)) {
            return null;
        }
        // 平铺订单成sku维度
        List<OrderExportBo> resultList = new ArrayList<>();
        orderList.forEach(order -> {
            List<EsOrderItemBo> itemList = order.getOrderItems();
            if (CollUtil.isNotEmpty(itemList)) {
                itemList.forEach(item -> {
                    // 拷贝部分字段，其他字段后续查询数据库设置
                    OrderExportBo orderExportBo = new OrderExportBo();
                    orderExportBo.setOrderId(order.getOrderId());
                    orderExportBo.setItemId(item.getItemId());
                    orderExportBo.setProductId(item.getProductId());
                    orderExportBo.setSkuId(item.getSkuId());
                    orderExportBo.setProductName(item.getProductName());
                    orderExportBo.setQuantity(item.getQuantity());
                    resultList.add(orderExportBo);
                });
            }
        });
        return resultList;
    }

}
