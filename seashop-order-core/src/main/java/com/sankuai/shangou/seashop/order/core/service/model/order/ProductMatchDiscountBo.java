package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ProductMatchDiscountBo {

    private Long id;
    /**
     * 折扣门槛(订单金额需要满足多少)
     */
    private BigDecimal quota;
    /**
     * 折扣比例(达到门槛后商品单价的优惠比例)
     */
    private BigDecimal discount;
    /**
     * 折扣活动ID
     */
    private Long activityId;
    /**
     * 折扣活动名称
     */
    private String activityName;
    /**
     * 是否用于全部商品
     */
    private Boolean izAllProduct;
    /**
     * 活动适用的商品
     */
    private List<Long> productIdList;
    /**
     * 满足条件的商品总金额
     */
    private BigDecimal productTotalAmount;

}
