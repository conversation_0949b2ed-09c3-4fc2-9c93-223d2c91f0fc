package com.sankuai.shangou.seashop.pay.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.core.service.PayService;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayBaseReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayCorpMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.UpdatePaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayBaseReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayCorpMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPayMemberReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPaySettleAccountQryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.AdaPaySettleAccountReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PaySettleAccountResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.adapay.AdaPaySettleAccountResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.MemberCmdFeign;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@RestController
@RequestMapping("/member")
public class MemberCmdController implements MemberCmdFeign {

    @Resource
    private PayService payService;

    @PostMapping(value = "/queryMember", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> queryMember(@RequestBody PayBaseReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createMember", request, req -> {
            req.checkParameter();
            if (req.getPaymentChannel().equals(PaymentChannelEnum.ADAPAY.getCode())) {
                payService.queryMember(JsonUtil.copy(req, AdaPayBaseReq.class));
            }
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/createMember", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> createMember(@RequestBody PayMemberReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createMember", request, req -> {
            req.checkParameter();
            if (req.getPaymentChannel().equals(PaymentChannelEnum.ADAPAY.getCode())) {
                payService.createMember(JsonUtil.copy(req, AdaPayMemberReq.class));
            }
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/createSettleAccount", consumes = "application/json")
    @Override
    public ResultDto<PaySettleAccountResp> createSettleAccount(@RequestBody PaySettleAccountReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createSettleAccount", request, req -> {
            req.checkParameter();
            if (req.getPaymentChannel().equals(PaymentChannelEnum.ADAPAY.getCode())) {
                AdaPaySettleAccountResp settleAccount = payService.createSettleAccount(JsonUtil.copy(req, AdaPaySettleAccountReq.class));
                return JsonUtil.copy(settleAccount, PaySettleAccountResp.class);
            } else {
                // req.checkParameter() 已经校验类型，这里不会执行
                return null;
            }
        });
    }

    @PostMapping(value = "/updateSettleAccount", consumes = "application/json")
    @Override
    public ResultDto<PaySettleAccountResp> updateSettleAccount(@RequestBody UpdatePaySettleAccountReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateSettleAccount", request, req -> {
            req.checkParameter();
            if (req.getPaymentChannel().equals(PaymentChannelEnum.ADAPAY.getCode())) {
                //先删除结算账号
                AdaPaySettleAccountQryReq adaPaySettleAccountQryReq =
                        JsonUtil.copy(req, AdaPaySettleAccountQryReq.class);
                payService.deleteSettleAccount(adaPaySettleAccountQryReq);
                //再创建结算账号
                AdaPaySettleAccountResp settleAccount = payService.createSettleAccount(JsonUtil.copy(req, AdaPaySettleAccountReq.class));
                return JsonUtil.copy(settleAccount, PaySettleAccountResp.class);
            } else {
                // req.checkParameter() 已经校验类型，这里不会执行
                return null;
            }
        });
    }

//    @Override
//    public ResultDto<PaySettleAccountResp> createMemberAndSettleAccount(PayMemberAndAccountReq request) throws TException {
//        return ThriftResponseHelper.responseInvoke("createMemberAndSettleAccount", request, req -> {
//            req.checkParameter();
//            if (req.getPayMemberReq().getPaymentChannel().equals(PaymentChannelEnum.ADAPAY.getCode())) {
//                AdaPaySettleAccountResp memberAndSettleAccount = payService.createMemberAndSettleAccount(JsonUtil.copy(req, AdaPayMemberAndAccountReq.class));
//                return JsonUtil.copy(memberAndSettleAccount, PaySettleAccountResp.class);
//            } else {
//                // req.checkParameter() 已经校验类型，这里不会执行
//                return null;
//            }
//        });
//    }

    @PostMapping(value = "/createCompanyMember", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> createCompanyMember(@RequestBody PayCorpMemberReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createCompanyMember", request, req -> {
            req.checkParameter();
            if (req.getPaymentChannel().equals(PaymentChannelEnum.ADAPAY.getCode())) {
                payService.createCompanyMember(JsonUtil.copy(req, AdaPayCorpMemberReq.class));
            }
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/updateCompanyMember", consumes = "application/json")
    @Override
    public ResultDto<Boolean> updateCompanyMember(@RequestBody PayCorpMemberReq request) {
        return ThriftResponseHelper.responseInvoke("updateCompanyMember", request, req -> {
            req.checkParameter();
            if (req.getPaymentChannel().equals(PaymentChannelEnum.ADAPAY.getCode())) {
                return payService.updateCompanyMember(JsonUtil.copy(req, AdaPayCorpMemberReq.class));
            }
            return false;
        });
    }


}
