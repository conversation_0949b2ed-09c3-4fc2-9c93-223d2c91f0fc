package com.sankuai.shangou.seashop.order.core.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.apache.commons.collections4.MapUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.BucketOrder;
import org.elasticsearch.search.aggregations.bucket.nested.ParsedNested;
import org.elasticsearch.search.aggregations.bucket.terms.ParsedLongTerms;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.ParsedTopHits;
import org.elasticsearch.search.aggregations.metrics.ParsedValueCount;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.RegionQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.order.common.config.EsIndexProps;
import com.sankuai.shangou.seashop.order.common.constant.EsConst;
import com.sankuai.shangou.seashop.order.common.enums.OrderComplaintStatusEnum;
import com.sankuai.shangou.seashop.order.common.enums.YesOrNoEnum;
import com.sankuai.shangou.seashop.order.common.es.EagleService;
import com.sankuai.shangou.seashop.order.common.es.model.EagleQueryResult;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.core.service.OrderStatsService;
import com.sankuai.shangou.seashop.order.core.service.ProductCommentService;
import com.sankuai.shangou.seashop.order.core.service.assit.EsDataHandleAssist;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShopCommentSummaryBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.IntegerFieldStatsCountBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.PlatformStatsTradeDataBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.ProductSaleAmountBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.SellerIndexTradeStatsBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.ShopOrderCountStatsBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.StatsShopTopNSaleProductParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.TopProductSaleStatsBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.repository.ComplaintRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryProductBuyCountReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.ProductBuyCountResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单统计
 * <p>可能通过ES使用，可能通过数据表实现，涉及ES的，直接在本类实现的，没有全部放到EsOrderService</p>
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderStatsServiceImpl implements OrderStatsService {

    @Resource
    private EagleService eagleService;
    @Resource
    private ComplaintRepository complaintRepository;
    @Resource
    private EsDataHandleAssist esDataHandleAssist;
    @Resource
    private ProductCommentService productCommentService;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private RegionQueryFeign regionQueryFeign;
    @Resource
    private EsIndexProps esIndexProps;

    /**
     * 平台统计交易数据
     * <AUTHOR>
     */
    @Override
    public PlatformStatsTradeDataBo statsPlatformIndexTradeData() {
        // 默认返回0
        PlatformStatsTradeDataBo dataBo = PlatformStatsTradeDataBo.defaultZero();
        // 统计订单数据
        buildOrderStatsForPlatformIndex(dataBo);
        // 统计售后数据
        buildRefundStatsForPlatformIndex(dataBo);
        // 统计今日有效销售总额
        buildTodayEffectiveAmount(dataBo);
        // 统计待处理投诉数据
        long complaintCount = complaintRepository.statsForStatusForPlatform(OrderComplaintStatusEnum.WAIT_PLATFORM_DEAL.getCode());
        dataBo.setUnderDealComplaintCount(complaintCount);
        // 统计今日有效销售总额
        return dataBo;
    }

    @Override
    public SellerIndexTradeStatsBo statsSellerIndexTradeData(Long shopId) {
        SellerIndexTradeStatsBo dataBo = SellerIndexTradeStatsBo.defaultZero();
        // 先统计订单数据
        buildOrderStatsForSellerIndex(shopId, dataBo);
        // 统计售后数据
        buildRefundStatsForSellerIndex(shopId, dataBo);
        // 统计待处理投诉数据
        long complaintCount = complaintRepository.statsForStatusForSeller(shopId, OrderComplaintStatusEnum.WAIT_SUPPLIER_DEAL.getCode());
        dataBo.setUnderDealComplaintCount(complaintCount);
        // 统计待回复评论数
        ShopCommentSummaryBo commentCount = productCommentService.queryShopCommentSummary(shopId);
        dataBo.setUnderReplyCommentCount(commentCount.getWaitReplyCount().longValue());
        dataBo.setTotalProductCommentCount(commentCount.getTotalCount().longValue());
        // 统计昨日数据，多次查询ES进行聚合
        buildYesterdayOrderCountForSellerIndex(shopId, dataBo);
        buildYesterdayPayCountForSellerIndex(shopId, dataBo);
        buildYesterdayShopUV(shopId,dataBo);
        return dataBo;
    }

    private void buildYesterdayShopUV(Long shopId, SellerIndexTradeStatsBo dataBo) {
        dataBo.setYesterdayVisitCount(shopRemoteService.getYesterdayShopUV(shopId));
    }

    @Override
    public TopProductSaleStatsBo statsTopNSaleProduct(StatsShopTopNSaleProductParamBo statsParam) {
        if (statsParam.getTopN() == null) {
            return TopProductSaleStatsBo.defaultZero();
        }
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        buildStatsTopNSaleProductQuery(statsParam, boolQueryBuilder);
        // 构建聚合查询，根据商品ID分组，对realTotalPrice求和汇总，然后根据汇总结果进行排序，取前N个
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        buildStatsTopNSaleProductAgg(statsParam, aggregationBuilders);
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = buildRequestAndDoSearch(esIndexProps.getIdxOrder(), boolQueryBuilder, aggregationBuilders);
        // 解析结果
        return resolveTopNSaleProduct(searchResult, statsParam);
    }

    @Override
    public List<ShopOrderCountStatsBo> searchPaidDateLessThanAndNotDeliver(Date lessThanDate) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("payDate").lt(lessThanDate.getTime()))
                .filter(QueryBuilders.termQuery("orderStatus", OrderStatusEnum.UNDER_SEND.getCode()));
        // 构建聚合查询，根据店铺分组，对orderId进行聚合
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        AggregationBuilder notDeliverAgg = AggregationBuilders.terms("shopNotDeliverAgg")
                // 聚合按照shopId分组
                .field("shopId")
                .size(EsConst.DEFAULT_AGG_SIZE)
                // 子聚合，根据orderId统计数量
                .subAggregation(AggregationBuilders.count("notDeliveryCount").field("orderId"));
        aggregationBuilders.add(notDeliverAgg);
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = buildRequestAndDoSearch(esIndexProps.getIdxOrder(), boolQueryBuilder, aggregationBuilders);
        // 解析结果
        if (searchResult == null || searchResult.getAggregations() == null) {
            return null;
        }
        // 解析商品ID分组
        ParsedLongTerms shopNotDeliverAgg = searchResult.getAggregations().get("shopNotDeliverAgg");
        if (shopNotDeliverAgg == null) {
            return null;
        }
        List<? extends Terms.Bucket> buckets = shopNotDeliverAgg.getBuckets();
        if (CollUtil.isEmpty(buckets)) {
            return null;
        }
        List<ShopOrderCountStatsBo> notDeliverList = new ArrayList<>();
        ShopOrderCountStatsBo statsBo = null;
        for (Terms.Bucket bucket : buckets) {
            statsBo = new ShopOrderCountStatsBo();
            statsBo.setShopId(bucket.getKeyAsNumber().longValue());
            ParsedValueCount count = bucket.getAggregations().get("notDeliveryCount");
            statsBo.setNotDeliverCount(count.getValue());
            notDeliverList.add(statsBo);
        }
        return notDeliverList;
    }

    @Override
    public List<ProductBuyCountResp> searchUserProductBuyCount(QueryProductBuyCountReq queryReq) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("userId", queryReq.getUserId()))
                .mustNot(QueryBuilders.termQuery("orderStatus", OrderStatusEnum.CLOSED.getCode()))
                .filter(QueryBuilders.nestedQuery("orderItems",
                        QueryBuilders.termsQuery("orderItems.productId", queryReq.getProductIdList()),
                        org.apache.lucene.search.join.ScoreMode.None));
        // 构建聚合查询，根据店铺分组，对productId进行聚合
        // 对商品明细进行聚合
        AggregationBuilder aggregationBuilders = AggregationBuilders.nested("buyCountSumAgg", "orderItems")
                .subAggregation(
                        AggregationBuilders.terms("groupByProductIdAgg")
                                // 对productId进行分组
                                .field("orderItems.productId")
                                // 子聚合，quantity求和
                                .subAggregation(AggregationBuilders.sum("quantityCount").field("orderItems.quantity"))
                );
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = buildRequestAndDoSearch(esIndexProps.getIdxOrder(), boolQueryBuilder, Collections.singletonList(aggregationBuilders));
        // 解析结果
        if (searchResult == null || searchResult.getAggregations() == null) {
            return Collections.emptyList();
        }
        // 解析聚合
        ParsedNested buyCountSumAgg = searchResult.getAggregations().get("buyCountSumAgg");
        if (buyCountSumAgg == null) {
            return Collections.emptyList();
        }
        ParsedLongTerms groupByProductIdAgg = buyCountSumAgg.getAggregations().get("groupByProductIdAgg");
        if (groupByProductIdAgg == null) {
            return Collections.emptyList();
        }
        List<? extends Terms.Bucket> buckets = groupByProductIdAgg.getBuckets();
        if (CollUtil.isEmpty(buckets)) {
            return Collections.emptyList();
        }
        List<ProductBuyCountResp> buyCountList = new ArrayList<>();
        ProductBuyCountResp buyCount = null;
        for (Terms.Bucket bucket : buckets) {
            buyCount = new ProductBuyCountResp();
            buyCount.setProductId(bucket.getKeyAsNumber().longValue());
            ParsedSum count = bucket.getAggregations().get("quantityCount");
            buyCount.setQuantity((long)count.getValue());
            buyCountList.add(buyCount);
        }
        return buyCountList;
    }

    @Override
    public ShippingAddressDto getNearOrderAddress(Long userId) {
        log.info("【订单】查询最近下单地址, userId={}");
        Order order = orderRepository.getOne(new LambdaQueryWrapper<Order>().eq(Order::getUserId, userId).orderByDesc(Order::getCreateTime));
        if (order == null) {
            return null;
        }
        log.info("【订单】最近下单地址, userId={}, order={}", userId, JsonUtil.toJsonString(order));

        ShippingAddressDto address = new ShippingAddressDto();
        address.setUserId(address.getUserId());
        address.setRegionId(order.getRegionId());
        address.setShipTo(order.getShipTo());
        address.setAddress(order.getRegionFullName());
        address.setAddressDetail(order.getAddress());
        address.setPhone(order.getCellPhone());

        RegionIdsReq req = new RegionIdsReq();
        req.setRegionIds(Collections.singletonList(order.getRegionId()));
        Map<String, AllPathRegionResp> regionMap = ThriftResponseHelper.executeThriftCall(()->regionQueryFeign.getAllPathRegions(req));
        if (MapUtils.isNotEmpty(regionMap) && regionMap.containsKey(order.getRegionId().toString())) {
            AllPathRegionResp region = regionMap.get(order.getRegionId().toString());
            address.setProvinceId(region.getProvinceId());
            address.setProvinceName(region.getProvinceName());
            address.setCityId(region.getCityId());
            address.setCityName(region.getCityName());
            address.setDistrictId(region.getCountyId());
            address.setDistrictName(region.getCountyName());
            address.setRegionFullName(region.getProvinceName()+" "+region.getCityName()+" "+region.getCountyName()+" "+region.getTownsNames());
            address.setRegionPath(region.getProvinceId()+","+region.getCityId()+","+region.getCountyId()+","+region.getTownIds());
        }
        log.info("【订单】最近下单地址, userId={}, address={}", userId, JsonUtil.toJsonString(address));
        return address;
    }


    //************************************************************


    private void buildOrderStatsForPlatformIndex(PlatformStatsTradeDataBo dataBo) {
        // 平台查询不需要查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 构建聚合查询
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        // 聚合订单状态分组，统计各个状态的数量
        AggregationBuilder statusStatsAgg = AggregationBuilders.terms("orderStatusGroup").field("orderStatus");
        aggregationBuilders.add(statusStatsAgg);
        // 执行统计
        EagleQueryResult orderStatsResult = buildRequestAndDoSearch(esIndexProps.getIdxOrder(), boolQueryBuilder, aggregationBuilders);
        // 解析订单状态的统计结果
        Map<Integer, IntegerFieldStatsCountBo> orderStatusMap = resolveOrderStatusAgg(orderStatsResult);
        // 设置订单状态的统计结果
        if (orderStatusMap != null) {
            if (orderStatusMap.containsKey(OrderStatusEnum.UNDER_PAY.getCode())) {
                dataBo.setUnderPayOrderCount(orderStatusMap.get(OrderStatusEnum.UNDER_PAY.getCode()).getCount());
            }
            if (orderStatusMap.containsKey(OrderStatusEnum.UNDER_SEND.getCode())) {
                dataBo.setUnderDeliveryOrderCount(orderStatusMap.get(OrderStatusEnum.UNDER_SEND.getCode()).getCount());
            }
            if (orderStatusMap.containsKey(OrderStatusEnum.FINISHED.getCode())) {
                dataBo.setCompletedOrderCount(orderStatusMap.get(OrderStatusEnum.FINISHED.getCode()).getCount());
            }
        }
    }

    private void buildRefundStatsForPlatformIndex(PlatformStatsTradeDataBo dataBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("refundStatus", RefundAuditStatusEnum.WAIT_PLATFORM_CONFIRM.getCode()));
        // 构建聚合查询
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        // 聚合订单状态分组，统计各个状态的数量
        AggregationBuilder statusStatsAgg = AggregationBuilders.terms("refundTypeGroup").field("refundType");
        aggregationBuilders.add(statusStatsAgg);
        // 执行统计
        EagleQueryResult orderStatsResult = buildRequestAndDoSearch(esIndexProps.getIdxRefund(), boolQueryBuilder, aggregationBuilders);
        // 解析订单状态的统计结果
        Map<Integer, IntegerFieldStatsCountBo> refundTypeMap = resolveRefundTypeAgg(orderStatsResult);
        // 设置订单状态的统计结果
        if (refundTypeMap != null) {
            if (refundTypeMap.containsKey(RefundTypeEnum.ONLY_REFUND.getCode())) {
                dataBo.setUnderDealRefundCount(refundTypeMap.get(RefundTypeEnum.ONLY_REFUND.getCode()).getCount());
            }
            if (refundTypeMap.containsKey(RefundTypeEnum.RETURN_AND_REFUND.getCode())) {
                dataBo.setUnderDealReturnCount(refundTypeMap.get(RefundTypeEnum.RETURN_AND_REFUND.getCode()).getCount());
            }
        }
    }

    /**
     * 今日有效销售总额=今日订单付款总额-今日订单且在今日退款成功的金额
     * <p>订单数据上的actualPayAmount实付金额字段，是退款成功后会减去退款金额的，
     * 而当前统计，如果有售后成功的，那也一定是今日退款成功的，所以直接统计订单数据上该字段</p>
     * <AUTHOR>
     * @param dataBo 数据对象
     */
    private void buildTodayEffectiveAmount(PlatformStatsTradeDataBo dataBo) {
        // 先设置默认值，后续重置
        dataBo.setTodayEffectiveAmount(BigDecimal.ZERO);
        // 当天的时间戳范围
        Date now = new Date();
        Date todayStart = DateUtil.beginOfDay(now);
        Date todayEnd = DateUtil.endOfDay(now);
        // 构建查询条件
        // 平台查询不需要查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        // 今日订单
        boolQueryBuilder.must(QueryBuilders.rangeQuery("orderDate").gte(todayStart.getTime()).lte(todayEnd.getTime()))
                // 付款成功的
                .must(QueryBuilders.termsQuery("orderStatus", Arrays.asList(OrderStatusEnum.UNDER_SEND.getCode(), OrderStatusEnum.UNDER_RECEIVE.getCode(), OrderStatusEnum.FINISHED.getCode())));
        // 构建聚合查询
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        // 聚合统计订单实付金额
        AggregationBuilder actualPayAmountAgg = AggregationBuilders.sum("actualPayAmountAgg").field("actualPayAmount");
        aggregationBuilders.add(actualPayAmountAgg);
        // 执行统计
        EagleQueryResult orderStatsResult = buildRequestAndDoSearch(esIndexProps.getIdxOrder(), boolQueryBuilder, aggregationBuilders);
        // 解析订单状态的统计结果
        if (orderStatsResult == null || orderStatsResult.getAggregations() == null) {
            return;
        }
        // 解析商品ID分组
        ParsedSum actualPayAmount = orderStatsResult.getAggregations().get("actualPayAmountAgg");
        if (actualPayAmount != null) {
            BigDecimal value = new BigDecimal(actualPayAmount.getValueAsString());
            BigDecimal todayEffectiveAmount = NumberUtil.nullToZero(value).setScale(2, RoundingMode.HALF_UP);
            dataBo.setTodayEffectiveAmount(todayEffectiveAmount);
        }

    }

    private void buildOrderStatsForSellerIndex(Long shopId, SellerIndexTradeStatsBo dataBo) {
        // 查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", shopId));
        // 构建聚合查询
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        // 聚合订单状态分组，统计各个状态的数量
        AggregationBuilder statusStatsAgg = AggregationBuilders.terms("orderStatusGroup").field("orderStatus");
        aggregationBuilders.add(statusStatsAgg);
        AggregationBuilder orderCountAgg = AggregationBuilders.count("orderCount").field("orderId");
        aggregationBuilders.add(orderCountAgg);
        // 执行统计
        EagleQueryResult orderStatsResult = buildRequestAndDoSearch(esIndexProps.getIdxOrder(), boolQueryBuilder, aggregationBuilders);
        Map<String, Aggregation> aggregations = orderStatsResult.getAggregations().asMap();
        // 解析订单状态的统计结果
        Map<Integer, IntegerFieldStatsCountBo> orderStatusMap = resolveOrderStatusAgg(orderStatsResult);
        // 设置订单状态的统计结果
        if (orderStatusMap != null) {
            if (orderStatusMap.containsKey(OrderStatusEnum.UNDER_PAY.getCode())) {
                dataBo.setUnderPayOrderCount(orderStatusMap.get(OrderStatusEnum.UNDER_PAY.getCode()).getCount());
            }
            if (orderStatusMap.containsKey(OrderStatusEnum.UNDER_SEND.getCode())) {
                dataBo.setUnderDeliveryOrderCount(orderStatusMap.get(OrderStatusEnum.UNDER_SEND.getCode()).getCount());
            }
        }
        // 解析订单数量
        ParsedValueCount orderCountAggResult = (ParsedValueCount) aggregations.get("orderCount");
        if (orderCountAggResult != null) {
            dataBo.setOrderCount(orderCountAggResult.getValue());
        }
    }

    private void buildYesterdayOrderCountForSellerIndex(Long shopId, SellerIndexTradeStatsBo dataBo) {
        // 查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", shopId));
        Date yesterday = DateUtil.yesterday();
        long beginStamp = DateUtil.beginOfDay(yesterday).getTime();
        long endStamp = DateUtil.endOfDay(yesterday).getTime();
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("orderDate").gte(beginStamp).lt(endStamp));
        // 构建聚合查询
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        // 订单数量
        AggregationBuilder orderCountAgg = AggregationBuilders.count("orderCount").field("orderId");
        aggregationBuilders.add(orderCountAgg);
        // 执行统计
        EagleQueryResult orderStatsResult = buildRequestAndDoSearch(esIndexProps.getIdxOrder(), boolQueryBuilder, aggregationBuilders);
        Map<String, Aggregation> aggregations = orderStatsResult.getAggregations().asMap();
        // 解析订单数量
        ParsedValueCount orderCountAggResult = (ParsedValueCount) aggregations.get("orderCount");
        if (orderCountAggResult != null) {
            dataBo.setYesterdayOrderCount(orderCountAggResult.getValue());
        }
    }

    private void buildYesterdayPayCountForSellerIndex(Long shopId, SellerIndexTradeStatsBo dataBo) {
        // 查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", shopId));
        Date yesterday = DateUtil.yesterday();
        long beginStamp = DateUtil.beginOfDay(yesterday).getTime();
        long endStamp = DateUtil.endOfDay(yesterday).getTime();
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("payDate").gte(beginStamp).lt(endStamp));
        // 构建聚合查询
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        // 订单数量
        AggregationBuilder orderCountAgg = AggregationBuilders.count("payCount").field("orderId");
        aggregationBuilders.add(orderCountAgg);
        // 执行统计
        EagleQueryResult orderStatsResult = buildRequestAndDoSearch(esIndexProps.getIdxOrder(), boolQueryBuilder, aggregationBuilders);
        Map<String, Aggregation> aggregations = orderStatsResult.getAggregations().asMap();
        // 解析订单数量
        ParsedValueCount orderCountAggResult = (ParsedValueCount) aggregations.get("payCount");
        if (orderCountAggResult != null) {
            dataBo.setYesterdayPayCount(orderCountAggResult.getValue());
        }
    }

    private void buildRefundStatsForSellerIndex(Long shopId, SellerIndexTradeStatsBo dataBo) {
        // 查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", shopId))
                .filter(QueryBuilders.termQuery("isDelete", YesOrNoEnum.NO.getCode()))
                // 查询待处理的数据
                .filter(QueryBuilders.termsQuery("refundStatus",
                        Arrays.asList(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode(), RefundStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode())));
        // 构建聚合查询
        List<AggregationBuilder> aggregationBuilders = new ArrayList<>();
        // 聚合订单状态分组，统计各个状态的数量
        AggregationBuilder statusStatsAgg = AggregationBuilders.terms("refundTypeGroup").field("refundType");
        aggregationBuilders.add(statusStatsAgg);
        // 执行统计
        EagleQueryResult orderStatsResult = buildRequestAndDoSearch(esIndexProps.getIdxRefund(), boolQueryBuilder, aggregationBuilders);
        // 解析订单状态的统计结果
        Map<Integer, IntegerFieldStatsCountBo> refundTypeMap = resolveRefundTypeAgg(orderStatsResult);
        // 设置订单状态的统计结果
        if (refundTypeMap != null) {
            if (refundTypeMap.containsKey(RefundTypeEnum.ONLY_REFUND.getCode())) {
                dataBo.setUnderDealRefundCount(refundTypeMap.get(RefundTypeEnum.ONLY_REFUND.getCode()).getCount());
            }
            if (refundTypeMap.containsKey(RefundTypeEnum.RETURN_AND_REFUND.getCode())) {
                dataBo.setUnderDealReturnCount(refundTypeMap.get(RefundTypeEnum.RETURN_AND_REFUND.getCode()).getCount());
            }
        }
    }

    private Map<Integer, IntegerFieldStatsCountBo> resolveOrderStatusAgg(EagleQueryResult searchResult) {
        if (searchResult == null || searchResult.getAggregations() == null) {
            return null;
        }
        Terms agg = searchResult.getAggregations().get("orderStatusGroup");
        return esDataHandleAssist.resolveCountAggToMap(agg.getBuckets());
    }

    private Map<Integer, IntegerFieldStatsCountBo> resolveRefundTypeAgg(EagleQueryResult searchResult) {
        if (searchResult == null || searchResult.getAggregations() == null) {
            return null;
        }
        Terms agg = searchResult.getAggregations().get("refundTypeGroup");
        return esDataHandleAssist.resolveCountAggToMap(agg.getBuckets());
    }

    private void buildStatsTopNSaleProductQuery(StatsShopTopNSaleProductParamBo statsParam, BoolQueryBuilder boolQueryBuilder) {
        // 用户条件必填
        if (statsParam.getShopId() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("shopId", statsParam.getShopId()));
        }
        if (statsParam.getStartTimeStamp() != null || statsParam.getEndTimeStamp() != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("orderDate");
            if (statsParam.getStartTimeStamp() != null) {
                rangeQueryBuilder.gte(statsParam.getStartTimeStamp());
            }
            if (statsParam.getEndTimeStamp() != null) {
                rangeQueryBuilder.lte(statsParam.getEndTimeStamp());
            }
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
    }

    private void buildStatsTopNSaleProductAgg(StatsShopTopNSaleProductParamBo statsParam, List<AggregationBuilder> aggregationBuilders) {
        // 对商品明细进行聚合
        AggregationBuilder productAgg = AggregationBuilders.nested("top_products", "orderItems")
                .subAggregation(
                        AggregationBuilders.terms("product_terms")
                                // 对productId进行分组
                                .field("orderItems.productId")
                                // 结果取前N个
                                .size(statsParam.getTopN())
                                // 根据total_sales排序，total_sales是后面定义的子聚合
                                .order(BucketOrder.aggregation("total_sales", false))
                                // 子聚合，对realTotalPrice求和到total_sales，然后取要返回的字段
                                .subAggregation(AggregationBuilders.sum("total_sales").field("orderItems.realTotalPrice"))
                                // 子聚合，定义返回字段
                                .subAggregation(
                                        AggregationBuilders.topHits("product_name")
                                                .size(1)
                                                .sort("orderDate", SortOrder.DESC)
                                                .fetchField("orderItems.productName")
                                )
                );
        aggregationBuilders.add(productAgg);
    }

    private TopProductSaleStatsBo resolveTopNSaleProduct(EagleQueryResult searchResult, StatsShopTopNSaleProductParamBo statsParam) {
        TopProductSaleStatsBo statsBo = TopProductSaleStatsBo.defaultZero();
        if (searchResult == null || searchResult.getAggregations() == null) {
            return statsBo;
        }
        // 解析聚合结果
        ParsedNested topProductsAgg = searchResult.getAggregations().get("top_products");
        if (topProductsAgg == null) {
            return statsBo;
        }
        // 解析商品ID分组
        ParsedLongTerms productTermsAgg = topProductsAgg.getAggregations().get("product_terms");
        if (productTermsAgg == null) {
            return statsBo;
        }
        List<? extends Terms.Bucket> buckets = productTermsAgg.getBuckets();
        if (CollUtil.isEmpty(buckets)) {
            return statsBo;
        }
        List<ProductSaleAmountBo> productSaleAmountBos = new ArrayList<>();
        statsBo.setSaleList(productSaleAmountBos);
        BigDecimal totalSales = BigDecimal.ZERO;
        ProductSaleAmountBo saleAmountBo = null;
        int rank = 1;
        for (Terms.Bucket bucket : buckets) {
            saleAmountBo = new ProductSaleAmountBo();
            saleAmountBo.setRank(rank++);
            // 解析商品ID
            Long productId = bucket.getKeyAsNumber().longValue();
            saleAmountBo.setProductId(productId);
            // 解析商品销售总额
            saleAmountBo.setSaleAmount(BigDecimal.ZERO);
            ParsedSum totalSalesAgg = bucket.getAggregations().get("total_sales");
            if (totalSalesAgg != null) {
                BigDecimal value = new BigDecimal(totalSalesAgg.getValueAsString()).setScale(2, RoundingMode.HALF_UP);
                saleAmountBo.setSaleAmount(value);
                totalSales = totalSales.add(value);
            }
            // 解析商品名称
            saleAmountBo.setProductName("");
            ParsedTopHits productNameAgg = bucket.getAggregations().get("product_name");
            if (productNameAgg != null) {
                String productName = null;
                SearchHit[] hits = productNameAgg.getHits().getHits();
                if (hits != null && hits.length > 0) {
                    SearchHit lastHit = hits[hits.length - 1];
                    productName = lastHit.getSourceAsMap().get("productName").toString();
                }
                saleAmountBo.setProductName(productName);
            }
            productSaleAmountBos.add(saleAmountBo);
        }
        statsBo.setAvgSaleAmount(totalSales.divide(new BigDecimal(statsParam.getTopN()), 2, RoundingMode.HALF_UP));
        return statsBo;
    }




    private EagleQueryResult buildRequestAndDoSearch(String index, BoolQueryBuilder boolQueryBuilder, List<AggregationBuilder> aggregationBuilders) {
        SearchRequest searchRequest = esDataHandleAssist.buildNonDocRequest(index, boolQueryBuilder, aggregationBuilders);
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        log.info("【订单搜索】统计交易数据结果为: {}", JsonUtil.toJsonString(searchResult));
        return searchResult;
    }



}
