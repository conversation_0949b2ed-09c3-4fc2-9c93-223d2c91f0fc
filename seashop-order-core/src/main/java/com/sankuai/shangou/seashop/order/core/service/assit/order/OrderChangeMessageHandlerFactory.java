package com.sankuai.shangou.seashop.order.core.service.assit.order;

import java.util.HashMap;
import java.util.Map;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;

import cn.hutool.core.map.MapUtil;

/**
 * <AUTHOR>
 */
@Component
public class OrderChangeMessageHandlerFactory implements ApplicationContextAware, InitializingBean {

    private ApplicationContext applicationContext;

    private final Map<OrderMessageEventEnum, OrderChangeMessageHandler> handlerMap = new HashMap<>(8);

    public OrderChangeMessageHandler getHandler(OrderMessageEventEnum event) {
        return handlerMap.get(event);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, OrderChangeMessageHandler> beanMap = this.applicationContext.getBeansOfType(OrderChangeMessageHandler.class);
        if (MapUtil.isEmpty(beanMap)) {
            return;
        }
        beanMap.forEach((k, v) -> handlerMap.put(v.getEvent(), v));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
