package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.core.service.ComplaintQueryService;
import com.sankuai.shangou.seashop.order.thrift.core.ComplaintQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryMComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryMallComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderRightsReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QuerySellerComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintRespPage;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderRightsResp;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/21 19:14
 */
@RestController
@RequestMapping("/complaintQuery")
public class ComplaintQueryController implements ComplaintQueryFeign {

    @Resource
    private ComplaintQueryService complaintQueryService;

    @PostMapping(value = "/pageMComplaint", consumes = "application/json")
    @Override
    public ResultDto<QueryOrderComplaintRespPage> pageMComplaint(@RequestBody QueryMComplaintReq queryMComplaintReq) {
        return ThriftResponseHelper.responseInvoke("pageMComplaint", queryMComplaintReq, req -> {
            req.checkParameter();
            return complaintQueryService.pageMComplaint(req);
        });
    }

    @PostMapping(value = "/pageSellerComplaint", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<QueryOrderComplaintResp>> pageSellerComplaint(@RequestBody QuerySellerComplaintReq queryOrderComplaintReq) {
        return ThriftResponseHelper.responseInvoke("pageSellerComplaint", queryOrderComplaintReq, req -> {
            req.checkParameter();
            return complaintQueryService.pageSellerComplaint(req);
        });
    }

    @PostMapping(value = "/pageMallComplaint", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<QueryOrderComplaintResp>> pageMallComplaint(@RequestBody QueryMallComplaintReq queryMallComplaintReq) {
        return ThriftResponseHelper.responseInvoke("pageMallComplaint", queryMallComplaintReq, req -> {
            req.checkParameter();
            return complaintQueryService.pageMallComplaint(req);
        });
    }

    @PostMapping(value = "/pageQueryOrderRights", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<QueryOrderRightsResp>> pageQueryOrderRights(@RequestBody QueryOrderRightsReq queryOrderRightsReq) {
        return ThriftResponseHelper.responseInvoke("pageQueryOrderRights", queryOrderRightsReq, req -> {
            req.checkParameter();
            return complaintQueryService.pageQueryOrderRights(req);
        });
    }
}
