package com.sankuai.shangou.seashop.order.core.service.assit.refund;

import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.enums.PlatformMessageTemplateEnum;
import com.sankuai.shangou.seashop.order.common.remote.MemberRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.user.MemberContactBo;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.core.service.assit.MessageNoticeHelper;
import com.sankuai.shangou.seashop.order.core.service.model.sms.OrderIdMsgBo;
import com.sankuai.shangou.seashop.order.core.service.model.sms.UserNameMsgBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.RefundEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformApproveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerApproveReq;
import com.sankuai.shangou.seashop.user.thrift.account.ManagerQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.response.ManagerResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ApplyRefundMessageHandler implements RefundMessageHandler {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private MemberRemoteService memberRemoteService;
    @Resource
    private MessageNoticeHelper messageNoticeHelper;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderRefundCmdFeign orderRefundCmdFeign;
    @Resource
    private ManagerQueryFeign managerQueryFeign;

    @Override
    public RefundEventEnum getEvent() {
        return RefundEventEnum.APPLY;
    }

    @Override
    public void handle(OrderRefundMessage message) {
        OrderRefund orderRefund = orderRefundRepository.getById(message.getRefundId());
        if (orderRefund == null) {
            log.warn("【售后消息处理】申请售后消息处理失败，订单售后不存在，message: {}", message);
            return;
        }
        // 用户通知
        try {
            MemberContactBo memberContact = memberRemoteService.getMemberContactByUserId(orderRefund.getUserId());
            log.info("【售后消息处理】申请售后消息处理，用户通知， memberContact: {}", JsonUtil.toJsonString(memberContact));
            if (memberContact != null) {
                // 区分退款或者退货
                PlatformMessageTemplateEnum template = null;
                if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(orderRefund.getRefundMode())) {
                    template = PlatformMessageTemplateEnum.MERCHANT_APPLY_RETURN;
                } else {
                    template = PlatformMessageTemplateEnum.MERCHANT_APPLY_REFUND;
                }

                // 构建消息体，邮箱和短信内容一样
                String siteName = messageNoticeHelper.getSiteName();
                String msgBody = messageNoticeHelper.buildEmailBody(template, siteName, memberContact.getUserName());
                UserNameMsgBo msgBo = new UserNameMsgBo(memberContact.getUserName());
                // 发送消息
                messageNoticeHelper.noticeForTemplate(memberContact, template, msgBo, msgBody);
            }
        } catch (Exception e) {
            log.error("【售后消息处理】申请售后消息处理失败，message: {}", message, e);
        }

        // 店铺通知
        MemberContactBo shopContact = null;
        try {
            shopContact = memberRemoteService.getMemberContactByShopId(orderRefund.getShopId());
            log.info("【售后消息处理】申请售后消息处理，给供应商发消息, shopContact: {}", JsonUtil.toJsonString(shopContact));
            if (shopContact != null) {
                PlatformMessageTemplateEnum template = PlatformMessageTemplateEnum.SUPPLIER_AFTER_SALE;
                // 构建消息体，邮箱和短信内容一样
                String siteName = messageNoticeHelper.getSiteName();
                String msgBody = messageNoticeHelper.buildEmailBody(template, siteName, orderRefund.getOrderId());
                OrderIdMsgBo msgBo = new OrderIdMsgBo(orderRefund.getOrderId());
                // 发送消息
                messageNoticeHelper.noticeForTemplate(shopContact, template, msgBo, msgBody);
            }
        } catch (Exception e) {
            log.error("【售后消息处理】申请售后消息处理失败，message: {}", message, e);
        }
    }

    /**
     * 构建供货商审核参数
     *
     * @param orderRefund 订单退款信息
     * @param order 订单信息
     * @param shopContact 店铺信息
     * @return 供货商审核参数
     */
    private SellerApproveReq buildSellerApproveReq(OrderRefund orderRefund, Order order, MemberContactBo shopContact) {
        SellerApproveReq sellerApproveReq = new SellerApproveReq();
        sellerApproveReq.setRefundId(orderRefund.getId());
        sellerApproveReq.setAuditStatus(RefundAuditStatusEnum.SUPPLIER_PASS);
        // 设置店铺
        ShopDto shop = new ShopDto();
        shop.setShopId(orderRefund.getShopId());
        shop.setShopName(order.getShopName());
        sellerApproveReq.setShop(shop);

        // 设置店铺管理员用户 信息
        UserDto user = new UserDto();
        if (shopContact == null) {
            shopContact = memberRemoteService.getMemberContactByShopId(orderRefund.getShopId());
        }
        user.setUserId(shopContact.getUserId());
        user.setUserName(shopContact.getUserName());
        user.setUserPhone(shopContact.getContact());
        sellerApproveReq.setUser(user);

        return sellerApproveReq;
    }

    /**
     * 构建平台审核参数
     *
     * @param orderRefund 订单退款信息
     * @param defaultManager 默认平台管理员信息
     * @return 平台审核参数
     */
    private PlatformApproveReq buildPlatformApproveReq(OrderRefund orderRefund, ManagerResp defaultManager) {
        PlatformApproveReq platformApproveReq = new PlatformApproveReq();
        platformApproveReq.setRefundId(orderRefund.getId());
        platformApproveReq.setOperationUserId(defaultManager.getId());

        // 设置平台管理员用户 信息
        UserDto user = new UserDto();
        user.setUserId(defaultManager.getId());
        user.setUserName(defaultManager.getUserName());
        platformApproveReq.setUser(user);

        return platformApproveReq;
    }

}
