package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.core.service.ComplaintCmdService;
import com.sankuai.shangou.seashop.order.thrift.core.ComplaintCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.*;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/21 19:14
 */
@RestController
@RequestMapping("/complaintCmd")
public class ComplaintCmdController implements ComplaintCmdFeign {

    @Resource
    private ComplaintCmdService complaintCmdService;

    @PostMapping(value = "/startComplaint", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> startComplaint(@RequestBody StartComplaintReq cmdOrderComplaintReq) throws TException {
        return ThriftResponseHelper.responseInvoke("startComplaint", cmdOrderComplaintReq, req -> {
            req.checkParameter();
            complaintCmdService.startComplaint(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/cancelComplaint", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> cancelComplaint(@RequestBody CancelComplaintReq cancelComplaintReq) {
        return ThriftResponseHelper.responseInvoke("cancelComplaint", cancelComplaintReq, req -> {
            req.checkParameter();
            complaintCmdService.cancelComplaint(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/dealSellerComplaint", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> dealSellerComplaint(@RequestBody DealSellerComplaintReq dealSellerComplaintReq) {
        return ThriftResponseHelper.responseInvoke("dealSellerComplaint", dealSellerComplaintReq, req -> {
            req.checkParameter();
            complaintCmdService.dealSellerComplaint(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/applyArbitrateComplaint", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> applyArbitrateComplaint(@RequestBody ApplyArbitrateComplaintReq applyArbitrateComplaintReq) {
        return ThriftResponseHelper.responseInvoke("applyArbitrateComplaint", applyArbitrateComplaintReq, req -> {
            req.checkParameter();
            complaintCmdService.applyArbitrateComplaint(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/applyMallArbitrateComplaint", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> applyMallArbitrateComplaint(@RequestBody ApplyArbitrateComplaintReq applyArbitrateComplaintReq) {
        return ThriftResponseHelper.responseInvoke("applyMallArbitrateComplaint", applyArbitrateComplaintReq, req -> {
            req.checkParameter();
            complaintCmdService.applyMallArbitrateComplaint(req);
            return new BaseResp();
        });
    }

    @PostMapping(value = "/dealMComplaint", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> dealMComplaint(@RequestBody DealMComplaintReq dealMComplaintReq) {
        return ThriftResponseHelper.responseInvoke("dealMComplaint", dealMComplaintReq, req -> {
            req.checkParameter();
            complaintCmdService.dealMComplaint(req);
            return new BaseResp();
        });
    }
}
