package com.sankuai.shangou.seashop.order.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.common.enums.MqErrorDataBizTypeEnum;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.RefundResultNotifyBo;
import com.sankuai.shangou.seashop.order.core.service.assit.MqErrorDataAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.RefundResultNotifyAssist;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessTypeEnum;

import cn.hutool.crypto.digest.MD5;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_ORDER_REFUND_NOTIFY,
//        group = MafkaConst.GROUP_ORDER_REFUND_NOTIFY)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_REFUND_NOTIFY + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_REFUND_NOTIFY + "_${spring.profiles.active}")
public class RefundNotifyListener implements RocketMQListener<MessageExt> {

    @Resource
    private RefundResultNotifyAssist refundResultNotifyAssist;
    @Resource
    private MqErrorDataAssist mqErrorDataAssist;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【退款回调】MQ收到的消息为:{}", body);

        RefundResultNotifyBo payResultBo = null;
        try {
            payResultBo = JsonUtil.parseObject(body, RefundResultNotifyBo.class);
        } catch (Exception e) {
            log.error("【mafka消费】【退款回调】消息转换失败", e);
            String traceId = message.getMsgId();
            mqErrorDataAssist.saveErrorData(MqErrorDataBizTypeEnum.REFUND_NOTIFY, MD5.create().digestHex(body), message.getMsgId(), traceId,
                e.getMessage(), body);
            return ;
        }
        if (payResultBo == null) {
            log.error("【mafka消费】【退款回调】MQ消息转换对象失败,消息内容为:{}", body);
            return;
        }
        Integer businessType = payResultBo.getBusinessType();
        // 只消费订单的消息
        if (BusinessTypeEnum.ORDER.getType().equals(businessType)) {
            log.info("【mafka消费】【退款回调】MQ消息转换对象后的内容为:{}", JsonUtil.toJsonString(payResultBo));
            try {
                refundResultNotifyAssist.handleRefundResult(payResultBo);
            } catch (Exception e) {
                log.error("【mafka消费】【退款回调】处理失败", e);
                String traceId = message.getMsgId();
                mqErrorDataAssist.saveErrorData(MqErrorDataBizTypeEnum.REFUND_NOTIFY, payResultBo.getChannelRefundId(), message.getMsgId(),
                    traceId, e.getMessage(), body);
                throw new RuntimeException(e);
            }
        } else {
            log.info("【mafka消费】【退款回调】不是订单的消息，不消费.【body】:{}", body);
        }
    }
}
