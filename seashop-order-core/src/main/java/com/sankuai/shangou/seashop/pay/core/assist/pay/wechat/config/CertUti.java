package com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.config;

import java.io.ByteArrayInputStream;
import java.io.Closeable;
import java.io.InputStream;
import java.security.KeyStore;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;

/**
 * 从p12 证书中提取pem 证书
 *
 * <AUTHOR>
 * @date : 2023/8/10
 */
public class CertUti {

    public static X509Certificate getCertificate(byte[] p12Byte, String mchId) {
        InputStream inputStream = new ByteArrayInputStream(p12Byte);
        try {
            KeyStore keystore = KeyStore.getInstance("PKCS12");
            char[] partnerId2charArray = mchId.toCharArray();
            keystore.load(inputStream, partnerId2charArray);
            X509Certificate certificate = (X509Certificate) keystore.getCertificate("Tenpay Certificate");
            return certificate;
        } catch (Exception e) {
            throw new IllegalArgumentException("提取cert失败");
        } finally {
            close(inputStream);
        }
    }

    public static PrivateKey getPrivateKey(byte[] p12Byte, String mchId) {
        InputStream inputStream = new ByteArrayInputStream(p12Byte);
        try {
            KeyStore keystore = KeyStore.getInstance("PKCS12");
            char[] partnerId2charArray = mchId.toCharArray();
            keystore.load(inputStream, partnerId2charArray);
            return (PrivateKey) keystore.getKey("Tenpay Certificate", partnerId2charArray);
        } catch (Exception e) {
            throw new IllegalArgumentException("提取cert失败");
        } finally {
            close(inputStream);
        }
    }

    private static void close(Closeable closeable) {
        if (null != closeable) {
            try {
                closeable.close();
            } catch (Exception var2) {
            }
        }
    }

}
