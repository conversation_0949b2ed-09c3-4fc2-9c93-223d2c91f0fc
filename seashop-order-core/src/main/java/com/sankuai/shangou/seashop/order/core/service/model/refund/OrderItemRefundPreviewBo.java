package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderItemRefundPreviewBo extends RefundDetailBo {

    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品SKU ID
     */
    private String skuId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品主图
     */
    private String mainImagePath;
    /**
     * 购买数量
     */
    private Long quantity;
    /**
     * 商品总价，总的可退金额
     */
    private BigDecimal productAmount;
    /**
     * 剩余可退数量
     */
    private Long remainRefundQuantity;
    /**
     * 剩余可退金额
     */
    private BigDecimal remainRefundAmount;
    /**
     * 商品计量单位
     */
    private String measureUnit;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单状态描述
     */
    private String orderStatusDesc;

}
