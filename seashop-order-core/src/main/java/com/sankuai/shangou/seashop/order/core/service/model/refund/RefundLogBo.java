package com.sankuai.shangou.seashop.order.core.service.model.refund;

import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RefundLogBo {

    /**
     * 退款ID
     */
    private Long refundId;
    /**
     * 操作人
     */
    private String operator;
    /**
     * 操作时间
     */
    private Date operateDate;
    /**
     * 操作内容
     */
    private String operateContent;
    /**
     * 操作步骤
     */
    private Integer step;
    /**
     * 操作步骤中文
     */
    private String stepName;
    /**
     * 快递公司
     */
    private String expressCompanyName;

    /**
     * 快递公司编码
     */
    private String expressCompanyCode;

    /**
     * 快递单号
     */
    private String shipOrderNumber;

    /**
     * 内容
     */
    private String remark;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 状态翻译
     */
    private String statusName;

}
