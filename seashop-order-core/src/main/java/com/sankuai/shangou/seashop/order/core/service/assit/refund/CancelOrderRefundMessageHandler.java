package com.sankuai.shangou.seashop.order.core.service.assit.refund;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.RefundEventEnum;

/**
 * <AUTHOR>
 */
@Service
public class CancelOrderRefundMessageHandler implements RefundMessageHandler {


    @Override
    public RefundEventEnum getEvent() {
        return RefundEventEnum.CANCEL;
    }

    @Override
    public void handle(OrderRefundMessage message) {
        // TODO 推送ERP:        public static void PushRefundsToErp(List<long> refundIds)
    }
}
