package com.sankuai.shangou.seashop.order.core.statemachine;

/**
 * <AUTHOR>
 */
public enum OrderEvent {

    /**
     * 发起支付
     */
    INITIATE_PAY,
    /**
     * 支付回调
     */
    PAY_NOTIFY,
    /**
     * 取消支付
     */
    CANCEL_PAY,
    /**
     * 取消订单
     */
    CANCEL_ORDER,
    /**
     * 供应商发货
     */
    DELIVERY,
    /**
     * 商家收货
     */
    CONFIRM_RECEIVE,
    /**
     * 关闭订单
     */
    CLOSE,
    /**
     * 延迟收货
     */
    DELAY_RECEIVE,

    ;

}
