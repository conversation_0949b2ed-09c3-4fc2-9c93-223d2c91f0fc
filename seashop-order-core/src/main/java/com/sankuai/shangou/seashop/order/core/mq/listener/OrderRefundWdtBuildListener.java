package com.sankuai.shangou.seashop.order.core.mq.listener;

import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.erp.thrift.biz.request.wdt.WdtRightsOrderItemDto;
import com.sankuai.shangou.seashop.erp.thrift.biz.request.wdt.WdtRightsOrderReq;
import com.sankuai.shangou.seashop.erp.thrift.channel.ErpWdtOrderCmdThriftService;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundSearchService;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundService;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformRefundDetailBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderRefundReportDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.ShopErpQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopErpReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopErpResp;
import io.netty.util.internal.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/08/02 11:35
 */

@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_REFUND + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_REFUND_WDT_BUILD + "_${spring.profiles.active}")
public class OrderRefundWdtBuildListener implements RocketMQListener<MessageExt> {

    @Resource
    private OrderRefundService orderRefundService;

    @Resource
    private ShopErpQueryFeign shopErpQueryFeign;

    @Resource
    private ErpWdtOrderCmdThriftService erpWdtOrderCmdThriftService;

    @Resource
    private OrderItemRepository orderItemRepository;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【售后ES构建】消息内容为: {}", body);
        try {
            OrderRefundMessage refundMessage = JsonUtil.parseObject(body, OrderRefundMessage.class);
            PlatformRefundDetailBo platformRefundDetailBo = orderRefundService.platformQueryDetail(refundMessage.getRefundId());
            if (platformRefundDetailBo == null) {
                log.info("【mafka消费】【同步售后单旺店通erp】消息内容为: body: {}, 售后单不存在", body);
                return;
            }
            QueryShopErpReq req = new QueryShopErpReq();
            req.setShopId(platformRefundDetailBo.getShopId());
            QueryShopErpResp queryShopErpResp = ThriftResponseHelper.executeThriftCall(() -> shopErpQueryFeign.queryShopErp(req));
            if (queryShopErpResp == null) {
                log.info("【mafka消费】【同步售后单旺店通erp】消息内容为: body: {}, 找不到erp配置", body);
                return;
            }
            if (StringUtil.isNullOrEmpty(queryShopErpResp.getWdtAppKey())) {
                log.info("【mafka消费】【同步售后单旺店通erp】消息内容为: body: {}, 店铺没有配置erp", body);
                return;
            }
            WdtRightsOrderReq wdtRightsOrderReq = platformRefundDetailBo.getOrderItemId().equals(0L) ? buildWdtOrder2(platformRefundDetailBo, queryShopErpResp) : buildWdt(platformRefundDetailBo, queryShopErpResp);
            ThriftResponseHelper.executeThriftCall(() -> erpWdtOrderCmdThriftService.createRightsOrder(wdtRightsOrderReq));
        } catch (Exception e) {
            log.error("【mafka消费】【订单售后】处理失败", e);
            // 失败后，重试默认3次，监控发现问题，然后依赖定时任务补偿
            throw new RuntimeException(e);
        }

    }

    private WdtRightsOrderReq buildWdt(PlatformRefundDetailBo refundDetailBo, QueryShopErpResp shopErpResp) {

        WdtRightsOrderReq wdtRightsOrderReq = new WdtRightsOrderReq();
        wdtRightsOrderReq.setShopNo(shopErpResp.getWdtShopNo());
        wdtRightsOrderReq.setAppKey(shopErpResp.getWdtAppKey());
        wdtRightsOrderReq.setAppSecret(shopErpResp.getWdtAppSecret());
        wdtRightsOrderReq.setSid(shopErpResp.getWdtSid());
        wdtRightsOrderReq.setBaseUrl("https://sandbox.wangdian.cn/openapi2");
        wdtRightsOrderReq.setTid(refundDetailBo.getOrderId());
        wdtRightsOrderReq.setPlatformId("127");//固定 127为自建商城
        wdtRightsOrderReq.setRightsOrderNo(refundDetailBo.getSourceRefundId());
        wdtRightsOrderReq.setType(formatRightsType(RefundModeEnum.valueOf(refundDetailBo.getRefundMode())));
        wdtRightsOrderReq.setStatus(FormatRightsStatus(RefundStatusEnum.valueOf(refundDetailBo.getStatus())));
        wdtRightsOrderReq.setRefundFee(refundDetailBo.getRefundAmount());
        wdtRightsOrderReq.setBuyerNick(refundDetailBo.getContactUserName());
        wdtRightsOrderReq.setRefundTime(DateUtil.formatDateTime(refundDetailBo.getCreateTime()));
        wdtRightsOrderReq.setRefundReason(refundDetailBo.getRefundReasonDesc());
        wdtRightsOrderReq.setRefundRemark(refundDetailBo.getRefundRemark());
        wdtRightsOrderReq.setLogisticsNumber(refundDetailBo.getShipOrderNumber());
        wdtRightsOrderReq.setExpressName(refundDetailBo.getExpressCompanyName());
        List<WdtRightsOrderItemDto> orderItems = new ArrayList<>();
        WdtRightsOrderItemDto item = new WdtRightsOrderItemDto();
        item.setOid(refundDetailBo.getOrderItemId());
        item.setNum(refundDetailBo.getReturnQuantity());
        orderItems.add(item);
        wdtRightsOrderReq.setOrderItems(orderItems);
        return wdtRightsOrderReq;
    }

    /**
     * 整单退
     *
     * @param wdtRightsOrderReq
     * @param shopErpResp
     * @return
     */
    private WdtRightsOrderReq buildWdtOrder2(PlatformRefundDetailBo refundDetailBo, QueryShopErpResp shopErpResp) {
        WdtRightsOrderReq wdtRightsOrderReq = new WdtRightsOrderReq();
        wdtRightsOrderReq.setShopNo(shopErpResp.getWdtShopNo());
        wdtRightsOrderReq.setAppKey(shopErpResp.getWdtAppKey());
        wdtRightsOrderReq.setAppSecret(shopErpResp.getWdtAppSecret());
        wdtRightsOrderReq.setSid(shopErpResp.getWdtSid());
        wdtRightsOrderReq.setBaseUrl("https://sandbox.wangdian.cn/openapi2");
        wdtRightsOrderReq.setTid(refundDetailBo.getOrderId());
        wdtRightsOrderReq.setPlatformId("127");//固定 127为自建商城
        wdtRightsOrderReq.setRightsOrderNo(refundDetailBo.getSourceRefundId());
        wdtRightsOrderReq.setType(formatRightsType(RefundModeEnum.valueOf(refundDetailBo.getRefundMode())));
        wdtRightsOrderReq.setStatus(FormatRightsStatus(RefundStatusEnum.valueOf(refundDetailBo.getStatus())));
        wdtRightsOrderReq.setRefundFee(refundDetailBo.getRefundAmount());
        wdtRightsOrderReq.setBuyerNick(refundDetailBo.getContactUserName());
        wdtRightsOrderReq.setRefundTime(DateUtil.formatDateTime(refundDetailBo.getCreateTime()));
        wdtRightsOrderReq.setRefundReason(refundDetailBo.getRefundReasonDesc());
        wdtRightsOrderReq.setRefundRemark(refundDetailBo.getRefundRemark());
        wdtRightsOrderReq.setLogisticsNumber(refundDetailBo.getShipOrderNumber());
        wdtRightsOrderReq.setExpressName(refundDetailBo.getExpressCompanyName());

        List<WdtRightsOrderItemDto> rightsOrderItemDtos = new ArrayList<>();

        List<String> orderIds = new ArrayList<>();
        orderIds.add(refundDetailBo.getOrderId());
        List<OrderItem> orderItems = orderItemRepository.getByOrderIdList(orderIds);


        for (OrderItem orderItem : orderItems) {
            WdtRightsOrderItemDto wdtRightsOrderItem = new WdtRightsOrderItemDto();
            wdtRightsOrderItem.setOid(orderItem.getId());
            wdtRightsOrderItem.setNum(orderItem.getQuantity());
            rightsOrderItemDtos.add(wdtRightsOrderItem);
        }
        wdtRightsOrderReq.setOrderItems(rightsOrderItemDtos);
        return wdtRightsOrderReq;
    }


    private int formatRightsType(RefundModeEnum refundModeEnum) {

        int result = 0;
        switch (refundModeEnum) {
            case RETURN_AND_REFUND:
                result = 3;//退货退款
                break;
            case ORDER_REFUND:
                result = 1;//仅退款
                break;

            case GOODS_REFUND:
                result = 2;//退货
                break;
            default:
                break;
        }
        return result;
    }

    private String FormatRightsStatus(RefundStatusEnum status) {
        String result = "wait_seller_agree";
        switch (status) {
            case PLATFORM_REFUSE:
            case BUYER_CANCEL:
            case SUPPLIER_REFUSE:
                result = "closed";//退款关闭
                break;
            case WAIT_BUYER_SEND:
                result = "goods_returning";//待退货
                break;
            case WAIT_SUPPLIER_RECEIVE:
                result = "goods_receiving";//待收货
                break;
            case REFUND_SUCCESS:
            case REFUNDING:
                result = "success";//退款成功
                break;
            default:
                result = "wait_seller_agree";//申请退款
                break;
        }
        return result;
        //wait_seller_agree（申请退款），  seller_refuse（拒绝退款） ， closed（退款关闭） ，goods_returning（待退货） goods_receiving（待收货），success（退款成功）
    }
}
