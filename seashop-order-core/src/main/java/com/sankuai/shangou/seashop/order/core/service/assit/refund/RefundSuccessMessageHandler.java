package com.sankuai.shangou.seashop.order.core.service.assit.refund;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.enums.PlatformMessageTemplateEnum;
import com.sankuai.shangou.seashop.order.common.remote.MemberRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.user.MemberContactBo;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.core.service.assit.MessageNoticeHelper;
import com.sankuai.shangou.seashop.order.core.service.model.sms.UserNameMsgBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.RefundEventEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class RefundSuccessMessageHandler implements RefundMessageHandler {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private MemberRemoteService memberRemoteService;
    @Resource
    private MessageNoticeHelper messageNoticeHelper;

    @Override
    public RefundEventEnum getEvent() {
        return RefundEventEnum.REFUND_SUCCESS;
    }

    /**
     * <AUTHOR>
     * @param message
     * void
     */
    @Override
    public void handle(OrderRefundMessage message) {
        log.info("【售后-退款成功】退款记录: {}", JsonUtil.toJsonString(message));
        // 这里不需要走主库，后续用到的数据都是供应商审核就能确定的
        OrderRefund refund = orderRefundRepository.getById(message.getRefundId());
        if (refund == null) {
            log.error("【售后-退款成功】退款记录不存在, refundId: {}", message.getRefundId());
            return;
        }
        try {
            log.info("【售后-退款成功】处理发送短信/邮箱消息");
            MemberContactBo contact = memberRemoteService.getMemberContactByUserId(refund.getUserId());
            // 系统设置的间隔时间，就是订单取消的时间很长，基本不可能会存在用户正好又支付的情况，所以没考虑并发
            String siteName = messageNoticeHelper.getSiteName();
            // 构建消息体，邮箱和短信内容一样
            String msgBody = messageNoticeHelper.buildEmailBody(PlatformMessageTemplateEnum.MERCHANT_REFUND_SUCCESS, siteName,
                    contact.getUserName());
            UserNameMsgBo msgBo = new UserNameMsgBo(contact.getUserName());
            // 发送消息
            messageNoticeHelper.noticeForTemplate(contact, PlatformMessageTemplateEnum.MERCHANT_REFUND_SUCCESS, msgBo, msgBody);
        } catch (Exception e) {
            log.error("【售后-退款成功】发送短信/邮箱消息异常", e);
        }
    }
}
