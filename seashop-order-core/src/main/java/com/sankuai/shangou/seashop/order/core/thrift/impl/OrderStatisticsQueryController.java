package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import com.sankuai.shangou.seashop.order.core.service.OrderStatsService;
import com.sankuai.shangou.seashop.order.core.service.model.stats.PlatformStatsTradeDataBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.SellerIndexTradeStatsBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.StatsShopTopNSaleProductParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.StatsUserPurchaseSkuParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.TopProductSaleStatsBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.UserPurchaseSkuStatsBo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderStatisticsQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.StatsUserPurchaseSkuReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.stats.StatsShopTopNSaleProductReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.PlatformIndexTradeDataStatsResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.SellerIndexTradeStatsResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.StatsUserPurchaseSkuResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.stats.TopProductSaleStatsResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/orderStatistics")
public class OrderStatisticsQueryController implements OrderStatisticsQueryFeign {

    @Resource
    private EsOrderService esOrderService;
    @Resource
    private OrderStatsService orderStatsService;

    @PostMapping(value = "/statsUserPurchaseSku", consumes = "application/json")
    @Override
    public ResultDto<StatsUserPurchaseSkuResp> statsUserPurchaseSku(@RequestBody StatsUserPurchaseSkuReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单统计】供应商采购统计", req, func -> {
            StatsUserPurchaseSkuParamBo paramBo = JsonUtil.copy(req, StatsUserPurchaseSkuParamBo.class);
            UserPurchaseSkuStatsBo dataBo = esOrderService.pageUserPurchaseSku(paramBo);
            return JsonUtil.copy(dataBo, StatsUserPurchaseSkuResp.class);
        });
    }

    @GetMapping(value = "/statsPlatformIndexTradeData")
    @Override
    public ResultDto<PlatformIndexTradeDataStatsResp> statsPlatformIndexTradeData() throws TException {
        return ThriftResponseHelper.responseInvoke("【订单统计】平台首页交易数据统计", null, func -> {
            PlatformStatsTradeDataBo dataBo = orderStatsService.statsPlatformIndexTradeData();
            return JsonUtil.copy(dataBo, PlatformIndexTradeDataStatsResp.class);
        });
    }

    @GetMapping(value = "/statsSellerIndexTradeData")
    @Override
    public ResultDto<SellerIndexTradeStatsResp> statsSellerIndexTradeData(@RequestParam Long shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单统计】供应商首页交易数据统计", shopId, func -> {
            SellerIndexTradeStatsBo dataBo = orderStatsService.statsSellerIndexTradeData(shopId);
            return JsonUtil.copy(dataBo, SellerIndexTradeStatsResp.class);
        });
    }

    @PostMapping(value = "/statsTopNSaleProduct", consumes = "application/json")
    @Override
    public ResultDto<TopProductSaleStatsResp> statsTopNSaleProduct(@RequestBody StatsShopTopNSaleProductReq req) throws TException {
        return ThriftResponseHelper.responseInvoke("【订单统计】供应商首页销售排行前N的商品统计", req, func -> {
            StatsShopTopNSaleProductParamBo paramBo = JsonUtil.copy(req, StatsShopTopNSaleProductParamBo.class);
            TopProductSaleStatsBo dataBo = orderStatsService.statsTopNSaleProduct(paramBo);
            return JsonUtil.copy(dataBo, TopProductSaleStatsResp.class);
        });
    }
}
