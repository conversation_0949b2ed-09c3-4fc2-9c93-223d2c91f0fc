package com.sankuai.shangou.seashop.order.core.service.assit;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.DateUtil;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.order.common.constant.LeafConst;

/**
 * 业务单号生成器
 * <p>Notice: 使用leaf生成的，需要申请key</p>
 *
 * <AUTHOR>
 */
@Service
public class BizNoGenerator {

    @Resource
    private LeafService leafService;

    /**
     * 批量生成订单号
     *
     * @param count 需要生成的订单号数量
     * <AUTHOR>
     */
    public List<String> generateOrderNo(int count) {
        List<Long> ids = leafService.batchGenerateNo(LeafConst.KEY_ORDER_NO, count);
        return ids.stream()
                .map(this::fillIdAndAppendTime)
                .collect(Collectors.toList());
    }

    /**
     * 使用雪花算法生成支付单号
     *
     * <AUTHOR>
     */
    public String generatePayNo() {
        Long id = leafService.generateNoBySnowFlake(LeafConst.KEY_PAY_NO);
        return String.valueOf(id);
    }

    /**
     * 使用雪花算法生成商品评价id
     *
     * @return 商品评价id
     */
    public List<Long> generateProductCommentId(int count) {
        // 按照100 分组 分批生成
        int groupSize = 100;
        int groupCount = count / groupSize;
        int remainCount = count % groupSize;
        List<Long> ids = new ArrayList<>();
        for (int i = 0; i < groupCount; i++) {
            ids.addAll(leafService.batchGenerateNoBySnowFlake(LeafConst.KEY_PRODUCT_COMMENT_ID, groupSize));
        }
        if (remainCount > 0) {
            ids.addAll(leafService.batchGenerateNoBySnowFlake(LeafConst.KEY_PRODUCT_COMMENT_ID, remainCount));
        }
        return ids;
    }

    /**
     * 使用雪花算法生成消息唯一id
     *
     * @return 消息唯一id
     */
    public Long generateMessageUniqueId() {
        return leafService.generateNoBySnowFlake(LeafConst.KEY_MESSAGE_REQUEST_ID);
    }

    /**
     * 使用雪花算法生成消息唯一id
     *
     * @return 消息唯一id
     */
    public List<Long> generateMessageUniqueIdBatch(int size) {
        return leafService.batchGenerateNoBySnowFlake(LeafConst.KEY_MESSAGE_REQUEST_ID, size);
    }

    public Long generateRefundId() {
        return leafService.generateNoBySnowFlake(LeafConst.KEY_ORDER_REFUND_ID);
    }

    /**
     * 使用雪花算法生成退款批次号
     *
     * <AUTHOR>
     */
    public String generateRefundBatchNo() {
        Long id = leafService.generateNoBySnowFlake(LeafConst.KEY_ORDER_REFUND_PAY_BATCH_NO);
        return String.valueOf(id);
    }

    public String generateRefundRecordNo() {
        Long id = leafService.generateNoBySnowFlake(LeafConst.KEY_ORDER_REFUND_RECORD_NO);
        return String.valueOf(id);
    }


    //********************************************************

    private String fillIdAndAppendTime(Long id) {
        String hourTime = DateUtil.formatHour(new Date());
        String padIdStr = StringUtils.leftPad(id.toString(), 6, "0");
        return hourTime + padIdStr;
    }

}
