package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import lombok.Builder;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description: 退款请求对象（未分账的支付订单）
 */
@Data
@Builder
public class AdaPayReverseCreateDto {

    private String appId;

    /**
     * Adapay生成的支付对象id
     * (支付时，汇付返回的id)
     */
    private String paymentId;

    /**
     * 退款金额
     */
    private String reverseAmt;

    /**
     * 传给汇付的退款订单号
     */
    private String orderNo;

    /**
     * 回调地址
     */
    private String notifyUrl;
}
