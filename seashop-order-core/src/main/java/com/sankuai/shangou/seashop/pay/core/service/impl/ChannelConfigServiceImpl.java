package com.sankuai.shangou.seashop.pay.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.pay.core.bo.ChannelConfigBo;
import com.sankuai.shangou.seashop.pay.core.bo.ChannelConfigQueryBo;
import com.sankuai.shangou.seashop.pay.core.bo.ChannelConfigSaveBo;
import com.sankuai.shangou.seashop.pay.core.config.AdaPayInitWithMerConfig;
import com.sankuai.shangou.seashop.pay.core.service.ChannelConfigService;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ChannelConfig;
import com.sankuai.shangou.seashop.pay.dao.core.model.AdaPayConfigModel;
import com.sankuai.shangou.seashop.pay.dao.core.model.ChannelConfigParamModel;
import com.sankuai.shangou.seashop.pay.dao.core.repository.ChannelConfigRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayResultCodeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.service.ChannelConfigLogResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@Service
@Slf4j
public class ChannelConfigServiceImpl implements ChannelConfigService {

    @Resource
    private ChannelConfigRepository channelConfigRepository;
    @Resource
    private AdaPayInitWithMerConfig adaPayInitWithMerConfig;
    @Resource
    private BaseLogAssist baseLogAssist;

    private ChannelConfigLogResp dealOldNewObject(List<ChannelConfigBo> newChannelConfigs, List<ChannelConfig> oldChannelConfigs) {
        ChannelConfigLogResp result = new ChannelConfigLogResp();
        Map<String, String> channelConfigBoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(newChannelConfigs)) {
            channelConfigBoMap = newChannelConfigs.stream().collect(Collectors.toMap(ChannelConfigBo::getConfigKey, ChannelConfigBo::getConfigValue, (o1, o2) -> o2));
        } else if (!CollectionUtils.isEmpty(oldChannelConfigs)) {
            channelConfigBoMap = oldChannelConfigs.stream().collect(Collectors.toMap(ChannelConfig::getConfigKey, ChannelConfig::getConfigValue, (o1, o2) -> o2));
        }
        result.setAppId(channelConfigBoMap.get("appId"));
        result.setApiKey(channelConfigBoMap.get("apiKey"));
        result.setApiMockKey(channelConfigBoMap.get("apiMockKey"));
        result.setRsaPrivateKey(channelConfigBoMap.get("rsaPrivateKey"));
        result.setRsaProductKey(channelConfigBoMap.get("rsaProductKey"));
        return result;
    }

    @Override
    public void update(ChannelConfigSaveBo saveBo) {

        List<String> keyList = saveBo.getConfigList().stream().map(ChannelConfigBo::getConfigKey).collect(Collectors.toList());
        List<ChannelConfig> channelConfigs = channelConfigRepository.queryByChannelAndKey(ChannelConfigParamModel.builder().paymentChannel(saveBo.getPaymentChannel()).configKeyList(keyList).build());
        if (CollUtil.isEmpty(channelConfigs) || channelConfigs.size() != saveBo.getConfigList().size()) {
            throw new BusinessException(PayResultCodeEnum.PARAM_ERROR.getCode(), PayResultCodeEnum.PARAM_ERROR.getMsg());
        }

        channelConfigs.parallelStream().forEach(channelConfig -> {
            ChannelConfigBo configBo = saveBo.getConfigList().stream().filter(config -> config.getConfigKey().equals(channelConfig.getConfigKey()) && saveBo.getPaymentChannel().equals(channelConfig.getPaymentChannel())).findFirst().get();
            channelConfig.setConfigValue(configBo.getConfigValue());
        });
        // 手动写日志
        List<ChannelConfigBo> newChannelConfigs = saveBo.getConfigList();
        List<String> configKeyList = saveBo.getConfigList().stream().map(ChannelConfigBo::getConfigKey).collect(Collectors.toList());
        List<ChannelConfig> oldChannelConfigs = channelConfigRepository.queryByChannelAndKey(ChannelConfigParamModel.builder().paymentChannel(saveBo.getPaymentChannel()).configKeyList(configKeyList).build());


        ChannelConfigLogResp oldValue = this.dealOldNewObject(null, oldChannelConfigs);
        ChannelConfigLogResp newValue = this.dealOldNewObject(newChannelConfigs, null);
        baseLogAssist.recordLog(ExaminModelEnum.SYSTEM_SETTING, ExaProEnum.MODIFY, "支付方式", saveBo.getOperationUserId(), saveBo.getOperationShopId(), oldValue, newValue);
        channelConfigRepository.updateBatchById(channelConfigs);

        if (PaymentChannelEnum.ADAPAY.getCode().equals(saveBo.getPaymentChannel())) {
            try {
                adaPayInitWithMerConfig.initAdaPayConfig();
            } catch (Exception e) {
                log.error("【汇付支付】更新配置后，初始化汇付支付配置失败", e);
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public List<ChannelConfig> queryList(ChannelConfigQueryBo queryBo) {

        List<ChannelConfig> channelConfigs = channelConfigRepository.queryByChannelAndKey(ChannelConfigParamModel.builder().paymentChannel(queryBo.getPaymentChannel()).configKeyList(queryBo.getConfigKeyList()).configKey(queryBo.getConfigKey()).build());

        return channelConfigs;
    }

    @Override
    public AdaPayConfigModel getAdaPayConfigModel() {

        List<ChannelConfig> channelConfigModels = this.queryList(ChannelConfigQueryBo.builder().paymentChannel(PaymentChannelEnum.ADAPAY.getCode()).build());
        AdaPayConfigModel adaPayConfigModel = new AdaPayConfigModel();
        this.assignConfigValues(channelConfigModels, adaPayConfigModel);

        return adaPayConfigModel;
    }

    private void assignConfigValues(List<ChannelConfig> configList, AdaPayConfigModel adaPayConfigModel) {
        configList.forEach(config -> {
            String configKey = config.getConfigKey();
            String configValue = config.getConfigValue();
            switch (configKey) {
                case AdaPayConfigModel.IZ_DEBUG:
                    adaPayConfigModel.setIzDebug(Boolean.valueOf(configValue));
                    break;
                case AdaPayConfigModel.PROD_MODE:
                    adaPayConfigModel.setProdMode(Boolean.valueOf(configValue));
                    break;
                case AdaPayConfigModel.APP_ID:
                    adaPayConfigModel.setAppId(configValue);
                    break;
                case AdaPayConfigModel.API_KEY:
                    adaPayConfigModel.setApiKey(configValue);
                    break;
                case AdaPayConfigModel.API_MOCK_KEY:
                    adaPayConfigModel.setApiMockKey(configValue);
                    break;
                case AdaPayConfigModel.DEVICE_ID:
                    adaPayConfigModel.setDeviceId(configValue);
                    break;
                case AdaPayConfigModel.RSA_PRODUCT_KEY:
                    adaPayConfigModel.setRsaProductKey(configValue);
                    break;
                case AdaPayConfigModel.RSA_PRIVATE_KEY:
                    adaPayConfigModel.setRsaPrivateKey(configValue);
                    break;
                case AdaPayConfigModel.IZ_OPEN:
                    adaPayConfigModel.setIzOpen(Boolean.valueOf(configValue));
                    break;
                default:
                    break;
            }
        });
    }
}
