package com.sankuai.shangou.seashop.order.core.service.model.stats;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 商家订单sku采购统计返回对象
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class UserPurchaseSkuStatsBo {

    private UserPurchaseSkuStatsSummaryBo summary;
    private BasePageResp<UserPurchaseSkuBo> pageData;

    public static UserPurchaseSkuStatsBo defaultEmpty(BasePageResp<UserPurchaseSkuBo> pageData) {
        UserPurchaseSkuStatsBo statsBo = new UserPurchaseSkuStatsBo();
        statsBo.setSummary(UserPurchaseSkuStatsSummaryBo.defaultZero());
        statsBo.setPageData(pageData);
        return statsBo;
    }

}
