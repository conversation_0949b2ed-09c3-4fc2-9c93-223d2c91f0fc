package com.sankuai.shangou.seashop.order.core.statemachine.action;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ActionResult {

    private boolean success;
    /**
     * 用于更细粒度的控制
     */
    private int errCode;
    private String message;

    public static ActionResult success() {
        ActionResult result = new ActionResult();
        result.setSuccess(true);
        return result;
    }

    public static ActionResult fail(int errCode) {
        return fail(errCode, null);
    }

    public static ActionResult fail(int errCode, String message) {
        ActionResult result = new ActionResult();
        result.setSuccess(false);
        result.setErrCode(errCode);
        result.setMessage(message);
        return result;
    }

}
