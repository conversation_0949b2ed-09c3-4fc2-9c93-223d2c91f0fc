package com.sankuai.shangou.seashop.order.core.mq.model.order;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class RemoveShoppingCartMessage {

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 当前下单成功，需要删除的skuId
     */
    private List<String> skuIdList;

}
