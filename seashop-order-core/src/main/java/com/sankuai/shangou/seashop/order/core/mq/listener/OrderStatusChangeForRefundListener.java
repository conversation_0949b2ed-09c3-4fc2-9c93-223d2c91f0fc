package com.sankuai.shangou.seashop.order.core.mq.listener;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundSearchService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 消费订单状态变更的消息，修改售后ES中的订单状态
 * <AUTHOR>
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_ORDER_CHANGE,
//        group = MafkaConst.GROUP_ORDER_CHANGE_FOR_REFUND)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_CHANGE + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_CHANGE_FOR_REFUND + "_${spring.profiles.active}")
public class OrderStatusChangeForRefundListener implements RocketMQListener<MessageExt> {

    @Resource
    private OrderRefundSearchService orderRefundSearchService;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
//        String body = (String) message.getBody();
//        log.info("【mafka消费】【订单状态变更-售后ES修改】消息内容为: {}", body);
//        OrderMessage orderMessage = JsonUtil.parseObject(body, OrderMessage.class);
//        // es构建
//        orderRefundSearchService.updateOrderStatus(orderMessage.getOrderId());
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【订单状态变更-售后ES修改】【OrderStatusChangeForRefundListener】消息内容为: {}", body);
        OrderMessage orderMessage = JsonUtil.parseObject(body, OrderMessage.class);
        // es构建
        try {
            orderRefundSearchService.updateOrderStatus(orderMessage.getOrderId());
        } catch (Exception e) {
            log.error("【mafka消费】【订单状态变更-售后ES修改】处理失败", e);
            throw new RuntimeException(e);
        }
    }
}
