package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.order.thrift.core.request.PlatformApproveBatchReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.PlatformApproveBatchResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TResultCode;
import com.sankuai.shangou.seashop.order.common.config.SystemSwitchProps;
import com.sankuai.shangou.seashop.order.common.enums.SwitchBizTypeEnum;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundService;
import com.sankuai.shangou.seashop.order.core.service.model.refund.ApplyRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.CancelOrderRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.CreateOrderRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformApproveBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.ReapplyRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerApproveParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerConfirmReceiveParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserDeliverParamBo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.CreateOrderRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ApplyOrderItemRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ApplyOrderRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.CancelOrderRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.PlatformApproveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ReapplyRefundReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerApproveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SellerConfirmReceiveReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.SendRefundGoodsReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.UserDeliverReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderRefundResp;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/orderRefundCmd")
public class OrderRefundCmdController implements OrderRefundCmdFeign {

    @Resource
    private OrderRefundService orderRefundService;
    @Resource
    private SystemSwitchProps systemSwitchProps;

    @PostMapping(value = "/applyRefund", consumes = "application/json")
    @Override
    public ResultDto<Long> applyRefund(@RequestBody ApplyOrderRefundReq applyReq) throws TException {
        log.info("【售后】待发货状态下，申请订单售后, 请求参数={}", JsonUtil.toJsonString(applyReq));
        return ThriftResponseHelper.responseInvoke("applyRefund", applyReq, func -> {
            applyReq.checkParameter();
            ApplyRefundBo applyBo = JsonUtil.copy(applyReq, ApplyRefundBo.class);
            // 业务逻辑处理
            return orderRefundService.applyOrderRefund(applyBo);
        });
    }

    @PostMapping(value = "/applyWholeOrderRefund", consumes = "application/json")
    @Override
    public ResultDto<Long> applyWholeOrderRefund(@RequestBody ApplyOrderRefundReq applyReq) throws TException {
        log.info("【售后】待收货/已完成状态下，申请整单退款退货, 请求参数={}", JsonUtil.toJsonString(applyReq));
        return ThriftResponseHelper.responseInvoke("applyWholeOrderRefund", applyReq, func -> {
            applyReq.checkParameter();
            ApplyRefundBo applyBo = JsonUtil.copy(applyReq, ApplyRefundBo.class);
            // 业务逻辑处理
            return orderRefundService.applyWholeOrderRefund(applyBo);
        });
    }

    @PostMapping(value = "/reapplyOrderRefund", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> reapplyOrderRefund(@RequestBody ReapplyRefundReq applyReq) throws TException {
        log.info("【售后】重新申请售后, 请求参数={}", JsonUtil.toJsonString(applyReq));
        return ThriftResponseHelper.responseInvoke("reapplyOrderRefund", applyReq, func -> {
            applyReq.checkParameter();
            ReapplyRefundBo applyBo = JsonUtil.copy(applyReq, ReapplyRefundBo.class);
            // 业务逻辑处理
            orderRefundService.reapplyRefund(applyBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/applyItemRefund", consumes = "application/json")
    @Override
    public ResultDto<Long> applyItemRefund(@RequestBody ApplyOrderItemRefundReq applyReq) throws TException {
        log.info("【售后】订单明细申请退货/退款, 请求参数={}", JsonUtil.toJsonString(applyReq));
        return ThriftResponseHelper.responseInvoke("applyItemRefund", applyReq, func -> {
            applyReq.checkParameter();
            ApplyRefundBo applyBo = JsonUtil.copy(applyReq, ApplyRefundBo.class);
            // 业务逻辑处理
            return orderRefundService.applyOrderItemRefund(applyBo);
        });
    }

    @PostMapping(value = "/cancelRefund", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> cancelRefund(@RequestBody CancelOrderRefundReq cancelReq) throws TException {
        log.info("【售后】取消售后, 请求参数={}", JsonUtil.toJsonString(cancelReq));
        return ThriftResponseHelper.responseInvoke("applyRefund", cancelReq, func -> {
            cancelReq.checkParameter();
            CancelOrderRefundBo cancelOrderRefundBo = JsonUtil.copy(cancelReq, CancelOrderRefundBo.class);
            // 业务逻辑处理
            orderRefundService.cancelOrderRefund(cancelOrderRefundBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/userDeliver", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> userDeliver(@RequestBody UserDeliverReq req) throws TException {
        log.info("【售后】买家寄货, 请求参数={}", JsonUtil.toJsonString(req));
        return ThriftResponseHelper.responseInvoke("userDeliver", req, func -> {
            req.checkParameter();
            UserDeliverParamBo paramBo = JsonUtil.copy(req, UserDeliverParamBo.class);
            // 业务逻辑处理
            orderRefundService.userDeliver(paramBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/sendRefundGoods", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> sendRefundGoods(@RequestBody SendRefundGoodsReq req) throws TException {
        log.info("【售后】退货寄回, 请求参数={}", req);
        return ThriftResponseHelper.responseInvoke("sendRefundGoods", req, func -> {
            req.checkParameter();
            UserDeliverParamBo paramBo = JsonUtil.copy(req, UserDeliverParamBo.class);
            // 业务逻辑处理
            orderRefundService.userDeliver(paramBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/sellerApprove", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> sellerApprove(@RequestBody SellerApproveReq req) throws TException {
        log.info("【售后】供应商审核, 请求参数={}", JsonUtil.toJsonString(req));
        return ThriftResponseHelper.responseInvoke("sellerApprove", req, func -> {
            req.checkParameter();
            SellerApproveParamBo paramBo = JsonUtil.copy(req, SellerApproveParamBo.class);
            // 业务逻辑处理
            orderRefundService.sellerApprove(paramBo);
            return BaseResp.of();
        });
    }

    /**
     * 供应商确认收货审核，仅确认收货逻辑，拒绝调用审核接口
     * 虽然也是审核相关，但是会涉及库存回退等，所以单独接口
     *
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/sellerConfirmReceive", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> sellerConfirmReceive(@RequestBody SellerConfirmReceiveReq req) throws TException {
        log.info("【售后】供应商审核, 请求参数={}", JsonUtil.toJsonString(req));
        return ThriftResponseHelper.responseInvoke("sellerApprove", req, func -> {
            req.checkParameter();
            SellerConfirmReceiveParamBo paramBo = JsonUtil.copy(req, SellerConfirmReceiveParamBo.class);
            // 业务逻辑处理
            orderRefundService.sellerConfirmReceive(paramBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/platformConfirm", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> platformConfirm(@RequestBody PlatformApproveReq req) {
        log.info("【售后】平台审核通过, 请求参数={}", JsonUtil.toJsonString(req));
        SystemSwitchProps.SwitchContent switchContent = systemSwitchProps.getSwitch(SwitchBizTypeEnum.ALLOW_REFUND_PLATFORM_CONFIRM.getKey());
        if (switchContent != null && Boolean.FALSE.equals(switchContent.getAllowFlag())) {
            log.info("【售后】平台审核通过，系统当前不允许平台确认退款");
            ResultDto<BaseResp> result = new ResultDto<>();
            String msg = StrUtil.isBlank(switchContent.getForbiddenMessage()) ? SwitchBizTypeEnum.ALLOW_REFUND_PLATFORM_CONFIRM.getDefaultMessage() : switchContent.getForbiddenMessage();
            result.fail(TResultCode.SERVER_ERROR.value(), msg);
            return result;
        }
        return ThriftResponseHelper.responseInvoke("platformConfirm", req, func -> {
            req.checkParameter();
            PlatformApproveBo paramBo = JsonUtil.copy(req, PlatformApproveBo.class);
            // 业务逻辑处理
            orderRefundService.platformConfirm(paramBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/platformReject", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> platformReject(@RequestBody PlatformApproveReq req) {
        log.info("【售后】平台驳回, 请求参数={}", JsonUtil.toJsonString(req));
        return ThriftResponseHelper.responseInvoke("platformReject", req, func -> {
            req.checkParameter();
            PlatformApproveBo paramBo = JsonUtil.copy(req, PlatformApproveBo.class);
            // 业务逻辑处理
            orderRefundService.platformReject(paramBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/platformBatchConfirm", consumes = "application/json")
    @Override
    public ResultDto<PlatformApproveBatchResp> platformBatchConfirm(@RequestBody PlatformApproveBatchReq batchReq) {
        SystemSwitchProps.SwitchContent switchContent = systemSwitchProps.getSwitch(SwitchBizTypeEnum.ALLOW_REFUND_PLATFORM_CONFIRM.getKey());
        if (switchContent != null && Boolean.FALSE.equals(switchContent.getAllowFlag())) {
            log.info("【售后】平台审核通过，系统当前不允许平台确认退款");
            ResultDto<PlatformApproveBatchResp> result = new ResultDto<>();
            String msg = StrUtil.isBlank(switchContent.getForbiddenMessage()) ? SwitchBizTypeEnum.ALLOW_REFUND_PLATFORM_CONFIRM.getDefaultMessage() : switchContent.getForbiddenMessage();
            result.fail(TResultCode.SERVER_ERROR.value(), msg);
            return result;
        }
        return ThriftResponseHelper.responseInvoke("【售后】平台批量审核通过", batchReq, func -> {
            batchReq.checkParameter();
            // 业务逻辑处理
            return orderRefundService.platformBatchConfirm(batchReq);
        });
    }

    @PostMapping(value = "/createOrderRefund", consumes = "application/json")
    @Override
    public ResultDto<CreateOrderRefundResp> createOrderRefund(@RequestBody CreateOrderRefundReq req) throws TException {
        log.info("【售后】创建售后单, 请求参数={}", req);
        return ThriftResponseHelper.responseInvoke("createOrderRefund", req, func -> {
            req.checkParameter();
            CreateOrderRefundBo refundBo = JsonUtil.copy(req, CreateOrderRefundBo.class);
            // 业务逻辑处理
            return orderRefundService.createOrderRefund(refundBo);
        });
    }
}
