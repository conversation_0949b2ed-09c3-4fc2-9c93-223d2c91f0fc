package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description: 退款结果返回对象（未分账的支付订单）
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class AdaPayReverseCreateResultDto {

    /**
     * Adapay生成的支付撤销对象id
     */
    private String id;

    /**
     * 撤销金额，必须大于0，保留两位小数点，如0.10、100.05等
     */
    private String reverseAmt;
}
