package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.order.core.service.model.pay.InitiatePayBo;
import com.sankuai.shangou.seashop.order.core.service.model.pay.PcOrderPayInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.pay.QueryOrderPayBo;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderUserReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPayChannelReq;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderPayService {

    /**
     * 商家PC获取订单支付方式
     * <AUTHOR>
     * @param queryOrderPay
     *
     */
    PcOrderPayInfoBo queryPcOrderPayInfo(QueryOrderPayBo queryOrderPay);

    /**
     * 发起支付。支付参数不同的支付方式和渠道会不同，统一返回json字符串，前端处理
     * @param initiatePayBo
     * @return
     */
    String initiatePayment(InitiatePayBo initiatePayBo);

    /**
     * 确认支付结果
     * @param queryReq 请求参数
     */
    Boolean confirmPayResult(QueryPayChannelReq queryReq);

    List<Long> queryPayOrderUserId(QueryOrderUserReq queryReq);

}
