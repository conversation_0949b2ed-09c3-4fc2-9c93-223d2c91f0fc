package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 订单明细当前有效的售后信息，包括金额和数量
 * <AUTHOR>
 */
@Getter
@Setter
public class ItemRemainRefundInfoBo {

    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 订单明细ID
     */
    private Long orderItemId;
    /**
     * 剩余可退数量
     */
    private Long remainQuantity;
    /**
     * 剩余可退金额
     */
    private BigDecimal remainAmount;

}
