package com.sankuai.shangou.seashop.order.core.task;

import com.hishop.himall.report.api.request.*;
import com.hishop.himall.report.api.service.ReportFeign;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.JobQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.JobLogInfoResp;
import com.sankuai.shangou.seashop.order.common.remote.PayRemoteService;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundRecordService;
import com.sankuai.shangou.seashop.order.core.service.OrderTaskService;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.RefundResultNotifyAssist;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefundRecord;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderWayBill;
import com.sankuai.shangou.seashop.order.dao.core.repository.*;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderItemReportDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderRefundReportDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.OrderRefundRecordStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ReverseOrderQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.ReverseOrderResp;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2024/1/2/002
 * @description:
 */
@Slf4j
@Component
public class OrderCraneTask {

    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private PayRemoteService payRemoteService;
    @Resource
    private OrderRefundRecordRepository orderRefundRecordRepository;
    @Resource
    private OrderRefundRecordService orderRefundRecordService;
    @Resource
    private OrderTaskService orderTaskService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private RefundResultNotifyAssist refundResultNotifyAssist;
    @Resource
    private ReportFeign reportFeign;

    @Resource
    private JobQueryFeign jobQueryFeign;

    @Resource
    private OrderItemRepository orderItemRepository;

    @Resource
    private OrderWayBillRepository orderWayBillRepository;

    /**
     * 订单退款状态查询
     */
//    @Crane("OrderRefundStatusTask")
    @XxlJob("orderRefundStatusTask")
    @XxlRegister(cron = "0 0/10 * * * ?",
            author = "snow",
            jobDesc = "订单退款状态查询")
    public void orderRefundStatusTask() {
        log.info("【订单退款状态查询】定时任务...start...");
        List<OrderRefund> orderRefunds = orderRefundRepository.queryRefundingList(RefundStatusEnum.REFUNDING.getCode());
        log.info("【订单退款状态查询】定时任务...orderRefunds={}", orderRefunds);
        if (orderRefunds.isEmpty()) {
            log.info("【订单退款状态查询】定时任务...没有需要查询的订单...end...");
            return;
        }
        for (OrderRefund orderRefund : orderRefunds) {
            log.info("【订单退款状态查询】定时任务...orderRefund={}", orderRefund);
            try {
                ReverseOrderResp reverseOrderResp = payRemoteService.queryReverseOrderOne(
                        ReverseOrderQueryReq.builder()
                                .channelRefundId(orderRefund.getRefundBatchNo()).build()
                );
                if (reverseOrderResp == null) {
                    log.info("【订单退款状态查询】定时任务...没有查询到退款单...orderRefund={}", orderRefund);
                    continue;
                }
                log.info("【订单退款状态查询】定时任务...reverseOrderResp={}", reverseOrderResp);
                if (ReverseStateEnums.REVERSE_SUCCESS.getStatus().equals(reverseOrderResp.getReverseState())) {
                    // 如果成功了
                    Order order = orderRepository.getByOrderId(orderRefund.getOrderId());
                    refundResultNotifyAssist.handleOrderRefundSuccess(order, orderRefund);
                } else if (ReverseStateEnums.REVERSE_ERROR.getStatus().equals(reverseOrderResp.getReverseState())) {
                    // 理论上不会有这种数据，如果出现，需要人工干预
                    log.error("【订单退款状态查询】定时任务...退款失败...orderRefund={}", orderRefund);
                }
            } catch (Exception e) {
                log.error("【订单退款状态查询】定时任务...异常...orderRefund={}", orderRefund, e);
            }
        }
    }

    /**
     * 订单退款记录状态查询（超支退款）
     */
//    @Crane("OrderRefundRecordStatusTask")
    @XxlJob("orderRefundRecordStatusTask")
    @XxlRegister(cron = "0 0/10 * * * ?",
            author = "snow",
            jobDesc = "订单退款记录状态查询（超支退款）")
    public void orderRefundRecordStatusTask() {
        log.info("【订单退款记录状态查询】定时任务...start...");
        List<OrderRefundRecord> orderRefundRecords = orderRefundRecordRepository.listByStatus(RefundPayStatusEnum.UN_PAY.getCode());
        log.info("【订单退款记录状态查询】定时任务...orderRefundRecords={}", orderRefundRecords);
        if (orderRefundRecords.isEmpty()) {
            log.info("【订单退款记录状态查询】定时任务...没有需要查询的订单...end...");
            return;
        }
        for (OrderRefundRecord orderRefundRecord : orderRefundRecords) {
            log.info("【订单退款记录状态查询】定时任务...orderRefundRecord={}", orderRefundRecord);
            try {
                ReverseOrderResp reverseOrderResp = payRemoteService.queryReverseOrderOne(
                        ReverseOrderQueryReq.builder()
                                .reverseId(orderRefundRecord.getRefundNo()).build()
                );
                if (reverseOrderResp == null) {
                    log.info("【订单退款记录状态查询】定时任务...没有查询到退款单...orderRefundRecord={}", orderRefundRecord);
                    continue;
                }
                log.info("【订单退款记录状态查询】定时任务...reverseOrderResp={}", reverseOrderResp);
                // 处理退款记录状态
                if (ReverseStateEnums.REVERSE_SUCCESS.getStatus().equals(reverseOrderResp.getReverseState())) {
                    // 如果成功了
                    orderRefundRecord.setRefundStatus(OrderRefundRecordStatusEnum.REFUND_SUCCESS.getCode());
                } else if (ReverseStateEnums.REVERSE_ERROR.getStatus().equals(reverseOrderResp.getReverseState())) {
                    // 如果失败了
                    orderRefundRecord.setRefundStatus(OrderRefundRecordStatusEnum.REFUND_FAIL.getCode());
                    orderRefundRecord.setErrMsg(reverseOrderResp.getChannelRefundMsg());
                } else {
                    continue;
                }
                orderRefundRecordService.updateObj(orderRefundRecord);
            } catch (Exception e) {
                log.error("【订单退款记录状态查询】定时任务...异常...orderRefundRecord={}", orderRefundRecord, e);
            }
        }
    }


    /**
     * 供应商待发货提醒
     * 每天上午10点：0 0 10 * * ?
     */
//    @Crane("RemindWaitDeliveryTask")
    @XxlJob("remindWaitDeliveryTask")
    @XxlRegister(cron = "0 0 10 * * ?",
            author = "snow",
            jobDesc = "供应商待发货提醒")
    public void remindWaitDelivery() {
        log.info("【定时任务】【供应商待发货提醒】...start...");
        orderTaskService.remindWaitDelivery();
    }

    /**
     * 订单关闭前20分钟提醒
     * 每10分钟执行一次：0 0/10 * * * ?
     */
//    @Crane("RemindOrderNotPayTask")
    @XxlJob("remindOrderNotPayTask")
    @XxlRegister(cron = "0 0/10 * * * ?",
            author = "snow",
            jobDesc = "订单关闭前20分钟提醒")
    public void remindNotPay() {
        log.info("【定时任务】【商家-订单未支付通知】...start...");
        orderTaskService.remindNotPay();
    }

    /**
     * 自动关闭订单
     * 每10分钟执行一次：0 0/10 * * * ?
     */
//    @Crane("OrderAutoCloseTask")
    @XxlJob("orderAutoCloseTask")
    @XxlRegister(cron = "0 0/10 * * * ?",
            author = "snow",
            jobDesc = "自动关闭订单")
    public void autoClose() {
        log.info("【定时任务】【商家-订单超时自动关闭】...start...");
        orderTaskService.autoClose();
    }

    /**
     * 自动完成订单
     * 每小时15和45分执行：0 15,45 * * * ?
     */
//    @Crane("OrderAutoFinishTask")
    @XxlJob("orderAutoFinishTask")
    @XxlRegister(cron = "0 15,45 * * * ?",
            author = "snow",
            jobDesc = "自动完成订单")
    public void autoFinish() {
        log.info("【定时任务】【商家-订单超时自动完成】...start...");
        orderTaskService.autoFinish();
    }

    @XxlJob("reportOrder")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "同步订单报表")
    public void reportOrder(String jobIdStr) {
        log.info("【定时任务】【同步订单报表】...start...");
        long jobId;
        if (StringUtils.hasLength(jobIdStr)) {
            jobId = Long.parseLong(jobIdStr.trim());
        } else {
            jobId = XxlJobHelper.getJobId();
        }
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryFeign.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<Order> carts = orderRepository.getByUpdateTime(triggerTime);
        List<ReportSourceOrderReq> list = carts.stream().map(order -> {
            ReportSourceOrderReq orderReq = new ReportSourceOrderReq();
            orderReq.setOrderId(order.getOrderId());
            orderReq.setShopId(order.getShopId());
            orderReq.setOrderTime(order.getCreateTime());
            orderReq.setProvinceId((long) order.getTopRegionId());
            orderReq.setUserId(order.getUserId());
            orderReq.setPaymentAmount(order.getTotalAmount());
            orderReq.setPaymentTime(order.getPayDate());
            orderReq.setUpdateTime(order.getUpdateTime());
            orderReq.setCreateTime(order.getCreateTime());
            orderReq.setFinishTime(order.getFinishDate());
            orderReq.setDeliveryTime(order.getShippingDate());
            return orderReq;
        }).collect(Collectors.toList());
        BaseResp res = ThriftResponseHelper.executeThriftCall(() -> reportFeign.batchCreateSourceOrder(new BatchReportSourceOrderReq(list)));
    }

    @XxlJob("reportOrderItem")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "同步订单项报表")
    public void reportOrderItem() {
        log.info("【定时任务】【同步订单项报表】...start...");
        long jobId = XxlJobHelper.getJobId();
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryFeign.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<OrderItemReportDto> orderItems = orderItemRepository.getByUpdateTime(triggerTime);

        for (OrderItemReportDto orderItem : orderItems) {

            ReportSourceOrderItemReq orderItemReq = new ReportSourceOrderItemReq();
            orderItemReq.setOrderId(orderItem.getOrderId());
            orderItemReq.setOrderItemId(orderItem.getId());
            orderItemReq.setOrderTime(orderItem.getOrderTime());
            orderItemReq.setUserId(orderItem.getUserId());
            orderItemReq.setAmount(orderItem.getRealTotalPrice());
            orderItemReq.setPlatform(orderItem.getPlatform());
            orderItemReq.setCreateTime(LocalDateTime.ofInstant(orderItem.getCreateTime().toInstant(), ZoneId.systemDefault()));
            orderItemReq.setPrice(orderItem.getSalePrice());
            orderItemReq.setPaymentTime(orderItem.getPaymentTime());
            orderItemReq.setDiscount(orderItem.getSalePrice().multiply(new BigDecimal(orderItem.getQuantity())).subtract(orderItem.getDiscountAmount()));
            orderItemReq.setQuantity(orderItem.getQuantity().intValue());
            orderItemReq.setProductId(orderItem.getProductId());
            orderItemReq.setShopId(orderItem.getShopId());
            orderItemReq.setUpdateTime(LocalDateTime.ofInstant(orderItem.getUpdateTime().toInstant(), ZoneId.systemDefault()));
            BaseResp res = ThriftResponseHelper.executeThriftCall(() -> reportFeign.createSourceOrderItem(orderItemReq));
        }
    }

    @XxlJob("reportOrderRefund")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "同步售后报表")
    public void reportOrderRefund() {
        log.info("【定时任务】【同步售后单项报表】...start...");
        long jobId = XxlJobHelper.getJobId();
//        long jobId = 8L;
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryFeign.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<OrderRefundReportDto> refunds = orderRefundRepository.getByUpdateTime(triggerTime);

        for (OrderRefundReportDto orderRefund : refunds) {
            ReportSourceRefundReq refundReq = new ReportSourceRefundReq();
            refundReq.setRefundId(orderRefund.getId());
            refundReq.setOrderId(orderRefund.getOrderId());
            refundReq.setApplyTime(orderRefund.getApplyDate());
            refundReq.setCompletionTime(orderRefund.getLastModifyTime());
            refundReq.setAmount(orderRefund.getAmount());
            if (orderRefund.getProductId() == null) {
                log.error("获取不到productId,售后单号:" + orderRefund.getId());
                continue;
            }
            refundReq.setProductId(orderRefund.getProductId());
            refundReq.setPlatform(orderRefund.getPlatform());
            refundReq.setQuantity(orderRefund.getReturnQuantity().intValue());
            refundReq.setOrderItemId(orderRefund.getOrderItemId());
            refundReq.setSkuId(orderRefund.getSkuId());
            refundReq.setUserId(orderRefund.getUserId());
            refundReq.setProductId(orderRefund.getProductId());
            refundReq.setShopId(orderRefund.getShopId().intValue());
            refundReq.setStatus(orderRefund.getStatus());
            refundReq.setCreateTime(orderRefund.getCreateTime());
            refundReq.setUpdateTime(orderRefund.getLastModifyTime());
            BaseResp res = ThriftResponseHelper.executeThriftCall(() -> reportFeign.createSourceRefund(refundReq));
        }
    }

    @XxlJob("reportOrderBill")
    @XxlRegister(cron = "0 0 3 * * ?",
            author = "snow",
            jobDesc = "同步物流信息")
    public void reportOrderBill() {
        log.info("【定时任务】【同步订单物流报表】...start...");
        long jobId = XxlJobHelper.getJobId();
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryFeign.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<OrderWayBill> orderWayBills = orderWayBillRepository.getByUpdateTime(triggerTime);

        for (OrderWayBill order : orderWayBills) {
            ReportSourceOrderBillReq orderReq = new ReportSourceOrderBillReq();
            orderReq.setBillId(order.getId());
            orderReq.setOrderId(order.getOrderId());
            orderReq.setDeliverTime(order.getCreateTime());
            orderReq.setShopId(0L);
            orderReq.setShipOrderNumber(order.getShipOrderNumber());
            orderReq.setExpressCompanyCode(order.getExpressCompanyCode());
            orderReq.setExpressCompanyName(order.getExpressCompanyName());
            orderReq.setUpdateTime(LocalDateTime.ofInstant(order.getUpdateTime().toInstant(), ZoneId.systemDefault()));
            orderReq.setCreateTime(LocalDateTime.ofInstant(order.getCreateTime().toInstant(), ZoneId.systemDefault()));
            BaseResp res = ThriftResponseHelper.executeThriftCall(() -> reportFeign.createSourceOrderBill(orderReq));
        }

    }
}
