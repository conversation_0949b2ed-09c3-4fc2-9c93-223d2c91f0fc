package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * 承载订单导出的数据模型
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderExportBo {

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 订单来源
     */
    private String platformDesc;

    /**
     * 店铺
     */
    private String shopName;

    /**
     * 买家
     */
    private Long userId;
    private String userName;

    /**
     * 下单时间
     */
    private String orderDateStr;

    /**
     * 付款时间
     */
    private String payDateStr;

    /**
     * 完成时间
     */
    private String finishDateStr;
    /**
     * 订单类型
     */
    private Integer orderType;
    /**
     * 订单类型
     */
    private String orderTypeDesc;

    /**
     * 支付方式
     */
    private String payMethodDesc;

    /**
     * 交易单号
     */
    private String gatewayOrderId;

    /**
     * 商品总额
     */
    private BigDecimal productTotalAmount;

    /**
     * 运费
     */
    private BigDecimal freight;

    /**
     * 税金
     */
    private BigDecimal tax;

    /**
     * 优惠券抵扣
     */
    private BigDecimal couponAmount;

    /**
     * 折扣抵扣
     */
    private BigDecimal discountAmount;

    /**
     * 满额减
     */
    private BigDecimal moneyOffAmount;

    /**
     * 供应商改价
     */
    private BigDecimal updateAmount;

    /**
     * 订单实付总额
     */
    private BigDecimal totalAmount;

    /**
     * 平台佣金
     */
    private BigDecimal commissionTotalAmount;

    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单状态
     */
    private String orderStatusDesc;

    /**
     * 买家留言
     */
    private String userRemark;

    /**
     * 收货人
     */
    private String shipTo;

    /**
     * 手机号码
     */
    private String cellPhone;

    /**
     * 收货地址
     */
    private String address;

    /**
     * 订单明细ID
     */
    private Long itemId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * SkuID
     */
    private String skuId;

    /**
     * 规格ID
     */
    private Long skuAutoId;

    /**
     * 一级分类
     */
    private String cateLevel1Name;

    /**
     * 二级分类
     */
    private String cateLevel2Name;

    /**
     * 三级分类
     */
    private String cateLevel3Name;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * SKU 级别的货号
     */
    private String skuCode;

    /**
     * UPC/69码
     */
    private String barCode;

    /**
     * 单价
     */
    private BigDecimal salePrice;

    /**
     * 数量
     */
    private Long quantity;
    /**
     * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
     */
    private Integer invoiceType;
    /**
     * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
     */
    private String invoiceTypeDesc;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 税号
     */
    private String invoiceCode;

    /**
     * 发票内容(发票明细、商品类别)
     */
    private String invoiceContext;
    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 注册电话
     */
    private String registerPhone;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行帐号
     */
    private String bankNo;

    /**
     * 收票人姓名
     */
    private String realName;

    /**
     * 收票人手机号
     */
    private String invoiceCellPhone;

    /**
     * 收票人邮箱
     */
    private String email;

    /**
     * 收票人地址地址
     */
    private String invoiceFullAddress;
}
