package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class UpdateItemAmountBo extends BaseParamReq {

    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 用户名
     */
    private String userName;
    /**
     * 订单明细ID
     */
    @PrimaryField
    private Long itemId;
    /**
     * 修改后的价格
     */
    @ExaminField(description = "改价金额")
    private BigDecimal updatedAmount;

}
