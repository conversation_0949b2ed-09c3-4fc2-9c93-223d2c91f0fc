package com.sankuai.shangou.seashop.order.core.service.model.stats;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class UserPurchaseSkuBo {

    /**
     * 订单ID
     */
    private String orderId;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单状态描述
     */
    private String orderStatusDesc;
    /**
     * 明细ID
     */
    private Long itemId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * sku描述
     */
    private List<String> skuDescList;
    /**
     * sku采购数量
     */
    private Long quantity;
    /**
     * sku采购金额
     */
    private BigDecimal realTotalPrice;
    /**
     * skuId
     */
    private Long skuAutoId;

}
