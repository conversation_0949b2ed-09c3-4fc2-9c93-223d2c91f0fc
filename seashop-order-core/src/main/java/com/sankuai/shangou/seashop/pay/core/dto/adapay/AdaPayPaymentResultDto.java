package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2024/2/22/022
 * @description:
 */
@Data
@Builder
public class AdaPayPaymentResultDto {

    @JsonProperty("id")
    private String id;

    @JsonProperty("out_trans_id")
    private String outTransId;

    @JsonProperty("status")
    private String status;

    @JsonProperty("end_time")
    private String endTime;

    @JsonProperty("error_code")
    private String errorCode;

    @JsonProperty("error_msg")
    private String errorMsg;

    @JsonProperty("pay_amt")
    private String payAmt;
}
