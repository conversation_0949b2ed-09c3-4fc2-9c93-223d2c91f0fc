package com.sankuai.shangou.seashop.pay.core.mq.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.huifu.adapay.core.exception.BaseAdaPayException;
import com.huifu.adapay.model.PaymentReverse;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.pay.common.enums.AdaPayEnums;
import com.sankuai.shangou.seashop.pay.core.config.AdaPayInitWithMerConfig;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseListQueryDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseListResultDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseResultDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentReverseDto;
import com.sankuai.shangou.seashop.pay.core.mq.model.OrderReverseExceptionMsg;
import com.sankuai.shangou.seashop.pay.core.service.ReverseOrderService;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ReverseOrder;
import com.sankuai.shangou.seashop.pay.dao.core.repository.ReverseOrderRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseTypeEnums;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * @author: lhx
 * @date: 2023/12/18/018
 * @description: 退款订单状态（未分账支付退款）异常订单通知 消费
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConstant.DEFAULT_NAMESPACE,
//        topic = MafkaConstant.ORDER_REVERSE_EXCEPTION_TOPIC,
//        group = MafkaConstant.ORDER_REVERSE_EXCEPTION_CONSUMER)
@RocketMQMessageListener(topic = MafkaConstant.ORDER_REVERSE_EXCEPTION_TOPIC + "_${spring.profiles.active}"
        , consumerGroup = MafkaConstant.ORDER_REVERSE_EXCEPTION_CONSUMER + "_${spring.profiles.active}")
public class OrderReverseExceptionListener implements RocketMQListener<String> {

    @Resource
    private ReverseOrderRepository reverseOrderRepository;
    @Resource
    private ReverseOrderService reverseOrderService;

    private static final Integer PAGE_NUM = 0;
    private static final Integer PAGE_SIZE = 20;

    private final static ObjectMapper objectMapper = new ObjectMapper();

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
//        String body = (String) mafkaMessage.getBody();
//        log.info("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容为: {}", body);
//        OrderReverseExceptionMsg orderReverseExceptionMsg = JsonUtil.parseObject(body, OrderReverseExceptionMsg.class);
//
//        ReverseOrder reverseOrder = reverseOrderRepository.getByReverseId(orderReverseExceptionMsg.getReverseId());
//        if (reverseOrder == null) {
//            log.info("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单不存在.【body】:{}", body);
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//
//        Integer reverseState = reverseOrder.getReverseState();
//        if (!ReverseStateEnums.REVERSE_ING.getStatus().equals(reverseState)) {
//            log.info("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单状态不是退款中.【reverseOrder】:{}", reverseOrder);
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//
//        AdaPayReverseListQueryDto adaPayReverseListQueryDto = AdaPayReverseListQueryDto.builder()
//                .paymentId(reverseOrder.getChannelPayId())
//                .appId(AdaPayInitWithMerConfig.appId)
//                .pageIndex(PAGE_NUM)
//                .pageSize(PAGE_SIZE).build();
//        AdaPaymentReverseDto adaPaymentReverseDto = null;
//
//        boolean continueSearch = true;
//        while (continueSearch) {
//            adaPayReverseListQueryDto.setPageIndex(adaPayReverseListQueryDto.getPageIndex() + 1);
//            Map<String, Object> adaPayReverseListQueryMap = BeanUtil.beanToMap(adaPayReverseListQueryDto, true, true);
//            try {
//                log.info("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单状态异常.【adaPayReverseListQueryMap】:{}", adaPayReverseListQueryMap);
//                Map<String, Object> resultMap = PaymentReverse.queryList(adaPayReverseListQueryMap);
//                log.info("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单状态异常.【resultMap】:{}", resultMap);
//                AdaPayReverseListResultDto adaPayReverseListResultDto = objectMapper.readValue(JsonUtil.toJsonString(resultMap), AdaPayReverseListResultDto.class);
//                if (null != adaPayReverseListResultDto && CollUtil.isNotEmpty(adaPayReverseListResultDto.getPaymentReverses())) {
//                    List<AdaPaymentReverseDto> paymentReverses = adaPayReverseListResultDto.getPaymentReverses();
//                    adaPaymentReverseDto = paymentReverses.stream()
//                            .filter(pr -> StrUtil.isNotBlank(pr.getOrderNo()) && pr.getOrderNo().equals(reverseOrder.getReverseId()))
//                            .findFirst().orElse(null);
//                    if (null != adaPaymentReverseDto) {
//                        continueSearch = false;
//                    } else if (null == adaPaymentReverseDto && paymentReverses.size() < PAGE_SIZE) {
//                        // 说明没找到，并且没有数据可以找了
//                        continueSearch = false;
//                    }
//                } else {
//                    continueSearch = false;
//                }
//            } catch (BaseAdaPayException e) {
//                log.error("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单状态异常.【body】:{}", body, e);
//                return ConsumeStatus.CONSUME_FAILURE;
//            } catch (Exception e) {
//                log.error("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单状态异常.【body】:{}", body, e);
//                return ConsumeStatus.CONSUME_FAILURE;
//            }
//        }
//
//        AdaPayReverseResultDto adaPayReverseResultDto = new AdaPayReverseResultDto();
//        adaPayReverseResultDto.setType(ReverseTypeEnums.REVERSE_NO_SETTLEMENT.getType());
//        adaPayReverseResultDto.setRefundId(orderReverseExceptionMsg.getReverseId());
//
//        if (null == adaPaymentReverseDto || AdaPayEnums.FAILED.getStatus().equals(adaPaymentReverseDto.getStatus())) {
//            // 为空说明调用确实没成功；状态为失败说明调用成功了，但是退款失败了
//            adaPayReverseResultDto.setPayStatus(ReverseStateEnums.REVERSE_ERROR.getStatus());
//            adaPayReverseResultDto.setChannelRefundId(null != adaPaymentReverseDto ? adaPaymentReverseDto.getId() : "");
//            adaPayReverseResultDto.setErrorMessage(null != adaPaymentReverseDto ? adaPaymentReverseDto.getReason() : "调用渠道异常");
//            reverseOrderService.updateAndSendSyncReverse(adaPayReverseResultDto);
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//        if (AdaPayEnums.SUCCEEDED.getStatus().equals(adaPaymentReverseDto.getStatus())) {
//            // 说明退款成功了
//            adaPayReverseResultDto.setPayStatus(ReverseStateEnums.REVERSE_SUCCESS.getStatus());
//            adaPayReverseResultDto.setChannelRefundId(adaPaymentReverseDto.getId());
//            reverseOrderService.updateAndSendSyncReverse(adaPayReverseResultDto);
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(String body) {
        try {
//            String body = (String) mafkaMessage.getBody();
            log.info("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容为: {}", body);
            OrderReverseExceptionMsg orderReverseExceptionMsg = JsonUtil.parseObject(body, OrderReverseExceptionMsg.class);

            ReverseOrder reverseOrder = reverseOrderRepository.getByReverseId(orderReverseExceptionMsg.getReverseId());
            if (reverseOrder == null) {
                log.info("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单不存在.【body】:{}", body);
                return;
            }

            Integer reverseState = reverseOrder.getReverseState();
            if (!ReverseStateEnums.REVERSE_ING.getStatus().equals(reverseState)) {
                log.info("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单状态不是退款中.【reverseOrder】:{}", reverseOrder);
                return;
            }

            AdaPayReverseListQueryDto adaPayReverseListQueryDto = AdaPayReverseListQueryDto.builder()
                    .paymentId(reverseOrder.getChannelPayId())
                    .appId(AdaPayInitWithMerConfig.appId)
                    .pageIndex(PAGE_NUM)
                    .pageSize(PAGE_SIZE).build();
            AdaPaymentReverseDto adaPaymentReverseDto = null;

            boolean continueSearch = true;
            while (continueSearch) {
                adaPayReverseListQueryDto.setPageIndex(adaPayReverseListQueryDto.getPageIndex() + 1);
                Map<String, Object> adaPayReverseListQueryMap = BeanUtil.beanToMap(adaPayReverseListQueryDto, true, true);
                try {
                    log.info("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单状态异常.【adaPayReverseListQueryMap】:{}", adaPayReverseListQueryMap);
                    Map<String, Object> resultMap = PaymentReverse.queryList(adaPayReverseListQueryMap);
                    log.info("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单状态异常.【resultMap】:{}", resultMap);
                    AdaPayReverseListResultDto adaPayReverseListResultDto = objectMapper.readValue(JsonUtil.toJsonString(resultMap), AdaPayReverseListResultDto.class);
                    if (null != adaPayReverseListResultDto && CollUtil.isNotEmpty(adaPayReverseListResultDto.getPaymentReverses())) {
                        List<AdaPaymentReverseDto> paymentReverses = adaPayReverseListResultDto.getPaymentReverses();
                        adaPaymentReverseDto = paymentReverses.stream()
                                .filter(pr -> StrUtil.isNotBlank(pr.getOrderNo()) && pr.getOrderNo().equals(reverseOrder.getReverseId()))
                                .findFirst().orElse(null);
                        if (null != adaPaymentReverseDto) {
                            continueSearch = false;
                        } else if (null == adaPaymentReverseDto && paymentReverses.size() < PAGE_SIZE) {
                            // 说明没找到，并且没有数据可以找了
                            continueSearch = false;
                        }
                    } else {
                        continueSearch = false;
                    }
                } catch (BaseAdaPayException e) {
                    log.error("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单状态异常.【body】:{}", body, e);
                    return;
                } catch (Exception e) {
                    log.error("【mafka消费】【退款订单状态（未分账支付退款）异常订单】消息内容,退款订单状态异常.【body】:{}", body, e);
                    return;
                }
            }

            AdaPayReverseResultDto adaPayReverseResultDto = new AdaPayReverseResultDto();
            adaPayReverseResultDto.setType(ReverseTypeEnums.REVERSE_NO_SETTLEMENT.getType());
            adaPayReverseResultDto.setRefundId(orderReverseExceptionMsg.getReverseId());

            if (null == adaPaymentReverseDto || AdaPayEnums.FAILED.getStatus().equals(adaPaymentReverseDto.getStatus())) {
                // 为空说明调用确实没成功；状态为失败说明调用成功了，但是退款失败了
                adaPayReverseResultDto.setPayStatus(ReverseStateEnums.REVERSE_ERROR.getStatus());
                adaPayReverseResultDto.setChannelRefundId(null != adaPaymentReverseDto ? adaPaymentReverseDto.getId() : "");
                adaPayReverseResultDto.setErrorMessage(null != adaPaymentReverseDto ? adaPaymentReverseDto.getReason() : "调用渠道异常");
                reverseOrderService.updateAndSendSyncReverse(adaPayReverseResultDto);
                return;
            }
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(adaPaymentReverseDto.getStatus())) {
                // 说明退款成功了
                adaPayReverseResultDto.setPayStatus(ReverseStateEnums.REVERSE_SUCCESS.getStatus());
                adaPayReverseResultDto.setChannelRefundId(adaPaymentReverseDto.getId());
                reverseOrderService.updateAndSendSyncReverse(adaPayReverseResultDto);
                return;
            }
        } catch (Exception e) {
            log.error("OrderReverseExceptionListener consumer fail,", e);
            throw new RuntimeException(e);
        }
    }
}
