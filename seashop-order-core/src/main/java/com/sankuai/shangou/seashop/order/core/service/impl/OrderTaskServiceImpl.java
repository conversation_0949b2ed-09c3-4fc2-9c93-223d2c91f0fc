package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.cola.statemachine.StateMachine;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.config.TaskProps;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.constant.OrderConst;
import com.sankuai.shangou.seashop.order.common.enums.PlatformMessageTemplateEnum;
import com.sankuai.shangou.seashop.order.common.remote.MemberRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.MessageRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.common.remote.model.user.MemberContactBo;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderMessagePublisher;
import com.sankuai.shangou.seashop.order.core.service.OrderStatsService;
import com.sankuai.shangou.seashop.order.core.service.OrderTaskService;
import com.sankuai.shangou.seashop.order.core.service.assit.BizNoGenerator;
import com.sankuai.shangou.seashop.order.core.service.assit.MessageNoticeHelper;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderFinishForFinanceBizAssist;
import com.sankuai.shangou.seashop.order.core.service.model.sms.SupplierDeliverTimeoutMsgBo;
import com.sankuai.shangou.seashop.order.core.service.model.sms.UserNameMsgBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.ShopOrderCountStatsBo;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderEvent;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonOrderQueryParamBo;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonPayRecordQueryParamBo;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderTaskServiceImpl implements OrderTaskService {

    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private OrderStatsService orderStatsService;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private MessageRemoteService messageRemoteService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private BizNoGenerator bizNoGenerator;
    @Resource
    private TaskProps taskProps;
    @Resource
    private StateMachine<OrderStatusEnum, OrderEvent, OrderContext> orderStateMachine;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private MemberRemoteService memberRemoteService;
    @Resource
    private OrderFinishForFinanceBizAssist orderFinishForFinanceBizAssist;
    @Resource
    private MessageNoticeHelper messageNoticeHelper;
    @Resource
    private OrderMessagePublisher orderMessagePublisher;

    /**
     * 定时任务时白天执行的，为了不影响线上使用，从ES查询数据
     * <AUTHOR>
     */
    @Override
    public void remindWaitDelivery() {
        TradeSiteSettingBo tradeSiteSetting = settingRemoteService.getTradeSiteSetting();
        if (tradeSiteSetting == null) {
            log.warn("【供应商未发货自动提醒】获取平台交易设置失败");
            return;
        }
        String remindWaitDelivery = tradeSiteSetting.getOrderWaitDeliveryRemindTime();
        if (StrUtil.isBlank(remindWaitDelivery)) {
            log.warn("【供应商未发货自动提醒】供应商未发货自动短信提醒时限配置为空");
            return;
        }
        int remindTime = Integer.parseInt(remindWaitDelivery);
        if (remindTime <= 0) {
            log.warn("【供应商未发货自动提醒】供应商未发货自动短信提醒时限配置异常");
            return;
        }
        Date date = DateUtil.offsetDay(new Date(), -remindTime);
        log.info("【供应商未发货自动提醒】查询订单的支付日期, remindTime={},date={}", remindTime, DateUtil.format(date, "yyyy-MM-dd HH:mm:ss"));
        List<ShopOrderCountStatsBo> needRemindList = orderStatsService.searchPaidDateLessThanAndNotDeliver(date);
        if (needRemindList.isEmpty()) {
            log.info("【供应商未发货自动提醒】没有需要提醒的订单");
            return;
        }
        // 获取店铺数据
        List<Long> shopIdList = needRemindList.stream().map(ShopOrderCountStatsBo::getShopId).distinct().collect(Collectors.toList());
        Map<Long, ShopResp> shopMap = shopRemoteService.getShopMap(shopIdList);
        // 获取店铺账号:phone
        List<MemberContactBo> memberContactList = memberRemoteService.getMemberContactByShopId(shopIdList);
        if (memberContactList == null) {
            memberContactList = new ArrayList<>(0);
        }
        Map<Long, MemberContactBo> shopContactMap = memberContactList.stream()
                .collect(Collectors.toMap(MemberContactBo::getShopId, Function.identity(), (oldV, newV) -> newV));
        // 考虑是否需要MQ异步
        List<Long> messageIdList = bizNoGenerator.generateMessageUniqueIdBatch(shopIdList.size());
        for (int i = 0; i < needRemindList.size(); i++) {
            try {
                ShopOrderCountStatsBo orderInfoBo = needRemindList.get(i);
                log.info("【供应商未发货自动提醒】开始提醒,orderInfoBo={}", JsonUtil.toJsonString(orderInfoBo));
                ShopResp shopResp = shopMap.get(orderInfoBo.getShopId());
                if (shopResp == null) {
                    log.warn("【供应商未发货自动提醒】没有获取到店铺信息,orderInfoBo={}", orderInfoBo);
                    return;
                }
                MemberContactBo contact = shopContactMap.get(orderInfoBo.getShopId());
                log.info("【供应商未发货自动提醒】发送短信,shopId={}, 联系方式为: {} ", orderInfoBo.getShopId(), JsonUtil.toJsonString(contact));
                if (contact == null || StrUtil.isBlank(contact.getContact())) {
                    log.warn("【供应商未发货自动提醒】没有获取到用户的联系方式, shopId={}", orderInfoBo.getShopId());
                    continue;
                }
                // 发送短信
                SupplierDeliverTimeoutMsgBo msgBo = new SupplierDeliverTimeoutMsgBo(shopResp.getShopName(), orderInfoBo.getNotDeliverCount(), remindTime);

                PlatformMessageTemplateEnum template = PlatformMessageTemplateEnum.SUPPLIER_DELIVER_TIMEOUT;
                Integer smsCode = messageNoticeHelper.getSmsCode(template);
                messageRemoteService.sendSms(smsCode, JsonUtil.toJsonString(msgBo), contact.getContact(), messageIdList.get(i));
            } catch (Exception e) {
                // 只捕获异常，每个发送不影响
                log.error("【供应商未发货自动提醒】消息发送异常,needRemindList={}", needRemindList, e);
            }
        }
    }

    @Override
    public void remindNotPay() {
        // 获取未支付订单关闭的配置
        int finalUnpaidTimeout = getNotPayCloseTimeoutHour();
        if (finalUnpaidTimeout <= 0) {
            log.warn("获取平台交易设置-未付款订单自动关闭时限失败");
            return;
        }
        // 订单关闭前N分钟发送短信提醒
        int noticeMinute = NumberUtil.nullToZero(taskProps.getOrderCloseNoticeMinute());
        Date now = new Date();
        int finalUnpaidTimeoutMinute = finalUnpaidTimeout * 60;
        // 下单时间小于这个提醒截止时间的，且没有提醒过的，需要提醒
        Date orderDateLe = DateUtil.offsetMinute(now, -(finalUnpaidTimeoutMinute - noticeMinute));
        CommonOrderQueryParamBo param = CommonOrderQueryParamBo.builder()
                .orderStatusEq(OrderStatusEnum.UNDER_PAY.getCode())
                .orderDateLe(orderDateLe)
                .build();
        log.info("【商家-订单未支付通知】查询订单的下单时间, param={}", JsonUtil.toJsonString(param));
        // 10分钟执行一次，数据量不会很大，直接查订单表
        List<Order> orderList = orderRepository.getByCondition(param);
        if (CollUtil.isEmpty(orderList)) {
            log.info("【商家-订单未支付通知】没有需要提醒的订单");
            return;
        }
        // 每个用户发送一次就行，按照用户分组，并拿到对应的订单号，最后需要根据订单号修改 is_send 字段
        Map<Long, List<String>> userIdMap = orderList.stream()
                .collect(Collectors.groupingBy(Order::getUserId, Collectors.mapping(Order::getOrderId, Collectors.toList())));
        // 通过userId获取用户的联系方式
        List<Long> userIdLists = orderList.stream().map(Order::getUserId).distinct().collect(Collectors.toList());
        List<MemberContactBo> memberContactList = memberRemoteService.getMemberContactByUserId(userIdLists);
        if (memberContactList == null) {
            memberContactList = new ArrayList<>(0);
        }
        Map<Long, MemberContactBo> memberContactMap = memberContactList.stream()
                .collect(Collectors.toMap(MemberContactBo::getUserId, Function.identity(), (oldV, newV) -> newV));
        // 系统设置的间隔时间，就是订单取消的时间很长，基本不可能会存在用户正好又支付的情况，所以没考虑并发
        String siteName = messageNoticeHelper.getSiteName();
        List<String> sendedOrderIdList = new ArrayList<>();
        for (Map.Entry<Long, List<String>> et : userIdMap.entrySet()) {
            try {
                Long userId = et.getKey();
                MemberContactBo contact = memberContactMap.get(userId);
                if (contact == null || StrUtil.isBlank(contact.getContact())) {
                    log.warn("【商家-订单未支付通知】没有获取到用户的联系方式,userId={}", userId);
                    continue;
                }
                // 构建邮箱消息体，短信的由短信模板确定内容
                String msgBody = messageNoticeHelper.buildEmailBody(PlatformMessageTemplateEnum.MERCHANT_WAIT_PAY, siteName, contact.getUserName());
                UserNameMsgBo msgBo = new UserNameMsgBo(contact.getUserName());
                messageNoticeHelper.noticeForTemplate(contact, PlatformMessageTemplateEnum.MERCHANT_WAIT_PAY, msgBo, msgBody);
                sendedOrderIdList.addAll(et.getValue());
            } catch (Exception e) {
                // 只捕获异常，每个发送不影响
                log.error("【商家-订单未支付通知】异常,et={}", et, e);
            }
        }
        if (CollUtil.isNotEmpty(sendedOrderIdList)) {
            orderRepository.updateIsSendBatch(sendedOrderIdList);
        }
    }

    @Override
    public void autoClose() {
        // 获取未支付订单关闭的配置
        int unpaidCloseTimeoutHour = getNotPayCloseTimeoutHour();
        log.info("【自动关闭订单】交易设置的关闭配置为: {}", unpaidCloseTimeoutHour);
        Date expireDate = DateUtil.offsetHour(new Date(), -unpaidCloseTimeoutHour);
        CommonOrderQueryParamBo param = CommonOrderQueryParamBo.builder()
            // 待付款和支付中的，支付中的是可能发起支付了，一直不支付
            .orderStatusIn(Arrays.asList(OrderStatusEnum.UNDER_PAY.getCode(), OrderStatusEnum.PAYING.getCode()))
            .orderDateLt(expireDate)
            .build();
        log.info("【自动关闭订单】查询订单参数: {}", JsonUtil.toJsonString(param));
        List<Order> orderList = orderRepository.getByCondition(param);
        if (CollUtil.isEmpty(orderList)) {
            log.info("【自动关闭订单】没有需要关闭的订单");
            return;
        }
        // 需要关闭的订单应该不多，循环里面涉及RPC调用等，时效不关键
        orderList.forEach(order -> {
            try {
                // 如果订单状态是支付中，则先尝试执行取消支付的操作，需要自动关闭的，是很多天之后的，还是支付中，可以系统取消
                boolean needCancelPay = OrderStatusEnum.PAYING.getCode().equals(order.getOrderStatus());
                if (needCancelPay) {
                    log.info("【自动关闭订单】订单状态是支付中，尝试执行取消支付操作, orderId={}", order.getOrderId());
                    OrderContext orderContext = OrderContext.builder()
                        .userId(CommonConst.USER_ID_DEFAULT_ZERO)
                        .userName("系统")
                        .orderId(order.getOrderId())
                        .cancelReason("过期没付款，自动取消")
                        .build();
                    orderStateMachine.fireEvent(OrderStatusEnum.PAYING, OrderEvent.CANCEL_PAY, orderContext);

                    // 执行完取消支付后，主库查询订单状态，如果是待付款的，则直接关闭
                    order = orderRepository.getByOrderIdForceMaster(order.getOrderId());
                }

                if (OrderStatusEnum.UNDER_PAY.getCode().equals(order.getOrderStatus())) {
                    log.info("【自动关闭订单】订单状态是待付款，执行关闭操作, orderId={}", order.getOrderId());
                    // 触发状态流转
                    OrderContext orderContext = OrderContext.builder()
                        .userId(CommonConst.USER_ID_DEFAULT_ZERO)
                        .userName("系统")
                        .orderId(order.getOrderId())
                        .cancelReason("过期没付款，自动关闭")
                        .build();
                    orderStateMachine.fireEvent(OrderStatusEnum.UNDER_PAY, OrderEvent.CLOSE, orderContext);
                }
            }
            catch (Exception e) {
                log.error("【自动关闭订单】异常,order={}", order, e);
            }
        });
    }

    @Override
    public void autoFinish() {
        // 获取当前需要自动收货完成的订单
        List<Order> finalFinishList = getNeedAutoFinishOrder();
        if (CollUtil.isEmpty(finalFinishList)) {
            log.info("【自动确认收货】当前订单都在延长收货期限内");
            return;
        }
        Date now = new Date();
        // 查询一些数据用于后续保存使用
        List<String> orderIdList = new ArrayList<>(finalFinishList.size());
        List<String> gatewayOrderIdList = new ArrayList<>(finalFinishList.size());
        finalFinishList.forEach(order -> {
            orderIdList.add(order.getOrderId());
            gatewayOrderIdList.add(order.getGatewayOrderId());
        });
        // 查询订单明细
        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(orderIdList);
        Map<String, List<OrderItem>> orderItemMap = orderItemList.stream().collect(Collectors.groupingBy(OrderItem::getOrderId));
        // 查询支付成功的记录，因为可能重复支付，支付记录表的数据可能不唯一，所以加上订单表上关联的支付单号查询
        CommonPayRecordQueryParamBo param = CommonPayRecordQueryParamBo.builder()
                .payStatusEq(PayStatusEnum.PAY_SUCCESS.getCode())
                .orderIdListIn(orderIdList)
                .payNoListIn(gatewayOrderIdList)
                .build();
        List<OrderPayRecord> paySuccessList = orderPayRecordRepository.getByCondition(param);
        Map<String, OrderPayRecord> paySuccessMap = paySuccessList.stream()
                .collect(Collectors.toMap(OrderPayRecord::getOrderId, Function.identity(), (oldV, newV) -> newV));
        // 定时任务执行与用户操作有很小的概率并发，但修改订单数据的时候条件加上了原状态，相当于乐观锁，所以这里没有额外考虑锁
        // 因为依赖订单的结果，也没有用批量更新
        finalFinishList.forEach(order -> {
            // 需要加锁，现在前端有确认收货按钮，所以这里加锁
            String lockKey = String.format(OrderConst.CONFIRM_RECEIPT_ORDER_PREFIX, order.getGatewayOrderId());
            LockHelper.lock(lockKey, ()->{
                Order orderNew = orderRepository.getById(order.getOrderId());
                if(orderNew.getOrderStatus().equals(OrderStatusEnum.FINISHED.getCode())) {
                    // 判断最新的订单状态，如果是已完成，则跳过这个订单
                    return;
                }
                TransactionHelper.doInTransaction(() -> {
                    // 修改订单为已完成
                    int cnt = orderRepository.updateOrderReceived(order.getOrderId(), OrderStatusEnum.UNDER_RECEIVE.getCode(),
                            OrderStatusEnum.FINISHED.getCode(), now, CommonConst.MESSAGE_AUTO_FINISH_REMARK);
                    // 订单更新成功，再更新其他数据
                    if (cnt == 1) {
                        orderFinishForFinanceBizAssist.finishOrder(order, now, orderItemMap.get(order.getOrderId()), paySuccessMap.get(order.getOrderId()));
                    }
                });
                try {
                    orderMessagePublisher.sendOrderChangeMessage(order.getOrderId(), OrderMessageEventEnum.COMPLETE_ORDER);
                } catch (Exception e) {
                    log.error("【自动确认收货】发送订单事件失败 ,orderId={}", order.getOrderId(), e);
                }
            });
        });
    }


    //********************************************************


    private int getNotPayCloseTimeoutHour() {
        TradeSiteSettingBo tradeSiteSetting = settingRemoteService.getTradeSiteSetting();
        if (tradeSiteSetting == null) {
            log.warn("获取平台交易设置失败");
            return 0;
        }
        String notPayCloseTime = tradeSiteSetting.getUnpaidTimeout();
        if (StrUtil.isBlank(notPayCloseTime)) {
            log.warn("获取平台交易设置-未付款订单自动关闭时限失败");
            return 0;
        }
        // 配置的单位为小时，
        int remindTimeHour = Integer.parseInt(notPayCloseTime);
        // 根据当前平台配置，这个配置的是N小时内未付款，不能再次发起支付，关闭需要在这个基础上再往后延12小时
        // 配置化的关闭时间
        int closeAfterForbidPayHour = NumberUtil.nullToZero(taskProps.getOrderCloseAfterPayForbiddenHour());
        log.info("获取平台交易设置-未付款订单自动关闭时限, remindTimeHour={},closeAfterForbidPayHour={}", remindTimeHour, closeAfterForbidPayHour);
        return remindTimeHour + closeAfterForbidPayHour;
    }

    /**
     * 获取当前需要自动收货完成的订单
     * <AUTHOR>
     */
    private List<Order> getNeedAutoFinishOrder() {
        TradeSiteSettingBo tradeSiteSetting = settingRemoteService.getTradeSiteSetting();
        if (tradeSiteSetting == null) {
            log.warn("【自动确认收货】获取平台交易设置失败");
            return null;
        }
        String autoReceivingTimeout = tradeSiteSetting.getNoReceivingTimeout();
        if (StrUtil.isBlank(autoReceivingTimeout)) {
            log.warn("【自动确认收货】获取平台交易设置-自动确认收货时限失败");
            return null;
        }
        int finishTime = Integer.parseInt(autoReceivingTimeout);
        log.info("【自动确认收货】交易设置的自动确认收货配置为: {}", finishTime);
        if (finishTime == 0) {
            finishTime = CommonConst.DEFAULT_AUTO_FINISH_DAYS;
        }
        Date shippingDateLt = DateUtil.offsetDay(new Date(), -finishTime);
        // 获取待收货的订单，每小时都会执行，数据量不会很大，直接查订单表
        CommonOrderQueryParamBo param = CommonOrderQueryParamBo.builder()
                // 查询待确认收货的
                .orderStatusEq(OrderStatusEnum.UNDER_RECEIVE.getCode())
                .shippingDateLt(shippingDateLt)
                .build();
        log.info("【自动确认收货】查询订单参数: {}", JsonUtil.toJsonString(param));
        List<Order> orderList = orderRepository.getByCondition(param);
        if (CollUtil.isEmpty(orderList)) {
            log.info("【自动确认收货】当前没有可以自动确认收货的订单");
            return null;
        }
        // 用户可能操作过延长收货，所以在前面数据的基础上，再次过滤
        return orderList.stream()
                .filter(order -> {
                    if (order.getReceiveDelay() == null) {
                        return true;
                    }
                    return DateUtil.compare(order.getShippingDate(), DateUtil.offsetDay(shippingDateLt, -order.getReceiveDelay())) < 0;
                })
                .collect(Collectors.toList());
    }

    /*private void buildAndSaveFinance(Order order, List<OrderItem> orderItemList, OrderPayRecord payRecord, Date now) {
        Finance finance = new Finance();
        finance.setOrderId(order.getOrderId());
        String adaPayId = payRecord != null && StrUtil.isNotBlank(payRecord.getPayNo()) ? payRecord.getPayNo() : "";
        finance.setAdapayId(adaPayId);
        String payNo = payRecord != null && StrUtil.isNotBlank(payRecord.getPayNo()) ? payRecord.getPayNo() : "";
        finance.setPayId(payNo);
        finance.setType(TransactionTypesEnum.FINISH.getCode());
        Date finishDate = order.getFinishDate() != null ? order.getFinishDate() : now;
        finance.setCreateDate(finishDate);
        finance.setShopId(order.getShopId());
        finance.setShopName(order.getShopName());
        finance.setUserId(order.getUserId());
        finance.setUserName(order.getUserName());
        String gatewayOrderId = order.getGatewayOrderId() != null ? order.getGatewayOrderId() : "";
        finance.setTransactionId(gatewayOrderId);
        finance.setTotalAmount(order.getTotalAmount());
        finance.setFreight(order.getFreight());
        finance.setProductAmount(order.getProductTotalAmount());
        finance.setDiscountAmount(order.getCouponAmount());
        finance.setPlatDiscountAmount(BigDecimal.ZERO);
        finance.setFullDiscount(order.getDiscountAmount());
        finance.setMoneyOff(order.getMoneyOffAmount());
        finance.setIntegralDiscount(BigDecimal.ZERO);
        finance.setActualPayAmount(payRecord.getPayAmount());

        PendingSettlementOrder settlementOrder = pendingSettlementOrderRepository.getByOrderId(order.getOrderId());
        if (settlementOrder != null) {
            finance.setSettlementAmount(settlementOrder.getSettlementAmount());
            finance.setCommissionAmount(settlementOrder.getPlatCommission());
            finance.setServiceAmount(BigDecimal.ZERO);
        } else {
            finance.setSettlementAmount(BigDecimal.ZERO);
            finance.setServiceAmount(BigDecimal.ZERO);
            finance.setCommissionAmount(BigDecimal.ZERO);
        }

        financeRepository.save(finance);

        List<FinanceItem> financeItemList = orderItemList.stream()
                .map(item -> {
                    FinanceItem financeItem = new FinanceItem();
                    financeItem.setFinanceId(finance.getId());
                    financeItem.setOrderId(item.getOrderId());
                    financeItem.setProductId(item.getProductId());
                    financeItem.setProductName(item.getProductName());
                    financeItem.setSku(item.getSku());
                    financeItem.setQuantity(item.getQuantity());
                    financeItem.setOriginalPrice(item.getCostPrice());
                    financeItem.setSalePrice(item.getSalePrice());
                    financeItem.setTotalAmount(NumberUtil.mul(item.getSalePrice(), item.getQuantity()));
                    financeItem.setDiscountPrice(item.getDiscountAmount());
                    financeItem.setFullDiscount(item.getFullDiscount());
                    financeItem.setMoneyOff(item.getMoneyOff());
                    financeItem.setCouponDiscount(item.getCouponDiscount());
                    financeItem.setPlatCouponDiscount(BigDecimal.ZERO);
                    financeItem.setCommisRate(item.getCommisRate());
                    return financeItem;
                })
                .collect(Collectors.toList());
        financeItemRepository.saveBatch(financeItemList);
    }*/

}
