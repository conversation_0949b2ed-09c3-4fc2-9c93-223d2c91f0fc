package com.sankuai.shangou.seashop.order.core.convert;

import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.OrderCreateReq;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname OrderConvert
 * Description //TODO
 * @date 2024/11/7 15:51
 */
@Mapper(componentModel = "spring")
public interface OrderConvert {

    @Mapping(expression = "java(createOrderBo.getPlatform().getCode())",target = "platform")
    Order convert(OrderCreateReq createOrderBo);
}
