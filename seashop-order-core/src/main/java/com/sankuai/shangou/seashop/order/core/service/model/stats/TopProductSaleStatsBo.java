package com.sankuai.shangou.seashop.order.core.service.model.stats;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class TopProductSaleStatsBo {

    private List<ProductSaleAmountBo> saleList;
    private BigDecimal avgSaleAmount;

    public static TopProductSaleStatsBo defaultZero() {
        TopProductSaleStatsBo bo = new TopProductSaleStatsBo();
        bo.setAvgSaleAmount(BigDecimal.ZERO);
        bo.setSaleList(Collections.emptyList());
        return bo;
    }

}
