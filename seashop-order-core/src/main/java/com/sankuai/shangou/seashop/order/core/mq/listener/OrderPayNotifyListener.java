package com.sankuai.shangou.seashop.order.core.mq.listener;

import cn.hutool.crypto.digest.MD5;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.common.enums.MqErrorDataBizTypeEnum;
import com.sankuai.shangou.seashop.order.core.mq.model.pay.PayResultBo;
import com.sankuai.shangou.seashop.order.core.service.assit.MqErrorDataAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.pay.OrderPayResultHandlerAssist;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_ORDER_PAY_NOTIFY,
//        group = MafkaConst.GROUP_ORDER_PAY_NOTIFY)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_PAY_NOTIFY + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_PAY_NOTIFY + "_${spring.profiles.active}")
public class OrderPayNotifyListener implements RocketMQListener<MessageExt> {

    @Resource
    private OrderPayResultHandlerAssist orderPayResultHandlerAssist;
    @Resource
    private MqErrorDataAssist mqErrorDataAssist;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage message, MessagetContext context) {
//        log.info("【支付回调】MQ收到的消息为:{}", message);
//        String body = (String) message.getBody();
//        PayResultBo payResultBo = JsonUtil.parseObject(body, PayResultBo.class);
//        if (payResultBo == null) {
//            log.error("【支付回调】MQ消息转换对象失败,消息内容为:{}", body);
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//        // 目前支付服务的回调是同一个MQ，包括了保证金的，所以判断是否是订单的，不是订单的不处理
//        if (!BusinessTypeEnum.ORDER.getType().equals(payResultBo.getBusinessType())) {
//            log.info("【支付回调】MQ消息不是订单支付回调,消息内容为:{}", body);
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//        log.info("【支付回调】MQ消息转换对象后的内容为:{}", JsonUtil.toJsonString(payResultBo));
//        orderPayResultHandlerAssist.handlePayResult(payResultBo);
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(MessageExt message) {
        log.info("【支付回调】MQ收到的消息为:{}", message);
        String body  = new String(message.getBody(), StandardCharsets.UTF_8);
        PayResultBo payResultBo = null;
        try {
            log.info("【支付回调】MQ消息转换对象前的内容为:{}", body);
            payResultBo = JsonUtil.parseObject(body, PayResultBo.class);
        } catch (Exception e) {
            log.error("【mafka消费】【订单创建失败】消息转换失败", e);
            String traceId = message.getMsgId();
            mqErrorDataAssist.saveErrorData(MqErrorDataBizTypeEnum.PAY_NOTIFY, MD5.create().digestHex(body), message.getMsgId(), traceId, e.getMessage(), body);
            return;
        }
        if (payResultBo == null) {
            log.error("【支付回调】MQ消息转换对象失败,消息内容为:{}", body);
            return;
        }
        // 目前支付服务的回调是同一个MQ，包括了保证金的，所以判断是否是订单的，不是订单的不处理
        if (!BusinessTypeEnum.ORDER.getType().equals(payResultBo.getBusinessType())) {
            log.info("【支付回调】MQ消息不是订单支付回调,消息内容为:{}", body);
            return;
        }
        log.info("【支付回调】MQ消息转换对象后的内容为:{}", JsonUtil.toJsonString(payResultBo));
        try {
            orderPayResultHandlerAssist.handlePayResult(payResultBo);
        } catch (Exception e) {
            log.error("【mafka消费】【订单创建失败】处理失败", e);
            String traceId = message.getMsgId();
            mqErrorDataAssist.saveErrorData(MqErrorDataBizTypeEnum.PAY_NOTIFY, payResultBo.getOrderId(), message.getMsgId(), traceId, e.getMessage(), body);
            throw new RuntimeException(e);
        }
    }
}
