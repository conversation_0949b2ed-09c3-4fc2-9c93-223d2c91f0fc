package com.sankuai.shangou.seashop.order.core.service.model.order;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * 订单附加信息：留言、优惠券、发票等
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderAdditionalBo {

    /**
     * 配送方式。目前是固定值。1：快递配送
     */
    private Integer deliveryType = 1;
    /**
     * 优惠券记录ID
     */
    private Long couponRecordId;
    /**
     * 优惠券ID
     */
    private Long couponId;
    /**
     * 优惠券金额
     */
    private BigDecimal couponAmount;
    /**
     * 折扣总金额
     */
    private BigDecimal discountAmount;
    /**
     * 满减总金额
     */
    private BigDecimal reductionAmount;
    /**
     * 满减条件金额
     */
    private BigDecimal reductionConditionAmount;
    /**
     * 运费
     */
    private BigDecimal freightAmount;
    /**
     * 用户备注
     */
    private String remark;
    /**
     * 税费
     */
    private BigDecimal taxAmount;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 发票信息
     */
    private InvoiceBo invoice;
    /**
     * 满减活动ID
     */
    private Long reductionActivityId;

    /**
     * 第三方订单号(美团订单号)
     */
    private String sourceOrderId;

}
