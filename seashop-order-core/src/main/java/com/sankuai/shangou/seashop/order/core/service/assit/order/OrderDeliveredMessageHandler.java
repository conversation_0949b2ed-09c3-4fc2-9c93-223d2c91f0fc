package com.sankuai.shangou.seashop.order.core.service.assit.order;

import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.enums.AppletMessageEventEnum;
import com.sankuai.shangou.seashop.order.common.enums.PlatformMessageTemplateEnum;
import com.sankuai.shangou.seashop.order.common.remote.MemberRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.MessageRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.message.OrderDeliveredAppletMessage;
import com.sankuai.shangou.seashop.order.common.remote.model.user.MemberContactBo;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.service.assit.MessageNoticeHelper;
import com.sankuai.shangou.seashop.order.core.service.model.sms.UserNameAndOrderMsgBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderWayBill;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderWayBillRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderDeliveredMessageHandler implements OrderChangeMessageHandler {

    @Resource
    private MessageRemoteService messageRemoteService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderWayBillRepository orderWayBillRepository;
    @Resource
    private MessageNoticeHelper messageNoticeHelper;
    @Resource
    private MemberRemoteService memberRemoteService;

    @Override
    public OrderMessageEventEnum getEvent() {
        return OrderMessageEventEnum.DELIVERY_ORDER;
    }

    @Override
    public void handle(OrderMessage message) {
        Order order = orderRepository.getByOrderIdForceMaster(message.getOrderId());
        if (order == null) {
            log.warn("【订单状态变更-发货】订单不存在, orderId: {}", message.getOrderId());
            return;
        }
        try {
            log.info("【订单状态变更-发货】处理发送小程序消息");
            List<OrderItem> orderItems = orderItemRepository.getByOrderId(message.getOrderId());
            String productName = orderItems.stream()
                    .map(OrderItem::getProductName)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
            productName = StringUtils.abbreviate(productName, CommonConst.WECHAT_MESSAGE_LENGTH);
            List<OrderWayBill> wayBills = orderWayBillRepository.getByOrderId(message.getOrderId());
            String companyName = wayBills.stream()
                    .map(OrderWayBill::getExpressCompanyName)
                    .reduce((a, b) -> a + "," + b)
                    .orElse("");
            OrderDeliveredAppletMessage appletMessage = new OrderDeliveredAppletMessage();
            appletMessage.setThing1(productName);
            appletMessage.setCharacter_string2(order.getOrderId());
            appletMessage.setAmount7(order.getTotalAmount().toPlainString() + "元");
            appletMessage.setThing4(companyName);
            appletMessage.setThing6("");
            messageRemoteService.sendAppletMessage(order.getUserId(), AppletMessageEventEnum.MEMBER_ORDER_DELIVERED, appletMessage);
        } catch (Exception e) {
            // 发送消息不影响主流程
            log.error("【订单状态变更-发货】发送小程序消息异常", e);
        }
        try {
            log.info("【订单状态变更-发货】处理发送短信/邮箱消息");
            MemberContactBo contact = memberRemoteService.getMemberContactByUserId(order.getUserId());
            // 系统设置的间隔时间，就是订单取消的时间很长，基本不可能会存在用户正好又支付的情况，所以没考虑并发
            String siteName = messageNoticeHelper.getSiteName();
            // 构建消息体，邮箱和短信内容一样
            String msgBody = messageNoticeHelper.buildEmailBody(PlatformMessageTemplateEnum.MERCHANT_ORDER_DELIVER, siteName,
                    contact.getUserName(), order.getOrderId());
            UserNameAndOrderMsgBo msgBo = new UserNameAndOrderMsgBo(contact.getUserName(), order.getOrderId());
            // 发送消息
            messageNoticeHelper.noticeForTemplate(contact, PlatformMessageTemplateEnum.MERCHANT_ORDER_DELIVER, msgBo, msgBody);
        } catch (Exception e) {
            log.error("【订单状态变更-发货】发送短信/邮箱消息异常", e);
        }
    }
}
