package com.sankuai.shangou.seashop.pay.core.service.adapay.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.pay.common.constant.AdaPayConstant;
import com.sankuai.shangou.seashop.pay.common.enums.AdaPayEnums;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentResultDto;
import com.sankuai.shangou.seashop.pay.core.service.PayOrderService;
import com.sankuai.shangou.seashop.pay.core.service.adapay.CallBackStrategyService;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.request.OrderPayQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description: 支付回调处理
 */
@Component("payment")
@Slf4j
public class PaymentCallBackServiceImpl implements CallBackStrategyService {

    @Resource
    private PayOrderService payOrderService;

    @Override
    public void callback(String data) {
        log.info("下单支付回调处理data={}", data);
        JSONObject jsonObject = JSONUtil.parseObj(data);
        String id = jsonObject.get(AdaPayConstant.ID).toString();
        String payId = jsonObject.get(AdaPayConstant.ORDER_NO).toString();
        String status = jsonObject.getStr(AdaPayConstant.STATUS);
        String outTransId = "";
        if (jsonObject.get(AdaPayConstant.OUT_TRANS_ID) != null) {
            outTransId = jsonObject.get(AdaPayConstant.OUT_TRANS_ID).toString();
        }
        String errorMsg = "";
        if (jsonObject.get(AdaPayConstant.ERROR_MSG) != null) {
            errorMsg = jsonObject.get(AdaPayConstant.ERROR_MSG).toString();
        }

        OrderPayResp orderPayResp = payOrderService.getOne(
                OrderPayQueryReq.builder()
                        .channelPayId(id).build()
        );
        if (orderPayResp != null && StrUtil.isNotBlank(orderPayResp.getOrderId())) {
            // 新加逻辑，如果支付的入参金额跟回调的金额不一致，这个订单不处理了
            OrderPayResp payResp = payOrderService.queryOrderPayResp(orderPayResp.getOrderId());
            String payAmt = jsonObject.get(AdaPayConstant.PAY_AMT).toString();
            BigDecimal bigDecimalValue = new BigDecimal(payAmt);
            if (bigDecimalValue.compareTo(payResp.getPayAmount()) != 0) {
                // 说明金额被篡改了，这个订单不处理了
                log.info("金额不相等，这个订单不处理了,支付金额={},回调金额={},", bigDecimalValue, payResp.getPayAmount());
                return;
            }

            AdaPaymentResultDto adaPaymentResultDto = new AdaPaymentResultDto();
            adaPaymentResultDto.setOrderId(orderPayResp.getOrderId());
            if (AdaPayEnums.SUCCEEDED.getStatus().equals(status)) {
                adaPaymentResultDto.setPayStatus(PayStateEnums.PAID.getStatus());
                String payTime = jsonObject.get(AdaPayConstant.END_TIME).toString();
                if (StrUtil.isNotBlank(payTime)) {
                    adaPaymentResultDto.setPayTime(DateUtil.parse(payTime, AdaPayConstant.TIME_FORMAT));
                } else {
                    adaPaymentResultDto.setPayTime(new Date());
                }
            } else {
                adaPaymentResultDto.setPayStatus(PayStateEnums.PAY_FAILED.getStatus());
            }
            adaPaymentResultDto.setPayAmount(orderPayResp.getPayAmount());
            adaPaymentResultDto.setBankCode(orderPayResp.getBankCode());
            adaPaymentResultDto.setErrorMsg(errorMsg);
            adaPaymentResultDto.setPayId(payId);
            adaPaymentResultDto.setOutTransId(outTransId);

            payOrderService.updateAndSendSyncPayment(adaPaymentResultDto, true);
        }
    }
}
