package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.ExceptionOrderService;
import com.sankuai.shangou.seashop.order.core.service.model.order.ExceptionOrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryExceptionOrderParamBo;
import com.sankuai.shangou.seashop.order.thrift.core.ExceptionOrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ExceptionOrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryExceptionOrderReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/exceptionOrderQuery")
public class ExceptionOrderQueryController implements ExceptionOrderQueryFeign {

    @Resource
    private ExceptionOrderService exceptionOrderService;

    @PostMapping(value = "/pageList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ExceptionOrderInfoDto>> pageList(@RequestBody QueryExceptionOrderReq queryReq) throws TException {
        log.info("【异常订单】分页查询异常订单, 请求参数={}", queryReq);
        return ThriftResponseHelper.responseInvoke("pageList", queryReq, func -> {
            // 参数对象转换
            QueryExceptionOrderParamBo createOrderBo = JsonUtil.copy(queryReq, QueryExceptionOrderParamBo.class);

            // 业务逻辑处理
            BasePageResp<ExceptionOrderInfoBo> page = exceptionOrderService.pageList(queryReq.buildPage(), createOrderBo);
            return PageResultHelper.transfer(page, ExceptionOrderInfoDto.class);
        });
    }
}
