package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.enums.OrderResultCodeEnum;
import com.sankuai.shangou.seashop.order.common.remote.PayRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.pay.CreateRefundBo;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundRecordService;
import com.sankuai.shangou.seashop.order.core.service.assit.BizNoGenerator;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefundRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Finance;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.OrderRefundRecordStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ExcessPaymentRefundReq;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessStatusTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/8/008
 * @description:
 */
@Service
@Slf4j
public class OrderRefundRecordServiceImpl implements OrderRefundRecordService {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private OrderRefundRecordRepository orderRefundRecordRepository;
    @Resource
    private PayRemoteService payRemoteService;
    @Resource
    private BizNoGenerator bizNoGenerator;
    @Resource
    private FinanceRepository financeRepository;

    /**
     * 可以退款的订单状态
     */
    private static final List<Integer> ORDER_REFUND_STATUS_LIST = Arrays.asList(
            OrderStatusEnum.UNDER_SEND.getCode(),
            OrderStatusEnum.UNDER_RECEIVE.getCode(),
            OrderStatusEnum.CLOSED.getCode(),
            OrderStatusEnum.FINISHED.getCode());

    private static final List<Integer> REFUND_RECORD_STATUS_LIST = Arrays.asList(
            RefundPayStatusEnum.UN_PAY.getCode(),
            RefundPayStatusEnum.PAY_SUCCESS.getCode());

    @Override
    public void excessPaymentRefund(ExcessPaymentRefundReq request) {
        log.info("【退款接口调用】超额支付退款, request={}", request);

        Order order = orderRepository.getByOrderId(request.getOrderId());
        if (null == order) {
            throw new BusinessException(OrderResultCodeEnum.ORDER_NOT_EXIST.getCode(), OrderResultCodeEnum.ORDER_NOT_EXIST.getMsg());
        }

        if (!ORDER_REFUND_STATUS_LIST.contains(order.getOrderStatus())) {
            // 判断订单状态是否可以退款
            throw new BusinessException(OrderResultCodeEnum.ORDER_STATUS_NOT_ALLOW_REFUND.getCode(), OrderResultCodeEnum.ORDER_STATUS_NOT_ALLOW_REFUND.getMsg());
        }

        OrderPayRecord orderPayRecord = orderPayRecordRepository.getByOrderIdAndPayNo(order.getOrderId(), request.getChannelId(), PayStatusEnum.PAY_SUCCESS.getCode());
        if (null == orderPayRecord) {
            // 判断是否有正常支付的记录
            throw new BusinessException(OrderResultCodeEnum.ORDER_PAY_RECORD_NOT_EXIST.getCode(), OrderResultCodeEnum.ORDER_PAY_RECORD_NOT_EXIST.getMsg());
        }

        BigDecimal totalAmount = order.getTotalAmount();
        BigDecimal orderAmount = orderPayRecord.getOrderAmount();
        if (totalAmount.compareTo(orderAmount) >= 0) {
            // 判断是否有可退款的金额
            throw new BusinessException(OrderResultCodeEnum.ORDER_REFUND_AMOUNT_NOT_EXIST.getCode(), OrderResultCodeEnum.ORDER_REFUND_AMOUNT_NOT_EXIST.getMsg());
        }

        // 已完成或退款中的金额
        BigDecimal oldTotalRefundAmount = BigDecimal.ZERO;
        // 查询已退金额（理论上只退一次，但目前保留了多次退款的逻辑）
        List<OrderRefundRecord> orderRefundRecords = orderRefundRecordRepository.listByRelateIdAndStatus(orderPayRecord.getId(), REFUND_RECORD_STATUS_LIST);
        if (CollUtil.isNotEmpty(orderRefundRecords)) {
            oldTotalRefundAmount = orderRefundRecords.stream().map(OrderRefundRecord::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        }

        // 退款金额
        BigDecimal refundAmount = request.getRefundAmount();

        // 判断退款金额是否超过可退金额
        if (totalAmount.add(oldTotalRefundAmount).add(refundAmount).compareTo(orderAmount) > 0) {
            throw new BusinessException(OrderResultCodeEnum.ORDER_REFUND_AMOUNT_GT_EXCESS_AMOUNT.getCode(), OrderResultCodeEnum.ORDER_REFUND_AMOUNT_GT_EXCESS_AMOUNT.getMsg());
        }

        String recordNo = bizNoGenerator.generateRefundRecordNo();

        // 调用封装
        CreateRefundBo createRefundBo = new CreateRefundBo();
        createRefundBo.setRefundNo(recordNo);
        createRefundBo.setOriginPayNo(orderPayRecord.getBatchNo());
        createRefundBo.setRefundAmount(refundAmount);
        createRefundBo.setBusinessStatusType(BusinessStatusTypeEnum.COMPENSATION.getType());

        // 退款记录
        OrderRefundRecord orderRefundRecord = new OrderRefundRecord();
        orderRefundRecord.setRelateId(orderPayRecord.getOrderId());
        orderRefundRecord.setPayNo(orderPayRecord.getBatchNo());
        orderRefundRecord.setPayChannelNo(order.getGatewayOrderId());
        orderRefundRecord.setRefundAmount(refundAmount);
        orderRefundRecord.setRefundNo(recordNo);
        orderRefundRecord.setBusinessType(BusinessTypeEnum.ORDER.getType());
        orderRefundRecord.setOperatorId(request.getOperatorId());
        orderRefundRecord.setOperatorName(request.getOperatorName());

        try {
            String refund = payRemoteService.createRefund(createRefundBo);
            orderRefundRecord.setRefundStatus(OrderRefundRecordStatusEnum.REFUNDING.getCode());
            orderRefundRecord.setErrMsg(refund);
            orderRefundRecordRepository.save(orderRefundRecord);
        } catch (BusinessException e) {
            orderRefundRecord.setRefundStatus(OrderRefundRecordStatusEnum.REFUND_FAIL.getCode());
            orderRefundRecord.setErrMsg(e.getMessage());
            orderRefundRecordRepository.save(orderRefundRecord);
            throw e;
        } catch (Exception e) {
            log.error("【退款接口调用】超额支付退款, request={}", request, e);
            orderRefundRecord.setRefundStatus(OrderRefundRecordStatusEnum.REFUND_FAIL.getCode());
            orderRefundRecord.setErrMsg(e.getMessage());
            orderRefundRecordRepository.save(orderRefundRecord);
            throw e;
        }

        log.info("【退款接口调用】超额支付退款, orderRefundRecord={}", orderRefundRecord);
    }

    @Override
    public void updateObj(OrderRefundRecord orderRefundRecord) {

        Finance finance = null;

        // 如果退款成功的，需要更新财务中间表
        if (RefundPayStatusEnum.PAY_SUCCESS.getCode().equals(orderRefundRecord.getRefundStatus())) {

            // 判断之前是否写入过
            Finance oldFinance = financeRepository.getByRefundId(orderRefundRecord.getId(), TransactionTypesEnum.EXCESS_PAYMENT_REFUND.getCode());
            if (null != oldFinance) {
                log.info("【写入财务】超支退款，orderRefundRecord: {}, 财务已存在，不再写入", orderRefundRecord);
            } else {
                OrderPayRecord orderPayRecord = orderPayRecordRepository.getById(orderRefundRecord.getRelateId());
                Order order = orderRepository.getByOrderId(orderPayRecord.getOrderId());

                finance = new Finance();
                finance.setOrderId(orderPayRecord.getOrderId());
                // 非异常时，errMsg存入汇付的退款单号
                finance.setAdapayId(orderRefundRecord.getErrMsg());
                finance.setPayId(orderPayRecord.getOrderId());
                finance.setType(TransactionTypesEnum.EXCESS_PAYMENT_REFUND.getCode());
                finance.setCreateDate(new Date());
                finance.setShopId(order.getShopId());
                finance.setShopName(order.getShopName());
                finance.setUserId(CommonConst.USER_ID_DEFAULT_ZERO);
                finance.setUserName("超支退款");
                finance.setTransactionId("");
                finance.setTotalAmount(orderRefundRecord.getRefundAmount());
                finance.setOrderRefundId(orderRefundRecord.getId());
                finance.setActualPayAmount(orderPayRecord.getPayAmount());
            }
        }

        final Finance finalFinance = finance;

        TransactionHelper.doInTransaction(() -> {
            orderRefundRecordRepository.updateById(orderRefundRecord);
            if (null != finalFinance) {
                financeRepository.save(finalFinance);
            }
        });
    }

}
