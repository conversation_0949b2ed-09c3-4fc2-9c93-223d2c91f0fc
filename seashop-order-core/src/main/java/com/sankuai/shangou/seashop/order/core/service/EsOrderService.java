package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryPlatformOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QuerySellerOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryUserOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.StatsUserPurchaseSkuParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.UserPurchaseSkuStatsBo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EsOrderService {

    /**
     * 构建ES订单
     * <AUTHOR>
     * @param orderId
     * void
     */
    void buildEsOrder(String orderId);

    /**
     * 更新ES中订单的支付信息：交易单号、支付单号
     * @param orderId 订单ID
     * @param payId 支付ID
     * @param tradeNo 交易单号
     */
    void updateEsOrderPayData(String orderId, String payId, String tradeNo);

    /**
     * 商家订单搜索
     * <p>接口和参数构建分角色，这样权限独立，底层实际调用ES可以公用</p>
     * <AUTHOR>
     * @param searchBo 查询参数
     */
    BasePageResp<OrderInfoBo> searchForUser(QueryUserOrderBo searchBo);

    /**
     * 卖家订单搜索
     * <p>接口和参数构建分角色，这样权限独立，底层实际调用ES可以公用</p>
     * <AUTHOR>
     * @param searchBo 查询参数
     */
    BasePageResp<OrderInfoBo> searchForSeller(QuerySellerOrderBo searchBo);

    /**
     * 平台订单搜索
     * <p>接口和参数构建分角色，这样权限独立，底层实际调用ES可以公用</p>
     * <AUTHOR>
     * @param searchBo 查询参数
     */
    BasePageResp<OrderInfoBo> searchForPlatform(QueryPlatformOrderBo searchBo);

    /**
     * 统计商品维度的用户限时购购买数量
     * @param userId 用户ID
     */
    Long countFlashSaleByProduct(Long userId, Long flashSaleId, Long productId);

    /**
     * 统计SKU维度的用户限时购购买数量
     * @param userId 用户ID
     */
    Long countFlashSaleBySku(Long userId, Long flashSaleId, String skuId);

    /**
     * 统计用户商品采购数据
     * @param searchBo 查询参数
     */
    UserPurchaseSkuStatsBo pageUserPurchaseSku(StatsUserPurchaseSkuParamBo searchBo);

    /**
     * 根据商品名称或ID获取订单ID
     * @param productNameOrId 商品名称或ID
     */
    List<String> getOrderIdByProductFromItem(String productNameOrId);

}
