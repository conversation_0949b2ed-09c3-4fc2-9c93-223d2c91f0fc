package com.sankuai.shangou.seashop.pay.core.service.adapay.impl;

import com.sankuai.shangou.seashop.pay.core.mq.publisher.CompanyMemberAuditProducer;
import com.sankuai.shangou.seashop.pay.core.service.adapay.CallBackStrategyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description: 企业账号申请回调处理
 */
@Component("corp_member")
@Slf4j
public class CompanyMemberCallBackServiceImpl implements CallBackStrategyService {

    @Resource
    private CompanyMemberAuditProducer companyMemberAuditProducer;

    @Override
    public void callback(String data) {
        log.info("企业账号申请回调data={}", data);
        companyMemberAuditProducer.sendMessage(data);
    }
}
