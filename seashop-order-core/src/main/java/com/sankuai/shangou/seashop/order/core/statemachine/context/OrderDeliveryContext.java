package com.sankuai.shangou.seashop.order.core.statemachine.context;

import com.sankuai.shangou.seashop.order.core.service.model.order.OrderExpressBo;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class OrderDeliveryContext extends OrderContext {

    private Long shopId;
    /**
     * 是否需要物流
     */
    private Boolean needExpress;
    private List<OrderExpressBo> expressList;

}
