package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.OrderCommentService;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderCommentBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderComment;
import com.sankuai.shangou.seashop.order.thrift.core.OrderCommentCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.AppendOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.DeleteOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.SaveOrderCommentReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/04 10:16
 */
@Slf4j
@RestController
@RequestMapping("/orderCommentCmd")
public class OrderCommentCmdController implements OrderCommentCmdFeign {

    @Resource
    private OrderCommentService orderCommentService;

    @PostMapping(value = "/saveOrderComment", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> saveOrderComment(@RequestBody SaveOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("saveOrderComment", request, req -> {
            req.checkParameter();

            orderCommentService.saveOrderComment(JsonUtil.copy(req, OrderCommentBo.class));
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/appendOrderComment", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> appendOrderComment(@RequestBody AppendOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("appendOrderComment", request, req -> {
            req.checkParameter();

            orderCommentService.appendOrderComment(JsonUtil.copy(req, OrderCommentBo.class));
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/deleteOrderCommentForPlatForm", consumes = "application/json")
    @ExaminProcess(processModel = ExaminModelEnum.ORDER,
            processType = ExaProEnum.MOVE,
            dto = DeleteOrderCommentReq.class,
            entity = OrderComment.class,
            actionName = "平台删除订单评价")
    public ResultDto<BaseResp> deleteOrderCommentForPlatForm(@RequestBody DeleteOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteOrderComment", request, req -> {
            req.checkParameter();

            orderCommentService.deleteOrderCommentForPlatForm(req.getId());
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/deleteOrderCommentForBuyer", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> deleteOrderCommentForBuyer(@RequestBody DeleteOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteOrderCommentForUser", request, req -> {
            req.checkForUser();

            orderCommentService.deleteOrderCommentForBuyer(req);
            return BaseResp.of();
        });
    }
}
