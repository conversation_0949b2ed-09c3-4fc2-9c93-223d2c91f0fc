package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.ProductCommentService;
import com.sankuai.shangou.seashop.order.core.service.model.order.ReplyProductCommentBo;
import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.HideProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.ReplyProductCommentReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:32
 */
@Slf4j
@RestController
@RequestMapping("/productCommentCmd")
public class ProductCommentCmdController implements ProductCommentCmdFeign {

    @Resource
    private ProductCommentService productCommentService;

    @PostMapping(value = "/hideProductCommentForPlatForm", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> hideProductCommentForPlatForm(@RequestBody HideProductCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("hideProductCommentForPlatForm", request, req -> {
            req.checkParameter();

            productCommentService.hideProductCommentForPlatForm(req);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/replyProductCommentForSeller", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> replyProductCommentForSeller(@RequestBody ReplyProductCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("replyProductCommentForPlatForm", request, req -> {
            req.checkParameter();

            productCommentService.replyProductCommentForSeller(JsonUtil.copy(req, ReplyProductCommentBo.class));
            return BaseResp.of();
        });
    }
}
