package com.sankuai.shangou.seashop.pay.core.assist.pay.model;

import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/03 17:07
 */
@Data
public class RefundNotifyResult {

    /**
     * 第三方的退款id
     */
    private String channelRefundId;

    /**
     * 内部的退款id
     */
    private String reverseId;

    /**
     * 退款状态
     */
    private String refundStatus;

    /**
     * 退款时间
     */
    private Date refundDate;

    /**
     * 退款是否成功
     */
    private Boolean success;

    /**
     * 返回给第三方的结果
     */
    private Object thirdResult;

}
