package com.sankuai.shangou.seashop.order.core.service.model.stats;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SellerIndexTradeStatsBo {

    /**
     * 全部订单数
     */
    private Long orderCount;
    /**
     * 待付款订单数量
     */
    private Long underPayOrderCount;
    /**
     * 待发货订单数量
     */
    private Long underDeliveryOrderCount;
    /**
     * 待处理 退款单数量
     */
    private Long underDealRefundCount;
    /**
     * 待处理 退货单数量
     */
    private Long underDealReturnCount;
    /**
     * 待处理 投诉单数量
     */
    private Long underDealComplaintCount;
    /**
     * 待回复评论数
     */
    private Long underReplyCommentCount;
    /**
     * 昨日访客数
     */
    private Long yesterdayVisitCount;
    /**
     * 昨日下单数
     */
    private Long yesterdayOrderCount;
    /**
     * 昨日付款数
     */
    private Long yesterdayPayCount;
    /**
     * 商品总评价数
     */
    private Long totalProductCommentCount;

    public static SellerIndexTradeStatsBo defaultZero() {
        SellerIndexTradeStatsBo bo = new SellerIndexTradeStatsBo();
        bo.setOrderCount(0L);
        bo.setUnderDealReturnCount(0L);
        bo.setUnderDealComplaintCount(0L);
        bo.setUnderDealRefundCount(0L);
        bo.setUnderDeliveryOrderCount(0L);
        bo.setUnderPayOrderCount(0L);
        bo.setUnderReplyCommentCount(0L);
        bo.setYesterdayOrderCount(0L);
        bo.setYesterdayPayCount(0L);
        bo.setYesterdayVisitCount(0L);
        bo.setTotalProductCommentCount(0L);
        return bo;
    }

}
