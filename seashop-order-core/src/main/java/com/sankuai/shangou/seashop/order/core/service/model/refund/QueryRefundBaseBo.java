package com.sankuai.shangou.seashop.order.core.service.model.refund;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class QueryRefundBaseBo extends BasePageReq {

    /**
     * 申请开始时间
     */
    private Date applyTimeStart;
    /**
     * 申请结束时间
     */
    private Date applyTimeEnd;

}
