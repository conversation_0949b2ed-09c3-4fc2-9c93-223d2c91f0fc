package com.sankuai.shangou.seashop.pay.core.mq.publisher;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.pay.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentResultDto;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description: 订单支付状态变更生产者
 */
@Slf4j
@Service
public class OrderPayStatusChangeProducer {

    /**
     * topic：seashop_order_pay_status_change_topic
     *
     * group：seashop_order_pay_status_change_consumer
     *
     * 发送端： com.sankuai.sgb2b.seashop.pay
     *
     * 消费端：com.sankuai.sgb2b.seashop.trade
     */

//    @MafkaProducer(namespace = MafkaConstant.DEFAULT_NAMESPACE, topic = MafkaConstant.ORDER_PAY_STATUS_CHANGE_TOPIC)
//    private IProducerProcessor producerProcessor;

    @Resource
    private DefaultRocketMq defaultRocketMq;

    public void sendMessage(AdaPaymentResultDto adaPaymentResultDto) {
        String msg = JSONUtil.toJsonStr(adaPaymentResultDto);
        log.info("订单支付状态变更生产者发送消息-adaPaymentResultDto：{}", msg);
        try {
//            ProducerResult producerResult = producerProcessor.sendMessage(JSONUtil.toJsonStr(adaPaymentResultDto));
            defaultRocketMq.convertAndSend(MafkaConst.TOPIC_ORDER_PAY_NOTIFY, msg);
            log.info("订单支付状态变更生产者发送消息成功");
        } catch (Exception e) {
            log.error("订单支付状态变更生产者发送消息失败-adaPaymentResultDto：{}", msg, e);
            throw new SystemException("订单支付状态变更生产者发送消息失败");
        }
    }
}
