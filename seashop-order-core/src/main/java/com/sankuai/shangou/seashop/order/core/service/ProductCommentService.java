package com.sankuai.shangou.seashop.order.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductCommentSummaryBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ReplyProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShopCommentSummaryBo;
import com.sankuai.shangou.seashop.order.thrift.core.request.HideProductCommentReq;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:35
 */
public interface ProductCommentService {

    /**
     * 查询商品评价（平台端）
     *
     * @param pageParam 分页参数
     * @param queryBo   查询条件
     * @return 评价数据
     */
    BasePageResp<ProductCommentBo> queryProductCommentForPlatform(BasePageParam pageParam, QueryProductCommentBo queryBo);

    /**
     * 查询商品评价（卖家端）
     *
     * @param pageParam 分页参数
     * @param queryBo   查询条件
     * @return 评价数据
     */
    BasePageResp<ProductCommentBo> queryProductCommentForSeller(BasePageParam pageParam, QueryProductCommentBo queryBo);

    /**
     * 查询商品评价（商城端）
     *
     * @param pageParam 分页参数
     * @param queryBo   查询条件
     * @return 评价数据
     */
    BasePageResp<ProductCommentBo> queryProductCommentForMall(BasePageParam pageParam, QueryProductCommentBo queryBo);

    /**
     * 隐藏商品评价（平台端）
     *
     * @param hideProductCommentReq 隐藏评价入参
     */
    void hideProductCommentForPlatForm(HideProductCommentReq hideProductCommentReq);

    /**
     * 回复商品评价（商家端）
     *
     * @param commentBo 回复内容
     */
    void replyProductCommentForSeller(ReplyProductCommentBo commentBo);

    /**
     * 根据订单id查询商品评价集合
     *
     * @param orderId 订单id
     * @return 评价数据集合
     */
    List<ProductCommentBo> queryProductCommentByOrderId(String orderId);

    /**
     * 根据商品id查询商品评价汇总信息
     *
     * @param productId 商品id
     * @return 评价汇总信息
     */
    ProductCommentSummaryBo queryProductCommentSummary(Long productId);

    /**
     * 根据店铺id查询店铺评价汇总信息
     *
     * @param shopId 店铺id
     * @return 评价汇总信息
     */
    ShopCommentSummaryBo queryShopCommentSummary(Long shopId);

    /**
     * 查询平台控制台的评论汇总
     *
     * @return 评价汇总信息
     */
    ShopCommentSummaryBo queryMCommentSummary();

    /**
     * 构建商品评论es
     *
     * @param productCommentId 商品评论id
     */
    void buildEsProductComment(Long productCommentId);
}
