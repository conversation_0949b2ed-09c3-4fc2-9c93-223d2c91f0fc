package com.sankuai.shangou.seashop.order.core.statemachine.action;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.cola.statemachine.Action;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderMessagePublisher;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderBizAssist;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderEvent;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;

/**
 * 确认收货
 * <AUTHOR>
 */
@Service
public class ConfirmReceiveAction extends BaseAction implements Action<OrderStatusEnum, OrderEvent, OrderContext> {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderBizAssist orderBizAssist;
    @Resource
    private OrderMessagePublisher orderMessagePublisher;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void executeStatusChange(OrderContext context, Order dbOrder) {
        Date finishTime = new Date();
        int cnt = orderRepository.updateOrderReceived(dbOrder.getOrderId(), OrderStatusEnum.UNDER_RECEIVE.getCode(),
                OrderStatusEnum.FINISHED.getCode(), finishTime, null);
        if (cnt != 1) {
            throw new BusinessException("订单状态发生改变，请重新刷页面操作");
        }
        // 订单对象设置完成时间，状态机之外的其他业务需要用到
        dbOrder.setFinishDate(finishTime);
        orderBizAssist.addOrderOperationLog(dbOrder.getOrderId(), context.getUserName(), "买家确认收货");
        // 申请MQ，发送订单完成消息，商品服务需要更新商品销量
        orderMessagePublisher.sendOrderChangeMessage(dbOrder.getOrderId(), OrderMessageEventEnum.COMPLETE_ORDER);
    }

    @Override
    protected void validateBizData(OrderContext context, Order dbOrder) {
        if (OrderStatusEnum.FINISHED.getCode().equals(dbOrder.getOrderStatus())) {
            throw new BusinessException("该订单已经确认过");
        }
        if (!OrderStatusEnum.UNDER_RECEIVE.getCode().equals(dbOrder.getOrderStatus())) {
            throw new BusinessException("订单状态发生改变，请重新刷页面操作!");
        }
    }

    @Override
    protected void dealExecuteResult(OrderContext context, ActionResult executeResult) {
        if (!executeResult.isSuccess()) {
            throw new BusinessException("订单状态发生改变，请重新刷页面操作");
        }
    }
}
