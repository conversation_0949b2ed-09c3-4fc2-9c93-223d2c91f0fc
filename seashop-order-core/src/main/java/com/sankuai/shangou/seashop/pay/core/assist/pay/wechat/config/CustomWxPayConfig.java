package com.sankuai.shangou.seashop.pay.core.assist.pay.wechat.config;

import java.nio.charset.StandardCharsets;
import java.security.PrivateKey;
import java.security.cert.X509Certificate;
import java.util.Collections;

import org.apache.commons.lang3.StringUtils;
import org.apache.http.impl.client.CloseableHttpClient;

import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.config.WxPayHttpProxy;
import com.github.binarywang.wxpay.exception.WxPayException;
import com.github.binarywang.wxpay.util.HttpProxyUtils;
import com.github.binarywang.wxpay.v3.WxPayV3HttpClientBuilder;
import com.github.binarywang.wxpay.v3.auth.AutoUpdateCertificatesVerifier;
import com.github.binarywang.wxpay.v3.auth.PrivateKeySigner;
import com.github.binarywang.wxpay.v3.auth.WxPayCredentials;
import com.github.binarywang.wxpay.v3.auth.WxPayValidator;

import lombok.extern.slf4j.Slf4j;

/**
 * 重写了sdk 中配置类的方法, v3 请求可以使用p12 证书
 *
 * <AUTHOR>
 * @date : 2023/8/10
 */
@Slf4j
public class CustomWxPayConfig extends WxPayConfig {

    public CustomWxPayConfig() {
        super();
    }

    @Override
    public CloseableHttpClient initApiV3HttpClient() throws WxPayException {
        String serialNo = this.getCertSerialNo();
        String apiV3Key = this.getApiV3Key();
        String mchId = this.getMchId();
        if (StringUtils.isBlank(apiV3Key)) {
            throw new WxPayException("请确保apiV3Key值已设置");
        } else {
            try {
                byte[] keyContent = this.getKeyContent();
                X509Certificate certificate = CertUti.getCertificate(keyContent, mchId);
                PrivateKey merchantPrivateKey = CertUti.getPrivateKey(keyContent, mchId);

                if (StringUtils.isBlank(serialNo)) {
                    this.setCertSerialNo(certificate.getSerialNumber().toString(16).toUpperCase());
                }

                WxPayHttpProxy wxPayHttpProxy = this.getWxPayHttpProxy();
                AutoUpdateCertificatesVerifier verifier = new AutoUpdateCertificatesVerifier(new WxPayCredentials(this.getMchId(),
                        new PrivateKeySigner(this.getCertSerialNo(), merchantPrivateKey)), apiV3Key.getBytes(StandardCharsets.UTF_8), this.getCertAutoUpdateTime(), wxPayHttpProxy);
                WxPayV3HttpClientBuilder wxPayV3HttpClientBuilder = WxPayV3HttpClientBuilder.create()
                        .withMerchant(this.getMchId(), this.getCertSerialNo(), merchantPrivateKey).withWechatpay(Collections.singletonList(certificate)).withValidator(new WxPayValidator(verifier));
                HttpProxyUtils.initHttpProxy(wxPayV3HttpClientBuilder, wxPayHttpProxy);
                CloseableHttpClient httpClient = wxPayV3HttpClientBuilder.build();

                this.setApiV3HttpClient(httpClient);
                this.setVerifier(verifier);
                this.setPrivateKey(merchantPrivateKey);
                return httpClient;
            } catch (Exception var15) {
                log.error("v3请求构造异常！", var15);
                throw new WxPayException("v3请求构造异常！", var15);
            }
        }
    }

    private WxPayHttpProxy getWxPayHttpProxy() {
        return StringUtils.isNotBlank(this.getHttpProxyHost()) && this.getHttpProxyPort() > 0 ? new WxPayHttpProxy(this.getHttpProxyHost(), this.getHttpProxyPort(), this.getHttpProxyUsername(), this.getHttpProxyPassword()) : null;
    }
}
