package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description: 支付结果返回对象
 */
@Data
public class AdaPaymentResultDto {

    /**
     * 来源订单ID
     */
    private String orderId;
    /**
     * 订单金额
     */
    private BigDecimal payAmount;
    /**
     * 支付状态 1 成功 2 失败
     */
    private Integer payStatus;
    /**
     * 支付时间
     */
    private Date payTime;

    private String errorMsg;

    private String outTransId;

    /**
     * 汇付支付ID
     */
    private String payId;

    /**
     * 业务类型
     */
    private Integer businessType;

    /**
     * 银行编码
     */
    private String bankCode;
}
