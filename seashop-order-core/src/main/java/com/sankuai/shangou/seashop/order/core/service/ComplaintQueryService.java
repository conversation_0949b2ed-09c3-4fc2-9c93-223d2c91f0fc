package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryMComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryMallComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QuerySellerComplaintReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderRightsReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderComplaintRespPage;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderRightsResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.QueryOrderRightsRespPage;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/21 19:16
 */
public interface ComplaintQueryService {

    // 申请记录分页查询
    QueryOrderComplaintRespPage pageMComplaint(QueryMComplaintReq queryMComplaintReq);

    // 申请记录分页查询
    BasePageResp<QueryOrderComplaintResp> pageSellerComplaint(QuerySellerComplaintReq queryOrderComplaintReq);

    // 申请记录分页查询
    BasePageResp<QueryOrderComplaintResp> pageMallComplaint(QueryMallComplaintReq queryMallComplaintReq);

    // 投诉维权分页查询
    BasePageResp<QueryOrderRightsResp> pageQueryOrderRights(QueryOrderRightsReq queryOrderRightsReq);
}
