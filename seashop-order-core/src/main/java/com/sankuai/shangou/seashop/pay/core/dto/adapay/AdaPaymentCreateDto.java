package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import com.sankuai.shangou.seashop.pay.thrift.core.request.adapay.dto.AdaPayPaymentCreateExpendDto;
import lombok.Builder;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
@Data
@Builder
public class AdaPaymentCreateDto {

    /**
     * 请求订单号，只能为英文、数字或者下划线的一种或多种组合，保证在app_id下唯一
     */
    private String orderNo;
    /**
     * 汇付支付 app_id
     */
    private String appId;

    /**
     * 支付渠道 目前只有 wx_lite
     */
    private String payChannel;

    /**
     * 交易金额，必须大于0，保留两位小数点，如0.10、100.05等
     */
    private String payAmt;

    /**
     * 支付模式，delay- 延时分账模式
     */
    private String payMode;

    /**
     * 商品标题
     */
    private String goodsTitle;
    /**
     * 商品描述信息，微信小程序和微信公众号该字段最大长度42个字符
     */
    private String goodsDesc;

    /**
     * 前端设备信息，详见 设备信息
     */
    private AdaPayPaymentDeviceInfoDto deviceInfo;

    /**
     * 异步通知地址，url为http/https路径，服务器POST回调，URL 上请勿附带参数
     */
    private String notifyUrl;

    /**
     * 渠道扩展参数
     */
    private AdaPayPaymentCreateExpendDto expend;

    /**
     * 订单失效时间，输入格式：yyyyMMddHHmmss，最长失效时间为微信、支付宝：反扫类：3分钟；非反扫类：2小时；云闪付：1天，值为空时默认最长时效时间
     */
    private String timeExpire;
}
