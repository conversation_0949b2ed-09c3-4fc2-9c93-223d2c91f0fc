package com.sankuai.shangou.seashop.pay.core.assist.pay.util;

import java.util.function.Supplier;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.order.common.constant.CacheConst;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.pay.dao.core.model.BasePayConfigModel;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;

import cn.hutool.cache.CacheUtil;
import cn.hutool.cache.impl.LRUCache;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/09/04 11:25
 */
@Component
@Slf4j
public class PaymentCacheUtil {

    /**
     * 本地配置缓存
     */
    private LRUCache<String, Object> CONFIG_CACHE = CacheUtil.newLRUCache(8, 60 * 1000);

    @Resource
    private SquirrelUtil squirrelUtil;


    public String getCacheKey(PaymentChannelEnum paymentChannel) {
        return CacheConst.PAY_CONFIG_CACHE_PREFIX + paymentChannel.getName();
    }

    public String getLockKey(PaymentChannelEnum paymentChannel) {
        return LockConst.PAY_CONFIG_LOCK_PREFIX + paymentChannel.getName();
    }

    public Object getPayConfig(PaymentChannelEnum paymentChannel, Supplier<BasePayConfigModel> getSource) {
        String cacheKey = getCacheKey(paymentChannel);
        Object config = CONFIG_CACHE.get(cacheKey);
        if (config == null) {
            // 读取redis 缓存 todo 暂时不使用redis 缓存, 方便调试
            // config = squirrelUtil.get(cacheKey);
            if (config == null) {
                // redis 中没有加锁后读取db
                config = LockHelper.lock(cacheKey, 30, () -> {
                    Object dbConfig = getSource.get();
                    // squirrelUtil.set(cacheKey, dbConfig);
                    return dbConfig;
                });
            }
            CONFIG_CACHE.put(cacheKey, config);
        }
        return config;
    }

    public void removePayConfig(PaymentChannelEnum paymentChannel) {
        try {
            String cacheKey = getCacheKey(paymentChannel);
            squirrelUtil.deleteKey(cacheKey);
            CONFIG_CACHE.remove(cacheKey);
            log.info("【支付】删除支付配置缓存成功, paymentChannel={}", paymentChannel);
        } catch (Exception e) {
            log.error("【支付】删除支付配置缓存失败, paymentChannel={}", paymentChannel, e);
        }
    }

}
