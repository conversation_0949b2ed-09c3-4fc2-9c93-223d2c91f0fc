package com.sankuai.shangou.seashop.order.core.service.model.stats;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class UserPurchaseSkuStatsSummaryBo {

    /**
     * 采购总金额
     */
    private BigDecimal totalAmount;
    /**
     * 订单总数量
     */
    private Long orderCount;
    /**
     * sku总数量
     */
    private Long skuQuantitySum;

    public static UserPurchaseSkuStatsSummaryBo defaultZero() {
        UserPurchaseSkuStatsSummaryBo summary = new UserPurchaseSkuStatsSummaryBo();
        summary.setTotalAmount(BigDecimal.ZERO);
        summary.setOrderCount(0L);
        summary.setSkuQuantitySum(0L);
        return summary;
    }

}
