package com.sankuai.shangou.seashop.order.core.service;

import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.order.core.service.model.stats.PlatformStatsTradeDataBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.SellerIndexTradeStatsBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.ShopOrderCountStatsBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.StatsShopTopNSaleProductParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.stats.TopProductSaleStatsBo;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryProductBuyCountReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.ProductBuyCountResp;

/**
 * <AUTHOR>
 */
public interface OrderStatsService {

    /**
     * 平台交易数据统计
     * @return 平台首页交易数据统计
     */
    PlatformStatsTradeDataBo statsPlatformIndexTradeData();

    /**
     * 供应商首页交易数据统计
     * @param shopId 店铺ID
     * @return 供应商首页交易数据统计
     */
    SellerIndexTradeStatsBo statsSellerIndexTradeData(Long shopId);

    /**
     * 统计销售排名前N的商品
     * @param statsParam 查询参数
     * @return 用户采购商品数量
     */
    TopProductSaleStatsBo statsTopNSaleProduct(StatsShopTopNSaleProductParamBo statsParam);

    /**
     * 查询支付时间小于指定时间，且状态为未发货的订单，用于定时任务进行短信提醒
     * 定时任务执行时有个时间点，减去交易设置的发货提醒时间，如果支付时间小于这个时间，说明超过了发货提醒时间，需要提醒
     * @param lessThanDate 支付时间需要小于的时间
     */
    List<ShopOrderCountStatsBo> searchPaidDateLessThanAndNotDeliver(Date lessThanDate);

    /**
     * 统计用户购买商品数量
     * 调用ES获取用户购买商品数量，虽然有延迟，但是用户的购买行为间隔理论上不会太短，延迟应该可以接受
     * @param queryReq 查询参数
     */
    List<ProductBuyCountResp> searchUserProductBuyCount(QueryProductBuyCountReq queryReq);

    /**
     * 查询最近下单的收货地址
     * @param userId 用户ID
     * @return 最近下单的收货地址
     */
    ShippingAddressDto getNearOrderAddress(Long userId);
}
