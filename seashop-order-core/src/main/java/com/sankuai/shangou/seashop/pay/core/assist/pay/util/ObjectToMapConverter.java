package com.sankuai.shangou.seashop.pay.core.assist.pay.util;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;

import cn.hutool.core.bean.BeanUtil;

/**
 * <AUTHOR>
 * @date 2024/09/02 18:58
 */
@Component
public class ObjectToMapConverter {

    /**
     * 使用将一个对象转换为Map<String, String>。
     *
     * @param obj 要转换的对象
     * @return 包含对象属性名和属性值的Map
     */
    public static Map<String, String> objectToStringMap(Object obj) {
        Map<String, Object> beanMap = BeanUtil.beanToMap(obj);
        Map<String, String> result = new HashMap<>();

        for (Map.Entry<String, Object> entry : beanMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof String) {
                result.put(key, (String) value);
                continue;
            }
            // 判断value 对象是否可以转json
            result.put(key, value == null ? null : JsonUtil.toJsonString(value));
        }

        return result;
    }

}
