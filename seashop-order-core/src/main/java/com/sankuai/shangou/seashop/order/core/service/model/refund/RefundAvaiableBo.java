package com.sankuai.shangou.seashop.order.core.service.model.refund;

import java.math.BigDecimal;

import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RefundAvaiableBo {

    private Long orderItemId;

    private Long productId;

    private Long skuAutoId;

    private String skuCode;

    private String skuId;

    private Long canRefundQuantity;

    private BigDecimal canRefundAmount;

    private RefundStatusEnum status;

    private Long refundQuantity;
    /**
     * 商品数量
     */
    private Long totalQuantity;
    /**
     * 分摊后的商品金额
     */
    private BigDecimal realTotalAmount;

}
