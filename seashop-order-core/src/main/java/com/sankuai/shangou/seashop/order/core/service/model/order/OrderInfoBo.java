package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.order.thrift.core.response.order.CustomServiceDto;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderInfoBo {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 牵牛花订单号
     */
    private String sourceOrderId;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 用户手机号
     */
    private String userPhone;
    /**
     * 用户名称
     */
    private String userName;
    /**
     * 用户昵称
     */
    private String nick;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 下单时间
     */
    private Date orderDate;
    /**
     * 商品总数量
     */
    private Long productQuantity;
    /**
     * 商品总金额，纯商品金额
     */
    private BigDecimal productTotalAmount;
    /**
     * 订单实付金额，支付金额
     */
    private BigDecimal totalAmount;
    /**
     * 订单实收金额(订单实付-退款金额)
     */
    private BigDecimal actualPayAmount;
    /**
     * 订单状态。1：待付款，2：待发货，3：待收货，4：已关闭，5：已完成，6：支付中
     */
    private Integer orderStatus;
    private String orderStatusDesc;
    /**
     * 订单商品列表
     */
    private List<OrderItemInfoBo> itemList;

    /**
     * 预计完成时间。待收货状态才有，显示自动确认收货时间
     */
    private String estimateCompleteTime;
    /**
     * 优惠券金额
     */
    private BigDecimal couponAmount;
    /**
     * 运费金额
     */
    private BigDecimal freight;
    /**
     * 改运费时备份的运费
     */
    private BigDecimal backupFreight;
    /**
     * 运费修改描述
     */
    private String freightUpdateDesc;
    /**
     * 税费金额
     */
    private BigDecimal tax;
    /**
     * 买家留言
     */
    private String userRemark;
    /**
     * 折扣金额
     */
    private BigDecimal discountAmount;
    /**
     * 满减金额
     */
    private BigDecimal moneyOffAmount;
    /**
     * 总的改价金额
     */
    private BigDecimal totalUpdatedAmount;
    /**
     * 卖家备注
     */
    private String sellerRemark;
    /**
     * 卖家备注标记
     */
    private Integer sellerRemarkFlag;
    /**
     * 支付渠道
     */
    private String payChannelName;
    /**
     * 是否有售后
     */
    private Boolean hasRefund;
    /**
     * 是否订单级别的售后
     */
    private Boolean whetherOrderLevelRefund;
    /**
     * 售后ID
     */
    private Long refundId;
    /**
     * 售后状态。
     */
    private Integer refundStatus;
    /**
     * 售后状态描述
     */
    private String refundStatusDesc;
    /**
     * 是否可以取消售后。用于控制前端【取消售后】按钮显示
     * 1. 发起了售后，订单退款，或仅退款，且是待供应商审核或待平台审核时，显示
     * 2. 发起了售后，退货退款时，待供应商审核或待买家寄货时，显示
     */
    private Boolean showCancelRefundBtn = false;
    /**
     * 是否显示【退款中】状态文案
     * 订单退款，审核中时显示
     */
    private Boolean showRefundingDesc = false;
    /**
     * 是否显示【退货/退款中】状态文案
     * 整单退，审核中时显示
     */
    private Boolean showRefundingAndReturnDesc = false;
    /**
     * 是否显示【退款被拒】按钮
     * 订单退款，供应商拒绝或者平台驳回
     */
    private Boolean showRefundingRejectBtn = false;
    /**
     * 是否显示【退货/退款被拒】按钮
     * 整单退，供应商拒绝或者平台驳回
     */
    private Boolean showRefundingAndReturnRejectBtn = false;
    /**
     * 用户取消了售后
     * 是否显示【已取消售后申请】按钮，与 showCancelRefundBtn 有区别，显示该按钮可以重新申请
     */
    private Boolean showCanceledRefundBtn = false;
    /**
     * 是否有审核中的售后，包括 待平台确认、待供应商审核
     */
    private Boolean hasAuditingRefund = false;
    /**
     * 下单平台。0:PC;2:小程序
     */
    private Integer platform;
    /**
     * 下单平台描述
     */
    private String platformDesc;
    /**
     * 支付时间
     */
    private Date payDate;
    /**
     * 完成时间
     */
    private Date finishDate;
    /**
     * 支付方式
     */
    private Integer payment;
    /**
     * 支付方式描述
     */
    private String paymentDesc;
    /**
     * 交易单号
     */
    private String gatewayOrderId;
    /**
     * 发货时间
     */
    private Date shippingDate;
    /**
     * 支付方式：1=綫上支付
     */
    private Integer paymentType;
    private String paymentTypeDesc;
    /**
     * 收货信息
     */
    private String shipTo;
    private String cellPhone;
    private Integer regionId;
    private Integer topRegionId;
    private String regionFullName;
    private String address;
    private OrderInvoiceBo orderInvoice;
    /**
     * 是否已过售后维权期，简单的按钮，前端需要根据状态和这个字段综合判断是否显示
     */
    private Boolean hasOverAfterSales;
    /**
     * 剩余支付时间，单位毫秒，待付款、支付中状态下显示
     */
    private Long remainPayTime;
    private String remainPayTimeDesc;
    /**
     * 订单类型。{@link com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderTypeEnum}
     */
    private Integer orderType;
    private String orderTypeDesc;
    ///**
    // * 订单来源。{@link com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderSourceEnum}
    // */
    //private Integer orderSource;
    //private String orderSourceDesc;
    /**
     * 客服列表
     */
    private List<CustomServiceDto> customServiceList;
    /**
     * 订单评价状态
     */
    private Integer commentStatus;
    private String commentStatusDesc;
    /**
     * 延长收货天数
     */
    private Integer receiveDelay;

    private Boolean wxSend;

    /**
     * 发票地址
     */
    private String invoiceUrl;

}
