package com.sankuai.shangou.seashop.pay.core.task;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.huifu.adapay.model.PaymentReverse;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderInfoDto;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryPlatformOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayRecordResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositPayReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositPayResp;
import com.sankuai.shangou.seashop.pay.common.constant.AdaPayConstant;
import com.sankuai.shangou.seashop.pay.common.enums.AdaPayEnums;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseQueryDto;
import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseResultDto;
import com.sankuai.shangou.seashop.pay.core.remote.OrderQueryRemoteService;
import com.sankuai.shangou.seashop.pay.core.service.PayOrderService;
import com.sankuai.shangou.seashop.pay.core.service.PayService;
import com.sankuai.shangou.seashop.pay.core.service.ReverseOrderService;
import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ReverseOrder;
import com.sankuai.shangou.seashop.pay.dao.core.repository.ReverseOrderRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseTypeEnums;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description: 支付服务Crane定时任务
 */
@Slf4j
@Component
public class PayCraneTask {

    @Resource
    private PayOrderService payOrderService;
    @Resource
    private ReverseOrderRepository reverseOrderRepository;
    @Resource
    private ReverseOrderService reverseOrderService;

    @Resource
    private OrderQueryRemoteService orderQueryRemoteService;

    @Resource
    private PayService payService;
    /**
     * 未支付订单查询
     * 1、查询未支付的订单
     * 2、调用支付渠道查询订单支付状态
     * 按状态取
     */
    //TODO 调度执行
//    @Crane("UnpaidOrderPayStatusQueryTask")
    @XxlJob("unpaidOrderPayStatusQueryTask")
    @XxlRegister(cron = "0 0/10 * * * ?",
            author = "snow",
            jobDesc = "未支付订单查询")
    public void unpaidOrderPayStatusQueryTask() {
        log.info("UnpaidOrderPayStatusQueryTask start");
        // 第一步，查出pay_order_pay未支付的订单
        List<OrderPay> unPaidList = payOrderService.getUnPaidList();
        if (CollUtil.isEmpty(unPaidList)) {
            log.info("待支付查询列表 unPaidList is empty -- UnpaidOrderPayStatusQueryTask end");
            return;
        }
        // 定义一个需要更新pay_order_pay未支付的订单状态的List,更新为2支付失败
        List<String> updateTwoOrderIdList = Lists.newArrayList();
        // 定义一个需要更新pay_order_pay未支付的订单状态的List,更新为1支付成功
        List<String> updateOneOrderIdList = Lists.newArrayList();
        List<String> orderIdList = unPaidList.stream().map(OrderPay::getOrderId).collect(Collectors.toList());
        // 分为B开头的保证金的支付单和普通订单的支付单
        List<String> notBBatchNoList = orderIdList.stream().filter(v -> !v.contains("B")).collect(Collectors.toList());
        // 普通订单逻辑
        if(!CollectionUtil.isEmpty(notBBatchNoList)){
            // 这个订单ID不是订单ID，是order_pay_record表的batch_no
            List<OrderPayRecordResp> listRecord = orderQueryRemoteService.queryOrderPayRecordList(notBBatchNoList);
            if(!CollectionUtil.isEmpty(listRecord)){
                // 查在order表也是支付中跟待支付的订单
                List<String> notBOrderIdList = listRecord.stream().map(OrderPayRecordResp::getOrderId).collect(Collectors.toList());
                QueryPlatformOrderReq queryReq = new QueryPlatformOrderReq();
                queryReq.setOrderIdList(notBOrderIdList);
                List<OrderInfoDto> orderInfoDtos = orderQueryRemoteService.pageQueryPlatformOrder(queryReq).getData();
                if(CollectionUtil.isEmpty(orderInfoDtos)){
                    updateTwoOrderIdList.addAll(notBOrderIdList);
                } else {
                    // 订单状态(1:待付款,2:待发货,3:待收货,4:已关闭,5:已完成,6:支付中)
                    List<Integer> successPayStatusList = Arrays.asList(2,3,5);
                    List<Integer> failPayStatusList = Arrays.asList(4);
                    List<OrderInfoDto> successPayOrderInfoDtos = orderInfoDtos.stream().filter(v -> successPayStatusList.contains(v.getOrderStatus())).collect(Collectors.toList());
                    List<OrderInfoDto> failPayOrderInfoDtos = orderInfoDtos.stream().filter(v -> failPayStatusList.contains(v.getOrderStatus())).collect(Collectors.toList());
                    if(!CollectionUtil.isEmpty(successPayOrderInfoDtos)){
                        updateOneOrderIdList.addAll(successPayOrderInfoDtos.stream().map(OrderInfoDto::getOrderId).collect(Collectors.toList()));
                    }
                    if(!CollectionUtil.isEmpty(failPayOrderInfoDtos)){
                        updateTwoOrderIdList.addAll(failPayOrderInfoDtos.stream().map(OrderInfoDto::getOrderId).collect(Collectors.toList()));
                    }
                }
            }
        }
        List<String> bOrderIdList = orderIdList.stream().filter(v -> v.contains("B")).collect(Collectors.toList());
        // 过滤出保证金的支付记录,并查询出待支付的
        if(!CollectionUtil.isEmpty(bOrderIdList)){
            CashDepositPayReq cashDepositPayReq = new CashDepositPayReq();
            cashDepositPayReq.setPayIdList(bOrderIdList);
            List<CashDepositPayResp> depositPayList = orderQueryRemoteService.selectCashDepositPay(cashDepositPayReq);
            if(CollectionUtil.isEmpty(depositPayList)){
                updateTwoOrderIdList.addAll(bOrderIdList);
            }else{
                // 支付状态  0: 未支付, 1: 支付成功, 2: 支付失败
                List<CashDepositPayResp> successPayDepositPayList = depositPayList.stream().filter(v -> 1==v.getPayStatus()).collect(Collectors.toList());
                List<CashDepositPayResp> failPayDepositPayList = depositPayList.stream().filter(v -> 2==v.getPayStatus()).collect(Collectors.toList());
                if(!CollectionUtil.isEmpty(successPayDepositPayList)){
                    updateOneOrderIdList.addAll(successPayDepositPayList.stream().map(CashDepositPayResp::getPayId).collect(Collectors.toList()));
                }
                if(!CollectionUtil.isEmpty(failPayDepositPayList)){
                    updateTwoOrderIdList.addAll(failPayDepositPayList.stream().map(CashDepositPayResp::getPayId).collect(Collectors.toList()));
                }
            }
        }
        // 第一步，根据order表，finance_cash_deposit_pay表更新pay_order_pay状态
        if(!CollectionUtil.isEmpty(updateOneOrderIdList)){
            log.info("PayCraneTask更新成支付成功的订单号有：updateOneOrderIdList = {}", JsonUtil.toJsonString(updateOneOrderIdList));
            payOrderService.updateUnPaid(updateOneOrderIdList, 1);
        }
        if(!CollectionUtil.isEmpty(updateTwoOrderIdList)){
            log.info("PayCraneTask更新成支付失败的订单号有：updateTwoOrderIdList = {}", JsonUtil.toJsonString(updateTwoOrderIdList));
            payOrderService.updateUnPaid(updateTwoOrderIdList, 2);
        }
        // 处理待支付的订单
        List<OrderPay> dealUnPaidList =unPaidList.stream().filter(v -> !updateOneOrderIdList.contains(v.getOrderId()) && !updateTwoOrderIdList.contains(v.getOrderId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(dealUnPaidList)) {
            log.info("过滤之后的待支付查询列表 dealUnPaidList is empty -- UnpaidOrderPayStatusQueryTask end");
            return;
        }
        payService.opOrderPayStatus(dealUnPaidList, true);
        log.info("PayCraneTask.UnpaidOrderPayStatusQueryTask end");
    }

    /**
     * 废弃
     * 未支付订单查询
     * 1、查询未支付的订单
     * 2、调用支付渠道查询订单支付状态
     * (30秒到5分钟内的)
     */
//    @Crane("UnpaidOrderPayStatusQryTask")
//    public void UnpaidOrderPayStatusQryTask() {
//        log.info("UnpaidOrderPayStatusQryTask start");
//        Date now = new Date();
//        Date endTime = DateUtil.offsetSecond(now, -30);
//        Date startTime = DateUtil.offsetMinute(now, -5);
//        List<OrderPay> unPaidList = orderPayService.getUnPaidListByTime(startTime, endTime);
//        opOrderPayStatus(unPaidList);
//        log.info("UnpaidOrderPayStatusQryTask end");
//    }

    /**
     * 退款订单状态定时任务查询（未分账的退款订单）
     */
//    @Crane("PayReverseStatusQueryTask")
    @XxlJob("payReverseStatusQueryTask")
    public void payReverseStatusQueryTask() {
        log.info("PayReverseStatusQueryTask start");
        List<ReverseOrder> reverseOrderList = reverseOrderRepository.getReverseIngByType(
                ReverseTypeEnums.REVERSE_NO_SETTLEMENT.getType(), ReverseStateEnums.REVERSE_ING.getStatus());

        if (CollUtil.isEmpty(reverseOrderList)) {
            log.info("退款查询列表 reverseOrderList is empty -- PayReverseStatusQueryTask end");
            return;
        }

        log.info("退款查询列表 reverseOrderList={}", reverseOrderList);
        for (ReverseOrder reverseOrder : reverseOrderList) {
            try {
                if (StrUtil.isBlank(reverseOrder.getChannelRefundId())) {
                    // channelRefundId为空时属于异常订单，在特殊处理逻辑中处理，这里不考虑
                    log.info("退款查询列表 reverseOrder={} channelRefundId is empty", reverseOrder);
                    continue;
                }
                AdaPayReverseQueryDto adaPayReverseQueryDto = AdaPayReverseQueryDto.builder()
                        .reverseId(reverseOrder.getChannelRefundId()).build();
                Map<String, Object> adaPayReverseMap = BeanUtil.beanToMap(adaPayReverseQueryDto, true, true);
                log.info("退款结果请求接受参数={}:", adaPayReverseMap);
                Map<String, Object> returnMap = PaymentReverse.query(adaPayReverseMap);
                log.info("退款结果请求返回参数={}:", returnMap);
                AdaPayReverseResultDto adaPayReverseResultDto = new AdaPayReverseResultDto();
                String status = returnMap.get(AdaPayConstant.STATUS).toString();
                String orderNo = returnMap.get(AdaPayConstant.ORDER_NO).toString();
                String refundedAmt = returnMap.get(AdaPayConstant.REFUNDED_AMT).toString();
                String errorMessage = "";
                if (returnMap.get(AdaPayConstant.ERROR_MSG) != null) {
                    errorMessage = returnMap.get(AdaPayConstant.ERROR_MSG).toString();
                }
                adaPayReverseResultDto.setType(ReverseTypeEnums.REVERSE_NO_SETTLEMENT.getType());
                adaPayReverseResultDto.setRefundId(orderNo);
                adaPayReverseResultDto.setRefundedAmt(refundedAmt);
                adaPayReverseResultDto.setErrorMessage(errorMessage);
                if (AdaPayEnums.PENDING.getStatus().equals(status)) {
                    continue;
                }
                if (AdaPayEnums.SUCCEEDED.getStatus().equals(status)) {
                    adaPayReverseResultDto.setPayStatus(ReverseStateEnums.REVERSE_SUCCESS.getStatus());
                } else {
                    adaPayReverseResultDto.setPayStatus(ReverseStateEnums.REVERSE_ERROR.getStatus());
                }
                reverseOrderService.updateAndSendSyncReverse(adaPayReverseResultDto);
            } catch (Exception e) {
                log.error("PayReverseStatusQueryTask execute error", e);
            }
        }
    }
}
