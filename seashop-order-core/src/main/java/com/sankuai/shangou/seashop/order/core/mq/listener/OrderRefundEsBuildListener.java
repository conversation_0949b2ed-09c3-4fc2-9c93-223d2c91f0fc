package com.sankuai.shangou.seashop.order.core.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.mq.model.DbTableDataChangeMessage;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundTableMessage;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundSearchService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/08/02 11:35
 */

@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_REFUND + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_REFUND_ES_BUILD + "_${spring.profiles.active}")
public class OrderRefundEsBuildListener implements RocketMQListener<MessageExt> {

    @Resource
    private OrderRefundSearchService orderRefundSearchService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【售后ES构建】消息内容为: {}", body);
        try {
            OrderRefundMessage refundMessage = JsonUtil.parseObject(body, OrderRefundMessage.class);
            orderRefundSearchService.buildEsRefund(refundMessage.getRefundId());
        } catch (Exception e) {
            log.error("【mafka消费】【订单售后】处理失败", e);
            // 失败后，重试默认3次，监控发现问题，然后依赖定时任务补偿
            throw new RuntimeException(e);
        }
    }
}
