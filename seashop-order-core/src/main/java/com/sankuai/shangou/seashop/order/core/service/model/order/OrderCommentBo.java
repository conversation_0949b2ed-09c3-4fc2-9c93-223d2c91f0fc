package com.sankuai.shangou.seashop.order.core.service.model.order;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/04 10:19
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderCommentBo {

    /**
     * 订单评价id
     */
    private Long id;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 包装评分 1-5分
     */
    private Integer packMark;

    /**
     * 物流评分 1-5分
     */
    private Integer deliveryMark;

    /**
     * 服务评分 1-5分
     */
    private Integer serviceMark;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 评价时间
     */
    private Date commentDate;

    /**
     * 商品评价
     */
    private List<ProductCommentBo> productCommentList;

}
