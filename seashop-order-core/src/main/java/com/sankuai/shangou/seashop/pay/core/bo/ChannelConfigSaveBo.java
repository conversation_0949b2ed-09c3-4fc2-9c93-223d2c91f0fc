package com.sankuai.shangou.seashop.pay.core.bo;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.Requiredness;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Getter
@Setter
@ToString
@TypeDoc(description = "保存渠道配置请求对象")
public class ChannelConfigSaveBo extends BaseParamReq {

    @FieldDoc(description = "支付渠道 1：汇付天下", requiredness = Requiredness.REQUIRED)
    private Integer paymentChannel;

    @FieldDoc(description = "配置信息列表", requiredness = Requiredness.REQUIRED)
    private List<ChannelConfigBo> configList;

}
