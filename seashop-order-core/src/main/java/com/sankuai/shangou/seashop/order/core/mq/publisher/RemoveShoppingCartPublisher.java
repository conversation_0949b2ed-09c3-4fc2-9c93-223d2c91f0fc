package com.sankuai.shangou.seashop.order.core.mq.publisher;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.mq.model.order.RemoveShoppingCartMessage;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 下单成功后，发送消息给购物车，删除购物车中的商品
 * <p>目前可能存在一个问题，假如一个账号可以多人使用，可能A在下单，B在操作购物车，导致下单删除购物车时把B操作的删除，暂不考虑</p>
 * <AUTHOR>
 */
@Service
@Slf4j
public class RemoveShoppingCartPublisher {

//    @MafkaProducer(namespace = "waimai", topic = MafkaConst.TOPIC_REMOVE_SHOPPING_CART)
//    private IProducerProcessor removeShoppingCartProducer;

    @Resource
    private DefaultRocketMq defaultRocketMq;

    public void sendRemoveShoppingCartMessage(Long userId, List<String> skuIdList) {
        log.info("【mafka生产】【删除购物车】发送删除购物车消息, userId: {}, orderId: {}", userId, JsonUtil.toJsonString(skuIdList));
        // 发送订单检查消息
        SendResult producerResult = null;
        try {
//            producerResult = removeShoppingCartProducer.sendMessage(JsonUtil.toJsonString(new RemoveShoppingCartMessage(userId, skuIdList)));
            producerResult = defaultRocketMq.syncSend(MafkaConst.TOPIC_REMOVE_SHOPPING_CART, JsonUtil.toJsonString(new RemoveShoppingCartMessage(userId, skuIdList)));
            log.info("【mafka生产】【删除购物车】发送删除购物车消息结果: {}", producerResult.toString());
        }
        catch (Exception e) {
            throw new BusinessException("发送消息失败");
        }
        if (!SendStatus.SEND_OK.equals(producerResult.getSendStatus())) {
            throw new BusinessException("发送消息失败");
        }
    }

}
