package com.sankuai.shangou.seashop.pay.core.assist.pay.model;

import java.util.Date;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/03 18:49
 */
@Data
public class RefundConfirmResult {

    /**
     * 第三方的退款id
     */
    private String channelRefundId;

    /**
     * 系统内部的退款id
     */
    private String reverseId;

    /**
     * 退款状态
     */
    private String refundStatus;

    /**
     * 退款时间
     */
    private Date refundDate;

    /**
     * 退款是否成功
     */
    private Boolean success;

}
