package com.sankuai.shangou.seashop.order.core.service.assit.refund;

import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;

import java.util.Date;

/**
 * <AUTHOR>
 */
public class RefundStatusHelper {

    public static boolean refundValid(Boolean hasCancel, Integer sellerAuditStatus, Integer managerAuditStatus) {
        return !Boolean.TRUE.equals(hasCancel) &&
                !RefundAuditStatusEnum.SUPPLIER_REFUSE.getCode().equals(sellerAuditStatus) &&
                !RefundAuditStatusEnum.PLATFORM_REFUSE.getCode().equals(managerAuditStatus);
    }

    public static boolean refundInvalid(Boolean hasCancel, Integer sellerAuditStatus, Integer managerAuditStatus) {
        return Boolean.TRUE.equals(hasCancel) ||
                RefundAuditStatusEnum.SUPPLIER_REFUSE.getCode().equals(sellerAuditStatus) ||
                RefundAuditStatusEnum.PLATFORM_REFUSE.getCode().equals(managerAuditStatus);
    }

    public static boolean refundRejected(Integer sellerAuditStatus, Integer managerAuditStatus) {
        return RefundAuditStatusEnum.SUPPLIER_REFUSE.getCode().equals(sellerAuditStatus) ||
                RefundAuditStatusEnum.PLATFORM_REFUSE.getCode().equals(managerAuditStatus);
    }

    public static boolean refundCompleted(Integer status) {
        return RefundStatusEnum.REFUND_SUCCESS.getCode().equals(status) ||
                RefundStatusEnum.REFUNDING.getCode().equals(status);
    }

    public static boolean hasOverAfterSale(int refundCloseDays, Date orderFinishDate) {
        Date now = new Date();
        Date lastFinishDate = DateUtil.offsetDay(now, -refundCloseDays);
        if (orderFinishDate != null && refundCloseDays != 0) {
            return DateUtil.compare(orderFinishDate, lastFinishDate) < 0;
        }
        return false;
    }

}
