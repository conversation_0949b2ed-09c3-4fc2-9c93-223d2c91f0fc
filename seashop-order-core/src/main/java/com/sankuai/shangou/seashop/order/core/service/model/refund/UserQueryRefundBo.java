package com.sankuai.shangou.seashop.order.core.service.model.refund;

import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundUserQueryTabEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 商家查询售后记录请求参数
 * <AUTHOR>
 */
@Getter
@Setter
public class UserQueryRefundBo extends QueryRefundBaseBo {

    /**
     * 用户信息
     */
    private UserBo user;
    /**
     * 搜索关键字
     */
    private String searchKey;
    /**
     * 商品名称或商品ID
     */
    private String productNameOrId;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 退款单号
     */
    private Long refundId;
    /**
     * 退款状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；5：待平台确认；6：退款成功；7：平台驳回；8：退款中；9：买家取消
     */
    private RefundStatusEnum refundStatus;

    /**
     * 退款状态。1：待供应商审核；2：待买家寄货；3：待供应商收货；4：供应商拒绝；5：待平台确认；6：退款成功；7：平台驳回；8：退款中；9：买家取消
     */
    private List<Integer> refundStatusList;

    /**
     * 订单状态。1:待付款；2：待发货；3：待收货；4：已关闭；5：已完成；6：支付中
     */
    private OrderStatusEnum orderStatus;
    /**
     * 查询类型。1：我申请的退款；2：我申请的退货
     */
    private RefundUserQueryTabEnum tab;

}
