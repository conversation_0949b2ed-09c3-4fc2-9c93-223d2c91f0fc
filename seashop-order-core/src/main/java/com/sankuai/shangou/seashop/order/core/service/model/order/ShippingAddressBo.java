package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

/**
 * 用户收货地址对象
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
public class ShippingAddressBo {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 区域ID
     */
    private Integer regionId;
    /**
     * 区域路径，省市区
     */
    private String regionPath;
    /**
     * 区域全称
     */
    private String regionFullName;
    /**
     * 收货人
     */
    private String shipTo;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 详细地址
     */
    private String addressDetail;
    /**
     * 收货人电话
     */
    private String phone;
    private Long provinceId;
    private String provinceName;
    private Long cityId;
    private String cityName;
    private Long districtId;
    private String districtName;
    private String streetName;

    /**
     * 店铺ID
     */
    private Long shopId;
}
