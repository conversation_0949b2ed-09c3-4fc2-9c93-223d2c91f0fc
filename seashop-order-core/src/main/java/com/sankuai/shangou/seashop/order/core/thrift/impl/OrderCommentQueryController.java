package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.OrderCommentService;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryOrderCommentBo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderCommentDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryOrderCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderCommentDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderCommentResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ShopMarkResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdReq;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/04 10:16
 */
@Slf4j
@RestController
@RequestMapping("/orderCommentQuery")
public class OrderCommentQueryController implements OrderCommentQueryFeign {

    @Resource
    private OrderCommentService orderCommentService;

    @PostMapping(value = "/queryOrderCommentForBuyer", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<OrderCommentResp>> queryOrderCommentForBuyer(@RequestBody QueryOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryOrderCommentForBuyer", request, req -> {
            req.checkForBuyer();

            BasePageResp<OrderCommentBo> pageResult = orderCommentService
                    .queryOrderCommentForBuyer(req.buildPage(), JsonUtil.copy(req, QueryOrderCommentBo.class));
            return PageResultHelper.transfer(pageResult, OrderCommentResp.class);
        });
    }

    @PostMapping(value = "/queryOrderCommentForPlatform", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<OrderCommentResp>> queryOrderCommentForPlatform(@RequestBody QueryOrderCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryOrderCommentForPlatform", request, req -> {
            req.checkParameter();

            BasePageResp<OrderCommentBo> pageResult = orderCommentService
                    .queryOrderCommentForPlatform(req.buildPage(), JsonUtil.copy(req, QueryOrderCommentBo.class));
            return PageResultHelper.transfer(pageResult, OrderCommentResp.class);
        });
    }

    @PostMapping(value = "/queryOrderCommentDetailForBuyer", consumes = "application/json")
    @Override
    public ResultDto<OrderCommentDetailResp> queryOrderCommentDetailForBuyer(@RequestBody QueryOrderCommentDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryOrderCommentDetailForBuyer", request, req -> {
            req.checkForBuyer();

            OrderCommentBo orderCommentBo = orderCommentService.queryOrderCommentDetailForBuyer(JsonUtil.copy(req, QueryOrderCommentBo.class));
            return JsonUtil.copy(orderCommentBo, OrderCommentDetailResp.class);
        });
    }

    @PostMapping(value = "/queryShopMarkByShopId", consumes = "application/json")
    @Override
    public ResultDto<ShopMarkResp> queryShopMarkByShopId(@RequestBody ShopIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopMarkByShopId", request, req -> {
            req.checkParameter();
            return JsonUtil.copy(orderCommentService.queryShopMarkByShopId(req.getShopId()), ShopMarkResp.class);
        });
    }
}
