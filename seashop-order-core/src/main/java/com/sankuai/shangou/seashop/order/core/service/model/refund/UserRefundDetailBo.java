package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class UserRefundDetailBo extends RefundDetailBo {

    /**
     * 是否可以重新申请
     */
    private Boolean canReapply;
    /**
     * 售后步骤
     */
    private List<RefundStepBo> stepList;
    /**
     * 是否明细退，如果为true，重新申请的时候调用明细预览接口，否则调用订单预览接口
     */
    private Boolean whetherItemRefund;

}
