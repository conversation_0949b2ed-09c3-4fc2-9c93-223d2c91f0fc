package com.sankuai.shangou.seashop.pay.core.assist.pay.model;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/02 11:17
 */
@Data
public class PayResult {

    /**
     * 是否请求成功
     */
    private boolean success;

    /**
     * 异常信息
     */
    private String errorMsg;

    /**
     * 支付渠道支付id(不一定有，有的是支付回调才会返回)
     */
    private String channelPayId;

    /**
     * 第三方请求参数
     */
    private Object remoteReq;

    /**
     * 第三方支付返回值
     */
    private Object remoteResp;


}
