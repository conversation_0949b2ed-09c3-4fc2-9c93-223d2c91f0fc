package com.sankuai.shangou.seashop.pay.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.pay.common.constant.LockConstant;
import com.sankuai.shangou.seashop.pay.core.bo.ChannelConfigSaveBo;
import com.sankuai.shangou.seashop.pay.core.service.WxShippingBiz;
import com.sankuai.shangou.seashop.pay.thrift.core.request.GetOrderRequest;
import com.sankuai.shangou.seashop.pay.thrift.core.request.UploadShippingRequest;
import com.sankuai.shangou.seashop.pay.thrift.core.response.DeliveryListResponse;
import com.sankuai.shangou.seashop.pay.thrift.core.service.WxShippingFeign;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/07/15/ $
 * @description:
 */
@RestController
@RequestMapping("/wxshipping")
@Slf4j
public class WxShippingController implements WxShippingFeign {

    @Resource
    private WxShippingBiz wxShippingBiz;

    @GetMapping("/isTradeManaged")
    @Override
    public ResultDto<Boolean> isTradeManaged(@RequestParam("paymentType") Integer paymentType) {
        return ThriftResponseHelper.responseInvoke("isTradeManaged", paymentType, req -> {
            return wxShippingBiz.isTradeManaged(paymentType);
        });
    }

    @PostMapping("/uploadShipping")
    @Override
    public ResultDto<Boolean> uploadShipping(@RequestBody @Validated UploadShippingRequest request) {
        return ThriftResponseHelper.responseInvoke("uploadShipping", request, req -> {
            return wxShippingBiz.uploadShipping(request);
        });
    }

    @PostMapping("/getOrder")
    @Override
    public ResultDto<Boolean> getOrder(@RequestBody @Validated GetOrderRequest request) {
        return ThriftResponseHelper.responseInvoke("getOrder", request, req -> {
            return wxShippingBiz.getOrder(request);
        });
    }

    @GetMapping("/getDeliveryList")
    @Override
    public ResultDto<DeliveryListResponse> getDeliveryList(@RequestParam("paymentType") Integer paymentType) {
        return ThriftResponseHelper.responseInvoke("getDeliveryList", paymentType, req -> {
            return wxShippingBiz.getDeliveryList(paymentType);
        });
    }
}
