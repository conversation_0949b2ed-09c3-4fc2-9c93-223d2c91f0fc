package com.sankuai.shangou.seashop.order.core.service.model.refund;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class RefundDetailBo {

    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 退款id，退款表主键ID
     */
    private Long refundId;
    /**
     * 三方售后单号
     */
    private String sourceRefundId;
    /**
     * 订单号
     */
    private String orderId;
    /**
     * 退款商品id。退明细时有值
     */
    private Long orderItemId;
    /**
     * 售后类型。1：仅退款；2：退货退款
     */
    private Integer refundType;
    /**
     * 售后类型描述
     */
    private String refundTypeDesc;
    /**
     * 退款模式。1：订单退款；2：货品退款；3：退货退款
     */
    private Integer refundMode;
    /**
     * 退款模式描述
     */
    private String refundModeDesc;
    /**
     * 申请退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 订单实付金额，整单退是订单金额，单品退是明细金额
     */
    private BigDecimal payAmount;
    /**
     * 购买数量，整单退是订单所有明细数量，单品退是明细数量
     */
    private Long orderQuantity;
    /**
     * 运费金额
     */
    private BigDecimal freightAmount;
    /**
     * 申请数量，仅退款和退货退款都有值
     */
    private Long applyQuantity;
    /**
     * 退款数量。默认是退货数量，仅退款或者弃货时，为申请数量(售后数量)
     */
    private Long refundQuantity;
    /**
     * 实际退货数量
     */
    private Long returnQuantity;

    /**
     * 退款原因类型
     */
    private String refundReasonDesc;
    /**
     * 退款说明
     */
    private String refundRemark;
    /**
     * 联系人姓名
     */
    private String contactUserName;
    /**
     * 联系人电话
     */
    private String contactUserPhone;
    /**
     * 退款方式。1：原路返回
     */
    private Integer refundPayType;
    /**
     * 退款方式描述
     */
    private String refundPayTypeDesc;
    /**
     * 售后凭证1。最多三张，与数据表保持一致分开
     */
    private String certPic1;
    /**
     * 售后凭证2。最多三张，与数据表保持一致分开
     */
    private String certPic2;
    /**
     * 售后凭证3。最多三张，与数据表保持一致分开
     */
    private String certPic3;
    /**
     * 是否需要退货
     */
    private Boolean hasReturn;
    /**
     * 是否订单全退。前端根据这个字段显示 退运费选项
     */
    private Boolean hasAllReturn;
    /**
     * 是否可以重新申请
     */
    private Boolean canReapply;
    /**
     * 是否可以取消
     */
    private Boolean hasCancel;
    /**
     * 供应商审核状态。
     */
    private Integer sellerAuditStatus;
    /**
     * 供应商审核状态描述
     */
    private String sellerAuditStatusDesc;
    /**
     * 平台审核状态
     */
    private Integer managerConfirmStatus;
    /**
     * 平台审核状态描述
     */
    private String managerConfirmStatusDesc;
    /**
     * 综合的售后状态
     */
    private Integer status;
    /**
     * 综合的售后状态描述
     */
    private String statusDesc;
    /**
     * 供应商备注
     */
    private String sellerRemark;
    /**
     * 平台备注
     */
    private String platformRemark;
    /**
     * 申请时间
     */
    private Date applyDate;
    /**
     * 最后修改时间
     */
    private Date lastModifyTime;
    /**
     * 申请内容
     */
    private String applicant;
    /**
     * 快递公司编码
     */
    private String expressCompanyCode;
    /**
     * 快递公司名称
     */
    private String expressCompanyName;
    /**
     * 快递单号
     */
    private String shipOrderNumber;
    /**
     * 审核状态（sellerAuditStatusDesc,managerConfirmStatus聚合）
     */
    private Integer auditStatus;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 订单状态描述
     */
    private String orderStatusDesc;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 计算营销均摊后的商品单价，realTotalPrice/quantity
     */
    private BigDecimal realSalePrice;

}
