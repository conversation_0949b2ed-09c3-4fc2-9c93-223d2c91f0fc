package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderWayBillBo {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 区域ID
     */
    private Integer regionId;
    /**
     * 区域全称
     */
    private String regionFullName;
    /**
     * 收货人
     */
    private String shipTo;
    /**
     * 收货地址完整路径，包括省市区和用户输入的
     */
    private String addressFullName;
    /**
     * 物流信息
     */
    private List<OrderExpressBo> expressList;

}
