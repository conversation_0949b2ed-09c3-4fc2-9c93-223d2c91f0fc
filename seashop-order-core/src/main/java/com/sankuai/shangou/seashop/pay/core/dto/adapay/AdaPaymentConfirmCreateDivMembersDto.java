package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Getter
@Setter
public class AdaPaymentConfirmCreateDivMembersDto {

    /**
     * 分账用户 Member对象 的 id；若是商户本身时，传入0
     */
    private String memberId;
    /**
     * 分账金额，精确到分，如0.50，1.00等，分账总金额必须等于主交易金额,金额不能为0.00
     */
    private String amount;

    /**
     * 是否手续费承担方，N-否，Y-是，手续费承担方有且只能有一个
     */
    private String feeFlag;

    /**
     * 控制台 主页面应用的app_id，不上送默认取商户自身app_id
     */
    private String appId;
}
