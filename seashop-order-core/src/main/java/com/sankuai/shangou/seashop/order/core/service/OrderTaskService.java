package com.sankuai.shangou.seashop.order.core.service;

/**
 * 订单定时任务相关逻辑
 * <AUTHOR>
 */
public interface OrderTaskService {

    /**
     * 供应商未发货自动短信提醒
     */
    void remindWaitDelivery();

    /**
     * 商家-订单关闭前短信提醒
     */
    void remindNotPay();

    /**
     * 商家-超时自动关闭
     */
    void autoClose();

    /**
     * 商家-超时自动确认收货并完成
     * 卖家发货后N天未确认收货，系统自动确认收货
     */
    void autoFinish();

}
