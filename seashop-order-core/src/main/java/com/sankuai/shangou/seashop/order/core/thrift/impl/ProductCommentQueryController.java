package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.ProductCommentService;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductCommentSummaryBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShopCommentSummaryBo;
import com.sankuai.shangou.seashop.order.thrift.core.ProductCommentQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.MallQueryProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryProductCommentSummaryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryShopCommentSummaryReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ProductCommentSummaryResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.ShopCommentSummaryResp;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:32
 */
@Slf4j
@RestController
@RequestMapping("/productCommentQuery")
public class ProductCommentQueryController implements ProductCommentQueryFeign {

    @Resource
    private ProductCommentService productCommentService;

    @PostMapping(value = "/queryProductCommentForPlatform", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ProductCommentResp>> queryProductCommentForPlatform(@RequestBody QueryProductCommentReq request) throws TException  {
        return ThriftResponseHelper.responseInvoke("queryProductCommentForPlatform", request, req -> {
            req.checkParameter();

            BasePageResp<ProductCommentBo> pageResult = productCommentService
                    .queryProductCommentForPlatform(req.buildPage(), JsonUtil.copy(req, QueryProductCommentBo.class));
            return PageResultHelper.transfer(pageResult, ProductCommentResp.class);
        });
    }

    @PostMapping(value = "/queryProductCommentForSeller", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ProductCommentResp>> queryProductCommentForSeller(@RequestBody QueryProductCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductCommentForSeller", request, req -> {
            req.checkForSeller();

            BasePageResp<ProductCommentBo> pageResult = productCommentService
                    .queryProductCommentForSeller(req.buildPage(), JsonUtil.copy(req, QueryProductCommentBo.class));
            return PageResultHelper.transfer(pageResult, ProductCommentResp.class);
        });
    }

    @PostMapping(value = "/queryProductCommentForMall", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ProductCommentResp>> queryProductCommentForMall(@RequestBody MallQueryProductCommentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductCommentForMall", request, req -> {
            req.checkParameter();

            BasePageResp<ProductCommentBo> pageResult = productCommentService
                    .queryProductCommentForMall(req.buildPage(), JsonUtil.copy(req, QueryProductCommentBo.class));
            return PageResultHelper.transfer(pageResult, ProductCommentResp.class);
        });
    }

    @PostMapping(value = "/queryProductCommentSummary", consumes = "application/json")
    @Override
    public ResultDto<ProductCommentSummaryResp> queryProductCommentSummary(@RequestBody QueryProductCommentSummaryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductCommentSummary", request, req -> {
            req.checkParameter();

            ProductCommentSummaryBo productCommentBo = productCommentService.queryProductCommentSummary(req.getProductId());
            return JsonUtil.copy(productCommentBo, ProductCommentSummaryResp.class);
        });
    }

    @PostMapping(value = "/queryShopCommentSummary", consumes = "application/json")
    @Override
    public ResultDto<ShopCommentSummaryResp> queryShopCommentSummary(@RequestBody QueryShopCommentSummaryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryShopCommentSummary", request, req -> {
            req.checkParameter();

            ShopCommentSummaryBo shopCommentSummary = productCommentService.queryShopCommentSummary(req.getShopId());
            return JsonUtil.copy(shopCommentSummary, ShopCommentSummaryResp.class);
        });
    }

    @GetMapping(value = "/queryMCommentSummary")
    @Override
    public ResultDto<ShopCommentSummaryResp> queryMCommentSummary() throws TException {
        return ThriftResponseHelper.responseInvoke("queryMCommentSummary", null, req -> {
            ShopCommentSummaryBo shopCommentSummary = productCommentService.queryMCommentSummary();
            return JsonUtil.copy(shopCommentSummary, ShopCommentSummaryResp.class);
        });
    }

}
