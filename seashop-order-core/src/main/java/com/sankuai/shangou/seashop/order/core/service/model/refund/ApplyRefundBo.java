package com.sankuai.shangou.seashop.order.core.service.model.refund;

import java.math.BigDecimal;

import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundTypeEnum;

import lombok.Getter;
import lombok.Setter;

/**
 * 申请退款数据对象。整单退与明细退复用一个对象，只有一个明细ID的字段区别
 * <AUTHOR>
 */
@Getter
@Setter
public class ApplyRefundBo {

    /**
     * 订单号
     */
    private String orderId;
    /**
     * 售后类型。1：仅退款；2：退货退款
     */
    private RefundTypeEnum refundType;
    /**
     * 退款模式，综合售后类型和是否整单退得到
     */
    private RefundModeEnum refundMode;
    /**
     * 申请退款金额
     */
    private BigDecimal refundAmount;
    /**
     * 退款数量。退货退款时有值
     */
    private Long refundQuantity;
    /**
     * 退款商品id。退明细时有值
     */
    private Long orderItemId;
    /**
     * 退款原因类型
     */
    private String refundReasonDesc;
    /**
     * 退款说明
     */
    private String refundRemark;
    /**
     * 联系人姓名
     */
    private String contactUserName;
    /**
     * 联系人电话
     */
    private String contactUserPhone;
    /**
     * 退款方式。1：原路返回
     */
    private RefundPayTypeEnum refundPayType;
    /**
     * 售后凭证1。最多三张，与数据表保持一致分开
     */
    private String certPic1;
    /**
     * 售后凭证2。最多三张，与数据表保持一致分开
     */
    private String certPic2;
    /**
     * 售后凭证3。最多三张，与数据表保持一致分开
     */
    private String certPic3;
    /**
     * 用户信息
     */
    private UserBo user;
    /**
     * 是否订单全退货，已发货之后才可能为true
     */
    private Boolean hasAllReturn;
    /**
     * 退款运费金额。发货后整单退款时有值，供应商审核时可以决定是否退运费
     */
    private BigDecimal returnFreightAmount;

    // 非传入，业务设置，申请的数量，与退货数量有区别，returnQuantity是实际退货的数量
    private Long applyQuantity;
    private BigDecimal returnCommissionAmount;

    /**
     * 指定状态
     */
    private RefundStatusEnum status;

    private String sourceRefundId;

    /**
     * 快递公司编码
     */
    private String expressCompanyCode;
    /**
     * 快递公司名称
     */
    private String expressCompanyName;
    /**
     * 快递单号
     */
    private String shipOrderNumber;

}
