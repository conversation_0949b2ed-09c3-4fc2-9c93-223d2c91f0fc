package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SellerRefundBo extends RefundBo {

    /**
     * 是否显示【查看物流】按钮
     * 供应商登录，买家填写了物流信息显示此按钮
     */
    private Boolean showWayBillBtn = false;
    /**
     * 审核截止时间描述
     */
    private String remainApproveDeadlineDesc;
    /**
     * 退款商品描述。如果是整单，则显示【订单所有商品】，否则显示具体商品名称
     */
    private String productDesc;

}
