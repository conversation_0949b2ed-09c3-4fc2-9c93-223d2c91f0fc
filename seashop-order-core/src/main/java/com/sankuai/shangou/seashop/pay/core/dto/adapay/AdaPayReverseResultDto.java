package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description: 退款结果返回对象（未分账的支付订单）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdaPayReverseResultDto {

    /**
     * 售后单
     */
    private String refundId;

    /**
     * 渠道退款单号
     */
    private String channelRefundId;

    /**
     * 订单ID
     */
    private String orderNo;

    /**
     * 订单金额
     */
    private String refundedAmt;
    /**
     * 支付状态 1 成功 2 失败
     */
    private Integer payStatus;

    /**
     * 错误描述
     */
    private String errorMessage;

    /**
     * 退款类型
     */
    private Integer type;

    /**
     * 业务类型: 1：订单 4：保证金
     */
    private Integer businessType;

    /**
     * 业务状态类型：1：正常订单退款；2：异常订单退款；3：订单补偿退款（支付金额大于订单金额）
     */
    private Integer businessStatusType;

}
