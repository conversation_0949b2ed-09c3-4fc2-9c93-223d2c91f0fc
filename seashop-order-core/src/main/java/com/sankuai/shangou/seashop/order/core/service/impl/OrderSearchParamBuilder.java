package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.enums.YesOrNoEnum;
import com.sankuai.shangou.seashop.order.common.es.EagleService;
import com.sankuai.shangou.seashop.order.common.remote.RemoteMemberService;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.common.utils.SettingUtil;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryOrderBaseBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryOrderCommonBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryUserOrderBo;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.*;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
public class OrderSearchParamBuilder {

    @Resource
    private EagleService eagleService;
    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private RemoteMemberService remoteMemberService;

    /**
     * 构建卖家订单搜索查询条件
     *
     * @param searchBo
     * @return BoolQueryBuilder 引用对象其实会改变数据，但由于有些条件直接可以确定不需要继续查询的，返回null特殊处理所以返回builder
     * <AUTHOR>
     */
    protected BoolQueryBuilder buildCommonSearchCondition(BoolQueryBuilder boolQueryBuilder, QueryOrderCommonBo searchBo) {
        if (StrUtil.isNotBlank(searchBo.getOrderId())) {
            // 订单ID精确匹配
            String[] orderIds = searchBo.getOrderId().split(",");
            if (orderIds.length > 1) {
                boolQueryBuilder.filter(QueryBuilders.termsQuery("orderId", orderIds));
            } else {
                boolQueryBuilder.filter(QueryBuilders.termQuery("orderId", orderIds[0]));
            }
        }
        String searchKey = searchBo.getSearchKey();
        if (StringUtils.isNotBlank(searchKey)) {
            this.assembleBasicFieldSplitSearch(boolQueryBuilder, searchKey, NumberUtils.INTEGER_ONE);
        }
        // cellPhone 是收货人手机号
        if (StrUtil.isNotBlank(searchBo.getCellPhone())) {
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("cellPhone", eagleService.appendWildcard(searchBo.getCellPhone())));
        }
        // userPhone 是商家(买家)手机号，没有放到ES，因为用户手机号码可以改
        if (StrUtil.isNotBlank(searchBo.getUserPhone())) {
            List<Long> userIds = remoteMemberService.queryUserIdByMobile(searchBo.getUserPhone());
            // 如果传入了手机号码，但是没有查到匹配的用户，则不需要再继续
            if (CollUtil.isEmpty(userIds)) {
                return null;
            }
            boolQueryBuilder.filter(QueryBuilders.termsQuery("userId", userIds));
        }
        if (StrUtil.isNotBlank(searchBo.getUserName())) {
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("userName", eagleService.appendWildcard(searchBo.getUserName())));
        }
        if (searchBo.getOrderStatus() != null && !OrderStatusEnum.UNKNOWN.getCode().equals(searchBo.getOrderStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("orderStatus", searchBo.getOrderStatus()));
        }
        if (CollUtil.isNotEmpty(searchBo.getOrderStatusList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("orderStatus", searchBo.getOrderStatusList()));
        }
        if (searchBo.getShopIdList() != null) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("shopId", searchBo.getShopIdList()));
        }
        if (CollUtil.isNotEmpty(searchBo.getPaymentTypeList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("paymentType", searchBo.getPaymentTypeList()));
        }
        if (CollUtil.isNotEmpty(searchBo.getPaymentList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("payment", searchBo.getPaymentList()));
        }
        if (CollUtil.isNotEmpty(searchBo.getOrderTypeList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("orderType", searchBo.getOrderTypeList()));
        }
        if (searchBo.getInvoiceType() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("orderInvoice.invoiceType", searchBo.getInvoiceType()));
        }
        if (searchBo.getHasInvoice() != null && searchBo.getHasInvoice()) {
            boolQueryBuilder.filter(QueryBuilders.existsQuery("orderInvoice"));
        }
        if (searchBo.getHasComment() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("hasCommented", searchBo.getHasComment() ? 1 : 0));
        }
        if (CollUtil.isNotEmpty(searchBo.getPlatformList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("platform", searchBo.getPlatformList()));
        }
        if (StringUtils.isNotBlank(searchBo.getOeCode())) {
            boolQueryBuilder.must(QueryBuilders.nestedQuery("orderItems", QueryBuilders.termQuery("orderItems.oeCode", searchBo.getOeCode()), org.apache.lucene.search.join.ScoreMode.None));
        }
        if (StringUtils.isNotBlank(searchBo.getBrandName())) {
            String brandName = searchBo.getBrandName();
            brandName = brandName.replaceAll("[,，]", " ");
            String[] keys = brandName.split(" ");

            BoolQueryBuilder brandNameShouldQuery = QueryBuilders.boolQuery();

            for (String key : keys) {
                if (StringUtils.isBlank(key)) continue;
                MatchQueryBuilder matchQuery = QueryBuilders.matchQuery("orderItems.brandName", key);
                brandNameShouldQuery.should(matchQuery);
            }

            // 至少命中一条 should
            brandNameShouldQuery.minimumShouldMatch(1);

            // 一定要加 nestedQuery 包裹！
            NestedQueryBuilder nested = QueryBuilders.nestedQuery("orderItems", brandNameShouldQuery, ScoreMode.None);
            boolQueryBuilder.must(nested);
        }
        if (StringUtils.isNotBlank(searchBo.getBrandCode())) {
            boolQueryBuilder.must(QueryBuilders.nestedQuery("orderItems", QueryBuilders.termQuery("orderItems.brandCode", searchBo.getBrandCode()), org.apache.lucene.search.join.ScoreMode.None));
        }
        if (CollectionUtils.isNotEmpty(searchBo.getBrandIds())) {
            boolQueryBuilder.must(QueryBuilders.nestedQuery("orderItems", QueryBuilders.termsQuery("orderItems.brandId", searchBo.getBrandIds()), org.apache.lucene.search.join.ScoreMode.None));
        }
        appendOrderDateParam(boolQueryBuilder, searchBo.getOrderStartTime(), searchBo.getOrderEndTime());
        appendFinishDateParam(searchBo, boolQueryBuilder);
        return boolQueryBuilder;
    }

    private void assembleBasicFieldSplitSearch(BoolQueryBuilder boolQueryBuilder, String searchKey, Integer minMatch) {
        // 去除前后空格
        searchKey = searchKey.trim();
        boolean onlyNumber = StrUtil.isNumeric(searchKey);
        boolean isLong = NumberUtil.isLong(searchKey);

        // 构建 should 查询组
        BoolQueryBuilder shouldGroup = QueryBuilders.boolQuery();

        // 精确匹配 orderId
        shouldGroup.should(QueryBuilders.termQuery("orderId", searchKey));

        // nested 查询中的 should 组合
        BoolQueryBuilder nestedShould = QueryBuilders.boolQuery();
        nestedShould.should(QueryBuilders.matchQuery("orderItems.oeCode", searchKey).boost(5f));
        nestedShould.should(QueryBuilders.matchQuery("orderItems.brandCode", searchKey).boost(4f));
        nestedShould.should(QueryBuilders.matchQuery("orderItems.productName", searchKey).boost(3f));
        nestedShould.should(QueryBuilders.matchQuery("orderItems.brandName", searchKey).boost(2f));
        nestedShould.should(QueryBuilders.matchQuery("orderItems.adaptableCar", searchKey).boost(1f));
        nestedShould.should(QueryBuilders.matchQuery("orderItems.partSpec", searchKey).boost(1f));

        if (onlyNumber && isLong) {
            long id = Long.parseLong(searchKey);
            nestedShould.should(QueryBuilders.termQuery("orderItems.productId", id));
            nestedShould.should(QueryBuilders.termQuery("orderItems.skuAutoId", id));
        }

        // nestedShould 至少匹配一项
        nestedShould.minimumShouldMatch(minMatch);

        // 构建 nested 查询
        NestedQueryBuilder nestedSearchQuery = QueryBuilders.nestedQuery("orderItems", nestedShould, ScoreMode.None);

        // 加入到 shouldGroup 中
        shouldGroup.should(nestedSearchQuery);

        // shouldGroup 至少匹配一个
        shouldGroup.minimumShouldMatch(minMatch);

        // 最终加入到 must 中，确保必须匹配至少一个
        boolQueryBuilder.must(shouldGroup);
    }

    /**
     * 构建商家订单搜索查询条件
     *
     * @param searchBo org.elasticsearch.index.query.BoolQueryBuilder
     * <AUTHOR>
     */
    protected BoolQueryBuilder buildUserSearchCondition(QueryUserOrderBo searchBo) {
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();

        // 用户ID必须匹配
        boolQueryBuilder.must(QueryBuilders.termQuery("userId", searchBo.getUserId()));

        String searchKey = searchBo.getSearchKey();
        if (StrUtil.isNotBlank(searchKey)) {
            this.assembleBasicFieldSplitSearch(boolQueryBuilder, searchKey, NumberUtils.INTEGER_ONE);
        }

        // 单一订单状态
        if (searchBo.getOrderStatus() != null && !OrderStatusEnum.UNKNOWN.getCode().equals(searchBo.getOrderStatus())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("orderStatus", searchBo.getOrderStatus()));
        }

        // 多个订单状态
        if (CollUtil.isNotEmpty(searchBo.getOrderStatusList())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("orderStatus", searchBo.getOrderStatusList()));
        }

        // 查询待评价订单
        if (Boolean.TRUE.equals(searchBo.getQueryUnCommented())) {
            TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
            int commentTimeoutDays = SettingUtil.getIntValueOrDefault("订单完成后允许评价天数", setting.getOrderCommentTimeout(), CommonConst.DEFAULT_COMMENT_TIMEOUT_DAYS);
            Date timeoutDate = DateUtil.offsetDay(new Date(), -commentTimeoutDays);
            Date beginOfDay = DateUtil.beginOfDay(timeoutDate);

            boolQueryBuilder.must(QueryBuilders.termQuery("hasCommented", YesOrNoEnum.NO.getCode())).must(QueryBuilders.existsQuery("finishDate")).must(QueryBuilders.termQuery("orderStatus", OrderStatusEnum.FINISHED.getCode())).must(QueryBuilders.rangeQuery("finishDate").gte(beginOfDay.getTime()));
        }

        // 精确匹配订单ID（非 searchKey）
        if (StrUtil.isNotBlank(searchBo.getOrderId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("orderId", searchBo.getOrderId()));
        }

        // 商品名称模糊匹配
        if (StrUtil.isNotBlank(searchBo.getProductName())) {
            String wildcard = eagleService.appendWildcard(searchBo.getProductName());
            boolQueryBuilder.must(QueryBuilders.nestedQuery("orderItems", QueryBuilders.wildcardQuery("orderItems.productName.keyword", wildcard), ScoreMode.None));
        }

        // 下单时间过滤
        appendOrderDateParam(boolQueryBuilder, searchBo.getOrderStartTime(), searchBo.getOrderEndTime());

        // 完成时间过滤
        appendFinishDateParam(searchBo, boolQueryBuilder);

        return boolQueryBuilder;
    }


    protected void appendOrderDateParam(BoolQueryBuilder boolQueryBuilder, Date orderStartTime, Date orderEndTime) {
        if (orderStartTime != null || orderEndTime != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("orderDate");
            if (orderStartTime != null) {
                rangeQueryBuilder.gte(orderStartTime.getTime());
            }
            if (orderEndTime != null) {
                rangeQueryBuilder.lte(orderEndTime.getTime());
            }
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
    }

    protected void appendFinishDateParam(QueryOrderBaseBo searchBo, BoolQueryBuilder boolQueryBuilder) {
        if (searchBo.getFinishStartTime() != null || searchBo.getFinishEndTime() != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("finishDate");
            if (searchBo.getFinishStartTime() != null) {
                rangeQueryBuilder.gte(searchBo.getFinishStartTime().getTime());
            }
            if (searchBo.getFinishEndTime() != null) {
                rangeQueryBuilder.lte(searchBo.getFinishEndTime().getTime());
            }
            boolQueryBuilder.filter(rangeQueryBuilder);
        }
    }


    protected List<SortBuilder<FieldSortBuilder>> buildFieldSortList() {
        List<SortBuilder<FieldSortBuilder>> sortBuilders = new ArrayList<>(2);
        appendDefaultFieldSort(sortBuilders);
        return sortBuilders;
    }

    protected void appendDefaultFieldSort(List<SortBuilder<FieldSortBuilder>> sortBuilders) {
        // 默认根据订单创建时间倒序排列，时间一致按照ID降序
        sortBuilders.add(SortBuilders.fieldSort("orderDate").order(SortOrder.DESC));
        sortBuilders.add(SortBuilders.fieldSort("orderId").order(SortOrder.DESC));
    }

}
