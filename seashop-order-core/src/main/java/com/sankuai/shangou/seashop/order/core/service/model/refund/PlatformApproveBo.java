package com.sankuai.shangou.seashop.order.core.service.model.refund;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class PlatformApproveBo extends BaseParamReq {

    /**
     * 退款id
     */
    @PrimaryField
    private Long refundId;
    /**
     * 审核备注
     */
    @ExaminField(description = "审核备注")
    private String remark;
    /**
     * 登录用户信息
     */
    private UserBo user;

}
