package com.sankuai.shangou.seashop.order.core.service.model.order;

import java.util.Date;
import java.util.List;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/04 10:20
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ProductCommentBo {

    /**
     * 商品评价自增主键
     */
    private Long id;

    /**
     * 订单id
     */
    private String orderId;

    /**
     * 子订单id
     */
    private Long subOrderId;

    /**
     * 商品评价id
     */
    private Long productCommentId;

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品缩略图
     */
    private String thumbnailsUrl;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称(商家账号)
     */
    private String userName;

    /**
     * 用户邮箱
     */
    private String email;

    /**
     * 用户手机号(商家手机号)
     */
    private String userMobile;

    /**
     * 评价内容
     */
    private String reviewContent;

    /**
     * 评价日期
     */
    private Date reviewDate;

    /**
     * 包装评分 1-5分
     */
    private Integer reviewMark;

    /**
     * 回复内容
     */
    private String replyContent;

    /**
     * 回复日期
     */
    private Date replyDate;

    /**
     * 追加评价内容
     */
    private String appendContent;

    /**
     * 追加评价日期
     */
    private Date appendDate;

    /**
     * 追加评论回复
     */
    private String replyAppendContent;

    /**
     * 追加评论回复时间
     */
    private Date replyAppendDate;

    /**
     * 是否回复
     */
    private Boolean hasReply;

    /**
     * 是否隐藏
     */
    private Boolean hasHidden;

    /**
     * 购买时间
     */
    private Date buyDate;

    /**
     * 评价图片 最多五张
     */
    private List<String> commentImageList;

    /**
     * 首次评价图片 最多五张
     */
    private List<ProductCommentImageBo> firstCommentImageList;

    /**
     * 追加评价图片 最多五张
     */
    private List<ProductCommentImageBo> appendCommentImageList;

    /**
     * 首次评价是否有图片
     */
    private Boolean hasImage;

    /**
     * 追加评价是否有图片
     */
    private Boolean appendHasImage;

    /**
     * 评价图片列表(风控通过后的)
     */
    private List<String> imageList;

    /**
     * 追加评价图片列表(风控通过后的)
     */
    private List<String> appendImageList;

    /**
     * 规格1 别名
     */
    private String spec1Alias;

    /**
     * 规格2 别名
     */
    private String spec2Alias;

    /**
     * 规格3 别名
     */
    private String spec3Alias;

    /**
     * 规格1 值
     */
    private String spec1Value;

    /**
     * 规格2 值
     */
    private String spec2Value;

    /**
     * 规格3 值
     */
    private String spec3Value;

    /**
     * sku名称
     */
    private String skuName;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 是否可以回复
     */
    private boolean needReply;

    /**
     * 用户头像
     */
    private String userAvatar;

    /**
     * 用户昵称
     */
    private String userNickName;
}
