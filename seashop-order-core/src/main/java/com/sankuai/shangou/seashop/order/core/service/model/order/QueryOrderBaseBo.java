package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderQueryFromEnum;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class QueryOrderBaseBo extends BasePageReq {

    /**
     * 下单时间-开始
     */
    private Date orderStartTime;
    /**
     * 下单时间-结束
     */
    private Date orderEndTime;
    /**
     * 完成时间-开始
     */
    private Date finishStartTime;
    /**
     * 完成时间-结束
     */
    private Date finishEndTime;
    /**
     * 查询来源。1-商家小程序；2-商家PC端；3-卖家PC端；4-平台PC端
     */
    private OrderQueryFromEnum queryFrom;
    /**
     * 基于scroll查询时的数据保留时间
     */
    private Long timeValueMinutes;

}
