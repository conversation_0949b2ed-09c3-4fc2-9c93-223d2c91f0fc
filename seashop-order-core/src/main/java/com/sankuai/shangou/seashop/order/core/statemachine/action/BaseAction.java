package com.sankuai.shangou.seashop.order.core.statemachine.action;

import com.alibaba.cola.statemachine.Action;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderContext;
import com.sankuai.shangou.seashop.order.core.statemachine.OrderEvent;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class BaseAction implements Action<OrderStatusEnum, OrderEvent, OrderContext> {

    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private OrderRepository orderRepository;

    @Override
    public void execute(OrderStatusEnum from, OrderStatusEnum to, OrderEvent event, OrderContext context) {
        log.info("【订单状态机】订单状态变更,from={},to={},event={},context={}", from, to, event, context);
        String orderId = context.getOrderId();
        String lockKey = LockConst.LOCK_ORDER_STATUS_CHANGE + orderId;
        log.info("【订单状态机】订单状态变更,lockKey={}", lockKey);
        // 订单状态的流转加锁处理，防止并发情况下导致订单状态异常
        try {
            ActionResult executeResult = distributedLockService.tryLock(new LockKey(LockConst.SCENE_ORDER_STATUS_CHANGE, lockKey), () -> {
                Order dbOrder = orderRepository.getByOrderId(orderId);
                // 如果订单的状态与当前状态不一致，说明订单已经被其他线程处理过了，直接返回
                if (!dbOrder.getOrderStatus().equals(from.getCode())) {
                    return ActionResult.fail(0, "订单状态已经变更，不需要再次处理");
                }
                validateOrder(context, dbOrder);
                // 上下文中设置 order 对象
                context.setOrder(dbOrder);
                // 实际执行状态变更的业务处理
                executeStatusChange(context, dbOrder);
                return ActionResult.success();
            }, 2L, TimeUnit.SECONDS);
            // 处理执行结果，比如可能会有其他线程处理过了，具体业务看报错还是忽略
            dealExecuteResult(context, executeResult);
        }
        catch (BusinessException be) {
            log.error("【订单状态机】订单状态变更异常,from={},to={},event={},context={}", from, to, event, context, be);
            throw be;
        }
        catch (Throwable e) {
            log.error("【订单状态机】订单状态变更异常,from={},to={},event={},context={}", from, to, event, context, e);
            throw new BusinessException("订单状态变更异常");
        }
    }


    protected abstract void executeStatusChange(OrderContext context, Order dbOrder);

    protected abstract void validateBizData(OrderContext context, Order dbOrder);

    protected abstract void dealExecuteResult(OrderContext context, ActionResult executeResult);


    //***************************************************



    protected void validateOrder(OrderContext context, Order dbOrder) {
        if (dbOrder == null) {
            throw new BusinessException("订单不存在");
        }
        validateBizData(context, dbOrder);
    }

}
