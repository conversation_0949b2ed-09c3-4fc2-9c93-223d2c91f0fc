package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.PageUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.order.common.config.EsIndexProps;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.order.common.enums.YesOrNoEnum;
import com.sankuai.shangou.seashop.order.common.es.EagleService;
import com.sankuai.shangou.seashop.order.common.es.model.EagleQueryResult;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.common.utils.SkuUtil;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundSearchService;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderRefundBizAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.PageQueryAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.RefundStatusHelper;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.EsOrderRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformQueryRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.QueryRefundBaseBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundItemBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerQueryRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserQueryRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserRefundBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPlatformQueryTabEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundSellerQueryTabEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundUserQueryTabEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderRefundSearchServiceImpl implements OrderRefundSearchService {

    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private EagleService eagleService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRefundBizAssist orderRefundBizAssist;
    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private PageQueryAssist pageQueryAssist;
    @Resource
    private EsIndexProps esIndexProps;
    @Resource
    private EsOrderServiceImpl esOrderService;

    @Override
    public void buildEsRefund(Long refundId) {
        log.info("【售后索引构建】售后ID为: {}", refundId);
        // 加锁，查询最新的数据
        String lockKey = LockConst.LOCK_ES_REFUND_BASE + refundId;
        distributedLockService.tryLock(new LockKey(LockConst.SCENE_ES_REFUND_ORDER_CHANGE, lockKey), () -> {
            // 构建ES存储对象
            Map<String/*docId*/, Map<String/*字段名称*/, Object/*字段值*/>> paramMap = new HashMap<>(2);
            // 构建需要保存的业务数据
            Map<String/*字段名称*/, Object/*字段值*/> doc = buildIndexData(refundId);
            paramMap.put(refundId.toString(), doc);
            log.info("【售后索引构建】售后ID为: {}, 索引内容为: {}", refundId, JsonUtil.toJsonString(paramMap));
            // 调用ES部分更新
            eagleService.partUpdate(esIndexProps.getIdxRefund(), paramMap);
        });
    }

    @Override
    public void updateOrderStatus(String orderId) {
        // 状态的修改是订单维度，需要获取最新的订单状态，且与基础信息是不同的锁，所以应该锁可以加到订单上
        String lockKey = LockConst.LOCK_ES_REFUND_ORDER_CHANGE + orderId;
        distributedLockService.tryLock(new LockKey(LockConst.SCENE_ES_REFUND_ORDER_CHANGE, lockKey), () -> {
            Order order = orderRepository.getByOrderId(orderId);
            if (order == null) {
                log.warn("订单不存在，orderId={}", orderId);
                return;
            }
            List<OrderRefund> refundList = orderRefundRepository.getByOrderId(orderId);
            if (CollUtil.isEmpty(refundList)) {
                log.warn("订单没有售后记录，orderId={}", orderId);
                return;
            }
            // 构建ES存储对象
            Map<String/*docId*/, String/*文档内容*/> batchDocMap = new HashMap<>(8);
            for (OrderRefund orderRefund : refundList) {
                Map<String/*字段名称*/, Object/*字段值*/> doc = new HashMap<>(4);
                doc.put("orderStatus", order.getOrderStatus());

                batchDocMap.put(orderRefund.getId().toString(), JsonUtil.toJsonString(doc));
            }
            // 调用ES部分更新
            eagleService.batchUpdate(esIndexProps.getIdxRefund(), batchDocMap);
        });
    }

    @Override
    public BasePageResp<UserRefundBo> userQueryPage(UserQueryRefundBo queryBo) {
        // 构建搜索请求
        SearchRequest searchRequest = buildUserSearchRequest(queryBo);
        log.info("【售后搜索】商家端搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        if (searchRequest == null) {
            return PageResultHelper.defaultEmpty(queryBo);
        }
        return searchAndConvertForUser(searchRequest, queryBo);
    }

    @Override
    public BasePageResp<SellerRefundBo> sellerQueryPage(SellerQueryRefundBo queryBo) {
        // 构建搜索请求
        SearchRequest searchRequest = buildSellerSearchRequest(queryBo);
        log.info("【售后搜索】供应商端搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        return searchAndConvertForSeller(searchRequest, queryBo);
    }

    @Override
    public BasePageResp<PlatformRefundBo> platformQueryPage(PlatformQueryRefundBo queryBo) {
        // 校验分页查询数据量，超过显示提示用户
        pageQueryAssist.checkOrderPageTotal(queryBo);
        // 构建搜索请求
        SearchRequest searchRequest = buildPlatformSearchRequest(queryBo);
        log.info("【售后搜索】平台端搜索条件为: {}", JsonUtil.toJsonString(searchRequest));
        return searchAndConvertForPlatform(searchRequest, queryBo);
    }


    //***************************************************************************


    private Map<String/*字段名称*/, Object/*字段值*/> buildIndexData(Long refundId) {
        OrderRefund orderRefund = orderRefundRepository.getByIdIncludeDelete(refundId);
        log.info("【售后索引构建】售后ID为: {}, 退款单信息为: {}", refundId, JsonUtil.toJsonString(orderRefund));
        Order order = orderRepository.getByOrderId(orderRefund.getOrderId());
        log.info("【售后索引构建】售后ID为: {}, 订单信息为: {}", refundId, JsonUtil.toJsonString(order));

        EsOrderRefundBo esOrderRefundBo = new EsOrderRefundBo();
        esOrderRefundBo.setOrderId(orderRefund.getOrderId());
        esOrderRefundBo.setRefundId(orderRefund.getId());
        esOrderRefundBo.setOrderItemId(orderRefund.getOrderItemId());
        if (order != null) {
            esOrderRefundBo.setUserId(order.getUserId());
            esOrderRefundBo.setUserName(order.getUserName());
            esOrderRefundBo.setShopId(order.getShopId());
            esOrderRefundBo.setShopName(order.getShopName());
            esOrderRefundBo.setOrderStatus(order.getOrderStatus());
            esOrderRefundBo.setOrderDate(order.getOrderDate().getTime());
        }

        esOrderRefundBo.setSellerAuditStatus(orderRefund.getSellerAuditStatus());
        esOrderRefundBo.setManagerConfirmStatus(orderRefund.getManagerConfirmStatus());
        esOrderRefundBo.setRefundStatus(orderRefund.getStatus());
        esOrderRefundBo.setRefundMode(orderRefund.getRefundMode());
        esOrderRefundBo.setApplyDate(orderRefund.getApplyDate().getTime());
        esOrderRefundBo.setHasCancel(orderRefund.getHasCancel());
        esOrderRefundBo.setHasAllReturn(orderRefund.getHasAllReturn());
        // 不是整单退，且有订单项ID，才需要设置商品信息
        if (orderRefund.getOrderItemId() != null && orderRefund.getOrderItemId() > 0) {
            OrderItem orderItem = orderItemRepository.getById(orderRefund.getOrderItemId());
            esOrderRefundBo.setProductId(orderItem.getProductId());
            esOrderRefundBo.setSkuId(orderItem.getSkuId());
            esOrderRefundBo.setProductName(orderItem.getProductName());
        }
        RefundTypeEnum refundType = orderRefundBizAssist.getRefundType(orderRefund);
        esOrderRefundBo.setRefundType(refundType.getCode());

        esOrderRefundBo.setIsDelete(orderRefund.getIsDelete());

        Map<String/*字段名称*/, Object/*字段值*/> doc = JsonUtil.beanToMap(esOrderRefundBo);
        log.info("【订单索引构建】售后ID为: {}, 索引内容为: {}", refundId, JsonUtil.toJsonString(doc));
        return doc;
    }


    /**
     * 构建商家查询订单的请求参数
     *
     * @param queryBo 请求参数
     * <AUTHOR>
     */
    private SearchRequest buildUserSearchRequest(UserQueryRefundBo queryBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("isDelete", YesOrNoEnum.NO.getCode()));

        UserBo user = queryBo.getUser();
        boolQueryBuilder.must(QueryBuilders.termQuery("userId", user.getUserId()));
        String productNameOrId = queryBo.getProductNameOrId();
        // 商家端的商品搜索，从订单明细中找到订单再到售后表搜索
        if (StrUtil.isNotBlank(productNameOrId)) {
            List<String> orderIdList = esOrderService.getOrderIdByProductFromItem(productNameOrId);
            if (CollUtil.isEmpty(orderIdList)) {
                return null;
            }
            boolQueryBuilder.must(QueryBuilders.termsQuery("orderId", orderIdList));
        }
        if (StrUtil.isNotBlank(queryBo.getSearchKey())) {
            // orderId精确匹配
            boolQueryBuilder.should(QueryBuilders.termQuery("orderId", queryBo.getSearchKey()))
                    // 商品名称模糊匹配
                    .should(QueryBuilders.wildcardQuery("productName", eagleService.appendWildcard(queryBo.getSearchKey())));
            // 任意满足一个即可
            boolQueryBuilder.minimumShouldMatch(1);
        }
        if (StrUtil.isNotBlank(queryBo.getOrderId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("orderId", queryBo.getOrderId()));
        }
        if (queryBo.getRefundId() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("refundId", queryBo.getRefundId()));
        }
        // 处理申请时间参数
        appendApplyDateParam(boolQueryBuilder, queryBo);
        if (queryBo.getRefundStatus() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("refundStatus", queryBo.getRefundStatus().getCode()));
        }
        if (!CollectionUtils.isEmpty(queryBo.getRefundStatusList())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("refundStatus", queryBo.getRefundStatusList()));
        }
        if (queryBo.getOrderStatus() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("orderStatus", queryBo.getOrderStatus().getCode()));
        }
        // 我申请的退款:包含发货前整单退货/退款、发货后退款方式为-仅退款的售后单
        // 我申请的退货:包含发货后退款方式为-退货退款的售后单

        if (RefundUserQueryTabEnum.ALL_APPLY.equals(queryBo.getTab())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("refundMode",
                    Arrays.asList(RefundModeEnum.ORDER_REFUND.getCode(), RefundModeEnum.GOODS_REFUND.getCode(),
                            RefundModeEnum.RETURN_AND_REFUND.getCode())));
        } else if (RefundUserQueryTabEnum.APPLY_REFUND.equals(queryBo.getTab())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("refundMode",
                    Arrays.asList(RefundModeEnum.ORDER_REFUND.getCode(), RefundModeEnum.GOODS_REFUND.getCode())));
        } else {
            boolQueryBuilder.must(QueryBuilders.termQuery("refundMode", RefundModeEnum.RETURN_AND_REFUND.getCode()));
        }
        return buildSortAndPageAndCreateRequest(queryBo, boolQueryBuilder);
    }

    private SearchRequest buildSellerSearchRequest(SellerQueryRefundBo queryBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("isDelete", YesOrNoEnum.NO.getCode()));

        ShopBo shop = queryBo.getShop();
        boolQueryBuilder.must(QueryBuilders.termQuery("shopId", shop.getShopId()));

//        if (StrUtil.isNotBlank(queryBo.getProductIdOrSkuId())) {
//            // productId OR skuId 精确匹配
//            // skuId含有下划线，productId是long类型，会报错，所以如果是含有下划线，因为要精确匹配，一定是skuId，否则是productId
//            // 如果不是long类型，则不匹配商品ID，否则会报错
//            boolean isLong = NumberUtil.isLong(queryBo.getProductIdOrSkuId());
//            if (isLong) {
//                boolQueryBuilder.must(QueryBuilders.termQuery("productId", queryBo.getProductIdOrSkuId()));
//            } else {
//                boolQueryBuilder.must(QueryBuilders.termQuery("skuId", queryBo.getProductIdOrSkuId()));
//            }
//
//        }
//
//        if (StrUtil.isNotBlank(queryBo.getProductName())) {
//            // 商品名称模糊匹配
//            String productName = eagleService.appendWildcard(queryBo.getProductName());
//            boolQueryBuilder.must(QueryBuilders.wildcardQuery("productName", productName));
//        }
        //cjzhao 2024/12/09注释 修改
        //由于es中和mysql一样  在整单退的情况下，没有写入productName 以及productId、 skuId等 所以修改方案
        //先用着两个条件查询数据库  取出orderId， 再到es中使用in查询
        //以后优化可重建索引 把orderItem数据存储es中

        if (StringUtils.isNotEmpty(queryBo.getProductIdOrSkuId()) || StringUtils.isNotEmpty(queryBo.getProductName())) {
            List<OrderItem> orderIdList = orderItemRepository.getListByString(queryBo.getProductIdOrSkuId(), queryBo.getProductName(), queryBo.getShop().getShopId());
            if (!CollUtil.isEmpty(orderIdList)) {
                List<String> orderIds = orderIdList.stream().map(OrderItem::getOrderId).collect(Collectors.toList());
                queryBo.setOrderIds(orderIds);
                boolQueryBuilder.must(QueryBuilders.termsQuery("orderId", orderIds));
            } else {
                boolQueryBuilder.must(QueryBuilders.termsQuery("orderId", new ArrayList<String>()));
            }
        }

        if (StrUtil.isNotBlank(queryBo.getOrderId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("orderId", queryBo.getOrderId()));
        }
        if (StrUtil.isNotBlank(queryBo.getUserName())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("userName", queryBo.getUserName()));
        }
        // 处理申请时间参数
        appendApplyDateParam(boolQueryBuilder, queryBo);

        if (queryBo.getRefundStatus() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("refundStatus", queryBo.getRefundStatus()));
        }
        // 买家取消：展示所有买家撤回的售后单
        if (RefundSellerQueryTabEnum.REFUND_BUYER_CANCEL.equals(queryBo.getTab())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("hasCancel", Boolean.TRUE));
        } else if (RefundSellerQueryTabEnum.REFUND_ALL.equals(queryBo.getTab())) {
            // 退款-全部：所有的仅退款、未发货整单取消的售后单
            // 待发货状态下的订单退款，也是仅退款
            boolQueryBuilder.must(QueryBuilders.termsQuery("refundMode",
                    Arrays.asList(RefundModeEnum.ORDER_REFUND.getCode(), RefundModeEnum.GOODS_REFUND.getCode())));
        } else if (RefundSellerQueryTabEnum.RETURN_ALL.equals(queryBo.getTab())) {
            // 退货-全部：所有的退货数据
            boolQueryBuilder.must(QueryBuilders.termQuery("refundMode", RefundModeEnum.RETURN_AND_REFUND.getCode()));
        } else if (RefundSellerQueryTabEnum.REFUND_WAIT_PROCESS.equals(queryBo.getTab())) {
            // 退款-待处理：待供应商审核的退款
            // 订单退款和仅退款都是退款
            boolQueryBuilder.must(QueryBuilders.termsQuery("refundMode",
                            Arrays.asList(RefundModeEnum.GOODS_REFUND.getCode(), RefundModeEnum.ORDER_REFUND.getCode())))
                    .must(QueryBuilders.termQuery("refundStatus", RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode()));
        } else if (RefundSellerQueryTabEnum.RETURN_WAIT_PROCESS.equals(queryBo.getTab())) {
            // 退货-待处理：待店铺审核的售后单、待店铺确认收货的退货
            boolQueryBuilder.must(QueryBuilders.termQuery("refundMode", RefundModeEnum.RETURN_AND_REFUND.getCode()))
                    .must(QueryBuilders.termsQuery("refundStatus",
                            Arrays.asList(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode(), RefundStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode())));
        } else if (RefundSellerQueryTabEnum.RETURN_BUYER_CANCEL.equals(queryBo.getTab())) {
            // 退货-待处理：待店铺审核的售后单、待店铺确认收货的退货
            boolQueryBuilder.must(QueryBuilders.termQuery("refundMode", RefundModeEnum.RETURN_AND_REFUND.getCode()))
                    .must(QueryBuilders.termsQuery("refundStatus",
                            Arrays.asList(RefundStatusEnum.BUYER_CANCEL.getCode())));
        } else {
            // 这里就是不能识别的查询tab，设置一个不存在的数据，让查询结果为空
            boolQueryBuilder.must(QueryBuilders.termQuery("refundMode", RefundModeEnum.UNKNOWN.getCode()));
        }
        return buildSortAndPageAndCreateRequest(queryBo, boolQueryBuilder);
    }

    private SearchRequest buildPlatformSearchRequest(PlatformQueryRefundBo queryBo) {
        // 构建查询条件
        BoolQueryBuilder boolQueryBuilder = QueryBuilders.boolQuery();
        boolQueryBuilder.must(QueryBuilders.termQuery("isDelete", YesOrNoEnum.NO.getCode()));

        if (StrUtil.isNotBlank(queryBo.getOrderId())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("orderId", queryBo.getOrderId()));
        }
        if (StrUtil.isNotBlank(queryBo.getUserName())) {
            boolQueryBuilder.must(QueryBuilders.termQuery("userName", queryBo.getUserName()));
        }
        if (StrUtil.isNotBlank(queryBo.getProductName())) {
            // 商品名称模糊匹配
            String productName = eagleService.appendWildcard(queryBo.getProductName());
            boolQueryBuilder.must(QueryBuilders.wildcardQuery("productName", productName));
        }
        if (queryBo.getShopId() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("shopId", queryBo.getShopId()));
        }
        if (StrUtil.isNotBlank(queryBo.getShopName())) {
            boolQueryBuilder.must(QueryBuilders.matchPhraseQuery("shopName", queryBo.getShopName()));
        }
        if (StrUtil.isNotBlank(queryBo.getProductIdOrSkuId())) {
            // productId OR skuId 精确匹配
            // skuId含有下划线，productId是long类型，会报错，所以如果是含有下划线，因为要精确匹配，一定是skuId，否则是productId
            // 如果不是long类型，则不匹配商品ID，否则会报错
            boolean isLong = NumberUtil.isLong(queryBo.getProductIdOrSkuId());
            if (isLong) {
                boolQueryBuilder.must(QueryBuilders.termQuery("productId", queryBo.getProductIdOrSkuId()));
            } else {
                boolQueryBuilder.must(QueryBuilders.termQuery("skuId", queryBo.getProductIdOrSkuId()));
            }
        }
        if (queryBo.getRefundStatus() != null) {
            boolQueryBuilder.must(QueryBuilders.termQuery("refundStatus", queryBo.getRefundStatus().getCode()));
        }
        // 处理申请时间参数
        appendApplyDateParam(boolQueryBuilder, queryBo);
        // 退款-全部：所有的仅退款、未发货整单取消的售后单
        // 待发货状态下的订单退款，也是仅退款
        if (RefundPlatformQueryTabEnum.REFUND_ALL.equals(queryBo.getTab())) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("refundMode",
                    Arrays.asList(RefundModeEnum.ORDER_REFUND.getCode(), RefundModeEnum.GOODS_REFUND.getCode())));
        } else if (RefundPlatformQueryTabEnum.RETURN_ALL.equals(queryBo.getTab())) {
            // 退货-全部：所有的退货数据
            boolQueryBuilder.must(QueryBuilders.termQuery("refundMode", RefundModeEnum.RETURN_AND_REFUND.getCode()));
        } else if (RefundPlatformQueryTabEnum.REFUND_WAIT_PROCESS.equals(queryBo.getTab())) {
            // 退款-待处理：待供应商审核的退款
            // 订单退款和仅退款都是退款
            boolQueryBuilder.must(QueryBuilders.termsQuery("refundMode",
                            Arrays.asList(RefundModeEnum.GOODS_REFUND.getCode(), RefundModeEnum.ORDER_REFUND.getCode())))
                    .must(QueryBuilders.termQuery("refundStatus", RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode()));
        } else if (RefundPlatformQueryTabEnum.RETURN_WAIT_PROCESS.equals(queryBo.getTab())) {
            // 退货-待处理：待店铺审核的售后单、待店铺确认收货的退货
            boolQueryBuilder.must(QueryBuilders.termQuery("refundMode", RefundModeEnum.RETURN_AND_REFUND.getCode()))
                    .must(QueryBuilders.termQuery("refundStatus", RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode()));
        } else {
            // 这里就是不能识别的查询tab，设置一个不存在的数据，让查询结果为空
            boolQueryBuilder.must(QueryBuilders.termQuery("refundMode", RefundModeEnum.UNKNOWN.getCode()));
        }
        return buildSortAndPageAndCreateRequest(queryBo, boolQueryBuilder);
    }


    private void appendApplyDateParam(BoolQueryBuilder boolQueryBuilder, QueryRefundBaseBo baseBo) {
        if (baseBo.getApplyTimeStart() != null || baseBo.getApplyTimeEnd() != null) {
            RangeQueryBuilder rangeQueryBuilder = QueryBuilders.rangeQuery("applyDate");
            if (baseBo.getApplyTimeStart() != null) {
                rangeQueryBuilder.gte(baseBo.getApplyTimeStart().getTime());
            }
            if (baseBo.getApplyTimeEnd() != null) {
                rangeQueryBuilder.lte(baseBo.getApplyTimeEnd().getTime());
            }
            boolQueryBuilder.must(rangeQueryBuilder);
        }
    }


    private SearchRequest buildSortAndPageAndCreateRequest(BasePageReq searchBo, BoolQueryBuilder boolQueryBuilder) {
        // 构建排序
        List<SortBuilder<FieldSortBuilder>> sortBuilders = buildFieldSortList();
        // 整合查询条件
        int from = PageUtil.getStart(searchBo.getPageNo(), searchBo.getPageSize());
        SearchSourceBuilder sourceBuilder = new SearchSourceBuilder()
                .query(boolQueryBuilder)
                .from(from)
                .size(searchBo.getPageSize())
                .trackTotalHits(true);
        // 设置排序
        sortBuilders.forEach(sourceBuilder::sort);
        // 创建搜索请求
        SearchRequest searchRequest = new SearchRequest(esIndexProps.getIdxRefund());
        searchRequest.source(sourceBuilder);
        return searchRequest;
    }

    private List<SortBuilder<FieldSortBuilder>> buildFieldSortList() {
        List<SortBuilder<FieldSortBuilder>> sortBuilders = new ArrayList<>(2);
        appendDefaultFieldSort(sortBuilders);
        return sortBuilders;
    }

    private void appendDefaultFieldSort(List<SortBuilder<FieldSortBuilder>> sortBuilders) {
        // 默认根据售后单申请(创建)时间倒序排列
        sortBuilders.add(SortBuilders.fieldSort("applyDate").order(SortOrder.DESC));
    }


    private BasePageResp<UserRefundBo> searchAndConvertForUser(SearchRequest searchRequest, BasePageReq searchBo) {
        BasePageResp<EsOrderRefundBo> resultBo = doSearch(searchRequest, searchBo);
        // 根据查询结果填充相关数据
        return fillRefundInfoForUser(resultBo);
    }

    private BasePageResp<SellerRefundBo> searchAndConvertForSeller(SearchRequest searchRequest, BasePageReq searchBo) {
        BasePageResp<EsOrderRefundBo> resultBo = doSearch(searchRequest, searchBo);
        // 根据查询结果填充相关数据
        return fillRefundInfoForSeller(resultBo);
    }

    private BasePageResp<PlatformRefundBo> searchAndConvertForPlatform(SearchRequest searchRequest, BasePageReq searchBo) {
        BasePageResp<EsOrderRefundBo> resultBo = doSearch(searchRequest, searchBo);
        // 根据查询结果填充相关数据
        return fillRefundInfoForPlatform(resultBo);
    }

    private BasePageResp<EsOrderRefundBo> doSearch(SearchRequest searchRequest, BasePageReq searchBo) {
        // 调用ES进行查询和聚合
        EagleQueryResult searchResult = eagleService.queryByCondition(searchRequest);
        log.debug("【售后搜索】入参为: {}, 搜索结果为: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(searchResult));
        // 解析搜索结果
        BasePageResp<EsOrderRefundBo> resultBo = resolveSearchResult(searchResult, searchBo);
        log.debug("【售后搜索】入参为: {}, 订单结果: {}", JsonUtil.toJsonString(searchBo), JsonUtil.toJsonString(resultBo));
        return resultBo;
    }

    /**
     * 解析搜索结果
     *
     * @param searchResult ES结果
     * <AUTHOR>
     */
    private BasePageResp<EsOrderRefundBo> resolveSearchResult(EagleQueryResult searchResult, BasePageReq searchBo) {
        if (searchResult.getTotalHit() != null && searchResult.getTotalHit() == 0) {
            BasePageResp<EsOrderRefundBo> basePageResp = new BasePageResp<>();
            basePageResp.setPageNo(searchBo.getPageNo());
            basePageResp.setPageSize(searchBo.getPageSize());
            basePageResp.setData(Collections.emptyList());
            basePageResp.setTotalCount(0L);
            basePageResp.setPages(0);
            return basePageResp;
        }
        // 解析商品列表
        List<EsOrderRefundBo> refundList = searchResult.getHits().stream()
                .map(hit -> JsonUtil.parseObject(hit, EsOrderRefundBo.class))
                .collect(Collectors.toList());
        // 构造分页结果
        int totalHit = searchResult.getTotalHit().intValue();
        BasePageResp<EsOrderRefundBo> basePageResp = new BasePageResp<>();
        basePageResp.setData(refundList);
        basePageResp.setPages(PageUtil.totalPage(totalHit, searchBo.getPageSize()));
        basePageResp.setTotalCount(searchResult.getTotalHit());
        basePageResp.setPageNo(searchBo.getPageNo());
        basePageResp.setPageSize(searchBo.getPageSize());
        return basePageResp;
    }


    /**
     * 商家售后列表填充相关数据
     *
     * @param resultBo 搜索结果
     * <AUTHOR>
     */
    private BasePageResp<UserRefundBo> fillRefundInfoForUser(BasePageResp<EsOrderRefundBo> resultBo) {
        if (CollUtil.isEmpty(resultBo.getData())) {
            return PageResultHelper.transfer(resultBo, UserRefundBo.class);
        }
        List<String> orderIdList = new ArrayList<>();
        List<Long> refundIdList = new ArrayList<>();
        resultBo.getData().forEach(es -> {
            orderIdList.add(es.getOrderId());
            refundIdList.add(es.getRefundId());
        });
        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
        Map<String, Order> orderMap = orderList.stream()
                .collect(Collectors.toMap(Order::getOrderId, order -> order));
        // 当前是每页的数据，虽然后续不一定会用到所有明细，但还是先一次性查出来
        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(orderIdList);
        Map<String, List<OrderItem>> orderItemGroupOrderMap = orderItemList.stream()
                .collect(Collectors.groupingBy(OrderItem::getOrderId));
        Map<Long, OrderItem> orderItemMap = orderItemList.stream()
                .collect(Collectors.toMap(OrderItem::getId, orderItem -> orderItem));
        // 反查售后表，ES没有设置全部数据
        List<OrderRefund> refundList = orderRefundRepository.listByIds(refundIdList);
        Map<Long, OrderRefund> refundMap = refundList.stream()
                .collect(Collectors.toMap(OrderRefund::getId, refund -> refund));
        // 相关订单所有有效的售后
        Map<String, List<OrderRefund>> orderValidRefundMap = getValidByOrderId(orderIdList);

        // 查询交易设置，获取售后维权期
        int refundCloseDays = getRefundCloseConfigDays();

        return PageResultHelper.transfer(resultBo, UserRefundBo.class, (es, result) -> {
            Order order = orderMap.get(es.getOrderId());
            result.setOrderPayAmount(order.getTotalAmount());

            // 设置默认值然后重置
            boolean hasOverAfterSale = RefundStatusHelper.hasOverAfterSale(refundCloseDays, order.getFinishDate());
            result.setHasOverAfterSales(hasOverAfterSale);

            OrderRefund orderRefund = refundMap.get(es.getRefundId());
            result.setRefundStatus(orderRefund.getStatus());
            result.setRefundStatusDesc(RefundStatusEnum.getDesc(orderRefund.getStatus()));
            result.setApplyDate(DateUtil.date(es.getApplyDate()));
            result.setRefundAmount(orderRefund.getAmount());
            result.setOrderItemId(es.getOrderItemId());
            result.setContactUserName(orderRefund.getContactPerson());
            result.setContactUserPhone(orderRefund.getContactCellPhone());
            result.setRefundPayType(orderRefund.getRefundPayType());
            result.setRefundQuantity(orderRefund.getReturnQuantity());
            result.setRefundPayTypeDesc(RefundPayTypeEnum.getDesc(orderRefund.getRefundPayType()));
            result.setManagerConfirmDate(orderRefund.getManagerConfirmDate());
            result.setReason(orderRefund.getReason());
            result.setHasAllReturn(orderRefund.getHasAllReturn());
            result.setRefundMode(orderRefund.getRefundMode());

            // 是否可以重新申请
            boolean canReapply = orderRefundBizAssist.canReapply(order, orderRefund, orderValidRefundMap.get(orderRefund.getOrderId()), hasOverAfterSale);
            result.setCanReapply(canReapply);

            RefundTypeEnum refundType = orderRefundBizAssist.getRefundType(orderRefund);
            result.setRefundType(refundType.getCode());

            // 如果是整单退，则设置所有的商品信息，否则只设置当前退款的商品信息
            if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) || orderRefund.getHasAllReturn()) {
                List<RefundItemBo> refundItemBoList = orderItemGroupOrderMap.get(es.getOrderId()).stream()
                        .map(this::convertRefundItem)
                        .collect(Collectors.toList());
                result.setItemList(refundItemBoList);
            }
            // 理论上此时一定存在orderItemId且大于0，防止空指针还是判断以下
            else if (es.getOrderItemId() != null && es.getOrderItemId() > 0) {
                OrderItem orderItem = orderItemMap.get(es.getOrderItemId());
                RefundItemBo refundItemBo = convertRefundItem(orderItem);
                result.setItemList(Collections.singletonList(refundItemBo));
            }
            // 退货退款，且状态为 待买家寄货，显示【请退货】按钮
            if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(es.getRefundMode()) &&
                    RefundAuditStatusEnum.WAIT_BUYER_SEND.getCode().equals(es.getSellerAuditStatus())) {
                result.setShowReturnGoodsBtn(true);
            }
            // 是否明细退：不是订单退款，且没有全部退货，且有订单项ID
            boolean whetherItem = !RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) &&
                    Boolean.FALSE.equals(result.getHasAllReturn()) && result.getOrderItemId() != null && result.getOrderItemId() > 0;
            result.setWhetherItemRefund(whetherItem);


            // 填充是否取消按钮
            fillRefundCanCancel(orderRefund, result);
        });
    }

    private void fillRefundCanCancel(OrderRefund orderRefund, UserRefundBo result) {
        if (orderRefund == null) {
            return;
        }
        if (Boolean.TRUE.equals(orderRefund.getHasCancel())) {
            result.setShowCancelRefundBtn(false);
            return;
        }
        boolean auditing = RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(orderRefund.getStatus())
                || RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode().equals(orderRefund.getStatus());
        // 【取消售后】订单退款，或仅退款，且是待供应商审核或待平台审核时，显示
        boolean onlyRefund = RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) ||
                RefundModeEnum.GOODS_REFUND.getCode().equals(orderRefund.getRefundMode());
        if (onlyRefund && auditing) {
            result.setShowCancelRefundBtn(true);
            return;
        }
        // 【取消售后】退货退款时，待供应商审核或待买家寄货时，显示
        boolean sellerAuditOrBuyerSend = RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(orderRefund.getStatus())
                || RefundStatusEnum.WAIT_BUYER_SEND.getCode().equals(orderRefund.getStatus());
        if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(orderRefund.getRefundMode()) && sellerAuditOrBuyerSend) {
            result.setShowCancelRefundBtn(true);
        }
    }

    /**
     * 商家售后列表填充相关数据。没有判空，理论上这种查出来一定会有数据的
     *
     * @param resultBo 搜索结果
     * <AUTHOR>
     */
    private BasePageResp<SellerRefundBo> fillRefundInfoForSeller(BasePageResp<EsOrderRefundBo> resultBo) {
        if (CollUtil.isEmpty(resultBo.getData())) {
            return PageResultHelper.transfer(resultBo, SellerRefundBo.class);
        }
        List<String> orderIdList = new ArrayList<>();
        List<Long> refundIdList = new ArrayList<>();
        resultBo.getData().forEach(es -> {
            orderIdList.add(es.getOrderId());
            refundIdList.add(es.getRefundId());
        });
        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
        Map<String, Order> orderMap = orderList.stream()
                .collect(Collectors.toMap(Order::getOrderId, order -> order));
        // 当前是每页的数据，虽然后续不一定会用到所有明细，但还是先一次性查出来
        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(orderIdList);
        Map<Long, OrderItem> orderItemMap = orderItemList.stream()
                .collect(Collectors.toMap(OrderItem::getId, orderItem -> orderItem));
        TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
        // 反查售后表，ES没有设置全部数据
        List<OrderRefund> refundList = orderRefundRepository.listByIds(refundIdList);
        Map<Long, OrderRefund> refundMap = refundList.stream()
                .collect(Collectors.toMap(OrderRefund::getId, refund -> refund));
        return PageResultHelper.transfer(resultBo, SellerRefundBo.class, (es, result) -> {
            Order order = orderMap.get(es.getOrderId());
            if (order != null) {
                result.setOrderPayAmount(order.getTotalAmount());
            }

            OrderRefund orderRefund = refundMap.get(es.getRefundId());
            if (orderRefund != null) {
                result.setRefundStatus(orderRefund.getStatus());
                result.setRefundStatusDesc(RefundStatusEnum.getDesc(orderRefund.getStatus()));

                result.setApplyDate(DateUtil.date(es.getApplyDate()));
                result.setRefundAmount(orderRefund.getAmount());
                result.setOrderItemId(es.getOrderItemId());
                result.setRefundQuantity(orderRefund.getReturnQuantity());

                result.setReason(orderRefund.getReason());
                result.setContactPerson(orderRefund.getContactPerson());
                result.setContactCellPhone(orderRefund.getContactCellPhone());
                result.setSellerRemark(orderRefund.getSellerRemark());
                result.setRefundPayType(orderRefund.getRefundPayType());
                result.setRefundPayTypeDesc(RefundPayTypeEnum.getDesc(orderRefund.getRefundPayType()));
                result.setReasonDetail(orderRefund.getReasonDetail());
                result.setManagerRemark(orderRefund.getManagerRemark());
                result.setManagerConfirmDate(orderRefund.getManagerConfirmDate());
                result.setCertPic1(orderRefund.getCertPic1());
                result.setCertPic2(orderRefund.getCertPic2());
                result.setCertPic3(orderRefund.getCertPic3());

                RefundTypeEnum refundType = orderRefundBizAssist.getRefundType(orderRefund);
                result.setRefundType(refundType.getCode());

                // 如果是整单退，前端显示固定文案【订单所有商品】，否则设置单个商品信息
                if (!es.getHasAllReturn() && !RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode())) {
                    OrderItem orderItem = orderItemMap.get(es.getOrderItemId());
                    if (orderItem != null) {
                        RefundItemBo refundItemBo = convertRefundItem(orderItem);
                        result.setItemList(Collections.singletonList(refundItemBo));
                        result.setProductDesc(refundItemBo.getProductName());
                    }
                } else {
                    result.setProductDesc("订单所有商品");
                }
            }

            // 退货退款，且买家填写了物流信息，显示【查看物流】按钮
            if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(es.getRefundMode()) &&
                    StrUtil.isNotBlank(es.getShipOrderNumber())) {
                result.setShowWayBillBtn(true);
            }
            // 填充剩余审核时间
            appendShopApproveDeadlineForSeller(result, setting);
        });
    }

    /**
     * 商家售后列表填充相关数据。没有判空，理论上这种查出来一定会有数据的
     *
     * @param resultBo 搜索结果
     * <AUTHOR>
     */
    private BasePageResp<PlatformRefundBo> fillRefundInfoForPlatform(BasePageResp<EsOrderRefundBo> resultBo) {
        if (CollUtil.isEmpty(resultBo.getData())) {
            return PageResultHelper.transfer(resultBo, PlatformRefundBo.class);
        }
        List<String> orderIdList = new ArrayList<>();
        List<Long> refundIdList = new ArrayList<>();
        resultBo.getData().forEach(es -> {
            orderIdList.add(es.getOrderId());
            refundIdList.add(es.getRefundId());
        });
        List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
        Map<String, Order> orderMap = orderList.stream()
                .collect(Collectors.toMap(Order::getOrderId, order -> order));
        // 当前是每页的数据，虽然后续不一定会用到所有明细，但还是先一次性查出来
        List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(orderIdList);
        Map<Long, OrderItem> orderItemMap = orderItemList.stream()
                .collect(Collectors.toMap(OrderItem::getId, orderItem -> orderItem));
        // 反查售后表，ES没有设置全部数据
        List<OrderRefund> refundList = orderRefundRepository.listByIds(refundIdList);
        Map<Long, OrderRefund> refundMap = refundList.stream()
                .collect(Collectors.toMap(OrderRefund::getId, refund -> refund));
        return PageResultHelper.transfer(resultBo, PlatformRefundBo.class, (es, result) -> {
            Order order = orderMap.get(es.getOrderId());
            if (order != null) {
                result.setOrderPayAmount(order.getTotalAmount());
            } else {
                result.setOrderPayAmount(BigDecimal.ZERO);
            }
            result.setApplyDate(DateUtil.date(es.getApplyDate()));

            OrderRefund orderRefund = refundMap.get(es.getRefundId());
            if (orderRefund != null) {
                result.setRefundAmount(orderRefund.getAmount());
                result.setRefundStatus(orderRefund.getStatus());
                result.setRefundStatusDesc(RefundStatusEnum.getDesc(orderRefund.getStatus()));
                result.setRefundQuantity(orderRefund.getReturnQuantity());
                RefundTypeEnum refundType = orderRefundBizAssist.getRefundType(orderRefund);
                result.setRefundType(refundType.getCode());
            } else {
                log.warn("【订单搜索】售后单ID为: {}, 未查询到售后单信息", es.getRefundId());
            }

            result.setApplyDate(DateUtil.date(es.getApplyDate()));
            result.setOrderItemId(es.getOrderItemId());


            // 如果是整单退，前端显示固定文案【订单所有商品】，否则设置单个商品信息
            if (!es.getHasAllReturn()) {
                OrderItem orderItem = orderItemMap.get(es.getOrderItemId());
                if (orderItem != null) {
                    RefundItemBo refundItemBo = convertRefundItem(orderItem);
                    result.setItemList(Collections.singletonList(refundItemBo));
                    result.setProductDesc(refundItemBo.getProductName());
                }
            }
            // 退货退款，且买家填写了物流信息，显示【查看物流】按钮
            if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(es.getRefundMode()) &&
                    StrUtil.isNotBlank(es.getShipOrderNumber())) {
                result.setShowWayBillBtn(true);
            }
        });
    }


    private RefundItemBo convertRefundItem(OrderItem orderItem) {
        RefundItemBo refundItemBo = new RefundItemBo();
        refundItemBo.setProductId(orderItem.getProductId());
        refundItemBo.setSkuId(orderItem.getSkuId());
        refundItemBo.setProductName(orderItem.getProductName());
        refundItemBo.setMainImagePath(orderItem.getThumbnailsUrl());

        String skuDesc = SkuUtil.assembleSkuName(orderItem.getColor(), orderItem.getSize(), orderItem.getVersion());
        refundItemBo.setSkuDesc(skuDesc);
        return refundItemBo;
    }

    private void appendShopApproveDeadlineForSeller(SellerRefundBo sellerRefund, TradeSiteSettingBo setting) {
        // 待审核时，设置审核截止时间
        if (!(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(sellerRefund.getRefundStatus()) ||
                RefundStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode().equals(sellerRefund.getRefundStatus()))) {
            return;
        }
        if (setting == null || StrUtil.isBlank(setting.getShopConfirmTimeout())) {
            return;
        }
        // 交易设置中的供应商审核超时时间
        int timeoutDays = Integer.parseInt(setting.getShopConfirmTimeout());
        // 申请时间+超时时间=截止时间
        Date deadline = DateUtil.offsetDay(sellerRefund.getApplyDate(), timeoutDays);
        Date now = new Date();
        // 如果当前时间已经超过了截止时间，不需要再计算了
        if (now.after(deadline)) {
            return;
        }
        // 计算剩余时间毫秒数
        long diffMs = DateUtil.betweenMs(now, deadline);
        // 格式化剩余时间  xx天xx小时xx分
        String formatDiff = DateUtil.formatBetween(diffMs, BetweenFormatter.Level.MINUTE);
        sellerRefund.setRemainApproveDeadlineDesc(formatDiff);
    }

    private int getRefundCloseConfigDays() {
        TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
        if (setting == null) {
            return 0;
        }
        String refundCloseConfigDays = setting.getSalesReturnTimeout();
        log.info("【订单】获取售后维权期配置为: {}", refundCloseConfigDays);
        refundCloseConfigDays = StrUtil.nullToDefault(refundCloseConfigDays, "0");
        return Integer.parseInt(refundCloseConfigDays);
    }

    private Map<String, List<OrderRefund>> getValidByOrderId(List<String> orderIdList) {
        List<OrderRefund> refundList = orderRefundRepository.getByOrderIdList(orderIdList);
        return Optional.ofNullable(refundList).map(list ->
                        list.stream()
                                // 有效的
                                .filter(refund -> RefundStatusHelper.refundValid(refund.getHasCancel(), refund.getSellerAuditStatus(), refund.getManagerConfirmStatus()))
                                .collect(Collectors.groupingBy(OrderRefund::getOrderId)))
                .orElse(Collections.emptyMap());
    }
}
