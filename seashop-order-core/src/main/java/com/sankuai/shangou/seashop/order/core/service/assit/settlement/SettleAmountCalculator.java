package com.sankuai.shangou.seashop.order.core.service.assit.settlement;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.finance.domain.PendingSettlementOrder;
import com.sankuai.shangou.seashop.order.dao.finance.repository.PendingSettlementOrderRepository;
import com.sankuai.shangou.seashop.order.finance.service.SettlementConfigService;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettlementConfigResp;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SettleAmountCalculator {

    @Resource
    private PendingSettlementOrderRepository pendingSettlementOrderRepository;

    @Resource
    private SettlementConfigService settlementConfigService;

    /**
     * 计算结算分账金额
     * 如果是【企业网银】支付的，手续费固定是10；
     * 全部分账的时候：支付确认金额/交易总金额*（交易总金额*费率  需要四舍五入），然后得到的值四舍五入
     * 部分分账的时候：确认金额/原交易金额*手续费总额（手续费总额=原交易金额×费率 需要四舍五入） ，然后得到的值四舍五入
     * <p>处理待结算数据，肯定是支付成功的才能处理，所以 order 对应的批次支付记录 batchNoRecordList 与 orderList 应该是一比一的关系</p>
     *
     * @param order             当前处理结算的订单，支付成功的订单
     * @param batchNoRecordList 订单支付成功记录对应的batchNo对应的所有支付记录
     * @param orderList         同一批次支付的订单
     *                          java.math.BigDecimal
     * <AUTHOR>
     */
    public BigDecimal calculate(Order order, List<OrderPayRecord> batchNoRecordList, List<Order> orderList) {
        log.info("【结算】开始计算结算金额,订单信息:{}, 支付记录信息:{}, 同一支付批次订单列表信息:{}",
                JsonUtil.toJsonString(order), JsonUtil.toJsonString(batchNoRecordList), JsonUtil.toJsonString(orderList));
        BigDecimal charge = BigDecimal.ZERO;
        List<OrderPayRecord> allPays = batchNoRecordList.stream()
                .filter(r -> PayStatusEnum.PAY_SUCCESS.getCode().equals(r.getPayStatus()))
                .collect(Collectors.toList());
        log.info("【结算】支付成功的支付记录:{}", JsonUtil.toJsonString(allPays));
        if (null != order.getPayment() && order.getPayment().equals(PayMethodEnum.COMPANY_BANK.getCode())) {
            log.info("【结算】企业网银支付");
            boolean isLast = allPays.get(allPays.size() - 1).getOrderId().equals(order.getOrderId());
            log.info("【结算】是否是最后一笔支付:{}", isLast);
            if (allPays.size() == 1) {
                charge = BigDecimal.TEN;
            } else if (!isLast) {
                BigDecimal totalAmount = orderList.stream()
                        .map(SettleAmountCalculator::getOrderTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                log.info("【结算】计算结算金额, 同支付批次交易总金额:{}", totalAmount);
                BigDecimal totalCharge = BigDecimal.TEN;
                charge = order.getActualPayAmount()
                        .divide(totalAmount, 2, RoundingMode.HALF_UP)
                        .multiply(totalCharge);
            } else {
                List<String> orderIdList = orderList.stream().map(Order::getOrderId).collect(Collectors.toList());
                List<PendingSettlementOrder> pendingSettlementOrders = pendingSettlementOrderRepository.listByOrderIdList(orderIdList);
                log.info("【结算】待结算数据:{}", JsonUtil.toJsonString(pendingSettlementOrders));
                BigDecimal hasPendAmount = pendingSettlementOrders.stream()
                        .map(PendingSettlementOrder::getChannelAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                log.info("【结算】已结算金额:{}", hasPendAmount);
                charge = BigDecimal.TEN.subtract(hasPendAmount);
            }
        } else {
            SettlementConfigResp config = settlementConfigService.getConfig();
            BigDecimal settlementFeeRate = config.getSettlementFeeRate() == null ? BigDecimal.ZERO : config.getSettlementFeeRate();
            if (allPays.size() == 1) {
                log.info("【结算】allPays.size() == 1");
                BigDecimal baseCharge = getOrderTotalAmount(order).multiply(settlementFeeRate).setScale(2, RoundingMode.HALF_UP);
                log.info("【结算】计算结算金额, 基础手续费:{}", baseCharge);
                charge = order.getActualPayAmount()
                        .divide(getOrderTotalAmount(order), 4, RoundingMode.HALF_UP)
                        .multiply(baseCharge);
            } else {
                log.info("【结算】部分分账");
                BigDecimal totalAmount = orderList.stream()
                        .map(SettleAmountCalculator::getOrderTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                log.info("【结算】计算结算金额, 同支付批次交易总金额:{}", totalAmount);
                BigDecimal totalCharge = totalAmount.multiply(settlementFeeRate);
                charge = order.getActualPayAmount()
                        .divide(totalAmount, 4, RoundingMode.HALF_UP)
                        .multiply(totalCharge);
            }
        }
        if (order.getActualPayAmount().compareTo(BigDecimal.ZERO) <= 0) {
            charge = BigDecimal.ZERO;
        }
        // 四舍五入
        charge = charge.setScale(2, RoundingMode.HALF_UP);
        log.info("【结算】计算结算金额结果:{}", charge);
        return charge;
    }

    /**
     * 商品应付+运费+税 - 优惠券金额 - 积分抵扣金额-满额减金额 (减掉积分抵扣部分)
     *
     * @param o
     * @return
     */
    @NotNull
    private static BigDecimal getOrderTotalAmount(Order o) {
        BigDecimal total = o.getProductTotalAmount()
                .add(o.getFreight())
                .add(o.getTax())
                .subtract(o.getCouponAmount())
                .subtract(o.getDiscountAmount())
                .subtract(o.getMoneyOffAmount());
        log.info("【结算】订单总金额:{}", total);
        return total;
    }

}
