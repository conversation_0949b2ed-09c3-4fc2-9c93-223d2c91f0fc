package com.sankuai.shangou.seashop.pay.core.service;

import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPayReverseResultDto;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ReverseOrderQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.ReverseOrderResp;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description:
 */
public interface ReverseOrderService {

    /**
     * 更新并发送同步退款结果
     *
     * @param adaPayReverseResultDto
     */
    void updateAndSendSyncReverse(AdaPayReverseResultDto adaPayReverseResultDto);


    /**
     * 条件查询单个退款信息
     *
     * @param request
     * @return
     */
    ReverseOrderResp getOne(ReverseOrderQueryReq request);
}
