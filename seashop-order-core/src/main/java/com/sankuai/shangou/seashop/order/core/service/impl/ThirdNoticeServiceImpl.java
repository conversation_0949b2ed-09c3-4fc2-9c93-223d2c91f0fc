package com.sankuai.shangou.seashop.order.core.service.impl;

import java.util.Date;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.order.common.constant.LeafConst;
import com.sankuai.shangou.seashop.order.core.service.ThirdNoticeService;
import com.sankuai.shangou.seashop.order.dao.core.domain.ThirdNotice;
import com.sankuai.shangou.seashop.order.dao.core.repository.ThirdNoticeRepository;
import com.sankuai.shangou.seashop.order.thrift.core.request.SaveThirdNoticeReq;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.crypto.SecureUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/29 17:42
 */
@Slf4j
@Service
public class ThirdNoticeServiceImpl implements ThirdNoticeService {

    @Resource
    private ThirdNoticeRepository thirdNoticeRepository;
    @Resource
    private LeafService leafService;

    @Override
    public Long saveThirdNotice(SaveThirdNoticeReq req) {
        String noticeCode = generateNoticeCode(req);
        ThirdNotice notice = thirdNoticeRepository.getByNoticeCode(noticeCode);
        if (notice != null) {
            log.warn("通知已存在, noticeCode:{}", noticeCode);
            return notice.getId();
        }

        Long noticeId = leafService.generateNoBySnowFlake(LeafConst.KEY_THIRD_NOTICE_ID);
        notice = new ThirdNotice();
        notice.setId(noticeId);
        notice.setBizCode(req.getBizCode());
        notice.setNoticeCode(noticeCode);
        notice.setNoticeBody(req.getNoticeBody());
        notice.setNoticeType(req.getNoticeType().getCode());
        notice.setNoticeSource(req.getNoticeSource().getCode());
        thirdNoticeRepository.save(notice);
        return noticeId;
    }

    /**
     * 生成通知编号
     *
     * @param req req 通知内容
     * @return 通知编号
     */
    private String generateNoticeCode(SaveThirdNoticeReq req) {
        return SecureUtil.md5(DateUtil.format(new Date(), DatePattern.NORM_DATETIME_MINUTE_PATTERN) + JsonUtil.toJsonString(req));
    }
}
