package com.sankuai.shangou.seashop.order.core.service.assit;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.MessageNoticeSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.SiteSettingQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMessageNoticeSettingRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseSitSettingRes;
import com.sankuai.shangou.seashop.order.common.config.MessageProps;
import com.sankuai.shangou.seashop.order.common.enums.PlatformMessageTemplateEnum;
import com.sankuai.shangou.seashop.order.common.remote.MessageRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.user.MemberContactBo;
import com.sankuai.shangou.seashop.order.core.service.model.sms.BaseSmsMsg;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class MessageNoticeHelper {
    @Resource
    private SiteSettingQueryFeign siteSettingQueryFeign;
    @Resource
    private MessageRemoteService messageRemoteService;
    @Resource
    private BizNoGenerator bizNoGenerator;
    @Resource
    private MessageNoticeSettingQueryFeign messageNoticeSettingQueryThriftService;
    @Resource
    private MessageProps messageProps;

    public void noticeForTemplate(MemberContactBo contact, PlatformMessageTemplateEnum messageTemplate, BaseSmsMsg smsMsg, String msgBody) {
        log.info("【消息通知】发送消息通知, contact={}, messageTemplate={}, msgBody={}",
                JsonUtil.toJsonString(contact), messageTemplate, msgBody);
        // 从配置获取消息配置，退款申请是否需要发送邮件和短信
        BaseMessageNoticeSettingRes setting = ThriftResponseHelper.executeThriftCall(() -> messageNoticeSettingQueryThriftService.getMessageNoticeSettingByType(messageTemplate.getCode()));
        log.info("获取消息通知设置,模板:{}", JsonUtil.toJsonString(setting));
        Long requestId = bizNoGenerator.generateMessageUniqueId();
        // 发送消息
        this.noticeIfNecessary(contact, setting, messageTemplate, requestId, smsMsg, msgBody);
    }

    public void noticeIfNecessary(MemberContactBo memberContact, BaseMessageNoticeSettingRes setting,
                                  PlatformMessageTemplateEnum messageTemplate,
                                  Long requestId, BaseSmsMsg smsMsg, String messageBody) {
        log.info("【消息通知】发送消息通知, requestId={}, messageBody={}, memberContact={}, setting={}",
                requestId, messageBody, JsonUtil.toJsonString(memberContact), JsonUtil.toJsonString(setting));
        String mobile = memberContact.getContact();
        String email = memberContact.getEmail();
        if (StrUtil.isNotBlank(mobile) && Boolean.TRUE.equals(setting.getSmsNotice())) {
            Integer smsCode = getSmsCode(messageTemplate);
            messageRemoteService.sendSms(smsCode, JsonUtil.toJsonString(smsMsg), mobile, requestId);
        }
        if (StrUtil.isNotBlank(email) && Boolean.TRUE.equals(setting.getEmaillNotice())) {
            messageRemoteService.sendEmail(messageTemplate.getDesc(), messageBody, email, requestId);
        }
    }

    public String getSiteName() {
        BaseSitSettingRes siteSetting = ThriftResponseHelper.executeThriftCall(() -> siteSettingQueryFeign.getSetting());
        if (siteSetting == null) {
            return "";
        }
        return siteSetting.getSiteName();
    }

    /**
     * 构建消息体，目前短信和邮箱消息体一样
     *
     * @param messageTemplate 消息模板
     * @param msgParams       消息参数
     * @return 消息体
     */
    public String buildEmailBody(PlatformMessageTemplateEnum messageTemplate, String siteName, String... msgParams) {
        // 默认取模板配置的
        String pattern = messageTemplate.getMsgPattern();
        if (messageProps != null && MapUtils.isNotEmpty(messageProps.getMessageConfigMap()) &&
                messageProps.getMessageConfigMap().containsKey(messageTemplate.name()) &&
                StrUtil.isNotBlank(messageProps.getMessageConfigMap().get(messageTemplate.name()).getEmailContentPattern())) {
            pattern = messageProps.getMessageConfigMap().get(messageTemplate.name()).getEmailContentPattern();
        }
        String msgBody = String.format(pattern, msgParams);
        // 站点名称不一定有
        if (StrUtil.isNotBlank(siteName)) {
            msgBody = msgBody + "【" + siteName + "】";
        }
        return msgBody;
    }

    public Integer getSmsCode(PlatformMessageTemplateEnum template) {
        Integer smsCode = template.getSmsTplCode();
        if (MapUtils.isNotEmpty(messageProps.getMessageConfigMap()) &&
                messageProps.getMessageConfigMap().containsKey(template.name()) &&
                messageProps.getMessageConfigMap().get(template.name()).getSmsTemplateCode() != null) {
            smsCode = messageProps.getMessageConfigMap().get(template.name()).getSmsTemplateCode();
        }
        log.info("【消息通知】获取短信模板code, template={}, smsCode={}", template, smsCode);
        return smsCode;
    }

}
