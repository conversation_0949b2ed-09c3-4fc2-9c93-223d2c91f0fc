package com.sankuai.shangou.seashop.order.core.thrift.impl;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.thrift.core.enums.TResultCode;
import com.sankuai.shangou.seashop.order.common.config.SystemSwitchProps;
import com.sankuai.shangou.seashop.order.common.enums.SwitchBizTypeEnum;
import com.sankuai.shangou.seashop.order.core.service.OrderPayService;
import com.sankuai.shangou.seashop.order.core.service.model.pay.InitiatePayBo;
import com.sankuai.shangou.seashop.order.thrift.core.OrderPayCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.InitiatePayReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.InitiatePayResp;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/orderPayCmd")
public class OrderPayCmdController implements OrderPayCmdFeign {

    @Resource
    private OrderPayService orderPayService;
    @Resource
    private SystemSwitchProps systemSwitchProps;

    @PostMapping(value = "/initiatePay", consumes = "application/json")
    @Override
    public ResultDto<InitiatePayResp> initiatePay(@RequestBody InitiatePayReq initiatePayReq) {
        log.info("【支付】发起支付, 请求参数={}", JsonUtil.toJsonString(initiatePayReq));
        SystemSwitchProps.SwitchContent switchContent = systemSwitchProps.getSwitch(SwitchBizTypeEnum.ALLOW_INIT_PAY.getKey());
        if (switchContent != null && Boolean.FALSE.equals(switchContent.getAllowFlag())) {
            log.info("【支付】发起支付, 系统当前不允许发起支付");
            ResultDto<InitiatePayResp> result = new ResultDto<>();
            String msg = StrUtil.isBlank(switchContent.getForbiddenMessage()) ? SwitchBizTypeEnum.ALLOW_INIT_PAY.getDefaultMessage() : switchContent.getForbiddenMessage();
            result.fail(TResultCode.SERVER_ERROR.value(), msg);
            return result;
        }
        return ThriftResponseHelper.responseInvoke("initiatePay", initiatePayReq, func -> {
            InitiatePayBo initiatePayBo = JsonUtil.copy(initiatePayReq, InitiatePayBo.class);
            // 业务逻辑处理
            String param = orderPayService.initiatePayment(initiatePayBo);
            InitiatePayResp resp = new InitiatePayResp();
            resp.setPayParamMap(param);
            return resp;
        });
    }
}
