package com.sankuai.shangou.seashop.pay.core.mq.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2024/1/30/030
 * @description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderReverseExceptionMsg {

    /**
     * 渠道支付ID
     */
    private String channelPayId;

    /**
     * 退款流水ID
     */
    private String reverseId;

    /**
     * 时间
     */
    private Date startTime;
}
