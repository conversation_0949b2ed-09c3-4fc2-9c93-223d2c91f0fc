package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 异常订单信息
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class ExceptionOrderInfoBo {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 订单编号
     */
    private String orderId;

    /**
     * 支付批次号，多笔订单同时支付时批次号相同
     */
    private String batchNo;

    /**
     * 支付渠道对应的唯一标识。支付流水号
     */
    private String payNo;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 支付金额
     */
    private BigDecimal payAmount;

    /**
     * 异常类型，1，重复支付；2，超时关闭；
     */
    private Integer errorType;
    /**
     * 异常类型描述
     */
    private String errTypeDesc;

    /**
     * 退款时间
     */
    private Date refundTime;

    /**
     * 退款状态。0:待退款；1:退款中；2:退款完成；3:退款失败；
     */
    private Integer refundStatus;
    /**
     * 退款状态描述
     */
    private String refundStatusDesc;

    /**
     * 退款失败原因
     */
    private String refundFailReason;

    /**
     * 退款批次号
     */
    private String refundBatchNo;

    /**
     * 退款操作员
     */
    private String refundManager;

}
