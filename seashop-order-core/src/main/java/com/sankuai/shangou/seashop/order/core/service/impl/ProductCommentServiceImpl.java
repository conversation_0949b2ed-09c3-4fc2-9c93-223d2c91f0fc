package com.sankuai.shangou.seashop.order.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributeLock;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.order.common.es.model.comment.EsProductCommentModel;
import com.sankuai.shangou.seashop.order.common.es.model.comment.EsProductCommentParam;
import com.sankuai.shangou.seashop.order.common.es.service.EsProductCommentService;
import com.sankuai.shangou.seashop.order.common.remote.RemoteMemberService;
import com.sankuai.shangou.seashop.order.common.remote.model.user.RemoteMemberBo;
import com.sankuai.shangou.seashop.order.core.service.ProductCommentService;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductCommentImageBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductCommentSummaryBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ReplyProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ShopCommentSummaryBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.ProductComment;
import com.sankuai.shangou.seashop.order.dao.core.domain.ProductCommentImage;
import com.sankuai.shangou.seashop.order.dao.core.repository.ProductCommentImageRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.ProductCommentRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.CommentEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.HideProductCommentReq;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:35
 */
@Service
@Slf4j
public class ProductCommentServiceImpl implements ProductCommentService {

    @Resource
    private ProductCommentRepository productCommentRepository;
    @Resource
    private ProductCommentImageRepository productCommentImageRepository;
    @Resource
    private BaseLogAssist baseLogAssist;
    @Resource
    private EsProductCommentService esProductCommentService;
    @Resource
    private RemoteMemberService remoteMemberService;

    private static String FORMAT_STR = "%s_%s";

    @Override
    public BasePageResp<ProductCommentBo> queryProductCommentForPlatform(BasePageParam pageParam, QueryProductCommentBo queryBo) {
        return queryProductComment(pageParam, queryBo);
    }

    @Override
    public BasePageResp<ProductCommentBo> queryProductCommentForSeller(BasePageParam pageParam, QueryProductCommentBo queryBo) {
        return queryProductComment(pageParam, queryBo);
    }

    @Override
    public BasePageResp<ProductCommentBo> queryProductCommentForMall(BasePageParam pageParam, QueryProductCommentBo queryBo) {
        queryBo.setFilterHidden(Boolean.TRUE);
        return queryProductComment(pageParam, queryBo);
    }

    private BasePageResp<ProductCommentBo> queryProductComment(BasePageParam pageParam, QueryProductCommentBo queryBo) {
        dealMallQueryStatus(queryBo);
        /*if (queryBo.getUseCache()) {
            log.info("product comment use cache");
            AssertUtil.throwIfTrue(pageParam.pageNum * pageParam.pageSize > CommonConst.MAX_PAGE_TOTAL_DEFAULT,
                CommonConst.MESSAGE_REACH_MAX_PAGE_TOTAL);
            return queryProductCommentUseCache(pageParam, queryBo);
        }*/

        log.info("product comment use db");
        return queryProductCommentUseDb(pageParam, queryBo);
    }

    private BasePageResp<ProductCommentBo> queryProductCommentUseDb(BasePageParam pageParam, QueryProductCommentBo queryBo) {
        Page<ProductComment> result = PageHelper.startPage(pageParam);
        LambdaQueryWrapper<ProductComment> wrapper = productCommentWrapperBuilder(queryBo);
        productCommentRepository.list(wrapper);

        BasePageResp<ProductComment> transfer = PageResultHelper.transfer(result, ProductComment.class);
        return dealPageProductComment(transfer, queryBo);
    }

    private BasePageResp<ProductCommentBo> queryProductCommentUseCache(BasePageParam pageParam, QueryProductCommentBo queryBo) {
        BasePageResp<EsProductCommentModel> result = esProductCommentService.page(pageParam, JsonUtil.copy(queryBo, EsProductCommentParam.class));

        BasePageResp<ProductComment> transfer = PageResultHelper.transfer(result, ProductComment.class);
        return dealPageProductComment(transfer, queryBo);
    }

    private BasePageResp<ProductCommentBo> dealPageProductComment(BasePageResp<ProductComment> pageResult, QueryProductCommentBo queryBo) {
        List<ProductComment> productCommentList = pageResult.getData();
        List<Long> productCommentIds = productCommentList.stream().map(ProductComment::getProductCommentId).collect(Collectors.toList());

        List<ProductCommentImage> imageList = productCommentImageRepository.listByProductCommentIdList(productCommentIds);
        Map<Long, List<ProductCommentImage>> imageMap = imageList.stream().collect(Collectors.groupingBy(ProductCommentImage::getProductCommentId));

        // 查询用户信息
        List<Long> userIds = productCommentList.stream().map(ProductComment::getUserId).collect(Collectors.toList());
        Map<Long, RemoteMemberBo> userMap = remoteMemberService.listByMemberIds(userIds)
                .stream().collect(Collectors.toMap(RemoteMemberBo::getId, Function.identity(), (k1, k2) -> k1));

        return PageResultHelper.transfer(pageResult, ProductCommentBo.class, (db, bo) -> {
            List<ProductCommentImage> subImgaeList = imageMap.getOrDefault(db.getProductCommentId(), Collections.EMPTY_LIST);
            Map<Integer, List<ProductCommentImage>> subImageMap = subImgaeList.stream().collect(Collectors.groupingBy(ProductCommentImage::getCommentType));
            bo.setFirstCommentImageList(JsonUtil.copyList(subImageMap.get(CommentEnum.CommentType.FIRST.getCode()), ProductCommentImageBo.class));
            bo.setAppendCommentImageList(JsonUtil.copyList(subImageMap.get(CommentEnum.CommentType.APPEND.getCode()), ProductCommentImageBo.class));
            bo.setSkuName(this.getSkuName(db.getSpec1Value(), db.getSpec2Value(), db.getSpec3Value()));
            // 是否存在初次评论
            // bo.setHasReply(db.getReplyDate() != null);

            // 判断是否可以回复 存在初次评论/追评没有回复的则可以回复
            if (db.getReplyDate() == null || (db.getAppendDate() != null && db.getReplyAppendDate() == null)) {
                bo.setNeedReply(Boolean.TRUE);
            }
            bo.setHasReply(!bo.isNeedReply());

            // 获取用户头像 和昵称
            RemoteMemberBo user = userMap.get(db.getUserId());
            if (user != null) {
                bo.setUserAvatar(user.getPhoto());
                bo.setUserNickName(user.getNick());
            }
        });
    }


    /**
     * 拼接skuName
     *
     * @param spec1Value
     * @param spec2Value
     * @param spec3Value
     * @return
     */
    public String getSkuName(String spec1Value, String spec2Value, String spec3Value) {
        List<String> specValues = new ArrayList<>();
        if (StringUtils.isNotEmpty(spec1Value)) {
            specValues.add(spec1Value);
        }
        if (StringUtils.isNotEmpty(spec2Value)) {
            specValues.add(spec2Value);
        }
        if (StringUtils.isNotEmpty(spec3Value)) {
            specValues.add(spec3Value);
        }
        return StringUtils.join(specValues, StrUtil.COMMA);
    }

    @Override
    public void hideProductCommentForPlatForm(HideProductCommentReq hideProductCommentReq) {
        Long productCommentId = hideProductCommentReq.getProductCommentId();
        ProductComment productComment = productCommentRepository.getByProductCommentId(productCommentId);
        AssertUtil.throwIfTrue(productComment == null, "商品评价不存在");

        ProductComment updateComment = new ProductComment();
        updateComment.setId(productComment.getId());
        updateComment.setHasHidden(Boolean.TRUE);
        productCommentRepository.updateById(updateComment);

        // 记录操作日志
        baseLogAssist.recordLog(ExaminModelEnum.ORDER, ExaProEnum.MODIFY,
                "隐藏商品评价",
                hideProductCommentReq.getOperationUserId(),
                hideProductCommentReq.getOperationShopId(),
                null, hideProductCommentReq);
    }

    @Override
    public void replyProductCommentForSeller(ReplyProductCommentBo commentBo) {
        ProductComment productComment = productCommentRepository.getByProductCommentId(commentBo.getProductCommentId());
        AssertUtil.throwIfTrue(productComment == null, "商品评价不存在");
        AssertUtil.throwIfTrue(!productComment.getShopId().equals(commentBo.getShopId()), "无权操作");

        ProductComment updProductComment = new ProductComment();
        updProductComment.setId(productComment.getId());
        boolean needUpd = Boolean.FALSE;
        if (StringUtils.isNotEmpty(commentBo.getReplyContent())) {
            updProductComment.setReplyContent(commentBo.getReplyContent());
            updProductComment.setReplyDate(new Date());
            needUpd = Boolean.TRUE;
        }
        if (StringUtils.isNotEmpty(commentBo.getReplyAppendContent())) {
            updProductComment.setReplyAppendContent(commentBo.getReplyAppendContent());
            updProductComment.setReplyAppendDate(new Date());
            needUpd = Boolean.TRUE;
        }

        if (needUpd) {
            productCommentRepository.updateById(updProductComment);
        }

        // 记录操作日志
        baseLogAssist.recordLog(ExaminModelEnum.ORDER, ExaProEnum.MODIFY,
                "回复商品评价",
                commentBo.getOperationUserId(),
                commentBo.getOperationShopId(),
                JsonUtil.copy(productComment, ReplyProductCommentBo.class), commentBo);
    }

    @Override
    public List<ProductCommentBo> queryProductCommentByOrderId(String orderId) {
        List<ProductComment> productCommentList = productCommentRepository.listByOrderId(orderId);
        List<Long> productCommentIdList = productCommentList.stream().map(ProductComment::getProductCommentId).collect(Collectors.toList());
        // 获取到评论图片后根据评论id + 评论类型分组
        List<ProductCommentImage> commentImageList = productCommentImageRepository.listByProductCommentIdList(productCommentIdList);
        Map<String, List<ProductCommentImage>> commentImageMap = commentImageList.stream()
                .collect(Collectors.groupingBy(item -> String.format(FORMAT_STR, item.getProductCommentId(), item.getCommentType())));

        List<ProductCommentBo> productCommentBoList = JsonUtil.copyList(productCommentList, ProductCommentBo.class);
        productCommentBoList.forEach(productCommentBo -> {

            Long productCommentId = productCommentBo.getProductCommentId();
            List<ProductCommentImage> firstCommentImgList = commentImageMap.get(String.format(FORMAT_STR, productCommentId, CommentEnum.CommentType.FIRST.getCode()));
            List<ProductCommentImage> appendCommentImgList = commentImageMap.get(String.format(FORMAT_STR, productCommentId, CommentEnum.CommentType.APPEND.getCode()));

            productCommentBo.setFirstCommentImageList(productCommentImageBoBuild(firstCommentImgList));
            productCommentBo.setAppendCommentImageList(productCommentImageBoBuild(appendCommentImgList));
            productCommentBo.setHasReply(productCommentBo.getReplyDate() != null);
        });
        return productCommentBoList;
    }

    @Override
    public ProductCommentSummaryBo queryProductCommentSummary(Long productId) {
        ProductCommentSummaryBo summaryBo = new ProductCommentSummaryBo();
        summaryBo.setProductId(productId);

        // 设置评分
        Map<Integer, Integer> reviewMarkMap = productCommentRepository.getReviewMarkMap(productId);
        summaryBo.setFiveStarCount(reviewMarkMap.getOrDefault(CommentEnum.ReviewMark.FIVE.getCode(), 0));
        summaryBo.setFourStarCount(reviewMarkMap.getOrDefault(CommentEnum.ReviewMark.FOUR.getCode(), 0));
        summaryBo.setThreeStarCount(reviewMarkMap.getOrDefault(CommentEnum.ReviewMark.THREE.getCode(), 0));
        summaryBo.setTwoStarCount(reviewMarkMap.getOrDefault(CommentEnum.ReviewMark.TWO.getCode(), 0));
        summaryBo.setOneStarCount(reviewMarkMap.getOrDefault(CommentEnum.ReviewMark.ONE.getCode(), 0));
        // 设置评论总数
        summaryBo.setTotalCount(reviewMarkMap.values().stream().mapToInt(Integer::intValue).sum());

        // 有图数
        summaryBo.setHasImageCount(productCommentRepository.countHasImage(productId));
        // 追评数
        summaryBo.setAppendCount(productCommentRepository.countAppendComment(productId));
        // 计算中评率
        summaryBo.setMiddleRate(countRate(CommentEnum.ReviewMarkRange.MIDDLE, reviewMarkMap, summaryBo.getTotalCount()));
        // 计算差评率
        summaryBo.setBadRate(countRate(CommentEnum.ReviewMarkRange.BAD, reviewMarkMap, summaryBo.getTotalCount()));
        // 计算好评率
        // summaryBo.setGoodRate(countRate(CommentEnum.ReviewMarkRange.GOOD, reviewMarkMap, summaryBo.getTotalCount()));
        summaryBo.setGoodRate(BigDecimal.valueOf(100).subtract(summaryBo.getMiddleRate()).subtract(summaryBo.getBadRate()));
        // 计算综合评分
        summaryBo.setScore(countSource(reviewMarkMap, summaryBo.getTotalCount()));
        return summaryBo;
    }

    @Override
    public ShopCommentSummaryBo queryShopCommentSummary(Long shopId) {
        ShopCommentSummaryBo summaryBo = new ShopCommentSummaryBo();

        summaryBo.setWaitReplyCount(productCommentRepository.countWaitReply(shopId));
        summaryBo.setTotalCount(Math.toIntExact(productCommentRepository.count(new LambdaQueryWrapper<ProductComment>()
                .eq(ProductComment::getShopId, shopId)
                .eq(ProductComment::getDeleted, Boolean.FALSE)
        )));
        return summaryBo;
    }

    @Override
    public ShopCommentSummaryBo queryMCommentSummary() {
        ShopCommentSummaryBo summaryBo = new ShopCommentSummaryBo();

//        summaryBo.setWaitReplyCount(productCommentRepository.countWaitReply(shopId));
        summaryBo.setTotalCount(Math.toIntExact(productCommentRepository.count(new LambdaQueryWrapper<ProductComment>())));
        return summaryBo;
    }

    @Override
    @DistributeLock(keyPattern = LockConst.LOCK_ES_PRODUCT_COMMENT_PATTERN, scenes =
            LockConst.SCENE_ES_PRODUCT_COMMENT,
            waitLock = true,
            keyValues = {"{0}"})
    public void buildEsProductComment(Long productCommentId) {
        ProductComment productComment = productCommentRepository.getByProductCommentId(productCommentId);
        if (productComment == null || productComment.getDeleted()) {
            esProductCommentService.deleteById(String.valueOf(productCommentId));
        } else {
            esProductCommentService.partUpdate(JsonUtil.copy(productComment, EsProductCommentModel.class));
        }
    }

    /**
     * 计算评价率
     *
     * @param reviewMarkRange 评分等级
     * @param totalCount      总数
     * @return 评价率
     */
    private BigDecimal countRate(CommentEnum.ReviewMarkRange reviewMarkRange, Map<Integer, Integer> reviewMarkMap, int totalCount) {
        if (reviewMarkRange == null || MapUtils.isEmpty(reviewMarkMap) || totalCount <= 0) {
            return BigDecimal.ZERO;
        }

        int curCount = 0;
        Integer[] markRange = reviewMarkRange.getMarkRange();
        for (Integer mark : markRange) {
            curCount += reviewMarkMap.getOrDefault(mark, 0);
        }

        return BigDecimal.valueOf(curCount).divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
    }

    /**
     * 计算综合评分
     *
     * @param reviewMarkMap 评分map
     * @param totalCount    总数
     * @return 综合评分
     */
    private BigDecimal countSource(Map<Integer, Integer> reviewMarkMap, int totalCount) {
        if (MapUtils.isEmpty(reviewMarkMap) || totalCount <= 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal totalScore = BigDecimal.ZERO;
        for (Map.Entry<Integer, Integer> entry : reviewMarkMap.entrySet()) {
            totalScore = totalScore.add(BigDecimal.valueOf(entry.getKey()).multiply(BigDecimal.valueOf(entry.getValue())));
        }

        return totalScore.divide(BigDecimal.valueOf(totalCount), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 商品评论构建筛选wrapper
     *
     * @param queryBo 查询条件
     * @return wrapper
     */
    private LambdaQueryWrapper<ProductComment> productCommentWrapperBuilder(QueryProductCommentBo queryBo) {
        LambdaQueryWrapper<ProductComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(queryBo.getProductId() != null, ProductComment::getProductId, queryBo.getProductId());
        wrapper.eq(StringUtils.isNotEmpty(queryBo.getSkuId()), ProductComment::getSkuId, queryBo.getSkuId());
        wrapper.eq(StringUtils.isNotEmpty(queryBo.getOrderId()), ProductComment::getOrderId, queryBo.getOrderId());
        wrapper.like(StringUtils.isNotEmpty(queryBo.getUserName()), ProductComment::getUserName, queryBo.getUserName());
        wrapper.eq(StringUtils.isNotEmpty(queryBo.getUserMobile()), ProductComment::getUserMobile, queryBo.getUserMobile());
        wrapper.eq(queryBo.getShopId() != null, ProductComment::getShopId, queryBo.getShopId());
        wrapper.like(StringUtils.isNotEmpty(queryBo.getProductName()), ProductComment::getProductName, queryBo.getProductName());
        wrapper.eq(queryBo.getReviewMark() != null, ProductComment::getReviewMark, queryBo.getReviewMark());
        wrapper.isNotNull(queryBo.isHasAppend(), ProductComment::getAppendDate);
        // 过滤掉删除的，隐藏的评论
        wrapper.eq(ProductComment::getDeleted, Boolean.FALSE);
        if (CommentEnum.ReplyStatus.UN_HANDLE.getCode().equals(queryBo.getReplyStatus())) {
            // 有待处理的评价或者追评(风控已经通过的)
            wrapper.and(comment -> comment.and(re -> re.isNull(ProductComment::getReplyDate))
                    .or(ap -> ap.isNotNull(ProductComment::getAppendDate).isNull(ProductComment::getReplyAppendDate)));
        }
        wrapper.eq(queryBo.isFilterHidden(), ProductComment::getHasHidden, Boolean.FALSE);
        wrapper.in(CollectionUtils.isNotEmpty(queryBo.getReviewMarkRange()), ProductComment::getReviewMark, queryBo.getReviewMarkRange());
        // 如果筛选有图片 hasImage 或者 appendHasImage 为true
        if (queryBo.isHasImage()) {
            wrapper.and(i -> i.eq(ProductComment::getHasImage, Boolean.TRUE).or().eq(ProductComment::getAppendHasImage, Boolean.TRUE));
        }
        wrapper.orderByDesc(ProductComment::getId);
        return wrapper;
    }

    /**
     * 处理商城端的筛选状态
     *
     * @param queryBo 查询条件
     */
    private void dealMallQueryStatus(QueryProductCommentBo queryBo) {
        CommentEnum.MallCommentStatus status = queryBo.getStatus();
        if (status == null || status.equals(CommentEnum.MallCommentStatus.ALL)) {
            return;
        }

        switch (status) {
            case GOOD:
                queryBo.setReviewMarkRange(Arrays.asList(CommentEnum.ReviewMarkRange.GOOD.getMarkRange()));
                break;
            case MIDDLE:
                queryBo.setReviewMarkRange(Arrays.asList(CommentEnum.ReviewMarkRange.MIDDLE.getMarkRange()));
                break;
            case BAD:
                queryBo.setReviewMarkRange(Arrays.asList(CommentEnum.ReviewMarkRange.BAD.getMarkRange()));
                break;
            case HAS_APPEND:
                queryBo.setHasAppend(Boolean.TRUE);
                break;
            case HAS_IMAGE:
                queryBo.setHasImage(Boolean.TRUE);
                break;
            default:
                break;
        }
    }

    /**
     * 商品评价bo图片构建
     *
     * @param commentImageList 评价图片
     * @return 评价图片bo
     */
    private List<ProductCommentImageBo> productCommentImageBoBuild(List<ProductCommentImage> commentImageList) {
        if (CollectionUtils.isEmpty(commentImageList)) {
            return Collections.EMPTY_LIST;
        }

        List<ProductCommentImageBo> imageBoList = JsonUtil.copyList(commentImageList, ProductCommentImageBo.class);
        return imageBoList;
    }
}
