package com.sankuai.shangou.seashop.order.core.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.DesensitizedUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.dto.ChangeFiled;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.order.common.config.PayProps;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.constant.LockConst;
import com.sankuai.shangou.seashop.order.common.remote.ExpressRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.common.utils.SettingUtil;
import com.sankuai.shangou.seashop.order.common.utils.SkuUtil;
import com.sankuai.shangou.seashop.order.common.utils.ThreadPoolUtil;
import com.sankuai.shangou.seashop.order.core.mq.publisher.OrderRefundMessagePublisher;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundService;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderBizAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderRefundBizAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.opLog.ChangeFieldDesc;
import com.sankuai.shangou.seashop.order.core.service.assit.opLog.OperationLogAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.RefundStatusHelper;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.ApplyRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.CancelOrderRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.CreateOrderRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.CreateOrderRefundItemBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.ItemRemainRefundInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.OrderItemRefundPreviewBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.OrderRefundPreviewBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformApproveBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.PlatformRefundDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.QueryErpRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.ReapplyRefundBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundAvaiableBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundItemBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundLogBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundLogListBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.RefundStepBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SaveRefundPlatformAuditBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerApproveParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerConfirmReceiveParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.SellerRefundDetailBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserDeliverParamBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserDeliveryBo;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserRefundDetailBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefundLog;
import com.sankuai.shangou.seashop.order.dao.core.po.QueryOrderRefundPagePo;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundLogRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.RefundEventEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.ExtRefundTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundLogStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundModeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundPayTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.PlatformApproveBatchReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderRefundResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.PlatformApproveBatchResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.PlatformApproveResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.UserRefundDetailExtResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.UserRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.UserRefundProductDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductBasicDto;
import com.sankuai.shangou.seashop.user.thrift.account.ShopMemberQueryFeign;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderRefundServiceImpl implements OrderRefundService {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private OrderBizAssist orderBizAssist;
    @Resource
    private OrderRefundBizAssist orderRefundBizAssist;
    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderRefundMessagePublisher orderRefundMessagePublisher;
    @Resource
    private OrderRefundLogRepository orderRefundLogRepository;
    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private ExpressRemoteService expressRemoteService;
    @Resource
    private ShopMemberQueryFeign shopMemberQueryFeign;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private PayProps payProps;
    @Resource
    private OperationLogAssist operationLogAssist;

    /**
     * 整单退款预览
     * <p>待发货状态下发起整单退款；待收货/已完成下 整单退货/退款</p>
     * <p>这里没有校验是否能退，让用户能进入页面，如果不能退，发起的时候会有校验</p>
     *
     * @param orderId 订单id
     * @param userBo  用户信息
     * <AUTHOR>
     */
    @Override
    public OrderRefundPreviewBo getOrderRefundPreview(String orderId, Long refundId, UserBo userBo) {
        // 如果传入了售后ID，根据售后详情重置
        OrderRefund orderRefund = null;
        if (refundId != null) {
            orderRefund = orderRefundRepository.getByIdIncludeDelete(refundId);
            orderId = orderRefund.getOrderId();
        }
        Order order = orderRepository.getByOrderId(orderId);
        orderBizAssist.validateOrder(userBo.getUserId(), order);
        OrderRefundPreviewBo previewBo = new OrderRefundPreviewBo();
        previewBo.setOrderDate(order.getOrderDate());
        previewBo.setOrderId(order.getOrderId());
        previewBo.setOrderStatus(order.getOrderStatus());
        previewBo.setOrderStatusDesc(OrderStatusEnum.getDesc(order.getOrderStatus()));
        previewBo.setOrderType(order.getOrderType());
        previewBo.setOrderTypeDesc(OrderTypeEnum.getDesc(order.getOrderType()));
        // 订单维度的退款金额为实付金额
        previewBo.setRemainRefundAmount(order.getTotalAmount());
        previewBo.setOrderTotalAmount(order.getTotalAmount());
        // 可退金额根据订单状态设置
        String remainAmountDesc = getRemainRefundAmountDesc(order);
        previewBo.setRemainRefundAmountDesc(remainAmountDesc);

        List<OrderItem> orderItemList = orderItemRepository.getByOrderId(orderId);
        Long quantity = orderItemList.stream().mapToLong(OrderItem::getQuantity).sum();
        // 整单的售后，数量直接是明细的所有数量
        previewBo.setRemainRefundQuantity(quantity);
        String contactUserName = StrUtil.blankToDefault(order.getShipTo(), userBo.getUserName());
        String contactUserPhone = StrUtil.blankToDefault(order.getCellPhone(), userBo.getUserPhone());
        previewBo.setContactUserName(contactUserName);
        previewBo.setContactUserPhone(contactUserPhone);
        if (orderRefund != null) {
            buildCommonRefundDetail(orderRefund, previewBo);
        }
        return previewBo;
    }

    /**
     * 获取订单明细退款预览
     *
     * @param orderId     订单id
     * @param userBo      用户信息
     * @param orderItemId 订单明细id
     * @return 预览信息。包括订单基本信息、金额，和联系人
     */
    @Override
    public OrderItemRefundPreviewBo getOrderItemRefundPreview(String orderId, Long orderItemId, Long refundId, UserBo userBo) {
        OrderRefund orderRefund = null;
        if (refundId != null) {
            orderRefund = orderRefundRepository.getByIdIncludeDelete(refundId);
            orderId = orderRefund.getOrderId();
            orderItemId = orderRefund.getOrderItemId();
        }
        Order order = orderRepository.getByOrderId(orderId);
        orderBizAssist.validateOrder(userBo.getUserId(), order);
        // 校验订单明细与订单是否匹配
        OrderItem orderItem = orderItemRepository.getById(orderItemId);
        checkOrderItemCanRefund(order, orderItem);
        // 校验退款记录是否存在
        List<OrderRefund> validRefundList = this.getValidByOrderId(orderId);
        // 校验是否有订单维度的售后，整单售后与单品售后互斥，不能同时存在
        checkRefundForOrderExists(validRefundList);
        ItemRemainRefundInfoBo remainInfo = orderRefundBizAssist.getItemRemainRefund(orderItem, validRefundList);
        if (remainInfo.getRemainQuantity() <= 0 || remainInfo.getRemainAmount().compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException("错误的退款申请,退款数量或金额超过订单明细数量或金额");
        }

        OrderItemRefundPreviewBo previewBo = new OrderItemRefundPreviewBo();
        previewBo.setOrderId(order.getOrderId());
        previewBo.setProductId(orderItem.getProductId());
        previewBo.setSkuId(orderItem.getSkuId());
        previewBo.setProductName(orderItem.getProductName());
        previewBo.setMainImagePath(orderItem.getThumbnailsUrl());
        previewBo.setQuantity(orderItem.getQuantity());
        previewBo.setProductAmount(orderItem.getRealTotalPrice());
        previewBo.setRemainRefundAmount(remainInfo.getRemainAmount());
        previewBo.setRemainRefundQuantity(remainInfo.getRemainQuantity());
        previewBo.setRealSalePrice(NumberUtil.div(orderItem.getRealTotalPrice(), orderItem.getQuantity(), 2));

        previewBo.setOrderStatus(order.getOrderStatus());
        previewBo.setOrderStatusDesc(OrderStatusEnum.getDesc(order.getOrderStatus()));

        ProductBasicDto product = productRemoteService.queryProductById(orderItem.getProductId());
        if (product != null) {
            previewBo.setMeasureUnit(product.getMeasureUnit());
        }
        String contactUserName = StrUtil.blankToDefault(order.getShipTo(), userBo.getUserName());
        String contactUserPhone = StrUtil.blankToDefault(order.getCellPhone(), userBo.getUserPhone());
        previewBo.setContactUserName(contactUserName);
        previewBo.setContactUserPhone(contactUserPhone);
        // 如果传入了售后ID，根据售后详情重置
        if (orderRefund != null) {
            buildCommonRefundDetail(orderRefund, previewBo);
        }
        return previewBo;
    }

    /**
     * 发货前，订单退款
     *
     * @param applyBo java.lang.Long
     * <AUTHOR>
     */
    @Override
    public Long applyOrderRefund(ApplyRefundBo applyBo) {
        Order order = orderRepository.getByOrderId(applyBo.getOrderId());
        // 校验，这里没有校验会产生并发的字段，所以放在分布式锁外面
        checkOrderCanRefund(applyBo.getUser(), order);
        // 待发货订单才能发起订单退款
        if (!OrderStatusEnum.isEqualsUnderSend(order.getOrderStatus())) {
            throw new BusinessException("只有待发货/待核销/待自提状态下才能发起订单退款");
        }
        // 设置数据
        setApplyDataForOrderRefund(applyBo, order);
        return handleOrderRefund(applyBo, order);
    }

    /**
     * 发货后/已完成 整单退款/退货退款
     *
     * @param applyBo java.lang.Long
     * <AUTHOR>
     */
    @Override
    public Long applyWholeOrderRefund(ApplyRefundBo applyBo) {
        Order order = orderRepository.getByOrderId(applyBo.getOrderId());
        // 校验，这里没有校验会产生并发的字段，所以放在分布式锁外面
        checkOrderCanRefund(applyBo.getUser(), order);
        // 待收货/已完成 状态下才能发起整单退款/退货退款
        if (!OrderStatusEnum.UNDER_RECEIVE.getCode().equals(order.getOrderStatus()) &&
                !OrderStatusEnum.FINISHED.getCode().equals(order.getOrderStatus())) {
            throw new BusinessException("只有待收货/已完成状态下才能发起整单退款/退货退款");
        }
        // 设置数据
        setApplyDataForWholeOrder(applyBo, order);
        return handleOrderRefund(applyBo, order);
    }

    private Long handleOrderRefund(ApplyRefundBo applyBo, Order order) {
        // 添加分布式锁，防止订单退款时的并发问题
        try {
            // 订单整单退款，退款明细也不能存在，所以锁的key为订单id
            String key = LockConst.LOCK_USER_REFUND_ORDER + applyBo.getOrderId();
            Long refundId = distributedLockService.tryLock(new LockKey(LockConst.SCENE_USER_REFUND_ORDER, key), () -> {
                List<OrderRefund> refundList = this.getValidByOrderId(order.getOrderId());
                AssertUtil.throwIfTrue(CollUtil.isNotEmpty(refundList), "错误的退款申请,订单已存在退款记录");
                // 整单退，订单明细ID设置为0，.net是任选订单明细的一个ID设置，后续跟进测试是否有影响
                applyBo.setOrderItemId(0L);
                return orderRefundBizAssist.saveOrderRefundData(applyBo, order);
            });
            // 发送MQ消息，下游消费
            orderRefundMessagePublisher.sendOrderRefund(applyBo.getOrderId(), refundId, RefundEventEnum.APPLY);
            return refundId;
        }
        catch (BusinessException be) {
            log.error("申请退款业务异常, orderId: {}", applyBo.getOrderId(), be);
            throw be;
        }
        catch (Throwable e) {
            log.error("申请退款失败, orderId: {}", applyBo.getOrderId(), e);
            throw new BusinessException("申请退款失败，请稍后重试");
        }
    }

    /**
     * 申请订单明细退款
     * <p>明细退时，需要支持同一个单品多次发起售后，只要单品所有有效的售后数量以及金额都没有超过可退就行，
     * 所谓有效是指售后没有被取消，且不是供应商拒绝的，平台驳回是一个最终状态，所以平台驳回的数据不能再次申请。
     * 申请单品售后时，没有校验数量与金额是否匹配，校验的是数量与金额两者任意一个是否超出可退</p>
     *
     * @param applyBo 申请退款参数
     * @return 退款详情
     */
    @Override
    public Long applyOrderItemRefund(ApplyRefundBo applyBo) {
        return applyOrderItemRefund(applyBo, null);
    }

    private Long applyOrderItemRefund(ApplyRefundBo applyBo, Order order) {
        log.info("[applyOrderItemRefund] applyBo:{}", JsonUtil.toJsonString(applyBo));
        if (order == null) {
            order = orderRepository.getByOrderId(applyBo.getOrderId());
        }

        // 校验，这里没有校验会产生并发的字段，所以放在分布式锁外面
        checkOrderCanRefund(applyBo.getUser(), order);
        // 待收货/已完成 状态下才能发起 单品退款/退货退款
        if (!OrderStatusEnum.UNDER_RECEIVE.getCode().equals(order.getOrderStatus()) &&
                !OrderStatusEnum.FINISHED.getCode().equals(order.getOrderStatus())) {
            throw new BusinessException("只有待收货/已完成状态下才能发起明细退款/退货退款");
        }
        // 添加分布式锁，防止订单退款时的并发问题
        try {
            // 订单整单退款，退款明细也不能存在，所以锁的key为订单id
            String key = LockConst.LOCK_USER_REFUND_ORDER + applyBo.getOrderId();
            Order finalOrder = order;
            Long refundId = distributedLockService.tryLock(new LockKey(LockConst.SCENE_USER_REFUND_ORDER, key), () -> {
                // 校验订单明细与订单是否匹配
                OrderItem orderItem = orderItemRepository.getById(applyBo.getOrderItemId());
                if (orderItem.getRealTotalPrice().compareTo(BigDecimal.ZERO) == 0 &&
                        RefundTypeEnum.ONLY_REFUND.equals(applyBo.getRefundType())) {
                    throw new BusinessException("商品实付0元，请发起退货退款");
                }
                // 根据需求，仅退款时，数量与金额需要联动，退货退款时用户可以任意输入，所以只校验一种情况
                if (RefundTypeEnum.ONLY_REFUND.equals(applyBo.getRefundType())) {
                    BigDecimal salePrice = NumberUtil.div(orderItem.getRealTotalPrice(), orderItem.getQuantity(), 2);
                    BigDecimal goalAmount = NumberUtil.mul(salePrice, applyBo.getRefundQuantity());
                    if (applyBo.getRefundAmount().compareTo(goalAmount) > 0) {
                        throw new BusinessException("退款金额超出数量最大可退金额");
                    }
                }

                // 设置数据
                setApplyDataForItem(applyBo, finalOrder, orderItem);
                // 先获取当前有效的售后记录
                List<OrderRefund> validRefundList = this.getValidByOrderId(finalOrder.getOrderId());
                // 校验是否存在订单维度的售后，单品与整单是互斥的
                checkRefundForOrderExists(validRefundList);
                ItemRemainRefundInfoBo validRefundInfo = orderRefundBizAssist.getItemRemainRefund(orderItem, validRefundList);
                if (log.isDebugEnabled()) {
                    log.debug("refund applyBo:{} validRefundInfo:{}", JsonUtil.toJsonString(applyBo), JsonUtil.toJsonString(validRefundInfo));
                }
                // 校验数量和金额是否能退
                if (applyBo.getApplyQuantity() > validRefundInfo.getRemainQuantity()) {
                    throw new BusinessException("错误的退款申请,退款数量超过订单明细数量");
                }
                // 对于金额，考虑商品实付金额就是0的情况，所以大于0即可
                boolean applyGtRemain = applyBo.getRefundAmount().compareTo(validRefundInfo.getRemainAmount()) > 0;
                // 申请的比剩下的还多
                if (applyGtRemain) {
                    throw new BusinessException("错误的退款申请,退款金额超过订单明细金额");
                }
                // 保存退款数据
                return orderRefundBizAssist.saveOrderRefundData(applyBo, finalOrder);
            });
            // 发送MQ消息，下游消费
            orderRefundMessagePublisher.sendOrderRefund(applyBo.getOrderId(), refundId, RefundEventEnum.APPLY);
            return refundId;
        }
        catch (BusinessException be) {
            log.error("申请明细退款业务异常, orderId: {}", applyBo.getOrderId(), be);
            throw be;
        }
        catch (Throwable e) {
            log.error("申请明细退款失败, orderId: {}", applyBo.getOrderId(), e);
            throw new BusinessException("申请退款失败，请稍后重试");
        }
    }

    @Override
    public void cancelOrderRefund(CancelOrderRefundBo cancelBo) {
        // 添加分布式锁，防止订单取消时的并发问题
        try {
            // 取消操作主要防止与供应商和平台审核的并发问题，所以锁的key为退款id
            String key = LockConst.LOCK_USER_REFUND_ORDER + cancelBo.getRefundId();
            OrderRefund orderRefund = distributedLockService.tryLock(new LockKey(LockConst.SCENE_USER_REFUND_ORDER, key), () -> {
                OrderRefund refund = orderRefundRepository.getById(cancelBo.getRefundId());
                log.info("取消退款, refundId: {}, refund: {}", cancelBo.getRefundId(), JsonUtil.toJsonString(refund));
                AssertUtil.throwIfNull(refund, "错误的退款申请,退款记录不存在");
                AssertUtil.throwIfTrue(refund.getHasCancel(), "该售后不可重复取消");
                // 判断状态是否可以取消
                boolean canCancel = canCancel(refund);
                AssertUtil.throwIfTrue(!canCancel, "该售后不可取消");
                orderRefundBizAssist.saveCancelOrderRefundData(refund, cancelBo.getUser());
                return refund;
            });
            // 发送MQ消息，下游消费
            orderRefundMessagePublisher.sendOrderRefund(orderRefund.getOrderId(), orderRefund.getId(), RefundEventEnum.CANCEL);
        }
        catch (BusinessException be) {
            log.error("取消退款业务异常, refundId: {}", cancelBo.getRefundId(), be);
            throw be;
        }
        catch (Throwable e) {
            log.error("取消退款失败, refundId: {}", cancelBo.getRefundId(), e);
            throw new BusinessException("取消退款失败，请稍后重试");
        }
    }

    /**
     * 重新发起售后申请
     * <p>未加锁，重新发起的前提是，售后已经取消，或者被供应商和平台拒绝，所以记录不会有并发修改的情况，暂未考虑同一个账号不同人操作的情况</p>
     *
     * @param applyBo void
     * <AUTHOR>
     */
    @Override
    public void reapplyRefund(ReapplyRefundBo applyBo) {
        OrderRefund originRefund = orderRefundRepository.getById(applyBo.getRefundId());
        if (originRefund == null) {
            throw new BusinessException("错误的退款申请,退款记录不存在");
        }
        Order order = orderRepository.getByOrderId(originRefund.getOrderId());
        // 校验是否能重新发起申请
        checkCanReapplyAndResetData(applyBo, originRefund, order, applyBo.getUser());
        // 重置更新退款数据
        // 是否整单退货，先与之前的保持一致
        applyBo.setHasAllReturn(originRefund.getHasAllReturn());
        // 如果是订单退
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(originRefund.getRefundMode())) {
            setApplyDataForOrderRefund(applyBo, order);
        }
        // 如果是整单退，设置运费，供应商审核时可以决定是否真的退运费
        else if (Boolean.TRUE.equals(originRefund.getHasAllReturn())) {
            setApplyDataForWholeOrder(applyBo, order);
        }
        // 单品售后
        else {
            // 校验订单明细与订单是否匹配
            OrderItem orderItem = orderItemRepository.getById(originRefund.getOrderItemId());
            // 设置数据
            setApplyDataForItem(applyBo, order, orderItem);
        }
        Long newRefundId = orderRefundBizAssist.saveReRefundData(originRefund, applyBo, order);
        // 发送MQ消息，下游消费
        try {
            orderRefundMessagePublisher.sendOrderRefund(originRefund.getOrderId(), newRefundId, RefundEventEnum.REAPPLY);
        }
        catch (Exception e) {
            log.warn("重新发起售后申请发送MQ消息失败, refundId: {}", applyBo.getRefundId(), e);
        }
    }

    @Override
    public UserRefundDetailBo userQueryDetail(Long refundId, UserBo user) {
        OrderRefund orderRefund = orderRefundRepository.getById(refundId);
        AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");
        AssertUtil.throwIfTrue(!orderRefund.getUserId().equals(user.getUserId()), "错误的退款申请,退款记录不属于该用户");
        // 构建公共的退款详情
        UserRefundDetailBo detail = new UserRefundDetailBo();
        buildCommonRefundDetail(orderRefund, detail);
        // 设置是否明细退
        boolean whetherItem = !RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) &&
                Boolean.FALSE.equals(orderRefund.getHasAllReturn()) && orderRefund.getOrderItemId() != null && orderRefund.getOrderItemId() > 0;
        detail.setWhetherItemRefund(whetherItem);
        // 设置商家的详情信息
        appendUserAppendDetail(detail, orderRefund);
        return detail;
    }

    @Override
    public UserRefundDetailExtResp userQueryDetailExt(Long refundId, UserBo user) {
        OrderRefund orderRefund = orderRefundRepository.getById(refundId);
        AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");
        AssertUtil.throwIfTrue(!orderRefund.getUserId().equals(user.getUserId()), "错误的退款申请,退款记录不属于该用户");
        // 构建公共的退款详情
        UserRefundDetailExtResp detailExt = new UserRefundDetailExtResp();

        List<UserRefundProductDto> productList = new ArrayList<>();
        UserRefundDetailBo detailBo = new UserRefundDetailBo();
        convertBaseCommonRefundDetail(orderRefund, detailBo);
        // 如果时整单退，包括了订单退款，则从订单获取并设置数据，否则从明细获取并设置
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) || orderRefund.getHasAllReturn()) {
            // 查询并设置订单支付金额
            Order order = orderRepository.getByOrderId(orderRefund.getOrderId());
            detailBo.setPayAmount(order.getActualPayAmount());
            detailBo.setFreightAmount(order.getFreight());
            List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(Collections.singletonList(orderRefund.getOrderId()));
            Long totalQuantity = orderItemList.stream().map(OrderItem::getQuantity).reduce(0L, Long::sum);
            detailBo.setOrderQuantity(totalQuantity);
            for (OrderItem orderItem : orderItemList) {
                UserRefundProductDto productDto = getUserRefundProductDto(orderItem);
                productList.add(productDto);
            }
        }
        else {
            OrderItem orderItem = orderItemRepository.getById(orderRefund.getOrderItemId());
            detailBo.setPayAmount(orderItem.getRealTotalPrice());
            detailBo.setFreightAmount(BigDecimal.ZERO);
            detailBo.setOrderQuantity(orderItem.getQuantity());
            // 如果是仅退款，返回前端的退货数量，用申请数量重置，为了支持单品多次售后，新增了申请数量字段，仅退款也需要设置数量
            if (RefundModeEnum.GOODS_REFUND.getCode().equals(orderRefund.getRefundMode())) {
                log.info("仅退款，退货数量重置为申请数量");
                detailBo.setRefundQuantity(orderRefund.getApplyQuantity());
            }

            UserRefundProductDto productDto = getUserRefundProductDto(orderItem);
            if (RefundModeEnum.GOODS_REFUND.getCode().equals(orderRefund.getRefundMode())) {
                productDto.setReturnQuantity(orderRefund.getApplyQuantity());
            }
            productList.add(productDto);
        }
        detailBo.setStatus(orderRefund.getStatus());
        detailBo.setStatusDesc(RefundStatusEnum.getDesc(orderRefund.getStatus()));
        // 设置是否明细退
        boolean whetherItem = !RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) &&
                Boolean.FALSE.equals(orderRefund.getHasAllReturn()) && orderRefund.getOrderItemId() != null && orderRefund.getOrderItemId() > 0;
        detailBo.setWhetherItemRefund(whetherItem);
        // 设置商家的详情信息
        appendUserAppendDetail(detailBo, orderRefund);

        UserRefundDetailResp detailResp = JsonUtil.copy(detailBo, UserRefundDetailResp.class);
        detailExt.setDetail(detailResp);
        detailExt.setProductList(productList);

        return detailExt;
    }

    private static UserRefundProductDto getUserRefundProductDto(OrderItem orderItem) {
        UserRefundProductDto productDto = new UserRefundProductDto();
        productDto.setProductId(orderItem.getProductId());
        productDto.setSkuId(orderItem.getSkuId());
        productDto.setProductName(orderItem.getProductName());
        productDto.setMainImagePath(orderItem.getThumbnailsUrl());
        String color = StrUtil.emptyToDefault(orderItem.getColor(), "");
        String size = StrUtil.emptyToDefault(orderItem.getSize(), "");
        String version = StrUtil.emptyToDefault(orderItem.getVersion(), "");
        productDto.setSkuDesc(color + size + version);
        List<String> skuDescList = new ArrayList<>();
        skuDescList.add(color);
        skuDescList.add(size);
        skuDescList.add(version);
        productDto.setSkuDescList(skuDescList);
        productDto.setPrice(orderItem.getSalePrice());
        productDto.setReturnQuantity(orderItem.getQuantity());
        return productDto;
    }

    @Override
    public SellerRefundDetailBo sellerQueryDetail(Long refundId, ShopBo shop) {
        OrderRefund orderRefund = orderRefundRepository.getById(refundId);
        AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");
        AssertUtil.throwIfTrue(!orderRefund.getShopId().equals(shop.getShopId()), "错误的退款申请,退款记录不属于该店铺");
        // 构建公共的退款详情
        SellerRefundDetailBo detail = new SellerRefundDetailBo();
        buildCommonRefundDetail(orderRefund, detail);
        // 设置供应商其他售后详情信息
        appendSellerRefundDetail(orderRefund, detail);
        detail.setRefundFinishDate(detail.getLastModifyTime());
        return detail;
    }

    @Override
    public PlatformRefundDetailBo platformQueryDetail(Long refundId) {
        OrderRefund orderRefund = orderRefundRepository.getById(refundId);
        AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");
        // 构建公共的退款详情
        PlatformRefundDetailBo detail = new PlatformRefundDetailBo();
        buildCommonRefundDetail(orderRefund, detail);
        // 处理商品信息
        Boolean hasAllReturn = orderRefund.getHasAllReturn();
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) || Boolean.TRUE.equals(hasAllReturn)) {
            detail.setItem(null);
        }
        else {
            OrderItem orderItem = orderItemRepository.getById(orderRefund.getOrderItemId());
            RefundItemBo itemBo = convertRefundItem(orderItem);
            detail.setItem(itemBo);
        }
        if (RefundStatusEnum.REFUND_SUCCESS.getCode().equals(orderRefund.getStatus())) {
            detail.setRefundFinishDate(orderRefund.getManagerConfirmDate());
        }
        return detail;
    }

    @Override
    public List<RefundDetailBo> queryRefundListByOrderIds(List<String> orderIds) {
        return Optional.ofNullable(orderRefundRepository.getByOrderIdListOrderByApplyTimeDesc(orderIds))
                .map(orderRefundList -> orderRefundList.stream().map(orderRefund -> {
                    RefundDetailBo detailBo = new RefundDetailBo();
                    convertBaseCommonRefundDetail(orderRefund, detailBo);
                    return detailBo;
                }).collect(Collectors.toList()))
                .orElse(null);
    }

    @Override
    public PlatformRefundDetailBo queryRefundDetailByRefundId(Long refundId) {
        return platformQueryDetail(refundId);
    }

    @Override
    public List<PlatformRefundDetailBo> queryRefundDetailBySourceRefundId(String sourceRefundId) {
        List<OrderRefund> refundList = orderRefundRepository.getBySourceRefundId(sourceRefundId);
        if (CollectionUtils.isEmpty(refundList)) {
            return Collections.emptyList();
        }
        return refundList.stream().map(orderRefund -> platformQueryDetail(orderRefund.getId())).collect(Collectors.toList());
    }

    @Override
    public BasePageResp<PlatformRefundDetailBo> queryErpRefundPage(BasePageParam pageParam, QueryErpRefundBo bo) {
        QueryOrderRefundPagePo po = new QueryOrderRefundPagePo();
        po.setPageNo(pageParam.getPageNum());
        po.setPageSize(pageParam.getPageSize());
        po.setShopId(bo.getShopId());
        po.setAuditStatus(bo.getAuditStatus());
        po.setRefundModes(bo.getRefundModes());
        po.setUserId(bo.getUserId());
        po.setOrderId(bo.getOrderId());
        if (CollectionUtils.isNotEmpty(bo.getStatusList())) {
            po.setStatusList(bo.getStatusList().stream().map(RefundStatusEnum::getCode).collect(Collectors.toList()));
        }
        switch (bo.getTimeType()) {
            case CREATE_TIME:
                po.setStartCreateTime(bo.getStartTime());
                po.setEndCreateTime(bo.getEndTime());
                break;
            case UPDATE_TIME:
                po.setStartUpdateTime(bo.getStartTime());
                po.setEndUpdateTime(bo.getEndTime());
                break;
            default:
                break;
        }
        Page<OrderRefund> refundPage = orderRefundRepository.queryOrderRefundPage(po);
        List<PlatformRefundDetailBo> refundDetailBos = Optional.ofNullable(refundPage.toPageInfo().getList())
                .orElse(Collections.emptyList())
                .stream().map(orderRefund -> platformQueryDetail(orderRefund.getId())).collect(Collectors.toList());
        return new BasePageResp<>(refundPage.getPageSize(), refundPage.getPageNum(),
                refundPage.getPages(), refundPage.getTotal(), refundDetailBos);
    }

    @Override
    public RefundLogListBo queryRefundLog(Long refundId) {
        OrderRefund orderRefund = orderRefundRepository.getById(refundId);
        AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");

        RefundLogListBo result = new RefundLogListBo();
        result.setUserId(orderRefund.getUserId());
        result.setShopId(orderRefund.getShopId());

        List<OrderRefundLog> dbLogList = orderRefundLogRepository.getByRefundIdAndSortByOperateDateDesc(refundId);
        if (CollUtil.isEmpty(dbLogList)) {
            result.setLogList(Collections.emptyList());
            return result;
        }
        List<Integer> passList = Arrays.asList(RefundStatusEnum.BUYER_APPLY.getCode(),
                RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode(), RefundStatusEnum.WAIT_BUYER_SEND.getCode(),
                RefundStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode(), RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode(),
                RefundStatusEnum.REFUND_SUCCESS.getCode(), RefundStatusEnum.BUYER_CANCEL.getCode());
        List<Integer> rejectList = Arrays.asList(RefundStatusEnum.SUPPLIER_REFUSE.getCode(),
                RefundStatusEnum.PLATFORM_REFUSE.getCode());
        List<Integer> reviewingList = Arrays.asList(RefundStatusEnum.REFUNDING.getCode());
        List<RefundLogBo> logList = dbLogList.stream().map(log -> {
            RefundLogBo refundLogBo = new RefundLogBo();
            refundLogBo.setRefundId(log.getRefundId());
            refundLogBo.setOperator(log.getOperator());
            refundLogBo.setOperateDate(log.getOperateDate());
            refundLogBo.setOperateContent(log.getOperateContent());
            refundLogBo.setStep(log.getStep());
            refundLogBo.setStepName(RefundStatusEnum.getStepDesc(log.getStep()));
            refundLogBo.setRemark(log.getRemark());
            if (RefundStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode().equals(log.getStep())) {
                refundLogBo.setExpressCompanyCode(orderRefund.getExpressCompanyCode());
                refundLogBo.setExpressCompanyName(orderRefund.getExpressCompanyName());
                refundLogBo.setShipOrderNumber(orderRefund.getShipOrderNumber());
            }
            if (passList.contains(log.getStep())) {
                refundLogBo.setStatus(RefundLogStatusEnum.PASS.getCode());
                refundLogBo.setStatusName(RefundLogStatusEnum.PASS.getDesc());
            }
            else if (rejectList.contains(log.getStep())) {
                refundLogBo.setStatus(RefundLogStatusEnum.REJECT.getCode());
                refundLogBo.setStatusName(RefundLogStatusEnum.REJECT.getDesc());
            }
            else if (reviewingList.contains(log.getStep())) {
                refundLogBo.setStatus(RefundLogStatusEnum.REVIEWING.getCode());
                refundLogBo.setStatusName(RefundLogStatusEnum.REVIEWING.getDesc());
            }
            return refundLogBo;
        }).collect(Collectors.toList());

        result.setLogList(logList);
        return result;
    }

    /**
     * 这里不需要考虑并发，首先认为一个账号只有一个人使用；然后只有待买家寄货才能发货
     *
     * @param paramBo void
     * <AUTHOR>
     */
    @Override
    public void userDeliver(UserDeliverParamBo paramBo) {
        OrderRefund orderRefund = orderRefundRepository.getById(paramBo.getRefundId());
        AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");
        AssertUtil.throwIfTrue(!orderRefund.getUserId().equals(paramBo.getUser().getUserId()), "错误的退款申请,退款记录不属于该用户");
        AssertUtil.throwIfTrue(orderRefund.getHasCancel(), "该售后已取消");
        AssertUtil.throwIfTrue(!RefundAuditStatusEnum.WAIT_BUYER_SEND.getCode().equals(orderRefund.getSellerAuditStatus()), "只有待买家寄货状态的能进行发货操作");

        // 校验订单支付时间是否超过支付撤销截止天数，这个是汇付支付渠道的配置，超过时间，不可进行支付撤销(未分帐前的退款实际是支付撤销)
        checkCanReverse(orderRefund.getOrderId());

        // 保存寄货信息
        orderRefundBizAssist.saveUserDeliverData(paramBo, orderRefund);
        // 调用基础服务，订阅物流信息，订阅后才能查询物流轨迹
        if (StrUtil.isBlank(paramBo.getShipOrderNumber())) {
            return;
        }
        // 订阅失败不影响主流程
        try {
            expressRemoteService.subscribe(paramBo.getExpressCompanyCode(), paramBo.getShipOrderNumber());
        }
        catch (Exception e) {
            log.warn("订阅物流信息失败, refundId: {}", paramBo.getRefundId(), e);
        }
        // 发送MQ消息，下游消费
        try {
            orderRefundMessagePublisher.sendOrderRefund(orderRefund.getOrderId(), orderRefund.getId(), RefundEventEnum.USER_DELIVER);
        }
        catch (Exception e) {
            log.warn("买家寄货发送MQ消息失败, refundId: {}", orderRefund.getId(), e);
        }
    }

    // 同意售后，退款状态流转为待买家寄货
    // 同意并弃货，退款状态流转为待平台确认
    @Override
    public void sellerApprove(SellerApproveParamBo paramBo) {
        // 这里考虑并发， 用户取消和供应商审核可能同时发生，与取消售后同一把锁
        String key = LockConst.LOCK_USER_REFUND_ORDER + paramBo.getRefundId();
        OrderRefund refund = null;
        try {
            refund = distributedLockService.tryLock(new LockKey(LockConst.SCENE_USER_REFUND_ORDER, key), () -> {
                OrderRefund orderRefund = orderRefundRepository.getByIdForceMaster(paramBo.getRefundId());
                AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");
                AssertUtil.throwIfTrue(!orderRefund.getShopId().equals(paramBo.getShop().getShopId()), "错误的退款申请,退款记录不属于该店铺");
                AssertUtil.throwIfTrue(orderRefund.getHasCancel(), "该售后已取消");
                // 供应商系统操作校验的是 待供应商审核和待供应商收货；定时任务执行时会从待买家寄货变成拒绝，所以状态校验有三个
                AssertUtil.throwIfTrue(!RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(orderRefund.getSellerAuditStatus()) &&
                        !RefundAuditStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode().equals(orderRefund.getSellerAuditStatus()) &&
                        !RefundAuditStatusEnum.WAIT_BUYER_SEND.getCode().equals(orderRefund.getSellerAuditStatus()), "只有待供应商审核状态的能进行审核操作");

                // 校验订单支付时间是否超过支付撤销截止天数，这个是汇付支付渠道的配置，超过时间，不可进行支付撤销(未分帐前的退款实际是支付撤销)
                // 允许拒绝
                if (!RefundAuditStatusEnum.SUPPLIER_REFUSE.equals(paramBo.getAuditStatus())) {
                    checkCanReverse(orderRefund.getOrderId());
                }

                // 整单退，才需要退运费，所以否则的话，设置为false
                if (!RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) && !orderRefund.getHasAllReturn()) {
                    paramBo.setWhetherRefundFreight(Boolean.FALSE);
                }
                // 退货退款，审核通过的时候，区分是否弃货，弃货的话，需要设置为待平台确认
                if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(orderRefund.getRefundMode()) &&
                        RefundAuditStatusEnum.SUPPLIER_PASS.equals(paramBo.getAuditStatus())) {
                    if (Boolean.TRUE.equals(paramBo.getWhetherAbandonGoods())) {
                        log.info("退货退款，审核通过，弃货，状态流转为待平台确认");
                        paramBo.setAuditStatus(RefundAuditStatusEnum.SUPPLIER_PASS);
                    }
                    else {
                        paramBo.setAuditStatus(RefundAuditStatusEnum.WAIT_BUYER_SEND);
                    }
                }
                // 保存供应商审核数据，包括退款和订单日志
                orderRefundBizAssist.saveSellerAuditData(paramBo, orderRefund);
                return orderRefund;
            });
        }
        catch (BusinessException e) {
            log.error("供应商审核退款失败, refundId: {}", paramBo.getRefundId(), e);
            throw e;
        }
        catch (Throwable e) {
            log.error("供应商审核退款失败, refundId: {}", paramBo.getRefundId(), e);
            throw new BusinessException("供应商审核失败");
        }
        // 发送MQ消息 TODO BCP处理一致性
        try {
            RefundEventEnum event = null;
            if (RefundAuditStatusEnum.SUPPLIER_REFUSE.equals(paramBo.getAuditStatus())) {
                event = RefundEventEnum.SELLER_REJECT;
            }
            else if (RefundAuditStatusEnum.SUPPLIER_PASS.equals(paramBo.getAuditStatus())) {
                event = RefundEventEnum.SELLER_PASS;
            }
            else if (RefundAuditStatusEnum.WAIT_BUYER_SEND.equals(paramBo.getAuditStatus())) {
                event = RefundEventEnum.WAIT_BUYER_SEND;
            }
            log.info("供应商审核事件: {}", event);
            if (event != null) {
                orderRefundMessagePublisher.sendOrderRefund(refund.getOrderId(), paramBo.getRefundId(), event);
            }
        }
        catch (Exception e) {
            log.warn("发送供应商审核消息失败，refundId={}", paramBo.getRefundId(), e);
        }
    }

    @Override
    public void sellerConfirmReceive(SellerConfirmReceiveParamBo paramBo) {
        UserBo user = paramBo.getUser();
        // 这里考虑并发， 用户取消和供应商审核可能同时发生，与取消售后同一把锁
        String key = LockConst.LOCK_USER_REFUND_ORDER + paramBo.getRefundId();
        OrderRefund refund = null;
        try {
            refund = distributedLockService.tryLock(new LockKey(LockConst.SCENE_USER_REFUND_ORDER, key), () -> {
                OrderRefund orderRefund = orderRefundRepository.getById(paramBo.getRefundId());
                AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");
                AssertUtil.throwIfTrue(!orderRefund.getShopId().equals(paramBo.getShop().getShopId()), "错误的退款申请,退款记录不属于该店铺");
                AssertUtil.throwIfTrue(orderRefund.getHasCancel(), "已取消状态的不能进行确认收货操作");
                AssertUtil.throwIfTrue(!RefundAuditStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode().equals(orderRefund.getSellerAuditStatus()), "只有待供应商收货状态的能进行确认收货操作");
                AssertUtil.throwIfTrue(RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()), "只有发货后可以进行确认收货操作");

                // 校验订单支付时间是否超过支付撤销截止天数，这个是汇付支付渠道的配置，超过时间，不可进行支付撤销(未分帐前的退款实际是支付撤销)
                checkCanReverse(orderRefund.getOrderId());

                Date now = new Date();
                OrderRefund updateRefund = new OrderRefund();
                // 确认收货状态设置为审核通过，下一流程就是待平台确认
                updateRefund.setSellerAuditStatus(RefundAuditStatusEnum.SUPPLIER_PASS.getCode());
                updateRefund.setStatus(RefundAuditStatusEnum.WAIT_PLATFORM_CONFIRM.getCode());
                updateRefund.setManagerConfirmStatus(RefundAuditStatusEnum.WAIT_PLATFORM_CONFIRM.getCode());
                updateRefund.setSellerConfirmArrivalDate(now);
                updateRefund.setManagerConfirmDate(now);
                updateRefund.setLastModifyTime(now);
                updateRefund.setUpdateTime(now);
                // 非整单退，且退货才能修改数量
                if (Boolean.FALSE.equals(orderRefund.getHasAllReturn()) &&
                        RefundModeEnum.RETURN_AND_REFUND.getCode().equals(orderRefund.getRefundMode())) {
                    // 能修改数量再校验
                    AssertUtil.throwIfTrue(paramBo.getConfirmQuantity() > orderRefund.getReturnQuantity(), "确认收货数量不能大于退货数量");
                    // 根据店铺确认的，重置数量，金额不需要重置，因为单品售后的金额是用户自己选的
                    updateRefund.setReturnQuantity(paramBo.getConfirmQuantity());
                    // 金额也要重置，如果确认收货的数量与申请数量不一致，则金额需要重置
                    // 需求描述：【修改退货数量，退款金额也要重新计算，退款金额=数量*实付单价；比如买家买10件，一起10元，买家申请退6件，退款金额6；供应商确认收货的时候把数量改成5，退款金额也改成5；如果买家申请退6件，退款金额4，供应商确认收货的时候把数量改成5，退款金额改成5】
                    if (!paramBo.getConfirmQuantity().equals(orderRefund.getReturnQuantity())) {
                        OrderItem orderItem = orderItemRepository.getById(orderRefund.getOrderItemId());
                        BigDecimal salePrice = NumberUtil.div(orderItem.getRealTotalPrice(), orderItem.getQuantity());
                        BigDecimal realAmount = NumberUtil.mul(salePrice, paramBo.getConfirmQuantity()).setScale(2, RoundingMode.HALF_UP);
                        updateRefund.setAmount(realAmount);
                    }
                    else {
                        updateRefund.setAmount(orderRefund.getAmount());
                    }
                }

                // 通过事务保存数据
                orderRefundBizAssist.saveConfirmReceiveData(updateRefund, orderRefund, user, orderRefund.getReturnQuantity(), paramBo.getConfirmQuantity());

                // 记录接口操作日志，这个目前的aop日志切面不支持
                try {
                    updateRefund.setId(orderRefund.getId());
                    List<ChangeFiled> orderChanged = operationLogAssist.buildChangeList(orderRefund, updateRefund, ChangeFieldDesc.REFUND_SELLER_RECEIVE_REFUND);
                    operationLogAssist.log(ExaminModelEnum.ORDER, ExaProEnum.MODIFY, "售后-供应商确认收货",
                            JsonUtil.toJsonString(orderChanged), paramBo, paramBo.getUser().getUserName());
                }
                catch (Exception e) {
                    log.error("【供应商确认收货】记录接口操作日志失败", e);
                }
                return orderRefund;
            });
        }
        catch (BusinessException be) {
            log.error("供应商审核退款业务异常, refundId: {}", paramBo.getRefundId(), be);
            throw be;
        }
        catch (Throwable e) {
            log.error("供应商审核退款失败, refundId: {}", paramBo.getRefundId(), e);
            throw new BusinessException("供应商审核失败");
        }
        // 发送MQ消息，调用支付接口退款，后续可能需要定时任务检查，TODO BCP处理一致性
        // 处理库存回退
        try {
            orderRefundMessagePublisher.sendOrderRefund(refund.getOrderId(), paramBo.getRefundId(), RefundEventEnum.SELLER_CONFIRM_RECEIVE);
        }
        catch (Exception e) {
            log.warn("发送供应商审核消息失败，refundId={}", paramBo.getRefundId(), e);
        }
    }

    @Override
    public void platformConfirm(PlatformApproveBo paramBo) {
        // 这里考虑并发， 用户取消和供应商审核可能同时发生，与取消售后同一把锁
        String key = LockConst.LOCK_USER_REFUND_ORDER + paramBo.getRefundId();
        OrderRefund refund = null;
        try {
            refund = distributedLockService.tryLock(new LockKey(LockConst.SCENE_USER_REFUND_ORDER, key), () -> {
                OrderRefund orderRefund = orderRefundRepository.getByIdForceMaster(paramBo.getRefundId());
                AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");
                AssertUtil.throwIfTrue(orderRefund.getHasCancel(), "该售后已取消");
                AssertUtil.throwIfTrue(!RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode().equals(orderRefund.getStatus()), "只有待平台确认的能进行平台确认操作");

                // 校验订单支付时间是否超过支付撤销截止天数，这个是汇付支付渠道的配置，超过时间，不可进行支付撤销(未分帐前的退款实际是支付撤销)
                checkCanReverse(orderRefund.getOrderId());

                // 如果不需要退款，则状态直接成功
                RefundAuditStatusEnum auditStatusEnum = RefundAuditStatusEnum.PLATFORM_PASS;
                if (orderRefund.getAmount() == null || orderRefund.getAmount().compareTo(BigDecimal.ZERO) == 0) {
                    auditStatusEnum = RefundAuditStatusEnum.REFUND_SUCCESS;
                }
                SaveRefundPlatformAuditBo auditPo = SaveRefundPlatformAuditBo.builder()
                        .orderId(orderRefund.getOrderId())
                        .refundId(paramBo.getRefundId())
                        .applyNumber(orderRefund.getApplyNumber())
                        .platformAuditStatus(auditStatusEnum)
                        .stepStatus(RefundStatusEnum.REFUND_SUCCESS)
                        .remark(paramBo.getRemark())
                        .userName(paramBo.getUser().getUserName())
                        .build();
                // 用于记录日志
                auditPo.setOperationUserId(paramBo.getOperationUserId());
                orderRefundBizAssist.savePlatformAuditPassData(orderRefund, auditPo);
                return orderRefund;
            });
        }
        catch (BusinessException be) {
            log.error("平台确认退款业务异常, refundId: {}", paramBo.getRefundId(), be);
            throw be;
        }
        catch (Throwable e) {
            log.error("平台确认退款失败, refundId: {}", paramBo.getRefundId(), e);
            throw new BusinessException("平台确认退款失败");
        }
        // 发送MQ消息，调用支付接口退款，后续可能需要定时任务检查，TODO BCP处理一致性
        orderRefundMessagePublisher.sendOrderRefund(refund.getOrderId(), paramBo.getRefundId(), RefundEventEnum.PLATFORM_CONFIRM);
    }

    @Override
    public void platformReject(PlatformApproveBo paramBo) {
        // 这里考虑并发， 用户取消和供应商审核可能同时发生，与取消售后同一把锁
        String key = LockConst.LOCK_USER_REFUND_ORDER + paramBo.getRefundId();
        OrderRefund refund = null;
        try {
            refund = distributedLockService.tryLock(new LockKey(LockConst.SCENE_USER_REFUND_ORDER, key), () -> {
                OrderRefund orderRefund = orderRefundRepository.getByIdForceMaster(paramBo.getRefundId());
                AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");
                AssertUtil.throwIfTrue(orderRefund.getHasCancel(), "该售后已取消");
                AssertUtil.throwIfTrue(!RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode().equals(orderRefund.getStatus()), "只有待平台确认的能进行平台驳回操作");

                // 如果是退货退款, 供应商已经通过, 平台不能驳回
                if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(orderRefund.getRefundMode())
                    && orderRefund.getReturnQuantity() > 0) {
                    throw new BusinessException("当前售后单供应商已确认收货, 无法驳回");
                }

                // 审核驳回，直接设置状态就行
                SaveRefundPlatformAuditBo auditPo = SaveRefundPlatformAuditBo.builder()
                        .orderId(orderRefund.getOrderId())
                        .refundId(paramBo.getRefundId())
                        .applyNumber(orderRefund.getApplyNumber())
                        .platformAuditStatus(RefundAuditStatusEnum.PLATFORM_REFUSE)
                        .stepStatus(RefundStatusEnum.PLATFORM_REFUSE)
                        .remark(paramBo.getRemark())
                        .userName(paramBo.getUser().getUserName())
                        .build();
                // 用于记录日志
                auditPo.setOperationUserId(paramBo.getOperationUserId());
                orderRefundBizAssist.savePlatformAuditRejectData(orderRefund, auditPo);
                return orderRefund;
            });
        }
        catch (BusinessException be) {
            log.error("平台驳回退款业务异常, refundId: {}", paramBo.getRefundId(), be);
            throw be;
        }
        catch (Throwable e) {
            log.error("平台驳回退款失败, refundId: {}", paramBo.getRefundId(), e);
            throw new BusinessException("平台驳回退款失败");
        }
        // 发送MQ消息，平台拒绝需要推送ERP，TODO BCP处理一致性
        try {
            orderRefundMessagePublisher.sendOrderRefund(refund.getOrderId(), paramBo.getRefundId(), RefundEventEnum.PLATFORM_REJECT);
        }
        catch (Exception e) {
            log.warn("发送平台拒绝消息失败，refundId={}", paramBo.getRefundId(), e);
        }
    }

    @Override
    public UserDeliveryBo getUserDelivery(Long refundId) {
        OrderRefund orderRefund = orderRefundRepository.getById(refundId);
        AssertUtil.throwIfNull(orderRefund, "错误的退款申请,退款记录不存在");
        UserDeliveryBo deliveryBo = new UserDeliveryBo();
        deliveryBo.setRefundId(refundId);
        deliveryBo.setOrderId(orderRefund.getOrderId());
        deliveryBo.setExpressCompanyName(orderRefund.getExpressCompanyName());
        deliveryBo.setShipOrderNumber(orderRefund.getShipOrderNumber());
        deliveryBo.setExpressCompanyCode(orderRefund.getExpressCompanyCode());
        deliveryBo.setRefundQuantity(orderRefund.getReturnQuantity());
        return deliveryBo;
    }


    private UserBo queryUser(Long userId) {
        // 查询用户信息 获取真实姓名

        UserBo userBo = new UserBo();
        userBo.setUserId(userId);
        userBo.setUserName("ERP");
        try {
            QueryMemberReq req = new QueryMemberReq();
            req.setId(userId);
            MemberResp memberResp = ThriftResponseHelper.executeThriftCall(() -> shopMemberQueryFeign.queryMember(req));
            if (memberResp != null) {
                userBo.setUserName(memberResp.getUserName());
                userBo.setUserPhone(memberResp.getCellPhone());
            }
        }
        catch (Exception e) {
            log.error("[REFUND]查询用户信息失败, userId: {}", userId, e);
        }
        return userBo;
    }

    private List<OrderRefund> queryInRefundItem(String orderId) {
        Set<Integer> inStatusList = Stream.of(RefundStatusEnum.BUYER_APPLY, RefundStatusEnum.WAIT_SUPPLIER_AUDIT,
                RefundStatusEnum.WAIT_BUYER_SEND,
                RefundStatusEnum.WAIT_SUPPLIER_RECEIVE, RefundStatusEnum.WAIT_PLATFORM_CONFIRM,
                RefundStatusEnum.WAIT_PLATFORM_CONFIRM, RefundStatusEnum.REFUND_SUCCESS,
                RefundStatusEnum.REFUNDING).map(RefundStatusEnum::getCode).collect(Collectors.toSet());
        // 查询订单item  排除 删除 取消的
        return Optional.ofNullable(orderRefundRepository.getByOrderIdAndStatus(orderId,
                        inStatusList))
                .orElse(Collections.emptyList());
    }

    private void checkSourceRefundId(String sourceOrderId) {
        if (StrUtil.isBlank(sourceOrderId)) {
            return;
        }
        //查询
        List<OrderRefund> orderRefunds = Optional.ofNullable(orderRefundRepository.getBySourceRefundId(sourceOrderId))
                .orElse(Collections.emptyList());
        AssertUtil.throwIfTrue(!orderRefunds.isEmpty(), "该订单" + sourceOrderId + "已存在退款记录");
    }

    /**
     * 整单退
     *
     * @param order    订单
     * @param createBo 入参
     */
    private RefundDetailBo handleOrderRefund(Order order, CreateOrderRefundBo createBo) {
        // 校验 当前状态与申请状态
        RefundModeEnum refundMode = createBo.getRefundMode();
        RefundTypeEnum refundType = createBo.getRefundType();
        List<OrderRefund> orderRefunds = queryInRefundItem(order.getOrderId());
        AssertUtil.throwIfTrue(!orderRefunds.isEmpty(), "订单已存在退款记录,无法申请整单退");
        //查询用户信息 获取真实姓名
        UserBo userBo = queryUser(order.getUserId());
        // 添加分布式锁，防止订单退款时的并发问题
        try {
            // 订单整单退款，退款明细也不能存在，所以锁的key为订单id
            String key = LockConst.LOCK_USER_REFUND_ORDER + createBo.getOrderId();
            // 整单退需要指定退货运费
            createBo.setRefundFreight(order.getFreight());
            OrderRefund orderRefund =
                    distributedLockService.tryLock(new LockKey(LockConst.SCENE_USER_REFUND_ORDER, key), () -> {
                        // sourceOrderId 唯一
                        checkSourceRefundId(createBo.getSourceRefundId());
                        List<OrderRefund> refundList = this.getValidByOrderId(createBo.getOrderId());
                        AssertUtil.throwIfTrue(CollUtil.isNotEmpty(refundList), "错误的退款申请,订单已存在退款记录");
                        return orderRefundBizAssist.saveOrderRefund(createBo, order, userBo);
                    });
            // 发送MQ消息，下游消费
            orderRefundMessagePublisher.sendOrderRefund(order.getOrderId(), orderRefund.getId(),
                    RefundEventEnum.APPLY);
            RefundDetailBo refundDetail = new RefundDetailBo();
            refundDetail.setRefundId(orderRefund.getId());
            refundDetail.setOrderId(createBo.getOrderId());
            refundDetail.setRefundAmount(orderRefund.getAmount());
            refundDetail.setStatus(orderRefund.getStatus());
            refundDetail.setApplyDate(orderRefund.getApplyDate());
            refundDetail.setFreightAmount(orderRefund.getReturnFreight());
            return refundDetail;
        }
        catch (BusinessException be) {
            log.error("申请退款业务异常, orderId: {}", createBo.getOrderId(), be);
            throw be;
        }
        catch (Throwable e) {
            log.error("申请退款失败, orderId: {}", createBo.getOrderId(), e);
            throw new BusinessException("申请退款失败，请稍后重试");
        }
    }

    private void checkProductRefundAvaiable(CreateOrderRefundBo createBo, Order order) {
        List<CreateOrderRefundItemBo> refundItems = createBo.getRefundItems();
        AssertUtil.throwIfTrue(CollectionUtil.isEmpty(refundItems), "商品退款或者退货退款必须有商品");
        List<OrderRefund> alreadyRefundList = queryInRefundItem(order.getOrderId())
                .stream()
                .filter(orderRefund -> {
                    //有整单退的记录  则抛出异常
                    if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) || orderRefund.getHasAllReturn()) {
                        AssertUtil.throwIfTrue(orderRefund.getHasAllReturn(), "订单已存在整单退的记录，无法进行商品售后");
                    }
                    return true;
                }).collect(Collectors.toList());
        // 在途或者中间状态的 售后单 覆盖 单品最新状态
        // 新需求，一个单品可以发起多次售后
        Map<Long, List<OrderRefund>> alreadyRefundMap = alreadyRefundList.stream()
                .collect(Collectors.groupingBy(OrderRefund::getOrderItemId));

//        Map<Long, OrderRefund> inRefundMap = alreadyRefundList.stream()
//                .collect(Collectors.toMap(OrderRefund::getOrderItemId, Function.identity(), (o1, o2) -> {
//                    o2.setAmount(o2.getAmount().add(o1.getAmount()));
//                    o2.setReturnFreight(o2.getReturnFreight().add(o1.getReturnFreight()));
//                    return o2;
//                }));
        //校验item 存在 数量等
        // 查询订单item 对应的可用数量 可退金额
        List<RefundAvaiableBo> refundAvaiableBoList = Optional.ofNullable(orderItemRepository.getByOrderId(order.getOrderId()))
                .orElse(Collections.emptyList())
                .stream()
                .map(orderItem -> {
                    RefundAvaiableBo refundAvaiableBo = new RefundAvaiableBo();
                    refundAvaiableBo.setSkuAutoId(orderItem.getSkuAutoId());
                    refundAvaiableBo.setSkuCode(orderItem.getSku());
                    refundAvaiableBo.setOrderItemId(orderItem.getId());
                    refundAvaiableBo.setProductId(orderItem.getProductId());
                    refundAvaiableBo.setSkuId(orderItem.getSkuId());
//                    OrderRefund orderRefund = inRefundMap.get(orderItem.getId());


                    List<OrderRefund> itemValidRefundList = alreadyRefundMap.get(orderItem.getId());

                    OrderRefund orderRefund = CollectionUtil.isEmpty(itemValidRefundList) ? null :
                            itemValidRefundList.get(itemValidRefundList.size() - 1);
                    refundAvaiableBo.setStatus(orderRefund != null ? RefundStatusEnum.valueOf(orderRefund.getStatus()) : null);

                    ItemRemainRefundInfoBo remain = orderRefundBizAssist.getItemRemainRefund(orderItem, itemValidRefundList);
                    refundAvaiableBo.setCanRefundAmount(remain.getRemainAmount());
                    refundAvaiableBo.setCanRefundQuantity(remain.getRemainQuantity());
                    refundAvaiableBo.setTotalQuantity(orderItem.getQuantity());
                    //商品分摊后的 总金额
                    refundAvaiableBo.setRealTotalAmount(orderItem.getRealTotalPrice());
                    return refundAvaiableBo;
                })
                .collect(Collectors.toList());

        AssertUtil.throwIfTrue(refundAvaiableBoList.isEmpty(), "订单商品不存在");
        Map<Long, RefundAvaiableBo> skuAutoIdAvailableMap =
                refundAvaiableBoList.stream().collect(Collectors.toMap(RefundAvaiableBo::getSkuAutoId,
                        Function.identity(), (o1, o2) -> o2));
        Map<String, RefundAvaiableBo> skuCodeAvailableMap =
                refundAvaiableBoList.stream().collect(Collectors.toMap(RefundAvaiableBo::getSkuCode,
                        Function.identity(), (o1, o2) -> o2));
        List<CreateOrderRefundItemBo> items = refundItems.stream()
                .peek(item -> {
                    RefundAvaiableBo refundAvaiableBo = item.getSkuAutoId() != null ?
                            skuAutoIdAvailableMap.get(item.getSkuAutoId()) :
                            skuCodeAvailableMap.get(item.getSkuCode());
                    Long quantity = item.getQuantity();
                    BigDecimal refundAmount = item.getRefundAmount();
                    AssertUtil.throwIfNull(refundAvaiableBo, "商品不存在");
                    AssertUtil.throwIfTrue(refundAvaiableBo.getCanRefundAmount().compareTo(refundAmount) < 0, "商品可退金额不足");
                    AssertUtil.throwIfTrue(quantity > refundAvaiableBo.getCanRefundQuantity(), "商品可退数量不足");
                    // 数量没有  金额有   可退金额 >= 申请金额
                    // 数量+金额都有，则校验  min(可退金额，（分摊价格/总数量）*退款数量） >=申请金额 否则报错 退款金额= 申请金额
                    // 数量有 金额没有  退款金额 = min(可退金额,（分摊价格/总数量）*退款数量)
                    BigDecimal calcMinRefund = refundAvaiableBo.getCanRefundAmount().min(
                            refundAvaiableBo.getRealTotalAmount()
                                    .multiply(BigDecimal.valueOf(quantity))
                                    .divide(BigDecimal.valueOf(refundAvaiableBo.getTotalQuantity()), 2, RoundingMode.HALF_UP)
                    );
                    if (refundAmount.compareTo(BigDecimal.ZERO) > 0) {
                        AssertUtil.throwIfTrue(calcMinRefund.compareTo(refundAmount) < 0, "商品可退金额不足");
                    }
                    else {
                        item.setRefundAmount(calcMinRefund);
                    }
                    // 组装需要批量插入的
                    item.setProductId(refundAvaiableBo.getProductId());
                    item.setOrderItemId(refundAvaiableBo.getOrderItemId());
                    item.setSkuId(refundAvaiableBo.getSkuId());
                    item.setSkuAutoId(refundAvaiableBo.getSkuAutoId());
                    item.setSkuCode(refundAvaiableBo.getSkuCode());
                }).collect(Collectors.toList());
        skuCodeAvailableMap.clear();
        skuAutoIdAvailableMap.clear();
        alreadyRefundMap.clear();
        createBo.setRefundItems(items);
    }

    private RefundDetailBo handleProductRefund(Order order, CreateOrderRefundBo createBo) {
        // 发货后 申请售后   商品退款 或者 商品退货退款
        RefundModeEnum refundMode = createBo.getRefundMode();
        if (!(RefundModeEnum.GOODS_REFUND.equals(refundMode)
                || RefundModeEnum.RETURN_AND_REFUND.equals(refundMode))) {
            throw new BusinessException("该订单状态下只能申请商品退款");
        }
        //查询用户信息 获取真实姓名
        UserBo userBo = queryUser(order.getUserId());
        // 添加分布式锁，防止订单退款时的并发问题
        try {
            // 订单商品退款，因为需要校验实时可用数量，可退金额，锁整个订单 防止并发问题
            String key = LockConst.LOCK_USER_REFUND_ORDER + createBo.getOrderId();
            OrderRefund orderRefund =
                    distributedLockService.tryLock(new LockKey(LockConst.SCENE_USER_REFUND_ORDER, key), () -> {
                        checkSourceRefundId(createBo.getSourceRefundId());
                        checkProductRefundAvaiable(createBo, order);
                        // 保存退款数据
                        return orderRefundBizAssist.saveProductRefund(createBo, order, userBo);
                    });
            // 发送MQ消息，下游消费
            orderRefundMessagePublisher.sendOrderRefund(createBo.getOrderId(), orderRefund.getId(),
                    RefundEventEnum.APPLY);
            //状态不是初始，则再次推
            RefundDetailBo refundDetail = new RefundDetailBo();
            refundDetail.setRefundId(orderRefund.getId());
            refundDetail.setOrderId(createBo.getOrderId());
            refundDetail.setRefundAmount(orderRefund.getAmount());
            refundDetail.setStatus(orderRefund.getStatus());
            refundDetail.setApplyDate(orderRefund.getApplyDate());
            refundDetail.setFreightAmount(orderRefund.getReturnFreight());
            return refundDetail;
        }
        catch (BusinessException be) {
            log.error("申请明细退款业务异常, orderId: {}", createBo.getOrderId(), be);
            throw be;
        }
        catch (Throwable e) {
            log.error("申请明细退款失败, orderId: {}", createBo.getOrderId(), e);
            throw new BusinessException("申请退款失败，请稍后重试");
        }

    }

    /**
     * 平台批量确认退款，批量发起允许部分成功
     * 整体调用单个审核的接口，多线程处理
     *
     * @param batchReq 批量入参
     * @return 审核通过结果，提示成功和失败条数
     */
    @Override
    public PlatformApproveBatchResp platformBatchConfirm(PlatformApproveBatchReq batchReq) {
        String remark = batchReq.getRemark();
        // 入参也校验了user非空
        UserBo user = JsonUtil.copy(batchReq.getUser(), UserBo.class);
        // 入参校验了refundIdList非空
        List<PlatformApproveBo> batchParamList = batchReq.getRefundIdList().stream()
                // 防止数组里面元素为null
                .filter(Objects::nonNull)
                .map(refundId -> {
                    PlatformApproveBo paramBo = new PlatformApproveBo();
                    paramBo.setRefundId(refundId);
                    paramBo.setRemark(remark);
                    paramBo.setUser(user);
                    return paramBo;
                }).collect(Collectors.toList());
        ThreadPoolExecutor executor = ThreadPoolUtil.ASYNC_API_POOL;
        log.info("【售后】平台批量审核通过, 最后需要批量操作的数据为:{}", JsonUtil.toJsonString(batchParamList));
        // 使用 completableFuture 发起多线程调用，每个售后单单独发起
        List<CompletableFuture<PlatformApproveResp>> futureList = batchParamList.stream()
                .map(paramBo -> CompletableFuture.supplyAsync(() -> {
                    // 如果没有异常，则认为处理成功
                    try {
                        platformConfirm(paramBo);
                        return null;
                    }
                    catch (Exception e) {
                        log.error("【售后】平台批量审核通过, 异常:{}", e.getMessage());
                        String errMsg = null;
                        if (e instanceof BusinessException) {
                            errMsg = e.getMessage();
                        }
                        else {
                            errMsg = "系统异常";
                        }
                        PlatformApproveResp resp = new PlatformApproveResp();
                        resp.setRefundId(paramBo.getRefundId());
                        resp.setErrorDesc(errMsg);
                        return resp;
                    }
                }, executor))
                .collect(Collectors.toList());
        // 使用CompletableFuture.allOf来等待所有任务完成
        CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0]));
        // 等待所有任务完成
        allFutures.join();
        int successCount = 0;
        int failCount = 0;
        // 检查每个CompletableFuture的结果
        List<PlatformApproveResp> errorList = new ArrayList<>(batchParamList.size());
        Set<String> errDescSet = new HashSet<>();
        for (CompletableFuture<PlatformApproveResp> future : futureList) {
            try {
                PlatformApproveResp result = future.get();
                // 约定，对象为null代表处理成功的
                if (result == null) {
                    successCount++;
                }
                else {
                    failCount++;
                    errorList.add(result);
                    errDescSet.add(result.getErrorDesc());
                }
            }
            catch (Exception e) {
                log.error("【售后】平台批量审核通过, 异常:{}", e.getMessage());
            }
        }

        PlatformApproveBatchResp resp = new PlatformApproveBatchResp();
        resp.setTotalCount(batchParamList.size());
        resp.setSuccessCount(successCount);
        resp.setFailCount(failCount);
        resp.setErrorDesc(String.join(",", errDescSet));
        resp.setErrorList(errorList);
        return resp;
    }

    @Override
    public CreateOrderRefundResp createOrderRefund(CreateOrderRefundBo createBo) {
        // 1. 查询订单
        Order order = orderRepository.getByOrderId(createBo.getOrderId());
        AssertUtil.throwIfNull(order, "订单不存在");
        AssertUtil.throwIfTrue(createBo.getRefundFreight() != null && createBo.getRefundFreight().compareTo(order.getFreight()) > 0, "退款运费不能大于订单运费");

        //校验sourceRefundId 唯一
        checkSourceRefundId(createBo.getSourceRefundId());


        //转换成  applyOrderRefund,applyWholeOrderRefund,applyOrderItemRefund
        RefundModeEnum refundMode = createBo.getRefundMode();
        RefundTypeEnum refundType = createBo.getRefundType();
        ApplyRefundBo bo = new ApplyRefundBo();
        List<String> pics = createBo.getPics();
        if (CollectionUtil.isNotEmpty(pics)) {
            bo.setCertPic1(pics.get(0));
            if (pics.size() > 1) {
                bo.setCertPic2(pics.get(1));
            }
            if (pics.size() > 2) {
                bo.setCertPic3(pics.get(2));
            }
        }


        UserBo userBo = queryUser(order.getUserId());
        bo.setOrderId(createBo.getOrderId());
        bo.setRefundMode(refundMode);
        bo.setRefundType(refundType);
        bo.setRefundReasonDesc(createBo.getRefundReason());
        bo.setStatus(createBo.getStatus());
        bo.setUser(userBo);
        bo.setReturnFreightAmount(createBo.getRefundFreight());
        bo.setRefundPayType(RefundPayTypeEnum.ORIGINAL);
        bo.setRefundAmount(createBo.getRefundAmount());
        bo.setSourceRefundId(createBo.getSourceRefundId());

        Long refundId = null;
        if (RefundModeEnum.ORDER_REFUND.equals(refundMode)) {
            refundId = applyOrderRefund(bo);
        }
        else if (RefundModeEnum.RETURN_AND_REFUND.equals(refundMode)) {
            refundId = applyWholeOrderRefund(bo);
        }
        else {
            checkProductRefundAvaiable(createBo, order);
            bo.setShipOrderNumber(createBo.getShipOrderNumber());
            bo.setExpressCompanyCode(createBo.getExpressCompanyCode());
            bo.setExpressCompanyName(createBo.getExpressCompanyName());
            //orderItemId 设置
            Optional.ofNullable(createBo.getRefundItems()).orElse(Collections.emptyList())
                    .stream().findFirst().ifPresent(createOrderRefundItemBo -> {
                        bo.setOrderItemId(createOrderRefundItemBo.getOrderItemId());
                        bo.setApplyQuantity(createOrderRefundItemBo.getQuantity());
                        //退货数量 申请数量
                        bo.setRefundQuantity(createOrderRefundItemBo.getQuantity());
                        bo.setRefundAmount(createOrderRefundItemBo.getRefundAmount());
                    });
            refundId = applyOrderItemRefund(bo, order);
        }
        CreateOrderRefundResp resp = new CreateOrderRefundResp();
        resp.setRefundId(refundId);
        resp.setSourceOrderId(order.getSourceOrderId());
        resp.setOrderId(order.getOrderId());
        resp.setRefundAmount(bo.getRefundAmount());
        return resp;
    }

    /**
     * 获取订单的有效退款记录，所谓有效，是指商家未取消、供应商未拒绝的记录，
     * <p>没通过sql实现，枚举定义在api，repository层不想写死，所以在业务层处理。一个订单的退款记录一般来说不多</p>
     *
     * @param orderId 订单id
     * <AUTHOR>
     */
    @Override
    public List<OrderRefund> getValidByOrderId(String orderId) {
        List<OrderRefund> refundList = orderRefundRepository.getByOrderId(orderId);
        return Optional.ofNullable(refundList).map(list ->
                        list.stream()
                                // 未取消
                                .filter(refund -> RefundStatusHelper.refundValid(refund.getHasCancel(), refund.getSellerAuditStatus(), refund.getManagerConfirmStatus()))
                                .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }

    @Override
    public List<OrderRefund> getRefundingByOrderId(String orderId) {
        List<OrderRefund> refundList = orderRefundRepository.getByOrderId(orderId);
        return Optional.ofNullable(refundList).map(list ->
                        list.stream()
                                // 过滤出没有被取消的
                                .filter(refund -> Boolean.FALSE.equals(refund.getHasCancel()))
                                .filter(refund -> {
                                    if (RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(refund.getSellerAuditStatus())) {
                                        return true;
                                    }
                                    // 平台的状态初始就设置了，所以需要综合供应商审核通过
                                    boolean sellerApprove = RefundAuditStatusEnum.SUPPLIER_PASS.getCode().equals(refund.getSellerAuditStatus());
                                    boolean platformConfirm = RefundAuditStatusEnum.WAIT_PLATFORM_CONFIRM.getCode().equals(refund.getManagerConfirmStatus());
                                    return sellerApprove && platformConfirm;
                                })
                                .collect(Collectors.toList()))
                .orElse(Collections.emptyList());
    }


    //*****************************************************************


    private void checkOrderCanRefund(UserBo userBo, Order order) {
        orderBizAssist.validateOrder(userBo.getUserId(), order);
        // 校验是否已过售后期
        checkOrderReturnTimeout(order);
    }

    /**
     * 校验是否已过售后维权期，基于订单完成时间
     *
     * @param order 订单
     */
    private void checkOrderReturnTimeout(Order order) {
        // 获取交易设置中的订单售后时间
        TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
        if (setting == null || StrUtil.isBlank(setting.getSalesReturnTimeout())) {
            return;
        }
        // 校验是否已过售后维权期
        int days = Integer.parseInt(setting.getSalesReturnTimeout());
        if (OrderStatusEnum.FINISHED.getCode().equals(order.getOrderStatus())) {
            Date finishDate = order.getFinishDate();
            if (DateUtil.offsetDay(finishDate, days).isBefore(new Date())) {
                throw new BusinessException("订单售后通道已关闭");
            }
        }
        // 校验是否已过支付撤销天数
        int paidReverseTimeout = SettingUtil.getIntValueOrDefault("支付后可撤销超时天数", payProps.getPaidReverseTimeoutDays(), CommonConst.DEFAULT_PAID_REVERSE_TIMEOUT_DAYS);
        Date paidReverseEndDate = DateUtil.offsetDay(order.getPayDate(), paidReverseTimeout);
        // 如果当前时间大于支付可以撤销的截止时间，则报错提示用户审核拒绝
        if (DateUtil.compare(new Date(), paidReverseEndDate) > 0) {
            throw new BusinessException(String.format("支付后%s天不可退款", paidReverseTimeout));
        }
    }

    private void checkOrderItemCanRefund(Order order, OrderItem orderItem) {
        if (orderItem == null) {
            throw new BusinessException("错误的退款申请,订单明细不存在");
        }
        String orderId = order.getOrderId();
        if (!orderItem.getOrderId().equals(orderId)) {
            throw new BusinessException("错误的退款申请,订单明细不属于该订单");
        }
    }


    /**
     * 校验是否有订单维度的售后，
     *
     * <AUTHOR>
     */
    private void checkRefundForOrderExists(List<OrderRefund> refundList) {
        if (CollUtil.isEmpty(refundList)) {
            return;
        }
        // 发起明细退款时，如果已经存在整单退，或者当前明细已经发起，则不允许申请
        boolean anyOrderRefund = refundList.stream()
                // 整单退才会设置为true，这个情况一般是待收货/已完成下的整单退
                .anyMatch(refund -> refund.getHasAllReturn() ||
                        // 待收货下的订单退款，也是整单退
                        RefundModeEnum.ORDER_REFUND.getCode().equals(refund.getRefundMode()));
        if (anyOrderRefund) {
            throw new BusinessException("错误的退款申请,已存在整单退款记录");
        }
    }


    /**
     * 校验是否能重新发起申请。重新申请大体与申请是一样的，但接口是同一个，所以校验时需要区分一下
     *
     * <AUTHOR>
     */
    private void checkCanReapplyAndResetData(ReapplyRefundBo applyBo, OrderRefund orderRefund, Order order, UserBo user) {
        // 校验
        checkOrderCanRefund(user, order);
        boolean canceled = orderRefund.getHasCancel();
        boolean rejected = RefundStatusHelper.refundRejected(orderRefund.getSellerAuditStatus(), orderRefund.getManagerConfirmStatus());
        // 既没有被取消，也没有被供应商拒绝
        if (!canceled && !rejected) {
            throw new BusinessException("错误的退款申请,退款记录不可重新申请");
        }
        // 先获取当前有效的售后记录
        List<OrderRefund> validRefundList = this.getValidByOrderId(order.getOrderId());
        // 如果是订单退款，只有待收货才能申请
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode())) {
            if (!OrderStatusEnum.UNDER_SEND.getCode().equals(order.getOrderStatus())) {
                throw new BusinessException("错误的退款申请,订单状态不可重新申请");
            }
            // 整单退，重新申请需要校验当前是否有有效的售后
            if (CollUtil.isNotEmpty(validRefundList)) {
                throw new BusinessException("错误的退款申请,已存在退款记录");
            }
        }
        else if (Boolean.TRUE.equals(orderRefund.getHasAllReturn())) {
            // 如果是整单退款
            if (!OrderStatusEnum.UNDER_RECEIVE.getCode().equals(order.getOrderStatus()) &&
                    !OrderStatusEnum.FINISHED.getCode().equals(order.getOrderStatus())) {
                throw new BusinessException("错误的退款申请,订单状态不可重新申请");
            }
            // 整单退，重新申请需要校验当前是否有有效的售后
            if (CollUtil.isNotEmpty(validRefundList)) {
                throw new BusinessException("错误的退款申请,已存在退款记录");
            }
        }
        else {
            // 其余情况就是明细退款了
            if (OrderStatusEnum.UNDER_SEND.getCode().equals(order.getOrderStatus())) {
                throw new BusinessException("错误的退款申请,订单状态不可重新申请");
            }
            OrderItem orderItem = orderItemRepository.getById(orderRefund.getOrderItemId());
            ItemRemainRefundInfoBo remainInfo = orderRefundBizAssist.getItemRemainRefund(orderItem, validRefundList);
            // 这里要校验剩余的数量和金额 与 发起重新申请的记录的数量和金额
            if (remainInfo.getRemainQuantity() < orderRefund.getReturnQuantity() ||
                    remainInfo.getRemainAmount().compareTo(orderRefund.getAmount()) < 0) {
                throw new BusinessException("错误的退款申请,该商品已全部退款");
            }
        }
    }

    /**
     * 当前售后是否可以取消
     *
     * @param refund boolean
     * <AUTHOR>
     */
    private boolean canCancel(OrderRefund refund) {
        boolean onlyRefund = RefundModeEnum.onlyRefund(refund.getRefundMode());
        boolean underAudit = RefundAuditStatusEnum.underAudit(refund.getSellerAuditStatus(), refund.getManagerConfirmStatus());
        // 如果是仅退款，包括订单退款，则待供应商审核和待平台审核才能拒绝
        if (onlyRefund && underAudit) {
            return true;
        }
        // 不是仅退款，目前就是退货退款，待供应商审核和待买家寄货可以取消
        return !onlyRefund && (RefundAuditStatusEnum.WAIT_BUYER_SEND.getCode().equals(refund.getSellerAuditStatus()) ||
                RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(refund.getSellerAuditStatus()));
    }

    /**
     * 构建公共的退款详情
     *
     * @param orderRefund 退款记录
     * @param detailBo    需要构建的明细对象，商家、供应商、平台的对象有些不一致
     * <AUTHOR>
     */
    private void buildCommonRefundDetail(OrderRefund orderRefund, RefundDetailBo detailBo) {
        convertBaseCommonRefundDetail(orderRefund, detailBo);
        // 如果时整单退，包括了订单退款，则从订单获取并设置数据，否则从明细获取并设置
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) || orderRefund.getHasAllReturn()) {
            // 查询并设置订单支付金额
            Order order = orderRepository.getByOrderId(orderRefund.getOrderId());
            detailBo.setPayAmount(order.getTotalAmount());
            detailBo.setFreightAmount(order.getFreight());
            List<OrderItem> orderItemList = orderItemRepository.getByOrderIdList(Collections.singletonList(orderRefund.getOrderId()));
            Long totalQuantity = orderItemList.stream().map(OrderItem::getQuantity).reduce(0L, Long::sum);
            detailBo.setOrderQuantity(totalQuantity);

            //detailBo.setRefundQuantity(orderRefund.getApplyQuantity());
        }
        else {
            OrderItem orderItem = orderItemRepository.getById(orderRefund.getOrderItemId());
            detailBo.setPayAmount(orderItem.getRealTotalPrice());
            detailBo.setFreightAmount(BigDecimal.ZERO);
            detailBo.setOrderQuantity(orderItem.getQuantity());

            // 如果是仅退款，返回前端的退货数量，用申请数量重置，为了支持单品多次售后，新增了申请数量字段，仅退款也需要设置数量
            /*if (RefundModeEnum.GOODS_REFUND.getCode().equals(orderRefund.getRefundMode())) {
                detailBo.setRefundQuantity(orderRefund.getApplyQuantity());
            }
            // 如果是退货退款，因为供应商也可以选择弃货，所以也用申请数量重置
            else if (RefundModeEnum.RETURN_AND_REFUND.getCode().equals(orderRefund.getRefundMode()) && orderRefund.getReturnQuantity() == 0) {
                detailBo.setRefundQuantity(orderRefund.getApplyQuantity());
            }*/
        }
        detailBo.setStatus(orderRefund.getStatus());
        detailBo.setStatusDesc(RefundStatusEnum.getDesc(orderRefund.getStatus()));
    }

    private void convertBaseCommonRefundDetail(OrderRefund orderRefund, RefundDetailBo detailBo) {
        detailBo.setShopId(orderRefund.getShopId());
        detailBo.setRefundId(orderRefund.getId());
        detailBo.setOrderId(orderRefund.getOrderId());
        detailBo.setOrderItemId(orderRefund.getOrderItemId());

        RefundTypeEnum refundType = orderRefundBizAssist.getRefundType(orderRefund);
        detailBo.setRefundType(refundType.getCode());
        detailBo.setRefundTypeDesc(refundType.getDesc());
        detailBo.setRefundMode(orderRefund.getRefundMode());
        detailBo.setRefundModeDesc(RefundModeEnum.getDesc(orderRefund.getRefundMode()));
        detailBo.setRefundAmount(orderRefund.getAmount());
        detailBo.setApplyQuantity(orderRefund.getApplyQuantity());
        // 退款数量，如果是仅退款或者弃货的，会用申请数量重置
        detailBo.setRefundQuantity(orderRefund.getReturnQuantity());
        detailBo.setReturnQuantity(orderRefund.getReturnQuantity());
        detailBo.setRefundReasonDesc(orderRefund.getReason());
        detailBo.setRefundRemark(orderRefund.getReasonDetail());
        detailBo.setContactUserName(orderRefund.getContactPerson());
        detailBo.setContactUserPhone(orderRefund.getContactCellPhone());
        detailBo.setRefundPayType(orderRefund.getRefundPayType());
        detailBo.setRefundPayTypeDesc(RefundPayTypeEnum.getDesc(orderRefund.getRefundPayType()));
        detailBo.setCertPic1(orderRefund.getCertPic1());
        detailBo.setCertPic2(orderRefund.getCertPic2());
        detailBo.setCertPic3(orderRefund.getCertPic3());
        detailBo.setHasAllReturn(orderRefund.getHasAllReturn());
        detailBo.setHasCancel(orderRefund.getHasCancel());
        detailBo.setSellerAuditStatus(orderRefund.getSellerAuditStatus());
        detailBo.setSellerAuditStatusDesc(RefundAuditStatusEnum.getDesc(orderRefund.getSellerAuditStatus()));
        detailBo.setManagerConfirmStatus(orderRefund.getManagerConfirmStatus());
        detailBo.setManagerConfirmStatusDesc(RefundAuditStatusEnum.getDesc(orderRefund.getManagerConfirmStatus()));
        detailBo.setSellerRemark(orderRefund.getSellerRemark());
        detailBo.setPlatformRemark(orderRefund.getManagerRemark());
        detailBo.setHasReturn(orderRefund.getHasReturn());
        detailBo.setApplyDate(orderRefund.getApplyDate());
        detailBo.setLastModifyTime(orderRefund.getLastModifyTime());
        detailBo.setApplicant(orderRefund.getApplicant());
        detailBo.setExpressCompanyCode(orderRefund.getExpressCompanyCode());
        detailBo.setExpressCompanyName(orderRefund.getExpressCompanyName());
        detailBo.setShipOrderNumber(orderRefund.getShipOrderNumber());
        detailBo.setCreateTime(orderRefund.getCreateTime());
        detailBo.setUpdateTime(orderRefund.getUpdateTime());
        if (orderRefund.getManagerConfirmStatus() > 6) {
            detailBo.setAuditStatus(orderRefund.getManagerConfirmStatus());
        }
        else if (orderRefund.getSellerAuditStatus() < 5) {
            detailBo.setAuditStatus(orderRefund.getSellerAuditStatus());
        }
        else {
            detailBo.setAuditStatus(5);
        }
        detailBo.setStatus(orderRefund.getStatus());
        detailBo.setStatusDesc(RefundStatusEnum.getDesc(orderRefund.getStatus()));
        detailBo.setUserId(orderRefund.getUserId());
        detailBo.setSourceRefundId(orderRefund.getSourceRefundId());
    }

    private RefundItemBo convertRefundItem(OrderItem orderItem) {
        RefundItemBo refundItemBo = new RefundItemBo();
        refundItemBo.setProductId(orderItem.getProductId());
        refundItemBo.setSkuId(orderItem.getSkuId());
        refundItemBo.setProductName(orderItem.getProductName());
        refundItemBo.setMainImagePath(orderItem.getThumbnailsUrl());

        String skuDesc = SkuUtil.assembleSkuName(orderItem.getColor(), orderItem.getSize(), orderItem.getVersion());
        refundItemBo.setSkuDesc(skuDesc);
        return refundItemBo;
    }

    /**
     * 进一步填充商家的售后详情信息
     *
     * @param detail 商家售后详情
     *               void
     * <AUTHOR>
     */
    private void appendUserAppendDetail(UserRefundDetailBo detail, OrderRefund orderRefund) {
        // 是否能重新申请，如果能，前端显示【重新申请】按钮
        Order order = orderRepository.getByOrderId(detail.getOrderId());
        // 订单有效的售后
        List<OrderRefund> validList = getValidByOrderId(orderRefund.getOrderId());
        // 交易设置中的售后维权期
        TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
        String refundCloseConfigDays = setting.getSalesReturnTimeout();
        int refundCloseDays = SettingUtil.getIntValueOrDefault("售后维权期天数", refundCloseConfigDays, CommonConst.DEFAULT_REFUND_CLOSE_DAYS);
        // 是否超过售后维权期
        boolean hasOverAfterSale = RefundStatusHelper.hasOverAfterSale(refundCloseDays, order.getFinishDate());
        boolean canReapply = orderRefundBizAssist.canReapply(order, orderRefund, validList, hasOverAfterSale);
        detail.setCanReapply(canReapply);
        // 处理售后步骤，后端根据业务计算，前端直接展示
        List<RefundStepBo> stepList = calStepList(detail);
        detail.setStepList(stepList);
    }

    /**
     * 计算售后步骤，后端直接处理好，前端显示
     * <pre>
     *     一、仅退款
     *     1. 正常情况下，即刚申请，或者审核中时，显示【买家申请售后】【供应商审核申请】【平台完成退款】，已完成的状态显示橘黄色，当前状态橘黄色高亮，未开始的状态显示灰色
     *     2. 如果供应商审核拒绝，直接显示【买家申请售后】【供应商审核申请】【供应商审核拒绝】，全部橘黄色
     *     3. 如果平台驳回，显示【买家申请售后】【供应商审核申请】【平台驳回申请】，全部橘黄色
     *     二、退货退款
     *     1.
     * </pre>
     *
     * @param detail 商家售后详情
     * <AUTHOR>
     */
    private List<RefundStepBo> calStepList(UserRefundDetailBo detail) {
        List<RefundStepBo> stepList = new ArrayList<>(8);
        // 已取消，只返回一条步骤数据，不需要高亮
        if (Boolean.TRUE.equals(detail.getHasCancel())) {
            stepList.add(new RefundStepBo(RefundStatusEnum.BUYER_CANCEL.getCode(), RefundStatusEnum.BUYER_CANCEL.getStepDesc(), true));
            return stepList;
        }
        // 非取消中的第一条步骤，一定是【买家申请】
        stepList.add(new RefundStepBo(RefundStatusEnum.BUYER_APPLY.getCode(), RefundStatusEnum.BUYER_APPLY.getStepDesc(), true));
        // 根据状态，计算补充后续的步骤
        appendStatusStep(stepList, detail);

        detail.setStepList(stepList);
        return stepList;
    }


    private void appendSellerRefundDetail(OrderRefund orderRefund, SellerRefundDetailBo detail) {
        // 判断退款完成，联系人和手机号码脱敏，支付完成回调时会设置支付状态为已完成
        if (RefundAuditStatusEnum.REFUND_SUCCESS.getCode().equals(orderRefund.getManagerConfirmStatus())) {
            detail.setContactUserName(DesensitizedUtil.chineseName(orderRefund.getContactPerson()));
            detail.setContactUserPhone(DesensitizedUtil.mobilePhone(orderRefund.getContactCellPhone()));
        }
        // 计算扩展的售后类型描述，页面需要根据是否整单退以及退款方式展示不同的描述
        appendSellerApproveDeadline(orderRefund, detail);
        Boolean hasAllReturn = orderRefund.getHasAllReturn();
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) || Boolean.TRUE.equals(hasAllReturn)) {
            RefundItemBo itemBo = new RefundItemBo();
            itemBo.setProductName(CommonConst.DESC_REFUND_ALL_PRODUCT_NAME);
            detail.setItem(itemBo);
        }
        else {
            OrderItem orderItem = orderItemRepository.getById(orderRefund.getOrderItemId());
            RefundItemBo itemBo = convertRefundItem(orderItem);
            detail.setItem(itemBo);
        }
    }

    /**
     * 获取供应商审核截止事件描述，格式化成 xx天xx小时xx分
     *
     * @param applyDate      申请时间
     * @param settingDaysStr 交易设置的超时天数
     * <AUTHOR>
     */
    private String getShopApproveDeadlineForSeller(Date applyDate, String settingDaysStr) {
        if (StrUtil.isBlank(settingDaysStr)) {
            return null;
        }
        // 交易设置中的供应商审核超时时间
        int timeoutDays = Integer.parseInt(settingDaysStr);
        // 申请时间+超时时间=截止时间
        Date deadline = DateUtil.offsetDay(applyDate, timeoutDays);
        Date now = new Date();
        // 如果当前时间已经超过了截止时间，不需要再计算了
        if (now.after(deadline)) {
            return null;
        }
        // 计算剩余时间毫秒数
        long diffMs = DateUtil.betweenMs(now, deadline);
        // 格式化剩余时间  xx天xx小时xx分
        return DateUtil.formatBetween(diffMs, BetweenFormatter.Level.MINUTE);
    }

    /**
     * 填充供应商审核的截止相关数据
     *
     * @param orderRefund 退款记录
     * @param detail      供应商售后详情
     *                    void
     * <AUTHOR>
     */
    private void appendSellerApproveDeadline(OrderRefund orderRefund, SellerRefundDetailBo detail) {
        String extRefundTypeDesc = ExtRefundTypeEnum.getDesc(orderRefund.getRefundMode(), orderRefund.getHasAllReturn());
        log.info("appendSellerApproveDeadline, orderRefund={}, detail={}", orderRefund, detail);
        detail.setExtRefundTypeDesc(extRefundTypeDesc);

        // 待审核时，设置审核截止时间
        if (RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(orderRefund.getStatus()) ||
                RefundStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode().equals(orderRefund.getStatus())) {
            // 供应商审核截止描述
            TradeSiteSettingBo setting = settingRemoteService.getTradeSiteSetting();
            String deadlineDesc = this.getShopApproveDeadlineForSeller(orderRefund.getApplyDate(), setting.getShopConfirmTimeout());
            detail.setRemainApproveDeadlineDesc(deadlineDesc);
            // 如果是仅退款，下一个步骤是平台审核，如果是退货退款，下一个状态是待买家寄货
            boolean onlyRefund = RefundModeEnum.ORDER_REFUND.getCode().equals(orderRefund.getRefundMode()) ||
                    RefundModeEnum.GOODS_REFUND.getCode().equals(orderRefund.getRefundMode());
            RefundStatusEnum nextStatus = onlyRefund ? RefundStatusEnum.WAIT_PLATFORM_CONFIRM : RefundStatusEnum.WAIT_BUYER_SEND;
            detail.setDeadlineNextStatus(nextStatus.getCode());
            detail.setDeadlineNextStatusDesc(nextStatus.getDesc());
        }
    }


    /**
     * 计算售后步骤，后端直接处理好，前端显示
     * <pre>
     *     一、仅退款
     *     1. 正常情况下，即刚申请，或者审核中时，显示【买家申请售后】【供应商审核申请】【平台确认】【汇付退款】，已完成的状态显示橘黄色，当前状态橘黄色高亮，未开始的状态显示灰色
     *     2. 如果供应商审核拒绝，直接显示【买家申请售后】【供应商审核申请】【供应商审核拒绝】，全部橘黄色
     *     3. 如果平台驳回，显示【买家申请售后】【供应商审核申请】【平台驳回申请】，全部橘黄色
     *     二、退货退款
     *     1. 正常情况下，即刚申请，或者审核中时，显示【买家申请售后】【供应商审核申请】【买家回寄商品】【供应商确认收货】【平台确认】【汇付退款】，已完成的状态显示橘黄色，当前状态橘黄色高亮，未开始的状态显示灰色
     *     2. 如果供应商审核拒绝，直接显示【买家申请售后】【供应商审核申请】【供应商审核拒绝】，全部橘黄色
     *     3. 如果平台驳回，显示【买家申请售后】【供应商审核申请】【平台驳回申请】，全部橘黄色
     *     4. 如果供应商审核通过，且同意弃货，显示【买家申请售后】【供应商审核申请】【平台确认】【汇付退款】
     * </pre>
     *
     * @param detail 商家售后详情
     * <AUTHOR>
     */
    private void appendStatusStep(List<RefundStepBo> stepList, UserRefundDetailBo detail) {
        // 供应商拒绝，显示【买家申请售后】【供应商审核申请】【供应商审核拒绝】，全部橘黄色
        if (RefundAuditStatusEnum.SUPPLIER_REFUSE.getCode().equals(detail.getSellerAuditStatus())) {
            stepList.add(new RefundStepBo(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode(), RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getStepDesc(), true));
            stepList.add(new RefundStepBo(RefundStatusEnum.SUPPLIER_REFUSE.getCode(), RefundStatusEnum.SUPPLIER_REFUSE.getStepDesc(), true, true));
            return;
        }
        // 平台驳回，显示【买家申请售后】【供应商审核申请】【平台驳回申请】，全部橘黄色
        if (RefundAuditStatusEnum.PLATFORM_REFUSE.getCode().equals(detail.getManagerConfirmStatus())) {
            stepList.add(new RefundStepBo(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode(), RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getStepDesc(), true));
            stepList.add(new RefundStepBo(RefundStatusEnum.PLATFORM_REFUSE.getCode(), RefundStatusEnum.PLATFORM_REFUSE.getStepDesc(), true, true));
            return;
        }
        if (RefundModeEnum.ORDER_REFUND.getCode().equals(detail.getRefundMode()) ||
                RefundModeEnum.GOODS_REFUND.getCode().equals(detail.getRefundMode())) {
            appendStepForRefund(stepList, detail);
            return;
        }
        appendStepForReturn(stepList, detail);
    }

    private void appendStepForRefund(List<RefundStepBo> stepList, UserRefundDetailBo detail) {
        // 剩下的就是正常显示了
        RefundStepBo sellerStep = new RefundStepBo(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode(), RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getStepDesc());
        stepList.add(sellerStep);
        // 平台确认
        RefundStepBo platformConfirm = new RefundStepBo(RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode(), RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getStepDesc());
        stepList.add(platformConfirm);
        // 汇付退款
        RefundStepBo refunded = new RefundStepBo(RefundStatusEnum.REFUND_SUCCESS.getCode(), RefundStatusEnum.REFUND_SUCCESS.getStepDesc());
        stepList.add(refunded);
        // 如果是待供应商审核，则第二步为当前步骤
        if (RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(detail.getStatus())) {
            sellerStep.setCurrentStep(true);
        }
        // 如果供应商审核通过，平台步骤显示颜色
        else if (RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode().equals(detail.getStatus())) {
            sellerStep.setHighlight(true);
            platformConfirm.setCurrentStep(true);
        }
        // 如果平台还没有审核通过，即没有完成退款，则平台状态为当前状态
        else if (RefundStatusEnum.REFUND_SUCCESS.getCode().equals(detail.getStatus())) {
            sellerStep.setHighlight(true);
            platformConfirm.setHighlight(true);
            refunded.setCurrentStep(true);
        }
    }

    private void appendStepForReturn(List<RefundStepBo> stepList, UserRefundDetailBo detail) {
        // 剩下的就是正常显示了
        // 供应商审核申请
        RefundStepBo sellerAuditStep = new RefundStepBo(RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getCode(), RefundStatusEnum.WAIT_SUPPLIER_AUDIT.getStepDesc());
        // 买家寄货
        RefundStepBo buyerSendStep = new RefundStepBo(RefundStatusEnum.WAIT_BUYER_SEND.getCode(), RefundStatusEnum.WAIT_BUYER_SEND.getStepDesc());
        // 供应商确认收货
        RefundStepBo sellerConfirmStep = new RefundStepBo(RefundStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode(), RefundStatusEnum.WAIT_SUPPLIER_RECEIVE.getStepDesc());
        // 平台完成退款
        RefundStepBo platformStep = new RefundStepBo(RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode(), RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getStepDesc());
        // 汇付退款
        RefundStepBo refunded = new RefundStepBo(RefundStatusEnum.REFUND_SUCCESS.getCode(), RefundStatusEnum.REFUND_SUCCESS.getStepDesc());
        if (RefundAuditStatusEnum.WAIT_SUPPLIER_AUDIT.getCode().equals(detail.getSellerAuditStatus())) {
            sellerAuditStep.setCurrentStep(true);
        }
        else if (RefundAuditStatusEnum.WAIT_BUYER_SEND.getCode().equals(detail.getSellerAuditStatus())) {
            sellerAuditStep.setHighlight(true);
            buyerSendStep.setCurrentStep(true);
        }
        else if (RefundAuditStatusEnum.WAIT_SUPPLIER_RECEIVE.getCode().equals(detail.getSellerAuditStatus())) {
            sellerAuditStep.setHighlight(true);
            buyerSendStep.setHighlight(true);
            sellerConfirmStep.setCurrentStep(true);
        }
        else if (RefundStatusEnum.WAIT_PLATFORM_CONFIRM.getCode().equals(detail.getStatus())) {
            sellerAuditStep.setHighlight(true);
            buyerSendStep.setHighlight(true);
            sellerConfirmStep.setHighlight(true);
            platformStep.setHighlight(true);
            platformStep.setCurrentStep(true);
        }
        else {
            sellerAuditStep.setHighlight(true);
            buyerSendStep.setHighlight(true);
            sellerConfirmStep.setHighlight(true);
            platformStep.setHighlight(true);
            platformStep.setHighlight(true);
            refunded.setCurrentStep(true);
        }

        stepList.add(sellerAuditStep);
        stepList.add(buyerSendStep);
        stepList.add(sellerConfirmStep);
        stepList.add(platformStep);
        stepList.add(refunded);
    }


    /**
     * 根据订单数据，设置预览订单售后时，可退金额
     *
     * @param order java.lang.String
     * <AUTHOR>
     */
    private String getRemainRefundAmountDesc(Order order) {
        // 如果是待发货状态下，直接退所有订单金额
        if (OrderStatusEnum.UNDER_SEND.getCode().equals(order.getOrderStatus())) {
            return String.format(CommonConst.MESSAGE_REFUND_AMOUNT_NO_FREIGHT_DESC, order.getTotalAmount());
        }
        // 如果是待收货或者已完成状态下，需要显示运费
        if (order.getFreight() != null && order.getFreight().compareTo(BigDecimal.ZERO) > 0) {
            return String.format(CommonConst.MESSAGE_REFUND_AMOUNT_INCLUDE_FREIGHT_DESC, order.getTotalAmount(), order.getFreight());
        }
        return String.format(CommonConst.MESSAGE_REFUND_AMOUNT_NO_FREIGHT_DESC, order.getTotalAmount());
    }


    private void setApplyDataForOrderRefund(ApplyRefundBo applyBo, Order order) {
        applyBo.setOrderItemId(0L);
        applyBo.setRefundMode(RefundModeEnum.ORDER_REFUND);
        // 订单退款是发货前，不需要退货
        applyBo.setHasAllReturn(false);
        // 订单退款，只能选 仅退款，退款数量默认为0
        applyBo.setRefundQuantity(CommonConst.REFUND_QUANTITY_DEFAULT_ZERO);
        // 发货前订单退款，不需要设置运费，默认全退
        applyBo.setReturnFreightAmount(BigDecimal.ZERO);
        // 防止页面修改，直接用数据库的重置，订单退，退订单实付金额
        applyBo.setRefundAmount(order.getTotalAmount());
        // 发货前，不需要退货
        applyBo.setApplyQuantity(CommonConst.REFUND_QUANTITY_DEFAULT_ZERO);
        applyBo.setReturnCommissionAmount(BigDecimal.ZERO);
    }

    private void setApplyDataForWholeOrder(ApplyRefundBo applyBo, Order order) {
        // 发货后，整单退，是否全部退货设置为true
        applyBo.setHasAllReturn(true);
        // 如果是整单退，设置运费，供应商审核时可以决定是否真的退运费
        applyBo.setReturnFreightAmount(NumberUtil.nullToZero(order.getFreight()));
        // 防止页面修改，直接用数据库的重置，订单退，退订单实付金额
        applyBo.setRefundAmount(order.getTotalAmount());
        List<OrderItem> orderItemList = orderItemRepository.getByOrderId(order.getOrderId());
        Long quantity = orderItemList.stream().mapToLong(OrderItem::getQuantity).sum();
        // 发货后，整单，默认申请数量为明细所有数量
        applyBo.setApplyQuantity(quantity);
        // 如果是退货退款，则退货数量默认为总数量
        if (RefundTypeEnum.ONLY_REFUND.equals(applyBo.getRefundType())) {
            applyBo.setRefundQuantity(CommonConst.REFUND_QUANTITY_DEFAULT_ZERO);
            applyBo.setRefundMode(RefundModeEnum.GOODS_REFUND);
        }
        else {
            applyBo.setRefundMode(RefundModeEnum.RETURN_AND_REFUND);
            applyBo.setRefundQuantity(quantity);
        }
        applyBo.setReturnCommissionAmount(order.getCommissionTotalAmount());
    }

    private void setApplyDataForItem(ApplyRefundBo applyBo, Order order, OrderItem orderItem) {
        // 单个退设置为false
        applyBo.setHasAllReturn(false);
        // 首先将传入的退货数量设置到申请数量
        applyBo.setApplyQuantity(applyBo.getRefundQuantity());
        // 数据表存了一个退款模式，底层逻辑复用，这里区分以下
        if (RefundTypeEnum.ONLY_REFUND.equals(applyBo.getRefundType())) {
            applyBo.setRefundQuantity(CommonConst.REFUND_QUANTITY_DEFAULT_ZERO);
            applyBo.setRefundMode(RefundModeEnum.GOODS_REFUND);
        }
        else {
            applyBo.setRefundMode(RefundModeEnum.RETURN_AND_REFUND);
        }
        checkOrderItemCanRefund(order, orderItem);

        if (orderItem.getCommisRate() != null && orderItem.getCommisRate().compareTo(BigDecimal.ZERO) > 0) {
            applyBo.setReturnCommissionAmount(NumberUtil.mul(applyBo.getRefundAmount(), orderItem.getCommisRate()));
        }
        BigDecimal returnCommission = NumberUtil.mul(applyBo.getRefundAmount(), orderItem.getCommisRate()).setScale(2, RoundingMode.HALF_UP);
        applyBo.setReturnCommissionAmount(returnCommission);
    }

    private void checkCanReverse(String orderId) {
        // 校验订单支付时间是否超过支付撤销截止天数，这个是汇付支付渠道的配置，超过时间，不可进行支付撤销(未分帐前的退款实际是支付撤销)
        Order order = orderRepository.getByOrderId(orderId);
        AssertUtil.throwIfTrue(order == null, "订单不存在");
        AssertUtil.throwIfTrue(order.getPayDate() == null, "当前订单未支付不可进行审核");
        int paidReverseTimeout = SettingUtil.getIntValueOrDefault("支付后可撤销超时天数", payProps.getPaidReverseTimeoutDays(), CommonConst.DEFAULT_PAID_REVERSE_TIMEOUT_DAYS);
        Date paidReverseEndDate = DateUtil.offsetDay(order.getPayDate(), paidReverseTimeout);
        // 如果当前时间大于支付可以撤销的截止时间，则报错提示用户审核拒绝
        if (DateUtil.compare(new Date(), paidReverseEndDate) > 0) {
            throw new BusinessException("已过了售后时间段，请取消售后单");
        }
    }


}
