package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderAndItemScrollBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderExportBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryPlatformOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QuerySellerOrderBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryUserOrderBo;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollQueryReq;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface OrderExportService {

    /**
     * 根据查询条件，构造商家导出的滚动查询
     * @param searchBo 查询条件
     */
    OrderAndItemScrollBo getScrollIdForUserExport(QueryUserOrderBo searchBo);

    /**
     * 根据查询条件，构造供应商导出的滚动查询
     * @param searchBo 查询条件
     */
    OrderAndItemScrollBo getScrollIdForSellerExport(QuerySellerOrderBo searchBo);

    /**
     * 根据查询条件，构造平台导出的滚动查询
     * @param searchBo 查询条件
     */
    OrderAndItemScrollBo getScrollIdForPlatformExport(QueryPlatformOrderBo searchBo);

    /**
     * 平台导出查询
     * @param searchBo 查询参数
     */
    BasePageResp<OrderExportBo> searchExportForPlatform(QueryPlatformOrderBo searchBo);

    /**
     * 卖家订单导出搜索，单独的接口是因为一个订单可能对应的商品很多，如果以订单为维度，可能每页数据太多
     * <p>接口和参数构建分角色，这样权限独立，底层实际调用ES可以公用</p>
     * <AUTHOR>
     * @param searchBo 查询参数
     */
    BasePageResp<OrderExportBo> searchExportForSeller(QuerySellerOrderBo searchBo);

    /**
     * 用户导出查询
     * @param searchBo 查询参数
     */
    BasePageResp<OrderExportBo> searchExportForUser(QueryUserOrderBo searchBo);

    /**
     * 通过scrollId查询订单列表
     * <AUTHOR>
     * @param queryReq 滚动查询参数
     */
    List<OrderExportBo> searchByScrollId(EsScrollQueryReq queryReq);

    /**
     * 清除ES内存中的scroll数据
     * 为了节约资源，滚动查询结束后需要清除
     * <AUTHOR>
     * @param scrollId 滚动ID
     */
    void clearScrollId(String scrollId);

}
