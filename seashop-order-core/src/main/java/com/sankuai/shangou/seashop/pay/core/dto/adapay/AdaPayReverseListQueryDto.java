package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import lombok.Builder;
import lombok.Data;

/**
 * @author: lhx
 * @date: 2023/11/23/023
 * @description:
 */
@Data
@Builder
public class AdaPayReverseListQueryDto {

    private String appId;

    /**
     * Adapay生成的支付对象id
     */
    private String paymentId;

    /**
     * 当前页码，取值范围1~300000，默认值为1
     */
    private Integer pageIndex;

    /**
     * 页面容量，取值范围1~20，默认值为10
     */
    private Integer pageSize;

    /**
     * 查询大于等于创建时间戳
     */
    private String createdGte;

    /**
     * 查询小于等于创建时间戳；若不为空时，created_gte字段值不能为空且小于created_lte时间
     */
    private String createdLte;
}
