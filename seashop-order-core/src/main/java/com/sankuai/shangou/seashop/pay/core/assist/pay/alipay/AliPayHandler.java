package com.sankuai.shangou.seashop.pay.core.assist.pay.alipay;

import javax.annotation.Resource;

import com.alipay.easysdk.factory.Factory;
import com.alipay.easysdk.kernel.Config;
import com.alipay.easysdk.kernel.util.ResponseChecker;
import com.alipay.easysdk.payment.common.models.AlipayTradeFastpayRefundQueryResponse;
import com.alipay.easysdk.payment.common.models.AlipayTradeQueryResponse;
import com.alipay.easysdk.payment.common.models.AlipayTradeRefundResponse;
import com.alipay.easysdk.payment.facetoface.models.AlipayTradePrecreateResponse;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.pay.core.assist.pay.AbstractPayHandler;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundResult;
import com.sankuai.shangou.seashop.pay.dao.core.model.AliPayConfigModel;
import com.sankuai.shangou.seashop.pay.dao.core.repository.ChannelConfigRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;

import lombok.extern.slf4j.Slf4j;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/09/04 18:23
 */
@Component
@Slf4j
public class AliPayHandler extends AbstractPayHandler<AliPayConfigModel> {

    @Resource
    private ChannelConfigRepository channelConfigRepository;

    @Resource
    private S3plusStorageService s3plusStorageService;

    @Value("${hishop.storage.domain}")
    private String hostName;
    @Value("${hishop.storage.base-path}")
    private String bucketName;

    @Value("${alipay.callback.url}")
    private String callBackUrl;
    @Override
    protected PaymentChannelEnum paymentChannel() {
        return PaymentChannelEnum.ALIPAY;
    }

    @Override
    protected AliPayConfigModel getInitPayConfig() {
        AliPayConfigModel aliPayConfig = channelConfigRepository.getAliPayConfig();
        // todo 此处可能需要解析远程的obs 证书
        Config config = new Config();
        config.protocol = "https";
        config.gatewayHost = "openapi.alipay.com";
        config.signType = "RSA2";
        config.appId = aliPayConfig.getAliPayAppId();
        config.merchantPrivateKey = aliPayConfig.getAliPayAppSecret();


        //easy sdk 不支持远端文件  所以先读取obs上文件  写入到classpath中  从本地读取
        setCertContent(aliPayConfig.getAliPayAppCertPath(), "merchantCert.crt");
        setCertContent(aliPayConfig.getAliPayCertPath(), "alipayCert.crt");
        setCertContent(aliPayConfig.getAliPayRootCertPath(), "alipayRootCert.crt");
        config.merchantCertPath = "alipay/merchantCert.crt";
        config.alipayCertPath = "alipay/alipayCert.crt";
        config.alipayRootCertPath = "alipay/alipayRootCert.crt";
        //非证书模式，无需赋值上面的三个证书路径，改为赋值如下的支付宝公钥字符串
//         config.alipayPublicKey = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEApTDpslPteEqpMubGe/0kG4PAAp83VjBN4MvM+omqcvcbeTO0RanisICvvXdGvHTZQIsABpx9SDCeHKleVQffx3jhrtHMucDPk8VAeVJnRTom6dMBGGYZMWyC8DL4+lfrKG+dfVyMPeRgU0Aup+YRtJPc+KslUNVkFzA5S1a8zBxlMGBjY3C6AGeEkoCqq2qPpPRYEWZKx1pEn3cnbISzDYMe80Yub3/lwCuPXfrC0NkOzokDsN/c6YxdANDltlF05YJOgi6R3QUCL8bgOWBOdfjKEZFeGAfOrsyB2aaIuNZ4vKBbUZ//qtFCna5UujYVx/DMiJCrYrfQ5aXtPl6hOQIDAQAB";
        config.notifyUrl = callBackUrl;
        Factory.setOptions(config);
        return aliPayConfig;
    }

    @Override
    public PayResult pay(PayParam payParam) {
        // 这样可以获取到支付配置
        try {
//            AliPayConfigModel payConfigCache = getPayConfigCache();
            PayResult payResult = new PayResult();
            AlipayTradePrecreateResponse response = Factory.Payment.FaceToFace().preCreate(payParam.getGoodsTitle(), payParam.getPayId(), payParam.getPayAmount().toString());
            if (ResponseChecker.success(response)) {
                payResult.setSuccess(true);
                Map<String, Object> appResult = new HashMap<>();
                appResult.put("out_trade_no", response.outTradeNo);
                appResult.put("qr_code_url", response.qrCode);
                payResult.setRemoteResp(appResult);
            } else {
                log.error("调用失败，原因：" + response.msg + "，" + response.subMsg);
                payResult.setSuccess(false);
            }
            return payResult;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public PayNotifyResult notifyPay(String payData) {

        try {
            // todo 支付回调
            PayNotifyResult result = new PayNotifyResult();
            Map<String, String> params = JsonUtil.parseObject(payData, Map.class);
            boolean verifyResult = Factory.Payment.Common().verifyNotify(params);
            if (verifyResult) {
                result.setPayId(params.get("out_trade_no"));
                result.setPayTime(new Date(params.get("gmt_payment")));
                result.setThirdResult(params);
            } else {
                result.setSuccess(false);
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public PayConfirmResult payConfirm(PayConfirmParam confirmParam) {
        // 构造请求参数以调用接口
        try {
            PayConfirmResult result = new PayConfirmResult();
            AlipayTradeQueryResponse response = Factory.Payment.Common()
                    .optional("trade_no",confirmParam.getChannelPayId())
                    .query(confirmParam.getChannelPayId());
            if (ResponseChecker.success(response)) {
                result.setSuccess(true);
                result.setPayId(response.tradeNo);
                result.setPayTime(new Date(response.sendPayDate));
                result.setTradeState(response.tradeStatus);
                return result;
            } else {
                log.error("调用失败，原因：" + response.msg + "，" + response.subMsg);
                result.setSuccess(false);
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public RefundResult refund(RefundParam refundParam) {
        try {
//            AliPayConfigModel payConfigCache = getPayConfigCache();
            RefundResult refundResult = new RefundResult();
            AlipayTradeRefundResponse response = Factory.Payment.Common()
                    .optional("out_request_no",refundParam.getReverseId())
                    .refund(refundParam.getOrderId(), refundParam.getReverseAmount().toString());
            if (ResponseChecker.success(response)) {
                refundResult.setSuccess(true);
                Map<String, Object> appResult = new HashMap<>();
                appResult.put("trade_no", response.tradeNo);
                appResult.put("out_trade_no", response.outTradeNo);
                appResult.put("fund_change", response.fundChange);
                appResult.put("buyer_logon_id", response.buyerLogonId);
                appResult.put("refund_fee", response.refundFee);
                refundResult.setChannelRefundId(response.tradeNo);
                refundResult.setRemoteResp(appResult);
            } else {
                log.error("调用失败，原因：" + response.msg + "，" + response.subMsg);
                refundResult.setSuccess(false);
            }
            return refundResult;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public RefundConfirmResult refundConfirm(RefundConfirmParam refundConfirmParam) {
        try {
            RefundConfirmResult result = new RefundConfirmResult();
            AlipayTradeFastpayRefundQueryResponse response = Factory.Payment.Common()
                    .optional("query_options","gmt_refund_pay")
                    .optional("trade_no",refundConfirmParam.getChannelPayId())
                    .queryRefund(refundConfirmParam.getChannelPayId(),refundConfirmParam.getReverseId());
            if (ResponseChecker.success(response)) {
                if(response.getRefundStatus().equals("REFUND_SUCCESS")){
                    result.setSuccess(true);
                }else {
                    result.setSuccess(false);
                }

                result.setReverseId(refundConfirmParam.getReverseId());
                result.setChannelRefundId(response.outRequestNo);
                result.setRefundStatus(response.refundStatus);
                result.setRefundDate(new Date(response.gmtRefundPay));
                return result;
            } else {
                log.error("调用失败，原因：" + response.msg + "，" + response.subMsg);
                result.setSuccess(false);
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public RefundNotifyResult notifyRefund(String refundData) {
        //支付宝只有退款到银行卡 才有回调

        try {
            RefundNotifyResult result = new RefundNotifyResult();
            Map<String, String> params = JsonUtil.parseObject(refundData, Map.class);
            boolean verifyResult = Factory.Payment.Common().verifyNotify(params);
            if (verifyResult) {

                result.setChannelRefundId(params.get("trade_no"));
                result.setReverseId(params.get("out_trade_no"));
                result.setRefundStatus(params.get("dback_status"));
                if (params.get("dback_status").toString().equals("Y")) {
                    result.setSuccess(true);
                } else {
                    result.setSuccess(false);
                }
                result.setRefundDate(new Date(params.get("bank_ack_time")));
                result.setThirdResult(params);
            } else {
                result.setSuccess(false);
            }
            return result;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void setCertContent(String obsPath, String fileName) {
        String uri = hostName + bucketName + "/" + obsPath;
        try {
            InputStream stream = s3plusStorageService.readExcelFromOnlineUrl(uri);
            InputStreamReader inputStreamReader = new InputStreamReader(stream);
            BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
            StringBuilder stringBuilder = new StringBuilder();
            String line;
            while ((line = bufferedReader.readLine()) != null) {
                stringBuilder.append(line);
                stringBuilder.append(System.lineSeparator());
            }
            String content = stringBuilder.toString();
            content = content.trim().replaceAll("\n$", "");
            Path path = Paths.get("alipay/" + fileName);
            byte[] bytes = content.getBytes();
            if (!Files.exists(path)) {
                Files.createDirectories(path.getParent());
                Files.createFile(path.toAbsolutePath());
            }
            Files.write(path, bytes);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
