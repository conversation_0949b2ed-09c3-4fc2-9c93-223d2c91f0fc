package com.sankuai.shangou.seashop.order.core.service.model;

import com.sankuai.shangou.seashop.order.thrift.core.enums.event.ThirdEvent;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ThirdPushEventBo {
    /**
     * 数据主键
     */
    private Long id;
    /**
     * 推送编号 可以为订单id或者 productId
     */
    private String sendCode;
    /**
     * 事件类型 0:订单事件 1:商品事件 2:退货单
     */
    private ThirdEvent.EventTypeEnum eventType;
    /**
     * 发送目标 1：旺店通 2：聚水潭 3：网店管家 4：吉客云
     */
    private ThirdEvent.SendTargetEnum sendTarget;
    /**
     * 推送状态 0:待推送 1：推送成功 2：推送失败，3：推送中
     */
    private ThirdEvent.SendStateEnum sendState;
    /**
     * 事件内容
     */
    private String eventBody;
    /**
     * 推送返回的错误消息
     */
    private String sendMsg;
}
