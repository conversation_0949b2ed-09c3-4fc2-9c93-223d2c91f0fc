package com.sankuai.shangou.seashop.order.core.service;

import com.sankuai.shangou.seashop.order.core.service.model.ThirdPushEventBo;

/**
 * <AUTHOR>
 */
public interface ThirdEventService {
    /**
     * 保存或者更新推送事件
     *
     * @param bo 推送事件
     */
    Long saveOrUpdatePushEvent(ThirdPushEventBo bo);

    /**
     * 根据id查询推送事件
     *
     * @param id id
     * @return 推送事件
     */
    ThirdPushEventBo queryPushEventById(Long id);

}
