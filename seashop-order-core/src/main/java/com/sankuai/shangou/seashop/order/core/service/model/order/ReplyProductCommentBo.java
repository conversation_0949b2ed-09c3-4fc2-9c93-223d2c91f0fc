package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/04 17:48
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ReplyProductCommentBo extends BaseParamReq {

    /**
     * 商品评论id
     */
    @ExaminField(description = "商品评价id")
    private Long productCommentId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 回复内容
     */
    @ExaminField(description = "回复评论")
    private String replyContent;

    /**
     * 追加回复内容
     */
    @ExaminField(description = "回复追加评论")
    private String replyAppendContent;

}
