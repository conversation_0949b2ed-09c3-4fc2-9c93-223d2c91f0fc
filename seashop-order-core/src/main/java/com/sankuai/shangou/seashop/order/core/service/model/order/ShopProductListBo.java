package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 购物车商品返回对象
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
public class ShopProductListBo {

    /**
     * 店铺订单信息
     */
    private OrderShopBo shop;
    /**
     * 商品列表
     */
    private List<ShopProductBo> productList;
    /**
     * 订单附加信息，主要是每个订单用户可以自定义的信息
     */
    private OrderAdditionalBo additional;

    /**
     * 订单ID。用途是，保存订单时将订单ID同时设置到这个入参数据，后续营销数据保存可以用到
     */
    private String orderId;

    public OrderAdditionalBo getAdditional() {
        if (additional == null) {
            additional = new OrderAdditionalBo();
        }
        return additional;
    }
}
