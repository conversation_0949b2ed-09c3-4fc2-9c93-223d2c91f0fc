package com.sankuai.shangou.seashop.order.core.mq.listener;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.nio.charset.StandardCharsets;

/**
 * 暂时先这么用，后续有了dts再改
 *
 * <AUTHOR>
 * @date 2024/07/24 14:59
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConst.TOPIC_ORDER_CHANGE + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_ORDER_ES_BUILD + "_${spring.profiles.active}")
public class OrderEsBuildListener implements RocketMQListener<MessageExt>  {

    @Resource
    private EsOrderService esOrderService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【订单ES构建】【OrderEsBuildListener】消息内容为: {}", body);

        OrderMessage orderMessage = null;
        try {
            orderMessage = JsonUtil.parseObject(body, OrderMessage.class);
            esOrderService.buildEsOrder(orderMessage.getOrderId());
        }
        catch (Exception e) {
            log.error("【mafka消费】【订单ES 构建失败】处理失败, msg: {}", body, e);
            throw new RuntimeException(e);
        }
    }
}
