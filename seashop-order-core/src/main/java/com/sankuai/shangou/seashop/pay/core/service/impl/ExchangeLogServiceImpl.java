package com.sankuai.shangou.seashop.pay.core.service.impl;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.pay.core.service.ExchangeLogService;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ExchangeLog;
import com.sankuai.shangou.seashop.pay.dao.core.repository.ExchangeLogRepository;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ExchangeLogReq;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 16:21
 */
@Service
public class ExchangeLogServiceImpl implements ExchangeLogService {

    @Resource
    private ExchangeLogRepository exchangeLogRepository;

    @Override
    public void insetExchangeLog(ExchangeLogReq request) {
        exchangeLogRepository.insetExchangeLog(JsonUtil.copy(request, ExchangeLog.class));
    }
}
