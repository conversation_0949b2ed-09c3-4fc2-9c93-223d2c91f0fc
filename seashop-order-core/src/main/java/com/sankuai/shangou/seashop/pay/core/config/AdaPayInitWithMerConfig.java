package com.sankuai.shangou.seashop.pay.core.config;

import com.huifu.adapay.Adapay;
import com.huifu.adapay.model.MerConfig;
import com.sankuai.shangou.seashop.pay.core.service.ChannelConfigService;
import com.sankuai.shangou.seashop.pay.dao.core.model.AdaPayConfigModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description 汇付支付初始化
 * @Date 2023/2/24 9:52
 */
@Component
@Slf4j(topic = "adaPayInitConfig")
public class AdaPayInitWithMerConfig implements CommandLineRunner {

    public static String appId = "";
    @Resource
    private ChannelConfigService channelConfigService;
    @Value("${adapay.callback.url}")
    private String adaPayCallbackUrl;

    @Override
    public void run(String... args) throws Exception {
        initAdaPayConfig();
    }

    public void initAdaPayConfig() throws Exception {
        log.info("汇付支付初始化开始");

        AdaPayConfigModel adaPayConfigModel = channelConfigService.getAdaPayConfigModel();
        log.info("汇付配置信息:{}", adaPayConfigModel);
        log.info("汇付当前回调地址为:{}", adaPayCallbackUrl);

        // debug 模式，开启后有详细的日志
        Adapay.debug = adaPayConfigModel.getIzDebug();
        // prodMode 模式，默认为生产模式，false可以使用mock模式
        Adapay.prodMode = adaPayConfigModel.getProdMode();

        MerConfig merConfig = new MerConfig();
        merConfig.setApiKey(adaPayConfigModel.getApiKey());
        merConfig.setApiMockKey(adaPayConfigModel.getApiMockKey());
        merConfig.setDeviceId(adaPayConfigModel.getDeviceId());
        merConfig.setRSAPrivateKey(adaPayConfigModel.getRsaPrivateKey());

        Adapay.initWithMerConfig(merConfig);
        appId = adaPayConfigModel.getAppId();
        log.info("汇付支付初始化完成");
    }
}
