package com.sankuai.shangou.seashop.pay.core.dto.adapay;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2024/1/30/030
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class AdaPaymentReverseDto {

    @JsonProperty("order_no")
    private String orderNo;
    @JsonProperty("reversed_amt")
    private String reversedAmt;
    @JsonProperty("created_time")
    private long createdTime;
    @JsonProperty("reason")
    private String reason;
    @JsonProperty("confirmed_amt")
    private String confirmedAmt;
    @JsonProperty("business_mode")
    private String businessMode;
    @JsonProperty("payment_id")
    private String paymentId;
    @JsonProperty("sub_payment_id")
    private String subPaymentId;
    @JsonProperty("prod_mode")
    private boolean prodMode;
    @JsonProperty("refunded_amt")
    private String refundedAmt;
    @JsonProperty("channel_no")
    private String channelNo;
    @JsonProperty("succeed_time")
    private long succeedTime;
    @JsonProperty("reverse_amt")
    private String reverseAmt;
    @JsonProperty("id")
    private String id;
    @JsonProperty("app_id")
    private String appId;
    @JsonProperty("object")
    private String object;
    @JsonProperty("status")
    private String status;

}
