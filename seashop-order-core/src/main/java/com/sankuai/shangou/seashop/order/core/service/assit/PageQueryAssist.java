package com.sankuai.shangou.seashop.order.core.service.assit;

import cn.hutool.core.util.ObjectUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.order.common.config.OrderQueryProps;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PageQueryAssist {

    @Resource
    private OrderQueryProps orderQueryProps;

    /**
     * 校验订单分页时当前页码的数据量，如果超过默认值10000条，则抛出异常
     * <p>这个校验的目的是处理深分页的问题，给出提示让用户添加查询条件缩小数据量</p>
     * <p>目前订单通过ES查询，要支持指定页码分页时，通过from+size的方式，超过ES默认的10000条，会报错；
     * scroll和search_after不支持指定页码。换成mysql查询可能也慢，所以折中考虑，超过数量提示用户精确搜索</p>
     * <AUTHOR>
     * @param pageReq 分页参数
     */
    public void checkOrderPageTotal(BasePageReq pageReq) {
        Integer maxPageTotal = ObjectUtil.defaultIfNull(orderQueryProps.getMaxPageTotal(), CommonConst.MAX_PAGE_TOTAL_DEFAULT);
        log.info("校验分页查询数据量, maxPageTotal:{}, pageReq:{}", maxPageTotal, pageReq);
        if (pageReq.getPageNo() * pageReq.getPageSize() > maxPageTotal) {
            throw new BusinessException(CommonConst.MESSAGE_REACH_MAX_PAGE_TOTAL);
        }
    }

}
