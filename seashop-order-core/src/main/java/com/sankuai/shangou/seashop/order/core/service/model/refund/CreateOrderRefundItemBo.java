package com.sankuai.shangou.seashop.order.core.service.model.refund;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class CreateOrderRefundItemBo {

    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 规格货号
     */
    private Long skuAutoId;
    /**
     * 规格货号
     */
    private String skuCode;
    /**
     * 购买数量
     */
    private Long quantity;
    /**
     * 订单id
     */
    private Long orderItemId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

}
