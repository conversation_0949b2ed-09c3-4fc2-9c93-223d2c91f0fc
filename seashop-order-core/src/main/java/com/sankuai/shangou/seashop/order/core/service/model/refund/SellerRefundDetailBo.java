package com.sankuai.shangou.seashop.order.core.service.model.refund;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SellerRefundDetailBo extends RefundDetailBo {

    /**
     * 退款完成日期
     */
    private Date refundFinishDate;
    /**
     * 扩展的退款类型描述。发起售后的时候选择的是【仅退款】和【退货退款】，
     * 供应商详情显示时，需要根据整单和单品售后区分显示
     */
    private String extRefundTypeDesc;
    /**
     * 退款明细，如果为null，前端显示【订单所有商品】
     */
    private RefundItemBo item;
    /**
     * 审核截止时间描述
     */
    private String remainApproveDeadlineDesc;
    /**
     * 审核超时的下一个状态
     */
    private Integer deadlineNextStatus;
    /**
     * 审核超时的下一个状态描述
     */
    private String deadlineNextStatusDesc;

}
