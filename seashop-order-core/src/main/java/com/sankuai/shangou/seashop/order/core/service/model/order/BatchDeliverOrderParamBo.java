package com.sankuai.shangou.seashop.order.core.service.model.order;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.order.core.service.model.ShopBo;
import com.sankuai.shangou.seashop.order.core.service.model.UserBo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 订单发货请求对象
 * <AUTHOR>
 */
@Getter
@Setter
public class BatchDeliverOrderParamBo extends BaseParamReq {

    /**
     * 当前登录操作的店铺信息
     */
    private ShopBo shop;
    /**
     * 当前登录操作的用户信息
     */
    private UserBo user;
    /**
     * 是否需要物流
     */
    private Boolean needExpress;
    /**
     * 批量发货列表。如果不需要物流，也需要传入订单号信息
     */
    private List<OrderDeliveryBo> orderWayBillList;

}
