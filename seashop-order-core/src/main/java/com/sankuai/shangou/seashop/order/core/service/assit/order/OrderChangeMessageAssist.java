package com.sankuai.shangou.seashop.order.core.service.assit.order;

import java.util.Arrays;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderChangeMessageAssist {

    @Resource
    private OrderChangeMessageHandlerFactory orderChangeMessageHandlerFactory;

    public void handleRefundMessage(OrderMessage message) {
        if (Arrays.stream(OrderMessageEventEnum.values()).filter(item -> item.name().equals(message.getOrderEventName())).count() == 0) {
            log.warn("[mafka-seashop_order_change_topic] message: {} 不符合订单变更事件，忽略执行", message);
            return;
        }

        OrderChangeMessageHandler handler = orderChangeMessageHandlerFactory.getHandler(OrderMessageEventEnum.valueOf(message.getOrderEventName()));
        if (handler == null) {
            return;
        }
        handler.handle(message);
    }

}
