package com.sankuai.shangou.seashop.pay.core.service;

import com.sankuai.shangou.seashop.pay.core.dto.adapay.AdaPaymentResultDto;
import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import com.sankuai.shangou.seashop.pay.thrift.core.request.OrderPayQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
public interface PayOrderService {

    /**
     * 条件查询单个支付信息
     *
     * @param request
     * @return
     */
    OrderPayResp getOne(OrderPayQueryReq request);

    /**
     * 查询 pay_order_pay表
     * @param orderId
     * @return
     */
    OrderPayResp queryOrderPayResp(String orderId);

    /**
     * 查询未支付的订单
     *
     * @return
     */
    List<OrderPay> getUnPaidList();

    /**
     * 根据order表更新pay_order_pay状态
     */
    void updateUnPaid(List<String> updateOrderIdList, Integer status);

    /**
     * 查询未支付的订单
     *
     * @param startTime
     * @param endTime
     * @return
     */
//    List<OrderPay> getUnPaidListByTime(Date startTime, Date endTime);

    /**
     * 更新并发送同步支付结果
     * flag true发送MQ，false 不发送MQ
     * @param adaPaymentResultDto
     */
    void updateAndSendSyncPayment(AdaPaymentResultDto adaPaymentResultDto, boolean flag);
}
