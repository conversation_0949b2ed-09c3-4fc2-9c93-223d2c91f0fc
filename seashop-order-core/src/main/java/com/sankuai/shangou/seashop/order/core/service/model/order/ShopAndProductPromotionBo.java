package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Getter;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * 店铺以及商品满足的营销信息
 * <AUTHOR>
 */
@Getter
@Setter
public class ShopAndProductPromotionBo {

    /**
     * 店铺营销，可能满足多个营销
     */
    private List<PromotionBo> shopPromotionList;
    /**
     * 店铺下多个商品的营销,以SKU为key
     */
    private Map<String/*skuId*/, ProductPromotionBo> productPromotionMap;

}
