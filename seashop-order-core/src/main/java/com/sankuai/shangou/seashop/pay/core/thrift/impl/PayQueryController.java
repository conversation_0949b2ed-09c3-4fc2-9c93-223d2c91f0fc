package com.sankuai.shangou.seashop.pay.core.thrift.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.pay.core.service.PayOrderService;
import com.sankuai.shangou.seashop.pay.core.service.PayService;
import com.sankuai.shangou.seashop.pay.core.service.ReverseOrderService;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.BillDownloadReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.OrderPayQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.ReverseOrderQueryReq;
import com.sankuai.shangou.seashop.pay.thrift.core.response.BillDownloadResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.ReverseOrderResp;
import com.sankuai.shangou.seashop.pay.thrift.core.service.PayQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: lhx
 * @date: 2023/11/22/022
 * @description:
 */
@RestController
@RequestMapping("/pay")
public class PayQueryController implements PayQueryFeign {

    @Resource
    private PayOrderService payOrderService;
    @Resource
    private ReverseOrderService reverseOrderService;
    @Resource
    private PayService payService;

    @PostMapping(value = "/queryOrderPayOne", consumes = "application/json")
    @Override
    public ResultDto<OrderPayResp> queryOrderPayOne(@RequestBody OrderPayQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryOrderPayOne", request, req -> {
            req.checkParameter();
            return payOrderService.getOne(req);
        });
    }

    @PostMapping(value = "/queryReverseOrderOne", consumes = "application/json")
    @Override
    public ResultDto<ReverseOrderResp> queryReverseOrderOne(@RequestBody ReverseOrderQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryReverseOrderOne", request, req -> {
            req.checkParameter();
            return reverseOrderService.getOne(req);
        });
    }

    @PostMapping(value = "/billDownload", consumes = "application/json")
    @Override
    public ResultDto<BillDownloadResp> billDownload(@RequestBody BillDownloadReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("billDownload", request, req -> {
            req.checkParameter();
            if (PaymentChannelEnum.ADAPAY.getCode().equals(req.getPaymentChannel())) {
                return payService.billDownload(req);
            } else {
                return null;
            }
        });
    }

    @PostMapping(value = "/queryCompletePay", consumes = "application/json")
    @Override
    public ResultDto<Map<String, OrderPayResp>> queryCompletePay(@RequestBody List<String> batchNos) throws TException {
        if(CollectionUtil.isEmpty(batchNos)){
            return ResultDto.newWithData(new HashMap<>());
        }
        return ThriftResponseHelper.responseInvoke("queryCompletePay", batchNos, req -> {
            return payService.queryCompletePay(req);
        });
    }

    @Override
    public ResultDto<Map<String, OrderPayResp>> queryPayOrderNoOutTransId(List<String> batchNos) throws TException {
        if(CollectionUtil.isEmpty(batchNos)){
            return ResultDto.newWithData(new HashMap<>());
        }
        return ThriftResponseHelper.responseInvoke("queryPayOrderPayMap", batchNos, req -> {
            return payService.queryPayOrderPayMap(req);
        });
    }
}
