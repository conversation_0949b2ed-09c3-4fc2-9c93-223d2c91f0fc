package com.sankuai.shangou.seashop.order.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.core.service.OrderPayRecordService;
import com.sankuai.shangou.seashop.order.thrift.core.OrderPayRecordFeign;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayRecordResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/5 15:45
 */
@Slf4j
@RestController
@RequestMapping("/orderPayRecord")
public class OrderPayRecordController implements OrderPayRecordFeign {

    @Resource
    private OrderPayRecordService orderPayRecordService;

    @PostMapping(value = "/queryOrderPayRecordList", consumes = "application/json")
    @Override
    public ResultDto<List<OrderPayRecordResp>> queryOrderPayRecordList(@RequestBody List<String> batchNoList) throws TException {
        return ThriftResponseHelper.responseInvoke("queryOrderPayRecordList", batchNoList, req ->
                orderPayRecordService.queryOrderPayRecordList(req));
    }

    @PostMapping(value = "/queryOrderPayRecordByOrderIds", consumes = "application/json")
    @Override
    public ResultDto<List<OrderPayRecordResp>> queryOrderPayRecordByOrderIds(@RequestBody List<String> orderIds) throws TException {
        return ThriftResponseHelper.responseInvoke("queryOrderPayRecordByOrderIds", orderIds, req ->
                orderPayRecordService.queryOrderPayRecordByOrderIds(req));
    }
}
