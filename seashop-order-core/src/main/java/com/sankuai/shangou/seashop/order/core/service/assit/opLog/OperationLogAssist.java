package com.sankuai.shangou.seashop.order.core.service.assit.opLog;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ReflectUtil;
import com.sankuai.shangou.seashop.base.boot.dto.BaseOperationLogDto;
import com.sankuai.shangou.seashop.base.boot.dto.ChangeFiled;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.log.producer.LogMafkaProducer;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderWayBill;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 无法通过aop处理的操作日志记录
 * <AUTHOR>
 */
@Service
@Slf4j
public class OperationLogAssist {

    @Resource
    private LogMafkaProducer logMafkaProducer;

    public void log(ExaminModelEnum logModule, ExaProEnum opType, String actionName, String opContent, BaseParamReq req, String userName) {
        try {
            BaseOperationLogDto logDto = new BaseOperationLogDto();
            logDto.setModuleId(logModule.getValue());
            logDto.setModuleName(logModule.getName());
            logDto.setOperationType(opType.getValue());
            logDto.setOperationName(opType.getName());
            logDto.setOperationTime(new Date());
            logDto.setOperationUserId(req.getOperationUserId());
            logDto.setActionName(actionName);
            logDto.setShopId(req.getOperationShopId());
            logDto.setOperationContent(opContent);
            logDto.setOperationUserName(userName);
            logMafkaProducer.sendMessage(logDto);
        } catch (Exception e) {
            // 不影响主流程
            log.error("记录操作日志失败", e);
        }
    }

    public void log(ExaminModelEnum logModule, ExaProEnum opType, String actionName, String opContent, Long userId, Long shopId, String userName) {
        try {
            BaseOperationLogDto logDto = new BaseOperationLogDto();
            logDto.setModuleId(logModule.getValue());
            logDto.setModuleName(logModule.getName());
            logDto.setOperationType(opType.getValue());
            logDto.setOperationName(opType.getName());
            logDto.setOperationTime(new Date());
            logDto.setOperationUserId(userId);
            logDto.setActionName(actionName);
            logDto.setShopId(shopId);
            logDto.setOperationContent(opContent);
            logDto.setOperationUserName(userName);
            logMafkaProducer.sendMessage(logDto);
        } catch (Exception e) {
            // 不影响主流程
            log.error("记录操作日志失败", e);
        }
    }

    public List<ChangeFiled> buildChangeList(Object beforeObj, Object updatedObj, List<ChangeFieldDesc> changeFieldDescList) {
        return changeFieldDescList.stream()
                .map(f -> {
                    ChangeFiled changeFiled = new ChangeFiled();
                    changeFiled.setFieldId(f.getFieldName());
                    changeFiled.setFieldName(f.getFieldDesc());
                    Object beforeV = ReflectUtil.getFieldValue(beforeObj, f.getFieldName());
                    if (beforeV == null) {
                        changeFiled.setBefore("");
                    } else {
                        if (beforeV instanceof Date) {
                            changeFiled.setBefore(DateUtil.format((Date) beforeV, CommonConst.DATE_TIME_PATTERN_DEFAULT));
                        } else {
                            changeFiled.setBefore(String.valueOf(beforeV));
                        }
                    }

                    Object afterV = ReflectUtil.getFieldValue(updatedObj, f.getFieldName());
                    if (afterV == null) {
                        changeFiled.setAfter("");
                    } else {
                        if (afterV instanceof Date) {
                            changeFiled.setAfter(DateUtil.format((Date) afterV, CommonConst.DATE_TIME_PATTERN_DEFAULT));
                        } else {
                            changeFiled.setAfter(String.valueOf(afterV));
                        }
                    }
                    return changeFiled;
                })
                .collect(Collectors.toList());
    }

    public List<ChangeFiled> buildChangeListForUpdateExpress(List<OrderWayBill> beforeObjList, List<OrderWayBill> updatedObjList,
                                                             ChangeFieldDesc changeFieldDesc, ChangeFieldDesc mainFieldDesc, String mainFieldValue) {
        ChangeFiled mainChangeField = new ChangeFiled();
        mainChangeField.setFieldId(mainFieldDesc.getFieldName());
        mainChangeField.setFieldName(mainFieldDesc.getFieldDesc());
        mainChangeField.setBefore(mainFieldValue);
        mainChangeField.setAfter(mainFieldValue);

        ChangeFiled changeField = new ChangeFiled();
        changeField.setFieldId(changeFieldDesc.getFieldName());
        changeField.setFieldName(changeFieldDesc.getFieldDesc());
        if (CollUtil.isNotEmpty(beforeObjList)) {
            changeField.setBefore(JsonUtil.toJsonString(beforeObjList));
        } else {
            changeField.setBefore("");
        }
        if (CollUtil.isNotEmpty(updatedObjList)) {
            changeField.setAfter(JsonUtil.toJsonString(updatedObjList));
        } else {
            changeField.setAfter("");
        }

        return Arrays.asList(mainChangeField, changeField);
    }

}
