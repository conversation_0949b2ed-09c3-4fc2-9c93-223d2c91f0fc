package com.sankuai.shangou.seashop.order.core.service.model.pay;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * PC端预支付时的订单信息
 * <AUTHOR>
 */
@Getter
@Setter
public class PcPrePayOrderInfoBo {

    /**
     * 订单ID
     */
    private List<String> orderIdList;
    /**
     * 订单总金额
     */
    private BigDecimal orderAmount;
    /**
     * 剩余支付时间
     */
    private Integer remainPayHour;
    /**
     * 剩余支付时间(秒) 下单时间 + 支付超时时间 - 当前时间
     */
    private Long remainPayTime;

}
