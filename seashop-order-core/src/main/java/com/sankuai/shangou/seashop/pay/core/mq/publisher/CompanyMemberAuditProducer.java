package com.sankuai.shangou.seashop.pay.core.mq.publisher;

import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.pay.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/11/24/024
 * @description: 企业账号审核生产者（透传回调，业务端自己处理）
 */
@Slf4j
@Service
public class CompanyMemberAuditProducer {
    /**
     * topic：seashop_corp_member_audit_topic
     * <p>
     * group：seashop_corp_member_audit_consumer
     * <p>
     * 发送端： com.sankuai.sgb2b.seashop.pay
     * <p>
     * 消费端：com.sankuai.sgb2b.seashop.user
     */
//    @MafkaProducer(namespace = MafkaConstant.DEFAULT_NAMESPACE, topic = MafkaConstant.CORP_MEMBER_AUDIT_TOPIC)
//    private IProducerProcessor producerProcessor;
    @Resource
    private DefaultRocketMq defaultRocketMq;

    public void sendMessage(String data) {
        log.info("企业账号审核生产者发送消息-data：{}", data);
        try {
            defaultRocketMq.convertAndSend(MafkaConstant.CORP_MEMBER_AUDIT_TOPIC, data);
//            ProducerResult producerResult = producerProcessor.sendMessage(data);
            log.info("企业账号审核生产者发送消息：成功");
        }
        catch (Exception e) {
            log.error("企业账号审核生产者发送消息失败-data：{}", data, e);
            throw new SystemException("企业账号审核生产者发送消息失败");
        }
    }
}
