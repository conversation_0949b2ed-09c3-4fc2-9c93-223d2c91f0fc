package com.sankuai.shangou.seashop.order.core.service.model.order;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 店铺下的商品营销对象
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class ProductPromotionBo {

    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 商品满足的多个营销信息，初始化，后续使用的使用不判断，直接add
     */
    private List<PromotionBo> promotionList = new ArrayList<>(3);

}
