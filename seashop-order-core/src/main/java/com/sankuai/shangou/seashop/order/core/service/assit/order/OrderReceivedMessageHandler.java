package com.sankuai.shangou.seashop.order.core.service.assit.order;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.service.assit.OrderFinishForFinanceBizAssist;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.event.OrderMessageEventEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 订单确定收货消息处理器
 * <AUTHOR>
 */
@Service
@Slf4j
public class OrderReceivedMessageHandler implements OrderChangeMessageHandler {

    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderFinishForFinanceBizAssist orderFinishForFinanceBizAssist;

    @Override
    public OrderMessageEventEnum getEvent() {
        return OrderMessageEventEnum.COMPLETE_ORDER;
    }

    @Override
    public void handle(OrderMessage message) {
        log.info("【订单状态变更-收货】收到订单收货消息: {}", JsonUtil.toJsonString(message));
        Order order = orderRepository.getByOrderIdForceMaster(message.getOrderId());
        log.info("【订单状态变更-收货】订单信息: {}", JsonUtil.toJsonString(order));
        if (order == null) {
            log.warn("【订单状态变更-发货】订单不存在, orderId: {}", message.getOrderId());
            return;
        }
        // 订单完成，更新财务相关数据
        orderFinishForFinanceBizAssist.finishOrder(order, order.getFinishDate());
    }
}
