package com.sankuai.shangou.seashop.pay.core.assist.pay;

import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.PayResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundConfirmResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundNotifyResult;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundParam;
import com.sankuai.shangou.seashop.pay.core.assist.pay.model.RefundResult;

/**
 * <AUTHOR>
 * @date 2024/09/02 9:24
 */
public interface PayHandler {

    /**
     * 支付
     *
     * @param payParam 支付参数
     * @return 支付结果
     */
    PayResult pay(PayParam payParam);

    /**
     * 支付回调
     *
     * @param payData 支付数据
     */
    PayNotifyResult notifyPay(String payData);

    /**
     * 支付确认
     *
     * @param confirmParam 支付确认参数
     * @return 支付确认结果
     */
    PayConfirmResult payConfirm(PayConfirmParam confirmParam);

    /**
     * 退款
     *
     * @param refundParam 退款参数
     * @return 退款结果
     */
    RefundResult refund(RefundParam refundParam);

    /**
     * 退款确认
     *
     * @param refundConfirmParam 退款确认参数
     * @return 退款确认结果
     */
    RefundConfirmResult refundConfirm(RefundConfirmParam refundConfirmParam);

    /**
     * 退款回调
     *
     * @param refundData 退款数据
     */
    RefundNotifyResult notifyRefund(String refundData);


}
