<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.starter</groupId>
        <artifactId>hishop-parent</artifactId>
        <version>2.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <name>himall-order</name>
    <groupId>com.hishop.himall</groupId>
    <artifactId>himall-order</artifactId>
    <version>1.0.3-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <himall.order.version>1.0.3-SNAPSHOT</himall.order.version>
        <himall.order.api.version>1.0.3-SNAPSHOT</himall.order.api.version>
        <himall.base.version>1.0.3-SNAPSHOT</himall.base.version>
        <himall.base.api.version>1.0.3-SNAPSHOT</himall.base.api.version>
        <himall.trade.version>1.0.3-SNAPSHOT</himall.trade.version>
        <erp.version>1.0.1-SNAPSHOT</erp.version>
        <himall.report.version>1.0.3-SNAPSHOT</himall.report.version>
<!--        <histore.starter.version>1.0.1-SNAPSHOT</histore.starter.version>-->
        <histore.starter.version>2.0.1-SNAPSHOT</histore.starter.version>
        <weixin-java-pay.version>4.4.0</weixin-java-pay.version>
        <alipay-easysdk.version>2.2.3</alipay-easysdk.version>
        <okhttp3.version>4.9.3</okhttp3.version>
        <gson.version>2.8.9</gson.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.code.gson</groupId>
                <artifactId>gson</artifactId>
                <version>${gson.version}</version>
            </dependency>
            <!-- order -->
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-order-api</artifactId>
                <version>${himall.order.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-order-common</artifactId>
                <version>${himall.order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-order-dao</artifactId>
                <version>${himall.order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-order-core</artifactId>
                <version>${himall.order.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-order-finance</artifactId>
                <version>${himall.order.version}</version>
            </dependency>
            <!-- end -->
            <!-- 项目其他服务依赖-->
            <dependency>
                <groupId>com.hishop.starter</groupId>
                <artifactId>hishop-rocketmq-starter</artifactId>
                <version>${histore.starter.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-api</artifactId>
                <version>${himall.base.api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-boot</artifactId>
                <version>${himall.base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-base-util</artifactId>
                <version>${himall.base.version}</version>
            </dependency>
            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-trade-api</artifactId>
                <version>${himall.trade.version}</version>
            </dependency>

            <dependency>
                <groupId>com.huifu.adapay.core</groupId>
                <artifactId>adapay-core-sdk</artifactId>
                <version>${adapay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.huifu.adapay</groupId>
                <artifactId>adapay-java-sdk</artifactId>
                <version>${adapay.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.binarywang</groupId>
                <artifactId>weixin-java-pay</artifactId>
                <version>${weixin-java-pay.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alipay.sdk</groupId>
                <artifactId>alipay-easysdk</artifactId>
                <version>${alipay-easysdk.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>himall-report-api</artifactId>
                <version>${himall.report.version}</version>
            </dependency>

            <dependency>
                <groupId>com.hishop.himall</groupId>
                <artifactId>seashop-erp-api</artifactId>
                <version>${erp.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <modules>
        <module>seashop-order-api</module>
        <module>seashop-order-common</module>
        <module>seashop-order-dao</module>
        <module>seashop-order-server</module>
        <module>seashop-order-core</module>
        <module>seashop-order-finance</module>
    </modules>

    <repositories>
        <repository>
            <id>central</id>
            <url>http://nexus.35hiw.com/repository/maven-central/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>huaweicloud</id>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>maven-public</id>
            <url>https://nexus.35hiw.com/repository/maven-public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>
        <repository>
            <id>aliyunmaven</id>
            <url>https://maven.aliyun.com/repository/public</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

    </repositories>

</project>