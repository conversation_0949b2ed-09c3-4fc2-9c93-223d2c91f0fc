2025-07-25 17:36:27.634 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-25 17:36:27.948 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [55] -| Starting BaseApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 39108 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-25 17:36:27.949 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-25 17:36:27.949 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [638] -| The following 1 profile is active: "develop_local"
2025-07-25 17:36:28.795 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-25 17:36:28.795 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-25 17:36:31.349 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-25 17:36:35.267 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-25 17:36:35.339 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-25 17:36:35.758 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-25 17:36:37.142 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-25 17:36:37.148 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-25 17:36:37.433 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-25 17:36:37.433 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-25 17:36:37.433 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-25 17:36:37.434 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-25 17:36:43.433 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-25 17:36:48.103 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv 124.71.221.178:9876
2025-07-25 17:36:56.337 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-25 17:36:57.682 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-25 17:36:59.323 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-25 17:36:59.627 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:37:01.468 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:37:06.978 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:37:06.978 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 17:37:08.250 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-25 17:37:10.012 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-25 17:37:11.330 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx06ef85741e60fe3d,wxAppSecret:d373b5835d638c62bf8b4aab4fbf493e
2025-07-25 17:37:16.667 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-25 17:37:17.086 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-25 17:37:17.467 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-25 17:37:17.544 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:37:19.287 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:37:24.474 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:37:24.475 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 17:37:24.486 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:37:26.231 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:37:31.922 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:37:31.923 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 17:37:31.933 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:37:33.796 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:37:39.717 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:37:39.717 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 17:37:39.725 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:37:41.477 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:37:46.764 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:37:46.765 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 17:37:47.787 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:37:49.428 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:37:54.584 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:37:54.584 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-25 17:37:54.612 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:37:56.503 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:38:01.773 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:38:01.773 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-25 17:38:01.787 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:38:03.689 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:38:09.271 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:38:09.271 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-25 17:38:09.280 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:38:10.898 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:38:16.201 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:38:16.203 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-25 17:38:16.211 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:38:17.956 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:38:23.164 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:38:23.164 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-25 17:38:23.174 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:38:25.550 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:38:31.311 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:38:31.311 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-25 17:38:31.322 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:38:33.360 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:38:40.065 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:38:40.065 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-25 17:38:40.074 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:38:41.843 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:38:47.396 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:38:47.396 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-25 17:38:47.405 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:38:49.093 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:38:54.445 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:38:54.445 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-25 17:38:54.455 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:38:54.566 |-WARN  [Thread-8][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-25 17:38:54.566 |-WARN  [Thread-8][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-25 17:38:54.566 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-25 17:38:54.569 |-WARN  [Thread-5][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-25 17:39:33.538 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-25 17:39:33.687 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [55] -| Starting BaseApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 12840 (E:\work\himallWork\himall-base\seashop-base-server\target\classes started by Admin in E:\work\himallWork)
2025-07-25 17:39:33.688 |-DEBUG [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-25 17:39:33.688 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [638] -| The following 1 profile is active: "develop_local"
2025-07-25 17:39:34.157 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-base.yml, group=1.0.0] success
2025-07-25 17:39:34.157 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-25 17:39:36.508 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OBS bean:defaultStorageClient success
2025-07-25 17:39:40.123 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-25 17:39:40.186 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-25 17:39:40.578 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-25 17:39:42.117 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-25 17:39:42.121 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-25 17:39:42.416 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-25 17:39:42.416 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-25 17:39:42.417 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-25 17:39:42.417 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-25 17:39:47.455 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-25 17:39:51.978 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-base) init on namesrv 124.71.221.178:9876
2025-07-25 17:39:58.882 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-25 17:40:00.154 |-INFO  [redisson-netty-2-4][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-25 17:40:02.054 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-25 17:40:02.314 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:40:04.002 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:40:09.323 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_operation_log_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_operation_log_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:40:09.325 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:logMafkaConsumer, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-25 17:40:10.460 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-25 17:40:12.123 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.core.config.WxMpConfiguration [39] -| init wxa config, wxAppKey:wx4aef4e4571ebda50,wxAppSecret:e5d71dc282e7e310f97a065a0756b629
2025-07-25 17:40:13.255 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.shop.config.WxaConfiguration [65] -| init wxa config, wxAppKey:wx06ef85741e60fe3d,wxAppSecret:d373b5835d638c62bf8b4aab4fbf493e
2025-07-25 17:40:18.239 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-25 17:40:18.642 |-INFO  [main][] -  com.sankuai.shangou.seashop.user.account.service.register.RegisterStrategyContext [29] -| ImportHandlerContainer init success
2025-07-25 17:40:19.002 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.security.context.LoginStrategyContext [29] -| ImportHandlerContainer init success
2025-07-25 17:40:19.079 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:40:20.720 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:40:26.006 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_register_msg_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='com.sankuai.sjst.ecom.epassportweb.signup.done_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:40:26.008 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberCreateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-25 17:40:26.020 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:40:27.818 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:40:33.133 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_ep_account_modify_msg_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='com.sankuai.sjst.ecom.epassportadminservice.bizacct.modify_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:40:33.134 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:memberUpdateListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-25 17:40:33.141 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:40:35.749 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:40:41.157 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:40:41.158 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-25 17:40:41.166 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:40:42.931 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:40:48.384 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:40:48.384 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-25 17:40:49.449 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:40:51.074 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:40:56.326 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_corp_member_audit_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_corp_member_audit_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:40:56.326 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:adaAuditListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-25 17:40:56.346 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:40:58.157 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:41:03.588 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_business_category_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_business_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:41:03.588 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:businessCategoryListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-25 17:41:03.598 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:41:05.378 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:41:10.550 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_product_category_change_user_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_product_category_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:41:10.550 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:categoryChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-25 17:41:10.558 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:41:12.231 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:41:17.781 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_custom_form_change_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_custom_form_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:41:17.781 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:customerFromChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-25 17:41:17.789 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:41:19.490 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:41:24.686 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_exclusive_member_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_user_exclusive_member_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:41:24.686 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:exclusiveMemberDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-25 17:41:24.696 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:41:26.552 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:41:32.691 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_comment_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:41:32.691 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-25 17:41:32.716 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:41:34.579 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:41:39.924 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_shop_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:41:39.924 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderFinishDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-25 17:41:39.934 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:41:41.593 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:41:46.750 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_product_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_product_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:41:46.750 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-25 17:41:46.758 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:41:48.498 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:41:53.689 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_shop_brand_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_shop_brand_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:41:53.689 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopBrandDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-25 17:41:53.701 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-25 17:41:55.442 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-25 17:42:00.868 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_user_shop_dts_consumer_develop_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_user_shop_dts_topic_develop_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-25 17:42:00.869 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:shopDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-25 17:42:06.937 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-25 17:42:06.954 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-25 17:42:08.352 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:12840, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-25 17:42:09.367 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=433, lastTimeStamp=1753436528358}] - instanceId:[InstanceId{instanceId=*******:12840, stable=false}] - machineBit:[20] @ namespace:[himall-base].
2025-07-25 17:42:09.540 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-base.__share__].
2025-07-25 17:42:09.543 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-25 17:42:09.543 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-base.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-25 17:42:09.544 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-base.__share__] jobSize:[0].
2025-07-25 17:42:12.422 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-25 17:42:16.151 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:syncRegionData, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1ba4c55c[class com.sankuai.shangou.seashop.base.core.task.RegionTask#syncRegionData]
2025-07-25 17:42:16.152 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportShop, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@222136e9[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportShop]
2025-07-25 17:42:16.152 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportRegion, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2f8288b2[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportRegion]
2025-07-25 17:42:16.152 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportMember, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@626d2e9b[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportMember]
2025-07-25 17:42:16.152 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportFavoriteProduct, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5f5e99af[class com.sankuai.shangou.seashop.base.core.task.ReportTask#reportFavoriteProduct]
2025-07-25 17:42:16.187 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshShopEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@74e37ad3[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#refreshShopEs]
2025-07-25 17:42:16.187 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:initShopForm, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4f1fabe5[class com.sankuai.shangou.seashop.user.shop.task.ShopEsTask#initShopForm]
2025-07-25 17:42:16.187 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:checkShopInfo, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@448d4d6b[class com.sankuai.shangou.seashop.user.shop.task.ShopInfoTask#checkShopInfo]
2025-07-25 17:42:22.022 |-INFO  [Thread-354][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-25 17:42:22.215 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-25 17:42:22.311 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-25 17:42:22.355 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-25 17:42:22.566 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-25 17:42:22.819 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [61] -| Started BaseApplication in 175.139 seconds (JVM running for 178.268)
2025-07-25 17:42:23.220 |-INFO  [task-15][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-25 17:42:23.220 |-INFO  [task-15][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-25 17:42:23.809 |-INFO  [task-15][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-base *******:8082 register finished
2025-07-25 17:42:25.307 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.BaseApplication [32] -| 服务启动成功！
2025-07-25 17:42:25.412 |-INFO  [task-13][b624554a3101b1e8] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-25 17:42:25.413 |-INFO  [task-13][b624554a3101b1e8] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-base.yml, group=1.0.0
2025-07-25 17:42:27.961 |-INFO  [RMI TCP Connection(8)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-25 17:42:31.106 |-WARN  [RMI TCP Connection(5)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
