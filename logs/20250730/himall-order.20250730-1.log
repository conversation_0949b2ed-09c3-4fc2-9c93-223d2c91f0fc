2025-07-30 11:55:48.200 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 11:55:48.359 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [55] -| Starting OrderApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 30824 (E:\work\himallWork\himall-order\seashop-order-server\target\classes started by Admin in E:\work\himallWork)
2025-07-30 11:55:48.360 |-DEBUG [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-30 11:55:48.360 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-07-30 11:55:48.823 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-order.yml, group=1.0.0] success
2025-07-30 11:55:48.823 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-30 11:55:53.581 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-30 11:55:53.584 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-30 11:55:53.681 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-07-30 11:55:55.395 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderCommentExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderCommentExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 11:55:55.396 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 11:55:55.398 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'pendingSettlementOrderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PendingSettlementOrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 11:55:55.398 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'platAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PlatAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 11:55:55.398 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'shopAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.ShopAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 11:55:58.190 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-30 11:55:58.270 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-30 11:55:58.801 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-30 11:55:59.611 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-30 11:55:59.617 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-30 11:55:59.939 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-30 11:55:59.940 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-30 11:55:59.940 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-30 11:55:59.940 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-30 11:56:05.981 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:56:09.784 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:56:18.892 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:56:18.892 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderDelayCheckListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-30 11:56:18.898 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:56:21.455 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:56:29.139 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:56:29.139 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderFailureListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-30 11:56:30.411 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-30 11:56:31.018 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-30 11:56:32.823 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-30 11:56:34.046 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-30 11:56:34.911 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:56:37.320 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:56:45.103 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:56:45.103 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-30 11:56:45.109 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:56:47.925 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:56:57.921 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:56:57.922 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-30 11:56:58.500 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-30 11:57:03.732 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-order) init on namesrv 124.71.221.178:9876
2025-07-30 11:57:11.589 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:14.159 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:57:21.953 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:57:21.953 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderPayNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-30 11:57:22.535 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:25.084 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:57:32.793 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:57:32.793 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-30 11:57:32.919 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:35.560 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:57:42.992 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:57:42.992 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-30 11:57:42.999 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:45.070 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:57:53.067 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:57:53.068 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-30 11:57:53.249 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:57:55.846 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:03.536 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:03.536 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-30 11:58:03.545 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:58:06.183 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:13.516 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:13.516 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeForRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-30 11:58:13.582 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:58:15.966 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:24.787 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:24.787 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-30 11:58:25.114 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.core.statemachine.config.OrderStateMachineConfiguration [127] -| @startuml
UNDER_RECEIVE --> FINISHED : CONFIRM_RECEIVE
UNDER_RECEIVE --> UNDER_RECEIVE : DELAY_RECEIVE
UNDER_PAY --> CLOSED : CANCEL_ORDER
UNDER_PAY --> CLOSED : CLOSE
UNDER_PAY --> PAYING : INITIATE_PAY
UNDER_SEND --> UNDER_RECEIVE : DELIVERY
PAYING --> UNDER_PAY : CANCEL_PAY
PAYING --> UNDER_SEND : PAY_NOTIFY
@enduml
2025-07-30 11:58:25.224 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:58:27.408 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:36.238 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:36.239 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-30 11:58:36.386 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:58:39.803 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:46.975 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:46.975 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-30 11:58:46.981 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:58:49.177 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:58:55.911 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:58:55.912 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:refundNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-30 11:58:59.355 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-07-30 11:58:59.356 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-30 11:58:59.447 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:59:01.542 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class java.lang.String
2025-07-30 11:59:07.468 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:59:07.468 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderReverseExceptionListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-30 11:59:08.126 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-30 11:59:08.249 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:59:10.131 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:59:15.794 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:59:15.794 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositPayStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_16
2025-07-30 11:59:15.828 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 11:59:17.997 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 11:59:23.605 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 11:59:23.605 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositReverseStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_17
2025-07-30 11:59:29.535 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-30 11:59:29.552 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-30 11:59:30.949 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:30824, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-30 11:59:31.874 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=364, lastTimeStamp=1753847970958}] - instanceId:[InstanceId{instanceId=*******:30824, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-30 11:59:32.079 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-order.__share__].
2025-07-30 11:59:32.081 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-30 11:59:32.082 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-30 11:59:32.082 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-order.__share__] jobSize:[0].
2025-07-30 11:59:35.160 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-30 11:59:38.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshProductCommentEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3354dd3[class com.sankuai.shangou.seashop.order.core.task.CommentTask#refreshProductCommentEs]
2025-07-30 11:59:38.608 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCommentTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4b0f8153[class com.sankuai.shangou.seashop.order.core.task.CommentTask#autoComment]
2025-07-30 11:59:38.608 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:ExecuteMqErrorDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6eba7c33[class com.sankuai.shangou.seashop.order.core.task.MqErrorDataTask#executeMqErrorDataTask]
2025-07-30 11:59:38.608 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildRefundEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@397c6a7b[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildRefundEsDataTask]
2025-07-30 11:59:38.608 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildOrderEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@100ee352[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildOrderEsDataTask]
2025-07-30 11:59:38.608 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCloseTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@27bcfd19[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoClose]
2025-07-30 11:59:38.608 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindWaitDeliveryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1ada5639[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindWaitDelivery]
2025-07-30 11:59:38.608 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoFinishTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@402fd8ab[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoFinish]
2025-07-30 11:59:38.608 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindOrderNotPayTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@275cb78f[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindNotPay]
2025-07-30 11:59:38.608 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderItem, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6748d248[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderItem]
2025-07-30 11:59:38.609 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrder, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@2f6b2b06[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrder]
2025-07-30 11:59:38.609 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderBill, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@11bf44c0[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderBill]
2025-07-30 11:59:38.609 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundRecordStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@539d69b7[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundRecordStatusTask]
2025-07-30 11:59:38.609 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@72663b1e[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundStatusTask]
2025-07-30 11:59:38.609 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderRefund, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4230d4f[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderRefund]
2025-07-30 11:59:38.609 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundCloseWhenDeliveryExpireTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6f51c04f[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundCloseWhenDeliveryExpire]
2025-07-30 11:59:38.609 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoConfirmArrival, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@57b9ccc3[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoConfirmArrival]
2025-07-30 11:59:38.609 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoAuditTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@14c4b3e1[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoAudit]
2025-07-30 11:59:38.611 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:unpaidOrderPayStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@8119d43[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#unpaidOrderPayStatusQueryTask]
2025-07-30 11:59:38.611 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:payReverseStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@69a377b6[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#payReverseStatusQueryTask]
2025-07-30 11:59:38.645 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@70e56195[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask$$EnhancerBySpringCGLIB$$8b215dbf#orderSettlement]
2025-07-30 11:59:38.645 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSplitting, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@410f8fd3[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask$$EnhancerBySpringCGLIB$$8b215dbf#orderSplitting]
2025-07-30 11:59:44.216 |-ERROR [Thread-469][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 11:59:44.405 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-30 11:59:44.488 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-30 11:59:44.524 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-30 11:59:44.727 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-30 11:59:45.034 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 11:59:45.034 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 11:59:45.780 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-order *******:8083 register finished
2025-07-30 11:59:46.984 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [61] -| Started OrderApplication in 247.534 seconds (JVM running for 251.717)
2025-07-30 11:59:46.992 |-INFO  [main][] -  adaPayInitConfig [35] -| 汇付支付初始化开始
2025-07-30 11:59:48.169 |-INFO  [main][] -  adaPayInitConfig [38] -| 汇付配置信息:AdaPayConfigModel(izOpen=true, appId=app_39b69a1c-d9ad-4e30-bb29-d80833b86885, apiKey=api_live_54054387-f98b-4f13-ad20-b6ebcbd07da4, apiMockKey=api_test_000fc218-bd56-4ea8-9609-8439687ad2bc, rsaPrivateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIYS3BfoZ2+g0eY21IZwvllVccKEcSwzrJUwLnWJ8vFvSM5mU5aPkMkhO1m5GR2aDJEz16hBuEK1ezzP73R5Sh6Qne2kXHWgCO6vKRWJ/PeAm8xBKddHKO3Xdg9MW1tVQoHA2pPV1ru2KOXT93YWOIKUlWTFpnelpgrIZLjZME2XAgMBAAECgYATy30LWpjK9meHIdlG8CZqch8VpRBAgnCcpjx1xiREWTXao2j79b5es7VbjeSTZkcsuQbCJNHbp4fGdrzX6YBzw/xR2gdnE/A4lRfcgvJ62wFBIbq4MZYevBmBX5Ng2AuxbdEbJ87Kd+zpFFHb6x0aB4aJjUSx5EpQaoiRuhBRIQJBAMWcgi44hvJQNnx9fVZZWs0ZyUDgf9hTVUEcJ+LyaUeNOXcsrP13NJ98PMDtSyhiVEsAmwugZDc2uWRbcbYUyBMCQQCtsE5m+UEH6ALg2lQUVyfuxORWb2OYXSeA3JBJN9RvzBo2WfQnzJO3Vs6BV2qcvqgUcKv+b4DwLPHODPmZyxztAkBvADwL1IrQ4AfLI/5cm7KqlPp8a97EWAMCoNsy2vISVBzceYbulaBEmdfSkzhthdZNjxiIjl7cuOuomMkl+0RrAkEAgEpEbszWmtdlIN5C0k9aAIPPwIRABS9xWT4RGPOy5uzTw6eHrsntpbLpjyGZbrNohMiAUcvcagpYhICS8GTVNQJBAMWPBEHATBX/Ka/5d/WC7SmIZ96d24NDJQDwlo0ufbZAp791HGoujq/w+SAySoQfA3VnVQyIkaqQlKou7Q5MS5A=, rsaProductKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwN6xgd6Ad8v2hIIsQVnbt8a3JituR8o4Tc3B5WlcFR55bz4OMqrG/356Ur3cPbc2Fe8ArNd/0gZbC9q56Eb16JTkVNA/fye4SXznWxdyBPR7+guuJZHc/VW2fKH2lfZ2P3Tt0QkKZZoawYOGSMdIvO+WqK44updyax0ikK6JlNQIDAQAB, deviceId=hishop1905, izDebug=true, prodMode=true)
2025-07-30 11:59:48.169 |-INFO  [main][] -  adaPayInitConfig [39] -| 汇付当前回调地址为:https://himall.cce.35hiw.com/himall-pay/payCallBack/adaPayCallback
2025-07-30 11:59:48.177 |-INFO  [main][] -  adaPayInitConfig [54] -| 汇付支付初始化完成
2025-07-30 11:59:49.917 |-INFO  [RMI TCP Connection(15)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 11:59:50.933 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-30 11:59:50.934 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-order.yml, group=1.0.0
2025-07-30 11:59:50.936 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [37] -| 服务启动成功！
2025-07-30 11:59:52.778 |-WARN  [RMI TCP Connection(13)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-30 14:39:11.690 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-30 14:39:11.690 |-INFO  [Thread-440][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-30 14:39:11.690 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-30 14:39:11.690 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 14:39:11.692 |-INFO  [Thread-440][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 14:39:11.692 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 14:39:11.692 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.692 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-30 14:39:11.692 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.692 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.692 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.692 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.692 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.693 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.693 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.693 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.693 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.693 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.693 |-INFO  [Thread-441][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 14:39:11.700 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-30 14:39:11.736 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=364, lastTimeStamp=1753857551736}] instanceId:[InstanceId{instanceId=*******:30824, stable=false}] @ namespace:[himall-order].
2025-07-30 14:39:11.768 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 14:53:02.855 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 14:53:03.050 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [55] -| Starting OrderApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 43832 (E:\work\himallWork\himall-order\seashop-order-server\target\classes started by Admin in E:\work\himallWork)
2025-07-30 14:53:03.051 |-DEBUG [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-30 14:53:03.052 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-07-30 14:53:03.592 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-order.yml, group=1.0.0] success
2025-07-30 14:53:03.593 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-30 14:53:09.152 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-30 14:53:09.152 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-30 14:53:09.258 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-07-30 14:53:11.380 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderCommentExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderCommentExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 14:53:11.382 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 14:53:11.383 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'pendingSettlementOrderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PendingSettlementOrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 14:53:11.384 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'platAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PlatAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 14:53:11.384 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'shopAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.ShopAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 14:53:14.334 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-30 14:53:14.442 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-30 14:53:15.004 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-30 14:53:15.891 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-30 14:53:15.895 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-30 14:53:16.150 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-30 14:53:16.150 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-30 14:53:16.150 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-30 14:53:16.151 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-30 14:53:22.523 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:53:26.144 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:53:39.114 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:53:39.114 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderDelayCheckListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-30 14:53:39.121 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:53:41.706 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:53:51.987 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:53:51.988 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderFailureListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-30 14:53:53.434 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-30 14:53:54.115 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-30 14:53:55.876 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-30 14:53:57.466 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-30 14:53:58.493 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:54:00.923 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:54:09.032 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:54:09.033 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-30 14:54:09.040 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:54:11.698 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:54:21.418 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:54:21.418 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-30 14:54:21.963 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-30 14:54:30.597 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-order) init on namesrv 124.71.221.178:9876
2025-07-30 14:54:40.478 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:54:43.770 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:54:52.782 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:54:52.782 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderPayNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-30 14:54:53.527 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:54:58.972 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:08.041 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:08.041 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-30 14:55:08.179 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:55:10.964 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:19.060 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:19.060 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-30 14:55:19.068 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:55:21.685 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:29.895 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:29.895 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-30 14:55:30.088 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:55:32.820 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:41.007 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:41.007 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-30 14:55:41.016 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:55:43.365 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:55:51.901 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:55:51.901 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeForRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-30 14:55:51.958 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:55:54.744 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:56:02.565 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:02.566 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-30 14:56:02.875 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.core.statemachine.config.OrderStateMachineConfiguration [127] -| @startuml
PAYING --> UNDER_SEND : PAY_NOTIFY
PAYING --> UNDER_PAY : CANCEL_PAY
UNDER_RECEIVE --> FINISHED : CONFIRM_RECEIVE
UNDER_RECEIVE --> UNDER_RECEIVE : DELAY_RECEIVE
UNDER_SEND --> UNDER_RECEIVE : DELIVERY
UNDER_PAY --> PAYING : INITIATE_PAY
UNDER_PAY --> CLOSED : CLOSE
UNDER_PAY --> CLOSED : CANCEL_ORDER
@enduml
2025-07-30 14:56:02.987 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:05.562 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:56:13.311 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:13.311 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-30 14:56:13.484 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:18.049 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:56:25.738 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:25.738 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-30 14:56:25.744 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:28.105 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:56:34.702 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:34.702 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:refundNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-30 14:56:37.818 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-07-30 14:56:37.818 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-30 14:56:37.918 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:40.733 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class java.lang.String
2025-07-30 14:56:47.416 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:47.416 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderReverseExceptionListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-30 14:56:48.021 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-30 14:56:48.141 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:50.438 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:56:57.918 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:56:57.919 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositPayStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_16
2025-07-30 14:56:57.965 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 14:56:59.963 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 14:57:05.519 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 14:57:05.519 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositReverseStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_17
2025-07-30 14:57:11.302 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-30 14:57:11.318 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-30 14:57:12.691 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:43832, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-30 14:57:13.554 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=364, lastTimeStamp=1753858632698}] - instanceId:[InstanceId{instanceId=*******:43832, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-30 14:57:13.737 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-order.__share__].
2025-07-30 14:57:13.740 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-30 14:57:13.740 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-30 14:57:13.740 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-order.__share__] jobSize:[0].
2025-07-30 14:57:16.823 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-30 14:57:20.622 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCommentTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@39fa3fc5[class com.sankuai.shangou.seashop.order.core.task.CommentTask#autoComment]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshProductCommentEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5d35e169[class com.sankuai.shangou.seashop.order.core.task.CommentTask#refreshProductCommentEs]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:ExecuteMqErrorDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@23b40e2a[class com.sankuai.shangou.seashop.order.core.task.MqErrorDataTask#executeMqErrorDataTask]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildOrderEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@57f28893[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildOrderEsDataTask]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildRefundEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3f1e91bf[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildRefundEsDataTask]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCloseTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4d52119c[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoClose]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoFinishTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4e949342[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoFinish]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindOrderNotPayTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@de12c13[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindNotPay]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderBill, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4639d779[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderBill]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderItem, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1bbaf0d3[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderItem]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrder, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@71de7e65[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrder]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindWaitDeliveryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@755b45f9[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindWaitDelivery]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7d51d634[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundStatusTask]
2025-07-30 14:57:20.623 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderRefund, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@31a19d25[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderRefund]
2025-07-30 14:57:20.624 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundRecordStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@330ee022[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundRecordStatusTask]
2025-07-30 14:57:20.624 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoConfirmArrival, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1cd50f37[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoConfirmArrival]
2025-07-30 14:57:20.624 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundCloseWhenDeliveryExpireTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6f469aef[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundCloseWhenDeliveryExpire]
2025-07-30 14:57:20.624 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoAuditTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@52336a97[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoAudit]
2025-07-30 14:57:20.625 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:payReverseStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@16f89f03[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#payReverseStatusQueryTask]
2025-07-30 14:57:20.627 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:unpaidOrderPayStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@22e0965c[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#unpaidOrderPayStatusQueryTask]
2025-07-30 14:57:20.657 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@792ae10d[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask$$EnhancerBySpringCGLIB$$a541170#orderSettlement]
2025-07-30 14:57:20.657 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSplitting, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@30845b6d[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask$$EnhancerBySpringCGLIB$$a541170#orderSplitting]
2025-07-30 14:57:25.759 |-ERROR [Thread-527][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 14:57:25.932 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-30 14:57:26.007 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-30 14:57:26.041 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-30 14:57:26.219 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-30 14:57:26.503 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 14:57:26.503 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 14:57:26.865 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-order *******:8083 register finished
2025-07-30 14:57:27.972 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [61] -| Started OrderApplication in 274.598 seconds (JVM running for 279.78)
2025-07-30 14:57:27.980 |-INFO  [main][] -  adaPayInitConfig [35] -| 汇付支付初始化开始
2025-07-30 14:57:29.067 |-INFO  [main][] -  adaPayInitConfig [38] -| 汇付配置信息:AdaPayConfigModel(izOpen=true, appId=app_39b69a1c-d9ad-4e30-bb29-d80833b86885, apiKey=api_live_54054387-f98b-4f13-ad20-b6ebcbd07da4, apiMockKey=api_test_000fc218-bd56-4ea8-9609-8439687ad2bc, rsaPrivateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIYS3BfoZ2+g0eY21IZwvllVccKEcSwzrJUwLnWJ8vFvSM5mU5aPkMkhO1m5GR2aDJEz16hBuEK1ezzP73R5Sh6Qne2kXHWgCO6vKRWJ/PeAm8xBKddHKO3Xdg9MW1tVQoHA2pPV1ru2KOXT93YWOIKUlWTFpnelpgrIZLjZME2XAgMBAAECgYATy30LWpjK9meHIdlG8CZqch8VpRBAgnCcpjx1xiREWTXao2j79b5es7VbjeSTZkcsuQbCJNHbp4fGdrzX6YBzw/xR2gdnE/A4lRfcgvJ62wFBIbq4MZYevBmBX5Ng2AuxbdEbJ87Kd+zpFFHb6x0aB4aJjUSx5EpQaoiRuhBRIQJBAMWcgi44hvJQNnx9fVZZWs0ZyUDgf9hTVUEcJ+LyaUeNOXcsrP13NJ98PMDtSyhiVEsAmwugZDc2uWRbcbYUyBMCQQCtsE5m+UEH6ALg2lQUVyfuxORWb2OYXSeA3JBJN9RvzBo2WfQnzJO3Vs6BV2qcvqgUcKv+b4DwLPHODPmZyxztAkBvADwL1IrQ4AfLI/5cm7KqlPp8a97EWAMCoNsy2vISVBzceYbulaBEmdfSkzhthdZNjxiIjl7cuOuomMkl+0RrAkEAgEpEbszWmtdlIN5C0k9aAIPPwIRABS9xWT4RGPOy5uzTw6eHrsntpbLpjyGZbrNohMiAUcvcagpYhICS8GTVNQJBAMWPBEHATBX/Ka/5d/WC7SmIZ96d24NDJQDwlo0ufbZAp791HGoujq/w+SAySoQfA3VnVQyIkaqQlKou7Q5MS5A=, rsaProductKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwN6xgd6Ad8v2hIIsQVnbt8a3JituR8o4Tc3B5WlcFR55bz4OMqrG/356Ur3cPbc2Fe8ArNd/0gZbC9q56Eb16JTkVNA/fye4SXznWxdyBPR7+guuJZHc/VW2fKH2lfZ2P3Tt0QkKZZoawYOGSMdIvO+WqK44updyax0ikK6JlNQIDAQAB, deviceId=hishop1905, izDebug=true, prodMode=true)
2025-07-30 14:57:29.067 |-INFO  [main][] -  adaPayInitConfig [39] -| 汇付当前回调地址为:https://himall.cce.35hiw.com/himall-pay/payCallBack/adaPayCallback
2025-07-30 14:57:29.076 |-INFO  [main][] -  adaPayInitConfig [54] -| 汇付支付初始化完成
2025-07-30 14:57:29.841 |-INFO  [RMI TCP Connection(8)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 14:57:31.372 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-30 14:57:31.374 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-order.yml, group=1.0.0
2025-07-30 14:57:31.377 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [37] -| 服务启动成功！
2025-07-30 14:57:33.005 |-WARN  [RMI TCP Connection(10)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
2025-07-30 15:28:00.481 |-WARN  [Thread-8][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-30 15:28:00.510 |-INFO  [Thread-498][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-30 15:28:00.510 |-INFO  [Thread-498][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 15:28:00.518 |-WARN  [Thread-11][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-30 15:28:00.518 |-WARN  [Thread-11][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-30 15:28:00.530 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-30 15:28:00.530 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 15:28:00.530 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.530 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.530 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.530 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.530 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.531 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.531 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.531 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.531 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.531 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.531 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.531 |-INFO  [Thread-499][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-30 15:28:00.541 |-WARN  [Thread-8][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-30 15:28:00.547 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=364, lastTimeStamp=1753860480547}] instanceId:[InstanceId{instanceId=*******:43832, stable=false}] @ namespace:[himall-order].
2025-07-30 15:28:00.583 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 15:28:37.257 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow [259] -| stopping server: Undertow - 2.2.28.Final
2025-07-30 15:28:37.303 |-INFO  [SpringApplicationShutdownHook][] -  io.undertow.servlet [389] -| Destroying Spring FrameworkServlet 'dispatcherServlet'
2025-07-30 15:28:37.494 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-30 15:28:37.499 |-INFO  [SpringApplicationShutdownHook][] -  com.xxl.job.core.server.EmbedServer [117] -| >>>>>>>>>>> xxl-job remoting server destroy success.
2025-07-30 15:28:37.499 |-INFO  [xxl-job, executor TriggerCallbackThread][] -  com.xxl.job.core.thread.TriggerCallbackThread [98] -| >>>>>>>>>>> xxl-job, executor callback thread destroy.
2025-07-30 15:28:37.501 |-INFO  [Thread-526][] -  com.xxl.job.core.thread.TriggerCallbackThread [128] -| >>>>>>>>>>> xxl-job, executor retry callback thread destroy.
2025-07-30 15:28:37.501 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [95] -| De-registering from Nacos Server now...
2025-07-30 15:28:37.537 |-INFO  [SpringApplicationShutdownHook][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [115] -| De-registration finished.
2025-07-30 15:28:37.586 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.586 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.586 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.587 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.588 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.588 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.588 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.588 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.588 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.588 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.588 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.588 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:37.588 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.616 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.617 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.620 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.621 |-INFO  [SpringApplicationShutdownHook][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [322] -| container destroyed, DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 15:28:40.664 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [215] -| dynamic-datasource start closing ....
2025-07-30 15:28:40.697 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| slave - Shutdown initiated...
2025-07-30 15:28:40.722 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| slave - Shutdown completed.
2025-07-30 15:28:40.722 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [slave] success,
2025-07-30 15:28:40.722 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [350] -| master - Shutdown initiated...
2025-07-30 15:28:40.726 |-INFO  [SpringApplicationShutdownHook][] -  com.zaxxer.hikari.HikariDataSource [352] -| master - Shutdown completed.
2025-07-30 15:28:40.726 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.destroyer.DefaultDataSourceDestroyer [98] -| dynamic-datasource close the datasource named [master] success,
2025-07-30 15:28:40.726 |-INFO  [SpringApplicationShutdownHook][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [219] -| dynamic-datasource all closed success,bye
2025-07-30 18:17:03.399 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-30 18:17:03.542 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [55] -| Starting OrderApplication using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 43972 (E:\work\himallWork\himall-order\seashop-order-server\target\classes started by Admin in E:\work\himallWork)
2025-07-30 18:17:03.542 |-DEBUG [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-30 18:17:03.543 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [638] -| The following 1 profile is active: "chengpei_local"
2025-07-30 18:17:03.970 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-order.yml, group=1.0.0] success
2025-07-30 18:17:03.971 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-30 18:17:09.358 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-30 18:17:09.359 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-30 18:17:09.459 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-07-30 18:17:11.183 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderCommentExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderCommentExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 18:17:11.183 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 18:17:11.185 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'pendingSettlementOrderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PendingSettlementOrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 18:17:11.185 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'platAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PlatAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 18:17:11.185 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'shopAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.ShopAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-30 18:17:13.697 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-30 18:17:13.777 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-30 18:17:14.304 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-30 18:17:15.960 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-30 18:17:15.966 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-30 18:17:16.261 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-30 18:17:16.261 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-30 18:17:16.261 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-30 18:17:16.261 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-30 18:17:22.357 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:17:26.396 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:17:38.816 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:17:38.816 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderDelayCheckListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-30 18:17:38.821 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:17:41.697 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:17:50.010 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:17:50.010 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderFailureListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-30 18:17:51.444 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-30 18:17:52.425 |-INFO  [redisson-netty-2-18][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-30 18:17:53.781 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-30 18:17:55.143 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-30 18:17:56.141 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:18:00.064 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:18:09.242 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:18:09.243 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-30 18:18:09.250 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:18:11.676 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:18:19.159 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:18:19.159 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-30 18:18:19.731 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-30 18:18:24.860 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-order) init on namesrv 124.71.221.178:9876
2025-07-30 18:18:32.683 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:18:35.228 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:18:42.863 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:18:42.863 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderPayNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-30 18:18:43.553 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:18:46.024 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:18:53.296 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:18:53.296 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-30 18:18:53.430 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:18:55.921 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:19:04.980 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:19:04.980 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-30 18:19:04.988 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:19:08.399 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:19:17.289 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:19:17.290 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-30 18:19:17.510 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:19:20.057 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:19:26.609 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:19:26.609 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-30 18:19:26.617 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:19:28.663 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:19:35.532 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:19:35.533 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeForRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-30 18:19:35.586 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:19:37.647 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:19:44.079 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:19:44.079 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-30 18:19:44.424 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.core.statemachine.config.OrderStateMachineConfiguration [127] -| @startuml
UNDER_SEND --> UNDER_RECEIVE : DELIVERY
PAYING --> UNDER_SEND : PAY_NOTIFY
PAYING --> UNDER_PAY : CANCEL_PAY
UNDER_PAY --> CLOSED : CANCEL_ORDER
UNDER_PAY --> CLOSED : CLOSE
UNDER_PAY --> PAYING : INITIATE_PAY
UNDER_RECEIVE --> UNDER_RECEIVE : DELAY_RECEIVE
UNDER_RECEIVE --> FINISHED : CONFIRM_RECEIVE
@enduml
2025-07-30 18:19:44.532 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:19:46.692 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:19:53.149 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:19:53.149 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-30 18:19:53.287 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:19:55.539 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:20:02.534 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:20:02.534 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-30 18:20:02.541 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:20:04.896 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:20:11.267 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:20:11.267 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:refundNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-30 18:20:14.729 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52] failed: connect timed out
2025-07-30 18:20:14.729 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-30 18:20:14.841 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:20:16.972 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class java.lang.String
2025-07-30 18:20:23.233 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:20:23.233 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderReverseExceptionListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-30 18:20:23.944 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-30 18:20:24.077 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:20:26.201 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:20:32.298 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:20:32.298 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositPayStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_16
2025-07-30 18:20:32.345 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-30 18:20:34.519 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-30 18:20:41.610 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-30 18:20:41.611 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositReverseStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_17
2025-07-30 18:20:50.976 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-30 18:20:51.000 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-30 18:20:52.758 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:43972, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-30 18:20:54.239 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=364, lastTimeStamp=1753870852773}] - instanceId:[InstanceId{instanceId=*******:43972, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-30 18:20:54.622 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-order.__share__].
2025-07-30 18:20:54.626 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-30 18:20:54.626 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-30 18:20:54.626 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-order.__share__] jobSize:[0].
2025-07-30 18:21:01.671 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-30 18:21:12.605 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCommentTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@47d0ee5b[class com.sankuai.shangou.seashop.order.core.task.CommentTask#autoComment]
2025-07-30 18:21:12.605 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshProductCommentEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5262253e[class com.sankuai.shangou.seashop.order.core.task.CommentTask#refreshProductCommentEs]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:ExecuteMqErrorDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7622892a[class com.sankuai.shangou.seashop.order.core.task.MqErrorDataTask#executeMqErrorDataTask]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildOrderEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@f0f78a4[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildOrderEsDataTask]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildRefundEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4f6dfd28[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildRefundEsDataTask]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCloseTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@c656a2e[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoClose]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderItem, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6eecd951[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderItem]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderBill, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1ad25670[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderBill]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindOrderNotPayTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3a766cbf[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindNotPay]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoFinishTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1d6545c5[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoFinish]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrder, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@15cb3169[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrder]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindWaitDeliveryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1d229476[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindWaitDelivery]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@14b810f9[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundStatusTask]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundRecordStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@23c424c6[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundRecordStatusTask]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderRefund, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6f4809ae[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderRefund]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundCloseWhenDeliveryExpireTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@66c0a115[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundCloseWhenDeliveryExpire]
2025-07-30 18:21:12.607 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoConfirmArrival, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@32ab4aec[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoConfirmArrival]
2025-07-30 18:21:12.608 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoAuditTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@30cbba6b[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoAudit]
2025-07-30 18:21:12.611 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:unpaidOrderPayStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1fe1d0a5[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#unpaidOrderPayStatusQueryTask]
2025-07-30 18:21:12.611 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:payReverseStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7c4a0245[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#payReverseStatusQueryTask]
2025-07-30 18:21:12.715 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSplitting, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@14decb0a[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask$$EnhancerBySpringCGLIB$$8a6929c5#orderSplitting]
2025-07-30 18:21:12.715 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@792b2318[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask$$EnhancerBySpringCGLIB$$8a6929c5#orderSettlement]
2025-07-30 18:21:23.045 |-ERROR [Thread-493][] -  com.xxl.job.core.server.EmbedServer [93] -| >>>>>>>>>>> xxl-job remoting server error.
java.net.BindException: Address already in use: bind
	at sun.nio.ch.Net.bind0(Native Method) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:433) ~[?:1.8.0_121]
	at sun.nio.ch.Net.bind(Net.java:425) ~[?:1.8.0_121]
	at sun.nio.ch.ServerSocketChannelImpl.bind(ServerSocketChannelImpl.java:223) ~[?:1.8.0_121]
	at io.netty.channel.socket.nio.NioServerSocketChannel.doBind(NioServerSocketChannel.java:141) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel$AbstractUnsafe.bind(AbstractChannel.java:562) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.bind(DefaultChannelPipeline.java:1334) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeBind(AbstractChannelHandlerContext.java:600) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannelHandlerContext.bind(AbstractChannelHandlerContext.java:579) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.DefaultChannelPipeline.bind(DefaultChannelPipeline.java:973) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.AbstractChannel.bind(AbstractChannel.java:260) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.bootstrap.AbstractBootstrap$2.run(AbstractBootstrap.java:380) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.runTask(AbstractEventExecutor.java:173) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute$$$capture(AbstractEventExecutor.java:166) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.AbstractEventExecutor.safeExecute(AbstractEventExecutor.java) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor.runAllTasks(SingleThreadEventExecutor.java:470) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:569) ~[netty-transport-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.107.Final.jar:4.1.107.Final]
	at java.lang.Thread.run(Thread.java:745) [?:1.8.0_121]
2025-07-30 18:21:23.232 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-30 18:21:23.317 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-30 18:21:23.357 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-30 18:21:23.548 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-30 18:21:23.848 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-30 18:21:23.848 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-30 18:21:24.213 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-order *******:8083 register finished
2025-07-30 18:21:25.428 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [61] -| Started OrderApplication in 271.111 seconds (JVM running for 276.903)
2025-07-30 18:21:25.436 |-INFO  [main][] -  adaPayInitConfig [35] -| 汇付支付初始化开始
2025-07-30 18:21:26.776 |-INFO  [main][] -  adaPayInitConfig [38] -| 汇付配置信息:AdaPayConfigModel(izOpen=true, appId=app_39b69a1c-d9ad-4e30-bb29-d80833b86885, apiKey=api_live_54054387-f98b-4f13-ad20-b6ebcbd07da4, apiMockKey=api_test_000fc218-bd56-4ea8-9609-8439687ad2bc, rsaPrivateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIYS3BfoZ2+g0eY21IZwvllVccKEcSwzrJUwLnWJ8vFvSM5mU5aPkMkhO1m5GR2aDJEz16hBuEK1ezzP73R5Sh6Qne2kXHWgCO6vKRWJ/PeAm8xBKddHKO3Xdg9MW1tVQoHA2pPV1ru2KOXT93YWOIKUlWTFpnelpgrIZLjZME2XAgMBAAECgYATy30LWpjK9meHIdlG8CZqch8VpRBAgnCcpjx1xiREWTXao2j79b5es7VbjeSTZkcsuQbCJNHbp4fGdrzX6YBzw/xR2gdnE/A4lRfcgvJ62wFBIbq4MZYevBmBX5Ng2AuxbdEbJ87Kd+zpFFHb6x0aB4aJjUSx5EpQaoiRuhBRIQJBAMWcgi44hvJQNnx9fVZZWs0ZyUDgf9hTVUEcJ+LyaUeNOXcsrP13NJ98PMDtSyhiVEsAmwugZDc2uWRbcbYUyBMCQQCtsE5m+UEH6ALg2lQUVyfuxORWb2OYXSeA3JBJN9RvzBo2WfQnzJO3Vs6BV2qcvqgUcKv+b4DwLPHODPmZyxztAkBvADwL1IrQ4AfLI/5cm7KqlPp8a97EWAMCoNsy2vISVBzceYbulaBEmdfSkzhthdZNjxiIjl7cuOuomMkl+0RrAkEAgEpEbszWmtdlIN5C0k9aAIPPwIRABS9xWT4RGPOy5uzTw6eHrsntpbLpjyGZbrNohMiAUcvcagpYhICS8GTVNQJBAMWPBEHATBX/Ka/5d/WC7SmIZ96d24NDJQDwlo0ufbZAp791HGoujq/w+SAySoQfA3VnVQyIkaqQlKou7Q5MS5A=, rsaProductKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwN6xgd6Ad8v2hIIsQVnbt8a3JituR8o4Tc3B5WlcFR55bz4OMqrG/356Ur3cPbc2Fe8ArNd/0gZbC9q56Eb16JTkVNA/fye4SXznWxdyBPR7+guuJZHc/VW2fKH2lfZ2P3Tt0QkKZZoawYOGSMdIvO+WqK44updyax0ikK6JlNQIDAQAB, deviceId=hishop1905, izDebug=true, prodMode=true)
2025-07-30 18:21:26.776 |-INFO  [main][] -  adaPayInitConfig [39] -| 汇付当前回调地址为:https://himall.cce.35hiw.com/himall-pay/payCallBack/adaPayCallback
2025-07-30 18:21:26.786 |-INFO  [main][] -  adaPayInitConfig [54] -| 汇付支付初始化完成
2025-07-30 18:21:27.877 |-INFO  [RMI TCP Connection(23)-*******][] -  io.undertow.servlet [389] -| Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-07-30 18:21:28.765 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-30 18:21:28.768 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-order.yml, group=1.0.0
2025-07-30 18:21:28.773 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.OrderApplication [37] -| 服务启动成功！
2025-07-30 18:21:32.354 |-WARN  [RMI TCP Connection(25)-*******][] -  org.elasticsearch.client.RestClient [72] -| request [GET http://124.71.221.117:9200/_cluster/health/] returned 1 warnings: [299 Elasticsearch-7.17.18-8682172c2130b9a411b1bd5ff37c9792367de6b0 "Elasticsearch built-in security features are not enabled. Without authentication, your cluster could be accessible to anyone. See https://www.elastic.co/guide/en/elasticsearch/reference/7.17/security-minimal-setup.html to enable security."]
