2025-07-17 18:54:03.552 |-INFO  [background-preinit][] -  org.hibernate.validator.internal.util.Version [21] -| HV000001: Hibernate Validator 6.2.3.Final
2025-07-17 18:54:03.709 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [55] -| Starting StartApp using Java 1.8.0_121 on DESKTOP-GBHOUCA with PID 32620 (E:\work\himallWork\himall-order\seashop-order-server\target\classes started by Admin in E:\work\himallWork)
2025-07-17 18:54:03.709 |-DEBUG [main][] -  com.sankuai.shangou.seashop.order.StartApp [56] -| Running with Spring Boot v2.7.18, Spring v5.3.33
2025-07-17 18:54:03.709 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [638] -| The following 1 profile is active: "chengpei_local"
2025-07-17 18:54:04.139 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-order.yml, group=1.0.0] success
2025-07-17 18:54:04.139 |-INFO  [main][] -  com.alibaba.cloud.nacos.configdata.NacosConfigDataLoader [255] -| [Nacos Config] Load config[dataId=himall-common.yml, group=1.0.0] success
2025-07-17 18:54:08.899 |-WARN  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-17 18:54:08.902 |-INFO  [ForkJoinPool.commonPool-worker-9][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-17 18:54:09.039 |-INFO  [main][] -  com.hishop.starter.storage.config.StorageClientRegistrar [67] -| register storage client type:OSS bean:defaultStorageClient success
2025-07-17 18:54:11.300 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderCommentExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderCommentExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-17 18:54:11.300 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'orderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-17 18:54:11.303 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'pendingSettlementOrderExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PendingSettlementOrderExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-17 18:54:11.303 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'platAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PlatAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-17 18:54:11.303 |-WARN  [main][] -  org.mybatis.spring.mapper.ClassPathMapperScanner [44] -| Skipping MapperFactoryBean with name 'shopAccountExtMapper' and 'com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.ShopAccountExtMapper' mapperInterface. Bean already defined with the same name!
2025-07-17 18:54:13.941 |-WARN  [main][] -  io.undertow.websockets.jsr [68] -| UT026010: Buffer pool was not set on WebSocketDeploymentInfo, the default pool will be used
2025-07-17 18:54:14.023 |-INFO  [main][] -  io.undertow.servlet [389] -| Initializing Spring embedded WebApplicationContext
2025-07-17 18:54:14.518 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| master - Starting...
2025-07-17 18:54:15.360 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| master - Start completed.
2025-07-17 18:54:15.365 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [80] -| slave - Starting...
2025-07-17 18:54:15.686 |-INFO  [main][] -  com.zaxxer.hikari.HikariDataSource [82] -| slave - Start completed.
2025-07-17 18:54:15.687 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [slave] success
2025-07-17 18:54:15.687 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [158] -| dynamic-datasource - add a datasource named [master] success
2025-07-17 18:54:15.689 |-INFO  [main][] -  com.baomidou.dynamic.datasource.DynamicRoutingDataSource [241] -| dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-07-17 18:54:20.741 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:54:22.890 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:54:30.922 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_check_delay_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_check_delay_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:54:30.922 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderDelayCheckListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_1
2025-07-17 18:54:30.933 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:54:32.549 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:54:37.309 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_failure_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_failure_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:54:37.309 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:createOrderFailureListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_2
2025-07-17 18:54:38.572 |-INFO  [main][] -  org.redisson.Version [43] -| Redisson 3.27.1
2025-07-17 18:54:39.178 |-INFO  [redisson-netty-2-6][] -  org.redisson.connection.ConnectionsHolder [191] -| 1 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-17 18:54:41.019 |-INFO  [redisson-netty-2-19][] -  org.redisson.connection.ConnectionsHolder [191] -| 24 connections initialized for 124.71.221.178/124.71.221.178:6379
2025-07-17 18:54:42.240 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.utils.SquirrelUtil [123] -| 缓存组件服务启动完成
2025-07-17 18:54:42.824 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:54:44.393 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:54:49.025 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:54:49.025 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_3
2025-07-17 18:54:49.032 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:54:50.660 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:54:55.214 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:54:55.215 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_4
2025-07-17 18:54:55.873 |-DEBUG [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [82] -| rocketmq.nameServer = 124.71.221.178:9876
2025-07-17 18:54:58.885 |-INFO  [main][] -  org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration [122] -| a producer (himall-order) init on namesrv 124.71.221.178:9876
2025-07-17 18:55:03.406 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:55:04.861 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:55:09.453 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:55:09.453 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderPayNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_5
2025-07-17 18:55:09.962 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:55:11.448 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:55:16.016 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:55:16.016 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_6
2025-07-17 18:55:16.140 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:55:17.697 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:55:22.708 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_dts_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:55:22.709 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_7
2025-07-17 18:55:22.717 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:55:24.174 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:55:28.651 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:55:28.651 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundEsBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_8
2025-07-17 18:55:28.806 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:55:30.323 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:55:34.850 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_refund_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_refund_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:55:34.850 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderRefundWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_9
2025-07-17 18:55:34.859 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:55:36.350 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:55:40.939 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_for_refund_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:55:40.939 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeForRefundListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_10
2025-07-17 18:55:40.983 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:55:42.451 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:55:47.144 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:55:47.145 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderStatusChangeListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_11
2025-07-17 18:55:47.316 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.core.statemachine.config.OrderStateMachineConfiguration [127] -| @startuml
UNDER_RECEIVE --> UNDER_RECEIVE : DELAY_RECEIVE
UNDER_RECEIVE --> FINISHED : CONFIRM_RECEIVE
PAYING --> UNDER_SEND : PAY_NOTIFY
PAYING --> UNDER_PAY : CANCEL_PAY
UNDER_PAY --> PAYING : INITIATE_PAY
UNDER_PAY --> CLOSED : CLOSE
UNDER_PAY --> CLOSED : CANCEL_ORDER
UNDER_SEND --> UNDER_RECEIVE : DELIVERY
@enduml
2025-07-17 18:55:47.421 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:55:48.882 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:55:53.596 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_wdt_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:55:53.597 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderWdtBuildListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_12
2025-07-17 18:55:53.711 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:55:55.166 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:55:59.979 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_comment_dts_es_build_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_product_comment_dts_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:55:59.979 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:productCommentDtsListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_13
2025-07-17 18:55:59.987 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:56:01.455 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:56:05.974 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:56:05.974 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:refundNotifyListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_14
2025-07-17 18:56:08.436 |-WARN  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.util.HttpUtil [183] -| 【HTTPCLIENT】checkUrlIsValid:https://oss-cn-hangzhou-internal.aliyuncs.com ERROR:Connect to oss-cn-hangzhou-internal.aliyuncs.com:443 [oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.52, oss-cn-hangzhou-internal.aliyuncs.com/100.118.28.43] failed: connect timed out
2025-07-17 18:56:08.436 |-INFO  [ForkJoinPool.commonPool-worker-2][] -  com.hishop.starter.storage.client.OSSClient [88] -| 【STORAGE】OSS 地址:https://oss-cn-hangzhou.aliyuncs.com 内网不可达，使用配置endpoint
2025-07-17 18:56:08.515 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:56:09.990 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class java.lang.String
2025-07-17 18:56:14.725 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_order_reverse_exception_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_exception_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:56:14.726 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:orderReverseExceptionListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_15
2025-07-17 18:56:15.373 |-INFO  [main][] -  com.sankuai.shangou.seashop.base.eimport.ImportHandlerContainer [32] -| ImportHandlerContainer init success
2025-07-17 18:56:15.460 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:56:17.021 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:56:21.666 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_category_pay_status_change_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_pay_status_change_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:56:21.667 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositPayStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_16
2025-07-17 18:56:21.696 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [628] -| Access-key or secret-key not configure in DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}.
2025-07-17 18:56:23.207 |-DEBUG [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [384] -| RocketMQ messageType: class org.apache.rocketmq.common.message.MessageExt
2025-07-17 18:56:27.767 |-INFO  [main][] -  org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer [349] -| running container: DefaultRocketMQListenerContainer{consumerGroup='seashop_cash_deposit_reverse_consumer_chengpei_local', namespace='', nameServer='124.71.221.178:9876', topic='seashop_order_reverse_topic_chengpei_local', consumeMode=CONCURRENTLY, selectorType=TAG, selectorExpression='*', messageModel=CLUSTERING', tlsEnable=false, instanceName=DEFAULT}
2025-07-17 18:56:27.767 |-INFO  [main][] -  org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar [109] -| Register the listener to container, listenerBeanName:cashDepositReverseStatusListener, containerBeanName:org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer_17
2025-07-17 18:56:32.837 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [24] -| HishopXxlJobClientBootAutoConfiguration init...
2025-07-17 18:56:32.852 |-INFO  [main][] -  com.hishop.xxljob.client.boot.HishopXxlJobClientBootAutoConfiguration [43] -| xxl-job-client-boot-starter: get local ip: *******
2025-07-17 18:56:34.065 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [74] -| Distribute Remote instanceId:[InstanceId{instanceId=*******:32620, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-17 18:56:34.854 |-INFO  [main][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [97] -| Distribute Remote machineState:[MachineState{machineId=346, lastTimeStamp=1752749794072}] - instanceId:[InstanceId{instanceId=*******:32620, stable=false}] - machineBit:[20] @ namespace:[himall-order].
2025-07-17 18:56:35.012 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [92] -| Submit jobId:[himall-order.__share__].
2025-07-17 18:56:35.015 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [107] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1].
2025-07-17 18:56:35.015 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [112] -| Submit jobId:[himall-order.__share__] is bound to thread:[DefaultPrefetchWorker-1] start.
2025-07-17 18:56:35.015 |-INFO  [main][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [56] -| Submit [himall-order.__share__] jobSize:[0].
2025-07-17 18:56:37.794 |-WARN  [main][] -  org.springframework.boot.autoconfigure.freemarker.FreeMarkerAutoConfiguration [65] -| Cannot find template location(s): [classpath:/templates/] (please add some templates, check your FreeMarker configuration, or set spring.freemarker.check-template-location=false)
2025-07-17 18:56:40.947 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refreshProductCommentEs, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@44b4b54[class com.sankuai.shangou.seashop.order.core.task.CommentTask#refreshProductCommentEs]
2025-07-17 18:56:40.947 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCommentTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6280829f[class com.sankuai.shangou.seashop.order.core.task.CommentTask#autoComment]
2025-07-17 18:56:40.947 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:ExecuteMqErrorDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7b3556d7[class com.sankuai.shangou.seashop.order.core.task.MqErrorDataTask#executeMqErrorDataTask]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildOrderEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@387051e3[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildOrderEsDataTask]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:BuildRefundEsDataTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@66bfbc52[class com.sankuai.shangou.seashop.order.core.task.OrderAndRefundEsDataBuildTask#buildRefundEsDataTask]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoCloseTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4d5e60[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoClose]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindOrderNotPayTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@511f3350[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindNotPay]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderAutoFinishTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6bda9fd9[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#autoFinish]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:remindWaitDeliveryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@701cf3dc[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#remindWaitDelivery]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundRecordStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1ac81de1[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundRecordStatusTask]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderRefundStatusTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3265285b[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#orderRefundStatusTask]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderRefund, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@d7e5d99[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderRefund]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderBill, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1a6bcdf9[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderBill]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrderItem, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4016ec40[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrderItem]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:reportOrder, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5b2a23e9[class com.sankuai.shangou.seashop.order.core.task.OrderCraneTask#reportOrder]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundCloseWhenDeliveryExpireTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@24473f80[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundCloseWhenDeliveryExpire]
2025-07-17 18:56:40.948 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoConfirmArrival, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@60873af4[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoConfirmArrival]
2025-07-17 18:56:40.949 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:refundSellerAutoAuditTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1784be48[class com.sankuai.shangou.seashop.order.core.task.OrderRefundTask#refundSellerAutoAudit]
2025-07-17 18:56:40.950 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:unpaidOrderPayStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@1d3bf23a[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#unpaidOrderPayStatusQueryTask]
2025-07-17 18:56:40.950 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:payReverseStatusQueryTask, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@9d7522[class com.sankuai.shangou.seashop.pay.core.task.PayCraneTask#payReverseStatusQueryTask]
2025-07-17 18:56:40.976 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSplitting, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5914bf5f[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask#orderSplitting]
2025-07-17 18:56:40.976 |-INFO  [main][] -  com.xxl.job.core.executor.XxlJobExecutor [183] -| >>>>>>>>>>> xxl-job register jobhandler success, name:orderSettlement, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@6bd02ccb[class com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask#orderSettlement]
2025-07-17 18:56:45.827 |-INFO  [Thread-17][] -  com.xxl.job.core.server.EmbedServer [82] -| >>>>>>>>>>> xxl-job remoting server start success, nettype = class com.xxl.job.core.server.EmbedServer, port = 7200
2025-07-17 18:56:45.987 |-INFO  [main][] -  io.undertow [120] -| starting server: Undertow - 2.2.28.Final
2025-07-17 18:56:46.073 |-INFO  [main][] -  org.xnio [95] -| XNIO version 3.8.7.Final
2025-07-17 18:56:46.107 |-INFO  [main][] -  org.xnio.nio [58] -| XNIO NIO Implementation Version 3.8.7.Final
2025-07-17 18:56:46.307 |-INFO  [main][] -  org.jboss.threads [52] -| JBoss Threads version 3.1.0.Final
2025-07-17 18:56:46.651 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-07-17 18:56:46.651 |-INFO  [main][] -  com.alibaba.nacos.plugin.auth.spi.client.ClientAuthPluginManager [56] -| [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-07-17 18:56:47.019 |-INFO  [main][] -  com.alibaba.cloud.nacos.registry.NacosServiceRegistry [76] -| nacos registry, 1.0.0 himall-order *******:8083 register finished
2025-07-17 18:56:48.041 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [61] -| Started StartApp in 170.79 seconds (JVM running for 174.555)
2025-07-17 18:56:48.053 |-INFO  [main][] -  adaPayInitConfig [32] -| 汇付支付初始化开始
2025-07-17 18:56:49.305 |-INFO  [main][] -  adaPayInitConfig [35] -| 汇付配置信息:AdaPayConfigModel(izOpen=null, appId=app_39b69a1c-d9ad-4e30-bb29-d80833b86885, apiKey=api_live_54054387-f98b-4f13-ad20-b6ebcbd07da4, apiMockKey=api_test_000fc218-bd56-4ea8-9609-8439687ad2bc, rsaPrivateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIYS3BfoZ2+g0eY21IZwvllVccKEcSwzrJUwLnWJ8vFvSM5mU5aPkMkhO1m5GR2aDJEz16hBuEK1ezzP73R5Sh6Qne2kXHWgCO6vKRWJ/PeAm8xBKddHKO3Xdg9MW1tVQoHA2pPV1ru2KOXT93YWOIKUlWTFpnelpgrIZLjZME2XAgMBAAECgYATy30LWpjK9meHIdlG8CZqch8VpRBAgnCcpjx1xiREWTXao2j79b5es7VbjeSTZkcsuQbCJNHbp4fGdrzX6YBzw/xR2gdnE/A4lRfcgvJ62wFBIbq4MZYevBmBX5Ng2AuxbdEbJ87Kd+zpFFHb6x0aB4aJjUSx5EpQaoiRuhBRIQJBAMWcgi44hvJQNnx9fVZZWs0ZyUDgf9hTVUEcJ+LyaUeNOXcsrP13NJ98PMDtSyhiVEsAmwugZDc2uWRbcbYUyBMCQQCtsE5m+UEH6ALg2lQUVyfuxORWb2OYXSeA3JBJN9RvzBo2WfQnzJO3Vs6BV2qcvqgUcKv+b4DwLPHODPmZyxztAkBvADwL1IrQ4AfLI/5cm7KqlPp8a97EWAMCoNsy2vISVBzceYbulaBEmdfSkzhthdZNjxiIjl7cuOuomMkl+0RrAkEAgEpEbszWmtdlIN5C0k9aAIPPwIRABS9xWT4RGPOy5uzTw6eHrsntpbLpjyGZbrNohMiAUcvcagpYhICS8GTVNQJBAMWPBEHATBX/Ka/5d/WC7SmIZ96d24NDJQDwlo0ufbZAp791HGoujq/w+SAySoQfA3VnVQyIkaqQlKou7Q5MS5A=, rsaProductKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCwN6xgd6Ad8v2hIIsQVnbt8a3JituR8o4Tc3B5WlcFR55bz4OMqrG/356Ur3cPbc2Fe8ArNd/0gZbC9q56Eb16JTkVNA/fye4SXznWxdyBPR7+guuJZHc/VW2fKH2lfZ2P3Tt0QkKZZoawYOGSMdIvO+WqK44updyax0ikK6JlNQIDAQAB, deviceId=hishop1905, izDebug=true, prodMode=true)
2025-07-17 18:56:49.321 |-INFO  [main][] -  adaPayInitConfig [50] -| 汇付支付初始化完成
2025-07-17 18:56:51.313 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-common.yml, group=1.0.0
2025-07-17 18:56:51.314 |-INFO  [main][] -  com.alibaba.cloud.nacos.refresh.NacosContextRefresher [131] -| [Nacos Config] Listening config: dataId=himall-order.yml, group=1.0.0
2025-07-17 18:56:51.316 |-INFO  [main][] -  com.sankuai.shangou.seashop.order.StartApp [37] -| 服务启动成功！
