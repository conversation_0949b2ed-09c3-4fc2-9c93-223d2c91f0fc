2025-07-31 11:21:54.903 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.spring.redis.SpringRedisMachineIdDistributor [108] -| Revert Remote [MachineState{machineId=349, lastTimeStamp=1753932114902}] instanceId:[InstanceId{instanceId=2.0.0.1:38544, stable=false}] @ namespace:[himall-trade].
2025-07-31 11:21:54.903 |-INFO  [Thread-399][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-31 11:21:55.098 |-INFO  [Thread-399][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-31 11:21:54.796 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [102] -| [HttpClientBeanHolder] Start destroying common HttpClient
2025-07-31 11:21:54.796 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [134] -| [NotifyCenter] Start destroying Publisher
2025-07-31 11:21:54.796 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [129] -| Close gracefully!
2025-07-31 11:21:55.112 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
2025-07-31 11:21:55.112 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.112 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.112 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.112 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.113 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.113 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.113 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.113 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.113 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.113 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.113 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.113 |-INFO  [Thread-400][] -  me.ahoo.cosid.segment.concurrent.DefaultPrefetchWorker [45] -| Shutdown!
2025-07-31 11:21:55.119 |-WARN  [Thread-10][] -  com.alibaba.nacos.common.notify.NotifyCenter [151] -| [NotifyCenter] Destruction of the end
2025-07-31 11:21:55.145 |-WARN  [Thread-7][] -  com.alibaba.nacos.common.http.HttpClientBeanHolder [111] -| [HttpClientBeanHolder] Destruction of the end
2025-07-31 11:21:55.308 |-INFO  [SpringApplicationShutdownHook][] -  me.ahoo.cosid.segment.concurrent.PrefetchWorkerExecutorService [75] -| Shutdown!
