package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.trade.common.remote.Order2TradeRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteFlashSaleDetailBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.PreviewFlashSaleOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PreviewFlashSaleOrderBuilder extends AbstractFlashSaleBuilder {

    @Resource
    private Order2TradeRemoteService orderRemoteService;

    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        PreviewFlashSaleOrderBuildContext previewOrderBuildContext = (PreviewFlashSaleOrderBuildContext) context;
        Long flashSaleId = previewOrderBuildContext.getFlashSaleId();
        Long productId = previewOrderBuildContext.getProductId();
        String skuId = previewOrderBuildContext.getSkuId();
        Long userId = previewOrderBuildContext.getUserId();
        Long quantity = previewOrderBuildContext.getQuantity();
        // 调用父类公共的构建方法
        return buildFlashSaleShopProduct(context, userId, flashSaleId, productId, skuId, quantity);
    }

    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {
        PreviewFlashSaleOrderBuildContext buildContext = (PreviewFlashSaleOrderBuildContext) context;
        // 预览相关的接口需要设置一些额外的数据
        super.fillExtraOrderPreviewData(context.getUserId(), dataHolder);
        // 计算并设置运费，单独计算运费，因为进入预览订单页如果商品在禁售区域不抛出异常
        calculateAndSetFreight(buildContext.getDefaultShippingAddress(), dataHolder, true);
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_FLASH_SALE;
    }

    @Override
    protected void checkFlashSale(BuildContext context, RemoteFlashSaleDetailBo flashSale) {
        // 预览时不需要额外校验

        /*PreviewFlashSaleOrderBuildContext buildContext = (PreviewFlashSaleOrderBuildContext) context;

        Long userId = buildContext.getUserId();
        Long flashSaleId = buildContext.getFlashSaleId();
        Long quantity = buildContext.getQuantity();
        Long productId = buildContext.getProductId();
        String skuId = buildContext.getSkuId();

        Integer limitType = flashSale.getLimitType();
        Integer totalCount = flashSale.getTotalCount();
        Integer limitCount = flashSale.getLimitCount();
        // 商品维度，只需要校验每个人的限购数量
        if (FlashSaleLimitTypeEnum.PRODUCT.getType().equals(limitType)) {
            // 商品维度已经购买的数量
            Long count = orderRemoteService.countFlashSaleByProduct(userId, flashSaleId, productId);
            if (count != null && count + quantity >= limitCount) {
                throw new BusinessException("超过限时购限购数量");
            }
        }
        // sku维度，校验库存和每个人的限购数量
        else if (FlashSaleLimitTypeEnum.SKU.getType().equals(limitType)) {
            if (totalCount == null || quantity > totalCount) {
                throw new BusinessException("限时购商品已经售罄");
            }
            // sku维度已购买数量
            Long count = orderRemoteService.countFlashSaleBySku(userId, flashSaleId, skuId);
            if (count != null && count + quantity >= limitCount) {
                throw new BusinessException("超过限时购限购数量");
            }
        } else {
            throw new BusinessException("限时购类型错误");
        }*/
    }


    //*******************************************************************





}
