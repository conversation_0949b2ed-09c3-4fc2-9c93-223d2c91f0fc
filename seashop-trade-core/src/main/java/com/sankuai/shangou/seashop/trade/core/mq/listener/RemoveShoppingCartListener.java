package com.sankuai.shangou.seashop.trade.core.mq.listener;

import javax.annotation.Resource;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.trade.core.mq.model.RemoveShoppingCartMessage;
import com.sankuai.shangou.seashop.trade.core.service.ShoppingCartService;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品信息更新监听器
 *
 * <AUTHOR>
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_REMOVE_SHOPPING_CART,
//        group = MafkaConst.GROUP_REMOVE_SHOPPING_CART)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_REMOVE_SHOPPING_CART + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_REMOVE_SHOPPING_CART + "_${spring.profiles.active}")
public class RemoveShoppingCartListener implements RocketMQListener<String> {

    @Resource
    private ShoppingCartService shoppingCartService;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
//        String body = (String) mafkaMessage.getBody();
//        log.info("【mafka消费】【删除购物车】消息内容为: {}", body);
//        if (body == null) {
//            log.error("【mafka消费】【删除购物车】消息内容为空");
//            return ConsumeStatus.CONSUME_SUCCESS;
//        }
//        RemoveShoppingCartMessage messageWrapper = JsonUtil.parseObject(body, RemoveShoppingCartMessage.class);
//        shoppingCartService.removeShoppingCart(messageWrapper.getUserId(), messageWrapper.getSkuIdList());
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(String body) {
        log.info("【mafka消费】【删除购物车】消息内容为: {}", body);
        if (body == null) {
            log.error("【mafka消费】【删除购物车】消息内容为空");
            return;
        }
        RemoveShoppingCartMessage messageWrapper = JsonUtil.parseObject(body, RemoveShoppingCartMessage.class);
        shoppingCartService.removeShoppingCart(messageWrapper.getUserId(), messageWrapper.getSkuIdList());
    }
}
