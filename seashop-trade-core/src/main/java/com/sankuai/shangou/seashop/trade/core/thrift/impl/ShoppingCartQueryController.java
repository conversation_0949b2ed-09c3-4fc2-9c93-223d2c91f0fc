package com.sankuai.shangou.seashop.trade.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.core.service.ShoppingCartService;
import com.sankuai.shangou.seashop.trade.core.service.model.*;
import com.sankuai.shangou.seashop.trade.thrift.core.ShoppingCartQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewBuyNowReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewCollocationOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewFlashSaleOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewOrderResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.UserShoppingCartResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 购物车查询相关的thrift服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/shoppingCart")
public class ShoppingCartQueryController implements ShoppingCartQueryFeign {

    @Resource
    private ShoppingCartService shoppingCartService;

    @GetMapping(value = "/getUserShoppingCartList")
    @Override
    public ResultDto<UserShoppingCartResp> getUserShoppingCartList(@RequestParam Long userId) throws TException {
        log.info("【购物车】获取用户购物车列表, userId={}", userId);
        return ThriftResponseHelper.responseInvoke("getUserShoppingCartList", userId, func -> {
            UserShoppingCartBo cartBo = shoppingCartService.getUserShoppingCartList(userId);
            return JsonUtil.copy(cartBo, UserShoppingCartResp.class);
        });
    }

    @GetMapping(value = "/getUserShoppingCartCount")
    @Override
    public ResultDto<Integer> getUserShoppingCartCount(@RequestParam Long userId) throws TException {
        log.info("【购物车】获取用户购物车SKU数量, userId={}", userId);
        return ThriftResponseHelper.responseInvoke("getUserShoppingCartCount",
            userId, func -> shoppingCartService.getUserShoppingCartCount(userId));
    }

    @PostMapping(value = "/previewOrder", consumes = "application/json")
    @Override
    public ResultDto<PreviewOrderResp> previewOrder(@RequestBody PreviewOrderReq previewOrderReq) throws TException {
        log.info("【购物车】获取订单预览信息, previewOrderReq={}", JsonUtil.toJsonString(previewOrderReq));
        return ThriftResponseHelper.responseInvoke("previewOrder", previewOrderReq, func -> {
            List<PreviewOrderSelectSkuBo> selectedSkuList = JsonUtil.copyList(previewOrderReq.getSelectedSkuList(), PreviewOrderSelectSkuBo.class);
            PreviewOrderBo cartBo = shoppingCartService.previewOrder(previewOrderReq.getUserId(), selectedSkuList);
            return JsonUtil.copy(cartBo, PreviewOrderResp.class);
        });
    }

    @PostMapping(value = "/previewFlashSaleOrder", consumes = "application/json")
    @Override
    public ResultDto<PreviewOrderResp> previewFlashSaleOrder(@RequestBody PreviewFlashSaleOrderReq previewOrderReq) throws TException {
        log.info("【购物车】获取限时购订单预览信息, previewOrderReq={}", JsonUtil.toJsonString(previewOrderReq));
        return ThriftResponseHelper.responseInvoke("previewFlashSaleOrder", previewOrderReq, func -> {
            PreviewFlashSaleOrderParamBo paramBo = JsonUtil.copy(previewOrderReq, PreviewFlashSaleOrderParamBo.class);
            PreviewOrderBo cartBo = shoppingCartService.previewFlashSaleOrder(paramBo);
            return JsonUtil.copy(cartBo, PreviewOrderResp.class);
        });
    }

    @PostMapping(value = "/previewCollocationOrder", consumes = "application/json")
    @Override
    public ResultDto<PreviewOrderResp> previewCollocationOrder(@RequestBody PreviewCollocationOrderReq previewOrderReq) throws TException {
        log.info("【购物车】获取组合购订单预览信息, previewOrderReq={}", JsonUtil.toJsonString(previewOrderReq));
        return ThriftResponseHelper.responseInvoke("previewFlashSaleOrder", previewOrderReq, func -> {
            PreviewCollocationOrderParamBo paramBo = JsonUtil.copy(previewOrderReq, PreviewCollocationOrderParamBo.class);
            PreviewOrderBo cartBo = shoppingCartService.previewCollocationOrder(paramBo);
            return JsonUtil.copy(cartBo, PreviewOrderResp.class);
        });
    }

    @PostMapping(value = "/previewBuyNowOrder", consumes = "application/json")
    @Override
    public ResultDto<PreviewOrderResp> previewBuyNowOrder(@RequestBody PreviewBuyNowReq previewOrderReq) throws TException {
        log.info("【购物车】获取立即购买订单预览信息, previewOrderReq={}", JsonUtil.toJsonString(previewOrderReq));
        return ThriftResponseHelper.responseInvoke("previewBuyNowOrder", previewOrderReq, func -> {
            PreviewBuyNowOrderParamBo paramBo = JsonUtil.copy(previewOrderReq, PreviewBuyNowOrderParamBo.class);
            PreviewOrderBo cartBo = shoppingCartService.previewBuyNowOrder(paramBo);
            return JsonUtil.copy(cartBo, PreviewOrderResp.class);
        });
    }

    @PostMapping(value = "/checkCanSubmit", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> checkCanSubmit(PreviewOrderReq previewOrderReq) throws TException {
        return ThriftResponseHelper.responseInvoke("校验购物车是否可以提交", previewOrderReq, func -> {
            shoppingCartService.checkCanSubmit(previewOrderReq);
            return BaseResp.of();
        });
    }


}
