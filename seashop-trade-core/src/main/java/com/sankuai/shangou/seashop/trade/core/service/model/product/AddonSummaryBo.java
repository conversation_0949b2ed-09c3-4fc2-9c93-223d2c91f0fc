package com.sankuai.shangou.seashop.trade.core.service.model.product;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class AddonSummaryBo {

    /**
     * 凑单tab对应已勾选商品sku数，从购物车进入，勾选的商品如果是活动中的，就会计入
     */
    private Integer selectedInActiveCount;
    /**
     * 凑单tab对应已勾选商品的实际金额，如果不满足活动，则是原总额；满足活动就是扣减玩活动后的
     */
    private BigDecimal selectedProductAmount;
    /**
     * 截止时间描述
     */
    private String deadlineDesc;
    /**
     * 活动描述
     */
    private String activityDesc;
    /**
     * 满足的营销描述
     */
    private String matchPromotionDesc;

    /**
     * 截止时间时间戳
     */
    private Long deadline;

}
