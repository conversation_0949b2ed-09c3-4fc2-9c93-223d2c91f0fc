package com.sankuai.shangou.seashop.trade.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.enums.TradeResultCode;
import com.sankuai.shangou.seashop.trade.core.service.PreOrderService;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewOrderBo;
import com.sankuai.shangou.seashop.trade.core.service.model.SubmitOrderBo;
import com.sankuai.shangou.seashop.trade.thrift.core.PreOrderQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.SubmitOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewOrderResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/preOrder")
public class PreOrderQueryController implements PreOrderQueryFeign {

    @Resource
    private PreOrderService preOrderService;

    @GetMapping(value = "/getSubmitToken")
    @Override
    public ResultDto<String> getSubmitToken(@RequestParam Long userId) {
        log.info("【预订单】获取订单提交防重token");
        return ThriftResponseHelper.responseInvoke("getSubmitToken", userId,
                func -> preOrderService.getSubmitToken(userId));
    }


    @PostMapping(value = "/previewErpOrder", consumes = "application/json")
    @Override
    public ResultDto<PreviewOrderResp> previewErpOrder(@RequestBody SubmitOrderReq req) throws TException {
        log.info("预订单】预订单, 请求参数={}", req);
        // 参数校验
        req.checkParameter();
        // 参数对象转换
        SubmitOrderBo submitOrderBo = JsonUtil.copy(req, SubmitOrderBo.class);
        PreviewOrderBo previewOrderBo = preOrderService.previewOrder(submitOrderBo);
        // 约定，如果返回null，代表是校验通过，创建订单了，否则因为要返回数据，所以对象不会为null
        if (previewOrderBo == null) {
            return ResultDto.newWithData(null);
        }
        ResultDto<PreviewOrderResp> resultDto = ResultDto.newWithData(JsonUtil.copy(previewOrderBo, PreviewOrderResp.class));
        if (!previewOrderBo.isSuccess()) {
            resultDto.setCode(TradeResultCode.BIZ_PRE_ORDER_VALIDATE_FAIL.getValue());
        }
        return resultDto;
    }

}
