package com.sankuai.shangou.seashop.trade.core.service.model;

import com.sankuai.shangou.seashop.trade.core.service.model.promotion.AddonDescBo;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.ShopAndProductPromotionBo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * 店铺信息，包括店铺基本信息和店铺级别的金额
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
public class ShoppingCartShopBo {

    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 勾选的sku的总金额，扣减优惠券和满减后的金额，也计算了折扣，等价于店铺订单实付金额
     */
    private BigDecimal selectedTotalAmount;
    /**
     * 商品总金额，由价格计算得到，不包含任何营销(包括折扣)
     */
    private BigDecimal productTotalAmount;
    /**
     * 商品总数量，这里指勾选的
     */
    private Long selectedQuantity;
    /**
     * 是否需要显示凑单按钮
     */
    private Boolean showAddonBtn = false;
    /**
     * 凑单需要的描述
     */
    private List<AddonDescBo> promotionDescList;
    /**
     * 店铺当前满足的满减活动描述
     */
    private List<String> reductionDescList;
    /**
     * 店铺以及商品满足的营销信息。提交订单这个业务才会设置，用于保存订单的营销快照
     */
    private ShopAndProductPromotionBo shopAndProductPromotion;
    /**
     * 店铺是否是选中状态。如果店铺下的所有商品都是选中的，则店铺是选中状态
     */
    private Boolean whetherSelected;
    /**
     * 是否有与订单和商品匹配的有效的优惠券
     */
    private Boolean hasValidCoupon;
    // 下面两个字段是 是否开启专属和当前用户是否时专属商家
    private Boolean whetherShopOpenExclusiveMember;
    private Boolean whetherUserBelongExclusiveMember;
    // 店铺是否需要处理满减。目前主要是购物车多店铺提交时，如果店铺选择了优惠券，则店铺不处理满减
    private boolean needReduction = true;


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        ShoppingCartShopBo that = (ShoppingCartShopBo) o;

        return shopId.equals(that.shopId);
    }

    @Override
    public int hashCode() {
        return shopId.hashCode();
    }

    public void addPromotionDesc(AddonDescBo desc) {
        if (promotionDescList == null) {
            promotionDescList = new ArrayList<>(5);
        }
        promotionDescList.add(desc);
    }

    public void addReductionDesc(String desc) {
        if (reductionDescList == null) {
            reductionDescList = new ArrayList<>(5);
        }
        reductionDescList.add(desc);
    }
}
