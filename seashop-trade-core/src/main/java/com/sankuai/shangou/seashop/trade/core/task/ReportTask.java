package com.sankuai.shangou.seashop.trade.core.task;

import com.hishop.himall.report.api.request.BatchReportSourceCartReq;
import com.hishop.himall.report.api.request.ReportSourceCartReq;
import com.hishop.himall.report.api.service.ReportFeign;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.thrift.core.JobQueryFeign;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.JobLogInfoResp;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;
import com.sankuai.shangou.seashop.trade.dao.core.repository.ShoppingCartRepository;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2024/1/2/002
 * @description:
 */
@Slf4j
@Component
public class ReportTask {


    @Resource
    private ShoppingCartRepository shoppingCartRepository;

    @Resource
    private ReportFeign reportFeignService;

    @Resource
    private JobQueryFeign jobQueryFeign;

    /**
     * 订单退款状态查询
     */
    @XxlJob("reportCart")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "同步购物车报表")
    public void reportCart(String jobIdStr) {
        log.info("【定时任务】【同步购物车至报表服务】...start...");
        long jobId;
        if (StringUtils.hasLength(jobIdStr)) {
            jobId = Long.parseLong(jobIdStr.trim());
        } else {
            jobId = XxlJobHelper.getJobId();
        }
//        long jobId = 1L;
        //todo 拿到id后 调用feign 获取任务上次执行时间-
        BaseReq baseReq = new BaseReq();
        baseReq.setId(jobId);
        JobLogInfoResp jobLogInfoResp = ThriftResponseHelper.executeThriftCall(() -> jobQueryFeign.getJobLog(baseReq));
        Date triggerTime = null;
        if (jobLogInfoResp != null) {
            triggerTime = jobLogInfoResp.getTriggerTime();
        }
        List<ShoppingCart> carts = shoppingCartRepository.getByUpdateTime(triggerTime);
        if (CollectionUtils.isEmpty(carts)) {
            return;
        }
        List<ReportSourceCartReq> cartReqs = carts.stream().map(cart -> {
            ReportSourceCartReq cartReq = new ReportSourceCartReq();
            cartReq.setQuantity(cart.getQuantity().intValue());
            cartReq.setProductId(cart.getProductId());
            cartReq.setUserId(cart.getUserId());
            cartReq.setShopId(0L);
            cartReq.setId(cart.getId());
            cartReq.setPlatform(cart.getPlatform());
            cartReq.setCreateTime(LocalDateTime.ofInstant(cart.getCreateTime().toInstant(), ZoneId.systemDefault()));
            return cartReq;
        }).collect(Collectors.toList());
        reportFeignService.batchCreateSourceCart(new BatchReportSourceCartReq(cartReqs));
    }
}
