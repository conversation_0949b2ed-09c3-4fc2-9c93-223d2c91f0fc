package com.sankuai.shangou.seashop.trade.core.service.model;

import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ChooseCouponParamBo {

    /**
     * 商家会员ID
     */
    private Long userId;
    /**
     * 优惠券ID
     */
    private Long couponId;
    /**
     * 店铺信息
     */
    private ShoppingCartShopBo shop;
    /**
     * 商品列表
     */
    private List<ShopProductBo> productList;
    /**
     * 订单附加信息
     */
    private OrderAdditionalBo additional;

}
