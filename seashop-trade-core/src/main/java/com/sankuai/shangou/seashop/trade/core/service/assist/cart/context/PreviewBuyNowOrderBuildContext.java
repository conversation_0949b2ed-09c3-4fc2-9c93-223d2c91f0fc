package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PreviewBuyNowOrderBuildContext implements BuildContext {

    /**
     * 商家用户ID
     */
    private Long userId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 购买数量
     */
    private Long quantity;

    /**
     * 用户默认的收货地址
     */
    private ShippingAddressBo defaultShippingAddress;

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_BUY_NOW;
    }

    @Override
    public boolean needDiscount() {
        return true;
    }

    @Override
    public boolean needReduction() {
        return true;
    }

    @Override
    public boolean needPromotion() {
        return true;
    }
}
