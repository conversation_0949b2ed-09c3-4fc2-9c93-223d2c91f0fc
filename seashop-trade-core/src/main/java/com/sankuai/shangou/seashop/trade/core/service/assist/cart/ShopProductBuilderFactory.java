package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

import java.util.Map;

import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;

/**
 * <AUTHOR>
 */
@Component
public class ShopProductBuilderFactory implements ApplicationContextAware, InitializingBean {

    private ApplicationContext applicationContext;
    private final Map<BuildType, ShopProductBuilder> builderMap = Maps.newHashMap();

    public ShopProductBuilder getBuilder(BuildContext context) {
        return builderMap.get(context.getBuildType());
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        Map<String, ShopProductBuilder> beanMap = applicationContext.getBeansOfType(ShopProductBuilder.class);
        if (MapUtils.isEmpty(beanMap)) {
            return;
        }
        beanMap.values().forEach(builder -> builderMap.put(builder.getBuildType(), builder));
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }
}
