package com.sankuai.shangou.seashop.trade.core.mq.model;

import lombok.Getter;
import lombok.Setter;

/**
 * 数据库数据表数据变更消息体包装类
 * <AUTHOR>
 */
@Getter
@Setter
public class DbTableDataChangeMessage<T> {

    /**
     * 变更的表名
     */
    private String tableName;
    /**
     * 变更时间戳
     */
    private Long timestamp;
    /**
     * 序列号
     */
    private Long scn;
    /**
     * 变更类型：insert/update/delete
     */
    private String type;
    /**
     * 来源IP
     */
    private String sourceIP;
    /**
     * 具体的变更数据
     */
    private T data;
    /**
     * 如果是修改操作，记录变更前的数据
     */
    private String diffMapJson;
}
