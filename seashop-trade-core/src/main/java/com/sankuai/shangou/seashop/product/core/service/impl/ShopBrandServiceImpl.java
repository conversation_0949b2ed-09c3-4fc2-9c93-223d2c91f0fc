package com.sankuai.shangou.seashop.product.core.service.impl;

import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import lombok.extern.slf4j.Slf4j;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.remote.user.RemoteShopService;
import com.sankuai.shangou.seashop.product.common.remote.user.model.RemoteShopBo;
import com.sankuai.shangou.seashop.product.core.service.ShopBrandService;
import com.sankuai.shangou.seashop.product.core.service.BrandService;
import com.sankuai.shangou.seashop.product.core.service.model.ShopBrandBo;
import com.sankuai.shangou.seashop.product.core.service.model.ShopBrandQueryBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.Brand;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopBrand;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopBrandExtDto;
import com.sankuai.shangou.seashop.product.dao.core.model.ShopBrandDto;
import com.sankuai.shangou.seashop.product.dao.core.repository.BrandRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ShopBrandRepository;

/**
 * <AUTHOR>
 * @date 2023/11/08 11:27
 */
@Service
@Slf4j
public class ShopBrandServiceImpl implements ShopBrandService {

    @Resource
    private ShopBrandRepository shopBrandRepository;
    @Resource
    private RemoteShopService remoteShopService;
    @Resource
    private BrandRepository brandRepository;
    @Resource
    private BrandService brandService;

    @Override
    public BasePageResp<ShopBrandBo> pageShopBrand(BasePageParam pageParam, ShopBrandQueryBo queryBo) {
        Page<ShopBrandExtDto> pageResult = PageHelper.startPage(pageParam);
        shopBrandRepository.listShopBrandExt(JsonUtil.copy(queryBo, ShopBrandDto.class));
        return PageResultHelper.transfer(pageResult, ShopBrandBo.class);
    }

    @Override
    public List<ShopBrandBo> listShopBrand(ShopBrandQueryBo queryBo) {
        RemoteShopBo shop = remoteShopService.getByShopId(queryBo.getShopId());
        // 如果是自营店铺返回所有品牌
        if (shop != null && shop.getWhetherSelf()) {
            List<Brand> brands = brandRepository.list(new LambdaQueryWrapper<Brand>().eq(Brand::getWhetherDelete, false));
            return JsonUtil.copyList(brands, ShopBrandBo.class, (source, target) -> {
                target.setShopId(queryBo.getShopId());
                target.setBrandId(source.getId());
                target.setBrandName(source.getName());
            });
        }

        List<ShopBrandExtDto> shopBrandList = shopBrandRepository.listShopBrandExt(JsonUtil.copy(queryBo, ShopBrandDto.class));
        return JsonUtil.copyList(shopBrandList, ShopBrandBo.class);
    }

    @Override
    public void createDefaultShopBrand(Long shopId) {
        log.info("【创建默认店铺品牌】开始为店铺{}创建默认品牌关联", shopId);

        // 查询默认品牌
        Brand defaultBrand = brandService.queryByName("默认品牌");
        if (defaultBrand == null) {
            log.error("【创建默认店铺品牌】默认品牌不存在，店铺ID：{}", shopId);
            throw new BusinessException("默认品牌不存在，请先创建名为'默认品牌'的品牌");
        }

        // 检查是否已经存在该品牌关联
        boolean exists = shopBrandRepository.existShopBrand(defaultBrand.getId(), shopId);
        if (exists) {
            log.info("【创建默认店铺品牌】店铺{}已存在默认品牌关联，跳过创建", shopId);
            return;
        }

        // 创建店铺品牌关联
        ShopBrand shopBrand = new ShopBrand();
        shopBrand.setShopId(shopId);
        shopBrand.setBrandId(defaultBrand.getId());
        shopBrand.setCreateTime(new Date());
        shopBrand.setUpdateTime(new Date());

        shopBrandRepository.save(shopBrand);
        log.info("【创建默认店铺品牌】为店铺{}创建默认品牌关联成功，品牌ID：{}", shopId, defaultBrand.getId());
    }
}
