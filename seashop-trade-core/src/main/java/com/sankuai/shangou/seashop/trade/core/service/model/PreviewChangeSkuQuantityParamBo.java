package com.sankuai.shangou.seashop.trade.core.service.model;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.OrderAdditionalDto;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class PreviewChangeSkuQuantityParamBo {

    /**
     * 商家会员ID
     */
    private Long userId;
   /**
     * 用户收货地址，可能为空
     */
    private Long shippingAddressId;
    /**
     * 购物车ID，根据要需改的数据行的ID，有就传
     */
    private Long id;
    /**
     * 商品ID，当前修改数量的productId
     */
    private Long productId;
    /**
     * skuId，当前修改数量的skuId
     */
    private String skuId;
    /**
     * 变更后的数量，前端根据sku的基本规则先做校验后传入实际要修改的值
     */
    private Long quantity;
    /**
     * 店铺信息，当前是整个对象传入，数据是为了保持用户前后看到的数据一致
     */
    private ShoppingCartShopBo shop;
    /**
     * 商品列表，当前是整个对象传入，数据是为了保持用户前后看到的数据一致
     */
    private List<ShopProductBo> productList;
    /**
     * 附加信息，店铺订单的附件信息，包括选择的优惠券，发票等
     */
    private OrderAdditionalBo additional;
    /**
     * 限时购活动id。如果不为空，代表是限时购
     */
    private Long flashSaleId;
    /**
     * 组合购活动id。如果不为空，代表是组合购
     */
    private Long collocationId;
    /**
     * 是否立即购买。如果为true，代表是立即购买
     */
    private Boolean whetherBuyNow;

}
