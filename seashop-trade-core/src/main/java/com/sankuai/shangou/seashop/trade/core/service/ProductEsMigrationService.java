package com.sankuai.shangou.seashop.trade.core.service;

import java.util.List;

/**
 * 商品ES索引迁移服务接口
 * 用于零停机迁移商品索引，支持替换号模糊搜索优化
 * 
 * <AUTHOR>
 */
public interface ProductEsMigrationService {

    /**
     * 批量重建所有商品ES索引
     * 支持分页处理，避免内存溢出
     * 
     * @param batchSize 批处理大小，建议100-500
     * @param maxConcurrency 最大并发数，建议5-10
     */
    void rebuildAllProductIndex(int batchSize, int maxConcurrency);

    /**
     * 重建指定商品的ES索引
     * 
     * @param productId 商品ID
     */
    void rebuildProductIndex(Long productId);

    /**
     * 重建指定商品列表的ES索引
     * 
     * @param productIds 商品ID列表
     */
    void rebuildProductIndexBatch(List<Long> productIds);
}
