package com.sankuai.shangou.seashop.trade.core.service.model.product;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.CateLevel1Bo;
import lombok.Getter;
import lombok.Setter;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SearchTradeProductRespBo {

    /**
     * 品牌列表
     */
    private List<TradeProductBrandBo> brandList;
    /**
     * 属性列表
     */
    private List<ProductAttributeBo> attributeList;
    /**
     * 类目树列表
     */
    private List<CateLevel1Bo> categoryTreeList;
    /**
     * 商品分页列表
     */
    private BasePageResp<SearchedTradeProductBo> productPage;

    public static SearchTradeProductRespBo defaultEmpty(BasePageResp<SearchedTradeProductBo> productPage) {
        SearchTradeProductRespBo resultBo = new SearchTradeProductRespBo();
        resultBo.setBrandList(Collections.emptyList());
        resultBo.setAttributeList(Collections.emptyList());
        resultBo.setCategoryTreeList(Collections.emptyList());
        resultBo.setProductPage(productPage);
        return resultBo;
    }

}
