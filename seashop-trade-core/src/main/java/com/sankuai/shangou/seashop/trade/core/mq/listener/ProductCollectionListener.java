package com.sankuai.shangou.seashop.trade.core.mq.listener;

import javax.annotation.Resource;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.trade.core.mq.model.DbTableDataChangeMessage;
import com.sankuai.shangou.seashop.trade.core.mq.model.ProductCollectionMessage;
import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/12 9:49
 */
/*@Profile("!develop")
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_PRODUCT_COLLECTION,
//        group = MafkaConst.GROUP_PRODUCT_COLLECTION)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_PRODUCT_COLLECTION + "_${spring.profiles.active}"
, consumerGroup = MafkaConst.GROUP_PRODUCT_COLLECTION + "_${spring.profiles.active}")
public class ProductCollectionListener implements RocketMQListener<String> {

    @Resource
    private TradeProductService tradeProductService;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
//        String body = mafkaMessage.getBody().toString();
//        log.info("【mafka消费】【更新商品收藏数】消息内容为: {}", body);
//        DbTableDataChangeMessage<ProductCollectionMessage> messageWrapper = JsonUtil.parseObject(body,
//                new TypeReference<DbTableDataChangeMessage<ProductCollectionMessage>>() {});
//        tradeProductService.updateProductCollectionCount(messageWrapper.getData().getProductId());
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(String body) {
        log.info("【mafka消费】【更新商品收藏数】消息内容为: {}", body);
        DbTableDataChangeMessage<ProductCollectionMessage> messageWrapper = JsonUtil.parseObject(body,
            new TypeReference<DbTableDataChangeMessage<ProductCollectionMessage>>() {});
        tradeProductService.updateProductCollectionCount(messageWrapper.getData().getProductId());
    }
}*/
