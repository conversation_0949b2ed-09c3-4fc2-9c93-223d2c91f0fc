package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;

/**
 * 预览订单页修改组合购商品数量上下文
 * <AUTHOR>
 */
public class ChangePreviewCollocationSkuQuantityContext extends BasePreviewChangeQuantityContext {

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_CHANGE_COLLOCATION_SKU_QUANTITY;
    }

    @Override
    public boolean needDiscount() {
        return false;
    }

    @Override
    public boolean needReduction() {
        return true;
    }

    @Override
    public boolean needExclusivePrice() {
        return false;
    }

    @Override
    public boolean needLadderPrice() {
        return false;
    }
}
