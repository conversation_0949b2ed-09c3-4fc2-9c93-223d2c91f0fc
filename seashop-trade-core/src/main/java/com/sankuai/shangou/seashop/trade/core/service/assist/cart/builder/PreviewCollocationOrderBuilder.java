package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.List;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.PreviewCollocationOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.model.PurchaseProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PreviewCollocationOrderBuilder extends AbstractCollocationBuilder {


    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        PreviewCollocationOrderBuildContext previewOrderBuildContext = (PreviewCollocationOrderBuildContext) context;
        // 查询组合购数据
        Long collocationId = previewOrderBuildContext.getCollocationId();
        Long userId = previewOrderBuildContext.getUserId();
        List<PurchaseProductBo> skuList = previewOrderBuildContext.getSkuList();
        // 调用父类公共的构建方法
        return buildCollocationShopProduct(userId, collocationId, skuList, context.getCurrentTime());
    }

    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {
        PreviewCollocationOrderBuildContext previewOrderBuildContext = (PreviewCollocationOrderBuildContext) context;
        ShippingAddressBo defaultShippingAddress = previewOrderBuildContext.getDefaultShippingAddress();
        // 预览相关的接口需要设置一些额外的数据
        super.fillExtraOrderPreviewData(context.getUserId(), dataHolder);
        // 计算并设置运费，单独计算运费，因为进入预览订单页如果商品在禁售区域不抛出异常
        calculateAndSetFreight(defaultShippingAddress, dataHolder, true);
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_COLLOCATION;
    }


    //*******************************************************************



}
