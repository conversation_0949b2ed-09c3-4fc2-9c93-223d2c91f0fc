package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;

/**
 * 预览订单页修改立即购买商品数量上下文
 * <AUTHOR>
 */
public class ChangePreviewBuyNowSkuQuantityContext extends BasePreviewChangeQuantityContext {

    // 单独设置这个字段，是用于去掉优惠券选择时需要重新计算满减，要重置
    private Boolean needReduction;

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_CHANGE_BUY_NOW_SKU_QUANTITY;
    }

    @Override
    public boolean needDiscount() {
        return true;
    }

    @Override
    public boolean needReduction() {
        if (needReduction == null) {
            return true;
        }
        return Boolean.TRUE.equals(needReduction);
    }

    public void resetNeedReduction(boolean needReduction) {
        this.needReduction = needReduction;
    }

    @Override
    public boolean needPromotion() {
        return true;
    }
}
