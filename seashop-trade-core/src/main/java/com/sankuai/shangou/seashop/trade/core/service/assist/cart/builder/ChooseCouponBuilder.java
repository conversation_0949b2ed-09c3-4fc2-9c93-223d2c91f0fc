package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteProductSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCustomShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChooseCouponBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;

/**
 * 选择优惠券的构建器
 * 优惠券与满减互斥
 *
 * <AUTHOR>
 */
@Service
public class ChooseCouponBuilder extends AbstractCustomShopProductBuilder {

    @Resource
    private ProductRemoteService productRemoteService;

    /**
     * 这里的构建店铺商品，完全基于页面传入的数据，不再需要查询数据库，因为整体逻辑只需要计算运费，保持用户看到的前后一致
     *
     * @param context    构建上下文
     * @param dataHolder 数据临时存储对象
     * <AUTHOR>
     */
    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        ChooseCouponBuildContext couponContext = (ChooseCouponBuildContext) context;
        // 选择优惠券默认不需要处理满减
        couponContext.resetNeedReduction(false);
        // 返回的店铺和商品数据，已页面提交的为准
        ShopProductListBo shopProductListBo = new ShopProductListBo();
        List<ShopProductBo> productList = couponContext.getProductList();

        List<String> skuIdList = productList.stream()
                .map(ShopProductBo::getSkuId)
                .distinct()
                .collect(Collectors.toList());
        Map<String, RemoteProductSkuBo> skuMap = productRemoteService.queryProductSkuToMap(skuIdList);
        // 保险起见，设置选中为true
        productList.forEach(shopProductBo -> {
            shopProductBo.setWhetherSelected(true);
            RemoteProductSkuBo sku = skuMap.get(shopProductBo.getSkuId());
            if (sku != null) {
                shopProductBo.setFreightTemplateId(sku.getFreightTemplateId());
                shopProductBo.setWeight(sku.getWeight());
                shopProductBo.setVolume(sku.getVolume());
            }
        });
        shopProductListBo.setProductList(productList);

        ShoppingCartShopBo shop = couponContext.getShop();
        // 选择优惠券的时候，店铺的实际金额可能是计算了满减后，或者之前选择了其他优惠券计算了金额的，这里用店铺的商品金额重置，保证选择优惠券时是重新计算的
        // 店铺的 productTotalAmount 是不包括折扣价格的，所以要重新汇总
        BigDecimal currentRealAmount = couponContext.getProductList().stream()
                .map(ShopProductBo::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        shop.setSelectedTotalAmount(currentRealAmount);
        shopProductListBo.setShop(shop);

        OrderAdditionalBo additional = couponContext.getAdditional();
        if (additional == null) {
            additional = new OrderAdditionalBo();
        }

        Long couponRecordId = couponContext.getCouponId();
        // 选择的优惠券记录ID为0时，时特殊值，代表去掉优惠券，此时需要重新考虑满减
        couponContext.resetNeedReduction(couponRecordId == 0);

        // 重置营销
        shop.setPromotionDescList(new ArrayList<>(5));
        additional.setReductionAmount(BigDecimal.ZERO);
        additional.setReductionActivityId(null);
        additional.setReductionConditionAmount(null);
        // 清除可能已经满足条件的满减标识，优惠券与满减互斥，预览订单页只会显示满减，所以直接置空
        shop.setReductionDescList(null);

        additional.setCouponRecordId(couponRecordId);
        shopProductListBo.setAdditional(additional);
        return Collections.singletonList(shopProductListBo);
    }


    /**
     * 提交订单时，如果校验通过的话，最终要提交的数据以页面提交的为准，如果校验没通过，也是返回页面提交的数据，只不过设置错误原因
     *
     * @param context
     * @param dataHolder void
     * <AUTHOR>
     */
    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {
        ChooseCouponBuildContext couponContext = (ChooseCouponBuildContext) context;
        // 计算优惠券
        calculateCoupon(couponContext, dataHolder);

        // 计算并设置运费
        calculateAndSetFreight(couponContext.getShippingAddress(), dataHolder);
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.CHOOSE_COUPON;
    }


    //********************************************************





}
