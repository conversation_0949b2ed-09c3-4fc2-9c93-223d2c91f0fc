package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCartShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.CartBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.PreviewOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewOrderSelectSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;
import com.sankuai.shangou.seashop.trade.dao.core.repository.ShoppingCartRepository;

/**
 * <AUTHOR>
 */
@Service
public class PreviewOrderShopProductBuilder extends AbstractCartShopProductBuilder {

    @Resource
    private ShoppingCartRepository shoppingCartRepository;


    @Override
    protected List<ShoppingCart> getShoppingCartList(BuildContext context) {
        PreviewOrderBuildContext previewOrderBuildContext = (PreviewOrderBuildContext) context;
        List<PreviewOrderSelectSkuBo> selectedSkuList = previewOrderBuildContext.getSelectedSkuList();
        // 首先获取勾选的购物车sku列表
        List<Long> idList = selectedSkuList.stream()
                .map(PreviewOrderSelectSkuBo::getId)
                .collect(Collectors.toList());
        return shoppingCartRepository.queryByIdList(idList);
    }


    @Override
    protected CartBuildDataHolder tempHoldData(List<ShopProductBo> userShopSkuList) {
        return CartBuildDataHolder.builder()
                .validProductList(userShopSkuList)
                .build();
    }

    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, CartBuildDataHolder dataHolder) {
        Map<ShoppingCartShopBo, List<ShopProductBo>> shopGroupedMap = dataHolder.getShopGroupedMap();
        // 对传入的购物车数据转成map，方便后面使用
        PreviewOrderBuildContext previewOrderBuildContext = (PreviewOrderBuildContext) context;
        Map<String, PreviewOrderSelectSkuBo> selectedSkuMap = previewOrderBuildContext.getSelectedSkuList().stream()
                .collect(Collectors.toMap(PreviewOrderSelectSkuBo::getSkuId, Function.identity(), (o1, o2) -> o2));
        // 预览订单页的数量和价格以购物车页面选择提交的为准，防止用户前后看到的不一致
        return shopGroupedMap.entrySet().stream()
                .map(entry -> {
                    // 店铺信息
                    ShoppingCartShopBo shopBo = entry.getKey();

                    BigDecimal shopAmount = BigDecimal.ZERO;
                    for (ShopProductBo sku : entry.getValue()) {
                        PreviewOrderSelectSkuBo selectSku = selectedSkuMap.get(sku.getSkuId());
                        if (selectSku == null) {
                            throw new BusinessException("购物车商品不存在");
                        }
                        // 先设置价格为前端传入，后续会用最新的折扣等重新计算
                        sku.setRealSalePrice(selectSku.getRealSalePrice());
                        sku.setFinalSalePrice(selectSku.getRealSalePrice());
                        sku.setQuantity(selectSku.getQuantity());
                        sku.setTotalAmount(NumberUtil.multiply(sku.getFinalSalePrice(), sku.getQuantity()).setScale(2, RoundingMode.HALF_UP));
                        // 累计店铺金额
                        shopAmount = shopAmount.add(sku.getTotalAmount());
                    }
                    shopBo.setSelectedTotalAmount(shopAmount);
                    shopBo.setProductTotalAmount(shopAmount);

                    // 预先定义每个店铺(订单)的附加信息对象
                    OrderAdditionalBo additional = new OrderAdditionalBo();

                    ShopProductListBo shopProductListBo = new ShopProductListBo();
                    shopProductListBo.setShop(entry.getKey());
                    shopProductListBo.setProductList(entry.getValue());
                    shopProductListBo.setAdditional(additional);
                    return shopProductListBo;
                }).collect(Collectors.toList());
    }

    @Override
    protected void processExpand(BuildContext context, CartBuildDataHolder dataHolder) {
        PreviewOrderBuildContext previewOrderBuildContext = (PreviewOrderBuildContext) context;

        ShippingAddressBo defaultShippingAddress = previewOrderBuildContext.getDefaultShippingAddress();
        // 预览相关的接口需要设置一些额外的数据,发票、保障标识、是否显示优惠券
        super.fillExtraOrderPreviewData(context.getUserId(), dataHolder);
        // 先计算优惠券再计算运费，计算并设置运费，单独计算运费，因为进入预览订单页如果商品在禁售区域不抛出异常
        calculateAndSetFreight(defaultShippingAddress, dataHolder, true);
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_ORDER;
    }
}
