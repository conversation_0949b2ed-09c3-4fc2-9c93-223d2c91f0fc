package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 基于用户自定义数据构建
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCustomShopProductBuilder extends AbstractShopProductBuilder<BaseBuildDataHolder> {

    @Override
    protected BaseBuildDataHolder buildShopAndProductList(BuildContext context) {
        // 初始化数据，可以做一些处理。比如购物车需要区分是否销售中
        BaseBuildDataHolder dataHolder = new BaseBuildDataHolder();
        // 子类根据业务场景，构建店铺商品列表
        List<ShopProductListBo> shopProductList = buildShopProduct(context, dataHolder);
        dataHolder.setShopProductList(shopProductList);
        if (context.needPromotion()) {
            Map<ShoppingCartShopBo, List<ShopProductBo>> shopGroupedMap = new HashMap<>(4);
            shopProductList.forEach(shopOrder -> {
                shopGroupedMap.put(shopOrder.getShop(), shopOrder.getProductList());
            });
            dataHolder.setShopGroupedMap(shopGroupedMap);
            appendShopPromotion(context, dataHolder);
        }
        return dataHolder;
    }

    @Override
    protected void extendResult(BuildResult result, BaseBuildDataHolder dataHolder) {
        // nothing to do
    }


}
