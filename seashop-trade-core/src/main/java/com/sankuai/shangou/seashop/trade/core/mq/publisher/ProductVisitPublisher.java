package com.sankuai.shangou.seashop.trade.core.mq.publisher;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.trade.core.mq.model.ProductVisitMessage;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/03 14:29
 */
@Service
@Slf4j
public class ProductVisitPublisher {

//    @MafkaProducer(namespace = MafkaConst.DEFAULT_NAMESPACE, topic = MafkaConst.TOPIC_PRODUCT_VISIT)
//    private IProducerProcessor producerProcessor;

    @Resource
    private DefaultRocketMq defaultRocketMq;

    public void sendMessage(ProductVisitMessage visitMessage) {
        log.info("[send product visit message] data: {}", JsonUtil.toJsonString(visitMessage));
        try {
            defaultRocketMq.syncSend(MafkaConst.TOPIC_PRODUCT_VISIT, JsonUtil.toJsonString(visitMessage));
        }
        catch (Exception e) {
            // 只是记录访问数, 不影响业务
            log.error("[send product visit message] error", e);
        }
    }

}
