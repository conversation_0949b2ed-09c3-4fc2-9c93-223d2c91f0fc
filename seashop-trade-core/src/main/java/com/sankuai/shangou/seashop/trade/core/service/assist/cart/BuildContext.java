package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

import java.util.Date;

/**
 * <AUTHOR>
 */
public interface BuildContext {

    Long getUserId();

    BuildType getBuildType();

    /**
     * 是否需要计算折扣
     */
    boolean needDiscount();

    /**
     * 是否需要计算满减
     */
    boolean needReduction();

    /**
     * 提交订单时的入口时间，后续对于营销的校验以及订单的下单时间等，都基于这个入口时间，
     * 防止逻辑执行过程中每次取最新的时间导致判断不一致
     * 子类按需重写
     */
    default Date getCurrentTime() {return new Date();};

    default boolean needExclusivePrice() {return true;}

    default boolean needLadderPrice() {return true;}

    // 是否需要重新计算价格，默认需要，但比如修改收货地址，其他都不用变，只需要用当前的金额计算运费就行，需要单独设置成false
    default boolean needReCalPrice() {return true;}

    /**
     * 是否需要查询营销数据，折扣和满减任意一个为true就需要查询，后者子类覆盖指定，比如有些情况下只需要专属价
     */
    default boolean needPromotion() {
        return needDiscount() || needReduction() || needExclusivePrice();
    }

}
