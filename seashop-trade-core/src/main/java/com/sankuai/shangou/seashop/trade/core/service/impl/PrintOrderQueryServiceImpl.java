package com.sankuai.shangou.seashop.trade.core.service.impl;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.request.PrintOrderDataReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderDataResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.PrintOrderItemDataResp;
import com.sankuai.shangou.seashop.trade.common.remote.PrintOrderRemoteService;
import com.sankuai.shangou.seashop.trade.core.service.PrintOrderQueryService;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PrintOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.OrderItemResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.OrderResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PrintOrderResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 15:22
 */
@Service
public class PrintOrderQueryServiceImpl implements PrintOrderQueryService {

    @Resource
    private PrintOrderRemoteService printOrderRemoteService;

    @Override
    public PrintOrderResp getOrderPrint(PrintOrderReq printOrderReq) {
        PrintOrderResp result = new PrintOrderResp();
        // 组装入参
        PrintOrderDataReq request = new PrintOrderDataReq();
        request.setOrderIdList(printOrderReq.getOrderIdList());
        // 调用远程打印订单服务，查询订单信息
        List<PrintOrderDataResp> orderList = printOrderRemoteService.batchQueryOrderByOrderIds(request);
        // 调用远程打印订单项服务，查询订单项商品信息
        List<PrintOrderItemDataResp> orderItemList = printOrderRemoteService.batchQueryOrderItemByOrderIds(request);
        // 将订单项按照订单id分组
        Map<String, List<PrintOrderItemDataResp>> orderItemMap = orderItemList.stream().collect(Collectors.groupingBy(PrintOrderItemDataResp::getOrderId));
        List<OrderResp> orderRespList = new ArrayList<>();
        for(PrintOrderDataResp orderDataResp : orderList){
            OrderResp orderResp = JsonUtil.copy(orderDataResp, OrderResp.class);
            orderResp.setOrderItemRespList(JsonUtil.copyList(orderItemMap.get(orderDataResp.getOrderId()), OrderItemResp.class));
            orderRespList.add(orderResp);
        }
        result.setOrderRespList(orderRespList);
        return result;
    }
}
