package com.sankuai.shangou.seashop.trade.core.service.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ShopInvoiceConfigBo {

    /**
     * 是否提供发票
     */
    private Boolean whetherInvoice;
    /**
     * 是否提供普通发票
     */
    private Boolean whetherPlainInvoice;
    /**
     * 是否提供电子发票
     */
    private Boolean whetherElectronicInvoice;
    /**
     * 是否提供增值税发票
     */
    private Boolean whetherVatInvoice;
    /**
     * 普通发票税率
     */
    private BigDecimal plainInvoiceRate;
    /**
     * 增值税税率
     */
    private BigDecimal vatInvoiceRate;
    /**
     * 订单完成后多少天开具增值税发票
     */
    private Integer vatInvoiceDay;
    /**
     * 售后维权期天数
     */
    private Integer warrantyDays;

    public void setDefault() {
        if (whetherInvoice == null) {
            whetherInvoice = false;
        }
        if (whetherPlainInvoice == null) {
            whetherPlainInvoice = false;
        }
        if (whetherElectronicInvoice == null) {
            whetherElectronicInvoice = false;
        }
        if (whetherVatInvoice == null) {
            whetherVatInvoice = false;
        }
        if (plainInvoiceRate == null) {
            plainInvoiceRate = BigDecimal.ZERO;
        }
        if (vatInvoiceRate == null) {
            vatInvoiceRate = BigDecimal.ZERO;
        }
        if (vatInvoiceDay == null) {
            vatInvoiceDay = 0;
        }
        if (warrantyDays == null) {
            warrantyDays = 0;
        }
    }

    public static ShopInvoiceConfigBo defaultEmpty() {
        ShopInvoiceConfigBo shopInvoiceConfigBo = new ShopInvoiceConfigBo();
        shopInvoiceConfigBo.setDefault();
        return shopInvoiceConfigBo;
    }


}
