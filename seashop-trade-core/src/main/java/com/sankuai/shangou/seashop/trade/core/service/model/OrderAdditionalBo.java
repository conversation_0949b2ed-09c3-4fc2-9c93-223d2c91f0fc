package com.sankuai.shangou.seashop.trade.core.service.model;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * 订单附加信息：留言、优惠券、发票等
 * <AUTHOR>
 */
@Getter
@Setter
public class OrderAdditionalBo {

    /**
     * 配送方式。目前是固定值。1：快递配送
     */
    private Integer deliveryType;
    /**
     * 用户领用优惠券的记录ID
     */
    private Long couponRecordId;
    /**
     * 优惠券ID，代表活动ID
     */
    private Long couponId;
    /**
     * 优惠券金额
     */
    private BigDecimal couponAmount;
    /**
     * 折扣总金额
     */
    private BigDecimal discountAmount;
    /**
     * 满减总金额
     */
    private BigDecimal reductionAmount;
    /**
     * 满减条件金额
     */
    private BigDecimal reductionConditionAmount;
    /**
     * 运费
     */
    private BigDecimal freightAmount;
    /**
     * 用户备注
     */
    private String remark;
    /**
     * 税费，税费开始由前端计算传入，后端根据发票选择，提交订单时重新计算比对
     */
    private BigDecimal taxAmount;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 发票信息
     */
    private InvoiceBo invoice;
    /**
     * 满减活动ID
     */
    private Long reductionActivityId;
    /**
     * 店铺发票配置，预览订单接口初始话一次，后续依赖前端传入后再返回或者前端保留
     */
    private ShopInvoiceConfigBo shopInvoiceConfig;


    /**
     * 第三方订单号(美团订单号)
     */
    private String sourceOrderId;

}
