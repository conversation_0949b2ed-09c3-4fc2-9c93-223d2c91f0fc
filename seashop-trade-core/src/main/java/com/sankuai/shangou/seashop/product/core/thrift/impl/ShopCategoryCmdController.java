package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminProcess;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.service.ShopCategoryService;
import com.sankuai.shangou.seashop.product.core.service.model.ShopCategoryBo;
import com.sankuai.shangou.seashop.product.core.service.model.TransferProductBo;
import com.sankuai.shangou.seashop.product.dao.core.domain.ShopCategory;
import com.sankuai.shangou.seashop.product.thrift.core.ShopCategoryCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.CreateDefaultShopCategoriesReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.DeleteShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.SaveShopCategoryReq;
import com.sankuai.shangou.seashop.product.thrift.core.request.shopcategory.TransferProductReq;

/**
 * <AUTHOR>
 * @date 2023/11/13 16:12
 */
@RestController
@RequestMapping("/shopCategory")
public class ShopCategoryCmdController implements ShopCategoryCmdFeign {

    @Resource
    private ShopCategoryService shopCategoryService;

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = SaveShopCategoryReq.class,
            entity = ShopCategory.class,
            actionName = "创建店铺分类")
    @PostMapping(value = "/createShopCategory", consumes = "application/json")
    public ResultDto<BaseResp> createShopCategory(@RequestBody SaveShopCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createShopCategory", request, req -> {
            req.checkParameter();

            shopCategoryService.saveShopCategory(JsonUtil.copy(req, ShopCategoryBo.class));
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.MODIFY,
            dto = SaveShopCategoryReq.class,
            entity = ShopCategory.class,
            actionName = "更新店铺分类")
    @PostMapping(value = "/updateShopCategory", consumes = "application/json")
    public ResultDto<BaseResp> updateShopCategory(@RequestBody SaveShopCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("updateShopCategory", request, req -> {
            req.checkForEdit();

            shopCategoryService.saveShopCategory(JsonUtil.copy(req, ShopCategoryBo.class));
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.MOVE,
            dto = DeleteShopCategoryReq.class,
            entity = ShopCategory.class,
            actionName = "删除店铺分类")
    @PostMapping(value = "/deleteShopCategory", consumes = "application/json")
    public ResultDto<BaseResp> deleteShopCategory(@RequestBody DeleteShopCategoryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deleteShopCategory", request, req -> {

            shopCategoryService.deleteShopCategory(req.getId(), req.getShopId());
            return new BaseResp();
        });
    }

    @Override
    @ExaminProcess(processModel = ExaminModelEnum.PRODUCT,
            processType = ExaProEnum.INSERT,
            dto = TransferProductReq.class,
            entity = ShopCategory.class,
            actionName = "转移商品")
    @PostMapping(value = "/transferProduct", consumes = "application/json")
    public ResultDto<BaseResp> transferProduct(@RequestBody TransferProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("transferProduct", request, req -> {

            shopCategoryService.asyncTransferProduct(JsonUtil.copy(req, TransferProductBo.class));
            return new BaseResp();
        });
    }

    @Override
    @PostMapping(value = "/createDefaultShopCategories", consumes = "application/json")
    public ResultDto<BaseResp> createDefaultShopCategories(@RequestBody CreateDefaultShopCategoriesReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createDefaultShopCategories", request, req -> {
            shopCategoryService.createDefaultShopCategories(req.getShopId());
            return new BaseResp();
        });
    }
}
