package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SubmitOrderContext implements BuildContext {

    private Long userId;

    /**
     * 收货地址
     */
    private ShippingAddressBo shippingAddress;
    /**
     * 所有店铺总金额
     */
    private BigDecimal totalAmount;
    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;

    /**
     * 订单平台，0：PC；2：小程序
     */
    private Integer platform;
    // 单独设置这个字段，是用于去掉优惠券选择时需要重新计算满减，要重置
    private Boolean needReduction;

    /**
     * 限时购活动id，限时购活动提交时字段有值
     */
    private Long flashSaleId;
    /**
     * 组合购活动id，组合购活动提交时字段有值
     */
    private Long collocationId;
    /**
     * 是否是立即购买
     */
    private Boolean whetherBuyNow;
    /**
     * 提交订单时的入口时间，后续对于营销的校验以及订单的下单时间等，都基于这个入口时间，
     * 防止逻辑执行过程中每次取最新的时间导致判断不一致
     */
    private Date currentTime;

    @Override
    public BuildType getBuildType() {
        return BuildType.SUBMIT_ORDER;
    }

    @Override
    public boolean needDiscount() {
        return true;
    }

    @Override
    public boolean needReduction() {
        if (needReduction == null) {
            return true;
        }
        return Boolean.TRUE.equals(needReduction);
    }

    public void resetNeedReduction(boolean needReduction) {
        this.needReduction = needReduction;
    }
}
