package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.enums.TradeOrderValidResultEnum;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildResult;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.BuyNowSubmitOrderContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

import cn.hutool.core.collection.CollUtil;

/**
 * 限时购提交订单的构建器
 * <AUTHOR>
 */
@Service
public class BuyNowSubmitOrderBuilder extends AbstractBuyNowBuilder {


    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        BuyNowSubmitOrderContext buildContext = (BuyNowSubmitOrderContext) context;

        List<ShopProductListBo> shopProductList = buildContext.getShopProductList();
        if (CollUtil.isEmpty(shopProductList)) {
            throw new BusinessException("立即购买商品列表为空");
        }
        // 立即购买是只有一个商品
        ShopProductListBo shopProductListBo = shopProductList.get(0);
        if (shopProductListBo == null) {
            throw new BusinessException("立即购买商品列表为空");
        }
        ShopProductBo productBo = shopProductListBo.getProductList().get(0);

        String skuId = productBo.getSkuId();
        Long quantity = productBo.getQuantity();

        // 下单逻辑中，附加信息，先取页面传入的，因为会包括配送方式，优惠券等
        OrderAdditionalBo additional = JsonUtil.copy(shopProductListBo.getAdditional(), OrderAdditionalBo.class);
        // 选择了优惠券时，不处理满减了
        boolean hasCoupon = super.hasCoupon(additional);
        if (hasCoupon) {
            buildContext.resetNeedReduction(false);
        }
        // 调用父类公共的构建方法
        List<ShopProductListBo> currentShopOrderList =  buildBuyNowShopProduct(context.getUserId(), skuId, quantity);
        currentShopOrderList.get(0).setAdditional(additional);
        currentShopOrderList.get(0).setProductIdList(Collections.singletonList(productBo.getProductId()));
        return currentShopOrderList;
    }

    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {
        BuyNowSubmitOrderContext buildContext = (BuyNowSubmitOrderContext) context;
        // 计算优惠券
        super.calculateCoupon(buildContext, dataHolder);
        // 调用父类公共的扩展处理，处理运费
        super.processBuyNowExpand(dataHolder, buildContext.getShippingAddress());

        // 计算税费，选择发票时，由前端自行计算传入税费金额，后端这里根据发票类型做计算校验
        calculateTax(dataHolder);

        // 校验价格、数量、总金额，会以页面传入的为基础，并设置错误信息
        TradeOrderValidResultEnum anyError = validatePriceAndQuantity(buildContext.getUserId(), buildContext.getShopProductList(), dataHolder.getShopProductList());
        // 如果有异常，返回页面也是用页面提交的数据，只不过设置了错误信息
        if (TradeOrderValidResultEnum.anyError(anyError)) {
            dataHolder.setShopProductList(buildContext.getShopProductList());
        } else {
            // 如果没有异常，进行优惠分摊
            splitPromotionAmount(context, dataHolder);
        }
        // 设置错误字段
        dataHolder.setAnyError(TradeOrderValidResultEnum.anyError(anyError));
        if (anyError != null) {
            dataHolder.setErrMsg(anyError.getMessage());
        }
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.SUBMIT_BUY_NOW;
    }

    @Override
    protected void extendResult(BuildResult result, BaseBuildDataHolder dataHolder) {
        result.setSuccess(!dataHolder.isAnyError());
        result.setErrMsg(dataHolder.getErrMsg());
    }


    //*******************************************************************

    /**
     * 比对店铺商品的价格和数量
     * <p>逻辑到此处，前面已经处理完满减和运费的计算了</p>
     * <AUTHOR>
     * @param originShopProductList 页面提交的数据
     * @param currentShopProductList 根据页面提交的，综合当前最新的数据，构建生成的最新的数据，这个数据对象计算了商品最新的单价和各种金额
     * boolean
     */
    /*protected boolean validatePriceAndQuantity(Long userId, List<ShopProductListBo> originShopProductList,
                                               List<ShopProductListBo> currentShopProductList) {
        Map<Long*//*shopId*//*, ShopProductListBo> currentShopProductListMap = currentShopProductList.stream()
                .collect(Collectors.toMap(k -> k.getShop().getShopId(), Function.identity(),(o1, o2)->o2));
        // 以页面提交的数据为基础，进行数据校验，并且最后将页面数据设置为返回的数据
        boolean anyError = false;
        // 遍历校验店铺数据
        for (ShopProductListBo osp : originShopProductList) {
            ShoppingCartShopBo submitShop = osp.getShop();
            // 每个店铺进行校验，以页面提交的为基础
            ShopProductListBo currentSp = currentShopProductListMap.get(submitShop.getShopId());
            // 如果店铺开启了专属，且当前用户不是专属商家，则不能提交订单
            ShoppingCartShopBo currentOrderShop = currentSp.getShop();
            if (Boolean.TRUE.equals(currentOrderShop.getWhetherShopOpenExclusiveMember()) &&
                    !Boolean.TRUE.equals(currentOrderShop.getWhetherUserBelongExclusiveMember())) {
                throw new BusinessException(String.format("当前商家不是店铺 %s 的专属商家，请移除店铺商品后重新提交", currentOrderShop.getShopName()));
            }
            boolean hasErr = validateAndSetMessageForShop(userId, osp, currentSp);
            if (hasErr) {
                anyError = true;
            }
        }
        return anyError;
    }*/

}
