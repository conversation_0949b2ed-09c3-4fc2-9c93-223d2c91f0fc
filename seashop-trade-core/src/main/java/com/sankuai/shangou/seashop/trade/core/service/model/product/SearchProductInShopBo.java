package com.sankuai.shangou.seashop.trade.core.service.model.product;

import java.math.BigDecimal;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.trade.core.service.model.UserBo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SearchProductInShopBo extends BasePageReq {

    /**
     * 用户信息
     */
    private UserBo user;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 搜索关键字，目前是针对商品名称
     */
    private String searchKey;
    /**
     * 金额区间开始，指商品原售价
     */
    private BigDecimal minPrice;
    /**
     * 金额区间结束，指商品原售价
     */
    private BigDecimal maxPrice;
    /**
     * 店铺分类id
     */
    private Long shopCategoryId;

    /**
     * 销售状态
     */
    private Integer saleStatus;

    /**
     * 审核状态
     */
    private Integer auditStatus;

}
