package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.config.SystemConfigProps;
import com.sankuai.shangou.seashop.trade.common.enums.TradeOrderValidResultEnum;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCartShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.CartBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.SubmitOrderContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 提交订单的构建器
 * <AUTHOR>
 */
@Service
@Slf4j
public class BaseSubmitOrderBuilder extends AbstractCartShopProductBuilder {

    @Resource
    private SystemConfigProps systemConfigProps;

    /**
     * 购物车提交订单，不重新查询数据库，已前端提交的为准
     * <AUTHOR>
     * @param context 数据上下文
     */
    @Override
    protected List<ShoppingCart> getShoppingCartList(BuildContext context) {
        SubmitOrderContext orderContext = (SubmitOrderContext) context;
        // 首先获取当前变更的购物车数据
        List<ShoppingCart> cartList = new ArrayList<>(200);
        int count = 0;
        for (ShopProductListBo shopOrder : orderContext.getShopProductList()) {
            for (ShopProductBo product : shopOrder.getProductList()) {
                if (product.getQuantity() == null || product.getQuantity() <= 0 ||
                        product.getProductId() == null || StrUtil.isBlank(product.getSkuId())) {
                    throw new BusinessException("商品信息异常，请检查");
                }
                ShoppingCart cart = new ShoppingCart();
                cart.setProductId(product.getProductId());
                cart.setSkuId(product.getSkuId());
                cart.setQuantity(product.getQuantity());
                cart.setUserId(product.getUserId());
                // 需要设置为true，否则不会计算商品金额
                cart.setWhetherSelect(true);
                cartList.add(cart);
                count = count + 1;

            }
        }
        int allowMaxOrderSubmitSkuCount = systemConfigProps.getAllowMaxOrderSubmitSkuCount();
        log.info("允许提交订单的最大商品数量={}, 当前商品数量={}", allowMaxOrderSubmitSkuCount, count);
        if (count > allowMaxOrderSubmitSkuCount) {
            throw new BusinessException("单次提交订单的商品数量不能超过" + allowMaxOrderSubmitSkuCount);
        }
        return cartList;
    }

    @Override
    protected CartBuildDataHolder tempHoldData(List<ShopProductBo> userShopSkuList) {
        return CartBuildDataHolder.builder().validProductList(userShopSkuList).build();
    }

    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, CartBuildDataHolder dataHolder) {
        SubmitOrderContext orderContext = (SubmitOrderContext) context;
        // 有些数据需要用页面的重置，比如页面选择了配送方式，优惠券等
        List<ShopProductListBo> originShopOrderList = orderContext.getShopProductList();
        int size = originShopOrderList.size();
        Map<Long, OrderAdditionalBo> shopAdditionalMap = new HashMap<>(size);
        Map<Long, List<ShopProductBo>> shopProductMap = new HashMap<>(size);
        for (ShopProductListBo shopOrder : originShopOrderList) {
            Long shopId = shopOrder.getShop().getShopId();
            shopAdditionalMap.put(shopId, shopOrder.getAdditional());
            shopProductMap.put(shopId, shopOrder.getProductList());
        }
        // 按店铺分组的商品信息
        Map<ShoppingCartShopBo, List<ShopProductBo>> shopGroupedMap = dataHolder.getShopGroupedMap();
        return shopGroupedMap.entrySet().stream()
                .map(entry -> {
                    // 店铺信息
                    ShoppingCartShopBo shopBo = entry.getKey();
                    // 店铺下的商品列表，数量要用页面的重置，目前的需求是即使别人改了购物车的数量不影响当前页面的提交
                    List<ShopProductBo> productList = entry.getValue();
                    Map<String, ShopProductBo> originSkuMap = shopProductMap.get(shopBo.getShopId()).stream()
                            .collect(Collectors.toMap(ShopProductBo::getSkuId, Function.identity(), (oldV, newV) -> newV));
                    Set<Long> productIdSet = new HashSet<>(productList.size());
                    for (ShopProductBo shopProductBo : productList) {
                        productIdSet.add(shopProductBo.getProductId());
                        ShopProductBo originSku = originSkuMap.get(shopProductBo.getSkuId());
                        shopProductBo.setQuantity(originSku.getQuantity());
                        // 是否选中默认设置选中，因为这里是提交
                        shopProductBo.setWhetherSelected(true);
                    }
                    // 下单逻辑中，附加信息，先取页面传入的，因为会包括配送方式，优惠券等
                    OrderAdditionalBo additional = JsonUtil.copy(shopAdditionalMap.get(shopBo.getShopId()), OrderAdditionalBo.class);
                    // 选择了优惠券时，不处理满减了
                    boolean hasCoupon = super.hasCoupon(additional);
                    // 购物车提交订单的时候，是多店铺的，所以标识需要设置到店铺
                    if (hasCoupon) {
                        shopBo.setNeedReduction(false);
                    }
                    // 对象转换
                    ShopProductListBo shopProductListBo = new ShopProductListBo();
                    shopProductListBo.setShop(shopBo);
                    shopProductListBo.setProductList(productList);
                    shopProductListBo.setAdditional(additional);
                    shopProductListBo.setProductIdList(new ArrayList<>(productIdSet));
                    return shopProductListBo;
                }).collect(Collectors.toList());
    }

    /**
     * 提交订单时，如果校验通过的话，最终要提交的数据以页面提交的为准，如果校验没通过，也是返回页面提交的数据，只不过设置错误原因
     * <AUTHOR>
     * @param context
	 * @param dataHolder
     * void
     */
    @Override
    protected void processExpand(BuildContext context, CartBuildDataHolder dataHolder) {
        SubmitOrderContext orderContext = (SubmitOrderContext) context;

        // 计算优惠券
        calculateCoupon(orderContext, dataHolder);

        // 计算并设置运费
        calculateAndSetFreight(orderContext.getShippingAddress(), dataHolder);

        // 计算税费，选择发票时，由前端自行计算传入税费金额，后端这里根据发票类型做计算校验
        calculateTax(dataHolder);

        // 校验价格、数量、总金额，会以页面传入的为基础，并设置错误信息
        TradeOrderValidResultEnum anyError = validatePriceAndQuantity(orderContext.getUserId(), orderContext.getShopProductList(), dataHolder.getShopProductList());
        // 如果有异常，返回页面也是用页面提交的数据，只不过设置了错误信息
        if (TradeOrderValidResultEnum.anyError(anyError)) {
            dataHolder.setShopProductList(orderContext.getShopProductList());
        } else {
            // 如果没有异常，进行优惠分摊
            splitPromotionAmount(context, dataHolder);
        }
        // 设置错误字段
        dataHolder.setAnyError(TradeOrderValidResultEnum.anyError(anyError));
        if (anyError != null) {
            dataHolder.setErrMsg(anyError.getMessage());
        }
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.SUBMIT_ORDER;
    }






    //********************************************************





    /**
     * 比对店铺商品的价格和数量
     * <p>逻辑到此处，前面已经处理完满减和运费的计算了</p>
     * <AUTHOR>
     * @param originShopProductList 页面提交的数据
	 * @param currentShopProductList 根据页面提交的，综合当前最新的数据，构建生成的最新的数据，这个数据对象计算了商品最新的单价和各种金额
     * boolean
     */
    /*protected boolean validatePriceAndQuantity(Long userId, List<ShopProductListBo> originShopProductList,
                                          List<ShopProductListBo> currentShopProductList) {
        Map<Long*//*shopId*//*, ShopProductListBo> currentShopProductListMap = currentShopProductList.stream()
                .collect(Collectors.toMap(k -> k.getShop().getShopId(), Function.identity(),(o1, o2)->o2));
        // 以页面提交的数据为基础，进行数据校验，并且最后将页面数据设置为返回的数据
        boolean anyError = false;
        // 遍历校验店铺数据
        for (ShopProductListBo osp : originShopProductList) {
            ShoppingCartShopBo submitShop = osp.getShop();
            // 每个店铺进行校验，以页面提交的为基础
            ShopProductListBo currentSp = currentShopProductListMap.get(submitShop.getShopId());
            // 如果店铺开启了专属，且当前用户不是专属商家，则不能提交订单
            ShoppingCartShopBo currentOrderShop = currentSp.getShop();
            if (Boolean.TRUE.equals(currentOrderShop.getWhetherShopOpenExclusiveMember()) &&
                    !Boolean.TRUE.equals(currentOrderShop.getWhetherUserBelongExclusiveMember())) {
                throw new BusinessException(String.format("当前商家不是店铺 %s 的专属商家，请移除店铺商品后重新提交", currentOrderShop.getShopName()));
            }
            boolean hasErr = validateAndSetMessageForShop(userId, osp, currentSp);
            if (hasErr) {
                anyError = true;
            }
        }
        return anyError;
    }*/

}
