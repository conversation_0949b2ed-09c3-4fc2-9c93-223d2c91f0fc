package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.sankuai.shangou.seashop.product.core.service.model.product.ProductSkuMergeBo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteLadderPriceBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteProductSkuBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteShopExclusivePriceBo;
import com.sankuai.shangou.seashop.trade.common.util.SkuUtil;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopExclusivePriceBo;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 */
@Service
public class ShoppingCartAssist {


    /**
     * 计算商品购买的基础起购数量
     * <AUTHOR>
     * @param multipleCount 倍数起购量
     * @param ladderPriceList 阶梯价，可以为空
     * int
     */
    public int calculateBaseBuyCount(int multipleCount, List<RemoteLadderPriceBo> ladderPriceList) {
        int minBuyCount = CommonConst.DEFAULT_BUY_COUNT;
        // 如果设置了阶梯价，则起购量为阶梯价的最小起购量
        if (CollectionUtils.isNotEmpty(ladderPriceList)) {
            minBuyCount = ladderPriceList.stream()
                    .mapToInt(RemoteLadderPriceBo::getMinBath)
                    .min()
                    .orElse(CommonConst.DEFAULT_BUY_COUNT);
        }
        // 综合起购倍数计算最终的起购量
        int mod = minBuyCount % multipleCount;
        // 余数为0，说明起购量就是倍数起购量的倍数，直接返回起购量就行
        if (mod == 0) {
            return minBuyCount;
        }
        return (minBuyCount / multipleCount + 1) * multipleCount;
    }

    /**
     * 平铺店铺sku的专享价，并转换成map结构
     * <p>一个店铺在同一个时间点，可能会有多个专享价活动，因为每个活动可以选择不同的商品sku；但同一个时间点，一个商品sku只会属于一个专享价活动。
     * 也就是，同一个时间点，专享价活动与商品sku是1：1的关系</p>
     * <AUTHOR>
     * @param shopExclusivePriceList
     * java.util.Map<java.lang.String,java.math.BigDecimal>
     */
    public Map<String, ShopExclusivePriceBo> flatSkuExclusivePriceAndToMap(List<RemoteShopExclusivePriceBo> shopExclusivePriceList) {
        if (CollectionUtils.isEmpty(shopExclusivePriceList)) {
            // 默认返回一个空的map，避免空指针
            return new HashMap<>(2);
        }
        return shopExclusivePriceList.stream()
                .flatMap(shopAct -> shopAct.getProductList().stream()
                        .map(product -> {
                            ShopExclusivePriceBo shopExclusivePriceBo = new ShopExclusivePriceBo();
                            shopExclusivePriceBo.setProductId(product.getProductId());
                            shopExclusivePriceBo.setSkuId(product.getSkuId());
                            shopExclusivePriceBo.setPrice(product.getPrice());
                            shopExclusivePriceBo.setActivityId(shopAct.getId());
                            shopExclusivePriceBo.setActivityName(shopAct.getName());
                            return shopExclusivePriceBo;
                        }))
                .collect(Collectors.toMap(ShopExclusivePriceBo::getSkuId, Function.identity(),(o1, o2)->o2));
    }

    /**
     * 从专享价SKU中获取对应的商品ID，用于判断设置同商品下任意一个sku是专享价的，设置专享价标识
     * <AUTHOR>
     * @param exclusivePriceMap
     * java.util.Map<java.lang.Long,java.lang.Long>
     */
    public Map<Long, Long> getExclusiveProductId(Map<String, ShopExclusivePriceBo> exclusivePriceMap) {
        if (MapUtil.isEmpty(exclusivePriceMap)) {
            return Collections.emptyMap();
        }
        return exclusivePriceMap.values().stream()
                .map(ShopExclusivePriceBo::getProductId)
                .distinct()
                .collect(Collectors.toMap(id -> id, id -> id, (o1, o2) -> o2));
    }
    public List<String> buildSkuDesc(ProductSkuMergeBo product) {
        List<String> skuNameList = new ArrayList<>();
        if (StrUtil.isNotBlank(product.getSpec1Value())) {
            skuNameList.add(SkuUtil.appendDesc(product.getSpec1Alias(), product.getSpec1Value()));
        }
        if (StrUtil.isNotBlank(product.getSpec2Value())) {
            skuNameList.add(SkuUtil.appendDesc(product.getSpec2Alias(), product.getSpec2Value()));
        }
        if (StrUtil.isNotBlank(product.getSpec3Value())) {
            skuNameList.add(SkuUtil.appendDesc(product.getSpec3Alias(), product.getSpec3Value()));
        }
        return skuNameList;
    }
    public List<String> buildSkuDesc(RemoteProductSkuBo skuBo) {
        List<String> skuNameList = new ArrayList<>();
        if (StrUtil.isNotBlank(skuBo.getSpec1Value())) {
            skuNameList.add(SkuUtil.appendDesc(skuBo.getSpec1Alias(), skuBo.getSpec1Value()));
        }
        if (StrUtil.isNotBlank(skuBo.getSpec2Value())) {
            skuNameList.add(SkuUtil.appendDesc(skuBo.getSpec2Alias(), skuBo.getSpec2Value()));
        }
        if (StrUtil.isNotBlank(skuBo.getSpec3Value())) {
            skuNameList.add(SkuUtil.appendDesc(skuBo.getSpec3Alias(), skuBo.getSpec3Value()));
        }
        return skuNameList;
    }

    public int getMultipleCount(Integer multipleCount) {
        return multipleCount == null ? CommonConst.DEFAULT_BUY_COUNT : multipleCount;
    }

}
