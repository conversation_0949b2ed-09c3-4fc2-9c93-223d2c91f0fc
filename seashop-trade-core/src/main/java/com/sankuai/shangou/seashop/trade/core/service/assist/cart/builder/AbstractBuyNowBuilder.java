package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteProductSkuBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCustomShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.ShoppingCartAssist;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ExclusiveShopDto;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractBuyNowBuilder extends AbstractCustomShopProductBuilder {

    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private ShoppingCartAssist shoppingCartAssist;


    protected List<ShopProductListBo> buildBuyNowShopProduct(Long userId, String skuId, Long quantity) {
        // 商品列表
        List<ShopProductBo> productList = buildProductList(userId, skuId, quantity);
        // 店铺信息，立即购买只会有一个商品
        Long shopId = productList.get(0).getShopId();
        ShoppingCartShopBo shop = buildOrderShop(userId, shopId, productList);
        shop.setSelectedQuantity(quantity);

        ShopProductListBo realTimeShopProductListBo = new ShopProductListBo();
        realTimeShopProductListBo.setShop(shop);
        realTimeShopProductListBo.setProductList(productList);
        realTimeShopProductListBo.setAdditional(new OrderAdditionalBo());
        List<Long> productIdList = productList.stream().map(ShopProductBo::getProductId).collect(Collectors.toList());
        realTimeShopProductListBo.setProductIdList(productIdList);
        return Collections.singletonList(realTimeShopProductListBo);
    }


    protected void processBuyNowExpand(BaseBuildDataHolder dataHolder, ShippingAddressBo shippingAddress) {
        // 计算并设置运费
        calculateAndSetFreight(shippingAddress, dataHolder);
    }


    //*******************************************************************

    private List<ShopProductBo> buildProductList(Long userId, String skuId, Long quantity) {
        // 获取SKU信息
        RemoteProductSkuBo skuBo = getProductBySkuId(skuId);
        // 构建商品列表
        List<ShopProductBo> productList = new ArrayList<>(2);
        ShopProductBo productBo = new ShopProductBo();
        productBo.setMaxBuyCount(skuBo.getMaxBuyCount());
        productBo.setShopId(skuBo.getShopId());
        productBo.setProductId(skuBo.getProductId());
        productBo.setSkuId(skuBo.getSkuId());
        productBo.setWhetherSelected(true);
        productBo.setProductName(skuBo.getProductName());
        productBo.setMainImagePath(getSkuImagePath(skuBo));
        productBo.setQuantity(quantity);
        productBo.setMeasureUnit(skuBo.getMeasureUnit());
        // 库存，SKU的库存，如果商品没有SKU，也会有一条SKU记录
        productBo.setSkuStock(skuBo.getStock());
        BigDecimal salePrice = getSkuSalePrice(skuBo);
        // 原售价，初始为商品的市场价
        productBo.setOriginSalePrice(salePrice);
        // 实际售价，初始为商品的市场价
        productBo.setRealSalePrice(salePrice);
        // 最终售价
        productBo.setFinalSalePrice(salePrice);
        // 阶梯价，商品查询的时候直接返回
        productBo.setLadderPriceList(skuBo.getLadderPriceList());
        // 最大购买量，商品表设置
        productBo.setMaxBuyCount(skuBo.getMaxBuyCount());
        // 倍数起购量
        int multipleCount = skuBo.getMultipleCount() == null ? CommonConst.DEFAULT_BUY_COUNT : skuBo.getMultipleCount();
        productBo.setMultipleCount(multipleCount);
        // 计算并设置起购量，起购量需要综合阶梯价和倍数起购量进行计算
        int minBuyCount = shoppingCartAssist.calculateBaseBuyCount(multipleCount, skuBo.getLadderPriceList());
        productBo.setMinBuyCount(minBuyCount);

        productBo.setFreightTemplateId(skuBo.getFreightTemplateId());
        productBo.setWeight(skuBo.getWeight());
        productBo.setVolume(skuBo.getVolume());
        productBo.setSaleStatus(skuBo.getSaleStatus());
        productBo.setAuditStatus(skuBo.getAuditStatus());
        productBo.setWhetherDeleted(skuBo.getWhetherDelete());
        // 设置商品规格名称列表
        List<String> skuNameList = shoppingCartAssist.buildSkuDesc(skuBo);
        productBo.setSkuNameList(skuNameList);
        // 先设置默认的专享价标识，后续会根据专享价进行重置
        productBo.setWhetherExclusive(false);
        productBo.setProductCode(skuBo.getProductCode());
        productBo.setColor(skuBo.getSpec1Value());
        productBo.setSize(skuBo.getSpec2Value());
        productBo.setVersion(skuBo.getSpec3Value());
        productBo.setSkuAutoId(skuBo.getSkuAutoId());
        productBo.setCategoryId(skuBo.getCategoryId());
        productBo.setSku(skuBo.getSkuCode());
        // 设置OE码,品牌号,替换号，零件规格等字段
        productBo.setOeCode(skuBo.getOeCode());
        productBo.setBrandCode(skuBo.getBrandCode());
        productBo.setPartSpec(skuBo.getPartSpec());
        productBo.setAdaptableCar(skuBo.getAdaptableCar());
        productBo.setBrandName(skuBo.getBrandName());
        productBo.setBrandId(skuBo.getBrandId());
        productBo.setTotalAmount(NumberUtil.multiply(productBo.getFinalSalePrice(), productBo.getQuantity()));

        productList.add(productBo);

        return productList;
    }

    private RemoteProductSkuBo getProductBySkuId(String skuId) {
        List<RemoteProductSkuBo> skuList = productRemoteService.queryProductSku(Lists.newArrayList(skuId));
        if (CollUtil.isEmpty(skuList)) {
            throw new BusinessException("商品不存在");
        }
        return skuList.get(0);
    }


    private ShoppingCartShopBo buildOrderShop(Long userId, Long shopId, List<ShopProductBo> productList) {
        // 查询店铺信息
        ShopResp shopBo = shopRemoteService.queryShopByIds(Collections.singletonList(shopId)).stream().findFirst().orElse(null);
        if (shopBo == null) {
            throw new BusinessException("当前店铺不可用");
        }

        ShoppingCartShopBo shop = new ShoppingCartShopBo();
        shop.setShopId(shopId);
        shop.setShopName(shopBo.getShopName());
        shop.setShowAddonBtn(false);
        BigDecimal totalAmount = productList.stream().map(ShopProductBo::getTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        shop.setSelectedTotalAmount(totalAmount);
        shop.setProductTotalAmount(totalAmount);
        return shop;
    }


}
