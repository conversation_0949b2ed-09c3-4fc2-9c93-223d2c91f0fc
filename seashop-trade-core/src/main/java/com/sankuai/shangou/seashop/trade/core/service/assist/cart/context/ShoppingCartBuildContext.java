package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class ShoppingCartBuildContext implements BuildContext {

    private Long userId;

    @Override
    public BuildType getBuildType() {
        return BuildType.SHOPPING_CART;
    }

    @Override
    public boolean needDiscount() {
        return true;
    }

    @Override
    public boolean needReduction() {
        return true;
    }
}
