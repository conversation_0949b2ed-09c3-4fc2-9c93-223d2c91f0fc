package com.sankuai.shangou.seashop.trade.core.service.model.cart;

import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.AddonSummaryBo;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 凑单加购返回结果。店铺和商品列表用于下次加购传入
 * <AUTHOR>
 */
@Getter
@Setter
public class AddFromAddonResultBo {

    /**
     * 凑单汇总信息
     */
    private AddonSummaryBo addonSummary;
    /**
     * 店铺信息
     */
    private ShoppingCartShopBo shop;
    /**
     * 商品列表
     */
    private List<ShopProductBo> productList;

}
