package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.promotion.thrift.core.dto.ShopOrderReductionDto;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.OrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.ProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.PromotionRecordOrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.coupon.CouponRecordOrderResp;
import com.sankuai.shangou.seashop.trade.common.constant.MessageConst;
import com.sankuai.shangou.seashop.trade.common.enums.TradeOrderValidResultEnum;
import com.sankuai.shangou.seashop.trade.common.util.FunctionUtil;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.CartBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.SubmitErpOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class SubmitErpOrderBuilder extends BaseSubmitOrderBuilder {

    private final PromotionRemoteService promotionRemoteService;

    public SubmitErpOrderBuilder(PromotionRemoteService promotionRemoteService) {
        super();
        this.promotionRemoteService = promotionRemoteService;
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.SUBMIT_ERP_ORDER;
    }

    /**
     * 提交订单时，如果校验通过的话，最终要提交的数据以页面提交的为准，如果校验没通过，也是返回页面提交的数据，只不过设置错误原因
     *
     * @param context
     * @param dataHolder void
     * <AUTHOR>
     */
    @Override
    protected void processExpand(BuildContext context, CartBuildDataHolder dataHolder) {
        SubmitErpOrderBuildContext orderContext = (SubmitErpOrderBuildContext) context;
        List<ShopProductListBo> shopProductList = orderContext.getShopProductList();
        if (orderContext.getAutoPromotion()) {
            //获取 满减 优惠券  比较 最优惠的
            PromotionRecordOrderQueryReq promotionRecordOrderQueryReq = convertPromotionRecordReq(dataHolder.getShopProductList(), orderContext.getUserId());
            if (log.isDebugEnabled()) {
                log.debug("submit erp promotion req:{}",JsonUtil.toJsonString(promotionRecordOrderQueryReq));
            }
            // 查询券 获取最优惠的 <shopId,>
            Map<Long, CouponRecordSimpleResp> couponMap = promotionRemoteService.getRecordByOrder(promotionRecordOrderQueryReq)
                    .stream()
                    .filter(couponRecordOrderResp -> CollectionUtil.isNotEmpty(couponRecordOrderResp.getRecordList()))
                    .collect(Collectors.toMap(CouponRecordOrderResp::getShopId,
                            couponRecordSimpleResp -> couponRecordSimpleResp.getRecordList().get(0), FunctionUtil.keep()));
            // 查询满减 <shopId,>
            Map<Long, ShopOrderReductionDto> reductionMap = promotionRemoteService.queryShopPromotionByOrder(promotionRecordOrderQueryReq).stream().collect(Collectors.toMap(ShopOrderReductionDto::getShopId,
                    Function.identity(), FunctionUtil.keep()));

            List<ShopProductListBo> newShopProductList = dataHolder.getShopProductList().stream()
                    .peek(shopProductListBo -> {
                        ShoppingCartShopBo shopCart = shopProductListBo.getShop();
                        CouponRecordSimpleResp useCoupon = couponMap.get(shopCart.getShopId());
                        BigDecimal couponPromotionAmount = useCoupon == null ? BigDecimal.ZERO : BigDecimal.valueOf(useCoupon.getPrice());
                        ShopOrderReductionDto useReduction = reductionMap.get(shopCart.getShopId());
                        BigDecimal reductionAmount = useReduction == null ? BigDecimal.ZERO : useReduction.getMoneyOffTotalFee();
                        OrderAdditionalBo additional = shopProductListBo.getAdditional();

                        BigDecimal promotionMaxAmount = cn.hutool.core.util.NumberUtil.max(couponPromotionAmount, reductionAmount);

                        // 不参与专享价的
                        BigDecimal calculateSkuTotalAmountBesideExclusive = dataProcessorAssist.calculateSkuTotalAmountBesideExclusive(shopProductListBo.getProductList());
                        // 要计算保留专享价的金额，后面店铺的总金额要加回去
                        BigDecimal exclusiveAmount = shopCart.getSelectedTotalAmount().subtract(calculateSkuTotalAmountBesideExclusive).setScale(2, RoundingMode.HALF_UP);

                        promotionMaxAmount = calculateSkuTotalAmountBesideExclusive.compareTo(promotionMaxAmount) > 0 ?
                                promotionMaxAmount : calculateSkuTotalAmountBesideExclusive;

                        if (useCoupon != null && couponPromotionAmount.compareTo(reductionAmount) > 0) {
                            //券优惠，放入券码
                            additional.setCouponRecordId(useCoupon.getId());
                            additional.setCouponAmount(promotionMaxAmount);
                            additional.setCouponId(useCoupon.getCouponId());
                            //非专享商品设置券ID
                            shopProductListBo.getProductList()
                                    .stream().filter(shopProductBo -> !Boolean.TRUE.equals(shopProductBo.getWhetherExclusive()))
                                    .forEach(shopProductBo -> {
                                        shopProductBo.setCouponId(useCoupon.getId());
                                    });
                        }
                        if (useReduction != null && reductionAmount.compareTo(couponPromotionAmount) >= 0) {
                            //满减优惠
                            additional.setReductionActivityId(useReduction.getActiveId());
                            additional.setReductionAmount(promotionMaxAmount);
                            additional.setReductionConditionAmount(useReduction.getMoneyOffTotalFee());
                        }
                        shopProductListBo.setAdditional(additional);

                        //重置店铺总金
                        shopCart.setSelectedTotalAmount(exclusiveAmount.add(calculateSkuTotalAmountBesideExclusive).subtract(promotionMaxAmount).setScale(2, RoundingMode.HALF_UP));

                        shopCart.setSelectedQuantity(shopProductListBo.getProductList().stream().mapToLong(ShopProductBo::getQuantity).sum());
                        shopProductListBo.setShop(shopCart);
                    }).collect(Collectors.toList());
            dataHolder.setShopProductList(newShopProductList);
        }
        if (log.isDebugEnabled()) {
            log.debug("erp submit {}", JsonUtil.toJsonString(dataHolder.getShopProductList()));
        }

        // 计算并设置运费
        calculateAndSetFreight(orderContext.getShippingAddress(), dataHolder);

        // 计算税费，选择发票时，由前端自行计算传入税费金额，后端这里根据发票类型做计算校验
        //calculateTax(dataHolder);

        // 校验价格、数量、总金额，会以页面传入的为基础，并设置错误信息
        TradeOrderValidResultEnum anyError = validatePriceAndQuantity(orderContext.getUserId(), orderContext.getShopProductList(), dataHolder.getShopProductList());
        if (log.isDebugEnabled()) {
            log.debug("erp submit valid:{} result:{}", JsonUtil.toJsonString(dataHolder.getShopProductList()), JsonUtil.toJsonString(shopProductList));
        }
        // 如果有异常，返回页面也是用页面提交的数据，只不过设置了错误信息
        if (TradeOrderValidResultEnum.anyError(anyError)) {
            dataHolder.setShopProductList(orderContext.getShopProductList());
        }
        else {
            // 预览订单不需要分摊 如果没有异常，进行优惠分摊
            splitPromotionAmount(context, dataHolder);
        }
        // 设置错误字段
        dataHolder.setAnyError(TradeOrderValidResultEnum.anyError(anyError));
        if (anyError != null) {
            dataHolder.setErrMsg(anyError.getMessage());
        }
    }


    private PromotionRecordOrderQueryReq convertPromotionRecordReq(List<ShopProductListBo> shopProductList, Long userId) {
        PromotionRecordOrderQueryReq promotionRecordOrderQueryReq = new PromotionRecordOrderQueryReq();
        promotionRecordOrderQueryReq.setUserId(userId);
        List<OrderQueryReq> orderList = shopProductList
                .stream()
                .map(shoppingCartShopProductDto -> {
                    Set<Long> productIds = new HashSet<>();
                    List<ProductQueryReq> productQueryReqs = shoppingCartShopProductDto.getProductList()
                            .stream()
                            .filter(shopProductBo -> !Boolean.TRUE.equals(shopProductBo.getWhetherExclusive()))
                            .map(shoppingCartProductDto -> {
                                ProductQueryReq productQueryReq = new ProductQueryReq();
                                productQueryReq.setProductId(shoppingCartProductDto.getProductId());
                                productQueryReq.setProductAmount(shoppingCartProductDto.getTotalAmount());
                                productIds.add(shoppingCartProductDto.getProductId());
                                return productQueryReq;
                            })
                            .collect(Collectors.toList());
                    if (productQueryReqs.isEmpty()) {
                        if (log.isDebugEnabled()) {
                            log.debug("shopId:{} 参与满减/优惠券的productId:{}", shoppingCartShopProductDto.getShop().getShopId(), productIds);
                        }
                        //全部参与了专享价 不参与满减 优惠券
                        return null;
                    }
                    OrderQueryReq orderQueryReq = new OrderQueryReq();
                    orderQueryReq.setShopId(shoppingCartShopProductDto.getShop().getShopId());
                    orderQueryReq.setProductList(productQueryReqs);
                    return orderQueryReq;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        promotionRecordOrderQueryReq.setOrderList(orderList);
        return promotionRecordOrderQueryReq;
    }

    /**
     * 比对店铺商品的价格和数量
     * <p>逻辑到此处，前面已经处理完满减和运费的计算了</p>
     *
     * @param originShopProductList  页面提交的数据
     * @param currentShopProductList 根据页面提交的，综合当前最新的数据，构建生成的最新的数据，这个数据对象计算了商品最新的单价和各种金额
     *                               boolean
     * <AUTHOR>
     */
    @Override
    protected TradeOrderValidResultEnum validatePriceAndQuantity(Long userId, List<ShopProductListBo> originShopProductList,
                                                                 List<ShopProductListBo> currentShopProductList) {
        Map<Long/*shopId*/, ShopProductListBo> currentShopProductListMap = currentShopProductList.stream()
                .collect(Collectors.toMap(k -> k.getShop().getShopId(), Function.identity(), (o1, o2) -> o2));
        // 以页面提交的数据为基础，进行数据校验，并且最后将页面数据设置为返回的数据
        TradeOrderValidResultEnum anyError = null;
        // 遍历校验店铺数据
        for (ShopProductListBo osp : originShopProductList) {
            // 每个店铺进行校验，以页面提交的为基础
            ShopProductListBo currentSp = currentShopProductListMap.get(osp.getShop().getShopId());
            TradeOrderValidResultEnum hasErr = validateAndSetMessageForShop(userId, osp, currentSp);
            if (TradeOrderValidResultEnum.anyError(hasErr)) {
                osp.getShop().setSelectedQuantity(currentSp.getShop().getSelectedQuantity());
                anyError = hasErr;
                break;
            }
        }
        return anyError;
    }

    public TradeOrderValidResultEnum validateAndSetMessageForShop(Long userId, ShopProductListBo originShopProductList, ShopProductListBo currentShopProductList) {
        Map<String, ShopProductBo> currentShopProductListMap = currentShopProductList.getProductList().stream()
                .collect(Collectors.toMap(ShopProductBo::getSkuId, Function.identity(), (o1, o2) -> o2));
        // 先每个店铺都查
        Map<Long, Long> productBuyCountMap = this.getProductBuyCount(userId, currentShopProductList.getProductIdList());
        boolean anyError = false;
        // 遍历店铺商品进行校验，以页面提交的为基础
        for (ShopProductBo origin : originShopProductList.getProductList()) {
            ShopProductBo current = currentShopProductListMap.get(origin.getSkuId());
            if (log.isDebugEnabled()) {
                log.debug("submit order origin:{}, current:{}", JsonUtil.toJsonString(origin), JsonUtil.toJsonString(current));
            }
            // 如果店铺开启了专属，且当前用户不是专属商家，则不能提交订单
            ShoppingCartShopBo currentOrderShop = currentShopProductList.getShop();
            if (Boolean.TRUE.equals(currentOrderShop.getWhetherShopOpenExclusiveMember()) &&
                    !Boolean.TRUE.equals(currentOrderShop.getWhetherUserBelongExclusiveMember())) {
                origin.setErrorMsg(String.format("当前商家不是店铺 %s 的专属商家，请移除店铺商品后重新提交", currentOrderShop.getShopName()));
                throw new BusinessException(origin.getErrorMsg());
            }

            // 商品sku不存在，或被删除，或非销售中
            if (current == null || Boolean.TRUE.equals(current.getWhetherDeleted())) {
                origin.setErrorMsg(MessageConst.PreOrder.PRODUCT_OR_PRICE_UPDATED);
                anyError = true;
                continue;
            }
            String productName = StringUtils.defaultString(current.getProductName(), StrUtil.EMPTY);
            // 页面提交的数量是否是否大于当前库存
            long quantity = origin.getQuantity();
            String unit = StrUtil.nullToEmpty(current.getMeasureUnit());
            if (quantity > current.getSkuStock()) {
                origin.setErrorMsg(productName + String.format(MessageConst.PreOrder.LACK_OF_STOCK, current.getSkuStock(), unit));
                anyError = true;
                continue;
            }
            // 页面提交的数量是否小于最新的起购量
            if (quantity < current.getMinBuyCount()) {
                origin.setErrorMsg(productName + String.format(MessageConst.PreOrder.LESS_THAN_MIN_BUY_COUNT, current.getMinBuyCount(), unit));
                anyError = true;
                continue;
            }
            long boughtCount = cn.hutool.core.util.NumberUtil.nullToZero(productBuyCountMap.get(current.getProductId()));
            // 页面提交的数量是否大于限购数
            if (needCheckMaxBuyCount(current.getMaxBuyCount()) && quantity + boughtCount > current.getMaxBuyCount()) {
                long remain = current.getMaxBuyCount() - boughtCount;
                log.info("页面提交的数量是否大于限购数, quantity={}, maxBuyCount={}, bought={}, remain={}", quantity, current.getMaxBuyCount(), boughtCount, remain);
                if (remain < 0) {
                    remain = 0;
                }
                origin.setErrorMsg(productName + String.format(MessageConst.PreOrder.OVER_MAX_BUY_COUNT,
                        current.getMaxBuyCount(), unit, boughtCount, unit, remain, unit));
                anyError = true;
                continue;
            }
            // 页面提交的数量是否满足倍数起购量
            if (quantity % current.getMultipleCount() != 0) {
                origin.setErrorMsg(productName + String.format(MessageConst.PreOrder.NOT_MATCH_MULTI_BUY_COUNT, current.getMultipleCount()));
                anyError = true;
            }
        }

        if (anyError) {
            return TradeOrderValidResultEnum.PRODUCT_FAIL;
        }
        return TradeOrderValidResultEnum.SUCCESS;
    }

    @Override
    protected List<ShoppingCart> getShoppingCartList(BuildContext context) {
        SubmitErpOrderBuildContext orderContext = (SubmitErpOrderBuildContext) context;
        return orderContext.getShopProductList().stream()
                .flatMap(shopList -> shopList.getProductList().stream())
                .map(shopProductBo -> {
                    ShoppingCart cart = new ShoppingCart();
                    cart.setProductId(shopProductBo.getProductId());
                    cart.setSkuId(shopProductBo.getSkuId());
                    cart.setQuantity(shopProductBo.getQuantity());
                    cart.setUserId(shopProductBo.getUserId());
                    // 需要设置为true，否则不会计算商品金额
                    cart.setWhetherSelect(true);
                    return cart;
                })
                .collect(Collectors.toList());

    }


}
