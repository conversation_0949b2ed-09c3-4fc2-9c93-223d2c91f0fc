package com.sankuai.shangou.seashop.trade.core.service.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class PreviewOrderSelectSkuBo {

    /**
     * 购物车主键ID
     */
    private Long id;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 购买数量
     */
    private Long quantity;
    /**
     * 原售价
     */
    //private BigDecimal originSalePrice;
    /**
     * 折扣售价
     */
    //private BigDecimal discountSalePrice;
    /**
     * 实际售价
     */
    private BigDecimal realSalePrice;
    /**
     * 最终售价。如果没有折扣，则=realSalePrice，如果有折扣，则=discountSalePrice
     */
    //private BigDecimal finalSalePrice;

}
