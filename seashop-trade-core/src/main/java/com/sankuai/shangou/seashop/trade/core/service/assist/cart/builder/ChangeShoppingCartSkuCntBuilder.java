package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCartShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.CartBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChangeShoppingCartSkuCntContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;
import com.sankuai.shangou.seashop.trade.dao.core.repository.ShoppingCartRepository;

/**
 * 变更购物车sku数量的构建器
 * <p>之所以需要构建，是因为购物车的sku数量变更后，如果购物车是勾选的，需要重新计算价格，因为可能满足新的优惠</p>
 * <p>逻辑中，虽然店铺商品的基础数据从前端传入了，但由于购买规则，比如数量等会发生变化，所以查询商品这些逻辑同样还是需要，
 * 但为了保持用户看到的数据尽可能一致，商品名称和店铺名称这种，会用前端传入的重置</p>
 * <AUTHOR>
 */
@Service
public class ChangeShoppingCartSkuCntBuilder extends AbstractCartShopProductBuilder {

    @Resource
    private ShoppingCartRepository shoppingCartRepository;

    @Override
    protected List<ShoppingCart> getShoppingCartList(BuildContext context) {
        ChangeShoppingCartSkuCntContext changeContext = (ChangeShoppingCartSkuCntContext) context;
        // 首先获取当前变更的购物车数据
        List<ShopProductBo> productList = changeContext.getProductList();
        if (CollectionUtils.isEmpty(productList) || changeContext.getId() == null) {
            throw new BusinessException("请选择要变更的商品");
        }
        List<Long> idList = productList.stream()
                .map(ShopProductBo::getId)
                .collect(Collectors.toList());
        List<ShoppingCart> cartList = shoppingCartRepository.queryByIdList(idList);
        Long userId = changeContext.getUserId();
        // 校验是否有不属于userId的数据
        boolean anyNotBelong = cartList.stream()
                .anyMatch(cart -> !cart.getUserId().equals(userId));
        if (anyNotBelong) {
            throw new BusinessException("只能操作自己的购物车数据");
        }
        return cartList;
    }

    @Override
    protected CartBuildDataHolder tempHoldData(List<ShopProductBo> userShopSkuList) {
        return CartBuildDataHolder.builder()
                .validProductList(userShopSkuList)
                .build();
    }

    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, CartBuildDataHolder dataHolder) {
        Map<ShoppingCartShopBo, List<ShopProductBo>> shopGroupedMap = dataHolder.getShopGroupedMap();
        ChangeShoppingCartSkuCntContext changeContext = (ChangeShoppingCartSkuCntContext) context;
        Map<String, ShopProductBo> selectedSkuMap = changeContext.getProductList().stream()
                .collect(Collectors.toMap(ShopProductBo::getSkuId, Function.identity(),(o1, o2)->o2));
        // 修改购物车数量时，会传入整个店铺的数据，非修改项的数据，用前端传入的重置，保持用户看到的一致
        // 修改项的部分，进行校验，再进行赋值
        return shopGroupedMap.entrySet().stream()
                .map(entry -> buildShopProductBo(changeContext, entry.getKey(), entry.getValue(), selectedSkuMap))
                .collect(Collectors.toList());
    }

    @Override
    protected void processExpand(BuildContext context, CartBuildDataHolder dataHolder) {

    }

    @Override
    public BuildType getBuildType() {
        return BuildType.CHANGE_SHOPPING_CART_SKU_CNT;
    }




    /***********************************************************/


    /**
     * 构建店铺商品数据
     * <AUTHOR>
     * @param changeContext 变更购物车的上下文
	 * @param shopBo 当前店铺数据
	 * @param productList 当前店铺商品列表
	 * @param selectedSkuMap 前端传入的商品列表
     * com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopProductBo
     */
    private ShopProductListBo buildShopProductBo(ChangeShoppingCartSkuCntContext changeContext,
                                                 ShoppingCartShopBo shopBo,
                                                 List<ShopProductBo> productList,
                                                 Map<String, ShopProductBo> selectedSkuMap) {
        // 店铺名称使用传入的，尽量保持用户看到的一致
        shopBo.setShopName(changeContext.getShop().getShopName());
        BigDecimal shopAmount = BigDecimal.ZERO;
        // 先每个店铺都查
        List<Long> productIdList = productList.stream().map(ShopProductBo::getProductId).collect(Collectors.toList());
        Map<Long, Long> productBuyCountMap = this.getProductBuyCount(changeContext.getUserId(), productIdList);
        long totalQuantity = 0;
        for (ShopProductBo sku : productList) {
            ShopProductBo selectSku = selectedSkuMap.get(sku.getSkuId());
            sku.setOriginSalePrice(selectSku.getOriginSalePrice());
            sku.setRealSalePrice(selectSku.getRealSalePrice());
            // 折扣价先清空，后续会重新计算折扣
            sku.setDiscountSalePrice(null);
            // 最终加个用原价，后续会重新计算
            sku.setFinalSalePrice(selectSku.getRealSalePrice());
            // 商品名称也是用前端传入的
            sku.setProductName(selectSku.getProductName());
            // 数量先默认为原数据
            sku.setQuantity(selectSku.getQuantity());
            // 如果是当前变更的商品，需要校验数量是否合法
            if (sku.getId().equals(changeContext.getId())) {
                // 当前修改的数据量用目标数量重置
                sku.setQuantity(changeContext.getQuantity());
                // 校验数量是否合法
                checkBuyQuantityForChange(changeContext.getQuantity(), sku, productBuyCountMap);
            }
            sku.setTotalAmount(NumberUtil.multiply(sku.getFinalSalePrice(), sku.getQuantity()).setScale(2, RoundingMode.HALF_UP));
            // 累计店铺金额
            if (Boolean.TRUE.equals(sku.getWhetherSelected())) {
                shopAmount = shopAmount.add(sku.getTotalAmount());
                totalQuantity = totalQuantity + sku.getQuantity();
            }
        }
        shopBo.setSelectedTotalAmount(shopAmount);
        shopBo.setProductTotalAmount(shopAmount);
        shopBo.setSelectedQuantity(totalQuantity);
        // 预先定义每个店铺(订单)的附加信息对象
        OrderAdditionalBo additional = new OrderAdditionalBo();

        ShopProductListBo shopProductListBo = new ShopProductListBo();
        shopProductListBo.setShop(shopBo);
        shopProductListBo.setProductList(productList);
        shopProductListBo.setAdditional(additional);
        return shopProductListBo;
    }


}
