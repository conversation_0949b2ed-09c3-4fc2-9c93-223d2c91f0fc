package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.List;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCustomShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

/**
 * 订单预览页-修改组合购数量
 * 组合购因为需要一套一套的购买，所以不支持修改数量
 * <AUTHOR>
 */
@Service
public class ChangePreviewCollocationSkuQuantityBuilder extends AbstractCustomShopProductBuilder {

    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        throw new BusinessException("组合购不支持订单预览页修改数量");
    }

    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {

    }

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_CHANGE_COLLOCATION_SKU_QUANTITY;
    }



    //*************************************************************

}
