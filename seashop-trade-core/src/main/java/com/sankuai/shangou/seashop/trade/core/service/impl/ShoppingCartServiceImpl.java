package com.sankuai.shangou.seashop.trade.core.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.trade.common.remote.UserShippingAddressRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.ShoppingCartService;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildResult;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.ShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.ShopProductBuilderFactory;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.AddonAddCartContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChangeShoppingCartSkuCntContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.PreviewBuyNowOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.PreviewCollocationOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.PreviewFlashSaleOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.PreviewOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.SelectAllContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.SelectShopContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.SelectShoppingCartSkuContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ShoppingCartBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.promotion.PromotionAssist;
import com.sankuai.shangou.seashop.trade.core.service.model.AddShoppingCartBo;
import com.sankuai.shangou.seashop.trade.core.service.model.AddonProductContext;
import com.sankuai.shangou.seashop.trade.core.service.model.ChangeShoppingCartSkuCntBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ClearInvalidShoppingCartSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.model.DeleteShoppingCartSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewBuyNowOrderParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewCollocationOrderParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewFlashSaleOrderParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewOrderBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewOrderSelectSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.model.SelectShopBo;
import com.sankuai.shangou.seashop.trade.core.service.model.SelectShoppingCartSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.UserShoppingCartBo;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.AddFromAddonBo;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.AddFromAddonResultBo;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.SelectAllBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.AddonSummaryBo;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;
import com.sankuai.shangou.seashop.trade.dao.core.repository.ShoppingCartRepository;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.PreviewOrderSelectSkuDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewOrderReq;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class ShoppingCartServiceImpl implements ShoppingCartService {

    @Resource
    private ShoppingCartRepository shoppingCartRepository;
    @Resource
    private ShopProductBuilderFactory shopProductBuilderFactory;
    @Resource
    private UserShippingAddressRemoteService userShippingAddressRemoteService;
    @Resource
    private PromotionAssist promotionAssist;


    /**
     * 获取用户的购物车列表
     * <p>购物车列表需要基于是否销售中的维度进行分组，非销售中的统一显示在列表的最下方，而销售中的sku还需要按照店铺进行分组，且每个店铺对应一个订单</p>
     * <p>这里没有查主库是考虑添加购物车到进入列表，中间的时延应该足够覆盖主从同步的时延</p>
     * <pre>
     *     1. 从购物车表中获取用户的购物车sku列表
     *     2. 从商品服务获取sku信息，关联购物车的sku，构建形成完整的sku维度的信息
     *     3. 对购物车的sku根据是否销售中进行分组，目的是减少后续需要计算价格的商品数量，只有销售中需要计算价格
     *     4. 对销售中的商品sku，根据店铺进行分组，营销活动是基于店铺的
     *     5. 对店铺的商品sku根据各种规则，计算价格和计算店铺的总金额
     *        5.1. 基础价格规则：专享价>阶梯价>商城价
     *        5.2. 营销规则：折扣会影响商品单价，且折扣是基于店铺总金额的
     * </pre>
     * <AUTHOR>
     * @param userId 登录用户ID
     */
    @Override
    public UserShoppingCartBo getUserShoppingCartList(Long userId) {
        // 初始化构建上下文
        BuildContext buildContext = new ShoppingCartBuildContext(userId);
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        if (buildResult == null) {
            return UserShoppingCartBo.defaultEmpty();
        }
        // 构建返回结果
        return UserShoppingCartBo.builder()
                .shopProductList(buildResult.getShopProductList())
                .invalidProductList(buildResult.getInvalidProductList())
                .totalAmount(buildResult.getTotalAmount())
                .whetherAllSelected(buildResult.getSummary().getWhetherAllSelected())
                .totalSelectedQuantity(buildResult.getSummary().getTotalSkuQuantity())
                .build();
    }

    @Override
    public int getUserShoppingCartCount(Long userId) {
        return shoppingCartRepository.getUserSkuCount(userId);
    }

    /**
     * 添加购物车
     * <p>添加购物车这个操作是用户在商品详情页处理好数量后，提交添加的，此时的商品数量应该是符合当前的数量规则的。
     * 而且实际下单的时候，会校验数量是否符合规则，所以添加购物车的时候，直接添加就行，没做数量校验</p>
     * <p>Notice：暂时未处理同一个账号不同地方登录同时添加和修改购物车的并发情况</p>
     * <AUTHOR>
     * @param addBo
     * void
     */
    @Override
    public void addShoppingCart(AddShoppingCartBo addBo) {
        // 添加购物车时，默认不处理选中
        saveShoppingCart(addBo.getUserId(), addBo.getProductId(), addBo.getSkuId(), addBo.getQuantity(), null);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addShoppingCartBatch(List<AddShoppingCartBo> shoppingCartList) {
        if (CollUtil.isEmpty(shoppingCartList)) {
            log.warn("【购物车】批量添加购物车，购物车列表为空");
            return;
        }
        // 先循环添加 后续改成批处理
        shoppingCartList.forEach(addBo -> saveShoppingCart(addBo.getUserId(), addBo.getProductId(), addBo.getSkuId(), addBo.getQuantity(), true));
    }

    @Override
    public void deleteShoppingCart(DeleteShoppingCartSkuBo deleteBo) {
        deleteShoppingCart(deleteBo.getIdList(), deleteBo.getUserId());
    }

    /**
     * 清除失效商品
     * <p>用户在操作的时候，为了保持用户看到的需要清除的与实际清除的一致，清除时，前端传入需要清除的ID，而且这样也能提高接口的效率。
     * 否则后端重新获取并校验有效性的话，耗时长，且有小概率发生清除的数据与用户页面看到的不一样的情况</p>
     * <AUTHOR>
     * @param clearBo
     * void
     */
    @Override
    public void clearInvalid(ClearInvalidShoppingCartSkuBo clearBo) {
        deleteShoppingCart(clearBo.getIdList(), clearBo.getUserId());
    }

    /**
     * 调整购物车sku数量
     * <p>此接口适用于购物车页面，通过按钮增减数量以及输入框直接输入数据修改数量。
     * 无论那种，前端根据基本的规则计算一个变化后的数值传入后端，后端再进行校验</p>
     * <p>由于数量的变更可能涉及优惠和折扣的变更，需要重新计算，为了不刷新整个页面，减少接口响应时间，调
     * 整时，前端传入当前整个店铺的数据，再单独传入修改项，如果是不满足条件不能修改数量的，后端返回错误码，前端不更新数据；
     * 否则，当前店铺的数据前端用后端返回的数据重置，并需要根据后端返回的当前店铺的实际金额，修改底部的总金额=原总金额-原店铺金额+现店铺金额</p>
     * <p>是否需要联合添加购物车接口考虑，同一个账号不同的地方登录，同时添加和修改购物车的并发情况</p>
     * <AUTHOR>
     * @param changeBo
     * void
     */
    @Override
    public ShopProductListBo changeShoppingCartSkuCnt(ChangeShoppingCartSkuCntBo changeBo) {
        ChangeShoppingCartSkuCntContext context = ChangeShoppingCartSkuCntContext.builder()
                .userId(changeBo.getUserId())
                .quantity(changeBo.getQuantity())
                .shop(changeBo.getShop())
                .productList(changeBo.getProductList())
                .id(changeBo.getId())
                .build();
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(context);
        // 构建修改购物车的返回数据，里面会校验数量是否符合规则
        BuildResult buildResult = builder.build(context);
        // 这种变更直接把数量设置为传入的数量
        int cnt = shoppingCartRepository.updateSkuQuantity(changeBo.getId(), changeBo.getQuantity());
        log.info("【购物车】修改购物车数量，用户ID={}，id={}，数量={}, 修改结果: {}", changeBo.getUserId(), changeBo.getId(), changeBo.getQuantity(), cnt);
        if (cnt == 0) {
            throw new BusinessException("修改购物车商品数量失败");
        }
        return buildResult.getShopProductList().get(0);
    }

    @Override
    public PreviewOrderBo previewOrder(Long userId, List<PreviewOrderSelectSkuBo> selectedSkuList) {
        // 初始化构建上下文
        PreviewOrderBuildContext buildContext = PreviewOrderBuildContext.builder()
                .userId(userId)
                .selectedSkuList(selectedSkuList)
                .build();
        // 获取用户默认的收货地址，用于计算运费和展示
        ShippingAddressBo defaultShippingAddress = userShippingAddressRemoteService.getUserDefaultShippingAddress(userId);
        buildContext.setDefaultShippingAddress(defaultShippingAddress);
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        if (buildResult == null) {
            return PreviewOrderBo.defaultEmpty();
        }

        return PreviewOrderBo.builder()
                .shopProductList(buildResult.getShopProductList())
                .totalAmount(buildResult.getTotalAmount())
                .shippingAddress(defaultShippingAddress)
                .summary(buildResult.getSummary())
                .build();
    }

    @Override
    public ShopProductListBo selectShopSku(SelectShoppingCartSkuBo selectBo) {
        SelectShoppingCartSkuContext context = SelectShoppingCartSkuContext.builder()
                .userId(selectBo.getUserId())
                .id(selectBo.getId())
                .whetherSelect(selectBo.getWhetherSelect())
                .shop(selectBo.getShop())
                .productList(selectBo.getProductList())
                .build();
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(context);
        // 构建修改购物车的返回数据，里面会校验数量是否符合规则
        BuildResult buildResult = builder.build(context);
        // 这种变更直接把数量设置为传入的数量
        int cnt = shoppingCartRepository.updateWhetherSelect(selectBo.getId(), selectBo.getWhetherSelect());
        if (cnt == 0) {
            throw new BusinessException("修改购物车选中状态失败");
        }
        // 对于选中操作而言，实际只会有当前店铺的数据，所以直接返回第一个店铺的数据就行
        return buildResult.getShopProductList().get(0);
    }

    @Override
    public void removeShoppingCart(Long userId, List<String> skuIdList) {
        shoppingCartRepository.removeByUserIdAndSku(userId, skuIdList);
    }

    @Override
    public AddFromAddonResultBo addFromAddon(AddFromAddonBo addFromAddonBo) {
        // 首先添加购物车
        saveShoppingCart(addFromAddonBo.getUserId(), addFromAddonBo.getProductId(),
                addFromAddonBo.getSkuId(), addFromAddonBo.getQuantity(), true);
        // 添加完购物车后，重新查一次最新的数据，因为可能有其他地方修改了购物车
        ShoppingCart cart = shoppingCartRepository.queryByUserIdAndSkuIdForceMaster(addFromAddonBo.getUserId(), addFromAddonBo.getSkuId());
        // 传入的已勾选的商品与加购的商品组合
        addOrUpdateAddonSelected(addFromAddonBo.getProductList(), cart);
        AddFromAddonResultBo resultBo = new AddFromAddonResultBo();
        // 店铺对象原路返回
        resultBo.setShop(addFromAddonBo.getShop());
        // 复用代码，适用构建器去获取并设置价格，构建结果处理了专享价和阶梯价
        AddonAddCartContext context = AddonAddCartContext.builder()
                .userId(addFromAddonBo.getUserId())
                .shop(addFromAddonBo.getShop())
                .productList(addFromAddonBo.getProductList())
                .build();
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(context);
        // 构建修改购物车的返回数据，里面会校验数量是否符合规则
        BuildResult buildResult = builder.build(context);
        // 只会有一个店铺，直接取值
        List<ShopProductBo> finalProductList = buildResult.getShopProductList().get(0).getProductList();
        // 组合后的商品列表返回
        resultBo.setProductList(finalProductList);
        // 处理折扣和满减
        List<Long> selectedProductIdList = new ArrayList<>();
        int skuCount = 0;
        BigDecimal selectedTotalAmount = BigDecimal.ZERO;
        for (ShopProductBo prod : finalProductList) {
            selectedProductIdList.add(prod.getProductId());
            skuCount = skuCount + 1;
            selectedTotalAmount = selectedTotalAmount.add(NumberUtil.multiply(prod.getRealSalePrice(), prod.getQuantity()));
        }
        // 构建凑单汇总
        AddonProductContext productContext = AddonProductContext.builder()
                .selectedProductIdList(selectedProductIdList)
                .selectedProductCount(skuCount)
                .selectedProductAmount(selectedTotalAmount)
                .type(addFromAddonBo.getType())
                .activityId(addFromAddonBo.getActivityId())
                .userId(addFromAddonBo.getUserId())
                .shopId(addFromAddonBo.getShop().getShopId())
                .productList(finalProductList)
                .build();
        AddonSummaryBo addonSummary = promotionAssist.buildAddonSummary(addFromAddonBo.getType(), productContext);
        resultBo.setAddonSummary(addonSummary);
        return resultBo;
    }

    @Override
    public PreviewOrderBo previewFlashSaleOrder(PreviewFlashSaleOrderParamBo paramBo) {
        Long userId = paramBo.getUserId();
        // 初始化构建上下文
        PreviewFlashSaleOrderBuildContext buildContext = PreviewFlashSaleOrderBuildContext.builder()
                .userId(userId)
                .flashSaleId(paramBo.getFlashSaleActivityId())
                .productId(paramBo.getProductId())
                .skuId(paramBo.getSkuId())
                .quantity(paramBo.getQuantity())
                .build();
        // 获取用户默认的收货地址，用于计算运费和展示
        ShippingAddressBo defaultShippingAddress = userShippingAddressRemoteService.getUserDefaultShippingAddress(userId);
        buildContext.setDefaultShippingAddress(defaultShippingAddress);
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        if (buildResult == null) {
            return PreviewOrderBo.defaultEmpty();
        }
        return PreviewOrderBo.builder()
                .shopProductList(buildResult.getShopProductList())
                .totalAmount(buildResult.getTotalAmount())
                .shippingAddress(defaultShippingAddress)
                .flashSaleId(paramBo.getFlashSaleActivityId())
                .summary(buildResult.getSummary())
                .build();
    }

    @Override
    public PreviewOrderBo previewCollocationOrder(PreviewCollocationOrderParamBo paramBo) {
        Long userId = paramBo.getUserId();
        // 初始化构建上下文
        PreviewCollocationOrderBuildContext buildContext = PreviewCollocationOrderBuildContext.builder()
                .userId(userId)
                .collocationId(paramBo.getCollocationId())
                .skuList(paramBo.getSkuList())
                .build();
        // 获取用户默认的收货地址，用于计算运费和展示
        ShippingAddressBo defaultShippingAddress = userShippingAddressRemoteService.getUserDefaultShippingAddress(userId);
        buildContext.setDefaultShippingAddress(defaultShippingAddress);
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        if (buildResult == null) {
            return PreviewOrderBo.defaultEmpty();
        }
        return PreviewOrderBo.builder()
                .shopProductList(buildResult.getShopProductList())
                .totalAmount(buildResult.getTotalAmount())
                .shippingAddress(defaultShippingAddress)
                .collocationId(paramBo.getCollocationId())
                .summary(buildResult.getSummary())
                .build();
    }

    @Override
    public PreviewOrderBo previewBuyNowOrder(PreviewBuyNowOrderParamBo paramBo) {
        Long userId = paramBo.getUserId();
        // 初始化构建上下文
        PreviewBuyNowOrderBuildContext buildContext = PreviewBuyNowOrderBuildContext.builder()
                .userId(userId)
                .productId(paramBo.getProductId())
                .skuId(paramBo.getSkuId())
                .quantity(paramBo.getQuantity())
                .build();
        // 获取用户默认的收货地址，用于计算运费和展示
        ShippingAddressBo defaultShippingAddress = userShippingAddressRemoteService.getUserDefaultShippingAddress(userId);
        buildContext.setDefaultShippingAddress(defaultShippingAddress);
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        if (buildResult == null) {
            return PreviewOrderBo.defaultEmpty();
        }
        return PreviewOrderBo.builder()
                .shopProductList(buildResult.getShopProductList())
                .totalAmount(buildResult.getTotalAmount())
                .shippingAddress(defaultShippingAddress)
                .whetherBuyNow(true)
                .summary(buildResult.getSummary())
                .build();
    }

    @Override
    public ShopProductListBo selectShop(SelectShopBo selectBo) {
        SelectShopContext context = SelectShopContext.builder()
                .userId(selectBo.getUserId())
                .whetherSelect(selectBo.getWhetherSelect())
                .shop(selectBo.getShop())
                .productList(selectBo.getProductList())
                .build();
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(context);
        // 构建修改购物车的返回数据，里面会校验数量是否符合规则
        BuildResult buildResult = builder.build(context);
        // 最后修改购物车
        List<Long> idList = selectBo.getProductList().stream()
                .map(ShopProductBo::getId)
                .collect(Collectors.toList());
        // 这种变更直接把数量设置为传入的数量
        boolean cnt = shoppingCartRepository.updateWhetherSelectBatch(idList, selectBo.getWhetherSelect());
        if (!cnt) {
            throw new BusinessException("修改购物车选中状态失败");
        }
        // 对于选中操作而言，实际只会有当前店铺的数据，所以直接返回第一个店铺的数据就行
        return buildResult.getShopProductList().get(0);
    }

    @Override
    public UserShoppingCartBo selectAll(SelectAllBo selectBo) {
        SelectAllContext context = SelectAllContext.builder()
                .userId(selectBo.getUserId())
                .whetherSelect(selectBo.getWhetherSelect())
                .shopProductList(selectBo.getShopProductList())
                .build();
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(context);
        // 构建修改购物车的返回数据，里面会校验数量是否符合规则
        BuildResult buildResult = builder.build(context);
        // 最后修改购物车
        List<Long> idList = selectBo.getShopProductList().stream()
                .flatMap(sp -> sp.getProductList().stream().
                        map(ShopProductBo::getId))
                .collect(Collectors.toList());
        // 这种变更直接把数量设置为传入的数量
        boolean cnt = shoppingCartRepository.updateWhetherSelectBatch(idList, selectBo.getWhetherSelect());
        if (!cnt) {
            throw new BusinessException("修改购物车选中状态失败");
        }
        // 构建返回结果
        return UserShoppingCartBo.builder()
                .shopProductList(buildResult.getShopProductList())
                .totalAmount(buildResult.getTotalAmount())
                .build();
    }

    @Override
    public void checkCanSubmit(PreviewOrderReq previewOrderReq) {
        List<PreviewOrderSelectSkuDto> selectedSkuList = previewOrderReq.getSelectedSkuList();
        if (CollUtil.isEmpty(selectedSkuList)) {
            throw new BusinessException("购物车数据有变化，请刷新重试");
        }
        // 首先获取勾选的购物车sku列表
        List<Long> idList = selectedSkuList.stream()
            .map(PreviewOrderSelectSkuDto::getId)
            .collect(Collectors.toList());
        List<ShoppingCart> cartList = shoppingCartRepository.queryByIdList(idList);
        if (CollUtil.isEmpty(cartList)) {
            throw new BusinessException("当前商品已被提交，请检查");
        }
        if (idList.size() != cartList.size()) {
            throw new BusinessException("购物车有部分商品已被提交，请检查");
        }
        Map<Long, ShoppingCart> cartMap = cartList.stream().collect(Collectors.toMap(ShoppingCart::getId, Function.identity(), (k1, k2) -> k2));
        selectedSkuList.forEach(sku -> {
            if (sku.getQuantity() == null || sku.getQuantity() <= 0) {
                throw new BusinessException("商品数量不能为空");
            }
            ShoppingCart cart = cartMap.get(sku.getId());
            // 被别人提交了
            if (cart == null) {
                throw new BusinessException("购物车数据有变化，请刷新重试");
            }
            // 数量被别人修改了
            if (!sku.getQuantity().equals(cart.getQuantity())) {
                throw new BusinessException("购物车数据有变化，请刷新重试");
            }
            // 选中状态被修改了
            if (!Boolean.TRUE.equals(cart.getWhetherSelect())) {
                throw new BusinessException("购物车数据有变化，请刷新重试");
            }
        });
    }


    /****************************************非对外方法*****************************/


    private void deleteShoppingCart(List<Long> idList, Long userId) {
        if (CollUtil.isEmpty(idList)) {
            throw new BusinessException("购物车ID不能为空");
        }
        // 校验传入的ID是否有不是userId的数据
        List<ShoppingCart> shoppingCartLit = MybatisUtil.queryBatch(ids -> shoppingCartRepository.queryByIdList(ids), idList);
        if (CollectionUtils.isEmpty(shoppingCartLit)) {
            throw new BusinessException("购物车数据不存在");
        }
        // 校验是否有不属于userId的数据
        boolean anyNotBelong = shoppingCartLit.stream()
                .anyMatch(cart -> !cart.getUserId().equals(userId));
        if (anyNotBelong) {
            throw new BusinessException("只能删除自己的购物车数据");
        }
        int cnt = shoppingCartRepository.deleteByIdList(userId, idList);
        log.info("【购物车】删除购物车，用户ID={}，idList={}，结果: {}", userId, idList, cnt);
        if (cnt != idList.size()) {
            throw new BusinessException("删除购物车失败");
        }
    }

    private void saveShoppingCart(Long userId, Long productId, String skuId, Long quantity, Boolean selected) {
        ShoppingCart shoppingCart = shoppingCartRepository.queryByUserIdAndSkuIdForceMaster(userId, skuId);
        Date now = new Date();
        if (shoppingCart == null) {
            shoppingCart = new ShoppingCart();
            shoppingCart.setProductId(productId);
            shoppingCart.setSkuId(skuId);
            shoppingCart.setUserId(userId);
            shoppingCart.setQuantity(quantity);
            shoppingCart.setAddTime(now);
            shoppingCart.setCreateTime(now);
            shoppingCart.setUpdateTime(now);
            shoppingCart.setWhetherSelect(selected);
            int cnt = shoppingCartRepository.insert(shoppingCart);
            log.info("【购物车】新添加购物车，用户ID={}，skuId={}，数量={}，结果: {}", userId, skuId, quantity, cnt);
            if (cnt == 0) {
                throw new BusinessException("添加购物车失败");
            }
        } else {
            int cnt = shoppingCartRepository.increaseSkuQuantityAndOverSelect(shoppingCart.getId(), quantity, selected);
            log.info("【购物车】修改购物车，用户ID={}，skuId={}，数量={}，结果: {}", userId, skuId, quantity, cnt);
            if (cnt == 0) {
                throw new BusinessException("添加购物车失败");
            }
        }
    }

    /**
     * 对传入的购物车数据以及新添加的组合起来
     * <p>如果加购的是购物车已有的，将最新的数量覆盖；否则用加购的数量</p>
     */
    private void addOrUpdateAddonSelected(List<ShopProductBo> productList, ShoppingCart cart) {
        // 如果是加购的，需要把加购的数量覆盖到购物车的数量上
        Optional<ShopProductBo> optional = productList.stream()
                .filter(product -> product.getSkuId().equals(cart.getSkuId()))
                .findFirst();
        if (optional.isPresent()) {
            ShopProductBo product = optional.get();
            product.setQuantity(cart.getQuantity());
            product.setWhetherSelected(cart.getWhetherSelect());
        } else {
            ShopProductBo product = new ShopProductBo();
            product.setId(cart.getId());
            product.setProductId(cart.getProductId());
            product.setSkuId(cart.getSkuId());
            product.setQuantity(cart.getQuantity());
            product.setWhetherDeleted(cart.getWhetherSelect());
            productList.add(product);
        }
    }


}
