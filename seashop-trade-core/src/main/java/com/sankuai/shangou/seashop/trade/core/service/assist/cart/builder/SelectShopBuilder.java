package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCustomShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.SelectShopContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;

import cn.hutool.core.collection.CollUtil;

/**
 * 勾选店铺构建器
 * <p>之所以需要构建，是因为购物车的sku数量变更后，如果购物车是勾选的，需要重新计算价格，因为可能满足新的优惠</p>
 * <p>逻辑中，虽然店铺商品的基础数据从前端传入了，但由于购买规则，比如数量等会发生变化，所以查询商品这些逻辑同样还是需要，
 * 但为了保持用户看到的数据尽可能一致，商品名称和店铺名称这种，会用前端传入的重置</p>
 * <AUTHOR>
 */
@Service
public class SelectShopBuilder extends AbstractCustomShopProductBuilder {


    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        // 基于页面传入的数据构建返回，勾选唯一会变化的是，根据优惠重新计算总金额，价格以页面看到的为准
        SelectShopContext buildContext = (SelectShopContext) context;
        ShoppingCartShopBo shop = buildContext.getShop();
        List<ShopProductBo> productList = buildContext.getProductList();
        if (shop == null || CollUtil.isEmpty(productList)) {
            throw new InvalidParamException("请选择店铺和商品");
        }

        boolean whetherSelect = buildContext.getWhetherSelect();
        BigDecimal totalAmount = BigDecimal.ZERO;
        long totalQuantity = 0;
        for (ShopProductBo sku : productList) {
            // 价格先用原价重置，后续会重新计算
            sku.setFinalSalePrice(sku.getRealSalePrice());

            sku.setWhetherSelected(whetherSelect);
            // 这里是原价计算
            BigDecimal amount = NumberUtil.multiply(sku.getFinalSalePrice(), sku.getQuantity()).setScale(2, RoundingMode.HALF_UP);
            sku.setTotalAmount(amount);
            if (Boolean.TRUE.equals(whetherSelect)) {
                totalAmount = totalAmount.add(amount);
                totalQuantity = totalQuantity + sku.getQuantity();
            }
        }
        shop.setWhetherSelected(whetherSelect);
        shop.setSelectedTotalAmount(totalAmount);
        shop.setProductTotalAmount(totalAmount);
        shop.setSelectedQuantity(totalQuantity);
        // 店铺选中，则用传入的重置
        shop.setWhetherSelected(whetherSelect);

        // 重置营销
        shop.setPromotionDescList(new ArrayList<>(5));
        shop.setReductionDescList(new ArrayList<>(5));

        // 预先定义每个店铺(订单)的附加信息对象
        OrderAdditionalBo additional = new OrderAdditionalBo();

        ShopProductListBo shopProductListBo = new ShopProductListBo();
        shopProductListBo.setShop(shop);
        shopProductListBo.setProductList(productList);
        shopProductListBo.setAdditional(additional);
        return Collections.singletonList(shopProductListBo);
    }

    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {

    }

    @Override
    public BuildType getBuildType() {
        return BuildType.SELECT_SHOP;
    }




    /***********************************************************/


}
