package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.util.List;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.model.PurchaseProductBo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PreviewCollocationOrderBuildContext implements BuildContext {

    /**
     * 商家用户ID
     */
    private Long userId;
    /**
     * 组合购活动ID
     */
    private Long collocationId;
    /**
     * 勾选购买的组合购商品列表
     */
    private List<PurchaseProductBo> skuList;
    /**
     * 用户默认的收货地址
     */
    private ShippingAddressBo defaultShippingAddress;

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_COLLOCATION;
    }

    @Override
    public boolean needDiscount() {
        return false;
    }

    @Override
    public boolean needReduction() {
        return true;
    }

    // 组合购商品参与使用优惠券、满减、折扣活动
    @Override
    public boolean needPromotion() {
        return true;
    }

    @Override
    public boolean needExclusivePrice() {
        return false;
    }

    @Override
    public boolean needLadderPrice() {
        return false;
    }
}
