package com.sankuai.shangou.seashop.product.core.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.product.core.service.ShopBrandService;
import com.sankuai.shangou.seashop.product.thrift.core.ShopBrandCmdFeign;
import com.sankuai.shangou.seashop.product.thrift.core.request.brand.CreateDefaultShopBrandReq;

/**
 * 店铺品牌操作控制器
 *
 * <AUTHOR>
 * @date 2023/11/13 11:06
 */
@RestController
@RequestMapping("/shopBrand")
public class ShopBrandCmdController implements ShopBrandCmdFeign {

    @Resource
    private ShopBrandService shopBrandService;

    @Override
    @PostMapping(value = "/createDefaultShopBrand", consumes = "application/json")
    public ResultDto<BaseResp> createDefaultShopBrand(@RequestBody CreateDefaultShopBrandReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createDefaultShopBrand", request, req -> {
            shopBrandService.createDefaultShopBrand(req.getShopId());
            return new BaseResp();
        });
    }
}
