package com.sankuai.shangou.seashop.product.core.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.product.core.service.model.ShopBrandBo;
import com.sankuai.shangou.seashop.product.core.service.model.ShopBrandQueryBo;

/**
 * <AUTHOR>
 * @date 2023/11/08 11:27
 */
public interface ShopBrandService {

    /**
     * 查询商家品牌列表
     *
     * @param pageParam 分页参数
     * @param queryBo   查询条件
     * @return 商家品牌列表
     */
    BasePageResp<ShopBrandBo> pageShopBrand(BasePageParam pageParam, ShopBrandQueryBo queryBo);

    /**
     * 查询商家品牌列表
     *
     * @param queryBo 查询条件
     * @return 商家品牌列表
     */
    List<ShopBrandBo> listShopBrand(ShopBrandQueryBo queryBo);

    /**
     * 为新创建的店铺创建默认品牌关联
     *
     * @param shopId 店铺ID
     */
    void createDefaultShopBrand(Long shopId);
}
