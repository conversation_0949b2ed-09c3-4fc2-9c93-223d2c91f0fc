package com.sankuai.shangou.seashop.trade.core.service.model;

import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteDiscountBo;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AddonProductContext {

    /**
     * 选中的商品ID列表，商品从购物车传入。
     * 一是购物车没有店铺ID，如果要计算店铺数据，还需要查一次商品；二是保持用户页面跳转看到的数据一致
     */
    private List<Long> selectedProductIdList;
    /**
     * 商品列表
     */
    private List<ShopProductBo> productList;
    /**
     * 选中的商品SKU数量
     */
    private int selectedProductCount;
    /**
     * 选中的商品总金额
     */
    private BigDecimal selectedProductAmount;
    /**
     * 折扣是否适用于全部商品
     */
    private Boolean discountForAll;
    /**
     * 当前的折扣活动
     * 选择的活动类型是某个折扣时有值
     */
    private RemoteDiscountBo shopDiscount;
    /**
     * 当前的凑单类型。1：折扣；2：满减
     */
    private Integer type;
    /**
     * 当前的活动ID
     */
    private Long activityId;
    private Long shopId;
    private Long userId;

}
