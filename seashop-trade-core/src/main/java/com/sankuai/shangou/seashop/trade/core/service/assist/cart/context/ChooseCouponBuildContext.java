package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.util.List;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ChooseCouponBuildContext implements BuildContext {

    private Long userId;
    /**
     * 店铺信息
     */
    private ShoppingCartShopBo shop;
    /**
     * 商品列表
     */
    private List<ShopProductBo> productList;
    /**
     * 订单附加信息
     */
    private OrderAdditionalBo additional;
    /**
     * 优惠券记录ID
     */
    private Long couponId;
    private ShippingAddressBo shippingAddress;

    // 单独设置这个字段，是用于去掉优惠券选择时需要重新计算满减，要重置
    private Boolean needReduction;


    @Override
    public Long getUserId() {
        return this.userId;
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.CHOOSE_COUPON;
    }

    @Override
    public boolean needDiscount() {
        return false;
    }

    @Override
    public boolean needReduction() {
        if (needReduction == null) {
            return true;
        }
        return Boolean.TRUE.equals(needReduction);
    }

    @Override
    public boolean needReCalPrice() {
        return false;
    }

    public void resetNeedReduction(boolean needReduction) {
        this.needReduction = needReduction;
    }
}
