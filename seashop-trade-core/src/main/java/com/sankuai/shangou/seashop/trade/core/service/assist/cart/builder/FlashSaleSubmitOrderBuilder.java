package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.enums.FlashSaleLimitTypeEnum;
import com.sankuai.shangou.seashop.trade.common.enums.TradeOrderValidResultEnum;
import com.sankuai.shangou.seashop.trade.common.remote.Order2TradeRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteFlashSaleDetailBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildResult;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.FlashSaleSubmitOrderContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

/**
 * 限时购提交订单的构建器
 * <AUTHOR>
 */
@Service
public class FlashSaleSubmitOrderBuilder extends AbstractFlashSaleBuilder {

    @Resource
    private Order2TradeRemoteService orderRemoteService;

    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        FlashSaleSubmitOrderContext previewOrderBuildContext = (FlashSaleSubmitOrderContext) context;

        Long userId = previewOrderBuildContext.getUserId();
        // 限时购ID
        Long flashSaleId = previewOrderBuildContext.getFlashSaleId();
        // 限时购只会提交一个店铺的一个商品SKU
        ShopProductListBo shopProductListBo = previewOrderBuildContext.getShopProductList().get(0);
        ShopProductBo shopProductBo = shopProductListBo.getProductList().get(0);

        Long productId = shopProductBo.getProductId();
        String skuId = shopProductBo.getSkuId();
        Long quantity = shopProductBo.getQuantity();

        // 下单逻辑中，附加信息，先取页面传入的，因为会包括配送方式，优惠券等
        OrderAdditionalBo additional = JsonUtil.copy(shopProductListBo.getAdditional(), OrderAdditionalBo.class);
        // 选择了优惠券时，不处理满减了
        boolean hasCoupon = super.hasCoupon(additional);
        if (hasCoupon) {
            previewOrderBuildContext.resetNeedReduction(false);
        }
        // 调用父类公共的构建方法
        List<ShopProductListBo> currentShopOrderList = buildFlashSaleShopProduct(context, userId, flashSaleId, productId, skuId, quantity);
        currentShopOrderList.get(0).setAdditional(additional);
        currentShopOrderList.get(0).setProductIdList(Collections.singletonList(productId));
        return currentShopOrderList;
    }

    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {
        FlashSaleSubmitOrderContext buildContext = (FlashSaleSubmitOrderContext) context;

        // 计算优惠券
        calculateCoupon(context, dataHolder);
        // 调用父类公共的扩展处理，处理优惠券、满减和运费
        super.processFlashSaleExpand(dataHolder, buildContext.getShippingAddress());

        // 计算税费，选择发票时，由前端自行计算传入税费金额，后端这里根据发票类型做计算校验
        calculateTax(dataHolder);

        // 校验价格、数量、总金额，会以页面传入的为基础，并设置错误信息
        TradeOrderValidResultEnum anyError = validatePriceAndQuantity(buildContext.getUserId(), buildContext.getShopProductList(), dataHolder.getShopProductList());
        // 如果有异常，返回页面也是用页面提交的数据，只不过设置了错误信息
        if (TradeOrderValidResultEnum.anyError(anyError)) {
            dataHolder.setShopProductList(buildContext.getShopProductList());
        } else {
            // 如果没有异常，进行优惠分摊
            splitPromotionAmount(context, dataHolder);
        }
        // 设置错误字段
        dataHolder.setAnyError(TradeOrderValidResultEnum.anyError(anyError));
        if (anyError != null) {
            dataHolder.setErrMsg(anyError.getMessage());
        }
    }

    @Override
    protected void checkFlashSale(BuildContext context, RemoteFlashSaleDetailBo flashSale) {
        FlashSaleSubmitOrderContext buildContext = (FlashSaleSubmitOrderContext) context;
        // 限时购只会提交一个店铺的一个商品SKU
        ShopProductListBo shopProductListBo = buildContext.getShopProductList().get(0);
        ShopProductBo shopProductBo = shopProductListBo.getProductList().get(0);

        Long userId = buildContext.getUserId();
        Long flashSaleId = buildContext.getFlashSaleId();
        Long quantity = shopProductBo.getQuantity();

        Integer limitType = flashSale.getLimitType();
        Integer totalCount = flashSale.getTotalCount();
        Integer limitCount = flashSale.getLimitCount();
        // 商品维度，只需要校验每个人的限购数量
        if (FlashSaleLimitTypeEnum.PRODUCT.getType().equals(limitType)) {
            // 商品维度已经购买的数量
            Long count = orderRemoteService.countFlashSaleByProduct(userId, flashSaleId, shopProductBo.getProductId());
            // limitCount=0代表不限制
            if (limitCount != null && limitCount != 0 && count != null && count + quantity > limitCount) {
                throw new BusinessException("超过限时购限购数量");
            }
        }
        // sku维度，校验库存和每个人的限购数量
        else if (FlashSaleLimitTypeEnum.SKU.getType().equals(limitType)) {
            if (totalCount == null || quantity > totalCount) {
                throw new BusinessException("超过限时购限购数量");
            }
            // sku维度已购买数量
            Long count = orderRemoteService.countFlashSaleBySku(userId, flashSaleId, shopProductBo.getSkuId());
            // limitCount=0代表不限制
            if (limitCount != null && limitCount != 0 && count != null && count + quantity > limitCount) {
                throw new BusinessException("超过限时购限购数量");
            }
        } else {
            throw new BusinessException("限时购类型错误");
        }
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.SUBMIT_FLASH_SALE_ORDER;
    }

    @Override
    protected void extendResult(BuildResult result, BaseBuildDataHolder dataHolder) {
        result.setSuccess(!dataHolder.isAnyError());
        result.setErrMsg(dataHolder.getErrMsg());
    }



    //*******************************************************************


}
