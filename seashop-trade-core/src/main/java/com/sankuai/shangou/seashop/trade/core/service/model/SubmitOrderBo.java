package com.sankuai.shangou.seashop.trade.core.service.model;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class SubmitOrderBo {

    /**
     * 用户ID
     */
    private UserBo user;
    /**
     * 页面防重复提交token
     */
    private String submitToken;
    /**
     * 收货地址
     */
    private Long shippingAddressId;
    /**
     * 传入的地址对象
     */
    private ShippingAddressBo shippingAddress;
    /**
     * 支付方式
     */
    private Integer payType;
    /**
     * 所有店铺总金额
     */
    private BigDecimal totalAmount;
    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;
    /**
     * 限时购活动id，限时购活动提交时字段有值
     */
    private Long flashSaleId;
    /**
     * 组合购活动id，组合购活动提交时字段有值
     */
    private Long collocationId;
    /**
     * 订单平台，0：PC；2：小程序
     */
    private Integer platform;
    /**
     * 是否立即购买
     */
    private Boolean whetherBuyNow;

}
