package com.sankuai.shangou.seashop.trade.core.service.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundResp;
import com.sankuai.shangou.seashop.trade.common.remote.FinanceRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.trade.core.service.ApiCashDepositRefundService;
import com.sankuai.shangou.seashop.trade.core.service.model.ApiShopSimpleModel;
import com.sankuai.shangou.seashop.trade.thrift.core.request.finance.ApiCashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.finance.ApiCashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.finance.ApiCashDepositRefundResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@Service
@Slf4j
public class ApiCashDepositRefundServiceImpl implements ApiCashDepositRefundService {

    @Resource
    private FinanceRemoteService financeRemoteService;
    @Resource
    private ShopRemoteService shopRemoteService;

    @Override
    public BasePageResp<ApiCashDepositRefundResp> refundList(ApiCashDepositRefundQueryReq request) {

        List<ApiShopSimpleModel> shopSimpleModels = new ArrayList<>();

        ShopSimpleQueryReq shopSimpleQueryReq = new ShopSimpleQueryReq();
        if (request.getShopId() != null || StrUtil.isNotBlank(request.getShopName())) {
            shopSimpleQueryReq.setShopIdList(Arrays.asList(request.getShopId()));
            shopSimpleQueryReq.setShopName(request.getShopName());
            ShopSimpleListResp shopSimpleListResp = shopRemoteService.querySimpleList(shopSimpleQueryReq);
            if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            shopSimpleModels = JsonUtil.copyList(shopSimpleListResp.getList(), ApiShopSimpleModel.class);
        }

        List<Long> shopIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(shopSimpleModels)) {
            // 拿到过滤后的店铺id
            shopIds = shopSimpleModels.stream().map(ApiShopSimpleModel::getId).collect(Collectors.toList());
        }

        CashDepositRefundQueryReq cashDepositRefundQueryReq = JsonUtil.copy(request, CashDepositRefundQueryReq.class);
        cashDepositRefundQueryReq.setShopIdList(shopIds);
        cashDepositRefundQueryReq.setStatus(request.getStatus());
        BasePageResp<CashDepositRefundResp> cashDepositRefundPage = financeRemoteService.queryCashDepositRefundList(cashDepositRefundQueryReq);
        if (null == cashDepositRefundPage || CollUtil.isEmpty(cashDepositRefundPage.getData())) {
            return PageResultHelper.defaultEmpty(request.buildPage());
        }

        List<CashDepositRefundResp> cashDepositRefundList = cashDepositRefundPage.getData();
        if (CollUtil.isEmpty(shopSimpleModels)) {
            // 没数据说明之前没查过店铺信息，需要去查一下店铺信息
            shopIds = cashDepositRefundList.stream().map(CashDepositRefundResp::getShopId).collect(Collectors.toList());
            shopSimpleQueryReq.setShopIdList(shopIds);
            ShopSimpleListResp shopSimpleListResp = shopRemoteService.querySimpleList(shopSimpleQueryReq);
            shopSimpleModels = JsonUtil.copyList(shopSimpleListResp.getList(), ApiShopSimpleModel.class);
        }

        final List<ApiShopSimpleModel> shopSimpleModelsFinal = shopSimpleModels;

        return PageResultHelper.transfer(cashDepositRefundPage, ApiCashDepositRefundResp.class,
            apiCashDepositRefundResp -> {
                // 设置店铺名称
                if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
                    ApiShopSimpleModel apiShopSimpleModel = shopSimpleModelsFinal.stream().filter(shopSimpleModel -> shopSimpleModel.getId().equals(apiCashDepositRefundResp.getShopId())).findFirst().orElse(null);
                    if (null != apiShopSimpleModel) {
                        apiCashDepositRefundResp.setShopName(apiShopSimpleModel.getShopName());
                    }
                }
            }
        );
    }

    @Override
    public ApiCashDepositRefundDetailResp refundDetail(Long id) {
        return JsonUtil.copy(financeRemoteService.refundDetail(id), ApiCashDepositRefundDetailResp.class);
    }
}
