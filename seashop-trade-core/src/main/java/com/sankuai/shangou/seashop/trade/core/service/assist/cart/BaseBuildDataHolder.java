package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

import java.util.List;
import java.util.Map;

import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteShopUserPromotionBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * 临时数据存储对象，没有区分业务用途，按需设置使用
 * <AUTHOR>
 */
@Getter
@Setter
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class BaseBuildDataHolder {

    /**
     * LIST格式的，按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;


    /**
     * MAP格式的，按店铺分组的商品列表
     */
    private Map<ShoppingCartShopBo, List<ShopProductBo>> shopGroupedMap;
    /**
     * 店铺营销信息
     */
    private Map<Long/*shopId*/, RemoteShopUserPromotionBo> shopPromotionMap;
    /**
     * 数据处理过程中，是否任意一个校验失败
     */
    private boolean anyError;
    private String errMsg;

}
