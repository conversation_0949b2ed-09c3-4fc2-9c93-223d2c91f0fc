package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteProductSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCustomShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChooseShippingAddressContext;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

/**
 * 选择收货地址的构建器
 * <AUTHOR>
 */
@Service
public class ChooseShippingAddressBuilder extends AbstractCustomShopProductBuilder {

    @Resource
    private ProductRemoteService productRemoteService;

    /**
     * 这里的构建店铺商品，完全基于页面传入的数据，不再需要查询数据库，因为整体逻辑只需要计算运费，保持用户看到的前后一致
     * <AUTHOR>
     * @param context 构建上下文
	 * @param dataHolder 数据临时存储对象
     */
    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        ChooseShippingAddressContext addressContext = (ChooseShippingAddressContext) context;
        List<ShopProductListBo> shopProductList = addressContext.getShopProductList();
        // 获取商品SKU信息
        List<String> skuIdList = shopProductList.stream()
                .flatMap(so -> so.getProductList().stream().map(ShopProductBo::getSkuId))
                .collect(Collectors.toList());
        Map<String, RemoteProductSkuBo> skuMap = productRemoteService.queryProductSkuToMap(skuIdList);
        shopProductList.forEach(so -> {
            so.getProductList().forEach(sku -> {
                RemoteProductSkuBo skuBo = skuMap.get(sku.getSkuId());
                if (skuBo == null) {
                    throw new BusinessException("有商品已下架，请刷新购物车后重试");
                }
                sku.setFreightTemplateId(skuBo.getFreightTemplateId());
                sku.setWeight(skuBo.getWeight());
                sku.setVolume(skuBo.getVolume());
            });
        });

        return shopProductList;
    }


    /**
     * 提交订单时，如果校验通过的话，最终要提交的数据以页面提交的为准，如果校验没通过，也是返回页面提交的数据，只不过设置错误原因
     * <AUTHOR>
     * @param context
	 * @param dataHolder
     * void
     */
    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {
        ChooseShippingAddressContext addressContext = (ChooseShippingAddressContext) context;
        // 计算并设置运费
        calculateAndSetFreight(addressContext.getShippingAddress(), dataHolder);
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.CHOOSE_SHIPPING_ADDRESS;
    }






    //********************************************************



}
