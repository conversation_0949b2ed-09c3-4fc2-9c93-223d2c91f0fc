package com.sankuai.shangou.seashop.trade.core.service.model.promotion;

import lombok.*;

/**
 * 营销对象
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class PromotionBo {

    /**
     * 营销活动ID
     */
    private Long promotionId;
    /**
     * 营销活动名称
     */
    private String promotionName;
    /**
     * 营销活动类型
     */
    private String promotionType;
    /**
     * 营销活动类型描述
     */
    private String promotionTypeDesc;
    /**
     * 满足营销活动的条件描述
     */
    private String matchConditionDesc;
    /**
     * 满足营销活动的值描述
     */
    private String promotionValueDesc;

}
