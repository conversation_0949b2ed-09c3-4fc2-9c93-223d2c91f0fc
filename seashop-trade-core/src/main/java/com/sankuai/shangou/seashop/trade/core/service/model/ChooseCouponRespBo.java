package com.sankuai.shangou.seashop.trade.core.service.model;

import lombok.*;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ChooseCouponRespBo {

    /**
     * 当前店铺以及商品列表
     */
    private ShopProductListBo shopProduct;
    /**
     * 预览订单页所有店铺以及对应商品总金额
     */
    private BigDecimal totalAmount;
    /**
     * 限时购活动id。如果不为空，代表是限时购，商品列表只会有一条记录
     */
    private Long flashSaleId;

}
