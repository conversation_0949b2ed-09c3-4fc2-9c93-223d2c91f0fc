package com.sankuai.shangou.seashop.trade.core.service.model.product;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * 查询到的交易商品基本信息对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
public class SearchedTradeProductBo {

    /**
     * 商品编码
     */
    private String productCode;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 销量
     */
    private Long saleCount;
    /**
     * 虚拟销量
     */
    private Long virtualSaleCounts;

    /**
     * 总销量 实际销量 + 虚拟销量
     */
    private Long totalSaleCounts;
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 商品所有图片地址，多个图片地址用逗号分隔
     */
    private String allImagePath;
    /**
     * 品牌ID
     */
    private Long brandId;
    /**
     * 分类ID
     */
    private Long categoryId;
    /**
     * 分类全路径
     */
    private String categoryPath;
    /**
     * 市场价
     */
    private BigDecimal marketPrice;
    /**
     * 原售价，不考虑任何营销前的，sku的最小售价
     */
    private BigDecimal minSalePrice;
    /**
     * 售价
     * 1. 默认原售价，即minSalePrice
     * 2. 优先限时购，营销活动优先
     * 3. 接着优先专享价
     * 4. 最后考虑阶梯价
     */
    private BigDecimal salePrice;
    /**
     * 临时售价，在 salePrice 的基础上，如果有折扣，则此价格为折扣价格，否则等于salePrice
     */
    private BigDecimal tempSalePrice;
    /**
     * 预估到手价，为空不显示
     */
    private BigDecimal estimatePrice;
    /**
     * 商品主图
     */
    private String mainImagePath;
    /**
     * 上架时间
     */
    private Long onsaleTime;
    /**
     * 商品属性
     */
    private List<ProductAttributeBo> productAttribute;

    /**
     * 条形码列表
     */
    private List<String> barCodes;

    /**
     * 添加时间
     */
    private Long addedTime;

    /**
     * 提交审核时间
     */
    private Long submitAuditTime;

    /**
     * 运费模板id
     */
    private Long freightTemplateId;

    /**
     * 是否低于警戒库存
     */
    private Boolean whetherBelowSafeStock;

    /**
     * 平台显示顺序
     */
    private Long displaySequence;

    /**
     * 店铺显示顺序
     */
    private Long shopDisplaySequence;

    /**
     * 是否删除
     */
    private Boolean whetherDelete;

    /**
     * 销售状态 1-销售中 2-仓库中 3-草稿箱
     */
    private Integer saleStatus;

    /**
     * 审核状态 1-审核中 2-审核通过 3-审核不通过 4-违规下架
     */
    private Integer auditStatus;
    /**
     * 是否专享价商品
     */
    private Boolean whetherExclusive;
    /**
     * 是否缺货（售罄）
     */
    private Boolean whetherOutOfStock;

    /**
     * 是否单规格商品
     */
    private Boolean whetherSingleSku;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 倍数起购量
     */
    private Integer multipleCount;
    /**
     * 起购量，根据倍数和阶梯价数量计算得到的最小购买数量
     */
    private Integer minBuyCount;
    /**
     * 购物车中商品维度数量
     */
    private Long cartProductCount;

    /**
     * 浏览量
     */
    private Integer visitCounts;
    /**
     * 单规格商品库存
     */
    private Long singleSkuStock;

    /**
     * 收藏数
     */
    private Integer favoriteCount;
    /**
     * 是否限时购
     */
    private Boolean whetherFlashSale;
    /**
     * 评论数
     */
    private Integer evaluateNum;
    /**
     * 是否显示价格XX起
     */
    private Boolean showPriceStartAt;

    /**
     * OE号
     */
    private String oeCode;
    /**
     * 品牌号
     */
    private String brandCode;
    /**
     * 品牌名称
     */
    private String brandName;
}
