package com.sankuai.shangou.seashop.trade.core.service.model;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class PreviewCollocationOrderParamBo {

    /**
     * 商家用户ID
     */
    private Long userId;
    /**
     * 组合购活动ID
     */
    private Long collocationId;
    /**
     * 勾选购买的组合购商品列表
     */
    private List<PurchaseProductBo> skuList;

}
