package com.sankuai.shangou.seashop.trade.core.thrift.impl;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.enums.TradeResultCode;
import com.sankuai.shangou.seashop.trade.core.service.PreOrderService;
import com.sankuai.shangou.seashop.trade.core.service.model.*;
import com.sankuai.shangou.seashop.trade.thrift.core.PreOrderCmdFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.ShoppingCartShopProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ChooseCouponReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ChooseShippingAddressReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PreviewChangeSkuQuantityReq;
import com.sankuai.shangou.seashop.trade.thrift.core.request.SubmitOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.ChooseCouponResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewChangeSkuQuantityResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PreviewOrderResp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/preOrder")
public class PreOrderCmdController implements PreOrderCmdFeign {

    @Resource
    private PreOrderService preOrderService;

    @PostMapping(value = "/submitOrder", consumes = "application/json")
    @Override
    public ResultDto<PreviewOrderResp> submitOrder(@RequestBody SubmitOrderReq orderReq) {
        log.info("【预订单】提交订单, 请求参数={}", JsonUtil.toJsonString(orderReq));
        // 这里的返回结果特殊处理，不适用公共的ThriftResponseHelper.responseInvoke，因为最后需要既返回错误码，也需要返回数据
        ResultDto<PreviewOrderResp> result = ThriftResponseHelper.responseInvoke("submitOrder", orderReq, func -> {
            // 参数基础校验
            orderReq.checkParameter();
            // 参数对象转换
            SubmitOrderBo submitOrderBo = JsonUtil.copy(orderReq, SubmitOrderBo.class);
            // 提交订单
            PreviewOrderBo previewOrderBo = preOrderService.submitOrder(submitOrderBo);
            // 约定，校验通过，创建订单了，否则因为要返回数据，所以对象不会为null
            if (previewOrderBo.isSuccess()) {
                PreviewOrderResp resp = new PreviewOrderResp();
                resp.setSuccess(true);
                resp.setOrderIdList(previewOrderBo.getOrderIdList());
                resp.setNeedPay(previewOrderBo.getNeedPay());
                resp.setAllOrderList(previewOrderBo.getAllOrderIdList());
                return resp;
            }
            // 不是成功，则返回页面数据
            return JsonUtil.copy(previewOrderBo, PreviewOrderResp.class);
        });
        if (result.getData() == null){
            return result;
        }
        if (!Boolean.TRUE.equals(result.getData().isSuccess())) {
            List<ShoppingCartShopProductDto> shopProductList = result.getData().getShopProductList();
            result.setCode(TradeResultCode.BIZ_PRE_ORDER_VALIDATE_FAIL.getValue());
            result.setMessage(getErrorMessage(shopProductList));
        }
        return result;
    }

    private String getErrorMessage(List<ShoppingCartShopProductDto> shopProductList) {
        if (CollUtil.isEmpty(shopProductList)) {
            return TradeResultCode.BIZ_PRE_ORDER_VALIDATE_FAIL.getDesc();
        }

        List<ShoppingCartProductDto> productList = shopProductList.get(0).getProductList();
        if (CollUtil.isEmpty(productList)) {
            return TradeResultCode.BIZ_PRE_ORDER_VALIDATE_FAIL.getDesc();
        }

        for (ShoppingCartProductDto product : productList) {
            if (StrUtil.isNotEmpty(product.getErrorMsg())) {
                return getShortProductName(product.getProductName()) + product.getErrorMsg();
            }
        }
        return TradeResultCode.BIZ_PRE_ORDER_VALIDATE_FAIL.getDesc();
    }

    protected String getShortProductName(String productName) {
        if (productName.length() <= CommonConst.CART_ERROR_MSG_PRODUCT_NAME_LENGTH) {
            return productName;
        }
        return productName.substring(0, CommonConst.CART_ERROR_MSG_PRODUCT_NAME_LENGTH) + CommonConst.CART_ERROR_MSG_PRODUCT_NAME_SHORT;
    }

    @PostMapping(value = "/submitErpOrder", consumes = "application/json")
    @Override
    public ResultDto<PreviewOrderResp> submitErpOrder(@RequestBody SubmitOrderReq req) throws TException {
        log.info("【ERP订单】提交订单, 请求参数={}", req);
        // 这里的返回结果特殊处理，不适用公共的ThriftResponseHelper.responseInvoke
        // 参数基础校验
        req.checkParameter();
        // 参数对象转换
        SubmitOrderBo submitOrderBo = JsonUtil.copy(req, SubmitOrderBo.class);
        PreviewOrderBo previewOrderBo = preOrderService.submitErpOrder(submitOrderBo);
        // 约定，如果返回null，代表是校验通过，创建订单了，否则因为要返回数据，所以对象不会为null
        if (previewOrderBo == null) {
            return ResultDto.newWithData(null);
        }
        ResultDto<PreviewOrderResp> resultDto = ResultDto.newWithData(JsonUtil.copy(previewOrderBo, PreviewOrderResp.class));
        if (!previewOrderBo.isSuccess()) {
            resultDto.setCode(TradeResultCode.BIZ_PRE_ORDER_VALIDATE_FAIL.getValue());
        }

        return resultDto;
    }

    @PostMapping(value = "/chooseCoupon", consumes = "application/json")
    @Override
    public ResultDto<ChooseCouponResp> chooseCoupon(@RequestBody ChooseCouponReq chooseCouponReq) throws TException {
        log.info("【预订单】选择优惠券, 请求参数={}", JsonUtil.toJsonString(chooseCouponReq));
        return ThriftResponseHelper.responseInvoke("chooseCoupon", chooseCouponReq, func -> {
            // 参数基础校验
            chooseCouponReq.checkParameter();
            // 参数对象转换
            ChooseCouponParamBo submitOrderBo = JsonUtil.copy(chooseCouponReq, ChooseCouponParamBo.class);
            ChooseCouponRespBo previewOrderBo = preOrderService.chooseCoupon(submitOrderBo);
            return JsonUtil.copy(previewOrderBo, ChooseCouponResp.class);
        });
    }

    @PostMapping(value = "/chooseShippingAddress", consumes = "application/json")
    @Override
    public ResultDto<PreviewOrderResp> chooseShippingAddress(@RequestBody ChooseShippingAddressReq chooseShippingAddressReq) {
        log.info("【预订单】选择收货地址, 请求参数={}", JsonUtil.toJsonString(chooseShippingAddressReq));
        return ThriftResponseHelper.responseInvoke("chooseShippingAddress", chooseShippingAddressReq, func -> {
            // 参数基础校验
            chooseShippingAddressReq.checkParameter();
            // 参数对象转换
            SubmitOrderBo submitOrderBo = JsonUtil.copy(chooseShippingAddressReq, SubmitOrderBo.class);
            PreviewOrderBo previewOrderBo = preOrderService.chooseShippingAddress(submitOrderBo);
            return JsonUtil.copy(previewOrderBo, PreviewOrderResp.class);
        });
    }

    @PostMapping(value = "/changePreviewSkuCount", consumes = "application/json")
    @Override
    public ResultDto<PreviewChangeSkuQuantityResp> changePreviewSkuCount(@RequestBody PreviewChangeSkuQuantityReq changeQuantityReq) throws TException {
        log.info("【预订单】预览订单页修改数量, 请求参数={}", JsonUtil.toJsonString(changeQuantityReq));
        return ThriftResponseHelper.responseInvoke("changePreviewSkuCount", changeQuantityReq, func -> {
            // 参数基础校验
            changeQuantityReq.checkParameter();
            // 参数对象转换
            PreviewChangeSkuQuantityParamBo submitOrderBo = JsonUtil.copy(changeQuantityReq, PreviewChangeSkuQuantityParamBo.class);
            ShopProductListBo previewOrderBo = preOrderService.changePreviewSkuQuantity(submitOrderBo);
            return JsonUtil.copy(previewOrderBo, PreviewChangeSkuQuantityResp.class);
        });
    }
}
