package com.sankuai.shangou.seashop.trade.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.trade.core.service.ApiCashDepositRefundService;
import com.sankuai.shangou.seashop.trade.thrift.core.CashDepositRefundQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.finance.ApiCashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.finance.ApiCashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.finance.ApiCashDepositRefundResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/1/001
 * @description:
 */
@RestController
@RequestMapping("/cashDepositRefund")
public class CashDepositRefundQueryController implements CashDepositRefundQueryFeign {

    @Resource
    private ApiCashDepositRefundService apiCashDepositRefundService;

    @PostMapping(value = "/refundList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<ApiCashDepositRefundResp>> refundList(@RequestBody ApiCashDepositRefundQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("refundList", request, req ->
            apiCashDepositRefundService.refundList(req));
    }

    @GetMapping(value = "/refundDetail")
    @Override
    public ResultDto<ApiCashDepositRefundDetailResp> refundDetail(Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("refundDetail", id, req ->
            apiCashDepositRefundService.refundDetail(req));
    }
}
