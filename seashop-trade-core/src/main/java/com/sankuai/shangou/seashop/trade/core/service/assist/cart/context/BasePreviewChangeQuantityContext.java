package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewChangeSkuQuantityParamBo;

import lombok.Getter;
import lombok.Setter;

/**
 * 基础的预览订单页修改数量上下文
 * <AUTHOR>
 */
@Getter
@Setter
public abstract class BasePreviewChangeQuantityContext implements BuildContext {

    private Long userId;
    /**
     * 收货地址
     */
    private ShippingAddressBo shippingAddress;
    private PreviewChangeSkuQuantityParamBo changeQuantityParam;

}
