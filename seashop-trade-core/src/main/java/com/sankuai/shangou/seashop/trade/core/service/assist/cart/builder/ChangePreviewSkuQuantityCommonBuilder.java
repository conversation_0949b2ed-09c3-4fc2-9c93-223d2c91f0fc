package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.OrderQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.request.coupon.ProductQueryReq;
import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteShopUserPromotionBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCustomShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.DataProcessorAssist;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.BasePreviewChangeQuantityContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class ChangePreviewSkuQuantityCommonBuilder extends AbstractCustomShopProductBuilder {

    @Resource
    private DataProcessorAssist dataProcessorAssist;

    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {
        BasePreviewChangeQuantityContext buildContext = (BasePreviewChangeQuantityContext) context;

        // 变更数量时，需要重新考虑满减与优惠券的关系
        reBuildCouponOrReduction(context.getUserId(), dataHolder);

        // 计算并设置运费
        calculateAndSetFreight(buildContext.getShippingAddress(), dataHolder);

        // 子类继承处理的自己的逻辑，目前只有购物车的需要同步修改到购物车
        extraChangeProcess(context, dataHolder);
    }

    /**
     * 空方法，子类继承处理
     * <AUTHOR>
     * @param context 业务上下文
	 * @param dataHolder 数据临时存储对象
     */
    protected void extraChangeProcess(BuildContext context, BaseBuildDataHolder dataHolder) {}


    private void reBuildCouponOrReduction(Long userId, BaseBuildDataHolder dataHolder) {
        ShopProductListBo shopOrder = dataHolder.getShopProductList().get(0);
        ShoppingCartShopBo shop = shopOrder.getShop();
        OrderAdditionalBo additional = shopOrder.getAdditional();
        // 对于之前选择了优惠券的，此时的满减是false，但可能之前的优惠券不满足条件了，后续要重新计算满减
        boolean hasReduction = additional.getReductionActivityId() != null && additional.getReductionActivityId() > 0;
        boolean hasCoupon = super.hasCoupon(additional);
        log.info("【预览订单修改数量】重新计算优惠券与满减, hasReduction={}, hasCoupon={}", hasReduction, hasCoupon);
        // 根据当前订单情况获取有效的优惠券
        Map<Long, List<CouponRecordSimpleResp>> validCouponMap = getOrderValidCoupon(userId, shop.getShopId(), shopOrder.getProductList());
        List<CouponRecordSimpleResp> shopValidCouponList = validCouponMap.get(shop.getShopId());
        if (CollUtil.isNotEmpty(shopValidCouponList)) {
            // 面额降序
            shopValidCouponList.sort((o1, o2) -> (int) (o2.getPrice() - o1.getPrice()));
        }
        log.info("【预览订单修改数量】重新计算优惠券与满减, 当前满足店铺条件的优惠券列表为: {}", JsonUtil.toJsonString(shopValidCouponList));
        // 接下来根据修改数量时的优惠券和满减情况，重新考虑并计算优惠券和满减
        shopOrder.setValidCouponList(shopValidCouponList);
        shopOrder.getShop().setHasValidCoupon(CollUtil.isNotEmpty(shopValidCouponList));

        // 如果之前选择了优惠券，此时修改数据量又没有满足条件的优惠券了，需要重新计算满减
        if (hasCoupon && CollUtil.isEmpty(shopValidCouponList)) {
            log.info("【预览订单修改数量】重新计算优惠券与满减, 之前选择了优惠券, 此时没有有效优惠券, 重新计算满减");
            // 重置之前的优惠券
            resetCouponInfo(shop, additional);

            Map<Long/*shopId*/, RemoteShopUserPromotionBo> shopPromotionMap = dataHolder.getShopPromotionMap();
            if (shopPromotionMap == null || !shopPromotionMap.containsKey(shop.getShopId())) {
                log.info("【预览订单修改数量】重新计算优惠券与满减, 没有满减");
                return;
            }
            RemoteShopUserPromotionBo shopPromotion = shopPromotionMap.get(shop.getShopId());
            if (shopPromotion == null) {
                log.info("【预览订单修改数量】重新计算优惠券与满减, 没有满减");
                return;
            }
            dealReduction(shopOrder, shopPromotion.getShopReduction());
        }
        // 如果之前选择了优惠券，此时也有满足条件的优惠券，则比较面额最大的与之前的是否相同，如果不同，重新计算并设置优惠券
        else if (hasCoupon && CollUtil.isNotEmpty(shopValidCouponList)) {
            log.info("【预览订单修改数量】重新计算优惠券与满减, 之前选择了优惠券，此时有优惠券，比较并重置优惠券");
            Long couponRecordId = additional.getCouponRecordId();
            // 设置的时候已经排序，面额最大的在前面
            CouponRecordSimpleResp coupon = shopValidCouponList.get(0);
            if (coupon.getId().equals(couponRecordId)) {
                log.info("【预览订单修改数量】重新计算优惠券与满减, 优惠券相同，计算面值");
                reCalCoupon(coupon, shop, additional, shop.getSelectedTotalAmount(), shopOrder.getProductList());
                return;
            }
            // 重置并重新计算优惠券
            resetAndReCalCoupon(shop, additional, shopValidCouponList, shopOrder.getProductList());
        }
        // 此时的else是之前没有优惠券的情况
        // 有满减，则不需要重新算，返回优惠券列表就行；之前没有满减，且此时有优惠券，则需要选择优惠券，因为可能是之前数量不满足
        else if (hasReduction){
            log.info("【预览订单修改数量】重新计算优惠券与满减, 之前没有选择优惠券，有满减，营销处理了满减，这里无需计算");
        }
        // 此时是之前既没有满减，也没有选择优惠券，此时有满足条件的优惠券
        else if (CollUtil.isNotEmpty(shopValidCouponList)) {
            log.info("【预览订单修改数量】重新计算优惠券与满减, 之前没有选择优惠券，也没有满减，此时有优惠券，重新计算优惠券");
            // 重置并重新计算优惠券
            resetAndReCalCoupon(shop, additional, shopValidCouponList, shopOrder.getProductList());
        }
    }

    private Map<Long, List<CouponRecordSimpleResp>> getOrderValidCoupon(Long userId, Long shopId, List<ShopProductBo> productList) {
        OrderQueryReq orderQueryReq = null;
        List<ProductQueryReq> prodList = new ArrayList<>(productList.size());
        ProductQueryReq prodReq = null;
        for (ShopProductBo sku : productList) {
            // 排除掉专享价的商品
            if (!Boolean.TRUE.equals(sku.getWhetherExclusive())) {
                prodReq = new ProductQueryReq();
                prodReq.setProductId(sku.getProductId());
                prodReq.setProductAmount(sku.getTotalAmount());
                prodList.add(prodReq);
            }
        }
        // 排除掉专享价后，可能为null
        if (CollUtil.isNotEmpty(prodList)) {
            orderQueryReq = new OrderQueryReq();
            orderQueryReq.setShopId(shopId);
            orderQueryReq.setProductList(prodList);
        }
        if (orderQueryReq == null) {
            return Collections.emptyMap();
        }
        return dataProcessorAssist.getValidCoupon(userId, Collections.singletonList(orderQueryReq));
    }

}
