package com.sankuai.shangou.seashop.trade.core.service.model;

import lombok.ToString;

import java.util.List;

import com.sankuai.shangou.seashop.promotion.thrift.core.response.CouponRecordSimpleResp;

/**
 * 购物车商品返回对象
 * <AUTHOR>
 */
@ToString
public class ShopProductListBo {

    /**
     * 店铺信息
     */
    private ShoppingCartShopBo shop;
    /**
     * 商品列表
     */
    private List<ShopProductBo> productList;
    /**
     * 订单附加信息
     */
    private OrderAdditionalBo additional;
    /**
     * 当前店铺的所有商品ID集合。目前仅部分需要用到productId的业务设置了，使用时需注意
     */
    private List<Long> productIdList;
    /**
     * 根据店铺商品和金额对应的有效的优惠券列表，目前是预览订单页使用，设置是否有优惠券以及默认选中最大面额的优惠券
     */
    private List<CouponRecordSimpleResp> validCouponList;

    public ShoppingCartShopBo getShop() {
        return shop;
    }

    public void setShop(ShoppingCartShopBo shop) {
        this.shop = shop;
    }

    public List<ShopProductBo> getProductList() {
        return productList;
    }

    public void setProductList(List<ShopProductBo> productList) {
        this.productList = productList;
    }

    public void setAdditional(OrderAdditionalBo additional) {
        this.additional = additional;
    }

    public OrderAdditionalBo getAdditional() {
        if (additional == null) {
            additional = new OrderAdditionalBo();
        }
        return additional;
    }

    public List<Long> getProductIdList() {
        return productIdList;
    }

    public void setProductIdList(List<Long> productIdList) {
        this.productIdList = productIdList;
    }

    public List<CouponRecordSimpleResp> getValidCouponList() {
        return validCouponList;
    }

    public void setValidCouponList(List<CouponRecordSimpleResp> validCouponList) {
        this.validCouponList = validCouponList;
    }
}
