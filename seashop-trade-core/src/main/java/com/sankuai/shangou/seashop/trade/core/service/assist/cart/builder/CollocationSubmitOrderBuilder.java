package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.enums.TradeOrderValidResultEnum;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildResult;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.CollocationSubmitOrderContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PurchaseProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 组合购提交订单构建器
 * <AUTHOR>
 */
@Service
@Slf4j
public class CollocationSubmitOrderBuilder extends AbstractCollocationBuilder {


    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        CollocationSubmitOrderContext buildContext = (CollocationSubmitOrderContext) context;
        // 查询组合购数据
        Long collocationId = buildContext.getCollocationId();
        Long userId = buildContext.getUserId();

        // 组合购，只会提交一个店铺的多个商品
        ShopProductListBo orderShop = buildContext.getShopProductList().get(0);
        List<ShopProductBo> orderProductList = orderShop.getProductList();
        // 基于传入的数据，转换成公共构建方法需要的对象
        List<PurchaseProductBo> skuList = orderProductList.stream()
                .map(sku -> {
                    PurchaseProductBo purchaseProductBo = new PurchaseProductBo();
                    purchaseProductBo.setProductId(sku.getProductId());
                    purchaseProductBo.setSkuId(sku.getSkuId());
                    purchaseProductBo.setQuantity(sku.getQuantity());
                    return purchaseProductBo;
                }).collect(Collectors.toList());
        // 下单逻辑中，附加信息，先取页面传入的，因为会包括配送方式，优惠券等
        OrderAdditionalBo additional = JsonUtil.copy(orderShop.getAdditional(), OrderAdditionalBo.class);
        // 选择了优惠券时，不处理满减了
        boolean hasCoupon = super.hasCoupon(additional);
        if (hasCoupon) {
            buildContext.resetNeedReduction(false);
        }
        // 调用父类公共的构建方法
        List<ShopProductListBo> currentShopOrderList =  buildCollocationShopProduct(userId, collocationId, skuList, context.getCurrentTime());
        currentShopOrderList.get(0).setAdditional(additional);
        return currentShopOrderList;
    }

    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {
        CollocationSubmitOrderContext buildContext = (CollocationSubmitOrderContext) context;
        ShippingAddressBo shippingAddress = buildContext.getShippingAddress();

        log.info("组合购提交订单处理优惠券运费等");
        // 计算优惠券
        super.calculateCoupon(buildContext, dataHolder);
        // 调用父类公共的扩展处理，处理优惠券、满减和运费
        super.processCollocationExpand(dataHolder, shippingAddress);

        // 计算税费，选择发票时，由前端自行计算传入税费金额，后端这里根据发票类型做计算校验
        calculateTax(dataHolder);

        // 校验价格、数量、总金额，会以页面传入的为基础，并设置错误信息
        // 组合购只会有一个店铺
        TradeOrderValidResultEnum anyError = validatePriceAndQuantity(buildContext.getUserId(), buildContext.getShopProductList(), dataHolder.getShopProductList());
        // 如果有异常，返回页面也是用页面提交的数据，只不过设置了错误信息
        if (TradeOrderValidResultEnum.anyError(anyError)) {
            log.info("组合购提交订单校验价格、数量、总金额异常，异常信息：{}", anyError);
            dataHolder.setShopProductList(buildContext.getShopProductList());
        } else {
            log.info("组合购提交订单校验价格、数量、总金额正常");
            // 如果没有异常，进行优惠分摊
            splitPromotionAmount(context, dataHolder);
        }
        // 设置错误字段
        dataHolder.setAnyError(TradeOrderValidResultEnum.anyError(anyError));
        if (anyError != null) {
            dataHolder.setErrMsg(anyError.getMessage());
        }
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.SUBMIT_COLLOCATION_ORDER;
    }

    @Override
    protected void extendResult(BuildResult result, BaseBuildDataHolder dataHolder) {
        result.setSuccess(!dataHolder.isAnyError());
        result.setErrMsg(dataHolder.getErrMsg());
    }





    //********************************************************

}
