package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.util.List;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PreviewErpOrderBuildContext extends AbstractErpOrderBuildContext {

    private Long userId;
    /**
     * 用户收货地址
     */
    private ShippingAddressBo shippingAddress;
    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;

    /**
     * 订单平台，0：PC；2：小程序
     */
    private Integer platform;


    private Boolean autoPromotion;


    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_ERP_ORDER;
    }

    @Override
    public boolean needDiscount() {
        return true;
    }

    @Override
    public boolean needReduction() {
        //自己计算满减 自动选择最优惠优惠券
        return false;
    }

}
