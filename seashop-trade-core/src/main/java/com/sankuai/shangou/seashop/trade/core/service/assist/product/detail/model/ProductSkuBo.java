package com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * 商品sku信息
 *
 * <AUTHOR>
 * @date 2023/11/13 19:46
 */
@Setter
@Getter
public class ProductSkuBo {

    /**
     * sku自增id
     */
    private Long skuAutoId;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 规格1值
     */
    private String spec1Value;

    /**
     * 规格2值
     */
    private String spec2Value;

    /**
     * 规格3值
     */
    private String spec3Value;

    /**
     * 销售价
     */
    private BigDecimal salePrice;

    /**
     * 库存
     */
    private long stock;

    /**
     * upc/69码
     */
    private String barCode;

    /**
     * 货号
     */
    private String skuCode;

    /**
     * 限时购价格
     */
    private BigDecimal flashSalePrice;

    /**
     * 限时购库存
     */
    private long flashSaleStock;

    /**
     * 限时购限购数量
     */
    private int flashSaleLimit;

    /**
     * 规格图片
     */
    private String showPic;

    /**
     * 单位
     */
    private String measureUnit;

    /**
     * 是否是专享价的sku
     */
    private boolean exclusiveSku;

    /**
     * 原始价格
     */
    private BigDecimal skuPirce;


}
