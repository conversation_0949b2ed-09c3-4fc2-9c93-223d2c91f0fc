package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.util.List;

import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SelectShoppingCartSkuContext implements BuildContext {

    private Long userId;
    /**
     * 购物车主键ID
     */
    private Long id;
    /**
     * 是否选中
     */
    private Boolean whetherSelect;
    /**
     * 店铺信息
     */
    private ShoppingCartShopBo shop;
    /**
     * 商品列表
     */
    private List<ShopProductBo> productList;


    @Override
    public BuildType getBuildType() {
        return BuildType.SELECT_SHOPPING_CART_SKU;
    }

    @Override
    public boolean needDiscount() {
        return true;
    }

    @Override
    public boolean needReduction() {
        return true;
    }
}
