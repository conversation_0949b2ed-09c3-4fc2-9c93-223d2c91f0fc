package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteLadderPriceBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteShopUserPromotionBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCartShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.CartBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.ShoppingCartAssist;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.AddonAddCartContext;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopExclusivePriceBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;
import com.sankuai.shangou.seashop.trade.dao.core.repository.ShoppingCartRepository;

import lombok.extern.slf4j.Slf4j;

/**
 * 凑单加购构建器
 * <AUTHOR>
 */
@Service
@Slf4j
public class AddonAddCartBuilder extends AbstractCartShopProductBuilder {

    @Resource
    private ShoppingCartRepository shoppingCartRepository;
    @Resource
    private ShoppingCartAssist shoppingCartAssist;

    @Override
    protected List<ShoppingCart> getShoppingCartList(BuildContext context) {
        AddonAddCartContext addonAddCartContext = (AddonAddCartContext) context;
        List<Long> cartIdList = addonAddCartContext.getProductList().stream()
                .map(ShopProductBo::getId)
                .collect(Collectors.toList());
        log.info("【凑单】查询购物车, idList={}", JsonUtil.toJsonString(cartIdList));
        return shoppingCartRepository.queryByIdList(cartIdList);
    }

    @Override
    protected CartBuildDataHolder tempHoldData(List<ShopProductBo> userShopSkuList) {
        return CartBuildDataHolder.builder()
                .validProductList(userShopSkuList)
                .build();
    }

    /**
     * 复用的构建器流程，这个方法中其实可以计算处理营销，但没有这么处理，是凑单还需要用营销做其他事情，放到外层一起处理，这里只调整基本价格
     * <AUTHOR>
     * @param context
	 * @param dataHolder
     */
    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, CartBuildDataHolder dataHolder) {
        Map<ShoppingCartShopBo, List<ShopProductBo>> shopGroupedMap = dataHolder.getShopGroupedMap();
        Map<Long, RemoteShopUserPromotionBo> shopPromotionMap = dataHolder.getShopPromotionMap();

        return shopGroupedMap.entrySet().stream()
                .map(entry -> {
                    RemoteShopUserPromotionBo promotion = shopPromotionMap.get(entry.getKey().getShopId());
                    // 计算并设置商品sku的实际售价和店铺总金额
                    calculatePriceAndAmountForShop(entry.getKey(), entry.getValue(), promotion);
                    ShopProductListBo shopProductListBo = new ShopProductListBo();
                    shopProductListBo.setShop(entry.getKey());
                    shopProductListBo.setProductList(entry.getValue());
                    return shopProductListBo;
                }).collect(Collectors.toList());
    }

    @Override
    protected void processExpand(BuildContext context, CartBuildDataHolder dataHolder) {

    }

    @Override
    public BuildType getBuildType() {
        return BuildType.ADDON;
    }




    //****************************************************************

    /**
     * 计算店铺商品sku的价格和总金额
     * <AUTHOR>
     * @param shop 店铺
     * @param shopSkuList 店铺的sku
     * @param shopPromotion 店铺的营销
     * void
     */
    protected void calculatePriceAndAmountForShop(ShoppingCartShopBo shop,
                                                  List<ShopProductBo> shopSkuList,
                                                  RemoteShopUserPromotionBo shopPromotion) {
        BigDecimal selectedAmount = BigDecimal.ZERO;
        // 处理并得到sku的专享价格
        Map<String, ShopExclusivePriceBo> skuExclusivePriceMap = shoppingCartAssist.flatSkuExclusivePriceAndToMap(shopPromotion.getShopExclusivePriceList());
        // 从专享价SKU中获取对应的商品ID，用于判断设置同商品下任意一个sku是专享价的，设置专享价标识
        Map<Long, Long> exclusiveProductIdMap = shoppingCartAssist.getExclusiveProductId(skuExclusivePriceMap);
        // 遍历计算商品的基础实际售价，并汇总商品总金额
        long totalQuantity = 0;
        for (ShopProductBo sku : shopSkuList) {
            // 计算商品的价格
            resetRealSalePriceIfNecessary(sku, skuExclusivePriceMap, exclusiveProductIdMap);
            // 此时的实际售价就是优惠售价
            sku.setDiscountSalePrice(sku.getRealSalePrice());
            BigDecimal skuAmount = NumberUtil.multiply(sku.getRealSalePrice(), sku.getQuantity());
            if (Boolean.TRUE.equals(sku.getWhetherSelected())) {
                selectedAmount = selectedAmount.add(skuAmount);
                totalQuantity = totalQuantity + sku.getQuantity();
            }
        }
        // 设置店铺初步的总金额
        shop.setSelectedTotalAmount(selectedAmount);
        shop.setSelectedQuantity(totalQuantity);
    }

    /**
     * 重置商品sku的实际售价
     * <p>售价优先规则：专享价>阶梯价>商城价</p>
     * <AUTHOR>
     * @param sku 购物车SKU
     * @param skuExclusivePriceMap 专享价
     * void
     */
    private void resetRealSalePriceIfNecessary(ShopProductBo sku,
                                               Map<String, ShopExclusivePriceBo> skuExclusivePriceMap,
                                               Map<Long, Long> productShouldExclusiveMap) {
        // 商品的某个sku有专享价，其他sku也要设置专享价标识
        if (productShouldExclusiveMap.containsKey(sku.getProductId())) {
            sku.setWhetherExclusive(true);
        }
        // 如果有专属价格，使用专属价格
        if (skuExclusivePriceMap != null && skuExclusivePriceMap.containsKey(sku.getSkuId())) {
            sku.setRealSalePrice(skuExclusivePriceMap.get(sku.getSkuId()).getPrice());
            sku.setFinalSalePrice(sku.getRealSalePrice());
            return;
        }
        // 没有专享价时，如果有阶梯价格，使用阶梯价格
        List<RemoteLadderPriceBo> ladderPriceList = sku.getLadderPriceList();
        if (CollectionUtils.isNotEmpty(ladderPriceList)) {
            long quantity = sku.getQuantity();
            // 无法保证接口返回的阶梯价是否是有序的，而且阶梯价目前最多三个，所以直接全部遍历，任意一个满足就返回
            for (RemoteLadderPriceBo ladderPrice : ladderPriceList) {
                if (quantity >= ladderPrice.getMinBath() && quantity <= ladderPrice.getMaxBath()) {
                    sku.setRealSalePrice(ladderPrice.getPrice());
                    sku.setFinalSalePrice(sku.getRealSalePrice());
                    return;
                }
            }
        }
    }




}
