package com.sankuai.shangou.seashop.trade.core.service.model.cart;

import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * 商家添加商品到购物车请求入参
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
public class AddFromAddonBo {

    /**
     * 商家用户ID
     */
    private Long userId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 加购数量
     */
    private Long quantity;
    private BigDecimal realSalePrice;
    /**
     * 凑单类型。1：折扣；2：满减
     */
    private Integer type;
    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 店铺信息
     */
    private ShoppingCartShopBo shop;
    /**
     * 商品列表
     */
    private List<ShopProductBo> productList;

}
