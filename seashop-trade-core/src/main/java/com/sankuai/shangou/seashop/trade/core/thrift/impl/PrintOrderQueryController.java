package com.sankuai.shangou.seashop.trade.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.trade.core.service.PrintOrderQueryService;
import com.sankuai.shangou.seashop.trade.thrift.core.PrintOrderQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.PrintOrderReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.PrintOrderResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 15:19
 */
@Slf4j
@RestController
@RequestMapping("/printOrder")
public class PrintOrderQueryController implements PrintOrderQueryFeign {

    @Resource
    private PrintOrderQueryService printOrderQueryService;

    @PostMapping(value = "/getOrderPrint", consumes = "application/json")
    @Override
    public ResultDto<PrintOrderResp> getOrderPrint(@RequestBody PrintOrderReq printOrderReq) throws TException {
        printOrderReq.checkParameter();
        return ThriftResponseHelper.responseInvoke("getOrderPrint", printOrderReq, req -> printOrderQueryService.getOrderPrint(req));
    }
}
