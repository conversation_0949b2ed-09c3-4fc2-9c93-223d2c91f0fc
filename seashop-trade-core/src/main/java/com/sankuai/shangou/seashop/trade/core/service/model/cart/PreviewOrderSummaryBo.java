package com.sankuai.shangou.seashop.trade.core.service.model.cart;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class PreviewOrderSummaryBo {

    /**
     * 预览订单页总的应付金额
     */
    private BigDecimal totalPayAmount;
    /**
     * 预览订单页总的商品金额
     */
    private BigDecimal totalProductAmount;
    /**
     * 预览订单页总的商品数量
     */
    private Long totalSkuQuantity;
    /**
     * 预览订单页总的折扣金额
     */
    private BigDecimal totalDiscountAmount;
    /**
     * 预览订单页总的优惠券金额
     */
    private BigDecimal totalCouponAmount;
    /**
     * 预览订单页总的运费金额
     */
    private BigDecimal totalFreightAmount;
    /**
     * 预览订单页总的税费金额
     */
    private BigDecimal totalTaxAmount;
    /**
     * 预览订单页总的满减金额
     */
    private BigDecimal totalReductionAmount;
    /**
     * 购物车中所有商品是否选中，也是所有店铺是否是选中
     */
    private Boolean whetherAllSelected;

}
