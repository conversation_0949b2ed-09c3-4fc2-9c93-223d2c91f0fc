package com.sankuai.shangou.seashop.trade.core.service.impl;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShippingAddressDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.ShopProductListDto;
import com.sankuai.shangou.seashop.order.thrift.core.dto.UserDto;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderPlatformEnum;
import com.sankuai.shangou.seashop.order.thrift.core.request.CreateOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderResp;
import com.sankuai.shangou.seashop.trade.common.constant.CacheConst;
import com.sankuai.shangou.seashop.trade.common.enums.ShopTradeSettingValidTypeEnum;
import com.sankuai.shangou.seashop.trade.common.remote.BaseRegionService;
import com.sankuai.shangou.seashop.trade.common.remote.Order2TradeRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.UserShippingAddressRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.base.ShopTradeSettingBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.PreOrderService;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildResult;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.ShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.ShopProductBuilderFactory;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.BasePreviewChangeQuantityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.BuyNowSubmitOrderContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChangePreviewBuyNowSkuQuantityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChangePreviewCartSkuQuantityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChangePreviewCollocationSkuQuantityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChangePreviewFlashSaleSkuQuantityContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChooseCouponBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChooseShippingAddressContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.CollocationSubmitOrderContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.FlashSaleSubmitOrderContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.PreviewErpOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.SubmitErpOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.SubmitOrderContext;
import com.sankuai.shangou.seashop.trade.core.service.model.ChooseCouponParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ChooseCouponRespBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewChangeSkuQuantityParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewOrderBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.core.service.model.SubmitOrderBo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PreOrderServiceImpl implements PreOrderService {

    @Resource
    private SquirrelUtil squirrelUtil;
    @Resource
    private ShopProductBuilderFactory shopProductBuilderFactory;
    @Resource
    private Order2TradeRemoteService order2TradeRemoteService;
    @Resource
    private UserShippingAddressRemoteService userShippingAddressRemoteService;
    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private BaseRegionService baseRegionService;


    /**
     * 获取接口幂等的token令牌
     * <p>redis设置token，实际接口带上token，校验token值</p>
     *
     * @param userId java.lang.String
     * <AUTHOR>
     */
    @Override
    public String getSubmitToken(Long userId) {
        String token = UUID.randomUUID().toString();
        String key = CacheConst.CACHE_ORDER_SUBMIT_TOKEN + token;
        // token随机，这样同一个账号可以分开提交订单
        // 考虑用户可能停留在页面的时间，这里的预览订单页可以修改数量等，设置有效时间为 60 分钟
        squirrelUtil.set(key, userId, CacheConst.EXPIRE_SUBMIT_ORDER_TOKEN_60_MIN);
        return token;
    }

    /**
     * 提交订单
     * <p>校验过程会以页面传入的数据为准，但会根据页面传入的商品重新计算价格、数量和总金额等。
     * 所以即使是恶意操作，通过接口工具修改了价格和数量等也没有关系，校验无法通过。
     * 最终创建订单时，会用校验通过的页面数据直接保存，不再计算</p>
     *
     * @param orderBo 需要创建订单的数据
     * <AUTHOR>
     */
    @Override
    public PreviewOrderBo submitOrder(SubmitOrderBo orderBo) {
        String key = CacheConst.CACHE_ORDER_SUBMIT_TOKEN + orderBo.getSubmitToken();
        // 校验token，CAS原子操作，如果token存在，则删除token，返回true，否则返回false
        Boolean checkTokenResult = squirrelUtil.compareAndDelete(key, orderBo.getUser().getUserId());
        /*if (!Boolean.TRUE.equals(checkTokenResult)) {
            throw new BusinessException("请勿重复提交订单");
        }*/
        // 初始化构建上下文，提交订单时，普通购物车提交、限时购、组合购获取和校验的数据不同，进一步区分
        SubmitOrderContext buildContext = chooseContext(orderBo);
        log.info("提交订单, 上下文类型={}", buildContext.getBuildType());
        // 设置公共数据
        buildContext(buildContext, orderBo);
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        if (buildResult == null) {
            throw new BusinessException("您的购物车商品已被提交，请查看订单");
        }
        log.info("提交订单, 构建结果={}", buildResult.isSuccess());
        // 如果校验通过，则调用订单服务创建订单
        if (buildResult.isSuccess()) {
            // 校验订单金额和订单商品数量
            validateConfigAmountAndQuantity(buildResult);
            // 调用订单服务是另一个系统，调用时会生成UUID，订单服务进行幂等校验，防止重复生成订单
            CreateOrderReq createOrderReq = new CreateOrderReq();
            createOrderReq.setTotalAmount(buildResult.getTotalAmount());
            createOrderReq.setShopProductList(JsonUtil.copyList(buildResult.getShopProductList(), ShopProductListDto.class));
            createOrderReq.setShippingAddress(JsonUtil.copy(buildContext.getShippingAddress(), ShippingAddressDto.class));

            // createOrderReq.setOrderSource(OrderSourceEnum.SELF);
            createOrderReq.setPlatform(OrderPlatformEnum.valueOf(orderBo.getPlatform()));
            createOrderReq.setUserInfo(JsonUtil.copy(orderBo.getUser(), UserDto.class));
            createOrderReq.setFlashSaleId(orderBo.getFlashSaleId());
            createOrderReq.setCollocationId(orderBo.getCollocationId());
            CreateOrderResp resp = order2TradeRemoteService.createOrder(createOrderReq);
            boolean needPay = buildResult.getTotalAmount().compareTo(BigDecimal.ZERO) > 0;
            return PreviewOrderBo.builder()
                    .success(true)
                    .orderIdList(resp.getOrderIdList())
                    .allOrderIdList(resp.getAllOrderIdList())
                    .needPay(needPay)
                    .build();
        }
        // 构建返回结果
        return PreviewOrderBo.builder()
                .success(false)
                .success(buildResult.isSuccess())
                .shopProductList(buildResult.getShopProductList())
                .shippingAddress(buildContext.getShippingAddress())
                .summary(buildResult.getSummary())
                .build();
    }

    @Override
    public PreviewOrderBo submitErpOrder(SubmitOrderBo orderBo) {
        /*String key = CacheConst.CACHE_ORDER_SUBMIT_TOKEN + orderBo.getSubmitToken();
        // 校验token，CAS原子操作，如果token存在，则删除token，返回true，否则返回false
        Boolean checkTokenResult = squirrelUtil.compareAndDelete(key, orderBo.getUser().getUserId());
        if (!Boolean.TRUE.equals(checkTokenResult)) {
            throw new BusinessException("请勿重复提交订单");
        }*/
        //类似普通购物车提交
        SubmitErpOrderBuildContext buildContext = new SubmitErpOrderBuildContext();
        // 设置公共数据
        buildContext.setUserId(orderBo.getUser().getUserId());
        buildContext.setShopProductList(orderBo.getShopProductList());
        buildContext.setPlatform(orderBo.getPlatform());
        buildContext.setShippingAddress(orderBo.getShippingAddress());

        log.info("提交订单, 上下文类型={}", buildContext.getBuildType());
        // 设置公共数据
        buildErpContext(buildContext, orderBo);
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        if (buildResult == null) {
            throw new BusinessException("您的购物车商品已被提交，请查看订单");
        }
        log.info("提交订单, 构建结果={}", buildResult.isSuccess());
        // 如果校验通过，则调用订单服务创建订单
        if (buildResult.isSuccess()) {
            // 校验订单金额和订单商品数量
            validateConfigAmountAndQuantity(buildResult);
            // 调用订单服务是另一个系统，调用时会生成UUID，订单服务进行幂等校验，防止重复生成订单
            CreateOrderReq createOrderReq = new CreateOrderReq();
            createOrderReq.setTotalAmount(buildResult.getTotalAmount());
            createOrderReq.setShopProductList(JsonUtil.copyList(buildResult.getShopProductList(), ShopProductListDto.class));
            createOrderReq.setShippingAddress(JsonUtil.copy(buildContext.getShippingAddress(), ShippingAddressDto.class));

            // createOrderReq.setOrderSource(OrderSourceEnum.SELF);
            createOrderReq.setPlatform(OrderPlatformEnum.valueOf(orderBo.getPlatform()));
            createOrderReq.setUserInfo(JsonUtil.copy(orderBo.getUser(), UserDto.class));
            createOrderReq.setFlashSaleId(orderBo.getFlashSaleId());
            createOrderReq.setCollocationId(orderBo.getCollocationId());
            CreateOrderResp resp = order2TradeRemoteService.createOrder(createOrderReq);
            boolean needPay = buildResult.getTotalAmount().compareTo(BigDecimal.ZERO) > 0;
            return PreviewOrderBo.builder()
                    .success(true)
                    .orderIdList(resp.getOrderIdList())
                    .allOrderIdList(resp.getAllOrderIdList())
                    .needPay(needPay)
                    .build();
        }
        // 构建返回结果
        return PreviewOrderBo.builder()
                .success(false)
                .success(buildResult.isSuccess())
                .shopProductList(buildResult.getShopProductList())
                .shippingAddress(buildContext.getShippingAddress())
                .summary(buildResult.getSummary())
                .build();
    }

    @Override
    public ChooseCouponRespBo chooseCoupon(ChooseCouponParamBo paramBo) {
        // 初始化构建上下文，复用提交订单的上下文，因为都是将页面数据整体提交
        ChooseCouponBuildContext buildContext = ChooseCouponBuildContext.builder()
                .userId(paramBo.getUserId())
                .shop(paramBo.getShop())
                .productList(paramBo.getProductList())
                .couponId(paramBo.getCouponId())
                .additional(paramBo.getAdditional())
                .build();
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        // 构建返回结果
        return ChooseCouponRespBo.builder()
                .shopProduct(buildResult.getShopProductList().get(0))
                .totalAmount(buildResult.getTotalAmount())
                .build();
    }

    @Override
    public PreviewOrderBo chooseShippingAddress(SubmitOrderBo orderBo) {
        // 初始化构建上下文，复用提交订单的上下文，因为都是将页面数据整体提交
        ChooseShippingAddressContext buildContext = new ChooseShippingAddressContext();
        buildContext(buildContext, orderBo);
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        // 构建返回结果
        return PreviewOrderBo.builder()
                .shopProductList(buildResult.getShopProductList())
                .totalAmount(buildResult.getTotalAmount())
                .shippingAddress(buildContext.getShippingAddress())
                .summary(buildResult.getSummary())
                .build();
    }

    @Override
    public PreviewOrderBo previewOrder(SubmitOrderBo orderBo) {
        // 初始化构建上下文，提交订单时，普通购物车提交
        PreviewErpOrderBuildContext buildContext = new PreviewErpOrderBuildContext();
        // 设置公共数据
        buildContext.setUserId(orderBo.getUser().getUserId());
        buildContext.setShopProductList(orderBo.getShopProductList());
        buildContext.setPlatform(orderBo.getPlatform());
        buildContext.setShippingAddress(completeShippingAddress(orderBo.getShippingAddress()));

        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        // 校验订单金额和订单商品数量
        validateConfigAmountAndQuantity(buildResult);
        // 不管校验成功还是失败，返回预览 由erp做处理
        // 构建返回结果
        return PreviewOrderBo.builder()
                .success(buildResult.isSuccess())
                .shopProductList(buildResult.getShopProductList())
                .totalAmount(buildResult.getTotalAmount())
                .shippingAddress(buildContext.getShippingAddress())
                .build();
    }

    @Override
    public ShopProductListBo changePreviewSkuQuantity(PreviewChangeSkuQuantityParamBo paramBo) {
        // 初始化构建上下文，不同的业务有不同的上下文和构建器
        BasePreviewChangeQuantityContext buildContext = chooseChangeContext(paramBo);
        // 获取构建器
        ShopProductBuilder builder = shopProductBuilderFactory.getBuilder(buildContext);
        // 构建购物车基本数据
        BuildResult buildResult = builder.build(buildContext);
        // 构建返回结果，修改都是针对一个店铺的，所以只取第一个店铺的数据
        return buildResult.getShopProductList().get(0);
    }


    //******************************************

    /**
     * 根据提交订单的参数选择处理上下文，普通购物车提交、普通立即购买、限时购、组合购获取和校验的数据不同，进一步区分
     * <p>限时购与组合购不会同时存在</p>
     *
     * @param orderBo 提交订单的入参数据
     * <AUTHOR>
     */
    private SubmitOrderContext chooseContext(SubmitOrderBo orderBo) {
        // 限时购
        if (orderBo.getFlashSaleId() != null) {
            FlashSaleSubmitOrderContext context = new FlashSaleSubmitOrderContext();
            context.setFlashSaleId(orderBo.getFlashSaleId());
            return context;
        }
        // 组合购
        if (orderBo.getCollocationId() != null) {
            CollocationSubmitOrderContext context = new CollocationSubmitOrderContext();
            context.setCollocationId(orderBo.getCollocationId());
            return context;
        }
        // 立即购买
        if (Boolean.TRUE.equals(orderBo.getWhetherBuyNow())) {
            BuyNowSubmitOrderContext context = new BuyNowSubmitOrderContext();
            context.setWhetherBuyNow(orderBo.getWhetherBuyNow());
            return context;
        }
        // 普通购物车
        return new SubmitOrderContext();
    }

    private void buildContext(SubmitOrderContext buildContext, SubmitOrderBo orderBo) {
        buildContext.setUserId(orderBo.getUser().getUserId());
        buildContext.setShopProductList(orderBo.getShopProductList());
        buildContext.setTotalAmount(orderBo.getTotalAmount());
        // 下单入口设置时间
        buildContext.setCurrentTime(new Date());

        // 初始化构建上下文
        Long shippingAddressId = orderBo.getShippingAddressId();
        if (shippingAddressId == null && orderBo.getShippingAddress() != null) {
            shippingAddressId = orderBo.getShippingAddress().getId();
        }
        // 获取用户选择的收货地址，用于计算运费和展示
        ShippingAddressBo shippingAddress = userShippingAddressRemoteService.getShippingAddress(shippingAddressId, orderBo.getUser().getUserId());
        buildContext.setShippingAddress(shippingAddress);

    }

    private void buildErpContext(SubmitOrderContext buildContext, SubmitOrderBo orderBo) {
        buildContext.setUserId(orderBo.getUser().getUserId());
        buildContext.setShopProductList(orderBo.getShopProductList());
        buildContext.setTotalAmount(orderBo.getTotalAmount());

        // 初始化构建上下文
        ShippingAddressBo address = orderBo.getShippingAddress();
        // 完善收货地址
        buildContext.setShippingAddress(completeShippingAddress(address));
    }

    /**
     * 完善收货地址
     *
     * @param address 收货地址
     * @return 完善后的收货地址
     */
    private ShippingAddressBo completeShippingAddress(ShippingAddressBo address) {
        if (address == null) {
            return null;
        }

        RegionIdsReq regionIdsReq = new RegionIdsReq();
        regionIdsReq.setRegionIds(Arrays.asList(address.getRegionId()));
        Map<String, AllPathRegionResp> regionRespMap = baseRegionService.getAllPathRegions(regionIdsReq);
        AllPathRegionResp allPathRegionResp = regionRespMap.get(address.getRegionId().toString());
        if (allPathRegionResp != null) {
            address.setProvinceId(allPathRegionResp.getProvinceId());
            address.setProvinceName(allPathRegionResp.getProvinceName());
            address.setCityId(allPathRegionResp.getCityId());
            address.setCityName(allPathRegionResp.getCityName());
            address.setDistrictName(allPathRegionResp.getCountyName());
            address.setStreetName(allPathRegionResp.getTownsNames());
            address.setRegionFullName(allPathRegionResp.getProvinceName() + " " + allPathRegionResp.getCityName() + " " + allPathRegionResp.getCountyName() + " " + allPathRegionResp.getTownsNames());
            address.setRegionPath(allPathRegionResp.getProvinceId() + "," + allPathRegionResp.getCityId() + "," + allPathRegionResp.getCountyId() + "," + allPathRegionResp.getTownIds());
        }
        return address;
    }

    private BasePreviewChangeQuantityContext chooseChangeContext(PreviewChangeSkuQuantityParamBo changeParam) {
        // 获取用户选择的收货地址，用于计算运费和展示
        Long shippingAddressId = changeParam.getShippingAddressId();
        Long userId = changeParam.getUserId();
        ShippingAddressBo shippingAddress = null;
        if (shippingAddressId != null) {
            shippingAddress = userShippingAddressRemoteService.getShippingAddress(shippingAddressId, userId);
        }
        // 限时购
        if (changeParam.getFlashSaleId() != null) {
            ChangePreviewFlashSaleSkuQuantityContext context = new ChangePreviewFlashSaleSkuQuantityContext();
            context.setChangeQuantityParam(changeParam);
            context.setUserId(changeParam.getUserId());
            context.setShippingAddress(shippingAddress);
            return context;
        }
        // 组合购
        if (changeParam.getCollocationId() != null) {
            ChangePreviewCollocationSkuQuantityContext context = new ChangePreviewCollocationSkuQuantityContext();
            context.setChangeQuantityParam(changeParam);
            context.setUserId(changeParam.getUserId());
            context.setShippingAddress(shippingAddress);
            return context;
        }
        // 立即购买
        if (Boolean.TRUE.equals(changeParam.getWhetherBuyNow())) {
            ChangePreviewBuyNowSkuQuantityContext context = new ChangePreviewBuyNowSkuQuantityContext();
            context.setChangeQuantityParam(changeParam);
            context.setUserId(changeParam.getUserId());
            context.setShippingAddress(shippingAddress);
            return context;
        }
        // 普通购物车
        ChangePreviewCartSkuQuantityContext context = new ChangePreviewCartSkuQuantityContext();
        context.setChangeQuantityParam(changeParam);
        context.setUserId(changeParam.getUserId());
        context.setShippingAddress(shippingAddress);
        return context;
    }


    /**
     * 根据店铺交易设置，校验订单金额和订单商品数量
     *
     * @param buildResult 构建结果，包含订单商品列表、订单金额、订单商品数量等
     */
    private void validateConfigAmountAndQuantity(BuildResult buildResult) {
        List<ShopProductListBo> shopOrderList = buildResult.getShopProductList();
        if (CollUtil.isEmpty(shopOrderList)) {
            throw new BusinessException("订单商品不能为空");
        }
        List<Long> shopIdList = shopOrderList.stream()
                .map(so -> so.getShop().getShopId())
                .distinct()
                .collect(Collectors.toList());
        // 还要校验订单金额和订单商品数量
        Map<Long, ShopTradeSettingBo> shopTradeSettingMap = settingRemoteService.getShopTradeSetting(shopIdList);
        if (MapUtil.isEmpty(shopTradeSettingMap)) {
            return;
        }
        for (ShopProductListBo so : shopOrderList) {
            ShopTradeSettingBo setting = shopTradeSettingMap.get(so.getShop().getShopId());
            if (setting == null) {
                continue;
            }
            ShoppingCartShopBo shop = so.getShop();
            if (shop.getSelectedTotalAmount() == null || shop.getSelectedQuantity() == null) {
                throw new BusinessException("订单金额和订单商品数量不能为空");
            }
            boolean amountMatch = shop.getSelectedTotalAmount().compareTo(NumberUtil.nullToZero(setting.getPurchaseMinPrice())) >= 0;
            int settingQuantity = setting.getPurchaseMinQuantity() == null ? 0 : setting.getPurchaseMinQuantity();
            boolean quantityMatch = shop.getSelectedQuantity() >= settingQuantity;
            if (ShopTradeSettingValidTypeEnum.MATCH_BOTH.getCode().equals(setting.getPurchaseMinValidType())) {
                // 任意一个不满足，抛出异常
                if (!amountMatch || !quantityMatch) {
                    throw new BusinessException(String.format("%s店铺的订单需同时满足起购金额%s元和起购数量%s件", shop.getShopName(), setting.getPurchaseMinPrice(), setting.getPurchaseMinQuantity()));
                }
            }
            else if (ShopTradeSettingValidTypeEnum.MATCH_ANY.getCode().equals(setting.getPurchaseMinValidType())) {
                // 都不满足，抛出异常
                if (!amountMatch && !quantityMatch) {
                    throw new BusinessException(String.format("%s店铺的订单需满足起购金额%s元或起购数量%s件", shop.getShopName(), setting.getPurchaseMinPrice(), setting.getPurchaseMinQuantity()));
                }
            }
        }
    }


}
