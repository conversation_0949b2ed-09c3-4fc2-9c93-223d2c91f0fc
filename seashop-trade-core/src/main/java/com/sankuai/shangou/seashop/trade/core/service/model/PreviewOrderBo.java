package com.sankuai.shangou.seashop.trade.core.service.model;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;

import com.sankuai.shangou.seashop.trade.core.service.model.cart.PreviewOrderSummaryBo;
import lombok.*;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 订单预览信息业务对象
 *
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PreviewOrderBo {

    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;
    /**
     * 预览订单页所有商品总金额
     */
    private BigDecimal totalAmount;
    /**
     * 默认的收货地址
     */
    private ShippingAddressBo shippingAddress;
    /**
     * 限时购活动id。如果不为空，代表是限时购，商品列表只会有一条记录
     */
    private Long flashSaleId;
    /**
     * 组合购活动id。如果不为空，代表是组合购
     */
    private Long collocationId;
    /**
     * 预览订单页汇总信息
     */
    private PreviewOrderSummaryBo summary;


    private boolean success = true;
    /**
     * 订单ID，创建订单成功会有
     */
    private List<String> orderIdList;
    /**
     * 是否立即购买
     */
    private Boolean whetherBuyNow;
    /**
     * 是否需要支付。如果实付金额时0元，则不需要调起支付
     */
    private Boolean needPay;

    /**
     * 所有订单ID 的集合，包括无需支付的
     */
    private List<String> allOrderIdList;

    public static PreviewOrderBo defaultEmpty() {
        PreviewOrderBo cartBo = new PreviewOrderBo();
        cartBo.setShopProductList(Collections.emptyList());
        return cartBo;
    }

}
