package com.sankuai.shangou.seashop.product.core.mq.publisher;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import com.sankuai.shangou.seashop.rocketmq.constant.TraceConstant;
import com.sankuai.shangou.seashop.rocketmq.utils.TraceUtils;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageHeaders;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.common.constant.MafkaConstant;
import com.sankuai.shangou.seashop.product.core.mq.model.SyncStockMessage;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/20 11:16
 */
@Service
@Slf4j
public class SyncStockPublisher {

    @Resource
    private DefaultRocketMq defaultRocketMq;

    /**
     * 发送同步库存消息
     *
     * @param messages  同步库存消息集合
     * @param productId 商品id
     */
    public void sendMessage(List<SyncStockMessage> messages, Long productId) {
        log.info("发送库存同步消息-data：{}", JsonUtil.toJsonString(messages));
        try {
            String partKey = MafkaConstant.PART_KEY_SYNC_STOCK_PREFIX + productId;

            List<Message<String>> msgList = messages.stream().map(item -> MessageBuilder
                    .withPayload(JsonUtil.toJsonString(item))
                    .setHeader(MessageHeaders.CONTENT_TYPE, "text/plain")
                    .setHeader(TraceConstant.MDC_TRACE_ID, TraceUtils.getTraceId())
                    .build()).collect(Collectors.toList());
            //defaultRocketMq.syncSendOrderly(MafkaConstant.TOPIC_SYNC_STOCK, msgList, partKey);
            for(SyncStockMessage msg : messages) {
                defaultRocketMq.syncSend(MafkaConstant.TOPIC_SYNC_STOCK, msg);
            }
        } catch (Exception e) {
            log.error("发送库存同步消息-data：{}", JsonUtil.toJsonString(messages), e);
        }
    }

}
