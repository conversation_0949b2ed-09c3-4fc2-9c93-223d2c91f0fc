package com.sankuai.shangou.seashop.trade.core.mq.listener;

import javax.annotation.Resource;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.trade.core.mq.model.ProductVisitMessage;
import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;
import com.sankuai.shangou.seashop.trade.core.service.model.product.ProductVisitBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/12 9:49
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_PRODUCT_VISIT,
//        group = MafkaConst.GROUP_PRODUCT_VISIT)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_PRODUCT_VISIT  + "_${spring.profiles.active}"
        , consumerGroup = MafkaConst.GROUP_PRODUCT_VISIT + "_${spring.profiles.active}")
public class ProductVisitListener implements RocketMQListener<String> {

    @Resource
    private TradeProductService tradeProductService;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
//        String body = mafkaMessage.getBody().toString();
//        log.info("【mafka消费】【更新商品浏览数】消息内容为: {}", body);
//        ProductVisitMessage productVisitMessage = JsonUtil.parseObject(body, ProductVisitMessage.class);
//
//        ProductVisitBo productVisitBo = new ProductVisitBo();
//        productVisitBo.setProductId(productVisitMessage.getProductId());
//        productVisitBo.setAddVisitCounts(productVisitMessage.getAddVisitCounts());
//        productVisitBo.setMessageId(mafkaMessage.getMessageID());
//        tradeProductService.updateProductVisitCount(productVisitBo);
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(String body) {
        log.info("【mafka消费】【更新商品浏览数】消息内容为: {}", body);
        ProductVisitMessage productVisitMessage = JsonUtil.parseObject(body, ProductVisitMessage.class);

        ProductVisitBo productVisitBo = new ProductVisitBo();
        productVisitBo.setProductId(productVisitMessage.getProductId());
        productVisitBo.setAddVisitCounts(productVisitMessage.getAddVisitCounts());
//        productVisitBo.setMessageId(mafkaMessage.getMessageID());
        tradeProductService.updateProductVisitCount(productVisitBo);
    }
}
