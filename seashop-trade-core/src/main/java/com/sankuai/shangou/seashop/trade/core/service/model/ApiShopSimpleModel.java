package com.sankuai.shangou.seashop.trade.core.service.model;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description: 店铺简单查询返回对象
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class ApiShopSimpleModel extends BaseThriftDto {

    private Long id;

    // 店铺名称
    private String shopName;

    // 最大保证金
    private BigDecimal maxCashDeposit;
}
