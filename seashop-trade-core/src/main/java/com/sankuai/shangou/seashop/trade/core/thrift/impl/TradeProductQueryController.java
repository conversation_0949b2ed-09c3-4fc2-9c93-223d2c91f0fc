package com.sankuai.shangou.seashop.trade.core.thrift.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;
import com.sankuai.shangou.seashop.trade.core.service.assist.product.detail.model.ProductBaseInfoBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.*;
import com.sankuai.shangou.seashop.trade.core.service.model.promotion.ShopAddonActivityParamBo;
import com.sankuai.shangou.seashop.trade.thrift.core.TradeProductQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.AddonActivityDto;
import com.sankuai.shangou.seashop.trade.thrift.core.dto.TradeProductDto;
import com.sankuai.shangou.seashop.trade.thrift.core.request.*;
import com.sankuai.shangou.seashop.trade.thrift.core.response.*;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.CalculateFreightResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.ProductBaseInfoResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.QueryProductByIdListResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/tradeProduct")
public class TradeProductQueryController implements TradeProductQueryFeign {

    @Resource
    private TradeProductService tradeProductService;

    @PostMapping(value = "/search", consumes = "application/json")
    @Override
    public ResultDto<SearchTradeProductResp> search(@RequestBody SearchTradeProductReq searchReq) throws TException {
        log.info("【商品搜索】交易商品分页搜索, searchReq={}", searchReq);
        return ThriftResponseHelper.responseInvoke("search", searchReq, func -> {
            SearchProductBo searchBo = JsonUtil.copy(searchReq, SearchProductBo.class);
            SearchTradeProductRespBo respBo = tradeProductService.search(searchBo);
            return JsonUtil.copy(respBo, SearchTradeProductResp.class);
        });
    }

    @PostMapping(value = "/searchInShop", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<TradeProductDto>> searchInShop(@RequestBody SearchProductInShopReq searchReq) throws TException {
        log.info("【店铺内商品搜索】交易商品分页搜索, searchInShop={}", searchReq);
        return ThriftResponseHelper.responseInvoke("searchInShop", searchReq, func -> {
            SearchProductInShopBo searchBo = JsonUtil.copy(searchReq, SearchProductInShopBo.class);
            BasePageResp<SearchedTradeProductBo> respBo = tradeProductService.searchInShop(searchBo);
            return JsonUtil.copy(respBo, new TypeReference<BasePageResp<TradeProductDto>>() {
            });
        });
    }

    @PostMapping(value = "/queryHotSaleProduct", consumes = "application/json")
    @Override
    public ResultDto<HotSaleProductResp> queryHotSaleProduct(@RequestBody QueryHotSaleProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryHotSaleProduct", request, req -> {
            req.checkParameter();

            List<SearchedTradeProductBo> productBoList = tradeProductService.queryHotSaleProduct(JsonUtil.copy(req, SearchProductBo.class));
            return HotSaleProductResp.builder().productList(JsonUtil.copyList(productBoList, TradeProductDto.class)).build();
        });
    }

    @PostMapping(value = "/queryMallShopProduct", consumes = "application/json")
    @Override
    public ResultDto<MallShopProductResp> queryMallShopProduct(@RequestBody QueryHotSaleProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryMallShopProduct", request, req -> {
            req.checkParameter();

            return tradeProductService.queryMallShopProduct(JsonUtil.copy(req, SearchProductBo.class));
        });
    }

    @PostMapping(value = "/queryHotAttentionProduct", consumes = "application/json")
    @Override
    public ResultDto<HotAttentionProductResp> queryHotAttentionProduct(@RequestBody QueryHotAttentionProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryHotAttentionProduct", request, req -> {
            req.checkParameter();

            List<SearchedTradeProductBo> productBoList = tradeProductService.queryHotAttentionProduct(JsonUtil.copy(req, SearchProductBo.class));
            return HotAttentionProductResp.builder().productList(JsonUtil.copyList(productBoList, TradeProductDto.class)).build();
        });
    }

    @PostMapping(value = "/queryGuessYouLike", consumes = "application/json")
    @Override
    public ResultDto<GuessYouLikeProductResp> queryGuessYouLike(@RequestBody QueryGuessYouLikeReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryGuessYouLike", request, req -> {
            req.checkParameter();

            List<SearchedTradeProductBo> productBoList = tradeProductService.queryGuessYouLike(req.getProductId());
            return GuessYouLikeProductResp.builder().productList(JsonUtil.copyList(productBoList, TradeProductDto.class)).build();
        });
    }

    @PostMapping(value = "/queryNewestProduct", consumes = "application/json")
    @Override
    public ResultDto<NewestProductResp> queryNewestProduct(@RequestBody QueryNewestProductReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryNewestProduct", request, req -> {
            req.checkParameter();

            List<SearchedTradeProductBo> productBoList = tradeProductService.queryNewestProduct(req.getShopId());
            return NewestProductResp.builder().productList(JsonUtil.copyList(productBoList, TradeProductDto.class)).build();
        });
    }

    @PostMapping(value = "/searchAddonProduct", consumes = "application/json")
    @Override
    public ResultDto<SearchAddonProductResp> searchAddonProduct(@RequestBody QueryAddonProductReq searchReq) throws TException {
        log.info("【凑单】获取凑单商品列表, searchReq={}", searchReq);
        return ThriftResponseHelper.responseInvoke("searchAddonProduct", searchReq, func -> {
            QueryAddonProductParamBo param = JsonUtil.copy(searchReq, QueryAddonProductParamBo.class);
            AddonProductResultBo result = tradeProductService.searchAddonProduct(param);
            return JsonUtil.copy(result, SearchAddonProductResp.class);
        });
    }

    @PostMapping(value = "/getShopAddonActivity", consumes = "application/json")
    @Override
    public ResultDto<AddonActivityResp> getShopAddonActivity(@RequestBody QueryShopAddonActivityReq req) throws TException {
        log.info("【凑单】获取凑单活动列表, req={}", req);
        return ThriftResponseHelper.responseInvoke("getShopAddonActivity", req, func -> {
            req.checkParameter();
            ShopAddonActivityParamBo param = JsonUtil.copy(req, ShopAddonActivityParamBo.class);
            List<AddonActivityBo> activityBoList = tradeProductService.getShopAddonActivity(param);
            List<AddonActivityDto> activityDtoList = JsonUtil.copyList(activityBoList, AddonActivityDto.class);
            AddonActivityResp resp = new AddonActivityResp();
            resp.setActivityList(activityDtoList);
            return resp;
        });
    }

    @PostMapping(value = "/queryProductBaseInfo", consumes = "application/json")
    @Override
    public ResultDto<ProductBaseInfoResp> queryProductBaseInfo(@RequestBody QueryProductDetailReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryProductBaseInfo", request, req -> {
            req.checkParameter();

            ProductBaseInfoBo productBaseInfoBo = tradeProductService.queryProductBaseInfo(JsonUtil.copy(req, QueryProductDetailBo.class));
            return JsonUtil.copy(productBaseInfoBo, ProductBaseInfoResp.class);
        });
    }

    @PostMapping(value = "/calculateFreight", consumes = "application/json")
    @Override
    public ResultDto<CalculateFreightResp> calculateFreight(@RequestBody CalculateFreightReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("calculateFreight", request, req -> {
            req.checkParameter();

            return tradeProductService.calculateFreight(JsonUtil.copy(request, CalculateFreightReq.class));
        });
    }

    @PostMapping(value = "/queryProductByIdList", consumes = "application/json")
    @Override
    public ResultDto<List<QueryProductByIdListResp>> queryProductByIdList(@RequestBody ProductIdsReq request) throws TException {
        request.checkParameter();
        return ThriftResponseHelper.responseInvoke("queryProductByIdList", request, req -> tradeProductService.queryProductByIdList(req));
    }
}
