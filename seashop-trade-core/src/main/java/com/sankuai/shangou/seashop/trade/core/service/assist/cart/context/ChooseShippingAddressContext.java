package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.util.Date;

import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ChooseShippingAddressContext extends SubmitOrderContext {

    /**
     * 提交订单时的入口时间，后续对于营销的校验以及订单的下单时间等，都基于这个入口时间，
     * 防止逻辑执行过程中每次取最新的时间导致判断不一致
     */
    private Date currentTime;

    @Override
    public BuildType getBuildType() {
        return BuildType.CHOOSE_SHIPPING_ADDRESS;
    }

    @Override
    public boolean needDiscount() {
        return false;
    }

    @Override
    public boolean needReduction() {
        return false;
    }

    @Override
    public boolean needReCalPrice() {
        return false;
    }
}
