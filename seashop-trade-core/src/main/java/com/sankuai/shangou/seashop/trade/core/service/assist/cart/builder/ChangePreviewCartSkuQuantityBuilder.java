package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.constant.MessageConst;
import com.sankuai.shangou.seashop.trade.common.enums.TradeResultCode;
import com.sankuai.shangou.seashop.trade.common.exception.ChangeQuantityException;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteProductSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.ShoppingCartAssist;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChangePreviewCartSkuQuantityContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewChangeSkuQuantityParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.dao.core.repository.ShoppingCartRepository;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单预览页-修改购物车数量
 * 1. 数据来源为页面提交的数据，目前为了简便，直接前端将预览接口返回的数据全部传入了
 * 2. 购物车预览的场景，需要考虑数量变化对能销售各种优惠的影响
 * 3. 购物车预览的场景，需要将变更同步到购物车
 * <AUTHOR>
 */
@Service
@Slf4j
public class ChangePreviewCartSkuQuantityBuilder extends ChangePreviewSkuQuantityCommonBuilder {

    @Resource
    private ShoppingCartAssist shoppingCartAssist;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private ShoppingCartRepository shoppingCartRepository;

    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        ChangePreviewCartSkuQuantityContext buildContext = (ChangePreviewCartSkuQuantityContext) context;
        // 理论上对象一定会存在，没做判空处理
        PreviewChangeSkuQuantityParamBo changeQuantityParam = buildContext.getChangeQuantityParam();
        ShoppingCartShopBo shop = changeQuantityParam.getShop();
        List<ShopProductBo> productList = changeQuantityParam.getProductList();
        OrderAdditionalBo additional = changeQuantityParam.getAdditional();

        List<Long> productIdList = productList.stream().map(ShopProductBo::getProductId).collect(Collectors.toList());

        // 校验并重置相关数据
        validAndReset(context.getUserId(), shop, productList, changeQuantityParam, additional, productIdList);

        ShopProductListBo shopProductListBo = new ShopProductListBo();
        shopProductListBo.setShop(shop);
        shopProductListBo.setProductList(productList);
        shopProductListBo.setAdditional(additional);
        shopProductListBo.setProductIdList(productIdList);

        // 选择了优惠券时，不处理满减了
        boolean hasCoupon = super.hasCoupon(additional);
        if (hasCoupon) {
            buildContext.resetNeedReduction(false);
        }
        return Collections.singletonList(shopProductListBo);
    }

    @Override
    protected void extraChangeProcess(BuildContext context, BaseBuildDataHolder dataHolder) {
        ChangePreviewCartSkuQuantityContext buildContext = (ChangePreviewCartSkuQuantityContext) context;
        // 理论上对象一定会存在，没做判空处理
        PreviewChangeSkuQuantityParamBo changeQuantityParam = buildContext.getChangeQuantityParam();
        // 购物车预览的场景，需要将变更同步到购物车
        shoppingCartRepository.updateSkuQuantity(changeQuantityParam.getId(), changeQuantityParam.getQuantity());
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_CHANGE_CART_SKU_QUANTITY;
    }


    //**************************************************

    private void validAndReset(Long userId, ShoppingCartShopBo shop, List<ShopProductBo> productList,
                               PreviewChangeSkuQuantityParamBo changeQuantityParam,
                               OrderAdditionalBo additional, List<Long> productIdList) {
        if (changeQuantityParam.getId() == null || changeQuantityParam.getId() <= 0) {
            throw new BusinessException("购物车ID不能为空");
        }
        // 需要修改的目标数量
        Long quantity = changeQuantityParam.getQuantity();
        String changeSkuId = changeQuantityParam.getSkuId();
        if (quantity == null || quantity <= 0) {
            throw new BusinessException("商品数量必须大于0");
        }
        // 获取商品SKU信息
        List<String> skuIdList = productList.stream().map(ShopProductBo::getSkuId).collect(Collectors.toList());
        Map<String, RemoteProductSkuBo> skuMap = productRemoteService.queryProductSkuToMap(skuIdList);
        if (MapUtil.isEmpty(skuMap) || skuMap.get(changeSkuId) == null) {
            throw new BusinessException("商品不存在或已下架");
        }
        RemoteProductSkuBo changedSku = skuMap.get(changeSkuId);
        String productName = getShortProductName(changedSku.getProductName());
        // 倍数起购量
        int multipleCount = changedSku.getMultipleCount() == null ? CommonConst.DEFAULT_BUY_COUNT : changedSku.getMultipleCount();
        // 计算并设置起购量，起购量需要综合阶梯价和倍数起购量进行计算
        int minBuyCount = shoppingCartAssist.calculateBaseBuyCount(multipleCount, changedSku.getLadderPriceList());
        String unit = StrUtil.nullToEmpty(changedSku.getMeasureUnit());
        if (changedSku.getSaleStatus() != ProductEnum.SaleStatusEnum.ON_SALE) {
            throw new BusinessException("商品已下架");
        }
        if (quantity < minBuyCount) {
            String message = String.format(MessageConst.PreOrder.LESS_THAN_MIN_BUY_COUNT, minBuyCount, unit);
            throw new BusinessException(message);
        }
        // 默认错误码，需要前端根据返回的数据重置输入框数据，然后重新请求接口
        int errCode = TradeResultCode.BIZ_CHANGE_QUANTITY_FAIL.getValue();
        if (quantity > changedSku.getStock()) {
            long errQ = (changedSku.getStock() / multipleCount) * multipleCount;
            // 需要变更的数量小于等于0时，前端不变更
            if (errQ <= 0) {
                errCode = TradeResultCode.BIZ_CHANGE_QUANTITY_FAIL_HOLD.getValue();
            }
            throw new ChangeQuantityException(errCode, productName,
                    String.format(CommonConst.MESSAGE_CART_CHANGE_CNT_LACK_STOCK, changedSku.getStock(), unit), errQ);
        }
        if (quantity % multipleCount != 0) {
            long b = quantity / multipleCount;
            // 给用户重置的数量优先向上取
            long errQ = (b + 1) * multipleCount;
            // 因为前面已经校验了库存，所以此时 b * sku.getMultipleCount() 一定在库存范围内
            if (errQ > changedSku.getStock()) {
                errQ = b * multipleCount;
            }
            // 如果数量小于最小起购量，则前端输入框还原不变化
            if (errQ < minBuyCount) {
                errCode = TradeResultCode.BIZ_CHANGE_QUANTITY_FAIL_HOLD.getValue();
            }
            throw new ChangeQuantityException(errCode, productName,
                    String.format(CommonConst.MESSAGE_CART_CHANGE_CNT_MULTIPLE, multipleCount), errQ);
        }
        Map<Long, Long> productBuyCountMap = super.getProductBuyCount(userId, productIdList);
        long boughtCount = NumberUtil.nullToZero(productBuyCountMap.get(changedSku.getProductId()));
        if (needCheckMaxBuyCount(changedSku.getMaxBuyCount()) && quantity + boughtCount > changedSku.getMaxBuyCount()) {
            long remain = changedSku.getMaxBuyCount() - boughtCount;
            log.info("页面提交的数量是否大于限购数, quantity={}, minBuyCount={}, maxBuyCount={}, stock={}, bought={}, remain={}",
                    quantity, minBuyCount, changedSku.getMaxBuyCount(), changedSku.getStock(), boughtCount, remain);
            if (remain < 0) {
                remain = 0;
            }
            long errQ = remain;
            // 如果剩余数量为0，或者数量小于最小起购量，或者剩余小于库存，则前端还原不变化，因为没法计算一个还能正常购买的值
            if (remain == 0 || remain < minBuyCount || changedSku.getStock() < remain) {
                errCode = TradeResultCode.BIZ_CHANGE_QUANTITY_FAIL_HOLD.getValue();
            } else {
                long b = remain / multipleCount;
                // 这里的重置只能向下取
                errQ = b * multipleCount;
                // 如果数量小于最小起购量，则前端还原不变化
                if (errQ < minBuyCount) {
                    errCode = TradeResultCode.BIZ_CHANGE_QUANTITY_FAIL_HOLD.getValue();
                }
            }
            String message = String.format(CommonConst.MESSAGE_CART_CHANGE_CNT_MAX_BUY_COUNT,
                    changedSku.getMaxBuyCount(), unit, boughtCount, unit, remain, unit);
            throw new ChangeQuantityException(errCode, productName, message, errQ);
        }


        // 校验通过，重置数据，修改sku的总金额用当前的重置，其他的用页面传入的不改变
        BigDecimal shopProductTotalAmount = BigDecimal.ZERO;
        for (ShopProductBo shopProductBo : productList) {
            RemoteProductSkuBo sku = skuMap.get(shopProductBo.getSkuId());
            // 即使不是当前修改的，价格也要用之前的价格重置，因为会可能需要重新计算折扣等
            // 重置价格，后面重新计算
            shopProductBo.setRealSalePrice(shopProductBo.getRealSalePrice());
            shopProductBo.setFinalSalePrice(shopProductBo.getRealSalePrice());
            shopProductBo.setDiscountSalePrice(null);
            shopProductBo.setWhetherSelected(true);
            shopProductBo.setFreightTemplateId(sku.getFreightTemplateId());
            shopProductBo.setWeight(sku.getWeight());
            shopProductBo.setVolume(sku.getVolume());
            // 每个商品都要设置阶梯价，修改数量业务中需要重新根据数量重置其他sku的价格
            shopProductBo.setLadderPriceList(sku.getLadderPriceList());
            // 购买限制只有当前修改的才重置
            if (changeSkuId.equals(shopProductBo.getSkuId())) {
                shopProductBo.setQuantity(quantity);
                shopProductBo.setSkuStock(changedSku.getStock());
                shopProductBo.setMaxBuyCount(changedSku.getMaxBuyCount());
                shopProductBo.setMinBuyCount(minBuyCount);
                shopProductBo.setMultipleCount(multipleCount);
                shopProductBo.setFreightTemplateId(changedSku.getFreightTemplateId());
            }
            // 重置总金额
            BigDecimal totalAmount = NumberUtil.mul(shopProductBo.getFinalSalePrice(), quantity).setScale(2, RoundingMode.HALF_UP);
            // 返回的数据，重置商品总金额
            shopProductBo.setTotalAmount(totalAmount);
            // 用最新的累计
            shopProductTotalAmount = shopProductTotalAmount.add(shopProductBo.getTotalAmount());
        }

        // 重置店铺金额数据，限时购只有一个商品
        shop.setProductTotalAmount(shopProductTotalAmount);
        shop.setSelectedTotalAmount(shopProductTotalAmount);
        // 重置营销
        shop.setPromotionDescList(new ArrayList<>(5));
        shop.setReductionDescList(new ArrayList<>(5));

        additional.setReductionAmount(BigDecimal.ZERO);
        additional.setReductionActivityId(null);
        additional.setReductionConditionAmount(null);

        additional.setDiscountAmount(BigDecimal.ZERO);


    }

}
