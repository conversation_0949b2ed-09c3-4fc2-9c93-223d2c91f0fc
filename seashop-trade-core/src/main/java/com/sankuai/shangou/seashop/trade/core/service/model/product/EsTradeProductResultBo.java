package com.sankuai.shangou.seashop.trade.core.service.model.product;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.*;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EsTradeProductResultBo {

    /**
     * 品牌ID，通过聚合结果得到
     */
    private List<Long> branchIdList;
    /**
     * 商品属性，通过聚合结果得到
     */
    private List<ProductAttributeBo> attributeList;
    /**
     * 类目ID，通过聚合结果得到
     */
    private List<Long> categoryIdList;
    /**
     * 商品分页列表
     */
    private BasePageResp<EsTradeProductBo> productPage;

    public static EsTradeProductResultBo defaultEmpty() {
        EsTradeProductResultBo resultBo = new EsTradeProductResultBo();
        resultBo.setBranchIdList(Collections.emptyList());
        resultBo.setAttributeList(Collections.emptyList());
        resultBo.setCategoryIdList(Collections.emptyList());
        return resultBo;
    }

}
