package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;

import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ProductEnum;
import com.sankuai.shangou.seashop.trade.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteProductSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;
import com.sankuai.shangou.seashop.trade.thrift.core.enums.ShoppingCartEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ExclusiveShopDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ExclusiveShopResp;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 基于购物车来构建店铺和商品的构建器
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCartShopProductBuilder extends AbstractShopProductBuilder<CartBuildDataHolder> {

    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private ShoppingCartAssist shoppingCartAssist;

    @Override
    protected CartBuildDataHolder buildShopAndProductList(BuildContext context) {
        // 获取购物车列表，在提交订单之前的数据，归根结底是以购物车为基础的
        List<ShoppingCart> userCartList = getShoppingCartList(context);
        if (CollectionUtils.isEmpty(userCartList)) {
            log.warn("购物车列表为空，请检查");
            return null;
        }
        // 根据入参构建基本的商品对象
        List<ShopProductBo> userShopSkuList = buildProductList(userCartList);
        if (CollectionUtils.isEmpty(userShopSkuList)) {
            log.warn("查询商品后列表为空，请检查");
            return null;
        }
        // 设置店铺信息和商品状态
        appendShopInfoAndSkuStatus(context.getUserId(), userShopSkuList);
        // 初始化数据，可以做一些处理。比如购物车需要区分是否销售中
        CartBuildDataHolder dataHolder = tempHoldData(userShopSkuList);
        // 对销售中的购物车sku根据店铺进行分组，只有购物车有非销售中的商品，其他业务类型数据都是有效的
        Map<ShoppingCartShopBo, List<ShopProductBo>> shopGroupedMap = groupByShop(dataHolder.getValidProductList());
        // 如果有效的店铺分组为空，直接返回
        if (MapUtils.isEmpty(shopGroupedMap)) {
            return dataHolder;
        }
        dataHolder.setShopGroupedMap(shopGroupedMap);
        // 实时取数据这边，都获取营销
        // 获取并店铺营销相关，接口同步返回当前商家在店铺的专享价
        appendShopPromotion(context, dataHolder);
        // 按需设置并计算商品价格，商品价格包括了折扣后的价格，因为折扣会影响单价，所以在这个里面处理了折扣
        List<ShopProductListBo> shopProductList = buildShopProduct(context, dataHolder);
        dataHolder.setShopProductList(shopProductList);
        return dataHolder;
    }

    @Override
    protected void extendResult(BuildResult result, CartBuildDataHolder dataHolder) {
        result.setInvalidProductList(dataHolder.getInvalidProductList());
        result.setSuccess(!dataHolder.isAnyError());
        result.setErrMsg(dataHolder.getErrMsg());
    }

    //*****************************************************************************

    /**
     * 获取购物车列表，子类实现
     * <p>目前涉及业务：购物车列表、订单预览页面、提交订单校验</p>
     * <p>无论哪种业务，最终返回前端的数据是差不多的，都是基于店铺分组的商品列表，但不同的业务数据来源有些区别</p>
     * <AUTHOR>
     * @param context 构建信息参数上下文
     */
    protected abstract List<ShoppingCart> getShoppingCartList(BuildContext context);


    /**
     * 临时存储区分一下数据，比如购物车需要区分是否销售中
     * <AUTHOR>
     * @param userShopSkuList 用户购物车商品列表
     */
    protected abstract CartBuildDataHolder tempHoldData(List<ShopProductBo> userShopSkuList);









    //**********************************************************************************





    /**
     * 构建购物车商品sku
     * <AUTHOR>
     * @param userCartList 购物车数据
     */
    private List<ShopProductBo> buildProductList(List<ShoppingCart> userCartList) {
        List<String> skuIdList = userCartList.stream()
                .map(ShoppingCart::getSkuId)
                .collect(Collectors.toList());
        // 获取商品SKU信息
        Map<String, RemoteProductSkuBo> skuMap = productRemoteService.queryProductSkuToMap(skuIdList);
        return userCartList.stream()
                .map(cart -> buildProduct(cart, skuMap))
                .collect(Collectors.toList());
    }

    /**
     * 基于购物车数据和商品数据，构建基础的店铺商品对象
     * <p>先以最新的商品属性进行设置，后续如果要以页面数据为准，再重置</p>
     * <AUTHOR>
     * @param cart 购物车数据
     * @param skuMap 商品SKU
     */
    private ShopProductBo buildProduct(ShoppingCart cart, Map<String, RemoteProductSkuBo> skuMap) {
        ShopProductBo cartSkuBo = new ShopProductBo();
        cartSkuBo.setId(cart.getId());
        cartSkuBo.setUserId(cart.getUserId());
        cartSkuBo.setProductId(cart.getProductId());
        cartSkuBo.setSkuId(cart.getSkuId());
        cartSkuBo.setQuantity(cart.getQuantity());
        cartSkuBo.setAddTime(cart.getAddTime());
        cartSkuBo.setWhetherSelected(cart.getWhetherSelect());

        RemoteProductSkuBo skuDto = skuMap.get(cart.getSkuId());
        if (skuDto == null) {
            log.info("购物车商品SKU不存在，skuId={}", cart.getSkuId());
            cartSkuBo.setWhetherDeleted(true);
            return cartSkuBo;
        }
        cartSkuBo.setShopId(skuDto.getShopId());
        cartSkuBo.setProductName(skuDto.getProductName());
        cartSkuBo.setMainImagePath(getSkuImagePath(skuDto));
        // 库存，SKU的库存，如果商品没有SKU，也会有一条SKU记录
        cartSkuBo.setSkuStock(skuDto.getStock());
        BigDecimal salePrice = getSkuSalePrice(skuDto);
        // 原售价，初始为商品的市场价
        cartSkuBo.setOriginSalePrice(salePrice);
        // 实际售价(考虑专享价和阶梯价)，初始为商品的市场价
        cartSkuBo.setRealSalePrice(salePrice);
        // 最终售价(考虑折扣)，初始为商城价
        cartSkuBo.setFinalSalePrice(salePrice);
        // 阶梯价，商品查询的时候直接返回
        cartSkuBo.setLadderPriceList(skuDto.getLadderPriceList());
        // 最大购买量，商品表设置
        cartSkuBo.setMaxBuyCount(skuDto.getMaxBuyCount());
        cartSkuBo.setMeasureUnit(skuDto.getMeasureUnit());
        // 倍数起购量
        int multipleCount = shoppingCartAssist.getMultipleCount(skuDto.getMultipleCount());
        cartSkuBo.setMultipleCount(multipleCount);
        // 计算并设置起购量，起购量需要综合阶梯价和倍数起购量进行计算
        int minBuyCount = shoppingCartAssist.calculateBaseBuyCount(multipleCount, skuDto.getLadderPriceList());
        cartSkuBo.setMinBuyCount(minBuyCount);

        cartSkuBo.setFreightTemplateId(skuDto.getFreightTemplateId());
        cartSkuBo.setWeight(skuDto.getWeight());
        cartSkuBo.setVolume(skuDto.getVolume());

        cartSkuBo.setSaleStatus(skuDto.getSaleStatus());
        cartSkuBo.setAuditStatus(skuDto.getAuditStatus());
        cartSkuBo.setWhetherDeleted(skuDto.getWhetherDelete());
        // 设置商品规格名称列表
        List<String> skuNameList = shoppingCartAssist.buildSkuDesc(skuDto);
        cartSkuBo.setSkuNameList(skuNameList);
        // 先设置默认的专享价标识，后续会根据专享价进行重置
        cartSkuBo.setWhetherExclusive(false);
        cartSkuBo.setProductCode(skuDto.getProductCode());
        cartSkuBo.setColor(skuDto.getSpec1Value());
        cartSkuBo.setSize(skuDto.getSpec2Value());
        cartSkuBo.setVersion(skuDto.getSpec3Value());
        cartSkuBo.setSkuAutoId(skuDto.getSkuAutoId());
        cartSkuBo.setCategoryId(skuDto.getCategoryId());
        cartSkuBo.setSku(skuDto.getSkuCode());
        // 设置OE码,品牌号,替换号，零件规格等字段
        cartSkuBo.setOeCode(skuDto.getOeCode());
        cartSkuBo.setBrandCode(skuDto.getBrandCode());
        cartSkuBo.setPartSpec(skuDto.getPartSpec());
        cartSkuBo.setAdaptableCar(skuDto.getAdaptableCar());
        cartSkuBo.setBrandName(skuDto.getBrandName());
        cartSkuBo.setBrandId(skuDto.getBrandId());
        return cartSkuBo;
    }

    /**
     * 设置店铺信息和商品状态
     * <AUTHOR>
     * @param userId 登录用户ID
     * @param cartSkuList 购物车商品列表
     * void
     */
    private void appendShopInfoAndSkuStatus(Long userId, List<ShopProductBo> cartSkuList) {
        // 获取店铺信息，接口同步返回当前商家在店铺里是否是专属
        List<Long> shopIdList = cartSkuList.stream()
                .map(ShopProductBo::getShopId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
       List<ShopResp> shopResps = shopRemoteService.queryShopByIds(shopIdList);
        for (ShopProductBo sku : cartSkuList) {
            ShopResp shop = shopResps.stream()
                    .filter(item -> item.getId().equals(sku.getShopId()))
                    .findFirst()
                    .orElse(null);
            if (shop == null) {
                sku.setSkuStatus(ShoppingCartEnum.SkuStatus.INVALID);
                continue;
            }
            sku.setShopName(shop.getShopName());
            // 商品非销售中，归类为失效
            if (Boolean.TRUE.equals(sku.getWhetherDeleted()) || StrUtil.isBlank(sku.getSkuId())) {
                sku.setSkuStatus(ShoppingCartEnum.SkuStatus.INVALID);
            } else if (!ProductEnum.SaleStatusEnum.ON_SALE.equals(sku.getSaleStatus())) {
                sku.setSkuStatus(ShoppingCartEnum.SkuStatus.OFF_SALE);
            } else if (!ProductEnum.AuditStatusEnum.ON_SALE.equals(sku.getAuditStatus())) {
                sku.setSkuStatus(ShoppingCartEnum.SkuStatus.OFF_SALE);
            } else if (sku.getSkuStock() == null || sku.getSkuStock() <= 0) {
                sku.setSkuStatus(ShoppingCartEnum.SkuStatus.SOLD_OUT);
            } else {
                sku.setSkuStatus(ShoppingCartEnum.SkuStatus.ON_SALE);
            }
        }
    }












}
