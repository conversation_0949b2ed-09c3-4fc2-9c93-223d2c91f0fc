package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.util.Date;

import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SubmitErpOrderBuildContext extends SubmitOrderContext {

    private Boolean autoPromotion;
    /**
     * 提交订单时的入口时间，后续对于营销的校验以及订单的下单时间等，都基于这个入口时间，
     * 防止逻辑执行过程中每次取最新的时间导致判断不一致
     */
    private Date currentTime;

    @Override
    public BuildType getBuildType() {
        return BuildType.SUBMIT_ERP_ORDER;
    }

    @Override
    public boolean needDiscount() {
        return true;
    }

    @Override
    public boolean needReduction() {
        //自己计算满减 自动选择最优惠优惠券
        return false;
    }



}
