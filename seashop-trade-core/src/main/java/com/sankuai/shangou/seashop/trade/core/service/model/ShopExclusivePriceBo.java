package com.sankuai.shangou.seashop.trade.core.service.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class ShopExclusivePriceBo {

    /**
     * 专享价活动ID
     */
    private Long activityId;
    /**
     * 专享价活动名称
     */
    private String activityName;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 专享价
     */
    private BigDecimal price;

}
