package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.List;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.PreviewBuyNowOrderBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

import lombok.extern.slf4j.Slf4j;

/**
 * 立即购买订单预览
 * <AUTHOR>
 */
@Service
@Slf4j
public class PreviewBuyNowOrderBuilder extends AbstractBuyNowBuilder {

    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        PreviewBuyNowOrderBuildContext previewOrderBuildContext = (PreviewBuyNowOrderBuildContext) context;
        String skuId = previewOrderBuildContext.getSkuId();
        Long quantity = previewOrderBuildContext.getQuantity();
        // 调用父类公共的构建方法
        return buildBuyNowShopProduct(context.getUserId(), skuId, quantity);
    }

    @Override
    protected void processExpand(BuildContext context, BaseBuildDataHolder dataHolder) {
        PreviewBuyNowOrderBuildContext buildContext = (PreviewBuyNowOrderBuildContext) context;
        // 预览相关的接口需要设置一些额外的数据
        super.fillExtraOrderPreviewData(context.getUserId(), dataHolder);
        // 计算并设置运费，单独计算运费，因为进入预览订单页如果商品在禁售区域不抛出异常
        calculateAndSetFreight(buildContext.getDefaultShippingAddress(), dataHolder, true);
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_BUY_NOW;
    }


    //*******************************************************************





}
