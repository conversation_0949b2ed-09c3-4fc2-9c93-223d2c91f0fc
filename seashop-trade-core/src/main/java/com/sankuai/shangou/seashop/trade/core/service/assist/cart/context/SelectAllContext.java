package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.util.List;

import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SelectAllContext implements BuildContext {

    private Long userId;
    /**
     * 是否选中
     */
    private Boolean whetherSelect;
    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;


    @Override
    public BuildType getBuildType() {
        return BuildType.SELECT_ALL;
    }

    @Override
    public boolean needDiscount() {
        return true;
    }

    @Override
    public boolean needReduction() {
        return true;
    }
}
