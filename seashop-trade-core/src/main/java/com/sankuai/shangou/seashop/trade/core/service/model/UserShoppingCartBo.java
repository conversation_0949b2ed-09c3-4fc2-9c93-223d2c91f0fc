package com.sankuai.shangou.seashop.trade.core.service.model;

import lombok.*;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserShoppingCartBo {

    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;
    /**
     * 失效的商品列表
     *
     */
    private List<ShopProductBo> invalidProductList;
    /**
     * 所有勾选的商品总金额
     */
    private BigDecimal totalAmount;
    /**
     * 购物车中所有商品是否选中，也是所有店铺是否是选中
     */
    private Boolean whetherAllSelected;
    /**
     * 所有勾选的商品总数量
     */
    private Long totalSelectedQuantity;

    public static UserShoppingCartBo defaultEmpty() {
        UserShoppingCartBo cartBo = new UserShoppingCartBo();
        cartBo.setShopProductList(Collections.emptyList());
        cartBo.setInvalidProductList(Collections.emptyList());
        return cartBo;
    }
}
