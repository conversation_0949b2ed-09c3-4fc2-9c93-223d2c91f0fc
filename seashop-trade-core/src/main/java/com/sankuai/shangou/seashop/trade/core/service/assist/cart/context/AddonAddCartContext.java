package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.util.List;

import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AddonAddCartContext implements BuildContext {

    private Long userId;
    private Long shopId;
    private ShoppingCartShopBo shop;
    /**
     * 商品列表。综合传入的以及新加购的
     */
    private List<ShopProductBo> productList;
    /**
     * 凑单加购的skuId
     */
    private String addonSkuId;

    /**
     * 是否需要计算折扣，如果凑单的活动是折扣，则需要计算折扣
     */
    private boolean shouldCalDiscount;
    private Integer type;
    private Long activityId;

    @Override
    public Long getUserId() {
        return this.userId;
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.ADDON;
    }

    @Override
    public boolean needDiscount() {
        return false;
    }

    @Override
    public boolean needReduction() {
        return false;
    }
}
