package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.promotion.core.remote.PromotionRemoteService;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteProductSkuBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationProductBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.promotion.RemoteCollocationSkuBo;
import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCustomShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.ShoppingCartAssist;
import com.sankuai.shangou.seashop.trade.core.service.model.PurchaseProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ExclusiveShopDto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractCollocationBuilder extends AbstractCustomShopProductBuilder {

    @Resource
    private PromotionRemoteService promotionRemoteService;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private ShoppingCartAssist shoppingCartAssist;

    protected List<ShopProductListBo> buildCollocationShopProduct(Long userId, Long collocationId, List<PurchaseProductBo> skuList, Date currentTime) {
        // 查询组合购数据
        RemoteCollocationBo collocationBo = promotionRemoteService.getCollocationById(collocationId);
        checkCollocation(collocationBo, currentTime);
        // 基于用户选择的组合购SKU与组合购配置，构建店铺商品列表
        List<ShopProductBo> productList = buildProductList(userId, skuList, collocationBo);

        // 店铺信息
        Long shopId = collocationBo.getShopId();
        ShopResp shopBo = shopRemoteService.queryShopByIds(Collections.singletonList(shopId)).stream()
                .findFirst()
                .orElse(null);
        if (shopBo == null) {
            throw new BusinessException("当前店铺不可用");
        }
        ShoppingCartShopBo shop = new ShoppingCartShopBo();
        shop.setShopId(shopId);
        shop.setShopName(shopBo.getShopName());
        shop.setShowAddonBtn(false);

        BigDecimal totalAmount = productList.stream()
                .map(ShopProductBo::getTotalAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        shop.setSelectedTotalAmount(totalAmount);
        shop.setProductTotalAmount(totalAmount);
        shop.setSelectedQuantity(productList.stream().mapToLong(ShopProductBo::getQuantity).sum());


        ShopProductListBo shopProductListBo = new ShopProductListBo();
        shopProductListBo.setShop(shop);
        shopProductListBo.setProductList(productList);
        return Collections.singletonList(shopProductListBo);
    }


    /**
     * 组合购只需要处理运费，与满减活动互斥
     *
     * @param dataHolder
     * @param shippingAddress void
     * <AUTHOR>
     */
    protected void processCollocationExpand(BaseBuildDataHolder dataHolder, ShippingAddressBo shippingAddress) {
        // 计算并设置运费
        calculateAndSetFreight(shippingAddress, dataHolder);
    }

    //**************************************************


    private void checkCollocation(RemoteCollocationBo collocationBo, Date currentTime) {
        if (collocationBo == null) {
            throw new BusinessException("组合购活动不存在");
        }
        // 统一用入口时间校验
        if (DateUtil.compare(collocationBo.getStartTime(), currentTime) > 0) {
            throw new BusinessException("组合购活动未开始");
        }
        if (DateUtil.compare(collocationBo.getEndTime(), currentTime) < 0) {
            throw new BusinessException("组合购活动已结束");
        }
    }

    private List<ShopProductBo> buildProductList(Long userId, List<PurchaseProductBo> skuList,
                                                 RemoteCollocationBo collocationBo) {
        List<String> skuIdList = skuList.stream().map(PurchaseProductBo::getSkuId).collect(Collectors.toList());
        // 获取SKU信息
        Map<String, RemoteProductSkuBo> buySkuMap = getBuyProductSkuMap(skuIdList);
        Map<String, RemoteCollocationSkuBo> collocationSkuMap = getCollocationSkuMap(collocationBo);

        return skuList.stream()
                .map(buySku -> {
                    RemoteProductSkuBo skuBo = buySkuMap.get(buySku.getSkuId());
                    RemoteCollocationSkuBo collocationSkuBo = collocationSkuMap.get(buySku.getSkuId());
                    if (skuBo == null || collocationSkuBo == null) {
                        throw new BusinessException("组合购商品不存在");
                    }
                    ShopProductBo productBo = new ShopProductBo();

                    productBo.setUserId(userId);
                    productBo.setProductId(skuBo.getProductId());
                    productBo.setProductName(skuBo.getProductName());
                    productBo.setSkuId(skuBo.getSkuId());
                    productBo.setQuantity(buySku.getQuantity());
                    productBo.setMeasureUnit(skuBo.getMeasureUnit());
                    // 库存，SKU的库存，如果商品没有SKU，也会有一条SKU记录
                    productBo.setSkuStock(skuBo.getStock());
                    productBo.setWhetherSelected(true);
                    productBo.setMainImagePath(getSkuImagePath(skuBo));
                    BigDecimal salePrice = getSkuSalePrice(skuBo);
                    productBo.setOriginSalePrice(salePrice);
                    productBo.setRealSalePrice(collocationSkuBo.getPrice());
                    productBo.setFinalSalePrice(collocationSkuBo.getPrice());
                    // 组合购与阶梯价互斥
                    productBo.setLadderPriceList(null);

                    productBo.setMaxBuyCount(skuBo.getMaxBuyCount());
                    // 倍数起购量
                    int multipleCount = skuBo.getMultipleCount() == null ? CommonConst.DEFAULT_BUY_COUNT : skuBo.getMultipleCount();
                    productBo.setMultipleCount(multipleCount);
                    // 计算并设置起购量，起购量需要综合阶梯价和倍数起购量进行计算
                    int minBuyCount = shoppingCartAssist.calculateBaseBuyCount(multipleCount, skuBo.getLadderPriceList());
                    productBo.setMinBuyCount(minBuyCount);

                    productBo.setSaleStatus(skuBo.getSaleStatus());
                    productBo.setAuditStatus(skuBo.getAuditStatus());
                    productBo.setWhetherDeleted(skuBo.getWhetherDelete());

                    productBo.setFreightTemplateId(skuBo.getFreightTemplateId());
                    productBo.setWeight(skuBo.getWeight());
                    productBo.setVolume(skuBo.getVolume());
                    productBo.setProductCode(skuBo.getProductCode());
                    productBo.setColor(skuBo.getSpec1Value());
                    productBo.setSize(skuBo.getSpec2Value());
                    productBo.setVersion(skuBo.getSpec3Value());
                    productBo.setSkuAutoId(skuBo.getSkuAutoId());
                    productBo.setCategoryId(skuBo.getCategoryId());
                    productBo.setSku(skuBo.getSkuCode());
                    // 设置OE码,品牌号,替换号，零件规格等字段
                    productBo.setOeCode(skuBo.getOeCode());
                    productBo.setBrandCode(skuBo.getBrandCode());
                    productBo.setPartSpec(skuBo.getPartSpec());
                    productBo.setAdaptableCar(skuBo.getAdaptableCar());
                    productBo.setBrandName(skuBo.getBrandName());
                    productBo.setBrandId(skuBo.getBrandId());
                    BigDecimal productAmount = NumberUtil.multiply(productBo.getRealSalePrice(), productBo.getQuantity());
                    productBo.setTotalAmount(productAmount);
                    List<String> skuNameList = shoppingCartAssist.buildSkuDesc(skuBo);
                    productBo.setSkuNameList(skuNameList);
                    return productBo;
                }).collect(Collectors.toList());
    }

    private Map<String, RemoteProductSkuBo> getBuyProductSkuMap(List<String> skuIdList) {
        List<RemoteProductSkuBo> skuList = productRemoteService.queryProductSku(skuIdList);
        if (CollUtil.isEmpty(skuList)) {
            throw new BusinessException("商品不存在");
        }
        return skuList.stream()
                .collect(Collectors.toMap(RemoteProductSkuBo::getSkuId, Function.identity(), (o1, o2) -> o2));
    }

    private Map<String, RemoteCollocationSkuBo> getCollocationSkuMap(RemoteCollocationBo collocationBo) {
        List<RemoteCollocationProductBo> productRespList = collocationBo.getProductRespList();
        if (CollUtil.isEmpty(productRespList)) {
            throw new BusinessException("组合购商品不存在");
        }
        return productRespList.stream()
                .flatMap(prod -> prod.getSkuRespList().stream())
                .collect(Collectors.toMap(RemoteCollocationSkuBo::getSkuId, Function.identity(), (o1,
                                                                                                  o2) -> o2));
    }

}
