package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.product.core.remote.ProductRemoteService;
import com.sankuai.shangou.seashop.trade.common.constant.CommonConst;
import com.sankuai.shangou.seashop.trade.common.remote.model.product.RemoteProductSkuBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BaseBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.ShoppingCartAssist;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ChangePreviewBuyNowSkuQuantityContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewChangeSkuQuantityParamBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * 订单预览页-修改立即购买数量
 * 1. 数据来源为页面提交的数据，使用前端传入数据的原因是保持用户前后看到的价格一致，实际到提交订单时会再次校验的，只是此时用户已经停留在页面了，那只是修改数量的话
 * 价格以用户已经看到的为准，所以其实理论上前端传入的数据只需要skuId、数量、价格这些即可，但是为了简便，直接将预览接口返回的数据全部传入了
 * 2. 限时购商品的数量修改，因为与其他营销互斥，所以直接根据页面传入的价格计算，然后校验数量规则
 * <AUTHOR>
 */
@Service
@Slf4j
public class ChangePreviewBuyNowSkuQuantityBuilder extends ChangePreviewSkuQuantityCommonBuilder {

    @Resource
    private ProductRemoteService productRemoteService;
    @Resource
    private ShoppingCartAssist shoppingCartAssist;

    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, BaseBuildDataHolder dataHolder) {
        ChangePreviewBuyNowSkuQuantityContext buildContext = (ChangePreviewBuyNowSkuQuantityContext) context;
        // 理论上对象一定会存在，没做判空处理
        PreviewChangeSkuQuantityParamBo changeQuantityParam = buildContext.getChangeQuantityParam();
        ShoppingCartShopBo shop = changeQuantityParam.getShop();
        List<ShopProductBo> productList = changeQuantityParam.getProductList();
        OrderAdditionalBo additional = changeQuantityParam.getAdditional();

        List<Long> productIdList = productList.stream().map(ShopProductBo::getProductId).collect(Collectors.toList());

        // 校验并重置相关数据
        validAndReset(context.getUserId(), shop, productList, changeQuantityParam, productIdList);

        // 选择了优惠券时，不处理满减了
        boolean hasCoupon = super.hasCoupon(additional);
        if (hasCoupon) {
            buildContext.resetNeedReduction(false);
        }

        // 重置营销
        shop.setPromotionDescList(new ArrayList<>(5));
        shop.setReductionDescList(new ArrayList<>(5));
        additional.setReductionAmount(BigDecimal.ZERO);
        additional.setDiscountAmount(BigDecimal.ZERO);
        additional.setReductionActivityId(null);
        additional.setReductionConditionAmount(null);

        ShopProductListBo shopProductListBo = new ShopProductListBo();
        shopProductListBo.setShop(shop);
        shopProductListBo.setProductList(productList);
        shopProductListBo.setAdditional(additional);
        shopProductListBo.setProductIdList(productIdList);
        return Collections.singletonList(shopProductListBo);
    }

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_CHANGE_BUY_NOW_SKU_QUANTITY;
    }




    //*******************************************************

    private void validAndReset(Long userId, ShoppingCartShopBo shop, List<ShopProductBo> productList,
                               PreviewChangeSkuQuantityParamBo changeQuantityParam, List<Long> productIdList) {
        // 立即购买只有一个商品
        ShopProductBo shopProductBo = productList.get(0);
        // 需要修改的目标数量
        Long quantity = changeQuantityParam.getQuantity();
        String changeSkuId = changeQuantityParam.getSkuId();

        List<RemoteProductSkuBo> skuList = productRemoteService.queryProductSku(Collections.singletonList(changeSkuId));
        if (CollUtil.isEmpty(skuList)) {
            throw new BusinessException("商品不存在或已下架");
        }
        RemoteProductSkuBo sku = skuList.get(0);

        // 倍数起购量
        int multipleCount = sku.getMultipleCount() == null ? CommonConst.DEFAULT_BUY_COUNT : sku.getMultipleCount();
        // 计算并设置起购量，起购量需要综合阶梯价和倍数起购量进行计算
        int minBuyCount = shoppingCartAssist.calculateBaseBuyCount(multipleCount, sku.getLadderPriceList());
        shopProductBo.setFreightTemplateId(sku.getFreightTemplateId());
        shopProductBo.setWhetherDeleted(sku.getWhetherDelete());
        shopProductBo.setSaleStatus(sku.getSaleStatus());
        shopProductBo.setAuditStatus(sku.getAuditStatus());
        shopProductBo.setWeight(sku.getWeight());
        shopProductBo.setVolume(sku.getVolume());
        shopProductBo.setMeasureUnit(sku.getMeasureUnit());

        // 重置价格，后面重新计算
        shopProductBo.setRealSalePrice(shopProductBo.getRealSalePrice());
        shopProductBo.setFinalSalePrice(shopProductBo.getRealSalePrice());
        shopProductBo.setDiscountSalePrice(null);
        shopProductBo.setWhetherSelected(true);
        // 使用页面传入的价格计算
        BigDecimal totalAmount = NumberUtil.mul(shopProductBo.getFinalSalePrice(), quantity);
        // 返回的数据，重置商品总金额
        shopProductBo.setTotalAmount(totalAmount);
        shopProductBo.setQuantity(quantity);
        shopProductBo.setSkuStock(sku.getStock());
        shopProductBo.setMaxBuyCount(sku.getMaxBuyCount());
        shopProductBo.setMinBuyCount(minBuyCount);
        shopProductBo.setMultipleCount(multipleCount);
        shopProductBo.setLadderPriceList(sku.getLadderPriceList());

        // 校验数量是否合法
        Map<Long, Long> productBuyCountMap = super.getProductBuyCount(userId, productIdList);
        checkBuyQuantityForChange(quantity, shopProductBo, productBuyCountMap);

        // 重置店铺金额数据，限时购只有一个商品
        shop.setProductTotalAmount(totalAmount);
        shop.setSelectedTotalAmount(totalAmount);
        // 重置营销
        shop.setPromotionDescList(new ArrayList<>(5));
        shop.setReductionDescList(new ArrayList<>(5));
        OrderAdditionalBo additional = changeQuantityParam.getAdditional();
        additional.setReductionAmount(BigDecimal.ZERO);
        additional.setDiscountAmount(BigDecimal.ZERO);


        
    }


}
