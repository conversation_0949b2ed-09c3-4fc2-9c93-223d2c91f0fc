package com.sankuai.shangou.seashop.trade.core.mq.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/01/05 17:20
 */
@NoArgsConstructor
@Getter
@Setter
public class ProductCommentMessage {

    /**
     * 主键
     */
    @JsonProperty("id")
    private Long id;
    /**
     * 商品唯一表示(美团Id组件获取)
     */
    @JsonProperty("product_id")
    private Long productId;

}
