package com.sankuai.shangou.seashop.trade.core.service.model.product;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class SearchProductBo extends BasePageReq {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     *  搜索关键字，目前是针对商品名称
     */
    private String searchKey;
    /**
     * 类目ID
     */
    private Long categoryId;
    /**
     * 类目全路径
     */
    private String categoryPath;
    /**
     * 属性ID
     */
    private Long attributeId;
    /**
     * 属性值
     */
    private String attributeValue;
    /**
     * 品牌ID
     */
    private Long brandId;
    /**
     * 商品id的集合
     */
    private List<Long> productIds;
    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 销售状态
     */
    private Integer saleStatus;

    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 商品条码,sku维度
     */
    private String barcode;

    /**
     * 优惠券ID
     */
    private Long couponId;

}
