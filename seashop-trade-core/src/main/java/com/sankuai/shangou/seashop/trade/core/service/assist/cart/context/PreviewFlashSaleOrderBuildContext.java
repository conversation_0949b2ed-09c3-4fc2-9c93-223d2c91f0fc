package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PreviewFlashSaleOrderBuildContext implements BuildContext {

    /**
     * 商家用户ID
     */
    private Long userId;
    /**
     * 限时购活动ID
     */
    private Long flashSaleId;
    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 购买数量
     */
    private Long quantity;

    /**
     * 用户默认的收货地址
     */
    private ShippingAddressBo defaultShippingAddress;

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_FLASH_SALE;
    }

    @Override
    public boolean needDiscount() {
        return false;
    }

    @Override
    public boolean needReduction() {
        return true;
    }

    @Override
    public boolean needPromotion() {
        return true;
    }

    @Override
    public boolean needExclusivePrice() {
        return false;
    }

    @Override
    public boolean needLadderPrice() {
        return false;
    }
}
