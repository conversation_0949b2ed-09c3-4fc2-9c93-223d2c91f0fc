package com.sankuai.shangou.seashop.trade.core.service.model.product;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 商品属性对象
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class ProductAttributeBo {

    /**
     * 属性ID
     */
    private Long attributeId;
    /**
     * 属性名称
     */
    private String attributeName;
    /**
     * 属性值，属性值存储是是单独的数据表，有 valueId和valueName，
     * 但在构建索引的时候，为了减少层次，也为了兼顾精确匹配，存储为字符串数组
     */
    private List<String> attributeValues;

}
