package com.sankuai.shangou.seashop.trade.core.service.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
public class ChangeShoppingCartSkuCntBo {

    /**
     * 商家会员ID，用于校验是否操作的是自己的数据
     */
    private Long userId;
    /**
     * 购物车主键ID
     */
    private Long id;
    /**
     * 变更后的数量(不管是增减还是输入框改数字，前端先计算后把数量传到后端)
     */
    private Long quantity;

    /**
     * 店铺信息
     */
    private ShoppingCartShopBo shop;
    /**
     * 商品列表，需要包括发生变更的那条数据
     */
    private List<ShopProductBo> productList;

}
