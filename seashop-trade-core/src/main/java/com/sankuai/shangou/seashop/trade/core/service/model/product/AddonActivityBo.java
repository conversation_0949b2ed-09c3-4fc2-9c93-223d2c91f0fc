package com.sankuai.shangou.seashop.trade.core.service.model.product;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 凑单页面对应的活动对象
 * <AUTHOR>
 */
@Getter
@Setter
public class AddonActivityBo {

    /**
     * 活动类型。1：折扣；2：满减
     */
    private Integer type;
    /**
     * 活动类型描述
     */
    private String typeDesc;
    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动结束时间
     */
    private Date endTime;

}
