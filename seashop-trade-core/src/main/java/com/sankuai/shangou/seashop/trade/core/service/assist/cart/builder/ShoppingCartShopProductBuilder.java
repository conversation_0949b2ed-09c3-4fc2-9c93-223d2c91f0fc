package com.sankuai.shangou.seashop.trade.core.service.assist.cart.builder;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.trade.core.service.assist.cart.AbstractCartShopProductBuilder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.CartBuildDataHolder;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.context.ShoppingCartBuildContext;
import com.sankuai.shangou.seashop.trade.core.service.model.OrderAdditionalBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.dao.core.domain.ShoppingCart;
import com.sankuai.shangou.seashop.trade.dao.core.repository.ShoppingCartRepository;
import com.sankuai.shangou.seashop.trade.thrift.core.enums.ShoppingCartEnum;

/**
 * 购物车构建
 * <AUTHOR>
 */
@Service
public class ShoppingCartShopProductBuilder extends AbstractCartShopProductBuilder {

    @Resource
    private ShoppingCartRepository shoppingCartRepository;

    @Override
    protected List<ShoppingCart> getShoppingCartList(BuildContext context) {
        ShoppingCartBuildContext cartContext = (ShoppingCartBuildContext) context;
        return shoppingCartRepository.queryByUserId(cartContext.getUserId());
    }


    @Override
    protected CartBuildDataHolder tempHoldData(List<ShopProductBo> userShopSkuList) {
        // 将商家所有的购物车sku，根据状态分组为 是否销售中
        Map<Boolean, List<ShopProductBo>> whetherValidMap = userShopSkuList.stream()
                .collect(Collectors.groupingBy(sku -> ShoppingCartEnum.SkuStatus.ON_SALE.equals(sku.getSkuStatus())));
        return CartBuildDataHolder.builder()
                .validProductList(whetherValidMap.get(Boolean.TRUE))
                .invalidProductList(whetherValidMap.get(Boolean.FALSE))
                .build();
    }

    @Override
    protected List<ShopProductListBo> buildShopProduct(BuildContext context, CartBuildDataHolder dataHolder) {
        Map<ShoppingCartShopBo, List<ShopProductBo>> shopGroupedMap = dataHolder.getShopGroupedMap();
        return shopGroupedMap.entrySet().stream()
                .map(entry -> {
                    // 预先定义每个店铺(订单)的附加信息对象
                    OrderAdditionalBo additional = new OrderAdditionalBo();
                    // 计算并设置商品sku的实际售价和店铺总金额
                    ShopProductListBo shopProductListBo = new ShopProductListBo();
                    shopProductListBo.setShop(entry.getKey());
                    shopProductListBo.setProductList(entry.getValue());
                    shopProductListBo.setAdditional(additional);
                    return shopProductListBo;
                }).collect(Collectors.toList());
    }

    @Override
    protected void processExpand(BuildContext context, CartBuildDataHolder dataHolder) {
        // 购物车不需要扩展
    }



    @Override
    public BuildType getBuildType() {
        return BuildType.SHOPPING_CART;
    }
}
