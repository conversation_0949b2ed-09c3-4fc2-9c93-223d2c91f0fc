package com.sankuai.shangou.seashop.trade.core.service.model.product;

import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
 * 评价汇总数据
 *
 * <AUTHOR>
 * @date 2023/12/25 11:01
 */
@Getter
@Setter
public class EsCommentSummaryBo {

    /**
     * 评价总数
     */
    private Integer totalCount;

    /**
     * 五分评价数
     */
    private Integer fiveStarCount;

    /**
     * 四分评价数
     */
    private Integer fourStarCount;

    /**
     * 三分评价数
     */
    private Integer threeStarCount;

    /**
     * 二分评价数
     */
    private Integer twoStarCount;

    /**
     * 一分评价数
     */
    private Integer oneStarCount;

    /**
     * 有图评价数
     */
    private Integer hasImageCount;

    /**
     * 追加评论数
     */
    private Integer appendCount;

    /**
     * 好评率
     */
    private BigDecimal goodRate;

    /**
     * 中评率
     */
    private BigDecimal middleRate;

    /**
     * 差评率
     */
    private BigDecimal badRate;

    /**
     * 综合评分
     */
    private BigDecimal score;

    public static EsCommentSummaryBo ofEmpty() {
        EsCommentSummaryBo summary = new EsCommentSummaryBo();
        summary.setTotalCount(0);
        summary.setFiveStarCount(0);
        summary.setFourStarCount(0);
        summary.setThreeStarCount(0);
        summary.setTwoStarCount(0);
        summary.setOneStarCount(0);
        summary.setHasImageCount(0);
        summary.setAppendCount(0);
        summary.setGoodRate(BigDecimal.ZERO);
        summary.setMiddleRate(BigDecimal.ZERO);
        summary.setBadRate(BigDecimal.ZERO);
        summary.setScore(BigDecimal.ZERO);
        return summary;
    }

}
