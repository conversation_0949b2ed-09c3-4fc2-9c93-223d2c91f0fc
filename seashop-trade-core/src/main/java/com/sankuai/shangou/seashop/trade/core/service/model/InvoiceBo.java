package com.sankuai.shangou.seashop.trade.core.service.model;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Getter;
import lombok.Setter;

/**
 * 发票信息
 * <AUTHOR>
 */
@Getter
@Setter
public class InvoiceBo extends BaseThriftDto {

    /**
     * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
     */
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    private String invoiceTitle;

    /**
     * 税号
     */
    private String invoiceCode;

    /**
     * 发票内容(发票明细、商品类别)
     */
    private String invoiceContext;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 注册电话
     */
    private String registerPhone;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行帐号
     */
    private String bankNo;

    /**
     * 收票人姓名
     */
    private String realName;

    /**
     * 收票人手机号
     */
    private String cellPhone;

    /**
     * 收票人邮箱
     */
    private String email;

    /**
     * 收票人地址区域id
     */
    private Integer regionId;

    /**
     * 收票人详细地址
     */
    private String address;

    /**
     * 订单完成后多少天开具增值税发票
     */
    private Integer vatInvoiceDay;

}
