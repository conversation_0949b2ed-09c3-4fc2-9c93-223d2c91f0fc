package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.util.List;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildType;
import com.sankuai.shangou.seashop.trade.core.service.model.PreviewOrderSelectSkuBo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class PreviewOrderBuildContext implements BuildContext {

    /**
     * 商家用户ID
     */
    private Long userId;
    /**
     * 用户勾选的商品列表
     */
    private List<PreviewOrderSelectSkuBo> selectedSkuList;
    /**
     * 用户默认的收货地址
     */
    private ShippingAddressBo defaultShippingAddress;

    @Override
    public BuildType getBuildType() {
        return BuildType.PREVIEW_ORDER;
    }

    @Override
    public boolean needDiscount() {
        return true;
    }

    @Override
    public boolean needReduction() {
        return true;
    }
}
