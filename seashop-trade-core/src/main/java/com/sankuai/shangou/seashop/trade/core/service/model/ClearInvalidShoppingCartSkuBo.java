package com.sankuai.shangou.seashop.trade.core.service.model;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 清除购物车失效sku请求入参
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
public class ClearInvalidShoppingCartSkuBo {

    /**
     * 购物车唯一标识，目前是数据表主键ID，取购物车列表的ID字段
     */
    private List<Long> idList;
    /**
     * 商家会员ID，用于校验是否删除的是自己的数据
     */
    private Long userId;
}
