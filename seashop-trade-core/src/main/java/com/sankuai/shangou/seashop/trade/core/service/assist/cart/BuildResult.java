package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

import java.math.BigDecimal;
import java.util.List;

import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.PreviewOrderSummaryBo;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class BuildResult {

    private boolean success;
    private String errMsg;
    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;
    /**
     * 失效的商品列表
     *
     */
    private List<ShopProductBo> invalidProductList;
    /**
     * 所有店铺总金额
     */
    private BigDecimal totalAmount;
    /**
     * 预览订单页汇总信息
     */
    private PreviewOrderSummaryBo summary;

}
