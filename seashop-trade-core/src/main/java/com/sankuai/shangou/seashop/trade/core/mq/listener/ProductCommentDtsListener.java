package com.sankuai.shangou.seashop.trade.core.mq.listener;

import javax.annotation.Resource;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.trade.core.mq.model.DbTableDataChangeMessage;
import com.sankuai.shangou.seashop.trade.core.mq.model.ProductCommentMessage;
import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/05 17:20
 */
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_PRODUCT_COMMENT,
//        group = MafkaConst.GROUP_PRODUCT_COMMENT)
/*@Profile("!develop")
@Slf4j
@Component
@RocketMQMessageListener(topic = MafkaConst.TOPIC_PRODUCT_COMMENT + "_${spring.profiles.active}"
, consumerGroup = MafkaConst.GROUP_PRODUCT_COMMENT + "_${spring.profiles.active}")
public class ProductCommentDtsListener implements RocketMQListener<String> {

    @Resource
    private TradeProductService tradeProductService;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
//        String body = (String) mafkaMessage.getBody();
//        log.info("【mafka消费】【商品评价变更】消息内容为: {}", body);
//        DbTableDataChangeMessage<ProductCommentMessage> messageWrapper = JsonUtil.parseObject(body,
//                new TypeReference<DbTableDataChangeMessage<ProductCommentMessage>>() {});
//        Long productId = messageWrapper.getData().getProductId();
//        tradeProductService.updateProductCommentSummary(productId);
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }


    @Override
    public void onMessage(String body) {
                log.info("【mafka消费】【商品评价变更】消息内容为: {}", body);
        DbTableDataChangeMessage<ProductCommentMessage> messageWrapper = JsonUtil.parseObject(body,
            new TypeReference<DbTableDataChangeMessage<ProductCommentMessage>>() {
            });
        Long productId = messageWrapper.getData().getProductId();
        tradeProductService.updateProductCommentSummary(productId);
    }
}*/
