package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

/**
 * <AUTHOR>
 */
public enum BuildType {

    // 购物车列表
    SHOPPING_CART,
    // 调整购物车商品数量
    CHANGE_SHOPPING_CART_SKU_CNT,
    // 勾选购物车商品
    SELECT_SHOPPING_CART_SKU,
    // 勾选购物车店铺
    SELECT_SHOP,
    // 全选购物车
    SELECT_ALL,


    // 凑单
    ADDON,



    // 购物车订单预览
    PREVIEW_ORDER,
    // 预览ERP订单
    PREVIEW_ERP_ORDER,
    // 限时购预览
    PREVIEW_FLASH_SALE,
    // 组合购预览
    PREVIEW_COLLOCATION,
    // 立即购买
    PREVIEW_BUY_NOW,
    // 预览订单页-修改购物车数量
    PREVIEW_CHANGE_CART_SKU_QUANTITY,
    // 预览订单页-修改立即购买数量
    PREVIEW_CHANGE_BUY_NOW_SKU_QUANTITY,
    // 预览订单页-修改限时购数量
    PREVIEW_CHANGE_FLASH_SALE_SKU_QUANTITY,
    // 预览订单页-修改组合购数量
    PREVIEW_CHANGE_COLLOCATION_SKU_QUANTITY,

    // 选择优惠券
    CHOOSE_COUPON,
    // 选择收货地址
    CHOOSE_SHIPPING_ADDRESS,

    // 提交订单
    SUBMIT_ORDER,
    // 立即购买提交订单
    SUBMIT_BUY_NOW,
    // 提交限时购订单
    SUBMIT_FLASH_SALE_ORDER,
    // 提交组合购订单
    SUBMIT_COLLOCATION_ORDER,

    SUBMIT_ERP_ORDER,
    ;

}
