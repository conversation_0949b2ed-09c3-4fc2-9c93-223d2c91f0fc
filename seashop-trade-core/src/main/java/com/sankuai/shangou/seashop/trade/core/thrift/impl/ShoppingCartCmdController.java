package com.sankuai.shangou.seashop.trade.core.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.core.service.ShoppingCartService;
import com.sankuai.shangou.seashop.trade.core.service.model.*;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.AddFromAddonBo;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.AddFromAddonResultBo;
import com.sankuai.shangou.seashop.trade.core.service.model.cart.SelectAllBo;
import com.sankuai.shangou.seashop.trade.thrift.core.ShoppingCartCmdFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.*;
import com.sankuai.shangou.seashop.trade.thrift.core.response.AddFromAddonResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.ShopProductResp;
import com.sankuai.shangou.seashop.trade.thrift.core.response.UserShoppingCartResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 购物车变更相关的thrift服务实现
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/shoppingCart")
public class ShoppingCartCmdController implements ShoppingCartCmdFeign {

    @Resource
    private ShoppingCartService shoppingCartService;

    @PostMapping(value = "/addShoppingCart", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> addShoppingCart(@RequestBody AddShoppingCartReq addReq) throws TException {
        log.info("【购物车】添加购物车, 请求参数={}", JsonUtil.toJsonString(addReq));
        return ThriftResponseHelper.responseInvoke("addShoppingCart", addReq, func -> {
            // 参数基础校验
            addReq.checkParameter();
            // 参数对象转换
            AddShoppingCartBo addBo = JsonUtil.copy(addReq, AddShoppingCartBo.class);
            // 业务逻辑处理
            shoppingCartService.addShoppingCart(addBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/addShoppingCartBatch", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> addShoppingCartBatch(AddShoppingCartBatchReq addReq) throws TException {
        return ThriftResponseHelper.responseInvoke("addShoppingCartBatch", addReq, func -> {
            // 参数基础校验
            addReq.checkParameter();

            // 业务逻辑处理
            shoppingCartService.addShoppingCartBatch(JsonUtil.copyList(addReq.getShoppingCartList(), AddShoppingCartBo.class));
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/deleteShoppingCart", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> deleteShoppingCart(@RequestBody DeleteShoppingCartSkuReq deleteReq) throws TException {
        log.info("【购物车】删除购物车, 请求参数={}", JsonUtil.toJsonString(deleteReq));
        return ThriftResponseHelper.responseInvoke("deleteShoppingCart", deleteReq, func -> {
            // 参数基础校验
            deleteReq.checkParameter();
            // 参数对象转换
            DeleteShoppingCartSkuBo deleteBo = JsonUtil.copy(deleteReq, DeleteShoppingCartSkuBo.class);
            // 业务逻辑处理
            shoppingCartService.deleteShoppingCart(deleteBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/clearInvalid", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> clearInvalid(@RequestBody ClearInvalidShoppingCartSkuReq clearReq) throws TException {
        log.info("【购物车】批量删除购物车, 请求参数={}", JsonUtil.toJsonString(clearReq));
        return ThriftResponseHelper.responseInvoke("clearInvalid", clearReq, func -> {
            // 参数基础校验
            clearReq.checkParameter();
            // 参数对象转换
            ClearInvalidShoppingCartSkuBo clearBo = JsonUtil.copy(clearReq, ClearInvalidShoppingCartSkuBo.class);
            // 业务逻辑处理
            shoppingCartService.clearInvalid(clearBo);
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/changeShoppingCartSkuCnt", consumes = "application/json")
    @Override
    public ResultDto<ShopProductResp> changeShoppingCartSkuCnt(@RequestBody ChangeShoppingCartQuantityReq changeReq) throws TException {
        log.info("【购物车】变更购物车sku数量, 请求参数={}", JsonUtil.toJsonString(changeReq));
        return ThriftResponseHelper.responseInvoke("changeShoppingCartSkuCnt", changeReq, func -> {
            // 参数基础校验
            changeReq.checkParameter();
            // 参数对象转换
            ChangeShoppingCartSkuCntBo changeBo = JsonUtil.copy(changeReq, ChangeShoppingCartSkuCntBo.class);
            // 业务逻辑处理
            ShopProductListBo newShopProduct = shoppingCartService.changeShoppingCartSkuCnt(changeBo);
            return JsonUtil.copy(newShopProduct, ShopProductResp.class);
        });
    }

    @PostMapping(value = "/selectShopSku", consumes = "application/json")
    @Override
    public ResultDto<ShopProductResp> selectShopSku(@RequestBody SelectShoppingCartSkuReq selectReq) throws TException {
        log.info("【购物车】选中sku, 请求参数={}", JsonUtil.toJsonString(selectReq));
        return ThriftResponseHelper.responseInvoke("selectShopSku", selectReq, func -> {
            // 参数基础校验
            selectReq.checkParameter();
            // 参数对象转换
            SelectShoppingCartSkuBo selectBo = JsonUtil.copy(selectReq, SelectShoppingCartSkuBo.class);
            // 业务逻辑处理
            ShopProductListBo newShopProduct = shoppingCartService.selectShopSku(selectBo);
            return JsonUtil.copy(newShopProduct, ShopProductResp.class);
        });
    }

    @PostMapping(value = "/addFromAddon", consumes = "application/json")
    @Override
    public ResultDto<AddFromAddonResp> addFromAddon(@RequestBody AddFromAddonReq addReq) throws TException {
        log.info("【购物车】凑单加购, 请求参数={}", JsonUtil.toJsonString(addReq));
        return ThriftResponseHelper.responseInvoke("addFromAddon", addReq, func -> {
            // 参数基础校验
            addReq.checkParameter();
            // 参数对象转换
            AddFromAddonBo addBo = JsonUtil.copy(addReq, AddFromAddonBo.class);
            // 业务逻辑处理
            AddFromAddonResultBo newShopProduct = shoppingCartService.addFromAddon(addBo);
            return JsonUtil.copy(newShopProduct, AddFromAddonResp.class);
        });
    }

    @PostMapping(value = "/selectShop", consumes = "application/json")
    @Override
    public ResultDto<ShopProductResp> selectShop(@RequestBody SelectShopReq selectReq) throws TException {
        log.info("【购物车】选中店铺, 请求参数={}", JsonUtil.toJsonString(selectReq));
        return ThriftResponseHelper.responseInvoke("selectShop", selectReq, func -> {
            // 参数基础校验
            selectReq.checkParameter();
            // 参数对象转换
            SelectShopBo selectBo = JsonUtil.copy(selectReq, SelectShopBo.class);
            // 业务逻辑处理
            ShopProductListBo newShopProduct = shoppingCartService.selectShop(selectBo);
            return JsonUtil.copy(newShopProduct, ShopProductResp.class);
        });
    }

    @PostMapping(value = "/selectAll", consumes = "application/json")
    @Override
    public ResultDto<UserShoppingCartResp> selectAll(@RequestBody SelectAllReq selectReq) throws TException {
        log.info("【购物车】选中整个购物车, 请求参数={}", JsonUtil.toJsonString(selectReq));
        return ThriftResponseHelper.responseInvoke("selectAll", selectReq, func -> {
            // 参数基础校验
            selectReq.checkParameter();
            // 参数对象转换
            SelectAllBo selectBo = JsonUtil.copy(selectReq, SelectAllBo.class);
            // 业务逻辑处理
            UserShoppingCartBo newShopProduct = shoppingCartService.selectAll(selectBo);
            return JsonUtil.copy(newShopProduct, UserShoppingCartResp.class);
        });
    }
}
