package com.sankuai.shangou.seashop.trade.core.mq.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

/**
 * 商品表变更消息体
 * <AUTHOR>
 */
@NoArgsConstructor
@Getter
@Setter
public class ProductMessage {

    /**
     * 主键
     */
    @JsonProperty("id")
    private Long id;
    /**
     * 商品唯一表示(美团Id组件获取)
     */
    @JsonProperty("product_id")
    private Long productId;
    /**
     * 店铺ID
     */
    @JsonProperty("shop_id")
    private Long shopId;

}
