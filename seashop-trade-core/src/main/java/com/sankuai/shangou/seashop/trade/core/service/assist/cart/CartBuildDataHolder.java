package com.sankuai.shangou.seashop.trade.core.service.assist.cart;

import java.util.List;

import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

/**
 * 基于购物车构建的数据存储对象
 * <p>这里的实时数据包括：购物车、商品和SKU和库存、营销活动，可能不一定会用全部，但可能会用部分用于计算，没有继续区分很细</p>
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class CartBuildDataHolder extends BaseBuildDataHolder {


    /**
     * 销售中的商品列表
     */
    private List<ShopProductBo> validProductList;
    /**
     * 失效(非销售中)的商品列表
     *
     */
    private List<ShopProductBo> invalidProductList;


}
