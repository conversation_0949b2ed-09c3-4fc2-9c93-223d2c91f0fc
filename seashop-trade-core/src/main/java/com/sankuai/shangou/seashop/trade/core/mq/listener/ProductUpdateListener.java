package com.sankuai.shangou.seashop.trade.core.mq.listener;

import javax.annotation.Resource;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.trade.common.constant.MafkaConst;
import com.sankuai.shangou.seashop.trade.core.mq.model.DbTableDataChangeMessage;
import com.sankuai.shangou.seashop.trade.core.mq.model.ProductMessage;
import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;

import lombok.extern.slf4j.Slf4j;

/**
 * 商品信息更新监听器
 * <AUTHOR>
 */
/*
@Profile("!develop")
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = MafkaConst.TOPIC_PRODUCT_UPDATE,
//        group = MafkaConst.GROUP_ES_PRODUCT_BUILD)
@RocketMQMessageListener(topic = MafkaConst.TOPIC_PRODUCT_UPDATE + "_${spring.profiles.active}"
, consumerGroup = MafkaConst.GROUP_ES_PRODUCT_BUILD + "_${spring.profiles.active}")
public class ProductUpdateListener implements RocketMQListener<String> {

    @Resource
    private TradeProductService tradeProductService;

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
//        String body = (String) mafkaMessage.getBody();
//        log.info("【mafka消费】【商品表变更】消息内容为: {}", body);
//        DbTableDataChangeMessage<ProductMessage> messageWrapper = JsonUtil.parseObject(body,
//                new TypeReference<DbTableDataChangeMessage<ProductMessage>>() {});
//        Long productId = messageWrapper.getData().getProductId();
//        tradeProductService.build(productId);
//        return ConsumeStatus.CONSUME_SUCCESS;
//    }

    @Override
    public void onMessage(String body) {
        log.info("【mafka消费】【商品表变更】消息内容为: {}", body);
        DbTableDataChangeMessage<ProductMessage> messageWrapper = JsonUtil.parseObject(body,
            new TypeReference<DbTableDataChangeMessage<ProductMessage>>() {});
        Long productId = messageWrapper.getData().getProductId();
        tradeProductService.build(productId);
    }
}
*/
