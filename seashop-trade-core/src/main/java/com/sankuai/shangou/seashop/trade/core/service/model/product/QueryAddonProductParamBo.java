package com.sankuai.shangou.seashop.trade.core.service.model.product;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.ShoppingCartShopBo;
import com.sankuai.shangou.seashop.trade.core.service.model.UserBo;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
public class QueryAddonProductParamBo extends BasePageReq {

    /**
     * 用户信息
     */
    private UserBo user;
    /**
     * 凑单类型。1：折扣；2：满减
     */
    private Integer type;
    /**
     * 活动ID
     */
    private Long activityId;
    /**
     * 店铺信息
     */
    private ShoppingCartShopBo shop;
    /**
     * 商品列表
     */
    private List<ShopProductBo> productList;

    // 以下属性业务设置，不是外部传入
    private Boolean discountForAll;
    private List<Long> discountProductIdList;

    /**
     * 销售状态
     */
    private Integer saleStatus;

    /**
     * 审核状态
     */
    private Integer auditStatus;

}
