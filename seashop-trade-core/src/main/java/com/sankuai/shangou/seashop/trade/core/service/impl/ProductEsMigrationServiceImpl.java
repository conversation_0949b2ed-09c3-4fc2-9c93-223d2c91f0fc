package com.sankuai.shangou.seashop.trade.core.service.impl;

import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.trade.core.service.ProductEsMigrationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 商品ES索引迁移服务
 * 用于零停机迁移商品索引，支持替换号模糊搜索优化
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class ProductEsMigrationServiceImpl implements ProductEsMigrationService {

    @Resource
    private ProductEsBuildService productEsBuildService;
    
    @Resource
    private ProductRepository productRepository;
    
    @Resource(name = "commonThreadPoolExecutor")
    private ThreadPoolExecutor threadPoolExecutor;

    /**
     * 批量重建商品ES索引
     * 支持分页处理，避免内存溢出
     * 
     * @param batchSize 批处理大小，建议100-500
     * @param maxConcurrency 最大并发数，建议5-10
     */
    @Override
    public void rebuildAllProductIndex(int batchSize, int maxConcurrency) {
        log.info("开始批量重建商品ES索引，批处理大小：{}，最大并发数：{}", batchSize, maxConcurrency);
        
        try {
            // 获取所有商品ID总数
            Long totalCount = productRepository.countAllProducts();
            log.info("需要重建的商品总数：{}", totalCount);
            
            // 计算批次数
            int totalBatches = (int) Math.ceil((double) totalCount / batchSize);
            log.info("总批次数：{}", totalBatches);
            
            // 分批处理
            for (int batch = 0; batch < totalBatches; batch++) {
                int offset = batch * batchSize;
                log.info("处理第 {}/{} 批，偏移量：{}", batch + 1, totalBatches, offset);
                
                // 获取当前批次的商品ID列表
                List<Long> productIds = productRepository.getProductIdsByPage(offset, batchSize);
                
                if (productIds.isEmpty()) {
                    log.info("第 {} 批没有数据，跳过", batch + 1);
                    continue;
                }
                
                // 并发处理当前批次
                processBatchConcurrently(productIds, maxConcurrency);
                
                // 批次间休息，避免对系统造成过大压力
                Thread.sleep(1000);
            }
            
            log.info("批量重建商品ES索引完成");
            
        } catch (Exception e) {
            log.error("批量重建商品ES索引失败", e);
            throw new RuntimeException("ES索引重建失败", e);
        }
    }

    /**
     * 并发处理单个批次的商品
     */
    private void processBatchConcurrently(List<Long> productIds, int maxConcurrency) {
        // 将批次再分割为更小的子批次进行并发处理
        int subBatchSize = Math.max(1, productIds.size() / maxConcurrency);
        
        CompletableFuture<Void>[] futures = new CompletableFuture[maxConcurrency];
        
        for (int i = 0; i < maxConcurrency; i++) {
            int startIndex = i * subBatchSize;
            int endIndex = (i == maxConcurrency - 1) ? productIds.size() : (i + 1) * subBatchSize;
            
            if (startIndex >= productIds.size()) {
                break;
            }
            
            List<Long> subBatch = productIds.subList(startIndex, endIndex);
            
            futures[i] = CompletableFuture.runAsync(() -> {
                processSubBatch(subBatch);
            }, threadPoolExecutor);
        }
        
        // 等待所有子批次完成
        CompletableFuture.allOf(futures).join();
    }

    /**
     * 处理子批次商品
     */
    private void processSubBatch(List<Long> productIds) {
        for (Long productId : productIds) {
            try {
                productEsBuildService.buildProductEs(productId);
                log.debug("商品 {} ES索引重建成功", productId);
            } catch (Exception e) {
                log.error("商品 {} ES索引重建失败", productId, e);
                // 继续处理其他商品，不中断整个流程
            }
        }
    }

    /**
     * 重建指定商品的ES索引
     */
    @Override
    public void rebuildProductIndex(Long productId) {
        log.info("重建商品 {} 的ES索引", productId);
        try {
            productEsBuildService.buildProductEs(productId);
            log.info("商品 {} ES索引重建成功", productId);
        } catch (Exception e) {
            log.error("商品 {} ES索引重建失败", productId, e);
            throw new RuntimeException("商品ES索引重建失败", e);
        }
    }

    /**
     * 重建指定商品列表的ES索引
     */
    @Override
    public void rebuildProductIndexBatch(List<Long> productIds) {
        log.info("批量重建 {} 个商品的ES索引", productIds.size());
        
        for (Long productId : productIds) {
            try {
                productEsBuildService.buildProductEs(productId);
                log.debug("商品 {} ES索引重建成功", productId);
            } catch (Exception e) {
                log.error("商品 {} ES索引重建失败", productId, e);
                // 继续处理其他商品
            }
        }
        
        log.info("批量重建ES索引完成，处理商品数：{}", productIds.size());
    }
}
