package com.sankuai.shangou.seashop.trade.core.service.model.cart;

import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SelectAllBo {

    private Long userId;
    /**
     * 是否选中
     */
    private Boolean whetherSelect;
    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;

}
