package com.sankuai.shangou.seashop.trade.core.service.assist.cart.context;

import java.util.List;

import com.sankuai.shangou.seashop.trade.common.remote.model.user.ShippingAddressBo;
import com.sankuai.shangou.seashop.trade.core.service.assist.cart.BuildContext;
import com.sankuai.shangou.seashop.trade.core.service.model.ShopProductListBo;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public abstract class AbstractErpOrderBuildContext implements BuildContext {
    private Long userId;
    /**
     * 用户收货地址
     */
    private ShippingAddressBo shippingAddress;
    /**
     * 按店铺分组的商品列表
     */
    private List<ShopProductListBo> shopProductList;

    /**
     * 订单平台，0：PC；2：小程序
     */
    private Integer platform;
}
