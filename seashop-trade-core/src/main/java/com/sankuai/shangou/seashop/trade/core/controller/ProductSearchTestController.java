package com.sankuai.shangou.seashop.trade.core.controller;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;
import com.sankuai.shangou.seashop.trade.core.service.model.product.SearchProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.SearchTradeProductRespBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 商品搜索测试控制器
 * 用于测试替换号模糊搜索功能
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/test/product/search")
public class ProductSearchTestController {

    @Resource
    private TradeProductService tradeProductService;

    /**
     * 测试商品搜索功能
     * 
     * @param searchKey 搜索关键词
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 搜索结果
     */
    @GetMapping("/test")
    public BaseResp<SearchTradeProductRespBo> testSearch(
            @RequestParam String searchKey,
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        log.info("测试商品搜索，关键词：{}", searchKey);
        
        try {
            SearchProductBo searchBo = new SearchProductBo();
            searchBo.setSearchKey(searchKey);
            searchBo.setPageNo(pageNo);
            searchBo.setPageSize(pageSize);
            
            SearchTradeProductRespBo result = tradeProductService.search(searchBo);
            
            log.info("搜索完成，关键词：{}，结果数量：{}", searchKey, 
                    result.getProductPage() != null ? result.getProductPage().getTotal() : 0);
            
            return BaseResp.success(result);
            
        } catch (Exception e) {
            log.error("商品搜索测试失败，关键词：{}", searchKey, e);
            return BaseResp.fail("搜索失败：" + e.getMessage());
        }
    }

    /**
     * 测试替换号模糊搜索
     * 
     * @param replaceNumber 替换号（支持部分匹配）
     * @return 搜索结果
     */
    @GetMapping("/testReplaceNumber")
    public BaseResp<SearchTradeProductRespBo> testReplaceNumberSearch(
            @RequestParam String replaceNumber) {
        
        log.info("测试替换号模糊搜索，替换号：{}", replaceNumber);
        
        try {
            SearchProductBo searchBo = new SearchProductBo();
            searchBo.setSearchKey(replaceNumber);
            searchBo.setPageNo(1);
            searchBo.setPageSize(20);
            
            SearchTradeProductRespBo result = tradeProductService.search(searchBo);
            
            log.info("替换号搜索完成，替换号：{}，结果数量：{}", replaceNumber, 
                    result.getProductPage() != null ? result.getProductPage().getTotal() : 0);
            
            return BaseResp.success(result);
            
        } catch (Exception e) {
            log.error("替换号搜索测试失败，替换号：{}", replaceNumber, e);
            return BaseResp.fail("搜索失败：" + e.getMessage());
        }
    }

    /**
     * 测试OE号模糊搜索
     * 
     * @param oeCode OE号（支持部分匹配）
     * @return 搜索结果
     */
    @GetMapping("/testOeCode")
    public BaseResp<SearchTradeProductRespBo> testOeCodeSearch(
            @RequestParam String oeCode) {
        
        log.info("测试OE号模糊搜索，OE号：{}", oeCode);
        
        try {
            SearchProductBo searchBo = new SearchProductBo();
            searchBo.setSearchKey(oeCode);
            searchBo.setPageNo(1);
            searchBo.setPageSize(20);
            
            SearchTradeProductRespBo result = tradeProductService.search(searchBo);
            
            log.info("OE号搜索完成，OE号：{}，结果数量：{}", oeCode, 
                    result.getProductPage() != null ? result.getProductPage().getTotal() : 0);
            
            return BaseResp.success(result);
            
        } catch (Exception e) {
            log.error("OE号搜索测试失败，OE号：{}", oeCode, e);
            return BaseResp.fail("搜索失败：" + e.getMessage());
        }
    }

    /**
     * 测试适配车型模糊搜索
     * 
     * @param carModel 适配车型（支持部分匹配）
     * @return 搜索结果
     */
    @GetMapping("/testCarModel")
    public BaseResp<SearchTradeProductRespBo> testCarModelSearch(
            @RequestParam String carModel) {
        
        log.info("测试适配车型模糊搜索，车型：{}", carModel);
        
        try {
            SearchProductBo searchBo = new SearchProductBo();
            searchBo.setSearchKey(carModel);
            searchBo.setPageNo(1);
            searchBo.setPageSize(20);
            
            SearchTradeProductRespBo result = tradeProductService.search(searchBo);
            
            log.info("适配车型搜索完成，车型：{}，结果数量：{}", carModel, 
                    result.getProductPage() != null ? result.getProductPage().getTotal() : 0);
            
            return BaseResp.success(result);
            
        } catch (Exception e) {
            log.error("适配车型搜索测试失败，车型：{}", carModel, e);
            return BaseResp.fail("搜索失败：" + e.getMessage());
        }
    }
}
