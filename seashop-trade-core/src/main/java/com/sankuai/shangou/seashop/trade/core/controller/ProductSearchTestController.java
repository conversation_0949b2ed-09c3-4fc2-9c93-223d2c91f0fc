package com.sankuai.shangou.seashop.trade.core.controller;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;
import com.sankuai.shangou.seashop.trade.core.service.model.product.SearchProductBo;
import com.sankuai.shangou.seashop.trade.core.service.model.product.SearchTradeProductRespBo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 商品搜索测试控制器
 * 用于测试替换号模糊搜索和大小写不敏感功能
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/test/product/search")
public class ProductSearchTestController {

    @Resource
    private TradeProductService tradeProductService;

    /**
     * 测试商品搜索功能
     * 
     * @param searchKey 搜索关键词
     * @param pageNo 页码
     * @param pageSize 页大小
     * @return 搜索结果
     */
    @GetMapping("/test")
    public BaseResp<SearchTradeProductRespBo> testSearch(
            @RequestParam String searchKey,
            @RequestParam(defaultValue = "1") Integer pageNo,
            @RequestParam(defaultValue = "10") Integer pageSize) {
        
        log.info("测试商品搜索，关键词：{}", searchKey);
        
        try {
            SearchProductBo searchBo = new SearchProductBo();
            searchBo.setSearchKey(searchKey);
            searchBo.setPageNo(pageNo);
            searchBo.setPageSize(pageSize);
            
            SearchTradeProductRespBo result = tradeProductService.search(searchBo);
            
            log.info("搜索完成，关键词：{}，结果数量：{}", searchKey, 
                    result.getProductPage() != null ? result.getProductPage().getTotal() : 0);
            
            return BaseResp.success(result);
            
        } catch (Exception e) {
            log.error("商品搜索测试失败，关键词：{}", searchKey, e);
            return BaseResp.fail("搜索失败：" + e.getMessage());
        }
    }

    /**
     * 测试大小写不敏感搜索
     * 
     * @param keyword 搜索关键词（测试大小写）
     * @return 搜索结果
     */
    @GetMapping("/testCaseInsensitive")
    public BaseResp<SearchTradeProductRespBo> testCaseInsensitiveSearch(
            @RequestParam String keyword) {
        
        log.info("测试大小写不敏感搜索，关键词：{}", keyword);
        
        try {
            SearchProductBo searchBo = new SearchProductBo();
            searchBo.setSearchKey(keyword);
            searchBo.setPageNo(1);
            searchBo.setPageSize(20);
            
            SearchTradeProductRespBo result = tradeProductService.search(searchBo);
            
            log.info("大小写不敏感搜索完成，关键词：{}，结果数量：{}", keyword, 
                    result.getProductPage() != null ? result.getProductPage().getTotal() : 0);
            
            return BaseResp.success(result);
            
        } catch (Exception e) {
            log.error("大小写不敏感搜索测试失败，关键词：{}", keyword, e);
            return BaseResp.fail("搜索失败：" + e.getMessage());
        }
    }

    /**
     * 测试替换号模糊搜索（大小写不敏感）
     * 
     * @param replaceNumber 替换号（支持部分匹配和大小写不敏感）
     * @return 搜索结果
     */
    @GetMapping("/testReplaceNumber")
    public BaseResp<SearchTradeProductRespBo> testReplaceNumberSearch(
            @RequestParam String replaceNumber) {
        
        log.info("测试替换号模糊搜索（大小写不敏感），替换号：{}", replaceNumber);
        
        try {
            SearchProductBo searchBo = new SearchProductBo();
            searchBo.setSearchKey(replaceNumber);
            searchBo.setPageNo(1);
            searchBo.setPageSize(20);
            
            SearchTradeProductRespBo result = tradeProductService.search(searchBo);
            
            log.info("替换号搜索完成，替换号：{}，结果数量：{}", replaceNumber, 
                    result.getProductPage() != null ? result.getProductPage().getTotal() : 0);
            
            return BaseResp.success(result);
            
        } catch (Exception e) {
            log.error("替换号搜索测试失败，替换号：{}", replaceNumber, e);
            return BaseResp.fail("搜索失败：" + e.getMessage());
        }
    }
}
