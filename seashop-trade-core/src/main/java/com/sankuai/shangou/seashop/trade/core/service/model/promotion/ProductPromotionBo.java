package com.sankuai.shangou.seashop.trade.core.service.model.promotion;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * 店铺下的商品营销对象
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class ProductPromotionBo {

    /**
     * 商品ID
     */
    private Long productId;
    /**
     * skuId
     */
    private String skuId;
    /**
     * 商品满足的多个营销信息，初始化，后续使用的使用不判断，直接add
     */
    private List<PromotionBo> promotionList;

    public List<PromotionBo> getPromotionList() {
        if (promotionList == null) {
            promotionList = new ArrayList<>();
        }
        return promotionList;
    }

}
