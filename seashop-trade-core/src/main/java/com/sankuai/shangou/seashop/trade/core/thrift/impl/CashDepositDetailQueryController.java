package com.sankuai.shangou.seashop.trade.core.thrift.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.thrift.core.enums.finance.CashDepositOperatorTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositDetailResp;
import com.sankuai.shangou.seashop.trade.common.remote.FinanceRemoteService;
import com.sankuai.shangou.seashop.trade.thrift.core.CashDepositDetailQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.finance.ApiCashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.finance.ApiCashDepositDetailResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/2/002
 * @description:
 */
@RestController
@RequestMapping("/cashDepositDetail")
public class CashDepositDetailQueryController implements CashDepositDetailQueryFeign {

    @Resource
    private FinanceRemoteService financeRemoteService;

    private static final Integer DAY_HOUR = 24;

    @Override
    @PostMapping(value = "/pageList",consumes = "application/json")
    public ResultDto<BasePageResp<ApiCashDepositDetailResp>> pageList(@RequestBody ApiCashDepositDetailQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            req.checkParameter();
            CashDepositDetailQueryReq queryReq = JsonUtil.copy(req, CashDepositDetailQueryReq.class);
            BasePageResp<CashDepositDetailResp> cashDepositDetailPage = financeRemoteService.pageList(queryReq);
            return PageResultHelper.transfer(cashDepositDetailPage, ApiCashDepositDetailResp.class, resp -> {
                if (resp.getOperatorType().equals(CashDepositOperatorTypeEnum.PAY.getType())) {
                    if (null != resp.getAddDate()) {
                        // 获取 addDate 和 当前时间的间隔天数，如果有小数，向上取整
                        long between = DateUtil.between(resp.getAddDate(), DateUtil.date(), DateUnit.HOUR, true);
                        long days = between / DAY_HOUR;
                        if (between % DAY_HOUR > 0) {
                            days++;
                        }
                        resp.setPaymentDays(Integer.valueOf(days + ""));
                    }
                }
            });
        });
    }
}
