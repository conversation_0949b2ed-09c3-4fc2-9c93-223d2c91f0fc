package com.sankuai.shangou.seashop.trade.core.service.model.promotion;

import com.sankuai.shangou.seashop.trade.thrift.core.enums.AddonProductPromotionTabEnum;
import lombok.*;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class AddonDescBo {

    /**
     * 凑单类型。1：折扣；2：满减
     */
    private Integer type;
    /**
     * 活动类型描述
     */
    private String typeDesc;
    /**
     * 凑单对应的营销描述
     */
    private String promotionDesc;

    public AddonDescBo(AddonProductPromotionTabEnum tabEnum, String promotionDesc) {
        this.type = tabEnum.getCode();
        this.typeDesc = tabEnum.getDesc();
        this.promotionDesc = promotionDesc;
    }

}
