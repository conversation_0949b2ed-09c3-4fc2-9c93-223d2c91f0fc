package com.sankuai.shangou.seashop.trade.core.service.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * 勾选购物车sku入参
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Builder
public class SelectShopBo {

    /**
     * 商家会员ID，用于校验是否操作的是自己的数据
     */
    private Long userId;
    /**
     * 是否选中
     */
    private Boolean whetherSelect;
    /**
     * 店铺信息
     */
    private ShoppingCartShopBo shop;
    /**
     * 商品列表，需要包括发生变更的那条数据
     */
    private List<ShopProductBo> productList;

}
