package com.sankuai.shangou.seashop.user.thrift.account.dto.shippingAddress;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@ToString
@Data
public class ShippingAddressDto extends BaseParamReq {

    private Long id;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 区域ID
     */
    private Integer regionId;
    /**
     * 收货人
     */
    private String shipTo;
    /**
     * 收货地址
     */
    private String address;
    /**
     * 详细地址
     */
    private String addressDetail;
    /**
     * 收货人电话
     */
    private String phone;
    /**
     * 是否为默认
     */
    private Boolean whetherDefault;
    /**
     * 是否为轻松购地址
     */
    private Boolean whetherQuick;
    /**
     * 经度
     */
    private BigDecimal longitude;
    /**
     * 纬度
     */
    private BigDecimal latitude;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 地区名称
     */
    private String regionName;
    /**
     * 收货地址
     */
    private String addressName;

    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 区县名称
     */
    private String districtName;
    /**
     * 街道名称
     */
    private String streetName;
    /**
     * 省市区街道全称
     */
    private String regionFullName;
    /**
     * 省份ID
     */
    private Long provinceId;
    /**
     * 城市ID
     */
    private Long cityId;
    /**
     * 完整的区域路径(逗号隔开)
     */
    private String regionPath;
}
