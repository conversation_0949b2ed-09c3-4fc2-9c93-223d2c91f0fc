package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryForbiddenAreaTplReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateDetailReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryRestrictedRegionReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ForbiddenAreaTplResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryRestrictedRegionResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 运费模版
 */
@FeignClient(name = "himall-base", contextId = "FreightAreaQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/freightArea")
public interface FreightAreaQueryFeign {

    /**
     * 供应商后台-交易-运费模版管理(供应商查询列表)
     */
    @PostMapping(value = "/queryFreightTemplateList", consumes = "application/json")
    ResultDto<QueryFreightTemplateResp> queryFreightTemplateList(@RequestBody QueryFreightTemplateReq queryFreightTemplateReq) throws TException;

    /**
     * 供应商后台-交易-运费模版管理(查询详情接口)
     */
    @PostMapping(value = "/queryFreightTemplateDetail", consumes = "application/json")
    ResultDto<QueryFreightTemplateDetailResp> queryFreightTemplateDetail(@RequestBody QueryFreightTemplateDetailReq queryFreightTemplateDetailReq) throws TException;

    /**
     * 平台端-运费模版管理(平台查询列表)
     */
    @PostMapping(value = "/queryMFreightTemplateList", consumes = "application/json")
    ResultDto<QueryFreightTemplateResp> queryMFreightTemplateList(@RequestBody QueryFreightTemplateReq queryFreightTemplateReq) throws TException;

    /**
     * 查询受限区域
     */
    @PostMapping(value = "/queryRestrictedRegion", consumes = "application/json")
    ResultDto<QueryRestrictedRegionResp> queryRestrictedRegion(@RequestBody QueryRestrictedRegionReq queryRestrictedRegionReq) throws TException;

    /**
     * 根据模板ID查询运费模板
     */
    @PostMapping(value = "/queryTplByTemplateIdList", consumes = "application/json")
    ResultDto<QueryFreightTemplateResp> queryTplByTemplateIdList(@RequestBody BaseBatchIdReq req) throws TException;

    /**
     * 查询开启非销售区域和地址在非销售区域的运费模板
     */
    @PostMapping(value = "/getForbiddenOnAndAddrInForbiddenArea", consumes = "application/json")
    ResultDto<ForbiddenAreaTplResp> getForbiddenOnAndAddrInForbiddenArea(@RequestBody QueryForbiddenAreaTplReq req) throws TException;
}
