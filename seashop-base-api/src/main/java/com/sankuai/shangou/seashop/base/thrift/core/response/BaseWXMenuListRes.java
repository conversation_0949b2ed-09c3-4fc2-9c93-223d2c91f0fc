package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.List;

@Data
public class BaseWXMenuListRes extends BaseThriftDto {
    private Long id;

    /**
     * 菜单名称
     */
    private String name;

    /**
     * 链接类型链接类型
     */
    private Integer linkType;

    /**
     * 链接值
     */
    private String linkValue;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 是否自定义链接
     */

    private Integer whetherCustom;

    /**
     * 下级菜单
     */
    private List<BaseWXMenuRes> subs;


    @Override
    public String toString() {
        return "BaseWXMenuListRes{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", linkType=" + linkType +
            ", linkValue='" + linkValue + '\'' +
            ", parentId=" + parentId +
            ", whetherCustom=" + whetherCustom +
            ", subs=" + subs +
            '}';
    }
}
