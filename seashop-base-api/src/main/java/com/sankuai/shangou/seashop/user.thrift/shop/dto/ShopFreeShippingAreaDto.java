package com.sankuai.shangou.seashop.user.thrift.shop.dto;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Data
public class ShopFreeShippingAreaDto extends BaseParamReq {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 供应商ID
     */
    private Long shopId;

    /**
     * 地区ID
     */
    private Integer regionId;

    /**
     * 地区全路径
     */
    private String regionPath;

    @Override
    public void checkParameter() {
        if (null == regionId) {
            throw new IllegalArgumentException("regionId不能为空");
        }
        if (null == regionPath) {
            throw new IllegalArgumentException("regionPath不能为空");
        }
    }
}
