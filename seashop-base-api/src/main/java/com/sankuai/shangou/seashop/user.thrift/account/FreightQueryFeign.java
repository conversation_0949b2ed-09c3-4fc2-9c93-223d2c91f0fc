package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.CalculateFreightReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.freight.CalculateFreightResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */

/**
 * 运费查询服务类
 */
@FeignClient(name = "himall-base", contextId = "FreightQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/freight")
public interface FreightQueryFeign {

    /**
     * 根据店铺和商品信息计算运费
     * @param calculateReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/calculateFreight", consumes = "application/json")
    ResultDto<CalculateFreightResp> calculateFreight(@RequestBody CalculateFreightReq calculateReq) throws TException;

}
