package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

/**
 * @description: 管理员信息
 * @author: LXH
 **/
@Data
public class ManagerResp extends BaseThriftDto {
    /**
     * 管理员id
     */
    private Long id;

    /**
     * 供应商id
     */
    private Long shopId;

    /**
     * 权限组id
     */
    private Long roleId;

    /**
     * 权限组名称
     */
    private String roleName;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 手机号码
     */
    private String cellphone;

    /**
     * 备注
     */
    private String remark;

    /**
     * 真实姓名
     */
    private String realName;

    /**
     * 加密方式
     */
    private String encryptionMode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 更新人
     */
    private Long updateUser;
    /**
     * 平台审核状态
     * 0, "默认"
     * 1, "待审核"
     * 2, "待审核"
     * 4, "审核拒绝"
     * 7, "审核通过"
     */
    private Integer plateStatus;
}
