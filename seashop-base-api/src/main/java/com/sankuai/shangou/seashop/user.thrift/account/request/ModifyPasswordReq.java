package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2024/05/24 9:38
 */
@Data
public class ModifyPasswordReq extends BaseParamReq {
    /**
     * 用户id
     */
    private Long id;

    /**
     * 旧密码
     */
    private String oldPassword;

    /**
     * 新密码
     */
    private String newPassword;

    /**
     * 确认密码
     */
    private String confirmPassword;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(id, "用户id不能为空");
        AssertUtil.throwIfTrue(StringUtils.isBlank(newPassword), "密码不能为空");
        AssertUtil.throwIfTrue(StringUtils.isBlank(confirmPassword), "确认密码不能为空");
    }
}
