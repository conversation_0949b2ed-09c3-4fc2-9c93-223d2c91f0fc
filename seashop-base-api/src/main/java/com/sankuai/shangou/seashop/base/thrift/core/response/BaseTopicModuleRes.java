package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class BaseTopicModuleRes  extends BaseThriftDto {
    private Long id;

    /**
     * 专题id
     */
    private Long topicId;

    /**
     * 专题名称
     */
    private String name;

    /**
     * 标题位置 0、left；1、center ；2、right
     */
    private Integer titleAlign;

    /**
     * 模板绑定商品
     */
    private List<BaseModuleProductRes> moduleProducts;
}
