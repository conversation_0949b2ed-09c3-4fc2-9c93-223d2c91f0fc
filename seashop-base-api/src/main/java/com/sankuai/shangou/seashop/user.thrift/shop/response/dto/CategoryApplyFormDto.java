package com.sankuai.shangou.seashop.user.thrift.shop.response.dto;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:53
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CategoryApplyFormDto extends BaseThriftDto {

    /**
     * 表单id
     */
    private Long formId;

    /**
     * 表单名称
     */
    private String formName;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 表单数据
     */
    private List<CategoryApplyFormFieldDto> fieldList;
}
