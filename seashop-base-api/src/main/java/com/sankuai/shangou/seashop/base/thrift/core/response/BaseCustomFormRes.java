package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class BaseCustomFormRes extends BaseThriftDto {
    private Long id;
    /**
     * 表单名称
     */
    private String name;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 自定义表单项
     */
    private List<BaseCustomFormFieldRes> formFields;

    /**
     * 创建时间
     */
    private Date updateDate;

}
