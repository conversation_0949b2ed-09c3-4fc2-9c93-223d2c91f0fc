package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberListReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryUserIdReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberGroupingResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberListResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.UserIdListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 商家服务类
 */
@FeignClient(name = "himall-base", contextId = "MemberQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/member")
public interface ShopMemberQueryFeign {

    /**
     * 分页查询商家信息
     * @param queryMemberPageReq 查询条件
     * @return 商家信息
     */
    @PostMapping(value = "/queryMemberPage", consumes = "application/json")
    ResultDto<BasePageResp<MemberResp>> queryMemberPage(@RequestBody QueryMemberPageReq queryMemberPageReq) throws TException;

    /**
     * 分页查询商家信息
     * @param queryMemberPageReq 查询条件
     * @return 商家信息
     */
    @PostMapping(value = "/queryMemberPageAndLabel", consumes = "application/json")
    ResultDto<BasePageResp<MemberResp>> queryMemberPageAndLabel(@RequestBody QueryMemberPageReq queryMemberPageReq) throws TException;

    /**
     * 查询单个商家信息
     * @param queryMemberReq 查询条件
     * @return 商家信息
     */
    @PostMapping(value = "/queryMember", consumes = "application/json")
    ResultDto<MemberResp> queryMember(@RequestBody QueryMemberReq queryMemberReq) throws TException;

    /**
     * 查询多个商家信息
     * @param queryMemberReq 查询条件
     * @return 商家信息
     */
    @PostMapping(value = "/queryMemberList", consumes = "application/json")
    ResultDto<MemberListResp> queryMemberList(@RequestBody QueryMemberListReq queryMemberReq) throws TException;

    /**
     * 查询商家分组信息
     * @return
     * @throws TException
     */
    @GetMapping(value = "/queryMemberGrouping")
    ResultDto<MemberGroupingResp> queryMemberGrouping() throws TException;

    /**
     * 根据条件查询用户ID
     * @param queryReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryUserId", consumes = "application/json")
    ResultDto<UserIdListResp> queryUserId(@RequestBody QueryUserIdReq queryReq) throws TException;
}
