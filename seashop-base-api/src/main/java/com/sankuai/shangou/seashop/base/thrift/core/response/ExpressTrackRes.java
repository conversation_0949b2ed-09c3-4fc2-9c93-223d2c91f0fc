package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class ExpressTrackRes extends BaseThriftDto {

    private long id;
    /**
     * 业务方类型
     */
    private int bizType;
    /**
     * 快递对接平台
     */
    private int platform;
    /**
     * 快递公司code
     */
    @JsonProperty("shipperCode")
    private String companyCode;
    /**
     * 快递单号
     */
    @JsonProperty("logisticsCode")
    private String expressNo;
    /**
     * 快递进度列表
     */
    @JsonProperty("traces")
    private List<ExpressItemDTO> expressProgressItemDTOList;
    /**
     * 快递单状态
     */
    private int status;
    /**
     * 发件人信息
     */
    private ExpressContactRes sender;
    /**
     * 收件人信息
     */
    private ExpressContactRes receiver;
    /**
     * 添加时间
     */
    private Date AddTime;
    /**
     * 扩展字段
     */
    private Map<String, String> extendMap;
}
