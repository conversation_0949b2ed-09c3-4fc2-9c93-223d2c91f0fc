package com.sankuai.shangou.seashop.user.thrift.account.enums;


/**
 * @description:
 * @author: LXH
 **/
public enum InvoiceSubjectTypeEnum {
    /**
     * 个人
     */
    PERSONAL(1, "个人"),
    /**
     * 企业
     */
    ENTERPRISE(2, "企业");

    private final Integer value;
    private final String name;

    InvoiceSubjectTypeEnum(Integer value, String name) {
        this.value = value;
        this.name = name;
    }

    public Integer getValue() {
        return value;
    }

    public String getName() {
        return name;
    }

    public static InvoiceSubjectTypeEnum findByValue(Integer value) {
        switch (value) {
            case 1:
                return PERSONAL;
            case 2:
                return ENTERPRISE;
            default:
                return null;
        }
    }

}
