package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/11/28 15:02
 */
@Data
public class SaveShopErpReq extends BaseParamReq {


    /**
     * 店铺id
     */
    @PrimaryField
    private Long shopId;

    /**
     * 开启erp类型 0未开启 1旺店通 2聚水潭 3网店管家 4吉客云
     */
    @ExaminField(description = "开启erp类型 0未开启 1旺店通 2聚水潭 3网店管家 4吉客云")
    private Integer erpType;

    /**
     * 聚水潭授权 0未授权 1已授权
     */
    @ExaminField(description = "聚水潭授权 0未授权 1已授权")
    private Boolean jstStatus;

    /**
     * 聚水潭授权url
     */
    @ExaminField(description = "聚水潭授权url")
    private String jstUrl;

    /**
     * 聚水潭授权url创建时间
     */
    @ExaminField(description = "聚水潭授权url创建时间")
    private Date jstUrlCreateTime;

    /**
     * 聚水潭授权码
     */
    @ExaminField(description = "聚水潭授权码")
    private String jstCode;

    /**
     * 聚水潭授权码获得时间
     */
    @ExaminField(description = "聚水潭授权码获得时间")
    private Date jstCodeGetTime;

    /**
     * 聚水潭访问令牌
     */
    @ExaminField(description = "聚水潭访问令牌")
    private String jstAccessToken;

    /**
     * 聚水潭访问令牌多少秒后过期
     */
    @ExaminField(description = "聚水潭访问令牌多少秒后过期")
    private Integer jstAccessTokenExpires;

    /**
     * 聚水潭更新令牌
     */
    @ExaminField(description = "聚水潭更新令牌")
    private String jstRefreshToken;

    /**
     * 聚水潭令牌获取时间
     */
    @ExaminField(description = "聚水潭令牌获取时间")
    private Date jstTokenGetTime;

    /**
     * 聚水潭店铺编号
     */
    @ExaminField(description = "聚水潭店铺编号")
    private String jstShopId;

    /**
     * 聚水潭公司编号
     */
    @ExaminField(description = "聚水潭公司编号")
    private String jstCoId;

    /**
     * 菠萝派供应商token
     */
    @ExaminField(description = "菠萝派供应商token")
    private String blpToken;

    /**
     * 旺店通访问令牌
     */
    @ExaminField(description = "旺店通访问令牌")
    private String wdtToken;

    /**
     * 是否发送短信
     */
    @ExaminField(description = "是否发送短信")
    private Boolean whetherSendSms;

    public void checkParameter() {
        if (shopId == null) {
            throw new IllegalArgumentException("shopId 不能为空");
        }
    }
}
