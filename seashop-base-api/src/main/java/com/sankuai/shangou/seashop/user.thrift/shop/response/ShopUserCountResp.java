package com.sankuai.shangou.seashop.user.thrift.shop.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopUserCountResp {
    /**
     * 今日新增商家
     */
    private Integer todayAddShopCount;

    /**
     * 供应商总数
     */
    private Integer shopCount;

    /**
     * 今日新增供应商
     */
    private Integer todayAddSupplierCount;

    /**
     * 待审核供应商
     */
    private Integer waitAuditSupplierCount;

    /**
     * 昨日新增供应商
     */
    private Integer yesterdayAddSupplierCount;

    /**
     * 到期供应商
     */
    private Integer expireShopCount;

    /**
     * 待处理提现店铺
     */
    private Integer waitWithdrawShopCount;
}
