package com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress;

import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@ToString
@Data
public class GetShippingAddressByIdReq extends BaseParamReq {

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 收货地址ID
     */
    private Long id;


    /**
     * 参数校验
     */
    @Override
    public void checkParameter() {
        if (this.userId == null || this.userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (this.id == null || this.id <= 0) {
            throw new InvalidParamException("id不能为空");
        }
    }

}
