package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CustomerServiceQueryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.CustomerServiceResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.CustomerServiceRespList;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopOpenApiSettingResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * @description：供应商客服查询接口
 * @author： LXH
 * @create： 2023/11/27 9:13
 */

/**
 * 供应商客服查询接口
 */
@FeignClient(name = "himall-base", contextId = "CustomerServiceQueryFeign", url = "${himall-base.dev.url:}",path = "/himall-base/shop/customerService")
public interface CustomerServiceQueryFeign {
    /**
     * 查询客服列表
     * @param baseIdReq 查询入参
     * @return 查询入参
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/queryPage",consumes = "application/json")
    ResultDto<BasePageResp<CustomerServiceResp>> queryPage(@RequestBody CustomerServiceQueryPageReq baseIdReq) throws TException;

    /**
     * 查询客服列表
     * @param baseIdReq 查询入参
     * @return 查询入参
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/queryList",consumes = "application/json")
    ResultDto<List<CustomerServiceRespList>> queryList(@RequestBody CustomerServiceQueryPageReq baseIdReq) throws TException;

    /**
     * 查询客服详情
     * @param baseIdReq 查询入参
     * @return 查询入参
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/queryDetail",consumes = "application/json")
    ResultDto<CustomerServiceResp> queryDetail(@RequestBody BaseIdReq baseIdReq) throws TException;

    /**
     * 查询HI客服信息
     * @param appKey 查询入参
     * @return 查询入参
     * @throws TException RPC调用异常
     */
    @GetMapping(value = "/queryOpenApiSetting")
    ResultDto<ShopOpenApiSettingResp> queryOpenApiSetting(@RequestParam String appKey) throws TException;

    /**
     * 查询HI客服信息byId
     * @param shopId 查询入参byId
     * @return 查询入参byId
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/queryOpenApiSettingByShop",consumes = "application/json")
    ResultDto<ShopOpenApiSettingResp> queryOpenApiSettingByShop(@RequestBody BaseIdReq shopId) throws TException;
}
