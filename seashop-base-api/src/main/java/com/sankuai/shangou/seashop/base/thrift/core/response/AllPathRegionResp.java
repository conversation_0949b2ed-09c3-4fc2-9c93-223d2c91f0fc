package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/16 10:26
 */
@Data
public class AllPathRegionResp extends BaseParamReq {

    /**
     * 省id
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long provinceId;

    /**
     * 省名称
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String provinceName;

    /**
     * 市id
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cityId;

    /**
     * 市名称
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String cityName;

    /**
     * 区id
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long countyId;

    /**
     * 区名称
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String countyName;

    /**
     * 乡镇id
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String townIds;

    /**
     * 乡镇名称
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String townsNames;
}
