package com.sankuai.shangou.seashop.user.thrift.shop.response.es;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BusinessCategoryEsResp extends BaseThriftDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目全名(包含上级)
     */
    private String fullCategoryName;

    /**
     * 是否冻结
     */
    private Boolean whetherFrozen;
}