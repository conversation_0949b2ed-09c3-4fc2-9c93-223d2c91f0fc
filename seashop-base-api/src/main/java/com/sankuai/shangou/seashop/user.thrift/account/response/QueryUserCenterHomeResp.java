package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/12/12 16:03
 */
@Data
public class QueryUserCenterHomeResp extends BaseParamReq {
    /**
     * 用户的基础信息
     */
    private UserBaseResp userBaseResp;

    /**
     * 优惠券张数
     */
    private Integer userCoupon;

    /**
     * 订单各状态的数量
     */
    private EachOrderStatusCountResp eachOrderStatusCountResp;

    /**
     * 商品推荐
     */
    private List<ProductBaseResp> recommendList;

    /**
     * 浏览记录
     */
    private List<ProductBaseResp> browsingList;

    /**
     * 收藏店铺
     */
    private List<FavoriteShopResp> favoriteShopList;

    /**
     * 商品关注
     */
    private List<ProductBaseResp> favoriteProductList;

    /**
     * 收藏店铺数量
     */
    private Integer favoriteShopNum;

    /**
     * 商品关注数量
     */
    private Integer favoriteProductNum;

    /**
     * 客服电话
     */
    private String sitePhone;
}
