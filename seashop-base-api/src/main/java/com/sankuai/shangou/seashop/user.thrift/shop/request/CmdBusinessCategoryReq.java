package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description: 店铺类目修改请求入参
 * @author: LXH
 **/
@ToString
@Data
public class CmdBusinessCategoryReq extends BaseParamReq {
    @PrimaryField
    @ExaminField(description = "店铺类目ID")
    public Long id;
    @ExaminField(description = "分佣比例")
    private BigDecimal commissionRate;
    @ExaminField(description = "保证金")
    private BigDecimal bond;

    public CmdBusinessCategoryReq(){

    }

    public CmdBusinessCategoryReq(BaseIdReq baseIdReq){
        this.id = baseIdReq.getId();
    }
}
