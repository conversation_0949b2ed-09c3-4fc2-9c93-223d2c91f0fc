package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @description: 供应商列表查询入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class QueryShopPageReq extends BasePageReq {
    /**
     * 供应商ID列表
     */
    private List<Long> shopIds;
    /**
     * 商家用户ID
     */
    private Long userId;
}
