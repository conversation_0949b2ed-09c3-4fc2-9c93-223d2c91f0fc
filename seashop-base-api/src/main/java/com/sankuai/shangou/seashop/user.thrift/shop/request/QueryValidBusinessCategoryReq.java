package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/02/01 10:18
 */
@ToString
@Data
public class QueryValidBusinessCategoryReq extends BaseParamReq {

    /**
     * 店铺Id
     */
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺Id不能为空");
    }
}
