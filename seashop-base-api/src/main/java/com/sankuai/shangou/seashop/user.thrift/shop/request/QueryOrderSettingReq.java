package com.sankuai.shangou.seashop.user.thrift.shop.request;

import lombok.Data;
import lombok.ToString;

/**
 * @description：店铺交易设置查询入参
 * @author： liweisong
 * @create： 2023/11/27 16:17
 */
@ToString
@Data
public class QueryOrderSettingReq {

    /**
     * 店铺ID
     */
    private Long shopId;

    public void checkParameter(){
        if(shopId == null){
            throw new IllegalArgumentException("shopId不能为空");
        }
    }
}
