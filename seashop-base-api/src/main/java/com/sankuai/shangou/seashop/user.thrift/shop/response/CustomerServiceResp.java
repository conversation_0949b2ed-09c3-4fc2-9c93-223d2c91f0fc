package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2023-11-29
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CustomerServiceResp extends BaseParamReq {

    /**
     * 客服id
     */
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 工具 1 QQ 2 旺旺 3 美洽 4 海商
     */
    private Integer tool;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 名称
     */
    private String name;

    /**
     * 通信账号
     */
    private String accountCode;

    /**
     * 终端类型
     */
    private Integer terminalType;

    /**
     * 客服状态
     */
    private Integer serverStatus;

    /**
     * 工具 1 QQ 2 旺旺 3 美洽 4 海商
     */
    private String toolDesc;

    /**
     * 类型
     */
    private String typeDesc;
}
