package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BusinessCategoryResp extends BaseThriftDto {
    /**
     * id
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 类目名
     */
    private String categoryName;

    /**
     * 类目全名(包含上级)
     */
    private String fullCategoryName;

    /**
     * 分佣比例
     */
    private BigDecimal commissionRate;

    /**
     * 保证金
     */
    private BigDecimal bond;

    /**
     * 是否冻结
     */
    private Boolean whetherFrozen;

    /**
     * 是否需要补充资料
     */
    private Boolean needSupply;
}
