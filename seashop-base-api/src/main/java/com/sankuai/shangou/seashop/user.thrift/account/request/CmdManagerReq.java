package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.List;

/**
 * @description: 编辑管理员请求入参
 * @author: LXH
 **/
@Data
public class CmdManagerReq extends BaseParamReq {
    /**
     * 管理员ID 修改时必填
     */
    private Long id;

    /**
     * 供应商id 新增时必填
     */
    private Long shopId;

    /**
     * 用户名 新增时必填
     */
    private String userName;

    /**
     * 密码 新增时必填
     */
    private String password;

    /**
     * 手机号 新增时必填
     */
    private String cellphone;

    /**
     * 权限组id 新增时必填
     */
    private Long roleId;

    /**
     * 操作人ID
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private Long operatorName;

    /**
     * 操作人ID
     */
    private String operatorIp;

    /**
     * 批量管理员ID 批量修改时使用
     */
    private List<Long> batchId;

    /**
     * 备注 非必填
     */
    private String remark;

    /**
     * 姓名
     */
    private String realName;
}
