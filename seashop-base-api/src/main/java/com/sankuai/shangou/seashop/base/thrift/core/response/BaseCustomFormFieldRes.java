package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

@Data
public class BaseCustomFormFieldRes extends BaseThriftDto {
    private Long id;

    /**
     * 自定义表单ID
     */
    private Long formId;
    /**
     * 字段名称
     */
    private String fieldName;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 格式
     */
    private Integer format;

    /**
     * 选项
     */
    private String option;

    /**
     * 是否必填
     */
    private Boolean isRequired;

    /**
     * 排序
     */
    private Long displaySequence;

    /**
     * 添加时间
     */
    private Date addedDate;
}
