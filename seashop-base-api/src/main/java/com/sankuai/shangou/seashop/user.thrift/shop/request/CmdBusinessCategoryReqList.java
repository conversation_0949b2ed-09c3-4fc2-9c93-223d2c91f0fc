package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 店铺类目修改列表请求入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdBusinessCategoryReqList extends BaseParamReq {
    @ExaminField(description = "店铺类目列表", isChildField = true, entityClassName = "com.sankuai.shangou.seashop.user.thrift.shop.request.CmdBusinessCategoryReq")
    public List<CmdBusinessCategoryReq> reqList;

    @PrimaryField(title = "店铺Id")
    @ExaminField(description = "店铺Id")
    public Long shopId;
}
