package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2024/1/23/023
 * @description:
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryFavoriteCountResp extends BaseParamReq {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 收藏数量
     */
    private Integer count;

    /**
     * 是否收藏
     */
    private Boolean favoriteFlag = false;
}
