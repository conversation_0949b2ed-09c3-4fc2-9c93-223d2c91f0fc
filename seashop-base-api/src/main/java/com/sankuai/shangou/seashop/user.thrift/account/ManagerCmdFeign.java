package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdManagerReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 管理员操作服务类
 */
@FeignClient(name = "himall-base", contextId = "ManagerCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/manager")
public interface ManagerCmdFeign {
    /**
     * 添加管理员
     *
     * @param cmdManagerReq 请求体
     * @return 管理员Id
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/addManager", consumes = "application/json")
    ResultDto<Long> addManager(@RequestBody CmdManagerReq cmdManagerReq) throws TException;

    /**
     * 编辑管理员
     *
     * @param cmdManagerReq 请求体
     * @return 管理员Id
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/editManager", consumes = "application/json")
    ResultDto<Long> editManager(@RequestBody CmdManagerReq cmdManagerReq) throws TException;

    /**
     * 删除管理员
     *
     * @param cmdManagerReq 请求体
     * @return 管理员Id
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/deleteManager", consumes = "application/json")
    ResultDto<Long> deleteManager(@RequestBody CmdManagerReq cmdManagerReq) throws TException;

    /**
     * 批量删除管理员
     *
     * @param cmdManagerReq 请求体
     * @return 管理员Id数量
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/batchDeleteManager", consumes = "application/json")
    ResultDto<Integer> batchDeleteManager(@RequestBody CmdManagerReq cmdManagerReq) throws TException;

}
