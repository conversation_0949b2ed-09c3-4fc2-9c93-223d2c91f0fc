package com.sankuai.shangou.seashop.user.thrift.shop.dto;


import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@ToString
@Data
public class QueryFreightTemplateDto extends BaseParamReq {
    /**
     * 主键
     */
    private Long id;

    /**
     * 运费模板名称
     */
    private String name;

    /**
     * 宝贝发货地
     */
    private Integer sourceAddress;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 是否商家负责运费
     */
    private Integer whetherFree;

    /**
     * 定价方法(按体积、重量计算）
     */
    private Integer valuationMethod;

    /**
     * 运送类型（物流、快递）
     */
    private Integer shippingMethod;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 非销售区域是否隐藏
     */
    private Boolean nonSalesAreaHide;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 关联商品数量
     */
    private Integer productNum;
}
