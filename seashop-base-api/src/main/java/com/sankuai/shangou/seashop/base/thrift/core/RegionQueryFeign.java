package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseRegionRes;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;
import java.util.Map;

@FeignClient(name = "himall-base",contextId = "RegionQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/region")
public interface RegionQueryFeign {

    /**
     * 初始化地区数据
     *
     * @param parentId
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getRegionByParentId")
    ResultDto<List<BaseRegionRes>> getRegionByParentId(@RequestParam("parentId") long parentId) throws TException;

    /**
     * 获取所有地区并排成树形结构
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getTreeRegions")
    ResultDto<String> getTreeRegions() throws TException;

    /**
     * 获取所有地区
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getAllRegions")
    ResultDto<List<BaseRegionRes>> getAllRegions() throws TException;


    /**
     * 根据地区id 获取所有上级地区和自己
     *
     * @param id
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getParentRegions")
    ResultDto<List<BaseRegionRes>> getParentRegions(@RequestParam("id") Long id) throws TException;

    /**
     * 根据地区code 获取所有上级地区和自己
     *
     * @param code
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getParentRegionsByCode")
    ResultDto<List<BaseRegionRes>> getParentRegionsByCode(@RequestParam("code") String code) throws TException;

    /**
     * 根据地区id 获取所有下级地区和自己
     *
     * @param id
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getSubRegions")
    ResultDto<List<BaseRegionRes>> getSubRegions(@RequestParam Long id) throws TException;

    /**
     * 根据地区id 获取一整条树的地区
     *
     * @param id
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getTrackRegionsById")
    ResultDto<List<BaseRegionRes>> getTrackRegionsById(@RequestParam Long id) throws TException;

    /**
     * 根据地区id集合获取地区的全路径
     *
     * @param regionIdsReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getAllPathRegions", consumes = "application/json")
    ResultDto<Map<String, AllPathRegionResp>> getAllPathRegions(@RequestBody RegionIdsReq regionIdsReq) throws TException;

}
