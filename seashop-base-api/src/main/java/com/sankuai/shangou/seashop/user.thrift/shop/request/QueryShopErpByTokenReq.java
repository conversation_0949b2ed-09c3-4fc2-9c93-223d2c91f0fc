package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.user.thrift.shop.enums.ErpTypeEnum;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 通过token+类型查询 入参
 *
 * <AUTHOR>
 * @date 2023/12/08 15:05
 */
@Data
public class QueryShopErpByTokenReq {

    /**
     * erp类型
     */
    private ErpTypeEnum erpType;

    private String token;

    public void checkParameter() {
        if (erpType == null) {
            throw new IllegalArgumentException("暂不支持该erp");
        }
        if (StringUtils.isBlank(token)) {
            throw new IllegalArgumentException("token不能为空");
        }
    }
}
