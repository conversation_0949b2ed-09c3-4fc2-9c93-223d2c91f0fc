package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

@ToString
@Data
public class DeleteFreightTemplateReq extends BaseParamReq {

    @ExaminField(description = "店铺ID")
    private Long shopId;

    @ExaminField(description = "模版ID")
    private Long templateId;

    public void checkParameter(){
        if(shopId == null){
            throw new IllegalArgumentException("shopId不能为空");
        }
        if(templateId == null){
            throw new IllegalArgumentException("templateId不能为空");
        }
    }
}
