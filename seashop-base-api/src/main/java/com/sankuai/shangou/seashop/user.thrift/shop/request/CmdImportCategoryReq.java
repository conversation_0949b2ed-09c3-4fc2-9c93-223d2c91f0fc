package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CmdImportCategoryReq extends BaseParamReq {
    @ExaminField(description = "店铺id")
    private String path;
    @PrimaryField(title = "店铺id")
    @ExaminField(description = "店铺id")
    private Long shopId;

}
