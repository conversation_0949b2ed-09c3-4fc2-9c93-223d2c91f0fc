package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Data
public class ShopSimpleQueryReq extends BaseThriftDto {

    /**
     * 店铺ID列表
     */
    private List<Long> shopIdList;

    /**
     * 店铺名称列表
     */
    private List<String> shopNameList;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 查保证金
     */
    private Boolean queryCashDeposit;
}
