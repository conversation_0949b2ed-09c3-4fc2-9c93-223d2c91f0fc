package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.dto.MenuDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryManagerReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMenuAuthReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.EpManagerResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.ManagerResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 管理员查询服务类
 */
@FeignClient(name = "himall-base", contextId = "ManagerQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/manager")
public interface ManagerQueryFeign {

    /**
     * 分页查询管理员信息
     *
     * @param queryManagerPageReq 请求体
     * @return 管理员信息
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/queryManagerPage", consumes = "application/json")
    ResultDto<BasePageResp<ManagerResp>> queryManagerPage(@RequestBody QueryManagerPageReq queryManagerPageReq) throws TException;

    /**
     * 查询管理员信息
     *
     * @param copy 请求体
     * @return 管理员信息
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/queryManager", consumes = "application/json")
    ResultDto<ManagerResp> queryManager(@RequestBody QueryManagerReq copy) throws TException;

    /**
     * 根据EP查询管理员信息
     *
     * @param id 请求体
     * @return 管理员信息
     * @throws TException RPC调用异常
     */
    @GetMapping(value = "/queryEpManager")
    ResultDto<EpManagerResp> queryEpManager(@RequestParam Integer id) throws TException;

    /**
     * 查询菜单权限
     *
     * @param queryMenuAuthReq 管理员信息
     * @return
     * @throws TException
     */

    @PostMapping(value = "/queryMenuAuth", consumes = "application/json")
    ResultDto<List<MenuDto>> queryMenuAuth(@RequestBody QueryMenuAuthReq queryMenuAuthReq) throws TException;

}
