package com.sankuai.shangou.seashop.user.thrift.auth;

import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.request.LoginReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.auth.request.LoginSmsReq;
import com.sankuai.shangou.seashop.user.thrift.auth.request.RefreshTokenReq;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

@Component
@FeignClient(name = "himall-base", contextId = "LoginUserFeign", url = "${himall-base.dev.url:}", path = "/himall-base/login")
public interface LoginUserFeign {
    @PostMapping(value = "/doLogin", consumes = "application/json")
    ResultDto<LoginResp> login(@RequestBody LoginReq loginReq);


    /**
     * 发送登录短信
     *
     * @return 短信发送结果
     */
    @PostMapping(value = "/sendLoginSms", consumes = "application/json")
     ResultDto<BaseResp> sendLoginSms(@RequestBody LoginSmsReq loginSms);

    /**
     * 小程序动态令牌换取手机号
     *
     * @param code
     * @return
     */
    @GetMapping(value = "/getWxUserPhone", consumes = "application/json")
    ResultDto<String> getWxUserPhone(@RequestParam(value = "code", required = false) String code);

    /**
     * 退出，根据登录平台类型退出
     * 需要携带token退出
     *
     * @return
     */
    @PostMapping(value = "/logout", consumes = "application/json")
    ResultDto<BaseResp> logout(@RequestBody TokenCache tokenCache);

    //刷新token
    @PostMapping(value = "/refreshToken", consumes = "application/json")
    ResultDto<LoginResp> refreshToken(@RequestBody RefreshTokenReq refreshTokenReq);
}
