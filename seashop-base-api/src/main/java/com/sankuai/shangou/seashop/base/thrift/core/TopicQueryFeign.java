package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWapTopicQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseShopReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseTopicRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWapTopicRes;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@FeignClient(name = "himall-base",contextId = "TopicQueryFeign",  url = "${himall-base.dev.url:}", path = "/himall-base/topic")
public interface TopicQueryFeign {

    /**
     * 获取专题列表（分页）
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "query", consumes = "application/json")
    ResultDto<BasePageResp<BaseTopicRes>> query(@RequestBody BaseTopicQueryReq query) throws TException;

    /**
     * 查询平台推荐的专题页
     * @return
     * @throws TException
     */
    @GetMapping(value = "queryRecommend")
    ResultDto<List<BaseTopicRes>> queryRecommend() throws TException;

    /**
     * 获取专题页详情
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "getById", consumes = "application/json")
    ResultDto<BaseTopicRes> getById(@RequestBody BaseShopReq query) throws TException;

    /**
     * 获取移动端专题页详情
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "getWapTopicById", consumes = "application/json")
    ResultDto<BaseWapTopicRes> getWapTopicById(@RequestBody BaseWapTopicQueryReq query) throws TException;

    /**
     * 获取PC首页详情
     * @return
     * @throws TException
     */
    @GetMapping(value = "getPCIndex")
    ResultDto<BaseWapTopicRes> getPCIndex() throws TException;

    /**
     * 获取平台PC首页
     * @return
     * @throws TException
     */
    @GetMapping(value = "getPlatIndex")
    ResultDto<String> getPlatIndex() throws TException;

    /**
     * 获取平台PC首页
     * @return
     * @throws TException
     */
    @GetMapping(value = "getPlatWapIndex")
    ResultDto<String> getPlatWapIndex() throws TException;


    /**
     * 获取供应商小程序首页
     * @param shopId
     * @return
     * @throws TException
     */
    @GetMapping(value = "getSellerIndex")
    ResultDto<String> getSellerIndex(@RequestParam Long shopId) throws TException;


    /**
     * 获取供应商PC首页
     * @param shopId
     * @return
     * @throws TException
     */
    @GetMapping(value = "getSellerPCIndex")
    ResultDto<String> getSellerPCIndex(@RequestParam Long shopId) throws TException;

    /**
     * 获取小程序首页
     * @return
     * @throws TException
     */
    @GetMapping(value = "getWapIndex")
    ResultDto<String> getWapIndex() throws TException;


}
