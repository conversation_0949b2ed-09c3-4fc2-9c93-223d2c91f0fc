package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/23/023
 * @description:
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryFavoriteCountListResp extends BaseParamReq {

    /**
     * 店铺收藏数量列表
     */
    private List<QueryFavoriteCountResp> list;
}
