package com.sankuai.shangou.seashop.base.thrift.core;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.dto.QueryExpressCompanyDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryBatchExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryErpExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryExpressCompanyPageReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryExpressCompanyReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressSiteSettingResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyPageResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryExpressCompanyResp;

@FeignClient(name = "himall-base",contextId = "ExpressQueryFeign",url = "${himall-base.dev.url:}",  path = "/himall-base/express")
public interface ExpressQueryFeign {

    /**
     * 平台-交易-快递设置-快递公司管理(查询列表)
     * @param queryExpressCompanyReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryExpressCompanyList",consumes = "application/json")
    ResultDto<QueryExpressCompanyResp> queryExpressCompanyList(@RequestBody QueryExpressCompanyReq queryExpressCompanyReq) throws TException;


    /**
     * 三方批量查询快递公司
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryBatchExpressCompany",consumes = "application/json")
    ResultDto<QueryExpressCompanyResp> queryBatchExpressCompany(@RequestBody QueryBatchExpressCompanyReq req) throws TException;

    /**
     * erp三方快递编号查询快递公司信息
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryExpressByThirdCode",consumes = "application/json")
    ResultDto<QueryExpressCompanyDto> queryExpressByThirdCode(@RequestBody QueryErpExpressCompanyReq req) throws TException;

    /**
     * 平台-交易-快递设置-物流设置（美团物流）查询
     * @return
     * @throws TException
     */
    @GetMapping(value = "/queryExpressSiteSetting")
    ResultDto<ExpressSiteSettingResp> queryExpressSiteSetting() throws TException ;

    /**
     * 查询快递公司列表
     *
     * @param req 入参
     * @return 快递公司列表
     * @throws TException
     */
    @PostMapping(value = "/queryExpressCompanyPage", consumes = "application/json")
    ResultDto<QueryExpressCompanyPageResp> queryExpressCompanyPage(@RequestBody QueryExpressCompanyPageReq req) throws TException;
}
