package com.sankuai.shangou.seashop.user.thrift.shop.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 供应商入驻协议请求入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdAgreementReq {
    /**
     * 是否同意协议
     */
    private Boolean agree;
    /**
     * 业务类型 1个人供应商 0企业供应商
     */
    private Integer businessType;
    /**
     * 用户id
     */
    private Long userId;
}
