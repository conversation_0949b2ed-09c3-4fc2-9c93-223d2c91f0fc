package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.JobLogInfoResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-base",contextId = "JobQueryFeign", path = "/himall-base/job", url = "${himall-base.dev.url:}")
public interface JobQueryFeign {




    @PostMapping(value = "/getJobLog", consumes = "application/json")
    ResultDto<JobLogInfoResp> getJobLog(@RequestBody BaseReq query) ;
}
