package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress.AddShippingAddressReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress.DeleteShippingAddressReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress.UpdateShippingAddressReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = "himall-base", contextId = "ShippingAddressCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/shippingAddress")
public interface ShippingAddressCmdFeign {

    /**
     * 保存收货地址
     *
     * @param addReq 收货地址信息
     * @return 保存成功后的收货地址ID
     */
    @PostMapping(value = "/addAddress", consumes = "application/json")
    ResultDto<Long> addAddress(@RequestBody AddShippingAddressReq addReq) throws TException;

    /**
     * 修改收货地址
     *
     * @param updateReq 收货地址信息
     * @return 保存成功后的收货地址ID
     */
    @PostMapping(value = "/updateAddress", consumes = "application/json")
    ResultDto<Boolean> updateAddress(@RequestBody UpdateShippingAddressReq updateReq) throws TException;

    /**
     * 删除收货地址
     *
     * @param deleteReq 待删除的收货地址ID
     * @return 保存成功后的收货地址ID
     */
    @PostMapping(value = "/deleteAddress", consumes = "application/json")
    ResultDto<Boolean> deleteAddress(@RequestBody DeleteShippingAddressReq deleteReq) throws TException;

}
