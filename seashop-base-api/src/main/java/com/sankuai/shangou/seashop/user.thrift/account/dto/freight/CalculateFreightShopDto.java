package com.sankuai.shangou.seashop.user.thrift.account.dto.freight;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CalculateFreightShopDto extends BaseParamReq {


    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺商品总金额
     */
    private BigDecimal totalAmount;
    /**
     * 店铺下的商品列表
     */
    private List<CalculateFreightProductDto> productList;

}
