package com.sankuai.shangou.seashop.user.thrift.shop.request;

import cn.hutool.core.collection.CollectionUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import java.util.List;

/**
 * 通过token+类型查询 入参
 *
 * <AUTHOR>
 * @date 2023/12/08 15:05
 */
public class QueryShopErpByJstReq extends BaseParamReq {

    /**
     * jst 店铺id
     */
    private List<String> jstShopIds;


    public void checkParameter() {
        if (CollectionUtil.isEmpty(jstShopIds)) {
            throw new IllegalArgumentException("jstShopIds不能为空");
        }
    }

    public List<String> getJstShopIds() {
        return jstShopIds;
    }

    public void setJstShopIds(List<String> jstShopIds) {
        this.jstShopIds = jstShopIds;
    }
}
