package com.sankuai.shangou.seashop.user.thrift.shop.enums;

/**
 * @description: 客户端类型枚举 1PC 2小程序
 * @author: LXH
 **/
public enum ClientTypeEnum {
    PC(1, "PC"),
    MINI_PROGRAM(2, "小程序");

    private final Integer value;
    private final String desc;


    ClientTypeEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

}
