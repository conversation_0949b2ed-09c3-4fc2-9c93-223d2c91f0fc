package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryShopShipperRespDto;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @description：查询供应商发/退货地址表返参
 * @author： liweisong
 * @create： 2023/11/27 9:59
 */
@ToString
@Data
public class QueryShopShipperResp extends BaseParamReq {

    /**
     * 供应商发/退货地址表集合
     */
    private List<QueryShopShipperRespDto> shopShipperRespDtoList;
}
