package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/02/21 9:50
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryFavoriteProductStatusResp extends BaseParamReq {
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商品ID
     */
    private Long productId;

    /**
     * 收藏状态 true:已收藏 false:未收藏
     */
    private Boolean favoriteStatus;
}
