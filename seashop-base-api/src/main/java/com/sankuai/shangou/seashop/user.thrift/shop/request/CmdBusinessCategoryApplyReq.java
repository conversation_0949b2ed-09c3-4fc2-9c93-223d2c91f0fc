package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.*;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class CmdBusinessCategoryApplyReq extends BaseParamReq {
    @NotNull(message = "店铺ID为必填项")
    private Long shopId;
    @NotNull(message = "申请记录ID为必填项")
    private Long applyId;
    @NotNull(message = "状态为必填项")
    private Integer status;
    /**
     * 拒绝原因
     */
    private String refuseReason;
}
