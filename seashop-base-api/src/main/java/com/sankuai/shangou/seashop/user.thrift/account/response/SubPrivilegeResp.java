package com.sankuai.shangou.seashop.user.thrift.account.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SubPrivilegeResp {
    /**
     * 主键
     */
    private Long id;

    /**
     * 父级ID
     */
    private Long parentId;

    /**
     * 名称
     */
    private String name;

    /**
     * 链接
     */
    private String url;

    /**
     * 排序
     */
    private Integer displaySequence;

    /**
     * 是否选择
     */
    private Boolean hasSelect;

    /**
     * 图标
     */
    private String icon;

    /**
     * 子页面权限
     */
    private List<SubpagesPrivilegeResp> subList;
}
