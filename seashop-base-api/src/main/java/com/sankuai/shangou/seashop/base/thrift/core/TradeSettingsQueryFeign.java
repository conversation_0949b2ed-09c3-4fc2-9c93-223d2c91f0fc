package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.response.TradeSiteSettingsResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @author： liweisong
 * @create： 2023/11/22 16:51
 */
@FeignClient(name = "himall-base", contextId = "TradeSettingsQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/tradeSettings")
public interface TradeSettingsQueryFeign {

    /**
     * 平台-交易-交易设置-交易参数（查询）
     * @return
     * @throws TException
     */
    @GetMapping(value = "/queryTradeSiteSetting")
    ResultDto<TradeSiteSettingsResp> queryTradeSiteSetting() throws TException;
}
