package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopIntroductionResp extends BaseThriftDto {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * 店铺logo
     */
    private String logo;

    /**
     * 商品评分
     */
    private BigDecimal packMark;

    /**
     * 服务评分
     */
    private BigDecimal serviceMark;

    /**
     * 综合评分
     */
    private BigDecimal comprehensiveMark;

    /**
     * 二维码
     */
    private String qrCode;
}
