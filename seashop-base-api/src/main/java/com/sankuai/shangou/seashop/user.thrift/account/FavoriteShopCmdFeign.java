package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.FavoriteShopCmdReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 收藏店铺相关操作服务
 */
@FeignClient(name = "himall-base", contextId = "FavoriteShopCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/favoriteShop")
public interface FavoriteShopCmdFeign {

    /**
     * 收藏店铺
     *
     * @param req 店铺id
     * @return 收藏店铺结果
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/addFavoriteShop", consumes = "application/json")
    ResultDto<BaseResp> addFavoriteShop(@RequestBody FavoriteShopCmdReq req) throws TException;

    /**
     * 取消收藏店铺
     *
     * @param req 店铺id
     * @return 取消收藏店铺结果
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/deleteFavoriteShop", consumes = "application/json")
    ResultDto<BaseResp> deleteFavoriteShop(@RequestBody FavoriteShopCmdReq req) throws TException;


}