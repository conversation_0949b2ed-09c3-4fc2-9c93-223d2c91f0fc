package com.sankuai.shangou.seashop.user.thrift.shop.request;

import lombok.Data;
import lombok.ToString;


/**
 * @description：查询供应商发/退货地址表入参
 * @author： liweisong
 * @create： 2023/11/27 9:53
 */
@ToString
@Data
public class QueryShopShipperReq {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 商家编号
     */
    private Long shopId;

    /**
     * 是否为默认发货地址
     */
    private Boolean defaultSendGoodsFlag;

    /**
     * 是否默认收货地址
     */
    private Boolean defaultGetGoodsFlag;


    public void checkParameter(){
        if(shopId == null){
            throw new IllegalArgumentException("shopId不能为空");
        }
    }

}
