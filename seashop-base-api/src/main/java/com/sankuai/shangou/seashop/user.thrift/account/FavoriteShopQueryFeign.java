package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.FavoriteShopPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryFavoriteCountReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.FavoriteShopRes;
import com.sankuai.shangou.seashop.user.thrift.account.response.QueryFavoriteCountListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 收藏店铺相关查询服务
 */
@FeignClient(name = "himall-base", contextId = "FavoriteShopQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/favoriteShop")
public interface FavoriteShopQueryFeign {

    /**
     * 查询收藏店铺
     *
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryPage", consumes = "application/json")
    ResultDto<BasePageResp<FavoriteShopRes>> queryPage(@RequestBody FavoriteShopPageReq req) throws TException;

    /**
     * 查询店铺收藏数量
     * 如果传入了memberId查询改用户是否收藏该店铺
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryCount", consumes = "application/json")
    ResultDto<QueryFavoriteCountListResp> queryCount(@RequestBody QueryFavoriteCountReq request) throws TException;
}
