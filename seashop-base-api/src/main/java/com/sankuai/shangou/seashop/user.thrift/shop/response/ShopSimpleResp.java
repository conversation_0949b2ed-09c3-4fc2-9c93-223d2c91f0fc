package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopSimpleResp extends BaseThriftDto {
    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 等级名称
     */
    private String gradeName;

    /**
     * 等级id
     */
    private Long gradeId;

    /**
     * 最大保证金
     */
    private BigDecimal maxCashDeposit;

    /**
     * 是否需要续签
     */
    private Boolean needRenew;

    /**
     * 是否是官方自营店
     */
    private Boolean whetherSelf;
}
