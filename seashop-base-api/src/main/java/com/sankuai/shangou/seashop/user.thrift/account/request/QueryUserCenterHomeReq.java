package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/12/12 16:03
 */
@Data
public class QueryUserCenterHomeReq extends BaseParamReq {

    /**
     * 商家ID
     */
    private Long userId;

    /**
     * 账号
     */
    private String userName;

    /**
     * 最近浏览的四个商品ID
     */
    private List<String> productIds;

    public void checkParameter(){
        if(userId == null){
            throw new IllegalArgumentException("userId不能为空");
        }
    }
}
