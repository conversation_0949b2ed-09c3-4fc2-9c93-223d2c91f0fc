package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CheckShopInfoResp extends BaseParamReq {

    /**
     * 是否需要提示需要签署合同
     */
    private boolean needSignContract;

    /**
     * 待签署合同的申请ID
     */
    private Long applyId;

    /**
     * 是否需要提示需要续签合同
     */
    private boolean needAgainSignContract;
}
