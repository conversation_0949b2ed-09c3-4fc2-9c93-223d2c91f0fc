package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description：修改供应商发/退货地址表入参
 * @author： liweisong
 * @create： 2023/11/27 9:22
 */
@ToString
@Data
public class UpdateShopShipperReq extends BaseParamReq {

    /**
     * 主键ID
     */
    @PrimaryField
    private Long id;

    /**
     * 商家编号
     */
    @ExaminField(description = "商家编号")
    private Long shopId;

    /**
     * 发货人姓名
     */
    @ExaminField(description = "发货人姓名")
    private String shipperName;

    /**
     * 发货人电话
     */
    @ExaminField(description = "发货人电话")
    private String telPhone;

    /**
     * 发货人地址
     */
    @ExaminField(description = "发货人地址")
    private String address;

    /**
     * 区域ID
     */
    @ExaminField(description = "区域ID")
    private Integer regionId;

    /**
     * 经度
     */
    @ExaminField(description = "经度")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ExaminField(description = "纬度")
    private BigDecimal latitude;

    /**
     * 创建时间
     */
    @ExaminField(description = "创建时间")
    private Date createTime;

    /**
     * 发货点名称
     */
    @ExaminField(description = "发货点名称")
    private String shipperTag;

    public void checkParameter() {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (shopId == null) {
            throw new IllegalArgumentException("shopId不能为空");
        }
        if (shipperName == null) {
            throw new IllegalArgumentException("shipperName发货人姓名不能为空");
        }
        if (telPhone == null) {
            throw new IllegalArgumentException("telPhone发货人电话不能为空");
        } else {
            String regex = "^(1[3456789]\\d{9})$";
            if (!telPhone.matches(regex)) {
                throw new IllegalArgumentException("手机号码格式不正确");
            }
        }
        if (address == null) {
            throw new IllegalArgumentException("address发货人地址不能为空");
        }
        if (regionId == null) {
            throw new IllegalArgumentException("regionId区域ID不能为空");
        }
    }
}
