package com.sankuai.shangou.seashop.user.thrift.account.request;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.user.thrift.account.dto.freight.CalculateFreightShopDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CalculateFreightReq extends BaseParamReq {

    /**
     * 区域ID
     */
    private Integer regionId;
    /**
     * 区域全路径，逗号隔开
     */
    private String regionPath;
    /**
     * 店铺列表，店铺对象包含商品列表
     */
    private List<CalculateFreightShopDto> shopList;
    /**
     * 是否忽略禁售区域商品
     * <p>这个标识的作用是：第一次进入订单预览页和后续其他操作都需要计算运费，但是第一次进入页面时，
     * 如果收货地址在禁售区域也应该显示数据，允许用户调整收货地址。所以第一次进入预览页这个标识设置为true，此时不报错而是忽略禁售区域的商品
     * 提交订单和其他接口要计算运费时，这个标识设置为false或者不设置，此时在禁售区域会抛出异常</p>
     */
    private Boolean ignoreForbiddenArea;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(StrUtil.isEmpty(regionPath), "收货地址不能为空");
    }
}
