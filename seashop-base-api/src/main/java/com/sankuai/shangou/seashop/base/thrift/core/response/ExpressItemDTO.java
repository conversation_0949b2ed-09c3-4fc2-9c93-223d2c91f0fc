package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@Data
public class ExpressItemDTO extends BaseThriftDto {
    /**
     * 进度项所在时间
     */
    @JsonProperty("acceptTime")
    private String time;
    /**
     * 进度项描述
     */
    @JsonProperty("acceptStation")
    private String context;
    /**
     * 进度项状态描述
     */
    private String statusDesc;
    /**
     * 所在城市
     */
    @JsonProperty("location")
    private String location;
    /**
     * 进度项状态码
     */
    private int status;
}
