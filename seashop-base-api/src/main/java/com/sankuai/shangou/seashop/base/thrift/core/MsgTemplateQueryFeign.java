package com.sankuai.shangou.seashop.base.thrift.core;


import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryAppletTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryMsgTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryWxAppletFormDataReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMsgTemplateResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryMsgTemplateResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryWxAppletFormDataResp;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/29 16:54
 */
@FeignClient(name = "himall-base", contextId = "MsgTemplateQueryFeign",url = "${himall-base.dev.url:}",  path = "/himall-base/msgTemplate")
public interface MsgTemplateQueryFeign {

    /**
     * 平台-小程序-消息配置-查询
     * @param queryMsgTemplateReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryMsgTemplate", consumes = "application/json")
    ResultDto<QueryMsgTemplateResp> queryMsgTemplate(@RequestBody QueryMsgTemplateReq queryMsgTemplateReq) throws TException;

    /**
     * 商城小程序微信通知
     * @param queryAppletTemplateReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryAppletTemplate", consumes = "application/json")
    ResultDto<List<BaseMsgTemplateResp>> queryAppletTemplate(@RequestBody QueryAppletTemplateReq queryAppletTemplateReq) throws TException;

    /**
     * 订单模版绑定的事件表
     * @param queryWxAppletFormDataReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryWxAppletFormData", consumes = "application/json")
    ResultDto<List<QueryWxAppletFormDataResp>> queryWxAppletFormData(@RequestBody QueryWxAppletFormDataReq queryWxAppletFormDataReq) throws TException;

    /**
     * 获取小程序token
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getAppletResetToken")
    ResultDto<String> getAppletResetToken() throws TException;
}
