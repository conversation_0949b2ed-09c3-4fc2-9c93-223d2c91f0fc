package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicJsonFileReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseTopicReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.TemplatePageIndexReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseShopReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-base", contextId = "TopicCMDFeign", url = "${himall-base.dev.url:}", path = "/himall-base/topic")
public interface TopicCMDFeign {
    /**
     * 新增专题页
     *
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/create", consumes = "application/json")
    ResultDto<Long> create(@RequestBody BaseTopicReq query) throws TException;

    /**
     * 修改专题页
     *
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/update", consumes = "application/json")
    ResultDto<Boolean> update(@RequestBody BaseTopicReq query) throws TException;

    /**
     * 删除专题页
     *
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/delete", consumes = "application/json")
    ResultDto<Boolean> delete(@RequestBody BaseShopReq req) throws TException;

    /**
     * 设置为首页
     *
     * @param shopReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/setHome", consumes = "application/json")
    ResultDto<Boolean> setHome(@RequestBody BaseShopReq shopReq) throws TException;

    /**
     * 上传json模板
     *
     * @param jsonFileReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/uploadTemplate", consumes = "application/json")
    ResultDto<String> uploadTemplate(@RequestBody BaseTopicJsonFileReq jsonFileReq) throws TException;

    /**
     * 编辑供应商首页
     *
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/editSellerIndex", consumes = "application/json")
    ResultDto<Boolean> editSellerIndex(@RequestBody TemplatePageIndexReq req) throws TException;
}
