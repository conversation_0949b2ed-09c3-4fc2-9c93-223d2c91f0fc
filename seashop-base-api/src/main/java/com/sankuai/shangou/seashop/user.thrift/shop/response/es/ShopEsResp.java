package com.sankuai.shangou.seashop.user.thrift.shop.response.es;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopEsResp extends BaseThriftDto {

    /**
     * 店铺id
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺logo
     */
    private String logo;

    /**
     * 店铺状态
     */
    private Integer shopStatus;

    /**
     * 是否开启专享会员
     */
    private Boolean whetherOpenExclusiveMember;

    /**
     * 订单销量
     */
    private Long orderSaleCount;

    /**
     * 商品销量
     */
    private Long productSaleCount;

    /**
     * 商品真实销量
     */
    private Long productRealSaleCount;

    /**
     * 商品虚拟销量
     */
    private Long productVirtualSaleCount;

    /**
     * 标记数量
     */
    private Long markCount;

    /**
     * 包装标记
     */
    private BigDecimal packMark;

    /**
     * 服务标记
     */
    private BigDecimal serviceMark;

    /**
     * 综合标记
     */
    private BigDecimal comprehensiveMark;
}
