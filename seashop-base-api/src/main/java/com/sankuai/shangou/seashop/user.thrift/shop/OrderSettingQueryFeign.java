package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBatchOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryOrderSettingResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @description：卖家中心，交易设置
 * @author： liweisong
 * @create： 2023/11/27 16:02
 */
@FeignClient(name = "himall-base", contextId = "OrderSettingQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/orderSetting")
public interface OrderSettingQueryFeign {

    /**
     * 供应商-交易-交易设置(查询)
     *
     * @param queryOrderSettingReq 查询请求入参
     * @return 查询结果
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/queryOrderSetting", consumes = "application/json")
    ResultDto<QueryOrderSettingResp> queryOrderSetting(@RequestBody QueryOrderSettingReq queryOrderSettingReq) throws TException;

    /**
     * 供应商-交易-交易设置(批量查询)
     *
     * @param queryBatchOrderSettingReq 查询请求入参
     * @return 查询结果
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/queryBatchOrderSetting", consumes = "application/json")
    ResultDto<List<QueryOrderSettingResp>> queryBatchOrderSetting(@RequestBody QueryBatchOrderSettingReq queryBatchOrderSettingReq) throws TException;
}
