package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopErpReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author： liweisong
 * @create： 2023/11/28 14:55
 */
@FeignClient(name = "himall-base", contextId = "ShopErpCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/shopErp")
public interface ShopErpCmdFeign {

    /**
     * 供应商后台-店铺-erp管理(保存)
     *
     * @param saveShopErpReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveShopErp", consumes = "application/json")
    ResultDto<BaseResp> saveShopErp(@RequestBody SaveShopErpReq saveShopErpReq) throws TException;
}
