package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * @description：删除供应商发/退货地址表入参
 * @author： liweisong
 * @create： 2023/11/27 9:23
 */
@ToString
@Data
public class DeleteShopShipperReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    @ExaminField(description = "商家编号")
    private Long shopId;

    public void checkParameter(){
        if(id == null){
            throw new IllegalArgumentException("主键不能为空");
        }
        if(shopId == null){
            throw new IllegalArgumentException("shopId不能为空");
        }
    }
}
