package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryApplyCategoryAndFormDataReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyDetailReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryWaitFinishContractNumReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryLastCategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.WaitFinishCategoryApplyResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 类目申请接口
 */
@FeignClient(name = "himall-base", contextId = "BusinessCategoryApplyQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/businessCategoryApply")
public interface BusinessCategoryApplyQueryFeign {
    /**
     * 分页查询类目申请(平台端)
     * @param queryBusinessCategoryApplyPageReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryPage", consumes = "application/json")
    ResultDto<BasePageResp<BusinessCategoryApplyResp>> queryPage(@RequestBody QueryBusinessCategoryApplyPageReq queryBusinessCategoryApplyPageReq) throws TException;

    /**
     * 分页查询类目申请(供应商端)
     * @param queryBusinessCategoryApplyPageReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryPageForSeller", consumes = "application/json")
    ResultDto<BasePageResp<BusinessCategoryApplyResp>> queryPageForSeller(@RequestBody QueryBusinessCategoryApplyPageReq queryBusinessCategoryApplyPageReq) throws TException;

    /**
     * 查询类目申请详情(平台端)
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryDetail", consumes = "application/json")
    ResultDto<BusinessCategoryApplyDetailResp> queryDetail(@RequestBody QueryBusinessCategoryApplyDetailReq request) throws TException;

    /**
     * 查询类目申请详情(供应商)
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryDetailForSeller", consumes = "application/json")
    ResultDto<BusinessCategoryApplyDetailResp> queryDetailForSeller(@RequestBody QueryBusinessCategoryApplyDetailReq request) throws TException;

    /**
     * 查询待审核和待签署合同的申请数
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryWaitFinishContractNum", consumes = "application/json")
    ResultDto<WaitFinishCategoryApplyResp> queryWaitFinishContractNum(@RequestBody QueryWaitFinishContractNumReq request) throws TException;

    /**
     * 获取申请的类目信息和需要补充的表单信息
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryApplyCategoryAndFormData", consumes = "application/json")
    ResultDto<QueryLastCategoryResp> queryApplyCategoryAndFormData(@RequestBody QueryApplyCategoryAndFormDataReq request) throws TException;
}
