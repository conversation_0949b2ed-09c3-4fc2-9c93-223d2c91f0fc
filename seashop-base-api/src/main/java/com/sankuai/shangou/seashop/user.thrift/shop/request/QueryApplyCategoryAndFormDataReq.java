package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * <AUTHOR>
 * @date 2023/11/30 8:38
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class QueryApplyCategoryAndFormDataReq extends BaseParamReq {

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 店铺id
     */
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(categoryId == null || categoryId <= 0, "请选择一个类目");
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "请选择一个店铺");
    }
}
