package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class ResetPasswordReq extends BaseParamReq {

    /**
     * 手机号
     */
    private String phone;

    /**
     * 新密码
     */
    private String newPassword;

    /**
     * 确认密码
     */
    private String confirmPassword;

    public ResetPasswordReq() {
    }

    public void checkParameter() {
        AssertUtil.throwIfTrue(StringUtils.isBlank(phone), "手机号不能为空");
        AssertUtil.throwIfTrue(StringUtils.isBlank(newPassword), "密码不能为空");
        AssertUtil.throwIfTrue(StringUtils.isBlank(confirmPassword), "确认密码不能为空");
        //确认密码和新密码一致
        AssertUtil.throwIfTrue(!newPassword.equals(confirmPassword), "确认密码和新密码不一致");
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getNewPassword() {
        return newPassword;
    }

    public void setNewPassword(String newPassword) {
        this.newPassword = newPassword;
    }

    public String getConfirmPassword() {
        return confirmPassword;
    }

    public void setConfirmPassword(String confirmPassword) {
        this.confirmPassword = confirmPassword;
    }
}
