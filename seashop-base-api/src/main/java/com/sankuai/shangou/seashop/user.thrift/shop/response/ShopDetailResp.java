package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.user.thrift.shop.group.AddCompanyGroup;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @description: 供应商详情返回参数
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopDetailResp extends BaseThriftDto {

    /**
     * 供应商ID
     */
    private Long id;

    /**
     * 供应商等级
     */
    private Long gradeId;

    /**
     * 供应商名称
     */
    private String shopName;

    /**
     * 店铺状态
     */
    private Integer shopStatus;

    /**
     * 汇付操作状态
     */
    private Integer adapayStatus;

    /**
     * 汇付操作拒绝原因
     */
    private String adapayReason;

    /**
     * 平台审核状态
     */
    private Integer plateStatus;

    /**
     * 平台审核拒绝原因
     */
    private String refuseReason;

    /**
     * 店铺创建时间
     */
    private Date createDate;

    /**
     * 店铺过期时间
     */
    private Date endDate;

    /**
     * 身份证号码
     */
    private String idCard;
    /**
     * 身份证正面
     */
    private String idCardUrl;
    /**
     * 身份证反面
     */
    private String idCardUrl2;

    /**
     * 身份证有效期类型
     */
    private Integer idCardExpireType;

    /**
     * 身份证有效期开始
     */
    private Date idCardStartDate;
    /**
     * 身份证有效期结束
     */
    private Date idCardEndDate;

    /**
     * 联系人姓名
     */
    private String contactsName;
    /**
     * 联系人电话
     */
    private String contactsPhone;
    /**
     * 联系人Email
     */
    private String contactsEmail;
    /**
     * 银行开户名
     */
    private String bankAccountName;
    /**
     * 银行账号
     */
    private String bankAccountNumber;
    /**
     * 银行名称
     */
    private String bankName;
    /**
     * 支行联行号
     */
    private String bankCode;
    /**
     * 开户银行所在地
     */
    private Integer bankRegionId;
    /**
     * 开户银行类型（1对公，2对私）
     */
    private Integer bankType;
    /**
     * 开户银行许可证
     */
    private String bankPhoto;
    /**
     * 商家发货人名称
     */
    private String senderName;
    /**
     * 商家发货人地址
     */
    private String senderAddress;
    /**
     * 商家发货人电话
     */
    private String senderPhone;
    /**
     * 是否缴纳保证金
     */
    private Boolean whetherPayBond;
    /**
     * 是否签署协议
     */
    private Boolean whetherAgreement;
    /**
     * 是否是需要补充资料
     */
    private Boolean whetherSupply;
    /**
     * 是否需要续签合同
     */
    private Boolean whetherAgainSign;
    /**
     * 是否官方自营
     */
    private Boolean whetherSelf;


    /**
     * 公司名称(个人姓名)
     */
    private String companyName;
    /**
     * 公司省市区(个人地址)
     */
    private Integer companyRegionId;
    /**
     * 公司地址(个人地址)
     */
    private String companyAddress;
    /**
     * 公司电话(个人电话)
     */
    private String companyPhone;
    /**
     * 公司员工数量
     */
    private Integer companyEmployeeCount;
    /**
     * 公司注册资金
     */
    private BigDecimal companyRegisteredCapital;
    /**
     * 营业执照号
     */
    private String businessLicenseNumber;
    /**
     * 营业执照
     */
    private String businessLicenseNumberPhoto;
    /**
     * 营业执照所在地
     */
    private Integer businessLicenseRegionId;
    /**
     * 营业执照有效期开始
     */
    private Date businessLicenseStart;
    /**
     * 营业执照有效期
     */
    private Date businessLicenseEnd;
    /**
     * 法定经营范围
     */
    private String businessSphere;
    /**
     * 组织机构代码
     */
    private String organizationCode;
    /**
     * 组织机构执照
     */
    private String organizationCodePhoto;
    /**
     * 税务登记证
     */
    private String taxRegistrationCertificate;
    /**
     * 税务登记证号
     */
    private String taxpayerId;
    /**
     * 纳税人识别号
     */
    private String taxRegistrationCertificatePhoto;
    /**
     * 法人代表
     */
    private String legalPerson;
    /**
     * 公司成立日期
     */
    private Date companyFoundingDate;
    /**
     * 公司类型
     */
    private String companyType;
    /**
     * 公司联系电话（用作公司售后电话）
     */
    private String contactPhone;
    /**
     * 执照地址
     */
    private String licenceCertAddr;
    /**
     * 财务负责人
     */
    private String financeChief;
    /**
     * 财务负责人联系方式
     */
    private String financeChiefPhone;
    /**
     * 服务商简介
     */
    private String introduct;
    /**
     * 经营类目
     */
    private List<BusinessCategoryResp> businessCategory;
    /**
     * 表单数据
     */
    private List<CategoryApplyFormDto> fieldList;
    /**
     * 业务类型 1个人供应商 0企业供应商
     */
    private Integer businessType;
    /**
     * 店铺账户
     */
    private String shopAccount;
    /**
     * 自定义数据
     */
    private String formData;
    /**
     * 注册步骤
     */
    private Integer stage;
    /**
     * 店铺类型 1商城店铺 2牵牛花店铺 3易九批店铺
     */
    private Integer shopType;
    /**
     * 管理员姓名
     */
    private String realName;
    /**
     * 管理员手机
     */
    private String memberPhone;
    /**
     * 管理员邮箱
     */
    private String memberEmail;
    /**
     * logo
     */
    private String logo;
    /**
     * 公司省市区名称
     */
    private String companyRegionName;
    /**
     * 开户银行所在地名称
     */
    private String bankRegionName;

    @FieldDoc(description = "省")
    private Long provinceId;
    @FieldDoc(description = "省")
    private String provinceName;
    @FieldDoc(description = "市")
    private Long cityId;
    @FieldDoc(description = "市")
    private String cityName;
    @FieldDoc(description = "区")
    private Long countyId;
    @FieldDoc(description = "区")
    private String countyName;
    @FieldDoc(description = "乡镇")
    private String townIds;
    @FieldDoc(description = "乡镇")
    private String townsNames;
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getGradeId() {
        return gradeId;
    }

    public void setGradeId(Long gradeId) {
        this.gradeId = gradeId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Integer getShopStatus() {
        return shopStatus;
    }

    public void setShopStatus(Integer shopStatus) {
        this.shopStatus = shopStatus;
    }

    public Integer getAdapayStatus() {
        return adapayStatus;
    }

    public void setAdapayStatus(Integer adapayStatus) {
        this.adapayStatus = adapayStatus;
    }

    public Integer getPlateStatus() {
        return plateStatus;
    }

    public void setPlateStatus(Integer plateStatus) {
        this.plateStatus = plateStatus;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getCreateDateLong() {
        return this.date2Long(this.createDate);
    }

    public void setCreateDateLong(Long createDate) {
        this.createDate = this.long2Date(createDate);
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Long getEndDateLong() {
        return this.date2Long(this.endDate);
    }

    public void setEndDateLong(Long endDate) {
        this.endDate = this.long2Date(endDate);
    }

    public String getContactsName() {
        return contactsName;
    }

    public void setContactsName(String contactsName) {
        this.contactsName = contactsName;
    }

    public String getContactsPhone() {
        return contactsPhone;
    }

    public void setContactsPhone(String contactsPhone) {
        this.contactsPhone = contactsPhone;
    }

    public String getContactsEmail() {
        return contactsEmail;
    }

    public void setContactsEmail(String contactsEmail) {
        this.contactsEmail = contactsEmail;
    }

    public String getBankAccountName() {
        return bankAccountName;
    }

    public void setBankAccountName(String bankAccountName) {
        this.bankAccountName = bankAccountName;
    }

    public String getBankAccountNumber() {
        return bankAccountNumber;
    }

    public void setBankAccountNumber(String bankAccountNumber) {
        this.bankAccountNumber = bankAccountNumber;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public Integer getBankRegionId() {
        return bankRegionId;
    }

    public void setBankRegionId(Integer bankRegionId) {
        this.bankRegionId = bankRegionId;
    }

    public Integer getBankType() {
        return bankType;
    }

    public void setBankType(Integer bankType) {
        this.bankType = bankType;
    }

    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    public String getSenderAddress() {
        return senderAddress;
    }

    public void setSenderAddress(String senderAddress) {
        this.senderAddress = senderAddress;
    }

    public String getSenderPhone() {
        return senderPhone;
    }

    public void setSenderPhone(String senderPhone) {
        this.senderPhone = senderPhone;
    }

    public Boolean getWhetherPayBond() {
        return whetherPayBond;
    }

    public void setWhetherPayBond(Boolean whetherPayBond) {
        this.whetherPayBond = whetherPayBond;
    }

    public Boolean getWhetherAgreement() {
        return whetherAgreement;
    }

    public void setWhetherAgreement(Boolean whetherAgreement) {
        this.whetherAgreement = whetherAgreement;
    }

    public Boolean getWhetherSupply() {
        return whetherSupply;
    }

    public void setWhetherSupply(Boolean whetherSupply) {
        this.whetherSupply = whetherSupply;
    }

    public Boolean getWhetherAgainSign() {
        return whetherAgainSign;
    }

    public void setWhetherAgainSign(Boolean whetherAgainSign) {
        this.whetherAgainSign = whetherAgainSign;
    }

    public Boolean getWhetherSelf() {
        return whetherSelf;
    }

    public void setWhetherSelf(Boolean whetherSelf) {
        this.whetherSelf = whetherSelf;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getCompanyRegionId() {
        return companyRegionId;
    }

    public void setCompanyRegionId(Integer companyRegionId) {
        this.companyRegionId = companyRegionId;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyPhone() {
        return companyPhone;
    }

    public void setCompanyPhone(String companyPhone) {
        this.companyPhone = companyPhone;
    }

    public Integer getCompanyEmployeeCount() {
        return companyEmployeeCount;
    }

    public void setCompanyEmployeeCount(Integer companyEmployeeCount) {
        this.companyEmployeeCount = companyEmployeeCount;
    }

    public BigDecimal getCompanyRegisteredCapital() {
        return companyRegisteredCapital;
    }

    public void setCompanyRegisteredCapital(BigDecimal companyRegisteredCapital) {
        this.companyRegisteredCapital = companyRegisteredCapital;
    }

    public String getCompanyRegisteredCapitalString() {
        return this.bigDecimal2String(this.companyRegisteredCapital);
    }

    public void setCompanyRegisteredCapitalString(String companyRegisteredCapital) {
        this.companyRegisteredCapital = this.string2BigDecimal(companyRegisteredCapital);
    }

    public String getBusinessLicenseNumber() {
        return businessLicenseNumber;
    }

    public void setBusinessLicenseNumber(String businessLicenseNumber) {
        this.businessLicenseNumber = businessLicenseNumber;
    }

    public String getBusinessLicenseNumberPhoto() {
        return businessLicenseNumberPhoto;
    }

    public void setBusinessLicenseNumberPhoto(String businessLicenseNumberPhoto) {
        this.businessLicenseNumberPhoto = businessLicenseNumberPhoto;
    }

    public Integer getBusinessLicenseRegionId() {
        return businessLicenseRegionId;
    }

    public void setBusinessLicenseRegionId(Integer businessLicenseRegionId) {
        this.businessLicenseRegionId = businessLicenseRegionId;
    }

    public Date getBusinessLicenseStart() {
        return businessLicenseStart;
    }

    public void setBusinessLicenseStart(Date businessLicenseStart) {
        this.businessLicenseStart = businessLicenseStart;
    }

    public Long getBusinessLicenseStartLong() {
        return this.date2Long(this.businessLicenseStart);
    }

    public void setBusinessLicenseStartLong(Long businessLicenseStart) {
        this.businessLicenseStart = this.long2Date(businessLicenseStart);
    }

    public Date getBusinessLicenseEnd() {
        return businessLicenseEnd;
    }

    public void setBusinessLicenseEnd(Date businessLicenseEnd) {
        this.businessLicenseEnd = businessLicenseEnd;
    }

    public Long getBusinessLicenseEndLong() {
        return this.date2Long(this.businessLicenseEnd);
    }

    public void setBusinessLicenseEndLong(Long businessLicenseEnd) {
        this.businessLicenseEnd = this.long2Date(businessLicenseEnd);
    }

    public String getBusinessSphere() {
        return businessSphere;
    }

    public void setBusinessSphere(String businessSphere) {
        this.businessSphere = businessSphere;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

    public String getOrganizationCodePhoto() {
        return organizationCodePhoto;
    }

    public void setOrganizationCodePhoto(String organizationCodePhoto) {
        this.organizationCodePhoto = organizationCodePhoto;
    }

    public String getTaxRegistrationCertificate() {
        return taxRegistrationCertificate;
    }

    public void setTaxRegistrationCertificate(String taxRegistrationCertificate) {
        this.taxRegistrationCertificate = taxRegistrationCertificate;
    }

    public String getTaxpayerId() {
        return taxpayerId;
    }

    public void setTaxpayerId(String taxpayerId) {
        this.taxpayerId = taxpayerId;
    }

    public String getTaxRegistrationCertificatePhoto() {
        return taxRegistrationCertificatePhoto;
    }

    public void setTaxRegistrationCertificatePhoto(String taxRegistrationCertificatePhoto) {
        this.taxRegistrationCertificatePhoto = taxRegistrationCertificatePhoto;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public Date getCompanyFoundingDate() {
        return companyFoundingDate;
    }

    public void setCompanyFoundingDate(Date companyFoundingDate) {
        this.companyFoundingDate = companyFoundingDate;
    }

    public Long getCompanyFoundingDateLong() {
        return this.date2Long(this.companyFoundingDate);
    }

    public void setCompanyFoundingDateLong(Long companyFoundingDate) {
        this.companyFoundingDate = this.long2Date(companyFoundingDate);
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getContactPhone() {
        return contactPhone;
    }

    public void setContactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
    }

    public String getLicenceCertAddr() {
        return licenceCertAddr;
    }

    public void setLicenceCertAddr(String licenceCertAddr) {
        this.licenceCertAddr = licenceCertAddr;
    }

    public String getFinanceChief() {
        return financeChief;
    }

    public void setFinanceChief(String financeChief) {
        this.financeChief = financeChief;
    }

    public String getFinanceChiefPhone() {
        return financeChiefPhone;
    }

    public void setFinanceChiefPhone(String financeChiefPhone) {
        this.financeChiefPhone = financeChiefPhone;
    }

    public String getIntroduct() {
        return introduct;
    }

    public void setIntroduct(String introduct) {
        this.introduct = introduct;
    }


    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getIdCardUrl() {
        return idCardUrl;
    }

    public void setIdCardUrl(String idCardurl) {
        this.idCardUrl = idCardurl;
    }

    public String getIdCardUrl2() {
        return idCardUrl2;
    }

    public void setIdCardUrl2(String idCardurl2) {
        this.idCardUrl2 = idCardurl2;
    }

    public Date getIdCardStartDate() {
        return idCardStartDate;
    }

    public void setIdCardStartDate(Date idCardStartDate) {
        this.idCardStartDate = idCardStartDate;
    }

    public Long getIdCardStartDateLong() {
        return this.date2Long(this.idCardStartDate);
    }

    public void setIdCardStartDateLong(Long idCardStartDate) {
        this.idCardStartDate = this.long2Date(idCardStartDate);
    }

    public Date getIdCardEndDate() {
        return idCardEndDate;
    }

    public void setIdCardEndDate(Date idCardEndDate) {
        this.idCardEndDate = idCardEndDate;
    }

    public Long getIdCardEndDateLong() {
        return this.date2Long(this.idCardEndDate);
    }

    public void setIdCardEndDateLong(Long idCardEndDate) {
        this.idCardEndDate = this.long2Date(idCardEndDate);
    }

    public String getBankPhoto() {
        return bankPhoto;
    }

    public void setBankPhoto(String bankPhoto) {
        this.bankPhoto = bankPhoto;
    }

    public List<BusinessCategoryResp> getBusinessCategory() {
        return businessCategory;
    }

    public void setBusinessCategory(List<BusinessCategoryResp> businessCategory) {
        this.businessCategory = businessCategory;
    }

    public List<CategoryApplyFormDto> getFieldList() {
        return fieldList;
    }

    public void setFieldList(List<CategoryApplyFormDto> fieldList) {
        this.fieldList = fieldList;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getShopAccount() {
        return shopAccount;
    }

    public void setShopAccount(String shopAccount) {
        this.shopAccount = shopAccount;
    }

    public String getFormData() {
        return formData;
    }

    public void setFormData(String formData) {
        this.formData = formData;
    }

    public Integer getStage() {
        return stage;
    }

    public void setStage(Integer stage) {
        this.stage = stage;
    }


    public String getAdapayReason() {
        return adapayReason;
    }

    public void setAdapayReason(String adapayReason) {
        this.adapayReason = adapayReason;
    }

    public String getRefuseReason() {
        return refuseReason;
    }

    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }

    public Integer getShopType() {
        return shopType;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getMemberPhone() {
        return memberPhone;
    }

    public void setMemberPhone(String memberPhone) {
        this.memberPhone = memberPhone;
    }

    public String getMemberEmail() {
        return memberEmail;
    }

    public void setMemberEmail(String memberEmail) {
        this.memberEmail = memberEmail;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getCompanyRegionName() {
        return companyRegionName;
    }

    public void setCompanyRegionName(String companyRegionName) {
        this.companyRegionName = companyRegionName;
    }

    public String getBankRegionName() {
        return bankRegionName;
    }

    public void setBankRegionName(String bankRegionName) {
        this.bankRegionName = bankRegionName;
    }
}
