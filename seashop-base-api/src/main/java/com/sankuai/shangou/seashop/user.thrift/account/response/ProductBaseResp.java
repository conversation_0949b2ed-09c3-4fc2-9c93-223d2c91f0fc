package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @author： liweisong
 * @create： 2023/12/13 14:30
 */
@Data
public class ProductBaseResp extends BaseThriftDto {


    /**
     * 主键
     */
    private Long id;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品主图
     */
    private String imagePath;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 最小销售价(展示这个价格)
     */
    private BigDecimal salePrice;

    /**
     * 店铺ID
     */
    private Long shopId;
}
