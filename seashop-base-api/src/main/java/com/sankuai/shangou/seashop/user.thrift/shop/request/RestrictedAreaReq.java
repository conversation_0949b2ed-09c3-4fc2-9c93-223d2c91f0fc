package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RestrictedAreaReq extends BaseParamReq {
    /**
     * 主键
     */
    @PrimaryField
    private Long id;

    /**
     * 运费模板ID
     */
    @ExaminField(description = "运费模板ID")
    private Long templateId;

    /**
     * 地区ID
     */
    @ExaminField(description = "地区ID")
    private Integer regionId;

    /**
     * 地区全路径
     */
    @ExaminField(description = "地区全路径")
    private String regionPath;

    private Date createTime;

    private Date updateTime;

    /**
     * 省份ID
     */
    @ExaminField(description = "省份ID")
    private Long provinceId;

    /**
     * 城市ID
     */
    @ExaminField(description = "城市ID")
    private Long cityId;

    /**
     * 区ID
     */
    @ExaminField(description = "区ID")
    private Long countyId;

    /**
     * 乡镇
     */
    @ExaminField(description = "乡镇")
    private String townId;

    /**
     * 省份名称
     */
    @ExaminField(description = "省份名称")
    private String provinceName;

    /**
     * 城市名称
     */
    @ExaminField(description = "城市名称")
    private String cityName;

    /**
     * 区名称
     */
    @ExaminField(description = "区名称")
    private String countyName;

    /**
     * 乡镇的名称
     */
    @ExaminField(description = "乡镇的名称")
    private String townName;
}
