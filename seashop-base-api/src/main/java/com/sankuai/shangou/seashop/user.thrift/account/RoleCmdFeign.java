package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdRoleReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 权限组操作服务类
 */
@FeignClient(name = "himall-base", contextId = "RoleCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/role")
public interface RoleCmdFeign {

    /**
     * 添加权限组
     * @return 添加权限组入参
     */
    @PostMapping(value = "/addRole", consumes = "application/json")
    ResultDto<Long> addRole(@RequestBody CmdRoleReq cmdRoleReq) throws TException;


    /**
     * 编辑权限组
     * @return 编辑权限组入参
     */
    @PostMapping(value = "/editRole", consumes = "application/json")
    ResultDto<Long> editRole(@RequestBody CmdRoleReq cmdRoleReq) throws TException;

    /**
     * 删除权限组
     * @return 删除权限组入参
     */
    @PostMapping(value = "/deleteRole", consumes = "application/json")
    ResultDto<Long> deleteRole(@RequestBody CmdRoleReq cmdRoleReq) throws TException;
}
