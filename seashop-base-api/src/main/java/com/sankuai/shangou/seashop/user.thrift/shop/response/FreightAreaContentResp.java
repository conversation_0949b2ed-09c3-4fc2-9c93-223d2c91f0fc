package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.FreightAreaDetailReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class FreightAreaContentResp extends BaseParamReq {

    /**
     * 主键
     */
    private Long id;

    /**
     * 运费模板ID
     */
    private Long freightTemplateId;

    /**
     * 地区选择
     */
    private String areaContent;

    /**
     * 首笔单元计量
     */
    private Integer firstUnit;

    /**
     * 首笔单元费用
     */
    private BigDecimal firstUnitMonry;

    /**
     * 递增单元计量
     */
    private Integer accumulationUnit;

    /**
     * 递增单元费用
     */
    private BigDecimal accumulationUnitMoney;

    /**
     * 是否为默认
     */
    private Boolean whetherDefault;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 指定可配送区域的详细地址
     */
    private List<FreightAreaDetailReq> detailReqList;
}
