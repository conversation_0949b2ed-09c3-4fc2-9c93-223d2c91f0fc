package com.sankuai.shangou.seashop.user.thrift.shop.request;

import java.util.Date;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.group.AddCompanyGroup;
import com.sankuai.shangou.seashop.user.thrift.shop.group.AddPersonGroup;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdShopStepsOneReq extends BaseParamReq {

    /**
     * id
     */
    @PrimaryField
    private Long shopId;
    /**
     * 公司名称
     */
    @NotBlank(message = "公司名称不能为空", groups = {AddCompanyGroup.class})
    @Size(max = 60, message = "公司名称不能超过60个字符")
    @ExaminField(description = "公司名称")
    private String companyName;
    /**
     * 公司所在地
     */
    @NotNull(message = "公司所在地不能为空", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    @ExaminField(description = "公司所在地")
    private Integer companyRegionId;
    /**
     * 公司详细地址
     */
    @NotBlank(message = "公司详细地址不能为空", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    @Size(max = 100, message = "公司所在地不能超过100个字符", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    @ExaminField(description = "公司详细地址")
    private String companyAddress;
    /**
     * 姓名
     */
    @Size(max = 10, min = 2, message = "姓名长度在2-10之间" ,groups = {AddPersonGroup.class})
    private String contactName;
    /**
     * 公司法定代表人
     */
    @Size(max = 10, min = 2, message = "法人名称长度在2-10之间", groups = {AddCompanyGroup.class})
    private String legalPerson;
    /**
     * 法人联系方式
     */
    @Size(max = 11, min = 11, message = "法人手机号不符合格式", groups = {AddCompanyGroup.class})
    private String contactsPhone;

    /**
     * 身份证有效期类型
     */
    @NotNull(message = "请选择有效期类型", groups = {AddCompanyGroup.class})
    private Integer idCardExpireType;

    /**
     * 身份证号
     */
    @NotBlank(message = "身份证号不能为空", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    @Size(max = 18, min = 18, message = "身份证号不符合格式", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private String idCard;
    private Date idCardStartDate;
    private Date idCardEndDate;
    /**
     * 身份证正面照
     */
    @NotBlank(message = "身份证正面照不能为空", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private String idCardUrl;
    /**
     * 身份证反面照
     */
    @NotBlank(message = "身份证反面照不能为空", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private String idCardUrl2;
    /**
     * 营业执照号
     */
    @Size(max = 1000, message = "营业执照号不能超过100个字符", groups = {AddCompanyGroup.class})
    private String businessLicenseNumber;
    /**
     * 营业执照所在地
     */
    private Integer businessLicenseArea;
    private Date businessLicenseStart;
    private Date businessLicenseEnd;
    /**
     * 法定经营范围
     */
    @Size(max = 200, message = "法定经营范围不能超过200个字符", groups = {AddCompanyGroup.class})
    private String businessSphere;
    /**
     * 营业执照号电子版
     */
    @NotBlank(message = "营业执照号电子版不能为空", groups = {AddCompanyGroup.class})
    private String businessLicenseNumberPhoto;
    /**
     * 开户银行
     */
    @NotBlank(message = "营业执照号电子版不能为空", groups = {AddCompanyGroup.class})
    private String bankPhoto;
    /**
     * 自定义数据
     */
    private String formData;
    /**
     * 管理员姓名
     */
    @Size(max = 10, min = 2, message = "管理员姓名长度在2-10之间", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private String realName;
    /**
     * 管理员手机
     */
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "管理员手机号不符合格式", groups = {AddCompanyGroup.class, AddPersonGroup.class})
    private String memberPhone;
    /**
     * 手机验证码
     */
    private String phoneCode ;
    /**
     * 管理员邮箱
     */
    private String memberEmail;
    /**
     * 邮箱验证码
     */
    private String emailCode ;
}