package com.sankuai.shangou.seashop.user.thrift.shop.response.dto;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:53
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CategoryApplyFormFieldDto extends BaseThriftDto {

    /**
     * 表单字段id
     */
    private Long fieldId;

    /**
     * 表单字段id
     */
    private String fieldName;

    /**
     * 表单字段值
     */
    private String fieldValue;

    /**
     * 表字段类型
     */
    private Integer type;

    /**
     * 格式
     */
    private Integer format;

    /**
     * 选项
     */
    private String option;

    /**
     * 是否必填
     */
    private Boolean isRequired;

    /**
     * 排序
     */
    private Long displaySequence;

    /**
     * 是否必填
     */
    private Boolean required;
}
