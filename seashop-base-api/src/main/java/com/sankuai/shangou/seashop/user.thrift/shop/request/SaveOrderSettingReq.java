package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * @description：店铺交易设置
 * @author： liweisong
 * @create： 2023/11/27 16:07
 */
@ToString
@Data
public class SaveOrderSettingReq extends BaseParamReq {

    /**
     * 店铺ID
     */
    @ExaminField
    private Long shopId;

    /**
     * 起购量校验方式：1：起购数量和起购金额同时满足 2：起购数量和起购金额满足其一
     */
    @ExaminField
    private Integer purchaseMinValidType;

    /**
     * 起购数量
     */
    @ExaminField
    private Integer purchaseMinQuantity;

    /**
     * 起购金额
     */
    @ExaminField
    private BigDecimal purchaseMinPrice;

    public void checkParameter(){

    }
}
