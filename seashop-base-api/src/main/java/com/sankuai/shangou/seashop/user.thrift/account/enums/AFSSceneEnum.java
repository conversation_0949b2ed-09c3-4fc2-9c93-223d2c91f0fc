package com.sankuai.shangou.seashop.user.thrift.account.enums;

import java.util.Objects;

/**
 * @description:
 * @author: LXH
 **/
public enum AFSSceneEnum {
    //登录
    LOGIN("LOGIN", "登录"),
    //注册
    REGISTER("REGISTER", "注册"),
    ;
    private final String code;
    private final String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    AFSSceneEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //根据code获取枚举
    public static AFSSceneEnum getEnumByCode(String code) {
        for (AFSSceneEnum afsScene : AFSSceneEnum.values()) {
            if (Objects.equals(afsScene.getCode(), code)) {
                return afsScene;
            }
        }
        return null;
    }

    //根据name获取枚举
    public static AFSSceneEnum getEnumByName(String name) {
        for (AFSSceneEnum afsScene : AFSSceneEnum.values()) {
            if (afsScene.name().equals(name)) {
                return afsScene;
            }
        }
        return null;
    }
}
