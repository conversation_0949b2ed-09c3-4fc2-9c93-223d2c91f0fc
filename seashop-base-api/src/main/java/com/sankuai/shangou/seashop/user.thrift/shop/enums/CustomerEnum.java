package com.sankuai.shangou.seashop.user.thrift.shop.enums;

public class CustomerEnum {
    public enum Tool {
        QQ(1, "QQ"),
        <PERSON><PERSON>(2, "旺旺"),
        <PERSON><PERSON><PERSON>(3, "美洽"),
        <PERSON><PERSON><PERSON>(4, "海商");
        private int value;
        private String desc;

        Tool(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static String getDescByValue(Integer tool) {
            if (tool == null) {
                return null;
            }
            for (Tool value : Tool.values()) {
                if (value.getValue() == tool) {
                    return value.getDesc();
                }
            }
            return null;
        }

        public int getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum Type {
        PreSale(1, "售前"),
        AfterSale(2, "售后");
        private int value;
        private String desc;

        Type(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public static String getDescByValue(Integer type) {
            if (type == null) {
                return null;
            }
            for (Type value : Type.values()) {
                if (value.getValue() == type) {
                    return value.getDesc();
                }
            }
            return null;
        }

        public int getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }
    }

    public enum TerminalType {
        PC(0, "PC"),
        Mobile(1, "Mobile"),
        All(2, "All");
        private final int value;
        private final String desc;

        TerminalType(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public int getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }

        public static String getDescByValue(Integer type) {
            if (type == null) {
                return null;
            }
            for (TerminalType value : TerminalType.values()) {
                if (value.getValue() == type) {
                    return value.getDesc();
                }
            }
            return null;
        }
    }

    public enum ServiceStatusType {
        Close(0, "关闭"),
        Open(1, "开启");
        private int value;
        private String desc;

        ServiceStatusType(int value, String desc) {
            this.value = value;
            this.desc = desc;
        }

        public int getValue() {
            return value;
        }

        public String getDesc() {
            return desc;
        }
    }
}
