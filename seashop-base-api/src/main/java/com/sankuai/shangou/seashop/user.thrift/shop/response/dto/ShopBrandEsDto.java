package com.sankuai.shangou.seashop.user.thrift.shop.response.dto;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ShopBrandEsDto extends BaseThriftDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 排序
     */
    private Long displaySequence;

    /**
     * 品牌logo
     */
    private String logo;

    /**
     * 品牌简介
     */
    private String description;
}
