package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2023-11-29
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CustomerServiceQueryPageReq extends BasePageReq {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺id集合
     */
    private List<Long> shopIds;

}
