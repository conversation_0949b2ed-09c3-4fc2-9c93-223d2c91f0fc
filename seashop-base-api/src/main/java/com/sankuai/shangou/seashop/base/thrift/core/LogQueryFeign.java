package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogDetailReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogMQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LogSellerQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.LogDetailResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.LogQueryResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author： liweisong
 * @create： 2023/12/1 13:57
 */
@FeignClient(name = "himall-base",contextId = "LogQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/log")
public interface LogQueryFeign {

    /**
     * 平台-系统-操作日志（查询）
     * @param logMQueryReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageMBaseLog", consumes = "application/json")
    ResultDto<BasePageResp<LogQueryResp>> pageMBaseLog(@RequestBody LogMQueryReq logMQueryReq) throws TException;

    /**
     * 供应商后台-系统-操作日志（查询）
     * @param logSellerQueryReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageSellerBaseLog", consumes = "application/json")
    ResultDto<BasePageResp<LogQueryResp>> pageSellerBaseLog(@RequestBody LogSellerQueryReq logSellerQueryReq) throws TException;

    /**
     * 供应商后台-系统-操作日志（查看详情）
     * @param logDetailReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryBaseLogDetail", consumes = "application/json")
    ResultDto<LogDetailResp> queryBaseLogDetail(@RequestBody LogDetailReq logDetailReq) throws TException;
}
