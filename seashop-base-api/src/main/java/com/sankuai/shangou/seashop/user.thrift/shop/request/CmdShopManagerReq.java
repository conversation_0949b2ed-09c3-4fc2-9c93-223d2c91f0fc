package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class CmdShopManagerReq extends BaseParamReq {

    /**
     * 店铺管理员名称
     */
    private String realName;
    /**
     * shopId
     */
    @PrimaryField
    private Long shopId;

    @FieldDoc(description = "管理员手机")
    private String memberPhone;
}
