package com.sankuai.shangou.seashop.user.thrift.account.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 标签信息
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LabelResp {
    /**
     * 标签名称
     */
    private String labelName;
    /**
     * 标签ID
     */
    private String id;
    /**
     * 商家数量
     */
    private Long memberCount;
}
