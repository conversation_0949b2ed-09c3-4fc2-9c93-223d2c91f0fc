package com.sankuai.shangou.seashop.user.thrift.account.request;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.sankuai.shangou.seashop.base.boot.enums.LoginTypeEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description: 平台管理员登录入参
 * @author: LXH
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RegisterReq extends BaseParamReq {
    /**
     * 注册类型 MOBILE WX_CODE
     */
    @JsonFormat(shape = JsonFormat.Shape.OBJECT)
    private String registerType;

    /**
     * 密码
     */
    private String password;

    /**
     * 确认密码
     */
    private String confirmPassword;

    /**
     * key
     */
    private String imageKey;

    /**
     * 图片验证码 base64
     */
    private String imageCode;

    /**
     * 手机
     */
    private String phone;

    /**
     * 手机验证码
     */
    private String phoneCode;

    /**
     * 微信注册code
     */
    private String weCode;

    /**
     * 微信openId
     */
    private String openId;

    /**
     * 微信unionId
     */
    private String unionId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(registerType, "注册类型不能为空");
        if (StrUtil.equals(registerType, LoginTypeEnum.MOBILE.name())) {
            AssertUtil.throwIfNull(phone, "手机号不能为空");
            AssertUtil.throwIfNull(phoneCode, "手机验证码不能为空");
            AssertUtil.throwIfNull(password, "密码不能为空");
            AssertUtil.throwIfNull(confirmPassword, "确认密码不能为空");
            AssertUtil.throwIfNull(imageKey, "图片验证码key不能为空");
            AssertUtil.throwIfNull(imageCode, "图片验证码不能为空");
        }

        if (StrUtil.equals(registerType, LoginTypeEnum.WX_CODE.name())) {
            AssertUtil.throwIfNull(weCode, "微信code不能为空");
        }
        super.checkParameter();
    }
}
