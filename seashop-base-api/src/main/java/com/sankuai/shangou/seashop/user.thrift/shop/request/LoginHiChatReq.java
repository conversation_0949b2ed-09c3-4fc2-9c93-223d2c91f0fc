package com.sankuai.shangou.seashop.user.thrift.shop.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LoginHiChatReq {
    @NotNull(message = "店铺ID不能为空")
    private Long shopId;
    /**
     * 管理员
     */
    private Long managerId;
}
