package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.ShopFreeShippingAreaDto;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@ToString
@Data
public class ShippingSettingsResp extends BaseThriftDto {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺运费-单笔订单实付金额门槛
     */
    private BigDecimal amountFreightCondition;

    /**
     * 店铺单笔订单实付金额满足条件后的运费
     */
    private BigDecimal amountFreight;

    /**
     * 店铺运费-单笔订单商品数量门槛
     */
    private Integer quantityFreightCondition;

    /**
     * 店铺单笔订单商品数量满足条件后的运费
     */
    private BigDecimal quantityFreight;

    /**
     * 包邮区域列表
     */
    private List<ShopFreeShippingAreaDto> areaList;
}
