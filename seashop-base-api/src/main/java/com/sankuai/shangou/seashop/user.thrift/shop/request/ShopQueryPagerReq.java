package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopQueryPagerReq extends BasePageReq {

    /**
     * 状态
     */
    private Integer status;

    /**
     * 审核状态
     */
    private Integer auditStatus;

    /**
     * 店铺名
     */
    private String shopName;

    /**
     * 状态
     */
    private String shopAccount;

    /**
     * 分类ID
     */
    private Long categoryId;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 创建时间
     */
    private Date createDateBegin;

    /**
     * 结束时间
     */
    private Date createDateEnd;

    /**
     * 过期时间
     */
    private Date expiredDateEnd;

    /**
     * 步骤
     */
    private Integer stage;

    /**
     * 店铺id
     */
    private List<Long> shopIds;

    /**
     * 是否签署合同
     */
    private Boolean whetherAgreement;

    /**
     * 是否支付保证金
     */
    private Boolean whetherPayBond;

    /**
     * 是否是供应商
     */
    private Boolean whetherSupply;

    /**
     * 是否需要续签合同
     */
    private Boolean whetherAgainSign;

    /**
     * 商家ID
     */
    private Long userId;

    /**
     * 是否前台搜索
     */
    private Boolean whetherFrontSearch;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺类型
     */
    private Integer shopType;
}
