package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:54
 */
@Data
public class SaveShopInvoiceReq extends BaseParamReq {

    /**
     * 主键
     */
    @PrimaryField
    private Long id;

    /**
     * 店铺ID
     */
    @ExaminField(description = "店铺ID")
    private Long shopId;

    /**
     * 是否提供发票
     */
    @ExaminField(description = "是否提供发票")
    @NotNull(message = "发票不能为NULL")
    private Boolean whetherInvoice;

    /**
     * 是否提供普通发票
     */
    @ExaminField(description = "是否提供普通发票")
    @NotNull(message = "普通发票不能为NULL")
    private Boolean whetherPlainInvoice;

    /**
     * 是否提供电子发票
     */
    @ExaminField(description = "是否提供电子发票")
    @NotNull(message = "电子发票不能为NULL")
    private Boolean whetherElectronicInvoice;

    /**
     * 普通发票税率
     */
    @ExaminField(description = "普通发票税率")
    @NotNull(message = "普通发票税率不能为NULL")
    private BigDecimal plainInvoiceRate;

    /**
     * 是否提供增值税发票
     */
    @ExaminField(description = "是否提供增值税发票")
    @NotNull(message = "增值税发票不能为NULL")
    private Boolean whetherVatInvoice;

    /**
     * 订单完成后多少天开具增值税发票
     */
    @ExaminField(description = "订单完成后多少天开具增值税发票")
    @NotNull(message = "订单完成后多少天开具增值税发票不能为NULL")
    private Integer vatInvoiceDay;

    /**
     * 增值税税率
     */
    @ExaminField(description = "增值税税率")
    @NotNull(message = "增值税税率不能为NULL")
    private BigDecimal vatInvoiceRate;

    private Date createTime;

    public void checkParameter() {
        if (whetherInvoice == null) {
            throw new IllegalArgumentException("whetherInvoice是否提供发票不能为空");
        }
    }
}
