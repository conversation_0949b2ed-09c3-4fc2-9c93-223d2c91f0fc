package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BusinessCategoryApplyPageResp {
    /**
     * 分页结果
     */
    private BasePageResp<BusinessCategoryApplyResp> pageResp;
}
