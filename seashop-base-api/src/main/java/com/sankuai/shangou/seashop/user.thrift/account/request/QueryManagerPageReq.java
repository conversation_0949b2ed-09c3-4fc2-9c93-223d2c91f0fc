package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

/**
 * @description: 查询管理员请求入参
 * @author: LXH
 **/
@Data
public class QueryManagerPageReq extends BasePageReq {
    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 管理员id的集合
     */
    private List<Long> managerIds;

    @Override
    public void checkParameter() {
        if (CollectionUtils.isNotEmpty(managerIds)) {
            AssertUtil.throwInvalidParamIfTrue(managerIds.size() > 200, "管理员id集合不能超过200个");
        }
    }
}
