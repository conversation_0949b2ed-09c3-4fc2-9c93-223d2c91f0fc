package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class PrivilegeResp {

    /**
     * 主键
     */
    private Long id;
    /**
     * 父级ID
     */
    private Long parentId;
    /**
     * 名称
     */
    private String name;
    /**
     * 链接
     */
    private String url;
    /**
     * 图标
     */
    private String icon;
    /**
     * 排序
     */
    private Integer displaySequence;
    /**
     * 子集
     */
    private List<SubPrivilegeResp> children = new ArrayList<>();
    /**
     * 是否选择
     */
    private Boolean hasSelect = false;
    /**
     * 激活图标
     */
    private String activedIcon;
    /**
     * 操作
     */
    @TableField("操作")
    private String action;
}
