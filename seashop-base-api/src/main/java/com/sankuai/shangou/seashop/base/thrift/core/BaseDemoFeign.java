package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.DemoModelReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.DemoWrapper;

import org.apache.thrift.TException;

public interface BaseDemoFeign {
    /**
     * 采购入库单创建接口，创建采购入库单
     * @param req
     * @return
     * @throws TException
     */
    ResultDto<DemoWrapper> createDemo(DemoModelReq req) throws TException;
}
