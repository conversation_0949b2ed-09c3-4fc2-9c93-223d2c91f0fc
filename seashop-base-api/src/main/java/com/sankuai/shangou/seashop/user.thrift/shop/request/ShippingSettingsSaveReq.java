package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.ShopFreeShippingAreaDto;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Data
public class ShippingSettingsSaveReq extends BaseParamReq {

    @PrimaryField
    private Long shopId;

    /**
     * 店铺运费-单笔订单实付金额门槛
     */
    @ExaminField(description = "店铺运费-单笔订单实付金额门槛")
    private BigDecimal amountFreightCondition;

    /**
     * 店铺单笔订单实付金额满足条件后的运费
     */
    @ExaminField(description = "店铺单笔订单实付金额满足条件后的运费")
    private BigDecimal amountFreight;

    /**
     * 店铺运费-单笔订单商品数量门槛
     */
    @ExaminField(description = "店铺运费-单笔订单商品数量门槛")
    private Integer quantityFreightCondition;

    /**
     * 店铺单笔订单商品数量满足条件后的运费
     */
    @ExaminField(description = "店铺单笔订单商品数量满足条件后的运费")
    private BigDecimal quantityFreight;

    /**
     * 包邮区域列表
     */
    private List<ShopFreeShippingAreaDto> areaList;

    @Override
    public void checkParameter() {
        if (null == this.shopId) {
            throw new IllegalArgumentException("店铺ID不能为空");
        }
        if (null == this.amountFreightCondition || this.amountFreightCondition.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("店铺运费不能为空或小于0");
        }
        if (this.amountFreightCondition.compareTo(new BigDecimal("999999")) > 0) {
            throw new IllegalArgumentException("店铺运费不能大于999999");
        }
        if (null == this.amountFreight || this.amountFreight.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("运费不能为空或小于0");
        }
        if (this.amountFreight.compareTo(new BigDecimal("999999")) > 0) {
            throw new IllegalArgumentException("运费不能大于999999");
        }
        if (null == this.quantityFreightCondition || this.quantityFreightCondition < 0) {
            throw new IllegalArgumentException("商品数量不能为空或小于0");
        }
        if (null == this.quantityFreight || this.quantityFreight.compareTo(BigDecimal.ZERO) < 0) {
            throw new IllegalArgumentException("店铺运费不能为空或小于0");
        }
        if (this.quantityFreight.compareTo(new BigDecimal("999999")) > 0) {
            throw new IllegalArgumentException("店铺运费不能大于999999");
        }
        /*if (CollectionUtils.isNotEmpty(areaList)) {
            areaList.forEach(ShopFreeShippingAreaDto::checkParameter);
        }*/
    }
}
