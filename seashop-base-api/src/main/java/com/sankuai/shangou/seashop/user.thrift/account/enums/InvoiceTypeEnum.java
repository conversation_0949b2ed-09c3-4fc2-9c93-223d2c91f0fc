package com.sankuai.shangou.seashop.user.thrift.account.enums;


/**
 * @description:
 * @author: LXH
 **/
public enum InvoiceTypeEnum {
    /**
     * 普通发票
     */
    NORMAL(1, "普通发票"),
    /**
     * 电子发票
     */
    ELECTRONIC(2, "电子发票"),
    /**
     * 增值税发票
     */
    VAT(3, "增值税发票");

    private final int value;
    private final String name;

    InvoiceTypeEnum(int value, String name) {
        this.value = value;
        this.name = name;
    }

    public static InvoiceTypeEnum valueOf(int value) {
        switch (value) {
            case 1:
                return NORMAL;
            case 2:
                return ELECTRONIC;
            case 3:
                return VAT;
            default:
                return null;
        }
    }

    public int getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
