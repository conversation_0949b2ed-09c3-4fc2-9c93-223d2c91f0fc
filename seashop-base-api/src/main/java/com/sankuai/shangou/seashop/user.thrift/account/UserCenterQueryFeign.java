package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryUserCenterHomeReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.QueryUserCenterHomeResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 商家中心-首页查询接口
 * @author： liweisong
 * @create： 2023/12/12 16:00
 */
@FeignClient(name = "himall-base", contextId = "UserCenterQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/userCenter")
public interface UserCenterQueryFeign {

    /**
     * 商家中心-首页(查询)
     * @param queryUserCenterHomeReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryUserCenterHome", consumes = "application/json")
    ResultDto<QueryUserCenterHomeResp> queryUserCenterHome(@RequestBody QueryUserCenterHomeReq queryUserCenterHomeReq) throws TException;
}
