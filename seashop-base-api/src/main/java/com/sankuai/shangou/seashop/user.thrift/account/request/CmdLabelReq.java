package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 标签单个操作请求入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdLabelReq extends BaseParamReq {
    /**
     * 标签ID
     */
    @PrimaryField(title = "标签ID")
    @ExaminField(description = "标签ID")
    private Long labelId;
    /**
     * 标签名称
     */
    @ExaminField(description = "标签ID")
    private String labelName;

}
