package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotNull;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdShopStatusReq extends BaseParamReq {
    @NotNull(message = "店铺ID为必填项")
    @PrimaryField
    private Long shopId;
    @NotNull(message = "状态为必填项")
    private Integer status;
    @ExaminField(description = "店铺状态")
    private Integer shopStatus;
    @ExaminField(description = "拒绝原因")
    private String refuseReason;
    /**
     * 结算账户ID
     */
    private String settleAccountId;
}
