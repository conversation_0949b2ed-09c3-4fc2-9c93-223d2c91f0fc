package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 单个商家查询入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryMemberContactReq extends BaseParamReq {

    /**
     * 店铺ID
     */
    private List<Long> shopIds;

    /**
     * 商家Id
     */
    private List<Long> userIds;
}
