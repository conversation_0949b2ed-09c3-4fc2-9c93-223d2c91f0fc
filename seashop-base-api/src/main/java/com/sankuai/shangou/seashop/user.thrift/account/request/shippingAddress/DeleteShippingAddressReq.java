package com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress;

import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 */
@ToString
@Getter
@Setter
public class DeleteShippingAddressReq extends BaseParamReq {

    /**
     * 需要删除的收货地址ID列表，列表是支持批量删除
     */
    private List<Long> idList;

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 参数校验
     */
    public void checkParameter() {
        if (idList == null || idList.isEmpty()) {
            throw new InvalidParamException("请选择要删除的数据");
        }
    }

}
