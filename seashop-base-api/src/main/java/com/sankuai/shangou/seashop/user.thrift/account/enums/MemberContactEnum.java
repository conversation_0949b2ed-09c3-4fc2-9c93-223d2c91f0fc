package com.sankuai.shangou.seashop.user.thrift.account.enums;

/**
 * @description: 商家联系枚举类
 * @author: LXH
 **/
public class MemberContactEnum {

    public enum Provider {
        SMS("SMS", 1 ,"短信","Himall.Plugin.Message.SMS"),
        EMAIL("Email", 0,"邮箱","Himall.Plugin.Message.Email"),
        ;

        private String code;
        private Integer value;
        private String desc;
        private String valueDesc;

        public static String getCodeByValue(Integer usertype) {
            for (Provider provider : Provider.values()) {
                if (provider.getValue().equals(usertype)) {
                    return provider.getCode();
                }
            }
            return "";
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        public Integer getValue() {
            return value;
        }

        public void setValue(Integer value) {
            this.value = value;
        }

        public String getValueDesc() {
            return valueDesc;
        }

        public void setValueDesc(String valueDesc) {
            this.valueDesc = valueDesc;
        }

        Provider(String code, Integer value , String desc, String valueDesc) {
            this.code = code;
            this.value = value;
            this.desc = desc;
            this.valueDesc = valueDesc;
        }

        //根据code获取enum
        public static Provider getEnumByCode(String code) {
            for (Provider provider : Provider.values()) {
                if (provider.getCode().equalsIgnoreCase(code)) {
                    return provider;
                }
            }
            return null;
        }

        //根据value获取enum
        public static Provider getEnumByValue(Integer value) {
            for (Provider provider : Provider.values()) {
                if (provider.getValue().equals(value)) {
                    return provider;
                }
            }
            return null;
        }


        public static String getValueDescByValue(Integer value) {
            for (Provider provider : Provider.values()) {
                if (provider.getValue().equals(value)) {
                    return provider.getValueDesc();
                }
            }
            return null;
        }
    }
}
