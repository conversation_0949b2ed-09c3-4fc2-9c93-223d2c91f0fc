package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/02/21 10:04
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryProductFavoriteCountResp extends BaseParamReq {

    /**
     * 商品id
     */
    private Long productId;

    /**
     * 收藏数
     */
    private Integer favoriteCount;
}
