package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 专属商家模版上传至文件系统后返回路径，文件名等信息
 * @author: liweisong
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ImportExclusiveReq  extends BaseParamReq {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 文件地址
     */
    private String filePath;

    public void checkParameter(){
        if(shopId == null){
            throw new IllegalArgumentException("shopId不能为空");
        }
        if(filePath == null){
            throw new IllegalArgumentException("filePath不能为空");
        }
    }
}
