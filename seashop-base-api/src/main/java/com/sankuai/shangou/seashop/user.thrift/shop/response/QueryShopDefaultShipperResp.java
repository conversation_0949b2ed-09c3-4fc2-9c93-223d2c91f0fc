package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryShopShipperRespDto;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/23 8:44
 */
@ToString
@Data
public class QueryShopDefaultShipperResp extends BaseParamReq {

    /**
     * 供应商默认的发货地址
     */
    private QueryShopShipperRespDto defaultShopSendShipper;

    /**
     * 供应商默认的收货地址
     */
    private QueryShopShipperRespDto defaultShopReceiveShipper;

}
