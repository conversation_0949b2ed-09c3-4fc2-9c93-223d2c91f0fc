package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;

@FeignClient(name = "himall-base",contextId = "WayBillFeign", path = "/himall-base/wayBill", url = "${himall-base.dev.url:}")
public interface WayBillFeign {

    /**
     * 注册电子面单平台
     */
    @PostMapping(value = "/setRegisterState")
    ResultDto<Boolean> setRegisterState() throws TException;

    /**
     * 获取电子面单平台地址，用于前端跳转
     */
    @GetMapping(value = "/goExpressBills")
    ResultDto<String> goExpressBills() throws TException;

}
