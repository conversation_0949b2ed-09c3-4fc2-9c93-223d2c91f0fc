package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseImportResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdBusinessCategoryReqList;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdImportCategoryReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "himall-base", contextId = "BusinessCategoryCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/businessCategory")
public interface BusinessCategoryCmdFeign {

    /**
     * updateBusinessCategory
     * @param reqList
     * @return
     * @throws TException
     */
    @PostMapping(value = "/updateBusinessCategory", consumes = "application/json")
    ResultDto<BaseResp> updateBusinessCategory(@RequestBody CmdBusinessCategoryReqList reqList) throws TException;

    /**
     * 删除店铺类目
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deleteBusinessCategory", consumes = "application/json")
    ResultDto<BaseResp> deleteBusinessCategory(@RequestBody BaseIdReq request) throws TException;

    /**
     * 导入店铺类目
     * @param cmdImportCategoryReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/importBusinessCategory", consumes = "application/json")
    ResultDto<BaseImportResp> importBusinessCategory(@RequestBody CmdImportCategoryReq cmdImportCategoryReq) throws TException;
}
