package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryBusinessCategoryTreeReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryValidBusinessCategoryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.*;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-base", contextId = "BusinessCategoryQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/businessCategory")
public interface BusinessCategoryQueryFeign {
    /**
     * 分页查询店铺类目
     * @param queryBusinessCategoryPageReq
     * @return
     * @throws TException
     */
    @PostMapping(value="/queryPage",consumes = "application/json")
    ResultDto<BasePageResp<BusinessCategoryResp>> queryPage(@RequestBody QueryBusinessCategoryPageReq queryBusinessCategoryPageReq) throws TException;

    /**
     * 分页查询店铺类目
     * @param baseBatchIdReq
     * @return
     * @throws TException
     */
    @PostMapping(value="/queryList",consumes = "application/json")
    ResultDto<BusinessCategoryRespList> queryList(@RequestBody BaseBatchIdReq baseBatchIdReq) throws TException;


    /**
     * 查询店铺经营的类目树
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value="/queryBusinessCategoryTree",consumes = "application/json")
    ResultDto<BusinessCategoryTreeResp> queryBusinessCategoryTree(@RequestBody QueryBusinessCategoryTreeReq request) throws TException;


    /**
     * 查询商家有效的类目id的集合
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value="/queryValidBusinessCategoryIds",consumes = "application/json")
    ResultDto<ValidBusinessCategoryIdsResp> queryValidBusinessCategoryIds(@RequestBody QueryValidBusinessCategoryReq request) throws TException;

    /**
     * 查询类目自定义表单
     * @param id
     * @return
     * @throws TException
     */
    @PostMapping(value="/queryById",consumes = "application/json")
    ResultDto<QueryLastCategoryResp> queryById(@RequestBody BaseBatchIdReq id) throws TException;
}
