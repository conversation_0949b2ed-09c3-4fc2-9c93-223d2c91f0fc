package com.sankuai.shangou.seashop.user.thrift.auth.enums;

/**
 * @description: 短信类型枚举
 * @author: chendongdong
 **/
public enum SmsTypeEnum {
    LOGIN("login", "登录短信"),
    REGISTER("register", "注册"),
    FIND_PASSWORD("find_password", "找回密码");

    private String code;
    private String msg;

    private SmsTypeEnum(String code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public String getCode() {
        return this.code;
    }

    public String getMsg() {
        return this.msg;
    }

    public static SmsTypeEnum nameOf(String name) {
        if (name == null) {
            return null;
        }
        for (SmsTypeEnum typeEnum : values()) {
            if (typeEnum.name().equals(name)) {
                return typeEnum;
            }
        }
        return null;
    }
}
