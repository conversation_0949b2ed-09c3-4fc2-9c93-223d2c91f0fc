package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MessageRecordDetailResp extends BaseThriftDto {
    private Long id;

    /**
     * 消息类别
     */
    private Integer messageType;

    /**
     * 消息类别描述
     */
    private String messageTypeDesc;

    /**
     * 内容类型
     */
    private Integer contentType;

    /**
     * 内容类型描述
     */
    private String contentTypeDesc;

    /**
     * 发送内容
     */
    private String sendContent;

    /**
     * 发送对象
     */
    private String toUserLabel;

    /**
     * 发送状态
     */
    private Integer sendState;

    /**
     * 发送状态描述
     */
    private String sendStateDesc;

    /**
     * 发送时间
     */
    private Date sendTime;

    /**
     * 发送时间描述
     */
    private String sendTimeDesc;

    /**
     * 群发发送的优惠券Id列表
     */
    private List<Long> couponIdList;


}
