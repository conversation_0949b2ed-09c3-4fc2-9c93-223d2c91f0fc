package com.sankuai.shangou.seashop.user.thrift.shop.dto;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:53
 */
@ToString
@Data
public class BusinessCategoryApplyFormFieldDto extends BaseParamReq {

    /**
     * 表单字段id
     */
    private Long fieldId;

    /**
     * 表单字段值
     */
    private String fieldValue;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(fieldId == null || fieldId <= 0, "表单字段id不能为空");
    }
}
