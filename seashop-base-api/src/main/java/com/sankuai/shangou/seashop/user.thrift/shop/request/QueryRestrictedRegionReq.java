package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


/**
 * <AUTHOR>
 * @date 2024/01/04 12:04
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryRestrictedRegionReq extends BaseParamReq {

    /**
     * 运费模板id
     */
    private Long freightTemplateId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(freightTemplateId == null || freightTemplateId <= 0, "运费模板id不能为空");
    }
}
