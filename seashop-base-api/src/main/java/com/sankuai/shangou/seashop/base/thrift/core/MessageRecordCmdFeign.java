package com.sankuai.shangou.seashop.base.thrift.core;


import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.MessageRecordCmdReq;

@FeignClient(name = "himall-base", contextId = "MessageRecordCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/messageRecord")
public interface MessageRecordCmdFeign {

    /**
     * 添加消息记录
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/addRecord", consumes = "application/json")
    ResultDto<Long> addRecord(@RequestBody MessageRecordCmdReq request) throws TException;


    /**
     * 发送失败删除发送记录
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deleteRecord", consumes = "application/json")
    ResultDto<BaseResp> deleteRecord(@RequestBody BaseIdReq request) throws TException;
}
