package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.FreightAreaContentReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.RestrictedAreaReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShippingFreeGroupReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ToString
@Data
public class QueryFreightTemplateDetailResp extends BaseParamReq {

    /**
     * 主键
     */
    private Long id;

    /**
     * 运费模板名称
     */
    private String name;

    /**
     * 宝贝发货地
     */
    private Integer sourceAddress;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 是否商家负责运费
     */
    private Integer whetherFree;

    /**
     * 定价方法(按体积、重量计算）
     */
    private Integer valuationMethod;

    /**
     * 运送类型（物流、快递）
     */
    private Integer shippingMethod;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 非销售区域是否隐藏
     */
    private Boolean nonSalesAreaHide;

    @FieldDoc(description = "非销售区域商品是否隐藏")
    private Boolean nonSalesAreaProductHide;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 指定可配送区域的运费
     */
    private List<FreightAreaContentReq> contentReqList;

    /**
     * 指定城市包邮
     */
    private List<ShippingFreeGroupReq> groupReqList;

    /**
     * 非销售区域
     */
    private List<RestrictedAreaReq> restrictedAreaReqList;
}
