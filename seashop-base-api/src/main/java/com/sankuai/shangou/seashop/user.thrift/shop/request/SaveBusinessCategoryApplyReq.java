package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.BusinessCategoryApplyFormDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:28
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SaveBusinessCategoryApplyReq extends BaseParamReq {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 类目id列表
     */
    private List<Long> categoryIdList;

    /**
     * 表单数据
     */
    private List<BusinessCategoryApplyFormDto> formList;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(categoryIdList), "类目id列表不能为空");
        if (!CollectionUtils.isEmpty(formList)) {
            formList.forEach(form -> form.checkParameter());
        }
    }
}
