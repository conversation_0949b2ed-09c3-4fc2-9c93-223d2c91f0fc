package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryShopShipperRespDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopDefaultShipperResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopShipperResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

/**
 * @description：供应商发/退货地址
 * @author： liweisong
 * @create： 2023/11/27 9:17
 */
@FeignClient(name = "himall-base", contextId = "ShopShipperQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/shopShipper")
public interface ShopShipperQueryFeign {

    /**
     * 查询供应商发/退货地址
     *
     * @param queryShopShipperReq 查询供应商发/退货地址信息
     */
    @PostMapping(value = "/queryShopShipperList",consumes = "application/json")
    ResultDto<QueryShopShipperResp> queryShopShipperList(@RequestBody QueryShopShipperReq queryShopShipperReq) throws TException;

    /**
     * 查询供应商默认的发/退货地址
     *
     * @param queryShopShipperReq 查询供应商默认的发/退货地址信息
     */
    @PostMapping(value = "/queryShopDefaultShipperList",consumes = "application/json")
    ResultDto<QueryShopDefaultShipperResp> queryShopDefaultShipperList(@RequestBody QueryShopShipperReq queryShopShipperReq) throws TException;

    /**
     * 批量查询供应商发/退货地址
     *
     * @param shopIds 查询供应商发/退货地址信息
     */
    @PostMapping(value = "/queryBatchShopShipperList",consumes = "application/json")
    ResultDto<Map<Long,List<QueryShopShipperRespDto>>> queryBatchShopShipperList(@RequestBody List<Long> shopIds) throws TException;
}
