package com.sankuai.shangou.seashop.user.thrift.shop;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveShopLogoReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopLogoResp;

/**
 * 店铺设置操作类
 */
@FeignClient(name = "himall-base", contextId = "ShopSetFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/shopSet")
public interface ShopSetFeign {

    @PostMapping(value = "/saveShopLogo", consumes = "application/json")
    ResultDto<BaseResp> saveShopLogo(@RequestBody SaveShopLogoReq savePageLogReq);

    @GetMapping(value = "/getShopLogo")
    ResultDto<ShopLogoResp> getShopLogo(@RequestParam Long shopId);

}
