package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MemberContactQueryPageReq extends BasePageReq {
    /**
     * 标签id
     */
    private List<Long> labelId;
    /**
     * 是否需要总数
     */
    private Boolean hasCount;
    /**
     * 消息类型
     */
    private Integer msgType;
    /**
     * 服务商
     */
    private String serviceProvider;
}
