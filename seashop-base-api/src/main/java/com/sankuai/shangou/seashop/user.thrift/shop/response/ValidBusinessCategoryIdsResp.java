package com.sankuai.shangou.seashop.user.thrift.shop.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/02/01 10:16
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ValidBusinessCategoryIdsResp {

    /**
     * 有效的经营类目id
     */
    private List<Long> categoryIds;

    /**
     * 待审核的经营类目id
     */
    private List<Long> waitAuditCategoryIds;

}
