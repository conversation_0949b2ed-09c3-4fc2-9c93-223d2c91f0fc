package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/12/09 10:27
 */
@ToString
@Data
public class AuditBusinessCategoryApplyReq extends BaseParamReq {

    /**
     * 经营类目申请id
     */
    private Long id;

    /**
     * 审核是否通过
     */
    private Boolean pass;

    /**
     * 拒绝原因
     */
    private String refuseReason;


    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "经营类目申请id不能为空");
        AssertUtil.throwInvalidParamIfNull(pass, "审核是否通过不能为空");
        if (!pass) {
            AssertUtil.throwInvalidParamIfTrue(StringUtils.isBlank(refuseReason), "拒绝原因不能为空");
        }
    }
}
