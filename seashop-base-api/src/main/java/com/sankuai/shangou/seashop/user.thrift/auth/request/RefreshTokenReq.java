package com.sankuai.shangou.seashop.user.thrift.auth.request;

import com.sankuai.shangou.seashop.base.boot.dto.TokenCache;
import com.sankuai.shangou.seashop.base.boot.enums.RoleEnum;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * @author: cdd
 * @date: 2024/5/17/017
 * @description: 发送登录短信对象
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class RefreshTokenReq {
    /**
     * 原token
     */
    private TokenCache token;
    /**
     * 角色类型
     */
    private String roleType;

    public void checkParameter() {
        AssertUtil.throwIfNull(token,"原token");
        AssertUtil.throwIfNull(RoleEnum.nameOf(roleType),"角色类型不能为空");
    }
}
