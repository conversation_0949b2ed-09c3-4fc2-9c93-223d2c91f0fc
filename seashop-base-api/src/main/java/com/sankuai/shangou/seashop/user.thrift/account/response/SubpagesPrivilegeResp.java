package com.sankuai.shangou.seashop.user.thrift.account.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description:
 * @author: LXH
 **/

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SubpagesPrivilegeResp {
    /**
     * 主键
     */
    private Long id;
    /**
     * 父级ID
     */
    private Long parentId;
    /**
     * 名称
     */
    private String name;
    /**
     * 链接
     */
    private String url;
}
