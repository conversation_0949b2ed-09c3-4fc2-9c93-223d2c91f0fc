package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/14 9:16
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryBusinessCategoryTreeReq extends BaseParamReq {

    /**
     * 店铺Id
     */
    private Long shopId;

    /**
     * 分类名称（支持模糊搜索）
     */
    private String categoryName;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺Id不能为空");
    }

}
