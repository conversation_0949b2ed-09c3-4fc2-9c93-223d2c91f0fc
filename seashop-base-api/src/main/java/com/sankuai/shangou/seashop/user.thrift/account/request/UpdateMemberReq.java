package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @description: 商家操作请求入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UpdateMemberReq extends BaseParamReq {
    /**
     * id
     */
    private Long id;

    /**
     * 用户名
     */
    private String userName;

    /**
     * 密码
     */
    private String password;

    /**
     * 手机号
     */
    @ExaminField(description = "手机号")
    private String mobile;

    /**
     * 昵称
     */
    @ExaminField(description = "昵称")
    private String nick;

    /**
     * 真实姓名
     */
    @ExaminField(description = "真实姓名")
    private String realName;

    /**
     * 性别
     */
    @ExaminField(description = "性别")
    private Integer sex;

    /**
     * 头像
     */
    @ExaminField(description = "头像")
    private String photo;

    /**
     * qq
     */
    @ExaminField(description = "qq")
    private String qq;

    /**
     * occupation
     */
    @ExaminField(description = "occupation")
    private String occupation;

    /**
     * 生日
     */
    @ExaminField(description = "生日")
    private Date birthDay;

    /**
     * 微信OpenId
     */
    private String openId;


    /**
     * 微信union
     */
    private String unionId;
}
