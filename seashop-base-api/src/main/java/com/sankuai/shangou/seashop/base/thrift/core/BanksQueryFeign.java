package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.response.BanksListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

@FeignClient(name = "himall-base",contextId = "BanksQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/bank")
public interface BanksQueryFeign {

    /**
     * 查询银行列表
     */
    @GetMapping(value = "/queryList")
    ResultDto<BanksListResp> queryList() throws TException;
}
