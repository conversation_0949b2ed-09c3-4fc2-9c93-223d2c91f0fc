package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;
import lombok.*;

import java.util.ArrayList;
import java.util.List;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
public class QueryFreightTemplateResp {

    /**
     * 运费模版管理查询接口返回对象列表
     */
    private List<QueryFreightTemplateDto> result = new ArrayList<>();
}
