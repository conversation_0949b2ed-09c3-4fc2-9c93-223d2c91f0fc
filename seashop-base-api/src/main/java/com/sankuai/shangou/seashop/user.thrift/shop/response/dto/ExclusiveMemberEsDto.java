package com.sankuai.shangou.seashop.user.thrift.shop.response.dto;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2024/1/9/009
 * @description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ExclusiveMemberEsDto extends BaseParamReq {

    /**
     * 主键
     */
    private Long id;

    /**
     * 供应商Id
     */
    private Long shopId;

    /**
     * 商家Id
     */
    private Long userId;
}
