package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.response.TreeBankRegionResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.*;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 供应商操作服务类
 */
@FeignClient(name = "himall-base", contextId = "ShopCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/shop")
public interface ShopCmdFeign {
    /**
     * 入驻申请
     */
    @PostMapping(value = "/residencyApplication", consumes = "application/json")
    ResultDto<String> residencyApplication(@RequestBody CmdAgreementReq cmdAgreementReq) throws TException;

    /**
     * 发送验证码
     */
    @PostMapping(value = "/sendCode", consumes = "application/json")
    ResultDto<BaseResp> sendCode(@RequestBody CmdSendCodeReq cmdSendCodeReq) throws TException;

    /**
     * 增加供应商基本信息
     */
    @PostMapping(value = "/editBaseInfo", consumes = "application/json")
    ResultDto<Long> editBaseInfo(@RequestBody CmdShopStepsOneReq cmdShopStepsOneReq) throws TException;

    /**
     * 增加供应商财务信息
     */
    @PostMapping(value = "/editBankInfo", consumes = "application/json")
    ResultDto<Long> editBankInfo(@RequestBody CmdShopStepsTwoReq cmdShopStepsTwoReq) throws TException;

    /**
     * 增加供应商类目信息
     */
    @PostMapping(value = "/editCategoryInfo", consumes = "application/json")
    ResultDto<Long> editCategoryInfo(@RequestBody CmdShopStepsThreeReq cmdShopStepsThreeReq) throws TException;

    /**
     * 运费设置保存
     */
    @PostMapping(value = "/saveShippingSettings", consumes = "application/json")
    ResultDto<BaseResp> saveShippingSettings(@RequestBody ShippingSettingsSaveReq request) throws TException;

    /**
     * 编辑个人供应商信息
     */
    @PostMapping(value = "/editShopPersonal", consumes = "application/json")
    ResultDto<Long> editShopPersonal(@Validated @RequestBody CmdShopReq cmdShopReq) throws TException;

    /**
     * 编辑店铺供应商信息
     */
    @PostMapping(value = "/editShopEnterprise", consumes = "application/json")
    ResultDto<Long> editShopEnterprise(@Validated @RequestBody CmdShopReq cmdShopReq) throws TException;

    /**
     * 冻结店铺供应商信息
     */
    @PostMapping(value = "/freezeShop", consumes = "application/json")
    ResultDto<Long> freezeShop(@Validated @RequestBody CmdShopStatusReq cmdShopStatusReq) throws TException;

    /**
     * 审核店铺供应商信息
     */
    @PostMapping(value = "/auditing", consumes = "application/json")
    ResultDto<BaseResp> auditing(@Validated @RequestBody CmdShopStatusReq cmdShopStatusReq) throws TException;

    /**
     * 提醒签署合同
     */
    @PostMapping(value = "/sendDepositRemind", consumes = "application/json")
    ResultDto<BaseResp> sendDepositRemind(@RequestBody BaseIdReq baseIdReq) throws TException;

    /**
     * 设置店铺类型
     */
    @PostMapping(value = "/setShopType", consumes = "application/json")
    ResultDto<BaseResp> setShopType(@Validated @RequestBody ShopTypeCmdReq req) throws TException;

    /**
     * 创建店铺二维码
     */
    @PostMapping(value = "/createQR", consumes = "application/json")
    ResultDto<String> createQR(@RequestBody CmdCreateQRReq cmdCreateQRReq) throws TException;

    /**
     * 编辑供应商财务信息
     */
    @PostMapping(value = "/modifyBankInfo", consumes = "application/json")
    ResultDto<Long> modifyBankInfo(@RequestBody CmdShopStepsTwoReq cmdShopStepsTwoReq) throws TException;

    /**
     * 编辑供应商个人信息
     */
    @PostMapping(value = "/modifyBaseInfo", consumes = "application/json")
    ResultDto<Long> modifyBaseInfo(@RequestBody CmdShopStepsOneReq cmdShopBaseReq) throws TException;

    /**
     * 编辑供应商管理员信息
     */
    @PostMapping(value = "/modifyManagerInfo", consumes = "application/json")
    ResultDto<Long> modifyManagerInfo(@RequestBody CmdShopManagerReq cmdShopManagerReq) throws TException;

    /**
     * 获取银行地区
     */
    @GetMapping(value = "/getBankRegion")
    ResultDto<String> getBankRegion() throws TException;

    /**
     * 获取银行地区根据上级ID
     */
    @PostMapping(value = "/getRegionByParentId", consumes = "application/json")
    ResultDto<List<TreeBankRegionResp>> getRegionByParentId(@RequestBody BaseIdReq baseIdReq) throws TException;

    /**
     * 获取地区根据ID
     */
    @PostMapping(value = "/getRegionById", consumes = "application/json")
    ResultDto<List<TreeBankRegionResp>> getRegionsById(@RequestBody BaseIdReq regionId) throws TException;

    /**
     * 更新序号
     */
    @PostMapping(value = "/updateSeq", consumes = "application/json")
    ResultDto<BaseResp> updateSeq(@RequestBody CmdShopSeqReq cmdShopSeqReq) throws TException;

    /**
     * 重新检查是否欠费
     */
    @PostMapping(value = "/checkShopArrears", consumes = "application/json")
    ResultDto<BaseResp> checkShopArrears(@RequestBody BaseIdReq baseIdReq) throws TException;

    /**
     * 注册电子面单平台
     */
    @PostMapping(value = "/setRegisterState", consumes = "application/json")
    ResultDto<Boolean> setRegisterState(@RequestBody BaseIdReq baseIdReq) throws TException;

    /**
     * 获取电子面单平台地址，用于前端跳转
     */
    @GetMapping(value = "/goExpressBills")
    ResultDto<String> goExpressBills(@RequestParam Long shopId) throws TException;

    @PostMapping(value = "/bindMemberPhone", consumes = "application/json")
    ResultDto<BaseResp> bindMemberPhone(CmdBindMemberPhoneReq cmdBindMemberPhoneReq);
}
