package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@ToString
@Data
public class QueryUserInvisibleShopReq extends BaseParamReq {

    /**
     * 用户id
     */
    private Long userId;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfNull(userId, "userId不能为空");
    }
}
