package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

@Data
public class BaseArticleRes extends BaseThriftDto {

    private Long id;

    /**
     * 文章分类id
     */
    private Long categoryId;

    /**
     * 文章分类名称
     */
    private String categoryName;
    /**
     * 标题
     */
    private String title;

    /**
     * icon url
     */
    private String iconUrl;

    /**
     * 添加时间
     */
    private Date addDate;

    /**
     * 排序字段
     */
    private Long displaySequence;

    /**
     * 是否显示
     */
    private Boolean isRelease;

    /**
     * 文章内容
     */
    private String content;

    /**
     * 文章seo标题
     */
    private String seoTitle;

    /**
     * 文档seo详情
     */
    private String seoDescription;

    /**
     * 文章seo关键字
     */
    private String seoKeywords;

}
