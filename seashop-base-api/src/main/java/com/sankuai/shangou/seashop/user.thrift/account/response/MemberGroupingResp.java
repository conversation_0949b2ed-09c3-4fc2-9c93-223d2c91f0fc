package com.sankuai.shangou.seashop.user.thrift.account.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class MemberGroupingResp {
    /**
     * 1个月活跃商家
     */
    private Integer oneMonthActiveMerchant;
    /**
     * 3个月活跃商家
     */
    private Integer threeMonthActiveMerchant;
    /**
     * 6个月活跃商家
     */
    private Integer sixMonthActiveMerchant;
    /**
     * 3个月沉睡商家
     */
    private Integer threeMonthSleepMerchant;
    /**
     * 6个月沉睡商家
     */
    private Integer sixMonthSleepMerchant;
    /**
     * 9个月沉睡商家
     */
    private Integer nineMonthSleepMerchant;
    /**
     * 12个月沉睡商家
     */
    private Integer twelveMonthSleepMerchant;
    /**
     * 24个月沉睡商家
     */
    private Integer twentyFourMonthSleepMerchant;
    /**
     * 今天生日商家
     */
    private Integer todayBirthdayMerchant;
    /**
     * 本月生日商家
     */
    private Integer monthBirthdayMerchant;
    /**
     * 下月月生日商家
     */
    private Integer nextMonthBirthdayMerchant;
    /**
     * 总商家数
     */
    private Integer totalMerchant;
    /**
     * 活跃商家总数
     */
    private Integer activeMerchant;
    /**
     * 沉睡商家总数
     */
    private Integer sleepMerchant;
    /**
     * 生日商家总数
     */
    private Integer birthdayMerchant;
}
