package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryDto;
import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/29 20:26
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class QueryLastCategoryResp extends BaseThriftDto {

    /**
     * 类目列表
     */
    private List<CategoryDto> categoryList;

    /**
     * 需要填写的表单数据
     */
    private List<CategoryApplyFormDto> formList;

}
