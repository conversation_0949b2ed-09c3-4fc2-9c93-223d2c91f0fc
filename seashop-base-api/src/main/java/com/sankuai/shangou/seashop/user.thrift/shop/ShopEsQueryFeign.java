package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryUserInvisibleShopReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopEsQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.ShopEsCombinationResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.UserInvisibleShopResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:供应商ES查询服务类
 */
@FeignClient(name = "himall-base", contextId = "ShopEsQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/shopEs")
public interface ShopEsQueryFeign {

    /**
     * 通过ES查询店铺信息（主要是商城店铺查询使用）
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryByShopEs", consumes = "application/json")
    ResultDto<ShopEsCombinationResp> queryByShopEs(ShopEsQueryReq request) throws TException;

    /**
     * 获取用户不可见店铺列表
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getUserInvisibleShop", consumes = "application/json")
    ResultDto<UserInvisibleShopResp> getUserInvisibleShop(@RequestBody QueryUserInvisibleShopReq request) throws TException;
}
