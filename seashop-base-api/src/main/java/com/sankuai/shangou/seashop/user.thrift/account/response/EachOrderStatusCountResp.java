package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/13 10:51
 */
@Data
public class EachOrderStatusCountResp extends BaseParamReq {

    /**
     * 全部订单数量
     */
    private Integer allOrderCount;

    /**
     * 待支付订单数量
     */
    private Integer waitingForPay;

    /**
     * 待发货订单数量
     */
    private Integer waitingForDelivery;

    /**
     * 待收货订单数量
     */
    private Integer waitingForRecieve;

    /**
     * 待处理售后订单数量
     */
    private Integer refundCount;

    /**
     * 待评论订单数量
     */
    private Integer waitingForComments;
}
