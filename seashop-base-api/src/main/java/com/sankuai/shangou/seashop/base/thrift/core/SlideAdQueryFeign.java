package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.response.SlideAdListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */

@FeignClient(name = "himall-base", contextId = "SlideAdQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/slideAd")
public interface SlideAdQueryFeign {

    /**
     * 查询限时购轮播图
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/queryLimitTimeBuy")
    ResultDto<SlideAdListResp> queryLimitTimeBuy() throws TException;

}
