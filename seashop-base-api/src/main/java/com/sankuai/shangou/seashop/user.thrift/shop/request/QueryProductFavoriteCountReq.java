package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/02/21 9:54ØØ
 */
@Data
public class QueryProductFavoriteCountReq extends BaseParamReq {

    /**
     * 商品ID
     */
    private Long productId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(productId, "商品ID不能为空");
    }
}
