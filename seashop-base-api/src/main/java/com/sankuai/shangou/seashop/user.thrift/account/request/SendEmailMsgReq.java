package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class SendEmailMsgReq extends BaseParamReq {
    /**
     * 标签ID
     */
    private List<Long> labelId;
    /**
     * 是否发送全部
     */
    private Boolean sendAll;
    /**
     * 标题
     */
    private String title;
    /**
     * 内容
     */
    private String content;

}