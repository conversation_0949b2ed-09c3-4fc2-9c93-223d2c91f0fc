package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

@ToString
@Data
public class FreightAreaDetailReq extends BaseParamReq {

    /**
     * 主键
     */
    @PrimaryField
    private Long id;

    /**
     * 运费模板ID
     */
    @ExaminField(description = "运费模板ID")
    private Long freightTemplateId;

    /**
     * 运费区域ID
     */
    @ExaminField(description = "运费区域ID")
    private Long freightAreaId;

    /**
     * 省份ID
     */
    @ExaminField(description = "省份ID")
    private Integer provinceId;

    /**
     * 城市ID
     */
    @ExaminField(description = "城市ID")
    private Integer cityId;

    /**
     * 县区ID
     */
    @ExaminField(description = "县区ID")
    private Integer countyId;

    /**
     * 乡镇ID集合
     */
    @ExaminField(description = "乡镇ID集合")
    private String townIds;

    /**
     * 省份名称
     */
    @ExaminField(description = "省份名称")
    private String provinceName;

    /**
     * 城市名称
     */
    @ExaminField(description = "城市名称")
    private String cityName;

    /**
     * 县区名称
     */
    @ExaminField(description = "县区名称")
    private String countyName;

    /**
     * 乡镇名称集合
     */
    @ExaminField(description = "乡镇名称集合")
    private String townIdsName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
