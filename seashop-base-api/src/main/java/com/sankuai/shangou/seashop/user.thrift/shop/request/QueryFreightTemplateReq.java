package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryFreightTemplateReq {
    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 模板名称的集合
     */
    private List<String> templateNames;

    /**
     * 定价方法(按体积、重量计算）
     */
    private Integer valuationMethod;

    /**
     * 定价方法(按体积、重量计算）
     */
    private List<Integer> valuationMethods;

    public void checkParameter(){
        if(shopId == null){
            throw new IllegalArgumentException("shopId不能为空");
        }
        if (CollectionUtils.isNotEmpty(templateNames)) {
            AssertUtil.throwIfTrue(templateNames.size() >  200, "模板名称不能超过200个");
        }
    }
}
