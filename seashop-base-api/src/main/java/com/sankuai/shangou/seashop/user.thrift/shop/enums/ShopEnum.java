package com.sankuai.shangou.seashop.user.thrift.shop.enums;

/**
 * @description:
 * @author: LXH
 **/
public class ShopEnum {

    public final static Long PLATFORM = 0L;

    public enum BusinessType {
        ENTERPRISE(0, "企业入驻"),
        PERSONAL(1, "个人入驻");
        private Integer code;
        private String desc;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        BusinessType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        //根据code获取枚举
        public static BusinessType getEnumByCode(Integer code) {
            for (BusinessType businessType : BusinessType.values()) {
                if (businessType.getCode().equals(code)) {
                    return businessType;
                }
            }
            return null;
        }
    }

    public enum AuditStatus {
        Default(0, "待审核"),
        Unusable(1, "待审核"),
        Wait<PERSON><PERSON><PERSON>(2, "待审核"),
        WaitPay(3, "待付款"),
        <PERSON><PERSON>(4, "审核拒绝"),
        Wait<PERSON>onfirm(5, "待确认"),
        <PERSON>ze(6, "冻结"),
        Open(7, "开启");
        private Integer code;
        private String desc;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        AuditStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(Integer code) {
            for (AuditStatus auditStatus : AuditStatus.values()) {
                if (auditStatus.getCode().equals(code)) {
                    return auditStatus.getDesc();
                }
            }
            return null;
        }

    }

    public enum CompanyEmployeeCount {
        LessThanFive(1, "5人以下"),
        FiveToTen(2, "5-10人"),
        EleToFifty(3, "11-50人"),
        ElevenToFifty(4, "51-100人"),
        OneHunToTwoHun(5, "101-200人"),
        TwoHunToThreeHun(6, "201-300人"),
        ThreeHunToFiveHun(7, "301-500人"),
        FiveHundredToThousand(8, "501-1000人"),
        MoreThanThousand(9, "1000人以上"),
        ;
        private Integer code;
        private String desc;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        CompanyEmployeeCount(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public enum ShopStage {
        Agreement(1, "许可协议"),
        CompanyInfo(2, "公司信息"),
        FinancialInfo(3, "财务信息"),
        ShopInfo(4, "店铺信息"),
        UploadPayOrder(5, "上传支付凭证"),
        Finish(6, "完成"),
        ;

        private Integer code;
        private String desc;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        ShopStage(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public enum PlatAuditStatus {
        Default(0, "默认"),
        Unusable(1, "待审核"),
        WaitAudit(2, "待审核"),
        Refuse(4, "审核拒绝"),
        Open(7, "审核通过"),
        ;

        private Integer code;
        private String desc;

        public static String getDescByCode(Integer plateStatus) {
            for (PlatAuditStatus platAuditStatus : PlatAuditStatus.values()) {
                if (platAuditStatus.getCode().equals(plateStatus)) {
                    return platAuditStatus.getDesc();
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        PlatAuditStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

    }

    public enum AdapayAuditStatus {
        Default(0, "待审核"),
        Unusable(1, "待审核"),
        AdapayNoRequest(8, "待审核"),
        AdapayPending(9, "审核中"),
        AdapaySuccess(10, "审核通过"),
        AdapayFailed(11, "审核失败"),
        ;

        private Integer code;
        private String desc;

        public static String getDescByCode(Integer adapayStatus) {
            for (AdapayAuditStatus adapayAuditStatus : AdapayAuditStatus.values()) {
                if (adapayAuditStatus.getCode().equals(adapayStatus)) {
                    return adapayAuditStatus.getDesc();
                }
            }
            return null;
        }

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        AdapayAuditStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

    public enum Grade {
        //        1	白金店铺
//2	钻石店铺
//3	套餐5
        PLATINUM(1, "白金店铺"),
        DIAMOND(2, "钻石店铺"),
        PACKAGE(3, "套餐5");
        private Integer code;
        private String desc;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        Grade(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        Grade getByCode(Integer code) {
            for (Grade grade : Grade.values()) {
                if (grade.getCode().equals(code)) {
                    return grade;
                }
            }
            return null;
        }

        public static String getDescByCode(Long code) {
            for (Grade grade : Grade.values()) {
                if (grade.getCode().equals(code.intValue())) {
                    return grade.getDesc();
                }
            }
            return null;
        }
    }

    public enum BankType {
        PUBLIC(1, "对公"),
        PRIVATE(2, "对私");
        private Integer code;
        private String desc;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        BankType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }

}
