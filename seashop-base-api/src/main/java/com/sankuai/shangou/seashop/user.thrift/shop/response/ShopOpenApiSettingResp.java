package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2023-11-30
 */
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ShopOpenApiSettingResp extends BaseThriftDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺编号
     */
    private Long shopId;

    /**
     * app_key
     */
    private String appKey;

    /**
     * app_secret
     */
    private String appSecret;

    /**
     * 增加时间
     */
    private Date addDate;

    /**
     * 最后重置时间
     */
    private Date lastEditDate;

    /**
     * 是否开启
     */
    private Boolean whetherEnable;

    /**
     * 是否已注册
     */
    private Boolean whetherRegistered;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public Date getAddDate() {
        return addDate;
    }

    public void setAddDate(Date addDate) {
        this.addDate = addDate;
    }

    public Long getAddDateLong() {
        return this.date2Long(this.addDate);
    }

    public void setAddDateLong(Long addDate) {
        this.addDate = this.long2Date(addDate);
    }

    public Date getLastEditDate() {
        return lastEditDate;
    }

    public void setLastEditDate(Date lastEditDate) {
        this.lastEditDate = lastEditDate;
    }

    public Long getLastEditDateLong() {
        return this.date2Long(this.lastEditDate);
    }

    public void setLastEditDateLong(Long lastEditDate) {
        this.lastEditDate = this.long2Date(lastEditDate);
    }

    public Boolean getWhetherEnable() {
        return whetherEnable;
    }

    public void setWhetherEnable(Boolean whetherEnable) {
        this.whetherEnable = whetherEnable;
    }

    public Boolean getWhetherRegistered() {
        return whetherRegistered;
    }

    public void setWhetherRegistered(Boolean whetherRegistered) {
        this.whetherRegistered = whetherRegistered;
    }
}
