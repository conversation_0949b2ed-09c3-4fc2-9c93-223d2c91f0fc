package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class QueryJstToExpireShopErpPageReq extends BaseParamReq {

    /**
     * 页码
     */
    public Integer pageNum;

    /**
     * 每页大小
     */
    public Integer pageSize;

    /**
     * 开始时间 对比tokenGetTime+expire
     */
    private Date startDate;

    /**
     * 结束时间 对比tokenGetTime+expire
     */
    private Date endDate;


    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(pageNum, "页码不能为空");
        AssertUtil.throwInvalidParamIfNull(pageSize, "每页大小不能为空");
    }
}
