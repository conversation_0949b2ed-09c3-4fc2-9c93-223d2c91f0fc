package com.sankuai.shangou.seashop.user.thrift.shop.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description：查询供应商发/退货地址表返参
 * @author： liweisong
 * @create： 2023/11/27 9:59
 */
@ToString
@Data
public class QueryShopShipperRespDto extends BaseParamReq {

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 发货人标签
     */
    private String shipperTag;

    /**
     * 发货人姓名
     */
    private String shipperName;

    /**
     * 区域ID
     */
    private Integer regionId;

    /**
     * 地址
     */
    private String address;

    /**
     * 电话
     */
    private String telPhone;

    /**
     * 是否为默认发货地址
     */
    private Boolean defaultSendGoodsFlag;

    /**
     * 是否默认收货地址
     */
    private Boolean defaultGetGoodsFlag;

    /**
     * 微信OpenId
     */
    private String wxOpenId;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 省份名称
     */
    private String provinceName;

    /**
     * 城市名称
     */
    private String cityName;

    /**
     * 县/区名称
     */
    private String countyName;

    /**
     * 乡镇名称
     */
    private String townsNames;
}
