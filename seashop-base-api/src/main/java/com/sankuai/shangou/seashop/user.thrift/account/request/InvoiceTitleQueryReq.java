package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.group.Add;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * @description: 发票抬头添加请求类
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class InvoiceTitleQueryReq extends BaseParamReq {
    @NotNull(message = "用户ID不能为空", groups = {Add.class})
    private Long userId;
    /**
     * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
     */
    @NotNull(message = "发票类型不能为空", groups = {Add.class})
    private Integer invoiceType;
    /**
     * 发票主体类型（1:个人、2:公司）
      */
    private Integer invoiceSubjectType;
}
