package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 商家操作请求入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdMemberReq extends BaseParamReq {
    /**
     * 商家ID
     */
    private Long id;

    /**
     * 密码
     */
    private String password;

    /**
     * 标签ID
     */
    private List<Long> labels;

    /**
     * 操作人IP
     */
    private String operatorIp;
}
