package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@Data
public class BaseSettledRes extends BaseThriftDto {
    private Long id;

    /**
     * 商家类型 0、仅企业可入驻；1、仅个人可入驻；2、企业和个人均可
     */
    private int businessType;


    /**
     * 商家结算类型 0、仅银行账户；1、仅微信账户；2、银行账户及微信账户均可
     */
    private int settlementAccountType;

    /**
     * 试用天数
     */
    private Integer trialDays;

    /**
     * 地址必填 0、非必填；1、必填
     */
    private int isCity;

    /**
     * 数必填 0、非必填；1、必填
     */
    private int isPeopleNumber;

    /**
     * 详细地址必填 0、非必填；1、必填
     */
    private int isAddress;

    /**
     * 营业执照号必填 0、非必填；1、必填
     */
    private int isBusinessLicenseCode;

    /**
     * 经营范围必填 0、非必填；1、必填
     */
    private int isBusinessScope;

    /**
     * 营业执照必填 0、非必填；1、必填
     */
    private int isBusinessLicense;

    /**
     * 机构代码必填 0、非必填；1、必填
     */
    private int isAgencyCode;

    /**
     * 机构代码证必填 0、非必填；1、必填
     */
    private int isAgencyCodeLicense;

    /**
     * 纳税人证明必填 0、非必填；1、必填"
     */
    private int isTaxpayerToProve;

    /**
     * 验证类型 0、验证手机；1、验证邮箱；2、均需验证
     */
    private int companyVerificationType;

    /**
     * 个人姓名必填 0、非必填；1、必填
     */
    private int isSName;

    /**
     * 个人地址必填 0、非必填；1、必填
     */
    private int isSCity;

    /**
     * 个人详细地址必填 0、非必填；1、必填
     */
    private int isSAddress;

    /**
     * 个人身份证必填 0、非必填；1、必填
     */
    private int isSidCard;

    /**
     * 个人身份证上传 0、非必填；1、必填
     */
    private int isSidCardUrl;

    /**
     * 个人验证类型 0、验证手机；1、验证邮箱；2、均需验证
     */
    private int selfVerificationType;

    /**
     * 自定义表单Id（企业）
     */
    private String customFormJson;

    /**
     * 自定义表单Id（个人）
     */
    private String personalCustomFormJson;
}
