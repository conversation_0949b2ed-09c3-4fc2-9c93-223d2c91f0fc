package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;


@FeignClient(name = "himall-base",contextId = "ImageCMDFeign", url = "${himall-base.dev.url:}", path = "/himall-base/image")
public interface ImageCMDFeign {
    /**
     * 新增图片
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/create", consumes = "application/json")
    ResultDto<Long> create(@RequestBody BasePhotoSpaceReq query) throws TException;

    /**
     * 批量新增图片
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/batchCreate", consumes = "application/json")
    ResultDto<Boolean> batchCreate(@RequestBody List<BasePhotoSpaceReq> query) throws TException;

    /**
     * 重命名图片
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/update", consumes = "application/json")
    ResultDto<Boolean> update(@RequestBody UpdatePhotoSpaceReq query) throws TException;

    /**
     * 删除图片
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/delete", consumes = "application/json")
    ResultDto<Boolean> delete(@RequestBody BasePhotoSpaceReq query) throws TException;


    /**
     * 移动分类下图片到指定的分类下
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/moveImageByCateId", consumes = "application/json")
    ResultDto<Boolean> moveImageByCateId(@RequestBody BaseMoveCateImageReq query) throws TException;


    /**
     * 移动图片到指定分类下
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/moveImageById", consumes = "application/json")
    ResultDto<Boolean> moveImageById(@RequestBody BaseMoveImageReq query) throws TException;


    /**
     * 根据id集合删除图片
     * @param idsReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deleteImageByIds", consumes = "application/json")
    ResultDto<Boolean> deleteImageByIds(@RequestBody BaseIdsReq idsReq) throws TException;


    /**
     * 新增图片分类
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/createCategory", consumes = "application/json")
    ResultDto<Long> createCategory(@RequestBody BasePhotoSpaceCategoryReq req) throws TException;


    /**
     * 修改图片分类
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/updateCategory", consumes = "application/json")
    ResultDto<Boolean> updateCategory(@RequestBody BasePhotoSpaceCategoryReq req) throws TException;

    /**
     * 删除图片分类（批量）
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deleteCategory", consumes = "application/json")
    ResultDto<Boolean> deleteCategory(@RequestBody BaseIdsReq req) throws TException;


}
