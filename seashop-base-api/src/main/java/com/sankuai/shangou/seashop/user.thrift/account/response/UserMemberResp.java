package com.sankuai.shangou.seashop.user.thrift.account.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/11/16
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserMemberResp {
    /**
     * 商家ID
     */
    private Long id;

    /**
     * 商家是否已注销，0否1是
     */
    private Boolean whetherLogOut;

    /**
     * 商家是否已冻结，0否1是
     */
    private Boolean disabled;
}
