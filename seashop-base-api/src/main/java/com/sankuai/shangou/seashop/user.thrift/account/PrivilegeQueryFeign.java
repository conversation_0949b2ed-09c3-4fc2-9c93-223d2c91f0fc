package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryPrivilegeReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryUserPrivilegeReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.PrivilegeRespList;
import com.sankuai.shangou.seashop.user.thrift.account.response.UserPrivilegeResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 权限查询服务类
 */
@FeignClient(name = "himall-base", contextId = "PrivilegeQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/privilege")
public interface PrivilegeQueryFeign {

    /**
     * 查询权限列表
     * @return 删除的权限ID
     */
    @PostMapping(value = "/queryPrivilegeList", consumes = "application/json")
    ResultDto<PrivilegeRespList> queryPrivilegeList(@RequestBody QueryPrivilegeReq queryPrivilegePageReq) throws TException;


    /**
     * 获取用户权限数据
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryUserPrivilege", consumes = "application/json")
    ResultDto<UserPrivilegeResp> queryUserPrivilege(@RequestBody QueryUserPrivilegeReq req) throws TException;
}
