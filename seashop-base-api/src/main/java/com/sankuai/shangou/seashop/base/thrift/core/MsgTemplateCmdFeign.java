package com.sankuai.shangou.seashop.base.thrift.core;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.BindOpenIdReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.CmdWxAppletFormDataReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.MsgTemplateAppletReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.MsgTemplateDataReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveMsgTemplateReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SendAppletMessageReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SendAppletReq;

/**
 * @author： liweisong
 * @create： 2023/11/29 16:54
 */
@FeignClient(name = "himall-base", contextId = "MsgTemplateCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/msgTemplate")
public interface MsgTemplateCmdFeign {

    /**
     * 平台-小程序-消息配置-保存
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveMsgTemplate", consumes = "application/json")
    ResultDto<BaseResp> saveMsgTemplate(@RequestBody SaveMsgTemplateReq request) throws TException;

    /**
     * 平台-小程序-消息配置-保存(上面的id跟秘钥)
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveMsgTemplateApplet", consumes = "application/json")
    ResultDto<BaseResp> saveMsgTemplateApplet(@RequestBody MsgTemplateAppletReq request) throws TException;

    /**
     * 平台-小程序-消息配置-保存(模板)
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveMsgTemplateData", consumes = "application/json")
    ResultDto<BaseResp> saveMsgTemplateData(@RequestBody MsgTemplateDataReq request) throws TException;

    /**
     * 平台-小程序-消息配置-获取模版ID
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getAppletSubscribeTmplate")
    ResultDto<BaseResp> getAppletSubscribeTmplate() throws TException;

    /**
     * 小程序发送通知
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/sendAppletMessageByTemplate", consumes = "application/json")
    ResultDto<BaseResp> sendAppletMessageByTemplate(@RequestBody SendAppletMessageReq request) throws TException;

    /**
     * 小程序发送通知(包装)
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/sendAppletMessage", consumes = "application/json")
    ResultDto<BaseResp> sendAppletMessage(@RequestBody SendAppletReq request) throws TException;


    /**
     * 订单模版绑定的事件表
     * @param cmdWxAppletFormDataReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/insertWxAppletFormData", consumes = "application/json")
    ResultDto<BaseResp> insertWxAppletFormData(@RequestBody CmdWxAppletFormDataReq cmdWxAppletFormDataReq) throws TException;

    /**
     * 绑定OpenId
     * @param bindOpenIdReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/bindOpenId", consumes = "application/json")
    ResultDto<BaseResp> bindOpenId(@RequestBody BindOpenIdReq bindOpenIdReq) throws TException;
}
