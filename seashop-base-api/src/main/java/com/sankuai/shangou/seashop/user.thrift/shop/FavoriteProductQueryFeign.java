package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFavoriteProductReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFavoriteProductStatusReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryProductFavoriteCountReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFavoriteProductResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFavoriteProductStatusResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryProductFavoriteCountResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description：商品收藏查询类接口
 * @author： liweisong
 * @create： 2023/11/28 9:27
 */

/**
 * 商品收藏查询类接口
 */
@FeignClient(name = "himall-base", contextId = "FavoriteProductQueryFeign",  url = "${himall-base.dev.url:}",path = "/himall-base/shop/favoriteProduct")
public interface FavoriteProductQueryFeign {
    /**
     * 商家中心-商品关注-分页查询
     * @param queryFavoriteProductReq 商品关注-搜索功能入参
     * @return 查询入参
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/pageFavoriteProduct", consumes = "application/json")
    ResultDto<BasePageResp<QueryFavoriteProductResp>> pageFavoriteProduct(@RequestBody QueryFavoriteProductReq queryFavoriteProductReq) throws TException;

    /**
     * 查询商品收藏状态
     * @param request 商品收藏状态查询入参
     * @return 查询入参
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/queryFavoriteProductStatus", consumes = "application/json")
    ResultDto<QueryFavoriteProductStatusResp> queryFavoriteProductStatus(@RequestBody QueryFavoriteProductStatusReq request) throws TException;

    /**
     * 查询商品收藏数量
     * @param request 商品收藏数量查询入参
     * @return 查询入参
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/queryFavoriteProductCount", consumes = "application/json")
    ResultDto<QueryProductFavoriteCountResp> queryFavoriteProductCount(@RequestBody QueryProductFavoriteCountReq request) throws TException;

}
