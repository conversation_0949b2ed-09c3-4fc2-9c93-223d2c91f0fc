package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/04 14:51
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryRestrictedRegionResp extends BaseParamReq {
    /**
     * 主键
     */
    private Long id;

    /**
     * 运费模板名称
     */
    private String name;

    /**
     * 宝贝发货地
     */
    private Integer sourceAddress;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 是否商家负责运费
     */
    private Integer whetherFree;

    /**
     * 定价方法(按体积、重量计算）
     */
    private Integer valuationMethod;

    /**
     * 运送类型（物流、快递）
     */
    private Integer shippingMethod;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 非销售区域是否隐藏
     */
    private Boolean nonSalesAreaHide;

    /**
     * 限制区域id的集合
     */
    private List<Long> restrictedRegionIds;
}
