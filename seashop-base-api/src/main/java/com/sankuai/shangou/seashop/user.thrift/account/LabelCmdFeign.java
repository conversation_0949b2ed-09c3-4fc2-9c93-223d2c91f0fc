package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.CmdLabelReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 标签操作服务类
 */
@FeignClient(name = "himall-base", contextId = "LabelThriftCmdService", url = "${himall-base.dev.url:}", path = "/himall-base/account/label")
public interface LabelCmdFeign {

    /**
     * 添加标签
     *
     * @param cmdLabelReq 请求体
     * @return 添加的标签ID
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/addLabel", consumes = "application/json")
    ResultDto<Long> addLabel(@RequestBody CmdLabelReq cmdLabelReq) throws TException;

    /**
     * 编辑标签
     *
     * @param cmdLabelReq 请求体
     * @return 编辑的标签ID
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/editLabel", consumes = "application/json")
    ResultDto<Long> editLabel(@RequestBody CmdLabelReq cmdLabelReq) throws TException;

    /**
     * 删除标签
     *
     * @param cmdLabelReq 请求体
     * @return 删除的标签ID
     * @throws TException RPC调用异常
     */
    @PostMapping(value = "/deleteLabel", consumes = "application/json")
    ResultDto<Long> deleteLabel(@RequestBody CmdLabelReq cmdLabelReq) throws TException;


}
