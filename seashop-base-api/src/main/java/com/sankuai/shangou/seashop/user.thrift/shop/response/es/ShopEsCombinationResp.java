package com.sankuai.shangou.seashop.user.thrift.shop.response.es;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.*;

import java.util.Collections;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class ShopEsCombinationResp extends BaseThriftDto {

    /**
     * 店铺列表
     */
    private BasePageResp<ShopEsResp> shopList;

    /**
     * 店铺品牌列表
     */
    private List<ShopBrandEsResp> shopBrandList;

    /**
     * 店铺经营类目列表
     */
    private List<BusinessCategoryEsResp> businessCategoryList;

    /**
     * 店铺品牌id列表
     */
    private List<Long> shopBrandIds;

    /**
     * 店铺经营类目id列表
     */
    private List<Long> categoryIds;

    public static ShopEsCombinationResp defaultEmpty() {
        ShopEsCombinationResp resultBo = new ShopEsCombinationResp();
        resultBo.setShopBrandList(CollUtil.newArrayList());
        resultBo.setBusinessCategoryList(CollUtil.newArrayList());
        resultBo.setShopBrandIds(Collections.emptyList());
        resultBo.setCategoryIds(Collections.emptyList());
        return resultBo;
    }
}
