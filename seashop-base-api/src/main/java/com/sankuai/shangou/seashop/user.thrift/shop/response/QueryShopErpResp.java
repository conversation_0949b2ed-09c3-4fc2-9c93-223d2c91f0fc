package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.baomidou.mybatisplus.annotation.TableField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/11/28 15:05
 */
@Data
public class QueryShopErpResp extends BaseParamReq {
    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 开启erp类型 0未开启 1旺店通 2聚水潭 3网店管家 4吉客云
     */
    private Integer erpType;

    /**
     * 聚水潭授权 0未授权 1已授权
     */
    private Boolean jstStatus;

    /**
     * 聚水潭授权url
     */
    private String jstUrl;

    /**
     * 聚水潭授权url创建时间
     */
    private Date jstUrlCreateTime;

    /**
     * 聚水潭授权码
     */
    private String jstCode;

    /**
     * 聚水潭授权码获得时间
     */
    private Date jstCodeGetTime;

    /**
     * 聚水潭访问令牌
     */
    private String jstAccessToken;

    /**
     * 聚水潭访问令牌多少秒后过期
     */
    private Integer jstAccessTokenExpires;

    /**
     * 聚水潭更新令牌
     */
    private String jstRefreshToken;

    /**
     * 聚水潭令牌获取时间
     */
    private Date jstTokenGetTime;

    /**
     * 聚水潭店铺编号
     */
    private String jstShopId;

    /**
     * 聚水潭公司编号
     */
    private String jstCoId;

    /**
     * 菠萝派供应商token
     */
    private String blpToken;

    /**
     * 旺店通访问令牌
     */
    private String wdtToken;

    /**
     * 是否发送短信
     */
    private Boolean whetherSendSms;

    @TableField("wdt_sid")
    private String wdtSid;

    /**
     * 旺店通开放应用 appkey
     */
    private String wdtAppKey;

    /**
     * 旺店通店铺编码
     */
    private String wdtShopNo;

    /**
     * 旺店通开放应用 appSecret
     */
    private String wdtAppSecret;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;


    public Long getJstUrlCreateTimeLong() {
        return this.date2Long(this.jstUrlCreateTime);
    }

    public void setJstUrlCreateTimeLong(Long jstUrlCreateTime) {
        this.jstUrlCreateTime = this.long2Date(jstUrlCreateTime);
    }


    public Long getJstCodeGetTimeLong() {
        return this.date2Long(this.jstCodeGetTime);
    }

    public void setJstCodeGetTimeLong(Long jstCodeGetTime) {
        this.jstCodeGetTime = this.long2Date(jstCodeGetTime);
    }


    public Long getJstTokenGetTimeLong() {
        return this.date2Long(this.jstTokenGetTime);
    }

    public void setJstTokenGetTimeLong(Long jstTokenGetTime) {
        this.jstTokenGetTime = this.long2Date(jstTokenGetTime);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }

    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }

    public Long getUpdateTimeLong() {
        return this.date2Long(this.updateTime);
    }

    public void setUpdateTimeLong(Long updateTime) {
        this.updateTime = this.long2Date(updateTime);
    }

}
