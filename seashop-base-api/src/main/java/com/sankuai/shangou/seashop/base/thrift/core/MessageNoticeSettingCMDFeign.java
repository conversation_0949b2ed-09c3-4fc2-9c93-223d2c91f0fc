package com.sankuai.shangou.seashop.base.thrift.core;


import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseMessageNoticeSettingReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "himall-base",contextId = "MessageNoticeSettingCMDFeign",url = "${himall-base.dev.url:}",  path = "/himall-base/messageNoticeSetting")
public interface MessageNoticeSettingCMDFeign {

    /**
     * 设置某种类型的消息通知
     * @param messageNoticeSettingReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/setMessageNoticeSetting", consumes = "application/json")
    ResultDto<Boolean> setMessageNoticeSetting(@RequestBody BaseMessageNoticeSettingReq messageNoticeSettingReq) throws TException;


}
