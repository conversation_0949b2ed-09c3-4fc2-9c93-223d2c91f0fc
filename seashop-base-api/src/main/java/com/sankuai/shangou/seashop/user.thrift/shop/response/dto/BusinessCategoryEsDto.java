package com.sankuai.shangou.seashop.user.thrift.shop.response.dto;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BusinessCategoryEsDto extends BaseThriftDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 类目名称
     */
    private String categoryName;

    /**
     * 类目全名(包含上级)
     */
    private String fullCategoryName;

    /**
     * 是否冻结
     */
    private Boolean whetherFrozen;
}
