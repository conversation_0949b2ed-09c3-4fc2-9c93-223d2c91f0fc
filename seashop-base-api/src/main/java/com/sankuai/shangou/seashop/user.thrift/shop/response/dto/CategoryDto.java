package com.sankuai.shangou.seashop.user.thrift.shop.response.dto;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/11/29 20:26
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CategoryDto extends BaseThriftDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 类目图标
     */
    private String icon;

    /**
     * 排序
     */
    private Long displaySequence;

    /**
     * 上级类目id
     */
    private Long parentCategoryId;

    /**
     * 分佣比例
     */
    private BigDecimal commissionRate;

    /**
     * 类目的深度
     */
    private Integer depth;

    /**
     * 类目的路径（以|分离）
     */
    private String path;

    /**
     * 类目全路径
     */
    private String fullCategoryName;

    /**
     * 保证金
     */
    private BigDecimal cashDeposit;
}
