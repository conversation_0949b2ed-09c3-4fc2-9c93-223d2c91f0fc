package com.sankuai.shangou.seashop.user.thrift.account.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class InvoiceTitleResp {
    /**
     * 发票抬头ID
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
     */
    private Integer invoiceType;

    /**
     * 抬头名称
     */
    private String name;

    /**
     * 税号
     */
    private String code;

    /**
     * 发票明细
     */
    private String invoiceContext;

    /**
     * 注册地址
     */
    private String registerAddress;

    /**
     * 注册电话
     */
    private String registerPhone;

    /**
     * 开户银行
     */
    private String bankName;

    /**
     * 银行帐号
     */
    private String bankNo;

    /**
     * 收票人姓名
     */
    private String realName;

    /**
     * 收票人手机号
     */
    private String cellPhone;

    /**
     * 收票人邮箱
     */
    private String email;

    /**
     * 收票人地址区域ID
     */
    private Integer regionId;

    /**
     * 收票人详细地址
     */
    private String address;

    /**
     * 是否默认
     */
    private Boolean whetherDefault;

    /**
     * 发票主体类型（1:个人、2:公司）
     */
    private Integer invoiceSubjectType;
}
