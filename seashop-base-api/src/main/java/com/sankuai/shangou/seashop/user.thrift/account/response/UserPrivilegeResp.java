package com.sankuai.shangou.seashop.user.thrift.account.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.ArrayList;
import java.util.List;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UserPrivilegeResp {

    /**
     * 标签信息列表
     */
    private List<PrivilegeResp> privilegeRespList = new ArrayList<>();

    /**
     * 按钮权限
     */
    private List<String> buttonPrivilegeList = new ArrayList<>();
}
