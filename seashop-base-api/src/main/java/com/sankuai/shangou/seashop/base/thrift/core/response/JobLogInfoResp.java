package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2023-11-29
 */
@Getter
@Setter
public class JobLogInfoResp extends BaseThriftDto {

    private static final long serialVersionUID = 1L;

    private Long id;

    private Long jobId;

    private Date triggerTime;

    /**
     * 调度状态
     */
    private Integer triggerCode;


}
