package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.BasePhotoSpaceQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BasePhotoSpaceCategoryRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BasePhotoSpaceRes;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "himall-base",contextId = "ImageQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/image")
public interface ImageQueryFeign {

    /**
     * 获取图片列表（分页）
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/query", consumes = "application/json")
    ResultDto<BasePageResp<BasePhotoSpaceRes>> query(@RequestBody BasePhotoSpaceQueryReq query) throws TException;

    /**
     * 获取已审核的图片列表（分页）
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryAudit", consumes = "application/json")
    ResultDto<BasePageResp<BasePhotoSpaceRes>> queryAudit(@RequestBody BasePhotoSpaceQueryReq query) throws TException;


    /**
     * 获取所有图片分类
     * @param shopId
     * @return
     * @throws TException
     */
    @GetMapping(value = "/queryCategorys")
    ResultDto<List<BasePhotoSpaceCategoryRes>> queryCategorys(@RequestParam("shopId") Long shopId) throws TException;

}
