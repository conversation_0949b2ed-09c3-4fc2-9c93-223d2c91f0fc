package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 专属商家入参
 * @author: liweisong
 * @date: 2023-11-17
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class DeleteExclusiveMemberReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    @ExaminField(description = "店铺ID")
    private Long shopId;


    public void checkParameter(){
        if(id == null){
            throw new IllegalArgumentException("id不能为空");
        }
    }

}
