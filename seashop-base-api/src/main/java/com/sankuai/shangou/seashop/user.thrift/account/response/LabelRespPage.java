package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 标签信息分页
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class LabelRespPage {
    /**
     * 标签信息列表
     */
    private BasePageResp<LabelResp> pageResp;
}
