package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @description：删除商品收藏
 * @author： liweisong
 * @create： 2023/11/28 9:30
 */
@ToString
@Data
public class DeleteFavoriteProductReq extends BaseParamReq {


    /**
     * 主键ID集合
     */
    private List<Long> ids;

    /**
     * 商品id
     */
    private String productId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 商品id集合
     */
    private List<String> productIdList;
}
