package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.List;

@Data
public class CmdShopStepsThreeReq extends BaseParamReq {
    /**
     * id
     */
    private Long shopId;
    /**
     * 店铺名称
     */
    private String shopName;
    /**
     * 店铺等级
     */
    private Integer shopGrade;
    /**
     * 选择类目
     */
    private List<Long> categories;
    /**
     * 所有关联自定义表单的分类数据
     */
    private List<CmdBusinessCategoryFormReq> customFormCategory;
    /**
     * 店铺logo
     */
    private String formImage;
    /**
     * 店铺banner
     */
    private String formFile;
}
