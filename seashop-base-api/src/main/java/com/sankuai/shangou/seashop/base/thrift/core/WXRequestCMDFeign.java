package com.sankuai.shangou.seashop.base.thrift.core;


import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWXMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.JsApiSignatureReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveWXAccountReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.JsApiSignatureRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.WXAccountResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "himall-base", contextId = "WXRequestCMDFeign", url = "${himall-base.dev.url:}", path = "/himall-base/WXRequest")
public interface WXRequestCMDFeign {

    /**
     * 获取微信公众号token
     * @param
     * @return
     * @throws TException
     */
    @PostMapping(value = "/getAccessToken", consumes = "application/json")
    ResultDto<String> getAccessToken() throws TException;

    @PostMapping(value = "/getJsApiSignature", consumes = "application/json")
    ResultDto<JsApiSignatureRes> getJsApiSignature(@RequestBody JsApiSignatureReq request) throws TException;

}
