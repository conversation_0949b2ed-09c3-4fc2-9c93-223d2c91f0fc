package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveOrderSettingReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description：卖家中心，交易设置
 * @author： liweisong
 * @create： 2023/11/27 16:01
 */
@FeignClient(name = "himall-base", contextId = "OrderSettingCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/orderSetting")
public interface OrderSettingCmdFeign {

    /**
     * 供应商-交易-交易设置(新增或者修改)
     * @param saveOrderSettingReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/addOrUpdateOrderSetting",consumes = "application/json")
    ResultDto<BaseResp> addOrUpdateOrderSetting(@RequestBody SaveOrderSettingReq saveOrderSettingReq) throws TException;

}
