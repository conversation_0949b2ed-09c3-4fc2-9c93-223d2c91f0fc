package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryRefundReasonResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:17
 */
@FeignClient(name = "himall-base", contextId = "RefundReasonQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/refundReason")
public interface RefundReasonQueryFeign {

    /**
     * 平台-交易-交易设置-售后原因（查询）
     * @return
     * @throws TException
     */
    @GetMapping(value = "/queryRefundReasonList")
    ResultDto<QueryRefundReasonResp> queryRefundReasonList() throws TException;
}
