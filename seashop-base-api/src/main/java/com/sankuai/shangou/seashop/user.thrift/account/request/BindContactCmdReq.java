package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class BindContactCmdReq extends BaseParamReq {
    private Long id;

    /**
     * 旧的联系方式
     */
    private String oldContact;

    /**
     * 新的的联系方式
     */
    private String contact;

    /**
     * 新的的联系方式 0email 1phone
     */
    private Integer usertype;

    /**
     * 验证码
     */
    private String code;

}
