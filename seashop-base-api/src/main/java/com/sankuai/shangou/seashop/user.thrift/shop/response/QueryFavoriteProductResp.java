package com.sankuai.shangou.seashop.user.thrift.shop.response;

import java.math.BigDecimal;
import java.util.Date;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import lombok.Data;
import lombok.ToString;

/**
 * @author： liweisong
 * @create： 2023/11/28 10:31
 */
@ToString
@Data
public class QueryFavoriteProductResp extends BaseParamReq {

    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商品ID
     */
    private String productId;

    /**
     * 收藏日期
     */
    private Date date;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 审核状态 1-审核中 2-审核通过 3-审核不通过 4-违规下架
     */
    private Integer auditStatus;

    /**
     * 审核状态
     */
    private String auditStatusName;

    /**
     * 销售状态
     */
    private Integer saleStatus;

    /**
     * 销售状态名称
     */
    private String saleStatusName;

    /**
     * 商品主图
     */
    private String mainImagePath;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 评价总数
     */
    private Integer totalCount;

    /**
     * 最少销售价
     */
    private BigDecimal minSalePrice;

    /**
     * 是否下架 0-未下架 1-已下架
     */
    private Integer offShelf;

    /**
     * 是否有库存 0-无库存 1-有库存
     */
    private Integer hasStock;

    /**
     * 库存状态
     */
    private Integer stockStatus;

    /**
     * 库存状态 0-已售罄 1-已下架，2审核通过现货
     */
    private String stockStatusName;

    /**
     * 是否单价格商品
     */
    private Boolean singlePrice;
}
