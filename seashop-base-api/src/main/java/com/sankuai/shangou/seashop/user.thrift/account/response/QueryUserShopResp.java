package com.sankuai.shangou.seashop.user.thrift.account.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryUserShopResp {

    /**
     * 店铺是否开启专属商家
     */
    private Boolean izOpenExclusiveMember;

    /**
     * 所查商家ID是否该店铺的专属商家中的一个
     */
    private Boolean flag;
}
