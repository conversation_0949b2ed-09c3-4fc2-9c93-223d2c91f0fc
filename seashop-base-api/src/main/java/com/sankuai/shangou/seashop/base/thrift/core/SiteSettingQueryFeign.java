package com.sankuai.shangou.seashop.base.thrift.core;


import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.enums.SystemStyleEnum;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseCustFormQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.*;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "himall-base", contextId = "SiteSettingQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/siteSetting")
public interface SiteSettingQueryFeign {
    /**
     * 获取站点设置
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getSetting")
    ResultDto<BaseSitSettingRes> getSetting() throws TException;


    /**
     * 获取入驻设置
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getSettled")
    ResultDto<BaseSettledRes> getSettled() throws TException;

    /**
     * 获取协议
     *
     * @param agreementType
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getAgreement")
    ResultDto<BaseAgreementRes> getAgreement(@RequestParam int agreementType) throws TException;


    /**
     * 获取所有协议
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getAllAgreement")
    ResultDto<BaseAllAgreementRes> getAllAgreement() throws TException;

    /**
     * 获取店铺设置
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getShopSettings")
    ResultDto<BaseShopSitSettingRes> getShopSettings() throws TException;


    /**
     * 获取自定义表单
     *
     * @param formId
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getCustomForm")
    ResultDto<BaseCustomFormRes> getCustomForm(@RequestParam long formId) throws TException;


    /**
     * 获取自定义表单（分页）
     *
     * @param query
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryCustomFormWithPage", consumes = "application/json")
    ResultDto<BasePageResp<BaseCustomFormRes>> queryCustomFormWithPage(@RequestBody BaseCustFormQueryReq query) throws TException;

    /**
     * 获取app设置
     *
     * @return
     */
    @GetMapping(value = "/getAppSettings")
    ResultDto<AppSitSettingRes> getAppSettings();

    /**
     * 获取商品设置
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getProductSettings")
    ResultDto<ProductSettingResp> getProductSettings() throws TException;

    /**
     * 根据key获取value
     *
     * @param key
     * @return
     * @throws TException
     */
    @GetMapping(value = "/querySettingsValueByKey")
    ResultDto<String> querySettingsValueByKey(@RequestParam String key) throws TException;

    /**
     * 短信配置
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/querySmsSetting")
    ResultDto<SmsSettingRes> querySmsSetting() throws TException;

    /**
     * 短信配置
     *
     * @param styleEnum
     * @return
     * @throws TException
     */
    @GetMapping(value = "/queryShopStyle")
    ResultDto<String> queryShopStyle(@RequestParam SystemStyleEnum styleEnum) throws TException;

    /**
     * 物流配置
     *
     * @return
     * @throws TException
     */
    @GetMapping(value = "/queryExpressConfig")
    ResultDto<ExpressConfigRes> queryExpressConfig() throws TException;

    /**
     * 获取官方水印
     *
     * @param systemStyleEnum
     * @return
     */
    @GetMapping(value = "/queryOfficeMark")
    ResultDto<String> queryOfficeMark(@RequestParam SystemStyleEnum systemStyleEnum);
}
