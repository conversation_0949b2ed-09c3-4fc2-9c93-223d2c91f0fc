package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.group.Update;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdShopReq extends BaseParamReq {
    /**
     * 店铺ID
     */
    @NotNull(message = "店铺ID为必填项" ,groups = {Update.class})
    @PrimaryField
    private Long shopId;
    /**
     * 店铺名称
     */
    @NotBlank(message = "店铺名称为必填项")
    @Size(max = 20, message = "店铺名称最多20个字符")
    @ExaminField(description = "店铺名称")
    private String shopName;
    /**
     * 公司信息
     */
    @ExaminField(description = "公司名称")
    private String companyName;
    @ExaminField(description = "公司所在地ID")
    private Integer companyRegionId;
    @ExaminField(description = "公司详细地址")
    private String companyAddress;
    @ExaminField(description = "公司联系方式")
    private String companyPhone;
    /**
     * 法人信息(店铺个人信息)
     */
    @ExaminField(description = "公司法定代表人")
    private String contactsName;
    /**
     * 身份证信息
     */
    @ExaminField(description = " 身份证号")
    private String idCard;
    private Date idCardStartDate;
    private Date idCardEndDate;
    @ExaminField(description = "身份证正面照")
    private String idCardUrl;
    @ExaminField(description = "身份证反面照")
    private String idCardUrl2;
    /**
     * 营业执照信息
     */
    @ExaminField(description = "营业执照号")
    private String businessLicenseNumber;
    @ExaminField(description = "营业执照URL")
    private String businessLicenseNumberPhoto;
    @ExaminField(description = "营业执照所在地")
    private Integer businessLicenseRegionId;
    private Date businessLicenseStart;
    private Date businessLicenseEnd;
    @ExaminField(description = "法定经营范围")
    private String businessSphere;
    @ExaminField(description = "开户行许可证URL")
    private String bankPhoto;
    /**
     * 店铺账户信息
     */
    @ExaminField(description = "店铺账户")
    private String shopAccount;
    @ExaminField(description = "店铺账户姓名")
    private String accountName;
    @ExaminField(description = "联系人手机")
    private String contactsPhone;
    @ExaminField(description = "联系人邮箱")
    private String contactsEmail;
    @ExaminField(description = "头像")
    private String logo;
    @ExaminField(description = "入驻表单")
    private String formData;
    @ExaminField(description = "公司法人")
    private String legalPerson;
    @FieldDoc(description = "身份证有效期类型")
    private Integer idCardExpireType;
}
