package com.sankuai.shangou.seashop.base.thrift.core;


import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseWXMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveWXAccountReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.WXAccountResp;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "himall-base", contextId = "WXMenuCMDFeign", url = "${himall-base.dev.url:}", path = "/himall-base/WXMenu")
public interface WXMenuCMDFeign {

    /**
     * 平台-设置-公众号设置
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveWXAccount", consumes = "application/json")
    ResultDto<BaseResp> saveWXAccount(@RequestBody SaveWXAccountReq request) throws TException;

    @GetMapping(value = "/getWXAccount", consumes = "application/json")
    ResultDto<WXAccountResp> getWXAccount() throws TException;

    /**
     * 设置-公众号菜单设置（新增）
     * @param wxMenuReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/create", consumes = "application/json")
    ResultDto<Integer> create(@RequestBody BaseWXMenuReq wxMenuReq) throws TException;

    /**
     * 设置-公众号菜单设置（修改）
     * @param wxMenuReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/update", consumes = "application/json")
    ResultDto<Boolean> update(@RequestBody BaseWXMenuReq wxMenuReq) throws TException;

    /**
     * 设置-公众号菜单设置（删除）
     * @param baseIdReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/delete", consumes = "application/json")
    ResultDto<Boolean> delete(@RequestBody BaseIdReq baseIdReq) throws TException;

    /**
     * 设置-公众号菜单设置（同步）
     * @return
     * @throws TException
     */
    @PostMapping(value = "/syncWXMenu", consumes = "application/json")
    ResultDto<Boolean> syncWXMenu() throws TException;
}
