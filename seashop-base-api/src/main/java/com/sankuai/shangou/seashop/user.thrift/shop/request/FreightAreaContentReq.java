package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@ToString
@Data
public class FreightAreaContentReq extends BaseParamReq {

    /**
     * 主键
     */
    @PrimaryField
    private Long id;

    /**
     * 运费模板ID
     */
    @ExaminField(description = "运费模板ID")
    private Long freightTemplateId;

    /**
     * 地区选择
     */
    @ExaminField(description = "地区选择")
    private String areaContent;

    /**
     * 首笔单元计量
     */
    @ExaminField(description = "首笔单元计量")
    private Integer firstUnit;

    /**
     * 首笔单元费用
     */
    @ExaminField(description = "首笔单元费用")
    private BigDecimal firstUnitMonry;

    /**
     * 递增单元计量
     */
    @ExaminField(description = "递增单元计量")
    private Integer accumulationUnit;

    /**
     * 递增单元费用
     */
    @ExaminField(description = "递增单元费用")
    private BigDecimal accumulationUnitMoney;

    /**
     * 是否为默认
     */
    @ExaminField(description = "是否为默认")
    private Boolean whetherDefault;

    private Date createTime;

    private Date updateTime;

    /**
     * 指定可配送区域的详细地址
     */
    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.user.dao.shop.domain.FreightAreaDetail", description = "指定可配送区域的详细地址")
    private List<FreightAreaDetailReq> detailReqList;
}
