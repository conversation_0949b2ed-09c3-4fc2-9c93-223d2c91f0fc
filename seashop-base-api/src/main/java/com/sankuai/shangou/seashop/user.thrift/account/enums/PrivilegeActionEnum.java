package com.sankuai.shangou.seashop.user.thrift.account.enums;


/**
 * @description:
 * @author: LXH
 **/
public enum PrivilegeActionEnum {
    MENU_PAGE("menu_page", "菜单页面"),
    MENU("menu", "菜单"),
    AUTHORITY_SUB_PAGE("authority_sub_page", "权限子页面"),
    SUB_PAGE("sub_page", "子页面");

    private final String value;
    private final String name;

    PrivilegeActionEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public String getName() {
        return name;
    }
}
