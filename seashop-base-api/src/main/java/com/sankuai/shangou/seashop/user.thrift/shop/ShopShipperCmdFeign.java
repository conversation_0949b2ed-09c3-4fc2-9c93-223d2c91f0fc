package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AddShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateShopShipperDefaultReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateShopShipperReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description：供应商发/退货地址
 * @author： liweisong
 * @create： 2023/11/27 9:13
 */

/**
 * 供应商发/退货地址新增修改删除接口
 */
@FeignClient(name = "himall-base", contextId = "ShopShipperCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/shopShipper")
public interface ShopShipperCmdFeign {

    /**
     * 保存供应商发/退货地址
     *
     * @param addShopShipperReq 供应商发/退货地址信息
     */
    @PostMapping(value = "/addShopShipper", consumes = "application/json")
    ResultDto<BaseResp> addShopShipper(@RequestBody AddShopShipperReq addShopShipperReq) throws TException;

    /**
     * 修改供应商发/退货地址
     *
     * @param updateShopShipperReq 修改供应商发/退货地址信息
     */
    @PostMapping(value = "/updateShopShipper", consumes = "application/json")
    ResultDto<BaseResp> updateShopShipper(@RequestBody UpdateShopShipperReq updateShopShipperReq) throws TException;

    /**
     * 修改供应商发/退货地址默认值
     *
     * @param updateShopShipperDefaultReq 修改供应商发/退货地址信息默认值
     */
    @PostMapping(value = "/updateShopShipperDefault", consumes = "application/json")
    ResultDto<BaseResp> updateShopShipperDefault(@RequestBody UpdateShopShipperDefaultReq updateShopShipperDefaultReq) throws TException;

    /**
     * 删除收货地址
     *
     * @param deleteShopShipperReq 待删除的供应商发/退货地址ID
     */
    @PostMapping(value = "/deleteShopShipper", consumes = "application/json")
    ResultDto<BaseResp> deleteShopShipper(@RequestBody DeleteShopShipperReq deleteShopShipperReq) throws TException;
}
