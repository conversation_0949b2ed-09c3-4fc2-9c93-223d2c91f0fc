package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 开启专属商家模式入参
 * @author: liweisong
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdUserShopReq extends BaseParamReq {

    @ExaminField(description = "店铺ID")
    private Long shopId;

    @ExaminField(description = "是否开启专属商家")
    private Boolean whetherOpenExclusiveMember;

    public void checkParameter(){
        if(shopId == null){
            throw new IllegalArgumentException("shopId不能为空");
        }
        if(whetherOpenExclusiveMember == null){
            throw new IllegalArgumentException("whetherOpenExclusiveMember不能为空");
        }
    }
}
