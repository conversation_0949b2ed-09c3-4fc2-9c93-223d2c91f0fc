package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

/**
 * @author： liweisong
 * @create： 2023/12/14 9:34
 */
@ToString
@Data
public class CopyFreightReq extends BaseParamReq {

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 店铺ID
     */
    private Long shopId;

    public void checkParameter() {
        if (templateId == null) {
            throw new IllegalArgumentException("templateId不能为空");
        }
    }
}
