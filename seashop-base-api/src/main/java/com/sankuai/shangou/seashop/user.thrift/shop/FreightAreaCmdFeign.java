package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AddFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CopyFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateFreightReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 运费模版
 */
@FeignClient(name = "himall-base", contextId = "FreightAreaCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/freightArea")
public interface FreightAreaCmdFeign {
    /**
     * 供应商后台-交易-运费模版管理(删除)
     * @param cmdFreightTemplateReq 运费模版删除请求入参
     * @return BaseResp
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/deleteFreightTemplate",consumes = "application/json")
    ResultDto<BaseResp> deleteFreightTemplate(@RequestBody DeleteFreightTemplateReq cmdFreightTemplateReq) throws TException;

    /**
     * 供应商后台-交易-运费模版管理(新增)
     * @param addFreightReq 新增运费模版请求入参
     * @return BaseResp
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/addFreightTemplate",consumes = "application/json")
    ResultDto<BaseResp> addFreightTemplate(@RequestBody AddFreightReq addFreightReq) throws TException;

    /**
     * 供应商后台-交易-运费模版管理(修改)
     * @param updateFreightReq 修改运费模版请求入参
     * @return BaseResp
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/updateFreightTemplate",consumes = "application/json")
    ResultDto<BaseResp> updateFreightTemplate(@RequestBody UpdateFreightReq updateFreightReq) throws TException;

    /**
     * 供应商后台-交易-运费模版管理(复制)
     * @param copyFreightReq 修改运费模版请求入参
     * @return BaseResp
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/copyFreightTemplate",consumes = "application/json")
    ResultDto<BaseResp> copyFreightTemplate(@RequestBody CopyFreightReq copyFreightReq) throws TException;
}
