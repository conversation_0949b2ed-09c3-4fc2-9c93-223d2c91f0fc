package com.sankuai.shangou.seashop.user.thrift.account.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;


/**
 * @description: 是否专属
 * @author: liweisong
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryUserShopReq {

    /**
     * 供应商ID列表
     */
    private Long shopId;

    /**
     * 商家用户ID
     */
    private Long userId;

    public void checkParameter() {
        if(shopId == null){
            throw new IllegalArgumentException("shopId不能为空");
        }
    }
}
