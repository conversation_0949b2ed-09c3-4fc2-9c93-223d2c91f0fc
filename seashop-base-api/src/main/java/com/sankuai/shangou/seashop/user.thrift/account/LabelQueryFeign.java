package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryLabelPageReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.LabelResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 标签查询服务类
 */
@FeignClient(name = "himall-base", contextId = "LabelThriftQueryService", url = "${himall-base.dev.url:}", path = "/himall-base/account/label")
public interface LabelQueryFeign {

    /**
     * 删除标签
     *
     * @return 删除的标签ID
     */
    @PostMapping(value = "/queryLabelPage", consumes = "application/json")
    ResultDto<BasePageResp<LabelResp>> queryLabelPage(@RequestBody QueryLabelPageReq queryLabelPageReq) throws TException;

}
