package com.sankuai.shangou.seashop.base.thrift.core;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.dto.PlatformTaskDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.QueryTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.task.PlatformTaskListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = "himall-base", contextId = "PlatformTaskQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/platformTask")
public interface PlatformTaskQueryFeign {

    /**
     * 分页查询平台任务列表
     * @param queryReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/pageList", consumes = "application/json")
    ResultDto<BasePageResp<PlatformTaskDto>> pageList(@RequestBody QueryTaskReq queryReq) throws TException;

    /**
     * 查询平台任务列表
     * @param queryReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryList", consumes = "application/json")
    ResultDto<PlatformTaskListResp> queryList(@RequestBody QueryTaskReq queryReq) throws TException;

}
