package com.sankuai.shangou.seashop.base.thrift.core;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.CompleteTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.CreateTaskReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.task.ExceptionTaskReq;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = "himall-base", contextId = "SellerTaskCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/sellerTask")
public interface SellerTaskCmdFeign {

    /**
     * 创建任务
     * @param createReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/createTask", consumes = "application/json")
    ResultDto<Long> createTask(@RequestBody CreateTaskReq createReq) throws TException;

    /**
     * 开始执行任务
     * @param idReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/start", consumes = "application/json")
    ResultDto<BaseResp> start(@RequestBody BaseIdReq idReq) throws TException;

    /**
     * 完成任务
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/complete", consumes = "application/json")
    ResultDto<BaseResp> complete(@RequestBody CompleteTaskReq req) throws TException;

    /**
     * 任务异常
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/exception", consumes = "application/json")
    ResultDto<BaseResp> exception(@RequestBody ExceptionTaskReq req) throws TException;

}
