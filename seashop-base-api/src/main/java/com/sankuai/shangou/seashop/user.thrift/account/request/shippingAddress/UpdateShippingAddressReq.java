package com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress;

import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.ValidationUtil;
import lombok.Data;
import lombok.ToString;

/**
 * <AUTHOR>
 */
@ToString
@Data
public class UpdateShippingAddressReq extends BaseParamReq {
    /**
     * 收货地址主键ID
     */
    private Long id;

    /**
     * 商家用户ID，外观层从用户信息获取
     */
    private Long userId;

    /**
     * 收货人
     */
    private String shipTo;

    /**
     * 收货人联系方式
     */
    private String phone;

    /**
     * 区域ID，省市区中的区ID
     */
    private Integer regionId;

    /**
     * 商家用户ID，外观层从用户信息获取
     */
    private String address;

    /**
     * 商家用户ID，外观层从用户信息获取
     */
    private String addressDetail;

    /**
     * 商家用户ID，外观层从用户信息获取
     */
    private Boolean whetherDefault;

    /**
     * 参数校验
     */
    public void checkParameter() {
        if (userId == null || userId <= 0) {
            throw new InvalidParamException("userId不能为空");
        }
        if (shipTo == null || shipTo.isEmpty()) {
            throw new InvalidParamException("收货人不能为空");
        }
        if (shipTo.length() > 20) {
            throw new InvalidParamException("收货人长度不能超过20个字符");
        }
        if (phone == null || phone.isEmpty()) {
            throw new InvalidParamException("收货人联系方式不能为空");
        }
        if (!ValidationUtil.isPhone(phone)) {
            throw new InvalidParamException("手机号码格式不符合要求");
        }
        if (regionId == null || regionId <= 0) {
            throw new InvalidParamException("所在地区不能为空");
        }
        if (address == null || address.isEmpty()) {
            throw new InvalidParamException("详细地址不能为空");
        }
        if (address.length() > 50) {
            throw new InvalidParamException("详细地址长度不能超过50个字符");
        }
        if (whetherDefault == null) {
            throw new InvalidParamException("是否默认不能为空");
        }
    }
}
