package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * @description：新增供应商发/退货地址表入参
 * @author： liweisong
 * @create： 2023/11/27 9:22
 * 04、新增地址，需完成以下字段的填写
 * 发货点名称：必填，30个字符以内
 * 发货人姓名：必填，10个字符以内
 * 发货地区：必填，级联组件，选择省市区街道
 * 发货详细地址&地图定位：必填，发货地址必须有定位信息，即输入地址后点击搜索地图按钮，在地图上选择图标并导入经纬度，否则保存不成功，toast提示“请搜索地图定位”
 * 发货人手机号：必填，只能输入11位手机号
 */
@ToString
@Data
public class AddShopShipperReq extends BaseParamReq {
    /**
     * 商家编号
     */
    @ExaminField(description = "商家编号")
    private Long shopId;

    /**
     * 发货点名称
     */
    @ExaminField(description = "发货点名称")
    private String shipperTag;

    /**
     * 发货人
     */
    @ExaminField(description = "发货人")
    private String shipperName;

    /**
     * 区域Id
     */
    @ExaminField(description = "区域Id")
    private Integer regionId;

    /**
     * 具体街道信息
     */
    @ExaminField(description = "具体街道信息")
    private String address;

    /**
     * 手机号码
     */
    @ExaminField(description = "手机号码")
    private String telPhone;

    /**
     * 经度
     */
    private BigDecimal longitude;

    /**
     * 纬度
     */
    private BigDecimal latitude;

    public void checkParameter() {
        if (StringUtils.isEmpty(shipperTag)) {
            throw new IllegalArgumentException("shipperTag发货点名称不能为空");
        }
        if (shipperTag.length() > 30) {
            throw new IllegalArgumentException("shipperTag发货点名称必须小于30个字符");
        }
        if (StringUtils.isEmpty(shipperName)) {
            throw new IllegalArgumentException("shipperName发货人姓名不能为空");
        }
        if (shipperName.length() > 10) {
            throw new IllegalArgumentException("shipperName发货人姓名必须小于10个字符");
        }
        if (regionId == null) {
            throw new IllegalArgumentException("regionId区域ID不能为空");
        }
        if (StringUtils.isEmpty(address)) {
            throw new IllegalArgumentException("address具体街道信息不能为空");
        }
        if (StringUtils.isEmpty(telPhone)) {
            throw new IllegalArgumentException("telPhone手机号码不能为空");
        } else {
            String regex = "^(1[3456789]\\d{9})$";
            if (!telPhone.matches(regex)) {
                throw new IllegalArgumentException("手机号码格式不正确");
            }
        }
    }
}
