package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description：店铺交易设置返参
 * @author： liweisong
 * @create： 2023/11/27 16:17
 */
@ToString
@Data
public class QueryOrderSettingResp extends BaseParamReq {

    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 起购量校验方式：1：起购数量和起购金额同时满足 2：起购数量和起购金额满足其一
     */
    private Integer purchaseMinValidType;

    /**
     * 起购数量
     */
    private Integer purchaseMinQuantity;

    /**
     * 起购金额
     */
    private BigDecimal purchaseMinPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人
     */
    private Long createUser;

    /**
     * 更新人
     */
    private Long updateUser;
}
