package com.sankuai.shangou.seashop.user.thrift.auth.request;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.enums.LoginPlatformEnum;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.*;

/**
 * @author: cdd
 * @date: 2024/5/17/017
 * @description: 发送登录短信对象
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class LoginSmsReq {
    /**
     * 手机号
     */
    private String phone;
    /**
     * 登录平台
     */
    private String loginPlatform;

    public void checkParameter() {
        AssertUtil.throwIfNull(StrUtil.isBlank(phone), "手机号不能为空");
        AssertUtil.throwIfNull(LoginPlatformEnum.nameOf(loginPlatform), "手机号不能为空");
    }
}
