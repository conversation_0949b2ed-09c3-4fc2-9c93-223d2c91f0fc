package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.BatchQueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description：发票管理
 * @author： liweisong
 * @create： 2023/11/29 8:50
 */

/**
 * 供应商发票管理查询接口
 */
@FeignClient(name = "himall-base", contextId = "ShopInvoiceQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/shopInvoice")
public interface ShopInvoiceQueryFeign {

    /**
     * 供应商后台-店铺-发票管理(查询)
     * 查看请求入参
     *
     * @param queryShopInvoiceReq 查看请求入参
     * @return 发票查看结果
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/queryShopInvoice", consumes = "application/json")
    ResultDto<QueryShopInvoiceResp> queryShopInvoice(@RequestBody QueryShopInvoiceReq queryShopInvoiceReq) throws TException;

    /**
     * 供应商后台-店铺-发票管理(批量查询)
     * 查看请求入参
     *
     * @param queryShopInvoiceReq 查看请求入参
     * @return 发票查看结果
     * @throws TException 发生错误时抛出TException
     */
    @PostMapping(value = "/batchQueryShopInvoice", consumes = "application/json")
    ResultDto<QueryShopInvoiceListResp> batchQueryInvoiceSetting(@RequestBody BatchQueryShopInvoiceReq queryShopInvoiceReq) throws TException;

    /**
     * 平台端-店铺-发票管理(查询)
     * 查看请求入参
     *
     * @return 发票查看结果
     */
    @GetMapping(value = "/querySelfShopInvoice")
    ResultDto<QueryShopInvoiceResp> querySelfShopInvoice();
}
