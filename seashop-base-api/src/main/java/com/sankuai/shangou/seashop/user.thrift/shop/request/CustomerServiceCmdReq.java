package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.user.thrift.shop.group.Add;
import com.sankuai.shangou.seashop.user.thrift.shop.group.Delete;
import com.sankuai.shangou.seashop.user.thrift.shop.group.Update;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2023-11-29
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CustomerServiceCmdReq extends BaseParamReq {

    /**
     * 客服id
     */
    @NotNull(message = "客服id不能为空",groups = {Update.class, Delete.class})
    private Long id;

    /**
     * 店铺id
     */
    @NotNull(message = "店铺id不能为空",groups = {Add.class, Update.class, Delete.class})
    private Long shopId;

    /**
     * 工具
     */
    @NotNull(message = "工具不能为空",groups = {Add.class, Update.class})
    private Integer tool;

    /**
     * 销售类型
     */
    @NotNull(message = "销售类型不能为空",groups = {Add.class, Update.class})
    private Integer type;

    /**
     * 客服名称
     */
    @NotBlank(message = "客服名称不能为空",groups = {Add.class, Update.class})
    private String name;

    /**
     * 通信账号
     */
    @NotBlank(message = "通信账号不能为空",groups = {Add.class, Update.class})
    private String accountCode;

    /**
     * 终端类型
     */
    @NotNull(message = "终端类型不能为空",groups = {Add.class, Update.class})
    private Integer terminalType;


}
