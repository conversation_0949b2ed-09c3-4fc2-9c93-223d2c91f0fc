package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ToString
@Data
public class UpdateFreightReq extends BaseParamReq {

    /**
     * 主键
     */
    @PrimaryField
    private Long id;

    /**
     * 运费模板名称
     */
    @ExaminField(description = "运费模板名称")
    private String name;

    /**
     * 宝贝发货地
     */
    private Integer sourceAddress;

    /**
     * 发送时间
     */
    private String sendTime;

    /**
     * 是否商家负责运费
     */
    private Integer whetherFree;

    /**
     * 定价方法(0:按件数，1：按重量，2：按体积计算）
     */
    private Integer valuationMethod;

    /**
     * 运送类型（1：物流、2：快递）
     */
    private Integer shippingMethod;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 非销售区域是否隐藏
     */
    private Boolean nonSalesAreaHide;

    private Boolean nonSalesAreaProductHide;

    private Date createTime;

    private Date updateTime;

    /**
     * 指定可配送区域的运费
     */
    private List<FreightAreaContentReq> contentReqList;

    /**
     * 指定城市包邮
     */
    private List<ShippingFreeGroupReq> groupReqList;

    /**
     * 非销售区域
     */
    private List<RestrictedAreaReq> restrictedAreaReqList;

    /**
     * 该模版是否没使用,默认没使用,true没使用，false已使用
     */
    private Boolean unUseFlag = true;

    public void checkParameter(){
        if(id == null){
            throw new IllegalArgumentException("id不能为空");
        }
        if(shopId == null){
            throw new IllegalArgumentException("shopId不能为空");
        }
        if(name == null){
            throw new IllegalArgumentException("name不能为空");
        }
        if(name.length() > 100){
            throw new IllegalArgumentException("运费模板名称限制100个字符");
        }
    }
}
