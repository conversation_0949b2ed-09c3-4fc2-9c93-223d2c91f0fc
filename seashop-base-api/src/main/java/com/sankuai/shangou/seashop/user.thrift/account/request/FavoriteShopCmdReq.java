package com.sankuai.shangou.seashop.user.thrift.account.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class FavoriteShopCmdReq {
    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 商家ID
     */
    private Long userId;

    /**
     * 店铺id列表
     */
    private List<Long> shopIdList;
}
