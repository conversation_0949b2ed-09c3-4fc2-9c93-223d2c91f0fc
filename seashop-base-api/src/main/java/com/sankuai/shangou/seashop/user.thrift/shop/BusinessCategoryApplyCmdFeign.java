package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AuditBusinessCategoryApplyReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveBusinessCategoryApplyReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.SaveSupplyCustomFormReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;


@FeignClient(name = "himall-base", contextId = "BusinessCategoryApplyCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/businessCategoryApply")
public interface BusinessCategoryApplyCmdFeign {

    /**
     * 添加经营类目申请
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveBusinessCategoryApply", consumes = "application/json")
    ResultDto<BaseResp> saveBusinessCategoryApply(@RequestBody SaveBusinessCategoryApplyReq request) throws TException;

    /**
     * 补充资料
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/supplyCustomForm", consumes = "application/json")
    ResultDto<BaseResp> supplyCustomForm(@RequestBody SaveSupplyCustomFormReq request) throws TException;

    /**
     * 经营类目申请审核
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/auditBusinessCategoryApply", consumes = "application/json")
    ResultDto<BaseResp> auditBusinessCategoryApply(@RequestBody AuditBusinessCategoryApplyReq request) throws TException;

}
