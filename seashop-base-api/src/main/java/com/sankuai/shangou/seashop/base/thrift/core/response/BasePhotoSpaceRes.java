package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

import java.util.Date;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class BasePhotoSpaceRes extends BaseThriftDto {
    private Long id;

    /**
     * 分类id
     */
    private Long photoCategoryId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 图片名称
     */
    private String photoName;

    /**
     * 图片路径
     */
    private String photoPath;

    /**
     * 图片大小
     */
    private Long fileSize;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 最后修改时间
     */
    private Date lastupdateTime;
}
