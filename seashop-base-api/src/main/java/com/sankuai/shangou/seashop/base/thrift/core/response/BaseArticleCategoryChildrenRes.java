package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

@Data
public class BaseArticleCategoryChildrenRes extends BaseThriftDto {

    /**
     * 文章分类id
     */
    private Long id;

    /**
     * 父分类id
     */
    private Long parentCategoryId;

    /**
     * 分类名称
     */
    private String name;

    /**
     * 排序字段
     */
    private Long displaySequence;

    /**
     * 是否默认
     */
    private Boolean isDefault;
}
