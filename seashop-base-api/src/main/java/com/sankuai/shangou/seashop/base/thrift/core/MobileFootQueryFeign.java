package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryFootMenusReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.QueryFootMenusResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author： liweisong
 * @create： 2023/11/29 11:36
 */


@FeignClient(name = "himall-base", contextId = "MobileFootQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/mobileFoot")
public interface MobileFootQueryFeign {

    /**
     * 平台-小程序-底部导航栏-查询
     * @param queryFootMenusReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryFootMenus", consumes = "application/json")
    ResultDto<QueryFootMenusResp> queryFootMenus(@RequestBody QueryFootMenusReq queryFootMenusReq) throws TException;
}
