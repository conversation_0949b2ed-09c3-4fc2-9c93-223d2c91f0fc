package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdShopSeqReq extends BaseParamReq {

    /**
     * 店铺ID列表
     */
    private List<Long> shopIdList;
    /**
     * 序号
     */
    private Integer serialNumber;
}
