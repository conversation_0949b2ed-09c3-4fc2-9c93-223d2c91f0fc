package com.sankuai.shangou.seashop.base.thrift.core;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddOrUpdateFootMenusReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteFootMenuReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SaveFootMenusReq;

/**
 * @author： liweisong
 * @create： 2023/11/29 11:36
 */
@FeignClient(name = "himall-base", contextId = "MobileFootCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/mobileFoot")
public interface MobileFootCmdFeign {

    /**
     * 平台-小程序-底部导航栏-新增或者编辑
     * @param addFootMenusReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/addOrUpdateFootMenus", consumes = "application/json")
    ResultDto<BaseResp> addOrUpdateFootMenus(@RequestBody AddOrUpdateFootMenusReq addFootMenusReq) throws TException;

    /**
     * 保存底部导航栏
     * @param saveFootMenusReq 添加底部导航栏入参
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveFootMenus", consumes = "application/json")
    ResultDto<BaseResp> saveFootMenus(@RequestBody SaveFootMenusReq saveFootMenusReq) throws TException;

    /**
     * 平台-小程序-底部导航栏-删除
     * @param deleteFootMenuReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deleteFootMenu", consumes = "application/json")
    ResultDto<BaseResp> deleteFootMenu(@RequestBody DeleteFootMenuReq deleteFootMenuReq) throws TException;
}
