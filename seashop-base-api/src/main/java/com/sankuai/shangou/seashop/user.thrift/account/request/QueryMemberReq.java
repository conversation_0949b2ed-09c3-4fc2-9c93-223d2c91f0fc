package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 单个商家查询入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryMemberReq extends BaseParamReq {

    /**
     * 商家名称
     */
    private String memberName;

    /**
     * 商家Id
     */
    private Long id;

    /**
     * ep账号id
     */
    private Integer epAccountId;

}
