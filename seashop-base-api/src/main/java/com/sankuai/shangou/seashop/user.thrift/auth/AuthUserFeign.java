package com.sankuai.shangou.seashop.user.thrift.auth;

import com.sankuai.shangou.seashop.base.boot.dto.LoginBaseDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginManagerDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginMemberDto;
import com.sankuai.shangou.seashop.base.boot.dto.LoginShopDto;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@Component
@FeignClient(name = "himall-base", contextId = "AuthUserFeign", url = "${himall-base.dev.url:}", path = "/himall-base/authInfo")
public interface AuthUserFeign {
    /**
     * 根据登录信息获取 平台用户信息
     * @param loginBaseDto
     * @return
     */

    @PostMapping(value = "/getManagerUser", consumes = "application/json")
     ResultDto<LoginManagerDto> getManagerUser(@RequestBody LoginBaseDto loginBaseDto);

    /**
     * 根据登录信息获取会员信息
     * @param loginBaseDto
     * @return
     */
    @PostMapping(value = "/getMemberUser",consumes = "application/json" )
     ResultDto<LoginMemberDto> getMemberUser(@RequestBody LoginBaseDto loginBaseDto);

    /**
     * 根据登录信息获取卖家信息
     * @param loginBaseDto
     * @return
     */
    @PostMapping(value = "/getShopUser",consumes = "application/json" )
     ResultDto<LoginShopDto> getShopUser(@RequestBody LoginBaseDto loginBaseDto);

}
