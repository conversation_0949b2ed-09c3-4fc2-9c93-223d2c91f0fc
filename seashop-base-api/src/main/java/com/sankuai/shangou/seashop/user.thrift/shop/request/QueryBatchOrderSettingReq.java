package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * @description：店铺交易设置查询入参
 * @author： liweisong
 * @create： 2023/11/27 16:17
 */
@ToString
@Data
public class QueryBatchOrderSettingReq extends BaseParamReq {

    /**
     * 店铺ID
     */
    private List<Long> shopIdList;

    public void checkParameter(){
        AssertUtil.throwIfNull(shopIdList, "shopIdList 不能为空");
        AssertUtil.throwIfTrue(shopIdList.size()>200, "一次查询数量不能超过200");
    }
}
