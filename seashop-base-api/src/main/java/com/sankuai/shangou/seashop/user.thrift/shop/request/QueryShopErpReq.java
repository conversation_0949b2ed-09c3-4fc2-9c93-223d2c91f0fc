package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/28 15:05
 */
@Data
public class QueryShopErpReq extends BaseParamReq {

    /**Ø
     * 店铺id
     */
    private Long shopId;
    public void checkParameter(){
        if(shopId == null){
            throw new IllegalArgumentException("shopId 不能为空");
        }
    }
}
