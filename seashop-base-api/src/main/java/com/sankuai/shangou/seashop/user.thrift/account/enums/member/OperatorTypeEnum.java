package com.sankuai.shangou.seashop.user.thrift.account.enums.member;

public enum OperatorTypeEnum {
    DELETE(0, "删除"),
    ADD(1, "新增"),
    ;

    private Integer code;
    private String desc;

    OperatorTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static OperatorTypeEnum getByCode(Integer code) {
        for (OperatorTypeEnum value : OperatorTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
