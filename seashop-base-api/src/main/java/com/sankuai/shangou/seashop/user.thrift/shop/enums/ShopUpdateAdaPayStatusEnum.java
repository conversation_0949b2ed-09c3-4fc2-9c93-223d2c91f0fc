package com.sankuai.shangou.seashop.user.thrift.shop.enums;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/12/08 15:06
 */
public enum ShopUpdateAdaPayStatusEnum {
    WAIT_AUDIT(1, "待审核"),
    AUDIT_REFUSE(2, "审核拒绝"),
    AUDIT_PASS(3, "审核通过")
    ;

    private final Integer type;

    private final String desc;

    ShopUpdateAdaPayStatusEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByCode(Integer shopType) {
        for (ShopUpdateAdaPayStatusEnum shopTypeEnum : ShopUpdateAdaPayStatusEnum.values()) {
            if (shopTypeEnum.getType().equals(shopType)) {
                return shopTypeEnum.getDesc();
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public static ShopUpdateAdaPayStatusEnum getEnumByType(Integer type) {
        for (ShopUpdateAdaPayStatusEnum shopTypeEnum : ShopUpdateAdaPayStatusEnum.values()) {
            if (Objects.equals(shopTypeEnum.getType(), type)) {
                return shopTypeEnum;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }
}
