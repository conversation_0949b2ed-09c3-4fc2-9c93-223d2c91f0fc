package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseWXMenuRes;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdCreateQRReq;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(name = "himall-base", contextId = "WXMenuQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/WXMenu")
public interface WXMenuQueryFeign {
    /**
     * 查询公众号菜单列表
     * @return
     * @throws TException
     */
    @GetMapping(value = "getWXMenus")
    ResultDto<List<BaseWXMenuListRes>> queryWXMenus() throws TException;

    /**
     * 根据ID查询公众号菜单
     * @param idReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "queryWXMenuById", consumes = "application/json")
    ResultDto<BaseWXMenuRes> queryWXMenuById(@RequestBody BaseIdReq idReq) throws TException;

    //    createQrCode
    @PostMapping(value = "createQR", consumes = "application/json")
    ResultDto<String> createQR(@RequestBody CmdCreateQRReq cmdCreateQRReq) throws TException;
}
