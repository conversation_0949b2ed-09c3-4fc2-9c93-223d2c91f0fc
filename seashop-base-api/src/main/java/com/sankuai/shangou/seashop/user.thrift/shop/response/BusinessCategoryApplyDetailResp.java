package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:53
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BusinessCategoryApplyDetailResp extends BaseThriftDto {

    /**
     * id
     */
    private Long id;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 审核状态 0-待审核 1-已审核 2-审核拒绝
     */
    private Integer auditedStatus;

    /**
     * 审核状态描述
     */
    private String auditedStatusDesc;

    /**
     * 审核时间
     */
    private Date auditedDate;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 签署状态 0-待签署 1-已签署
     */
    private Integer agreementStatus;

    /**
     * 签署状态描述
     */
    private String agreementStatusDesc;

    /**
     * 合同ID
     */
    private Long shopAgreementId;

    /**
     * 是否需要补充资料
     */
    private Boolean needSupply;

    /**
     * 申请类目
     */
    private List<CategoryDto> applyCategoryList;

    /**
     * 表单数据
     */
    private List<CategoryApplyFormDto> formList;

    /**
     * 表单变动数据(补充数据时使用)
     */
    private List<CategoryApplyFormDto> changeFormList;
}
