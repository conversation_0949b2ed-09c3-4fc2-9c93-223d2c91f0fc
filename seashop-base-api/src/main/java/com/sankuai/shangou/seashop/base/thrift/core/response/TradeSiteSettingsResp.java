package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author： liweisong
 * @create： 2023/11/22 17:06
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class TradeSiteSettingsResp extends BaseParamReq {

    @ExaminField(description = "未付款超时")
    private String unpaidTimeout;

    @ExaminField(description = "确认收货超时")
    private String noReceivingTimeout;

    @ExaminField(description = "自动收货完成前时间")
    private String beforeReceivingDays;

    @ExaminField(description = "延迟收货时间")
    private String noReceivingDelayDays;

    @ExaminField(description = "关闭评价通道时限")
    private String orderCommentTimeout;

    @ExaminField(description = "供应商未发货自动短信提醒时限")
    private String orderWaitDeliveryRemindTime;

    @ExaminField(description = "企业网银限制金额")
    private String companyBankOrderAmount;

    @ExaminField(description = "订单退货期限")
    private String salesReturnTimeout;

    @ExaminField(description = "供应商自动确认售后时限")
    private String shopConfirmTimeout;

    @ExaminField(description = "用户发货限时")
    private String sendGoodsCloseTimeout;

    @ExaminField(description = "供应商确认到货时限")
    private String shopNoReceivingTimeout;

}
