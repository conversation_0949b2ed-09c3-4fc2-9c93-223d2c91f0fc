package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @description: 商家列表查询入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryMemberPageReq extends BasePageReq {

    /**
     * 商家名称
     */
    private String memberName;

    /**
     * 标签
     */
    private Long label;

    /**
     * 是否关注微信
     */
    private Boolean whetherFocusWeiXin;

    /**
     * 注册开始时间
     */
    private Date registerTimeStart;

    /**
     * 注册结束时间
     */
    private Date registerTimeEnd;

    /**
     * 商家等级
     */
    private Integer gradeId;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 昵称
     */
    private String weChatNick;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 商家来源
     */
    private Integer platform;

    /**
     * 是否查询总数
     */
    private Boolean whetherCount;

    /**
     * 是否为供应商
     */
    private Boolean seller;

    /**
     * 购买次数 最小值
     */
    private Integer buyCountStart;

    /**
     * 购买次数 最大值
     */
    private Integer buyCountEnd;

    /**
     * 笔单价 最小值
     */
    private Double priceStart;

    /**
     * 笔单价 最大值
     */
    private Double priceEnd;

    /**
     * 累计消费金额 最小值
     */
    private Double totalAmountStart;

    /**
     * 累计消费金额 最大值
     */
    private Double totalAmountEnd;
}
