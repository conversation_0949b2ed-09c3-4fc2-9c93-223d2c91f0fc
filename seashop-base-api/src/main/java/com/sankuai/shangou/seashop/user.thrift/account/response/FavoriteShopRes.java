package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: LXH
 **/
@Data
public class FavoriteShopRes extends BaseParamReq {
    /**
     * 收藏店铺id
     */
    private Long userId;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺logo
     */
    private String shopLogo;

    /**
     * 人气
     */
    private Integer popularity;

    /**
     * 收藏时间
     */
    private Date createTime;

    /**
     * 是否冻结
     */
    private Boolean freeze;

    /**
     * 店铺状态
     */
    private Integer shopStatus;
}
