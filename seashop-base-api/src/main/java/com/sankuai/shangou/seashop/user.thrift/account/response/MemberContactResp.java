package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/12 19:25
 */
@Data
public class MemberContactResp extends BaseThriftDto {
    /**
     * 主键
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * shopId,有的时候才会返回
     */
    private Long shopId;

    /**
     * 用户类型(0 Email 1 SMS)
     */
    private Integer userType;

    /**
     * 插件名称
     */
    private String serviceProvider;

    /**
     * 联系号码
     */
    private String contact;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 用户名称
     */
    private String userName;
}
