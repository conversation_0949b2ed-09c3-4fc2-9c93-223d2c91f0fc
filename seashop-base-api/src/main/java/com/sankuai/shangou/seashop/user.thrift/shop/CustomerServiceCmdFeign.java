package com.sankuai.shangou.seashop.user.thrift.shop;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CustomerServiceCmdReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.LoginHiChatReq;

/**
 * @description：供应商客服新增修改删除接口
 * @author： LXH
 * @create： 2023/11/27 9:13
 */
@FeignClient(name = "himall-base", contextId = "CustomerServiceCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/customerService")
public interface CustomerServiceCmdFeign {

    /**
     * 添加客服信息
     * @param customerServiceCmdReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/add", consumes = "application/json")
    ResultDto<BaseResp> add(@RequestBody CustomerServiceCmdReq customerServiceCmdReq) throws TException;

    /**
     * 编辑客服信息
     * @param customerServiceCmdReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/update", consumes = "application/json")
    ResultDto<BaseResp> update(@RequestBody CustomerServiceCmdReq customerServiceCmdReq) throws TException;

    /**
     * 删除客服信息
     * @param customerServiceCmdReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/delete", consumes = "application/json")
    ResultDto<BaseResp> delete(@RequestBody CustomerServiceCmdReq customerServiceCmdReq) throws TException;

    /**
     * 登录HiChat
     * @param loginHiChatReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/loginHiChat", consumes = "application/json")
    ResultDto<String> loginHiChat(@RequestBody LoginHiChatReq loginHiChatReq) throws TException;

    /**
     * 获取未读消息数量
     * @param managerId 管理员Id
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getHiChatUnReadCount")
    ResultDto<Long> getHiChatUnReadCount(@RequestParam Long managerId) throws TException;
}
