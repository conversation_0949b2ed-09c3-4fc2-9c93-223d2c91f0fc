package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/11/21 10:49
 */
@Data
public class SaveShopLogoReq extends BaseParamReq {

    @Schema(description = "店铺id")
    private Long shopId;

    @Schema(description = "logo地址")
    private String logo;

    @Override
    public void checkParameter() {
        AssertUtil.throwIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
    }
}
