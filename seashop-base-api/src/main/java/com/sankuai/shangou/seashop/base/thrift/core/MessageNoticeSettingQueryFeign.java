package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseMessageNoticeSettingRes;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "himall-base",contextId = "MessageNoticeSettingQueryFeign",url = "${himall-base.dev.url:}", path = "/himall-base/messageNoticeSetting")
public interface MessageNoticeSettingQueryFeign {

    /**
     * 获取所有类型的消息通知
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getMessageNoticeSetting")
    ResultDto<List<BaseMessageNoticeSettingRes>> getMessageNoticeSetting() throws TException;

    /**
     * 获取某种类型的消息通知
     * @param type
     * @return
     * @throws TException
     */
    @GetMapping(value = "/getMessageNoticeSettingByType")
    ResultDto<BaseMessageNoticeSettingRes> getMessageNoticeSettingByType(@RequestParam Integer type) throws TException;
}
