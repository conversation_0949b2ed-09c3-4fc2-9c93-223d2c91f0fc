package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:55
 */
@Data
public class QueryShopInvoiceReq extends BaseParamReq {

    /**
     * 店铺ID
     */
    private Long shopId;


    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null, "店铺shopId不能为空");
    }
}
