package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/6 14:32
 */
@Data
public class FloorEnableReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    @ExaminField(description = "是否显示，true:显示，false:不显示")
    private Boolean enable;

    public void checkParameter() {
        if (id == null) {
            throw new IllegalArgumentException("id 不能为空");
        }
        if (enable == null) {
            throw new IllegalArgumentException("enable 不能为空");
        }
    }
}
