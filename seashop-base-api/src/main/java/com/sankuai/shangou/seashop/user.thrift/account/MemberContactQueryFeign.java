package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryMemberContactReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberContactRespList;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 商家联系方式查询服务类
 */
@FeignClient(name = "himall-base", contextId = "MemberContactQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/memberContact")
public interface MemberContactQueryFeign {

    /**
     * 查询商家联系方式
     * @return 商家联系方式
     */
    @PostMapping(value = "/queryMemberContact", consumes = "application/json")
    ResultDto<MemberContactRespList> queryMemberContact(@RequestBody QueryMemberContactReq memberContactReq) throws TException;
}
