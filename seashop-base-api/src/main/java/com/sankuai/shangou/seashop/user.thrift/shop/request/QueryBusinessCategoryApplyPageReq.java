package com.sankuai.shangou.seashop.user.thrift.shop.request;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryBusinessCategoryApplyPageReq extends BasePageReq {


    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺状态 0-待审核 1-已审核 2-审核拒绝ØØØ
     */
    private Integer auditedStatus;

    /**
     * 签署状态 待签署=0,已签署=1
     */
    private Integer agreementStatus;

    /**
     * 店铺Id
     */
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "shopId不能为空");
    }
}
