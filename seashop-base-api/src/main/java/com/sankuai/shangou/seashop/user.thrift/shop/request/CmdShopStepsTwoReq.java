package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
public class CmdShopStepsTwoReq extends BaseParamReq {
    /**
     * 银行账号
     */
    @NotBlank(message = "银行账号不能为空")
    @ExaminField(description = "银行账号")
    private String bankAccountNumber;
    /**
     * 银行开户名
     */
    @NotBlank(message = "银行开户名不能为空")
    @ExaminField(description = "银行开户名")
    private String bankAccountName;
    /**
     * 银行编码
     */
    @NotBlank(message = "银行编码不能为空")
    @ExaminField(description = "银行编码")
    private String bankCode;
    /**
     * 开户银行所在地
     */
    @NotBlank(message = "开户银行所在地不能为空")
    @ExaminField(description = "开户银行所在地")
    private String bankName;
    /**
     * 银行类型
     */
    @NotNull(message = "银行类型不能为空")
    @ExaminField(description = "银行类型")
    private Integer bankType;
    /**
     * 银行省份
     */
    private Integer bankRegionProv;
    /**
     * 银行城市
     */
    private Integer bankRegionCity;
    /**
     * 银行地区
     */
    @ExaminField(description = "银行地区")
    private Integer bankRegionId;
    /**
     * 店铺ID
     */
    @PrimaryField
    private Long shopId;

}