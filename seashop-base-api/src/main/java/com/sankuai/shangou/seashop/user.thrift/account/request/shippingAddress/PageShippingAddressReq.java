package com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/21 9:34
 */
@Data
public class PageShippingAddressReq extends BasePageReq {

    /**
     * 用户id
     */
    private Long userId;

    public void checkParameter() {
        if (userId == null) {
            throw new IllegalArgumentException("userId is null");
        }
    }
}
