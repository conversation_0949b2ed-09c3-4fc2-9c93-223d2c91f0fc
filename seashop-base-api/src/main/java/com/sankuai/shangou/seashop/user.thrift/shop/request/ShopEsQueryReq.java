package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@Data
public class ShopEsQueryReq extends BasePageReq {


    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺状态
     */
    private Integer shopStatus;

    /**
     * 店铺状态
     */
    private List<Integer> shopStatusList;

    /**
     * 类目ID
     */
    private Long categoryId;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 类目ID列表
     */
    private List<Long> categoryIdList;

    /**
     * 是否检索最后一级类目
     */
    private Boolean searchLastCategory;
}
