package com.sankuai.shangou.seashop.base.thrift.core;


import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.EmailBodyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.SmsBodyReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-base",contextId = "MessageCMDFeign",url = "${himall-base.dev.url:}",  path = "/himall-base/message")
public interface MessageCMDFeign {
    /**
     * 发送邮件
     * @param emailBodyReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/sendEmail", consumes = "application/json")
    ResultDto<Boolean> sendEmail(@RequestBody EmailBodyReq emailBodyReq) throws TException;

    /**
     * 发送邮件
     * @param smsBodyReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/sendSms", consumes = "application/json")
    ResultDto<Boolean> sendSms(@RequestBody SmsBodyReq smsBodyReq);

    /**
     * 短信可用数目
     * @return
     */
    @GetMapping(value = "/querySmsBalance")
    ResultDto<Long> querySmsBalance();
}
