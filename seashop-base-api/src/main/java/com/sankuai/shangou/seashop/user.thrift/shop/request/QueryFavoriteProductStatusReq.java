package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;

/**
 * @description：查询商品收藏列表
 * @author： liweisong
 * @create： 2023/11/28 9:37
 */
@ToString
@Data
public class QueryFavoriteProductStatusReq extends BaseParamReq {

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 商品ID
     */
    private Long productId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfNull(userId, "用户ID不能为空");
        AssertUtil.throwInvalidParamIfNull(productId, "商品ID不能为空");
    }


}
