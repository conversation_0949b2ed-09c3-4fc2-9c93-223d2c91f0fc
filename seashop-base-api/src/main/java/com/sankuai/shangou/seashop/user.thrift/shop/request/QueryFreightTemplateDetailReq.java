package com.sankuai.shangou.seashop.user.thrift.shop.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryFreightTemplateDetailReq {

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 店铺ID
     */
    private Long shopId;

    public void checkParameter(){
        if(templateId == null){
            throw new IllegalArgumentException("templateId不能为空");
        }
    }
}
