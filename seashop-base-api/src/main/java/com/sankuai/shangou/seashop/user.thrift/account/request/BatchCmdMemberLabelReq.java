package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 商家操作请求入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BatchCmdMemberLabelReq extends BaseParamReq {
    /**
     * 商家ID
     */
    @PrimaryField
    private List<Long> ids;


    /**
     * 标签ID
     */
    private List<Long> labels;


    /**
     * 操作类型 0:删除 1:新增
     */
    private Integer operatorType;


}
