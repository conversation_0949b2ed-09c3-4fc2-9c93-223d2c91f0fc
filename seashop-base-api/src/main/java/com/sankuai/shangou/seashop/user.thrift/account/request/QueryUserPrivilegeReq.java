package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 查询权限列表请求入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryUserPrivilegeReq extends BaseParamReq {
    /**
     * 平台类型
     */
    private Integer platform;

    /**
     * 店铺ID
     */
    private Long managerId;

}
