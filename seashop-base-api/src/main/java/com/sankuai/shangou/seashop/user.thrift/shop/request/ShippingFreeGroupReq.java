package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;

import java.util.Date;
import java.util.List;

@ToString
@Data
public class ShippingFreeGroupReq extends BaseParamReq {
    /**
     * 主键
     */
    @PrimaryField
    private Long id;

    /**
     * 运费模版ID
     */
    @ExaminField(description = "运费模板ID")
    private Long templateId;

    /**
     * 包邮条件类型
     */
    @ExaminField(description = "包邮条件类型")
    private Integer conditionType;

    /**
     * 包邮条件值
     */
    @ExaminField(description = "包邮条件值")
    private String conditionNumber;

    private Date createTime;

    private Date updateTime;

    /**
     * 指定城市包邮的地区全路径
     */
    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.user.dao.shop.domain.ShippingFreeRegion", description = "指定可配送区域的详细地址")
    private List<ShippingFreeRegionReq> regionReqList;
}
