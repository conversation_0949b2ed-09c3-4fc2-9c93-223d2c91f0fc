package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

import java.util.List;


@ToString
@Data
public class AddFreightReq extends BaseParamReq {

    /**
     * 运费模板名称
     */
    @ExaminField(description = "运费模板名称")
    private String name;

    /**
     * 宝贝发货地
     */
    @ExaminField(description = "宝贝发货地")
    private Integer sourceAddress;

    /**
     * 发送时间(0为立即发货，其他的换算为小时为单位)
     */
    @ExaminField(description = "发送时间(0为立即发货，其他的换算为小时为单位)")
    private String sendTime;

    /**
     * 是否商家负责运费(1是，0否)
     */
    @ExaminField(description = "是否商家负责运费(1是，0否)")
    private Integer whetherFree;

    /**
     * 定价方法(0:按件数，1：按重量，2：按体积计算）
     */
    @ExaminField(description = "定价方法(0:按件数，1：按重量，2：按体积计算）")
    private Integer valuationMethod;

    /**
     * 运送类型（1：物流、2：快递）
     */
    @ExaminField(description = "运送类型（1：物流、2：快递）")
    private Integer shippingMethod;

    /**
     * 店铺ID
     */
    @ExaminField(description = "店铺ID")
    private Long shopId;

    /**
     * 非销售区域是否隐藏(1是，0否)
     */
    @ExaminField(description = "非销售区域是否隐藏(1是，0否)")
    private Boolean nonSalesAreaHide;

    @FieldDoc(description = "非销售区域商品是否隐藏")
    private Boolean nonSalesAreaProductHide;
    /**
     * 指定可配送区域的运费
     */
    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.user.dao.shop.domain.FreightAreaContent", description = "指定可配送区域的运费")
    private List<FreightAreaContentReq> contentReqList;

    /**
     * 指定城市包邮
     */
    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.user.dao.shop.domain.ShippingFreeGroup", description = "指定城市包邮")
    private List<ShippingFreeGroupReq> groupReqList;

    /**
     * 非销售区域
     */
    @ExaminField(isChildField = true, entityClassName = "com.sankuai.shangou.seashop.user.dao.shop.domain.RestrictedArea", description = "非销售区域")
    private List<RestrictedAreaReq> restrictedAreaReqList;

    public void checkParameter() {
        if (shopId == null) {
            throw new IllegalArgumentException("shopId不能为空");
        }
        if (StringUtils.isEmpty(name)) {
            throw new IllegalArgumentException("name不能为空");
        }
        if (name.length() > 100) {
            throw new IllegalArgumentException("运费模板名称限制100个字符");
        }
        if (whetherFree == null) {
            throw new IllegalArgumentException("whetherFree不能为空");
        }
        if (valuationMethod == null) {
            throw new IllegalArgumentException("valuationMethod不能为空");
        }
    }
}
