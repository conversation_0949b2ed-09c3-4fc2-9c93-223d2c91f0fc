package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @description: 标签信息
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class MemberLabelResp extends BaseThriftDto {
    /**
     * id
     */
    private Long id;
    /**
     * 商家Id
     */
    private Long memId;
    /**
     * 标签Id
     */
    private Long labelId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
