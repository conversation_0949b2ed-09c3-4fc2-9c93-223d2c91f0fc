package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/12/01 14:33
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryBusinessCategoryApplyDetailReq extends BasePageReq {

    /**
     * 申请记录id
     */
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "id不能为空");
    }

    public void checkForShop() {
        checkParameter();
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "shopId不能为空");
    }
}
