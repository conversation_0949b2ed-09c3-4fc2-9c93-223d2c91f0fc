package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.LoginResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.*;
import com.sankuai.shangou.seashop.user.thrift.account.response.EpManagerResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberLabelRespList;
import com.sankuai.shangou.seashop.user.thrift.account.response.MemberResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.ResetPasswordResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CheckCodeCmdReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdSendCodeReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * 商家操作服务类
 */
@FeignClient(name = "himall-base", contextId = "MemberCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/member")
public interface ShopMemberCmdFeign {

    /**
     * 冻结商家
     *
     * @return 冻结的账户ID
     */
    @PostMapping(value = "/freezeMember", consumes = "application/json")
    ResultDto<Long> freezeMember(@RequestBody CmdMemberReq cmdMemberReq) throws TException;

    /**
     * 解冻商家
     *
     * @return 解冻的账户ID
     */
    @PostMapping(value = "/thawingMember", consumes = "application/json")
    ResultDto<Long> thawingMember(@RequestBody CmdMemberReq cmdMemberReq) throws TException;

    /**
     * 修改密码
     *
     * @return 修改的密码ID
     */
    @PostMapping(value = "/changePassword", consumes = "application/json")
    ResultDto<Long> changePassword(@RequestBody CmdMemberReq cmdMemberReq) throws TException;

    /**
     * 解绑手机号
     *
     * @return 解绑的手机号ID
     */
    @PostMapping(value = "/unbindPhone", consumes = "application/json")
    ResultDto<Long> unbindPhone(@RequestBody CmdMemberReq cmdMemberReq) throws TException;

    /**
     * 批量删除商家
     *
     * @return 处理成功的商家数量
     */
    @PostMapping(value = "/batchDelete", consumes = "application/json")
    ResultDto<Long> batchDelete(@RequestBody BatchCmdMemberReq batchCmdMemberReq) throws TException;

    /**
     * 获取商家标签
     *
     * @return 获取的商家标签
     */
    @PostMapping(value = "/getMemberLabel", consumes = "application/json")
    ResultDto<MemberLabelRespList> getMemberLabel(@RequestBody QueryMemberReq queryMemberReq) throws TException;

    /**
     * 批量添加商家标签
     *
     * @return 处理成功的商家数量
     */
    @PostMapping(value = "/batchAddMemberLabel", consumes = "application/json")
    ResultDto<BaseResp> batchAddMemberLabel(@RequestBody BatchCmdMemberLabelReq cmdMemberReq) throws TException;

    /**
     * 牵牛花添加会员
     *
     * @return 添加的会员信息
     */
    @PostMapping(value = "/addMember", consumes = "application/json")
    ResultDto<MemberResp> addMember(@RequestBody AddMemberReq addMemberReq) throws TException;

    @PostMapping(value = "/setMemberLabel", consumes = "application/json")
    ResultDto<Long> setMemberLabel(@RequestBody CmdMemberReq cmdMemberReq) throws TException;

    @GetMapping(value = "/imageVerificationCode")
    ResultDto<ImageVerificationCodeResp> imageVerificationCode() throws TException;
    /**
     * 牵牛花更新会员
     *
     * @return 更新的会员信息
     */
    @PostMapping(value = "/updateMember", consumes = "application/json")
    ResultDto<MemberResp> updateMember(@RequestBody UpdateMemberReq addMemberReq) throws TException;

    /**
     * 发送邮件消息
     *
     * @return 发送的邮件消息
     */
    @PostMapping(value = "/sendEmailMsg", consumes = "application/json")
    ResultDto<BaseResp> sendEmailMsg(@RequestBody SendEmailMsgReq apiSendEmailMsgReq) throws TException;

    /**
     * 绑定联系方式
     *
     * @return 绑定的联系方式
     */
    @PostMapping(value = "/bindContact", consumes = "application/json")
    ResultDto<BaseResp> bindContact(@RequestBody BindContactCmdReq bindContactCmdReq) throws TException;

    /**
     * 发送验证码
     *
     * @return 发送的验证码
     */
    @PostMapping(value = "/sendCode", consumes = "application/json")
    ResultDto<BaseResp> sendCode(@RequestBody CmdSendCodeReq sendCodeCmdReq) throws TException;

    /**
     * 获取OPENID
     *
     * @return 获取的OPENID
     */
    @PostMapping(value = "/getOpenId", consumes = "application/json")
    ResultDto<String> getOpenId(@RequestBody OpenIdQueryReq code) throws TException;

    /**
     * 校验验证码
     *
     * @return 校验的验证码
     */
    @PostMapping(value = "/checkCode", consumes = "application/json")
    ResultDto<BaseResp> checkCode(@RequestBody CheckCodeCmdReq sendCodeCmdReq) throws TException;

    /**
     * 更新登录时间
     *
     * @return 更新的登录时间
     */
    @GetMapping(value = "/updateLoginTime")
    ResultDto<BaseResp> updateLoginTime(@RequestParam Long id) throws TException;

    /**
     * 登出
     *
     * @return 登出的结果
     */
    @PostMapping(value = "/logout", consumes = "application/json")
    ResultDto<BaseResp> logout(@RequestBody BaseIdReq baseIdReq) throws TException;

    /**
     * 检查注册
     *
     * @return 检查的注册结果
     */
    @GetMapping(value = "/checkAndCreateUser", consumes = "application/json")
    ResultDto<EpManagerResp> checkAndCreateUser(@RequestParam Integer id) throws TException;

    /**
     * 重置密码
     *
     * @return 重置的密码结果
     */
    @PostMapping(value = "/resetPassword", consumes = "application/json")
    ResultDto<ResetPasswordResp> resetPassword(@RequestBody ResetPasswordReq resetReq) throws TException;

    /**
     * 修改密码
     *
     * @return 修改的密码结果
     */
    @PostMapping(value = "/modifyPassword", consumes = "application/json")
    ResultDto<ResetPasswordResp> modifyPassword(@RequestBody ModifyPasswordReq resetReq) throws TException;

    /**
     * 注册用户
     *
     * @return 注册的用户信息
     */
    @PostMapping(value = "/register", consumes = "application/json")
    ResultDto<LoginResp> register(@RequestBody RegisterReq registerReq) throws TException;

}
