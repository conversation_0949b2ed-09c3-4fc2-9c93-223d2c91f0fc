package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleCategoryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-base",contextId = "ArticleCMDFeign", path = "/himall-base/article", url = "${himall-base.dev.url:}")
public interface ArticleCMDFeign {

    @PostMapping(value = "/create", consumes = "application/json")
    ResultDto<Long> create(@RequestBody BaseArticleReq articleReq) throws TException;

    @PostMapping(value = "/update", consumes = "application/json")
    ResultDto<Boolean> update(@RequestBody BaseArticleReq articleReq) throws TException;

    @PostMapping(value = "/delete", consumes = "application/json")
    ResultDto<Boolean> delete(@RequestBody BaseIdsReq idsReq) throws TException;

    @PostMapping(value = "/createCategory", consumes = "application/json")
    ResultDto<Long> createCategory(@RequestBody BaseArticleCategoryReq categoryReq) throws TException;

    @PostMapping(value = "/updateCategory", consumes = "application/json")
    ResultDto<Boolean> updateCategory(@RequestBody BaseArticleCategoryReq categoryReq) throws TException;

    @PostMapping(value = "/deleteCategory", consumes = "application/json")
    ResultDto<Boolean> deleteCategory(@RequestBody BaseIdsReq idsReq) throws TException;

}
