package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.BusinessCategoryEsDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.ExclusiveMemberEsDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.ShopBrandEsDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopEsResp extends BaseThriftDto {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺logo
     */
    private String logo;

    /**
     * 店铺状态
     */
    private Integer shopStatus;

    /**
     * 订单量（已完成）
     */
    private Long orderSaleCount;

    /**
     * 商品销量（已完成）
     */
    private Long productSaleCount;

    /**
     * 是否开启专属商家
     */
    private Boolean whetherOpenExclusiveMember;

    /**
     * 专属商家对象的用户信息
     */
    private List<ExclusiveMemberEsDto> exclusiveMemberList;

    /**
     * 品牌列表
     */
    private List<ShopBrandEsDto> shopBrandList;

    /**
     * 类目列表
     */
    private List<BusinessCategoryEsDto> businessCategoryList;

    /**
     * 评分数量
     */
    private Long markCount;

    /**
     * 包装评分
     */
    private BigDecimal packMark;

    /**
     * 服务评分
     */
    private BigDecimal serviceMark;

    /**
     * 综合评分
     */
    private BigDecimal comprehensiveMark;
}
