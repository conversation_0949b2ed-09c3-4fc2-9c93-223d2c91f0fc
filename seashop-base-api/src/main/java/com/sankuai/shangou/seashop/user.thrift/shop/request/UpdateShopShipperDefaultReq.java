package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;

import lombok.Data;
import lombok.ToString;

/**
 * @description：修改供应商发/退货地址表默认值入参
 * @author： liweisong
 * @create： 2023/11/27 13:48
 */
@ToString
@Data
public class UpdateShopShipperDefaultReq extends BaseParamReq {

    /**
     * 主键ID
     */
    @PrimaryField
    private Long id;

    /**
     * 是否为默认发货地址
     */
    @ExaminField(description = "是否为默认发货地址")
    private Boolean defaultSendGoodsFlag;

    /**
     * 是否默认收货地址
     */
    @ExaminField(description = "是否默认收货地址")
    private Boolean defaultGetGoodsFlag;

    /**
     * 默认核销地址
     */
    @ExaminField(description = "默认核销地址")
    private Boolean defaultVerificationFlag;

    /**
     * 商家编号
     */
    @ExaminField(description = "商家编号")
    private Long shopId;

    public void checkParameter() {
        if (id == null) {
            throw new IllegalArgumentException("id不能为空");
        }
        if (shopId == null) {
            throw new IllegalArgumentException("shopId不能为空");
        }
        if (defaultSendGoodsFlag == null && defaultGetGoodsFlag == null) {
            throw new IllegalArgumentException("三个默认值设置不能同时为空");
        }
    }
}
