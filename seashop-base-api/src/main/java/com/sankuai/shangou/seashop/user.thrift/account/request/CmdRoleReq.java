package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description: 查询权限列表请求入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdRoleReq extends BaseParamReq {
    /**
     * id
     */
    private Long id;

    /**
     * 店铺ID 0为平台
     */
    private Long shopId;

    /**
     * 角色名称
     */
    private String roleName;

    /**
     * 权限id
     */
    private List<Long> privilegeIds;

    /**
     * 操作人id
     */
    private Long operatorId;

    /**
     * 操作人名称
     */
    private String operatorName;
}
