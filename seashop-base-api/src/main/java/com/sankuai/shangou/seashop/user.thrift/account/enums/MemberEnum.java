package com.sankuai.shangou.seashop.user.thrift.account.enums;

/**
 * @description: 商家枚举类
 * @author: LXH
 **/
public class MemberEnum {

    public enum PlatformType {
        PC(0, "PC"),
        <PERSON><PERSON>in(1, "微信"),
        Android(2, "安卓"),
        IOS(3, "苹果"),
        Wap(4, "触屏"),
        WeiXinSmallProg(5, "商城小程序"),
        Mobile(99, "移动端")
        ;

        private Integer code;
        private String desc;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getDesc() {
            return desc;
        }

        public void setDesc(String desc) {
            this.desc = desc;
        }

        PlatformType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
    }
}
