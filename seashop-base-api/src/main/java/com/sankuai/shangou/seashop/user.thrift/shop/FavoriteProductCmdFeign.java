package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AddFavoriteProductReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteFavoriteProductReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description：商品收藏修改类接口
 * @author： liweisong
 * @create： 2023/11/28 9:27
 */

/**
 * 商品收藏修改接口
 */
@FeignClient(name = "himall-base", contextId = "FavoriteProductCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/favoriteProduct")
public interface FavoriteProductCmdFeign {

    /**
     * 商品详情-收藏商品
     * @param addFavoriteProductReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/addFavoriteProduct", consumes = "application/json")
    ResultDto<BaseResp> addFavoriteProduct(@RequestBody AddFavoriteProductReq addFavoriteProductReq) throws TException;

    /**
     * 商品详情-删除商品
     * @param deleteFavoriteProductReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deleteFavoriteProduct", consumes = "application/json")
    ResultDto<BaseResp> deleteFavoriteProduct(@RequestBody DeleteFavoriteProductReq deleteFavoriteProductReq) throws TException;
}
