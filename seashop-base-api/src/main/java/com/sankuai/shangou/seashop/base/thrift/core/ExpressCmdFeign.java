package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:17
 */

@FeignClient(name = "himall-base",contextId = "ExpressCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/express")
public interface ExpressCmdFeign {

    /**
     * 平台-交易-快递设置-快递公司管理(新增)
     */
    @PostMapping(value = "/createExpressCompany", consumes = "application/json")
    ResultDto<BaseResp> createExpressCompany(@RequestBody AddExpressCompanyReq request) throws TException;

    /**
     * 平台-交易-快递设置-快递公司管理(编辑)
     */
    @PostMapping(value = "/updateExpressCompany", consumes = "application/json")
    ResultDto<BaseResp> updateExpressCompany(@RequestBody CmdExpressCompanyReq request) throws TException;

    /**
     * 平台-交易-快递设置-快递公司管理(模版设置)
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/updateExpressTemplate", consumes = "application/json")
    ResultDto<BaseResp> updateExpressTemplate(@RequestBody UpdateExpressTemplateReq request) throws TException;

    /**
     * 平台-交易-快递设置-快递公司管理(开启状态)
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/updateExpressStatus", consumes = "application/json")
    ResultDto<BaseResp> updateExpressStatus(@RequestBody UpdateExpressStatusReq request) throws TException;

    /**
     * 平台-交易-快递设置-快递公司管理(删除)
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deleteExpressCompany", consumes = "application/json")
    ResultDto<BaseResp> deleteExpressCompany(@RequestBody DeleteExpressCompanyReq request) throws TException;

    /**
     * 平台-交易-快递设置-物流设置(保存美团物流)
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/addOrUpdateSiteSetting", consumes = "application/json")
    ResultDto<BaseResp> addOrUpdateSiteSetting(@RequestBody ExpressSiteSettingReq request) throws TException;
}
