package com.sankuai.shangou.seashop.user.thrift.account.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryMemberListReq {
    /**
     * 会员名称列表
     */
    private List<String> userNames;

    /**
     * 会员id列表
     */
    private List<Long> memberIds;
}
