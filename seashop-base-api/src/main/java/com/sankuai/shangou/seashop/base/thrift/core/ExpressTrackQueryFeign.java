package com.sankuai.shangou.seashop.base.thrift.core;


import java.util.List;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.ExpressSubscribeReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.LoadSubscribedExpressReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ExpressTrackRes;


@FeignClient(name = "himall-base",contextId = "ExpressTrackQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/expressTrack")
public interface ExpressTrackQueryFeign {


    /**
     * 同步美团目前支持的快递编码
     * @return
     * @throws TException
     */
    @GetMapping(value="/synchroSupportCompany")
    ResultDto<Boolean> synchroSupportCompany() throws TException;

    /**
     * 订阅需要查询的物流编码
     * @param request
     * @return
     */
    @PostMapping(value = "/submitExpressSubscribe",consumes = "application/json")
    ResultDto<Long> submitExpressSubscribe(@RequestBody ExpressSubscribeReq request);


    /**
     * 批量订阅需要查询的物流编码
     * @param request
     * @return
     */
    @PostMapping(value="/batchSubmitExpressSubscribe", consumes = "application/json")
    ResultDto<Boolean> batchSubmitExpressSubscribe(@RequestBody List<ExpressSubscribeReq> request);


    /**
     * 根据美团快递编码，查询物流轨迹
     * @param request
     * @return
     */
    @PostMapping(value = "/loadSubscribedExpressByCompanyCodeAndExpressNo",consumes = "application/json")
    ResultDto<ExpressTrackRes> loadSubscribedExpressByCompanyCodeAndExpressNo(@RequestBody LoadSubscribedExpressReq request);


    /**
     * 根据海商快递编码，查询物流轨迹
     * @param request
     * @return
     */
    @PostMapping(value = "/loadSubscribedExpressByHiShopCompanyCodeAndExpressNo",consumes = "application/json")
    ResultDto<ExpressTrackRes> loadSubscribedExpressByHiShopCompanyCodeAndExpressNo(@RequestBody LoadSubscribedExpressReq request);

    /**
     * 获取物流剩余条数
     * @return
     * @throws TException
     */
    @PostMapping(value = "/queryBalance")
    ResultDto<Long> queryBalance() throws TException;
}
