package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;

/**
 * @description:
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class BusinessCategoryApplyResp extends BaseThriftDto {

    /**
     * id
     */
    private Long id;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 审核状态 0-待审核 1-已审核 2-审核拒绝
     */
    private Integer auditedStatus;

    /**
     * 审核状态描述
     */
    private String auditedStatusDesc;

    /**
     * 审核时间
     */
    private Date auditedDate;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 签署状态 0-待签署 1-已签署
     */
    private Integer agreementStatus;

    /**
     * 签署状态描述
     */
    private String agreementStatusDesc;

    /**
     * 合同ID
     */
    private Long shopAgreementId;

    /**
     * 是否需要补充资料
     */
    private Boolean needSupply;

    /**
     * 是否冻结
     */
    private Boolean whetherFrozen;
}
