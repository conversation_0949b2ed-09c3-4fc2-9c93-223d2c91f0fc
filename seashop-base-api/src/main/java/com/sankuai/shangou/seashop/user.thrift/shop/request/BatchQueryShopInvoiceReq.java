package com.sankuai.shangou.seashop.user.thrift.shop.request;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class BatchQueryShopInvoiceReq {

    /**
     * 店铺ID
     */
    private List<Long> shopIdList;

    public void checkParameter() {
        if (CollUtil.isEmpty(shopIdList)) {
            throw new InvalidParamException("店铺shopId不能为空");
        }
    }
}
