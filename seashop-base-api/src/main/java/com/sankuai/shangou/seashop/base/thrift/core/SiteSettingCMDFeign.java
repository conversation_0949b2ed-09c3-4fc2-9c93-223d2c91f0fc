package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.*;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "himall-base", contextId = "SiteSettingCMDFeign", url = "${himall-base.dev.url:}", path = "/himall-base/siteSetting")
public interface SiteSettingCMDFeign {

    /**
     * 保存站点设置
     * @param settingReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveSettings", consumes = "application/json")
    ResultDto<Boolean> saveSettings(@RequestBody BaseSiteSettingReq settingReq) throws TException;


    /**
     * 保存入驻设置
     * @param settledReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveSettled", consumes = "application/json")
    ResultDto<Boolean> saveSettled(@RequestBody BaseSettledReq settledReq) throws TException;


    /**
     * 保存协议
     * @param agreementReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveAgreement", consumes = "application/json")
    ResultDto<Boolean> saveAgreement(@RequestBody BaseAgreementReq agreementReq) throws TException;

    /**
     * 新增自定义表单
     * @param baseCustomFormReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/createCustomFrom", consumes = "application/json")
    ResultDto<Long> createCustomFrom(@RequestBody BaseCustomFormReq baseCustomFormReq) throws TException;

    /**
     * 修改自定义表单
     * @param baseCustomFormReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/updateCustomFrom", consumes = "application/json")
    ResultDto<Boolean> updateCustomFrom(@RequestBody BaseCustomFormReq baseCustomFormReq) throws TException;


    /**
     * 批量删除自定义表单
     * @param baseIdsReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deletesCustomFrom", consumes = "application/json")
    ResultDto<Boolean> deletesCustomFrom(@RequestBody BaseIdsReq baseIdsReq) throws TException;

    /**
     * 查询自定义表单是否重名
     * @param name
     * @return
     * @throws TException
     */
    @GetMapping(value = "/existCustomFrom")
    ResultDto<Boolean> existCustomFrom(@RequestParam String name) throws TException;

    /**
     * 保存站点店铺设置
     * @param settingReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveShopSettings", consumes = "application/json")
    ResultDto<Boolean> saveShopSettings(BaseShopSitSettingReq settingReq) throws TException;


    /**
     * 保存商品设置
     * @param settingReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveProductSettings", consumes = "application/json")
    ResultDto<BaseResp> saveProductSettings(@RequestBody SaveProductSettingReq settingReq) throws TException;


    /**
     * 保存所有协议
     * @param agreementReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveAllAgreement", consumes = "application/json")
    ResultDto<Boolean> saveAllAgreement(@RequestBody BaseAllAgreementReq agreementReq) throws TException;

    /**
     * 保存短信配置
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveSmsSetting", consumes = "application/json")
    ResultDto<BaseResp> saveSmsSetting(@RequestBody SmsSettingReq req) throws TException;

    /**
     * 保存风格
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveShopStyle", consumes = "application/json")
    ResultDto<BaseResp> saveShopStyle(@RequestBody SystemStyleReq req) throws TException;

    /**
     * 保存风格
     * @param req
     * @return
     * @throws TException
     */
    @PostMapping(value = "/saveExpressConfig", consumes = "application/json")
    ResultDto<BaseResp> saveExpressConfig(@RequestBody ExpressConfigReq req) throws TException;



}
