package com.sankuai.shangou.seashop.user.thrift.shop.enums;

/**
 * <AUTHOR>
 * @date 2023/12/08 15:06
 */
public enum ShopTypeEnum {
    MALL(1, "商城店铺"),
    QIAN_NIU_HUA(2, "牵牛花店铺"),
    YI_JIU_PI(3, "易九批店铺");

    private final Integer type;

    private final String desc;

    ShopTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByCode(Integer shopType) {
        for (ShopTypeEnum shopTypeEnum : ShopTypeEnum.values()) {
            if (shopTypeEnum.getType().equals(shopType)) {
                return shopTypeEnum.getDesc();
            }
        }
        return null;
    }

    public Integer getType() {
        return type;
    }

    public static ShopTypeEnum getEnumByType(Integer type) {
        for (ShopTypeEnum shopTypeEnum : ShopTypeEnum.values()) {
            if (shopTypeEnum.getType().equals(type)) {
                return shopTypeEnum;
            }
        }
        return null;
    }

    public String getDesc() {
        return desc;
    }
}
