package com.sankuai.shangou.seashop.user.thrift.account.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

/**
 * @description: 管理员信息
 * @author: LXH
 **/
@Data
public class EpManagerResp extends BaseThriftDto {
        /**
         * 管理员id
         */
        private Long managerId;

        /**
         * 管理员名称
         */
        private String managerName;

        /**
         * 供应商id
         */
        private Long shopId;

        /**
         * 供应商名称
         */
        private String shopName;

        /**
         * 商家id
         */
        private Long userId;

        /**
         * 用户名
         */
        private String userName;

        /**
         * 权限组id
         */
        private Long roleId;

        /**
         * 手机号码
         */
        private String cellphone;

        /**
         * 真实姓名
         */
        private String realName;

        /**
         * 微信用户ID
         */
        private String weiXinOpenId;

        /**
         * 密码是否符合规则
         */
        private Boolean passwordCorrect;

        /**
         * 用户名是否符合规则
         */
        private Boolean userNameCorrect;

        /**
         * 最后登录时间
         */
        private Date lastLoginTime;

        /**
         * 用户是否冻结
         */
        private Boolean userFreeze;

        /**
         * 店铺是否冻结
         */
        private Boolean shopFreeze;

        /**
         * 是否已经拥有ep账户
         */
        private Boolean epExist;

        /**
         * 现在的EP登录名称
         */
        private String epAccount;

        /**
         * 管理员是否删除
         */
        private boolean managerDelete = false;
}
