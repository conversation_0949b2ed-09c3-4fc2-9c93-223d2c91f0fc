package com.sankuai.shangou.seashop.base.thrift.core;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryCustomerFormFieldReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryCustomerFormReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormFieldListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormListRes;

/**
 * <AUTHOR>
 * @date 2023/11/30 15:43
 */
@FeignClient(name = "himall-base",contextId = "CustomFormQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/customForm")
public interface CustomFormQueryFeign {

    /**
     * 根据表单id的集合查询表单信息
     */
    @PostMapping(value = "/queryCustomFormByFormIds", consumes = "application/json")
    ResultDto<BaseCustomFormListRes> queryCustomFormByFormIds(@RequestBody QueryCustomerFormReq request) throws TException;

    /**
     * 根据表单字段id的集合查询字段信息
     */
    @PostMapping(value = "/queryCustomFieldByFieldIds", consumes = "application/json")
    ResultDto<BaseCustomFormFieldListRes> queryCustomFieldByFieldIds(@RequestBody QueryCustomerFormFieldReq request) throws TException;
}
