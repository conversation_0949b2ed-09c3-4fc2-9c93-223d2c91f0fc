package com.sankuai.shangou.seashop.base.thrift.core;


import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.TradeSiteSettingsReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author： liweisong
 * @create： 2023/11/22 16:50
 */
@FeignClient(name = "himall-base", contextId = "TradeSettingsCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/tradeSettings")
public interface TradeSettingsCmdFeign {

    /**
     * 交易参数设置
     * @param tradeSiteSettingsReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/addOrUpdateSiteSetting", consumes = "application/json")
    ResultDto<BaseResp> addOrUpdateSiteSetting(@RequestBody TradeSiteSettingsReq tradeSiteSettingsReq) throws TException;
}
