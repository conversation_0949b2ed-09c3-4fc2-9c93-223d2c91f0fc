package com.sankuai.shangou.seashop.user.thrift.shop.enums;


public enum VerificationTypeEnum {
    PHONE(0, "验证手机"),
    EMAIL(1, "验证邮箱"),
    ALL(2, "均需验证");
    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    VerificationTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    //根据code获取枚举
    public static VerificationTypeEnum getEnumByCode(Integer code) {
        for (VerificationTypeEnum verificationType : VerificationTypeEnum.values()) {
            if (verificationType.getCode().equals(code)) {
                return verificationType;
            }
        }
        return null;
    }
}
