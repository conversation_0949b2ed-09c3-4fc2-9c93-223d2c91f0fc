package com.sankuai.shangou.seashop.user.thrift.account.dto.freight;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CalculateFreightProductDto extends BaseParamReq {
    /**
     * 商品ID
     */
    private Long productId;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 运费模板ID
     */
    private Long templateId;

    /**
     * 重量
     */
    private BigDecimal weight;

    /**
     * 体积
     */
    private BigDecimal volume;

    /**
     * 商品总金额
     */
    private BigDecimal productAmount;

    /**
     * 购买数量
     */
    private Long buyCount;

    /**
     * 商品名称，用于异常提示
     */
    private String productName;

}
