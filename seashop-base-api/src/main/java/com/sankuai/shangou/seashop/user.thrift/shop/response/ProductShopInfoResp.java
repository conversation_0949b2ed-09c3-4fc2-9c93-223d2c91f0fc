package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: LXH
 **/
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ProductShopInfoResp extends BaseThriftDto {
    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 店id
     */
    private Long id;
    /**
     * 手机
     */
    private String phone;
    /**
     * 店铺名称
     */
    private String name;
    /**
     * 店铺地址
     */
    private String address;
    /**
     * 包装评分
     */
    private BigDecimal packMark;
    /**
     * 服务评分
     */
    private BigDecimal serviceMark;
    /**
     * 综合评分
     */
    private BigDecimal comprehensiveMark;
    /**
     * 商品评分
     */
    private BigDecimal productMark;
    /**
     * 是否自营
     */
    private Boolean whetherSelf;
    /**
     * 品牌LOGO
     */
    private String brandLogo;
    /**
     * 品牌ID
     */
    private Long brandId;
    /**
     * 保证金
     */
    private BigDecimal cashDeposits;
    /**
     * 店铺已缴纳保证金
     */
    private BigDecimal cashDepositPaid;
    /**
     * 店铺保证金欠费金额
     */
    private BigDecimal cashDepositNeedPay;
    /**
     * 是否支持7天无理由退货
     */
    private Boolean sevenDayNoReasonReturn;
    /**
     * 是否支持消费者保障
     */
    private Boolean customerSecurity;
    /**
     * 是否支持及时发货
     */
    private Boolean timelyDelivery;
    /**
     * 是否虚拟品牌
     */
    private Boolean virtual;
    /**
     * 入驻时间
     */
    private Date createTime;
    /**
     * 店铺图片
     */
    private String logo;
    /**
     * 店铺商品数
     */
    private Long productCount;
    /**
     * 店铺关注人数
     */
    private Long followCount;
    /**
     * 用户是否已关注当前店铺
     */
    private Boolean followFlag;
}
