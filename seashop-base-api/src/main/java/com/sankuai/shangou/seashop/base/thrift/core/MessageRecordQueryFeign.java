package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.MessageRecordQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordDetailResp;
import com.sankuai.shangou.seashop.base.thrift.core.response.MessageRecordResp;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-base",contextId = "MessageRecordQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/messageRecord")
public interface MessageRecordQueryFeign {

    /**
     * 已发送消息
     * @param apiSendEmailMsgReq
     * @return
     */
    @PostMapping(value = "/queryPage", consumes = "application/json")
    ResultDto<BasePageResp<MessageRecordResp>> queryPage(@RequestBody MessageRecordQueryReq apiSendEmailMsgReq);

    /**
     * 查询消息详情
     * @param id
     * @return
     */
    @PostMapping(value = "/queryDetail", consumes = "application/json")
    ResultDto<MessageRecordDetailResp> queryDetail(@RequestBody BaseIdReq id);
}
