package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.InvoiceTitleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.InvoiceTitleRespList;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description: 发票抬头服务类
 * @author: LXH
 **/
@FeignClient(name = "himall-base", contextId = "InvoiceTitleQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/invoiceTitle")
public interface InvoiceTitleQueryFeign {

    /**
     * 查询发票抬头列表
     *
     * @param queryReq 查询条件
     * @return 发票抬头列表
     */
    @PostMapping(value = "/queryList", consumes = "application/json")
    ResultDto<InvoiceTitleRespList> queryList(@RequestBody InvoiceTitleQueryReq queryReq) throws TException;

}
