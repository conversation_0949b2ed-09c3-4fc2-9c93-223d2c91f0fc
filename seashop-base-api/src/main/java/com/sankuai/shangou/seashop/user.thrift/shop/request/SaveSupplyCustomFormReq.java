package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.BusinessCategoryApplyFormDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:28
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class SaveSupplyCustomFormReq extends BaseParamReq {

    /**
     * 申请记录id
     */
    private Long id;

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 表单数据
     */
    private List<BusinessCategoryApplyFormDto> formList;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(id == null || id <= 0, "申请记录id不能为空");
        AssertUtil.throwInvalidParamIfTrue(shopId == null || shopId <= 0, "店铺id不能为空");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(formList), "表单数据不能为空");
        formList.forEach(form -> form.checkParameter());
    }

}
