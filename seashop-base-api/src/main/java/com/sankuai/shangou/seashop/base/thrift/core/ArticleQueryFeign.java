package com.sankuai.shangou.seashop.base.thrift.core;

import java.util.List;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseArticleQueryReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryTopArticleReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.ArticleCategoryListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseArticleCategoryRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseArticleRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.SystemArticleCategoryRes;

@FeignClient(name = "himall-base",contextId = "ArticleQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/article")
public interface ArticleQueryFeign {

    @PostMapping(value = "/queryWithPage", consumes = "application/json")
    ResultDto<BasePageResp<BaseArticleRes>> queryWithPage(@RequestBody BaseArticleQueryReq query) throws TException;

    @PostMapping(value = "/queryTopByCategoryId", consumes = "application/json")
    ResultDto<List<BaseArticleRes>> queryTopByCategoryId(@RequestBody QueryTopArticleReq query) throws TException;

    @GetMapping(value = "/queryAllCategory")
    ResultDto<ArticleCategoryListRes> queryAllCategory() throws TException;

    @PostMapping(value = "/getArticleById", consumes = "application/json")
    ResultDto<BaseArticleRes> getArticleById(@RequestBody BaseReq query) throws TException;

    @PostMapping(value = "/getBaseArticleCategoryById", consumes = "application/json")
    ResultDto<BaseArticleCategoryRes> getBaseArticleCategoryById(@RequestBody BaseReq query) throws TException;


    @PostMapping(value = "/getBaseArticleCategoryByParentId", consumes = "application/json")
    ResultDto<List<BaseArticleCategoryRes>> getBaseArticleCategoryByParentId(@RequestBody BaseReq query) throws TException;


    @PostMapping(value = "/getArticleCategorysByParentId", consumes = "application/json")
    ResultDto<List<SystemArticleCategoryRes>> getArticleCategorysByParentId(@RequestBody BaseReq query) throws TException;
}
