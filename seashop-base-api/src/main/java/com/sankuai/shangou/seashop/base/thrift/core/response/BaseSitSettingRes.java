package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import lombok.*;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class BaseSitSettingRes extends BaseThriftDto {
    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 站点logo
     */
    private String logo;

    /**
     * 微信logo
     */
    private String wxLogo;

    /**
     * 官方水印
     */
    private String officeMark;

    /**
     * 卖家端logo
     */
    private String memberLogo;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * pc商城登录图片
     */
    private String pcLoginPic;

    /**
     * pc商城登录按钮
     */
    private String pcBottomPic;

    /**
     * 客服电话
     */
    private String sitePhone;

    /**
     * 网店授权域名
     */
    private String siteUrl;

    /**
     * 输入框关键字
     */
    private String inputBoxKeyWords;

    /**
     * 热门关键字
     */
    private String popularKeyWords;

    /**
     * 页脚
     */
    private String pageFoot;

    /**
     * SEO
     */
    private String siteSEOTitle;
    /**
     * SEO关键字
     */
    private String siteSEOKeywords;
    /**
     * SEO详情
     */
    private String siteSEODescription;

    /**
     * 注册类型 0 普通账号  1 手机号
     */
    private String registerType;

    /**
     * 是否打开邮箱效验
     */
    private String emailVerifOpen;

    /**
     * 是否强制绑定手机
     */
    private String isConBindCellPhone;

    /**
     * QQ地图key
     */
    private String qQMapAPIKey;

    /**
     * 高德地图key
     */
    private String jDRegionAppKey;

    /**
     * 底部服务
     */
    private String footServer;

    //第三方流量统计代码
    /**
     * 第三方流量统计代码
     */
    @ExaminField(description = "第三方流量统计代码")
    private String thirdPartyFlowCode;

    //    是否提供app下载
    /**
     * 是否提供app下载
     */
    @ExaminField(description = "是否提供app下载")
    private String isAppDownload;

    //    商城app版本号
    /**
     * 商城app版本号
     */
    @ExaminField(description = "商城app版本号")
    private String appVersion;

    //    appleStore
    @ExaminField(description = "appleStore")
    private String appleStore;

    //    android
    @ExaminField(description = "android")
    private String android;

    //    供应商app更新说明
    /**
     * 供应商app更新说明
     */
    @ExaminField(description = "供应商app更新说明")
    private String appUpdate;

    /**
     * 是否演示站点
     */
    @ExaminField(description = "是否演示站点")
    private String isTestApi;
}
