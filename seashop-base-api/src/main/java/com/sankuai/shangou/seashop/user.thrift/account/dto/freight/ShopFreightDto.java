package com.sankuai.shangou.seashop.user.thrift.account.dto.freight;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopFreightDto extends BaseParamReq {

    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 运费
     */
    private BigDecimal freight;

}
