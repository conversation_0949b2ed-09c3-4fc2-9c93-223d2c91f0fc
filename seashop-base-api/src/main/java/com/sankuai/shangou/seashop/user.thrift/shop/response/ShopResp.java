package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Date;
import java.util.List;

/**
 * @description: 供应商信息返回参数
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopResp extends BaseThriftDto {

    /**
     * 供应商ID
     */
    private Long id;

    /**
     * 供应商等级
     */
    private Long gradeId;

    /**
     * 供应商名称
     */
    private String shopName;

    /**
     * 店铺状态
     */
    private Integer shopStatus;

    /**
     * 店铺状态文本
     */
    private String shopStatusDesc;

    /**
     * 汇付操作状态
     */
    private Integer adapayStatus;

    /**
     * 汇付操作状态文本
     */
    private String adapayStatusDesc;

    /**
     * 平台审核状态
     */
    private Integer plateStatus;

    /**
     * 平台审核状态文本
     */
    private String plateStatusDesc;

    /**
     * 更新汇付操作状态
     */
    private Integer updateAdaPayStatus;

    /**
     * 更新汇付操作状态文本
     */
    private String updateAdaPayStatusDesc;

    /**
     * 店铺创建时间
     */
    private Date createDate;

    /**
     * 店铺过期时间
     */
    private Date endDate;

    /**
     * 联系人姓名
     */
    private String contactsName;

    /**
     * 联系人电话
     */
    private String contactsPhone;

    /**
     * 联系人Email
     */
    private String contactsEmail;

    /**
     * 银行开户名
     */
    private String bankAccountName;

    /**
     * 银行账号
     */
    private String bankAccountNumber;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 支行联行号
     */
    private String bankCode;

    /**
     * 开户银行所在地
     */
    private Integer bankRegionId;

    /**
     * 开户银行类型（1对公，2对私）
     */
    private Integer bankType;

    /**
     * 商家发货人名称
     */
    private String senderName;

    /**
     * 商家发货人地址
     */
    private String senderAddress;

    /**
     * 商家发货人电话
     */
    private String senderPhone;

    /**
     * 是否缴纳保证金
     */
    private Boolean whetherPayBond;

    /**
     * 是否签署协议
     */
    private Boolean whetherAgreement;

    /**
     * 是否是需要补充资料
     */
    private Boolean whetherSupply;

    /**
     * 是否需要续签合同
     */
    private Boolean whetherAgainSign;

    /**
     * 是否官方自营
     */
    private Boolean whetherSelf;

    /**
     * 业务类型 1个人供应商 0企业供应商
     */
    private Integer businessType;

    /**
     * 店铺账户
     */
    private String shopAccount;

    /**
     * 店铺类型 1商城店铺 2牵牛花店铺 3易九批店铺
     */
    private Integer shopType;

    /**
     * 店铺类型描述 1商城店铺 2牵牛花店铺 3易九批店铺
     */
    private String shopTypeDesc;

    /**
     * 经营类目
     */
    private String fullCategoryName;

    /**
     * 是否开启专属商家
     */
    private Boolean whetherOpenExclusiveMember;

    /**
     * 汇付商家ID
     */
    private Long adaMemberId;

    /**
     * 排序序号
     */
    private Integer serialNumber;

    /**
     * 排序序号
     */
    private List<Long> categoryIds;
}