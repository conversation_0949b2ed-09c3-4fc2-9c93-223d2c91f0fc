package com.sankuai.shangou.seashop.user.thrift.account.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: 专属商家入参
 * @author: liweisong
 * @date: 2023-11-17
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class AddExclusiveMemberReq extends BaseParamReq {

    @ExaminField(description = "店铺ID")
    private Long shopId;

    @ExaminField(description = "商家账号")
    private String userName;

    @ExaminField(description = "商家ID")
    private Long userId;

    public void checkParameter(){
        if(shopId == null){
            throw new IllegalArgumentException("shopId不能为空");
        }
    }
}
