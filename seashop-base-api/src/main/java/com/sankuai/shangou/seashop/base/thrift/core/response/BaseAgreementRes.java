package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.Data;

import java.util.Date;

@Data
public class BaseAgreementRes extends BaseThriftDto {
    private Long id;

    /**
     * 协议类型 0买家注册协议，1卖家入驻协议 ,2APP关于我们,3隐私政策,4卖家入驻协议文件
     */
    private int agreementType;
    /**
     * 最后修改时间
     */
    private Date lastUpdateTime;

    /**
     * 协议内容
     */
    private String agreementContent;
}
