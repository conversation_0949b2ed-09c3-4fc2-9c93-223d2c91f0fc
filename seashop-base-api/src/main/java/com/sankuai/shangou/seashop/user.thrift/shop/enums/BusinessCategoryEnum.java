package com.sankuai.shangou.seashop.user.thrift.shop.enums;

/**
 * @description:
 * @author: LXH
 **/
public class BusinessCategoryEnum {

    public enum ApplyStatus {
        UnAudited(0, "待审核"),
        Audited(1, "审核通过"),
        Refused(2, "已拒绝");
        private final Integer code;
        private final String desc;

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        ApplyStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDescByCode(Integer code) {
            for (ApplyStatus value : ApplyStatus.values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return null;
        }
    }

    public enum AgreementStatus {
        UnAudited(0, "待签署"),
        Audited(1, "已签署");
        private final Integer code;
        private final String desc;

        AgreementStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }


        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static String getDescByCode(Integer code) {
            for (AgreementStatus value : AgreementStatus.values()) {
                if (value.getCode().equals(code)) {
                    return value.getDesc();
                }
            }
            return null;
        }
    }

}
