package com.sankuai.shangou.seashop.user.thrift.shop.response.es;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ShopBrandEsResp extends BaseThriftDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 品牌ID
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 品牌logo
     */
    private String logo;
}
