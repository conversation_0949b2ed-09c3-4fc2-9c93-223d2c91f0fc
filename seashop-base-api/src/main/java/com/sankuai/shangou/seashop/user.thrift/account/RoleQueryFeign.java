package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.QueryRoleReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.RoleRespList;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 权限组查询服务类
 */
@FeignClient(name = "himall-base", contextId = "RoleQueryFeign", path = "/himall-base/account/role", url = "${himall-base.dev.url:}")
public interface RoleQueryFeign {

    /**
     * 查询权限组列表
     * @return 删除的权限组ID
     */
    @PostMapping(value = "/queryRoleList", consumes = "application/json")
    ResultDto<RoleRespList> queryRoleList(@RequestBody QueryRoleReq queryRoleReq) throws TException;

}
