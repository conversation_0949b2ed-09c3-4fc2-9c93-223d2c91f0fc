package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/5 16:18
 */
@Data
public class SwapDisplaySequenceReq extends BaseParamReq {

    /**
     * idOne
     */
    @PrimaryField
    private Long idOne;

    /**
     * idOneSequence
     */
    @ExaminField(description = "序号")
    private Integer idOneSequence;

    /**
     * idTwo
     */
    private Long idTwo;

    /**
     * idTwoSequence
     */
    private Integer idTwoSequence;


    public void checkParameter() {
        if (idOne == null) {
            throw new IllegalArgumentException("idOne 不能为空");
        }
        if (idOneSequence == null) {
            throw new IllegalArgumentException("idOneSequence 不能为空");
        }
        if (idTwo == null) {
            throw new IllegalArgumentException("idTwo 不能为空");
        }
        if (idTwoSequence == null) {
            throw new IllegalArgumentException("idTwoSequence 不能为空");
        }
    }
}
