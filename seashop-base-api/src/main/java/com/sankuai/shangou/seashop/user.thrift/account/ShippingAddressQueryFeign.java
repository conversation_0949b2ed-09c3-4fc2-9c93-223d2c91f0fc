package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.dto.shippingAddress.ShippingAddressDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress.GetShippingAddressByIdReq;
import com.sankuai.shangou.seashop.user.thrift.account.request.shippingAddress.PageShippingAddressReq;
import com.sankuai.shangou.seashop.user.thrift.account.response.shippingAddress.DefaultShippingAddressResp;
import com.sankuai.shangou.seashop.user.thrift.account.response.shippingAddress.ShippingAddressListResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */

/**
 * 用户收货地址查询接口
 */
@FeignClient(name = "himall-base", contextId = "ShippingAddressQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/shippingAddress")
public interface ShippingAddressQueryFeign {

    /**
     * 获取用户默认的收货地址
     *
     * @param userId 商家用户ID，必填
     * @return 默认收货地址，如果没有地址则返回空；如果有地址，但没有标识为默认的，则根据时间返回第一个
     */
    @GetMapping(value = "/getDefaultAddress")
    ResultDto<DefaultShippingAddressResp> getDefaultAddress(@RequestParam Long userId) throws TException;

    /**
     * 根据ID获取收货地址
     * @param getReq 查询参数，包含userId用户判断水平越权
     * @return 收货地址
     */
    @PostMapping(value = "/getById", consumes = "application/json")
    ResultDto<DefaultShippingAddressResp> getById(@RequestBody GetShippingAddressByIdReq getReq) throws TException;

    /**
     * 获取用户收货地址列表
     *
     * @param userId 商家用户ID，必填
     * @return 用户收货地址列表，如果还没有设置，返回空数组
     */
    @GetMapping(value = "/getList")
    ResultDto<ShippingAddressListResp> getList(@RequestParam Long userId) throws TException;


    /**
     * 获取用户收货地址列表(分页的）
     *
     * @param request 商家用户ID，必填
     * @return 用户收货地址列表，如果还没有设置，返回空数组
     */
    @PostMapping(value = "/pageShippingAddress",consumes = "application/json")
    ResultDto<BasePageResp<ShippingAddressDto>> pageShippingAddress(@RequestBody PageShippingAddressReq request) throws TException;
}
