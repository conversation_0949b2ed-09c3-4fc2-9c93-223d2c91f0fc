package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddRefundReasonReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.DeleteRefundReasonReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateRefundReasonReq;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:17
 */
@FeignClient(name = "himall-base", contextId = "RefundReasonCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/refundReason")
public interface RefundReasonCmdFeign {

    /**
     * 平台-交易-交易设置-售后原因（新增）
     * @param addRefundReasonReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/addRefundReason", consumes = "application/json")
    ResultDto<BaseResp> addRefundReason(@RequestBody AddRefundReasonReq addRefundReasonReq) throws TException;

    /**
     * 平台-交易-交易设置-售后原因（修改）
     * @param cmdRefundReasonReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/updateRefundReason", consumes = "application/json")
    ResultDto<BaseResp> updateRefundReason(@RequestBody UpdateRefundReasonReq cmdRefundReasonReq) throws TException;


    /**
     * 平台-交易-交易设置-售后原因（删除）
     * @param deleteRefundReasonReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deleteRefundReason", consumes = "application/json")
    ResultDto<BaseResp> deleteRefundReason(@RequestBody DeleteRefundReasonReq deleteRefundReasonReq) throws TException;
}
