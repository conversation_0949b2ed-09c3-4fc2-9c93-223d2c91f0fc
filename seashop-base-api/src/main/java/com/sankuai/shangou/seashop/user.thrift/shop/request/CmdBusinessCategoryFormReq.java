package com.sankuai.shangou.seashop.user.thrift.shop.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * @description: z自定义表单分类请求入参
 * @author: LXH
 **/
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class CmdBusinessCategoryFormReq {
    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 分类ID
     */
    private Long categoryId;
    /**
     * 自定义表单ID
     */
    private Long formId;
    /**
     * 自定义数据
     */
    private String formData;
    /**
     * 自定义表单
     */
    private String customJson;
    /**
     * 根据自定义Id获取字段值
     */
    private String fieldData;
    /**
     * 自定义表单名称
     */
    private String formName;
}
