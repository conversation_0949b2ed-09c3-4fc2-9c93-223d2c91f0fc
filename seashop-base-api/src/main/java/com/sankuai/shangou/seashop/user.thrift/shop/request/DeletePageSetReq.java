package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/5 17:30
 */
@Data
public class DeletePageSetReq extends BaseParamReq {

    @PrimaryField
    private Long id;

    @ExaminField(description = "店铺ID")
    private Long shopId;

    public void checkParameter(){
        if(id == null){
            throw new IllegalArgumentException("id 不能为空");
        }
    }
}
