package com.sankuai.shangou.seashop.user.thrift.shop.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:55
 */
@Data
public class QueryShopInvoiceResp extends BaseParamReq {


    /**
     * 主键
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 是否提供发票
     */
    private Boolean whetherInvoice;

    /**
     * 是否提供普通发票
     */
    private Boolean whetherPlainInvoice;

    /**
     * 是否提供电子发票
     */
    private Boolean whetherElectronicInvoice;

    /**
     * 普通发票税率
     */
    private BigDecimal plainInvoiceRate;

    /**
     * 是否提供增值税发票
     */
    private Boolean whetherVatInvoice;

    /**
     * 订单完成后多少天开具增值税发票
     */
    private Integer vatInvoiceDay;

    /**
     * 增值税税率
     */
    private BigDecimal vatInvoiceRate;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
