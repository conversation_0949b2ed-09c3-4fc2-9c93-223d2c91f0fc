package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

/**
 * @author： liweisong
 * @create： 2023/12/14 16:30
 */
@Data
public class ShopPageDetailReq extends BaseParamReq {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 1:商城，2供应商，商城端需要看风控通过的，默认供应商
     */
    private Integer clientFrom = 2;
}
