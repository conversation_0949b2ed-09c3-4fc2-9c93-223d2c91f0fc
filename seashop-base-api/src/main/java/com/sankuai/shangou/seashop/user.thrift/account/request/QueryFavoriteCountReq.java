package com.sankuai.shangou.seashop.user.thrift.account.request;

import cn.hutool.core.collection.CollUtil;
import com.sankuai.shangou.seashop.base.boot.exception.InvalidParamException;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/23/023
 * @description:
 */
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Data
public class QueryFavoriteCountReq extends BaseParamReq {

    /**
     * 用户id
     */
    private Long memberId;

    /**
     * 店铺id列表
     */
    private List<Long> shopIdList;

    @Override
    public void checkParameter() {
        if (CollUtil.isEmpty(this.shopIdList)) {
            throw new InvalidParamException("shopIdList不能为空");
        }
    }

}
