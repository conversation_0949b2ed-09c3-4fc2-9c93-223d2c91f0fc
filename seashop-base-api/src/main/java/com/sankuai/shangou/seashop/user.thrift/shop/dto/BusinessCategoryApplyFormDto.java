package com.sankuai.shangou.seashop.user.thrift.shop.dto;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;
import lombok.ToString;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:53
 */
@ToString
@Data
public class BusinessCategoryApplyFormDto extends BaseParamReq {

    /**
     * 表单id
     */
    private Long formId;

    /**
     * 类目id
     */
    private Long categoryId;

    /**
     * 表单数据
     */
    private List<BusinessCategoryApplyFormFieldDto> fieldList;

    @Override
    public void checkParameter() {
        AssertUtil.throwInvalidParamIfTrue(formId == null || formId <= 0, "表单id不能为空");
        AssertUtil.throwInvalidParamIfTrue(categoryId == null || categoryId <= 0, "类目id不能为空");
        AssertUtil.throwInvalidParamIfTrue(CollectionUtils.isEmpty(fieldList), "表单数据不能为空");
        fieldList.forEach(field -> field.checkParameter());
    }
}
