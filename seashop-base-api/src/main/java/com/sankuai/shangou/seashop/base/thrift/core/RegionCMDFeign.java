package com.sankuai.shangou.seashop.base.thrift.core;


import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseRegionReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.BaseUpdateRegionReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.base.BaseIdsReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@FeignClient(name = "himall-base", contextId = "RegionCMDFeign", url = "${himall-base.dev.url:}", path = "/himall-base/region")
public interface RegionCMDFeign {

    /**
     * 初始化地区数据
     * @return
     * @throws TException
     */
    @GetMapping(value = "/initData")
    ResultDto<Boolean> initData() throws TException;

    /**
     * 初始化地区数据
     * @return
     * @throws TException
     */
    @GetMapping(value = "/initHishopData")
    ResultDto<Boolean> initHishopData() throws TException;

    /**
     * 同步美团地区编码至海商的地址库中，使用区域简称匹配，如匹配不是则放弃
     * @return
     * @throws TException
     */
    @GetMapping(value = "/SynMTRegionCode")
    ResultDto<Boolean> SynMTRegionCode() throws TException;

    /**
     * 添加地区
     * @param regionReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/createRegion", consumes = "application/json")
    ResultDto<Long> createRegion(@RequestBody BaseRegionReq regionReq) throws TException;


    /**
     * 添加地区
     * @param regionReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/createRegion", consumes = "application/json")
    ResultDto<Boolean> updateRegion(@RequestBody BaseUpdateRegionReq regionReq) throws TException;

    /**
     * 删除地区(兼容批量)
     * @param idsReq
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deleteRegion", consumes = "application/json")
    ResultDto<Boolean> deleteRegion(@RequestBody BaseIdsReq idsReq) throws TException;

    /**
     * 删除地区(兼容批量)
     * @return
     * @throws TException
     */
    @GetMapping(value = "reviewRegion")
    ResultDto<Boolean> reviewRegion() throws TException;

}
