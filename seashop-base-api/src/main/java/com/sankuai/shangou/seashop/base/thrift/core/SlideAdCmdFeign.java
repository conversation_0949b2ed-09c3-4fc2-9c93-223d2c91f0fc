package com.sankuai.shangou.seashop.base.thrift.core;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.thrift.core.request.AddLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.MoveLimitTimeBuyReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.UpdateLimitTimeBuyReq;

import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@FeignClient(name = "himall-base", contextId = "SlideAdCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/slideAd")
public interface SlideAdCmdFeign {

    /**
     * 新增限时购轮播图
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/addLimitTimeBuy", consumes = "application/json")
    ResultDto<BaseResp> addLimitTimeBuy(@RequestBody AddLimitTimeBuyReq request) throws TException;

    /**
     * 修改限时购轮播图
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/updateLimitTimeBuy", consumes = "application/json")
    ResultDto<BaseResp> updateLimitTimeBuy(@RequestBody UpdateLimitTimeBuyReq request) throws TException;

    /**
     * 限时购轮播图位置移动
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/moveLimitTimeBuy", consumes = "application/json")
    ResultDto<BaseResp> moveLimitTimeBuy(@RequestBody MoveLimitTimeBuyReq request) throws TException;

    /**
     * 删除限时购轮播图
     *
     * @param request
     * @return
     * @throws TException
     */
    @PostMapping(value = "/deleteLimitTimeBuy", consumes = "application/json")
    ResultDto<BaseResp> deleteLimitTimeBuy(@RequestBody BaseIdReq request) throws TException;
}
