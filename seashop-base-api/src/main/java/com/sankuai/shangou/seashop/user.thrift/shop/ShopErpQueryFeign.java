package com.sankuai.shangou.seashop.user.thrift.shop;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryJstToExpireShopErpPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopErpByJstReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopErpByTokenReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopErpReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopErpDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopErpResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.WdtTokenResp;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 供应商后台店铺的erp管理接口
 * @author： liweisong
 * @create： 2023/11/28 14:56
 */
@FeignClient(name = "himall-base", contextId = "ShopErpQueryFeign", url = "${himall-base.dev.url:}", path = "/himall-base/shop/shopErp")
public interface ShopErpQueryFeign {

    /**
     * 供应商后台-店铺-erp管理(查看)
     */
    @PostMapping(value = "/queryShopErp", consumes = "application/json")
    ResultDto<QueryShopErpResp> queryShopErp(@RequestBody QueryShopErpReq queryShopErpReq) throws TException;

    /**
     * 通过erp类型和token查询erp配置信息
     */
    @PostMapping(value = "/queryShopByErpToken", consumes = "application/json")
    ResultDto<Long> queryShopByErpToken(@RequestBody QueryShopErpByTokenReq req) throws TException;

    /**
     * 查询聚水潭token快过期的数据
     */
    @PostMapping(value = "/queryJstToExpireShopErpPage", consumes = "application/json")
    ResultDto<List<QueryShopErpDetailResp>> queryJstToExpireShopErpPage(@RequestBody QueryJstToExpireShopErpPageReq req) throws TException;

    /**
     * 根据jst参数查询
     */
    @PostMapping(value = "/queryErpByJst", consumes = "application/json")
    ResultDto<List<QueryShopErpDetailResp>> queryErpByJst(@RequestBody QueryShopErpByJstReq req) throws TException;

    /**
     * 查询旺店通授权码
     */
    @PostMapping(value = "/queryWdtTokenByShopId", consumes = "application/json")
    ResultDto<WdtTokenResp> queryWdtTokenByShopId(@RequestBody QueryShopErpReq req) throws TException;

}
