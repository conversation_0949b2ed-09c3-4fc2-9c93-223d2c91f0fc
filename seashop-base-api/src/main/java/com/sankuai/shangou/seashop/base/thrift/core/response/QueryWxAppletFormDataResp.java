package com.sankuai.shangou.seashop.base.thrift.core.response;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 14:14
 */
@Data
public class QueryWxAppletFormDataResp extends BaseParamReq {

    private Long id;

    /**
     * 事件ID
     */
    private Long eventId;

    /**
     * 事件值
     */
    private String eventValue;

    /**
     * 事件的表单ID
     */
    private String formId;

    /**
     * 事件时间
     */
    private Date eventTime;

    /**
     * FormId过期时间
     */
    private Date expireTime;
}
