package com.sankuai.shangou.seashop.user.thrift.shop.request;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/15 15:52
 */
@Data
public class EnoughCashFlagReq extends BaseParamReq {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 经营类目ID
     */
    private Long categoryId;

    public void checkParameter() {
        AssertUtil.throwIfNull(shopId, "店铺ID不能为空");
        AssertUtil.throwIfNull(categoryId, "经营类目ID不能为空");
    }
}
