package com.sankuai.shangou.seashop.user.thrift.account;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.user.thrift.account.request.InvoiceTitleCmdReq;
import org.apache.thrift.TException;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * @description: 发票抬头服务类
 * @author: LXH
 **/
@FeignClient(name = "himall-base", contextId = "InvoiceTitleCmdFeign", url = "${himall-base.dev.url:}", path = "/himall-base/account/invoiceTitle")
public interface InvoiceTitleCmdFeign {

    /**
     * 保存发票抬头
     *
     * @param cmdReq 保存条件
     * @return 保存结果
     */
    @PostMapping(value = "/save", consumes = "application/json")
    ResultDto<BaseResp> save(@RequestBody InvoiceTitleCmdReq cmdReq) throws TException;

    /**
     * 删除发票抬头
     *
     * @param cmdReq 删除条件
     * @return 删除结果
     */
    @PostMapping(value = "/delete", consumes = "application/json")
    ResultDto<BaseResp> delete(@RequestBody InvoiceTitleCmdReq cmdReq)  throws TException;

}
