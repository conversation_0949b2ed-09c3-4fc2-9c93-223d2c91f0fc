package com.sankuai.shangou.seashop.user.thrift.shop.request;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * @description:
 * @author: LXH
 **/
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ProductShopQueryReq {
    //店铺id
    @NotBlank(message = "店铺id不能为空")
    private Long shopId;
    /**
     * 商品ID
     */
    private String productId;
    /**
     * 商家Id
     */
    private Long userId;
}
