package com.sankuai.shangou.seashop.user.shop.service;

import com.sankuai.shangou.seashop.user.thrift.shop.request.AddFavoriteProductReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteFavoriteProductReq;

/**
 * @author： liweisong
 * @create： 2023/11/28 9:50
 */
public interface FavoriteProductCmdService {

    /**
     * 商品关注
     * @param addFavoriteProductReq
     * @return
     */
    void addFavoriteProduct(AddFavoriteProductReq addFavoriteProductReq);

    /**
     * 删除商品关注
     * @param deleteFavoriteProductReq
     * @return
     */
    void deleteFavoriteProduct(DeleteFavoriteProductReq deleteFavoriteProductReq);
}
