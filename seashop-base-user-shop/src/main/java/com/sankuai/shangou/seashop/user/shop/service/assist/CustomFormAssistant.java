package com.sankuai.shangou.seashop.user.shop.service.assist;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.core.service.CustomFormService;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryCustomerFormFieldReq;
import com.sankuai.shangou.seashop.base.thrift.core.request.QueryCustomerFormReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormFieldListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormListRes;
import com.sankuai.shangou.seashop.base.thrift.core.response.BaseCustomFormRes;
import com.sankuai.shangou.seashop.user.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.user.common.remote.base.model.RemoteFormBo;
import com.sankuai.shangou.seashop.user.common.remote.base.model.RemoteFormFieldBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/30 15:20
 */
@Service
@Slf4j
public class CustomFormAssistant {

    @Resource
    private CustomFormService customFormService;


    public List<RemoteFormBo> listCustomerForms(List<Long> formIds) {
        if (CollectionUtils.isEmpty(formIds)) {
            return Collections.EMPTY_LIST;
        }

        List<RemoteFormBo> result = new ArrayList<>();
        List<List<Long>> subIdsArr = Lists.partition(formIds, CommonConstant.BATCH_QUERY_SIZE);
        subIdsArr.forEach(subIds -> {
            List<BaseCustomFormRes> forms = customFormService.queryCustomFormByFormIds(subIds);
            result.addAll(JsonUtil.copyList(forms, RemoteFormBo.class));
        });
        return result;
    }

    public RemoteFormBo getCustomerForm(Long formId) {
        List<RemoteFormBo> formList = listCustomerForms(Arrays.asList(formId));
        if (CollectionUtils.isEmpty(formList)) {
            return null;
        }
        return formList.get(0);
    }

    public List<RemoteFormFieldBo> getCustomerFormFields(List<Long> fieldIds) {
        if (CollectionUtils.isEmpty(fieldIds)) {
            return Collections.EMPTY_LIST;
        }

        List<RemoteFormFieldBo> result = new ArrayList<>();
        List<List<Long>> subIdsArr = Lists.partition(fieldIds, CommonConstant.BATCH_QUERY_SIZE);
        subIdsArr.forEach(subIds -> {
            List<BaseCustomFormField> fields = customFormService.queryCustomFieldByFieldIds(subIds);
            result.addAll(JsonUtil.copyList(fields, RemoteFormFieldBo.class));
        });
        return result;
    }

    /**
     * 获取字段名称map
     *
     * @param fieldIds 字段id的集合
     * @return 字段名称map
     */
    public Map<Long, String> getCustomerFieldNameMap(List<Long> fieldIds) {
        if (CollectionUtils.isEmpty(fieldIds)) {
            return MapUtils.EMPTY_MAP;
        }

        List<RemoteFormFieldBo> fields = getCustomerFormFields(fieldIds);
        return fields.stream().collect(Collectors.toMap(RemoteFormFieldBo::getId, RemoteFormFieldBo::getFieldName, (k1, k2) -> k2));
    }


    /**
     * 获取字段名称map
     *
     * @param fieldIds 字段id的集合
     * @return 字段名称map
     */
    public Map<Long, RemoteFormFieldBo> getCustomerFieldMap(List<Long> fieldIds) {
        if (CollectionUtils.isEmpty(fieldIds)) {
            return MapUtils.EMPTY_MAP;
        }

        List<RemoteFormFieldBo> fields = getCustomerFormFields(fieldIds);
        return fields.stream().collect(Collectors.toMap(RemoteFormFieldBo::getId, v->v, (k1, k2) -> k2));
    }
}
