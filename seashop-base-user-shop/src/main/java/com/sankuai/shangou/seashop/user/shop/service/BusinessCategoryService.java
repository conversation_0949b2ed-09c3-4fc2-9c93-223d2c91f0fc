package com.sankuai.shangou.seashop.user.shop.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.eimport.ImportResult;
import com.sankuai.shangou.seashop.user.common.remote.product.model.CategoryTreeNodeBo;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategory;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdBusinessCategoryReqList;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdImportCategoryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryRespList;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryLastCategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ValidBusinessCategoryIdsResp;

public interface BusinessCategoryService {
    /**
     * 根据条件分页查询类目申请信息
     *
     * @param queryBusinessCategoryPageReq 查询条件
     * @return 申请信息
     */
    BasePageResp<BusinessCategoryResp> queryPage(QueryBusinessCategoryPageReq queryBusinessCategoryPageReq);

    void setCategoryName(List<BusinessCategoryResp> basePageResp);

    /**
     * 更新类目申请信息
     *
     * @param reqList 更新参数
     * @return 更新结果
     */
    BaseResp updateBusinessCategory(CmdBusinessCategoryReqList reqList);

    /**
     * 根据店铺id查询类目信息
     *
     * @param id 店铺id
     * @return 类目信息
     */
    List<BusinessCategoryResp> queryListByShopId(Long id);

    /**
     * 根据店铺id查询类目信息Id
     *
     * @param id 店铺id
     * @return 类目信息
     */
    List<Long> queryIdListByShopId(Long id);

    List<Long> queryIdListByShopIdLimit(Long id);

    /**
     * 根据店铺id查询最大保证金信息
     *
     * @param collect 店铺id
     * @return 类目信息
     */
    List<BusinessCategory> queryShopMaxBondList(List<Long> collect);

    /**
     * 根据店铺id查询最大保证金信息
     *
     * @param shopId 店铺id
     * @return 类目信息
     */
    BusinessCategory queryShopMaxBond(Long shopId);

    /**
     * 删除经营分类
     *
     * @param id 经营分类id
     */
    void deleteBusinessCategory(BaseIdReq id);

    /**
     * 查询经营分类树
     *
     * @param shopId 店铺id
     * @return 经营分类树
     */
    List<CategoryTreeNodeBo> queryBusinessCategoryTree(Long shopId);

    /**
     * 查询经营分类树（支持按名称搜索）
     *
     * @param shopId 店铺id
     * @param categoryName 分类名称（支持模糊搜索）
     * @return 经营分类树
     */
    List<CategoryTreeNodeBo> queryBusinessCategoryTree(Long shopId, String categoryName);


    /**
     *
     * @param shopId
     * @param categoryId
     * @return
     */
    BusinessCategory queryOne(Long shopId, Long categoryId);

    /**
     * 根据店铺id和类目id查询类目信息
     *
     * @param shopId 店铺id
     * @param thirdCategoryIds 类目id
     * @return 类目信息
     */
    List<BusinessCategory> queryListByShopIdAndCategoryIds(Long shopId, List<Long> thirdCategoryIds);

    List<BusinessCategory> queryListByCategoryIds(List<Long> thirdCategoryIds);

    /**
     * 批量更新类目信息
     *
     * @param businessCategoryList 类目信息
     */
    void updateBatchById(List<BusinessCategory> businessCategoryList);

    /**
     * 根据店铺id查询类目信息
     *
     * @param req 店铺id
     * @return 类目信息
     */
    BusinessCategoryRespList queryList(BaseBatchIdReq req);

    /**
     * 查询商家有效的类目id的集合
     *
     * @param shopId 店铺id
     * @return 类目id集合
     */
    ValidBusinessCategoryIdsResp queryValidBusinessCategoryIds(Long shopId);

/**
     * 查询类目自定义表单
     *
     * @param id 类目id
     * @return 类目自定义表单
     */
    QueryLastCategoryResp queryById(BaseBatchIdReq id);

    /**
     * 导入类目信息
     *
     * @param req 文件路径
     * @return 导入结果
     */
    ImportResult importBusinessCategory(CmdImportCategoryReq req);

    /**
     * 根据类目id的集合删除经营类目
     *
     * @param categoryIds 类目id集合
     */
    void removeByCategoryIds(List<Long> categoryIds);

    List<Long> queryShopIdListByCategoryAndShop(Long shopId, List<Long> categoryIds);

    List<Long> queryShopIdListByCategory(List<Long> categoryIds);
}
