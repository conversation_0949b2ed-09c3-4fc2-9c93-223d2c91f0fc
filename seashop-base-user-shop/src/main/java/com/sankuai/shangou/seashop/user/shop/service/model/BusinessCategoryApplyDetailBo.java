package com.sankuai.shangou.seashop.user.shop.service.model;

import java.util.Date;
import java.util.List;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/30 10:53
 */
@Getter
@Setter
@Builder
public class BusinessCategoryApplyDetailBo {

    /**
     * 申请id
     */
    private Long id;

    /**
     * 申请时间
     */
    private Date applyDate;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 审核状态 0-待审核 1-已审核 2-审核拒绝
     */
    private Integer auditedStatus;

    /**
     * 审核状态描述
     */
    private String auditedStatusDesc;

    /**
     * 审核时间
     */
    private Date auditedDate;

    /**
     * 拒绝原因
     */
    private String refuseReason;

    /**
     * 签署状态 0-待签署 1-已签署
     */
    private Integer agreementStatus;

    /**
     * 签署状态描述
     */
    private String agreementStatusDesc;

    /**
     * 合同ID
     */
    private Long shopAgreementId;

    /**
     * 是否需要补充资料
     */
    private Boolean needSupply;

    /**
     * 申请类目
     */
    private List<BusinessCategoryBo> applyCategoryList;

    /**
     * 字段集合
     */
    private List<BusinessCategoryFormBo> formList;

    /**
     * 表单变动集合
     */
    private List<BusinessCategoryFormBo> changeFormList;

}
