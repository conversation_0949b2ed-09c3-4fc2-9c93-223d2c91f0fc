package com.sankuai.shangou.seashop.user.shop.service.assist;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.user.common.remote.base.model.RemoteFormFieldBo;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryForm;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormFieldBo;
import com.sankuai.shangou.seashop.user.shop.service.model.CategoryBindFormBo;

import cn.hutool.core.collection.CollUtil;

/**
 * <AUTHOR>
 * @date 2023/11/30 13:34
 */
@Component
public class FormAssistant {

    @Resource
    private FormFieldAssistant formFieldAssistant;

    /**
     * 将数据库中的表单数据转换成bo
     * 主要是解析filedJson 并且补充字段名称
     * 单表单内字段名称不允许重复, 所以可以用名称确定唯一
     *
     * @param businessCategoryForms 数据库中的表单数据
     * @return bo
     */
    public List<BusinessCategoryFormBo> transBusinessCategoryFormToBo(List<BusinessCategoryForm> businessCategoryForms) {
        if (CollUtil.isEmpty(businessCategoryForms)) {
            return Collections.emptyList();
        }

        // 存放所有的字段信息
        List<BusinessCategoryFormFieldBo> allFieldList = new ArrayList<>();
        List<BusinessCategoryFormBo> boList = businessCategoryForms.stream().map(businessCategoryForm -> {
            BusinessCategoryFormBo businessCategoryFormBo = new BusinessCategoryFormBo();
            businessCategoryFormBo.setId(businessCategoryForm.getId());
            businessCategoryFormBo.setShopId(businessCategoryForm.getShopId());
            businessCategoryFormBo.setCategoryId(businessCategoryForm.getCategoryId());
            businessCategoryFormBo.setFormId(businessCategoryForm.getFormId());
            businessCategoryFormBo.setFieldList(formFieldAssistant.transToFieldList(businessCategoryForm.getFormData()));
            allFieldList.addAll(businessCategoryFormBo.getFieldList());
            return businessCategoryFormBo;
        }).collect(Collectors.toList());

        // 填充字段名称
        formFieldAssistant.fillFieldName(allFieldList);
        return boList;
    }


    /**
     * 将数据库中的表单数据转换成bo
     * 主要是解析filedJson 并且补充字段名称
     * 单表单内字段名称不允许重复, 所以可以用名称确定唯一
     *
     * @param businessCategoryForms 数据库中的表单数据
     * @return bo
     */
    public List<BusinessCategoryFormBo> transBusinessCategoryFormToBo(List<BusinessCategoryForm> businessCategoryForms, Map<Long, CategoryBindFormBo> categoryBindFormBoMap) {
        if (CollUtil.isEmpty(businessCategoryForms)) {
            return Collections.emptyList();
        }
        return businessCategoryForms.stream().map(businessCategoryForm -> {
            BusinessCategoryFormBo businessCategoryFormBo = new BusinessCategoryFormBo();
            businessCategoryFormBo.setId(businessCategoryForm.getId());
            businessCategoryFormBo.setShopId(businessCategoryForm.getShopId());
            businessCategoryFormBo.setCategoryId(businessCategoryForm.getCategoryId());
            businessCategoryFormBo.setFormId(businessCategoryForm.getFormId());
            businessCategoryFormBo.setFieldList(formFieldAssistant.transToFieldList(businessCategoryForm.getFormData()));
            CategoryBindFormBo newCate = categoryBindFormBoMap.get(businessCategoryForm.getCategoryId());
            if (CollUtil.isNotEmpty(businessCategoryFormBo.getFieldList())){
                if (newCate==null||newCate.getForm() == null) {
                    return null;
                }
                List<RemoteFormFieldBo> remoteFormFieldBos = newCate.getForm().getFormFields();
                businessCategoryFormBo.getFieldList().forEach(v->{
//                    获取同一个字段的数据
                    Optional<RemoteFormFieldBo> remoteFormFieldBo = remoteFormFieldBos.stream().filter(o->o.getId().equals(v.getFieldId())).findFirst();
                    remoteFormFieldBo.ifPresent(formFieldBo -> v.setFieldName(formFieldBo.getFieldName()));
                });
            }
            return businessCategoryFormBo;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public List<BusinessCategoryFormBo> transBusinessCategoryFormToBoAll(List<BusinessCategoryForm> businessCategoryForms) {
        if (CollUtil.isEmpty(businessCategoryForms)) {
            return Collections.emptyList();
        }

        // 存放所有的字段信息
        List<BusinessCategoryFormFieldBo> allFieldList = new ArrayList<>();
        List<BusinessCategoryFormBo> boList = businessCategoryForms.stream().map(businessCategoryForm -> {
            BusinessCategoryFormBo businessCategoryFormBo = new BusinessCategoryFormBo();
            businessCategoryFormBo.setId(businessCategoryForm.getId());
            businessCategoryFormBo.setCategoryId(businessCategoryForm.getCategoryId());
            businessCategoryFormBo.setFormId(businessCategoryForm.getFormId());
            businessCategoryFormBo.setFieldList(formFieldAssistant.transToFieldList(businessCategoryForm.getFormData()));
            allFieldList.addAll(businessCategoryFormBo.getFieldList());
            return businessCategoryFormBo;
        }).collect(Collectors.toList());

        // 填充表单名称
        formFieldAssistant.fillFormName(boList);

        // 填充字段名称
        formFieldAssistant.fillFieldAttr(allFieldList);
        return boList;
    }
}
