package com.sankuai.shangou.seashop.user.shop.service.assist;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.NumberUtil;
import com.sankuai.shangou.seashop.base.core.service.RegionService;
import com.sankuai.shangou.seashop.base.thrift.core.request.RegionIdsReq;
import com.sankuai.shangou.seashop.base.thrift.core.response.AllPathRegionResp;
import com.sankuai.shangou.seashop.user.account.service.assist.RegionHelper;
import com.sankuai.shangou.seashop.user.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.user.common.enums.FreightEnum;
import com.sankuai.shangou.seashop.user.dao.shop.domain.*;
import com.sankuai.shangou.seashop.user.dao.shop.repository.*;
import com.sankuai.shangou.seashop.user.shop.service.model.CalculateFreightProductBo;
import com.sankuai.shangou.seashop.user.shop.service.model.CalculateFreightShopBo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FreightCalculateAssistant {

    @Resource
    private ShippingFreeRegionRepository shippingFreeRegionRepository;
    @Resource
    private ShippingFreeGroupRepository shippingFreeGroupRepository;
    @Resource
    private FreightTemplateRepository freightTemplateRepository;
    @Resource
    private FreightAreaContentRepository freightAreaContentRepository;
    @Resource
    private FreightAreaDetailRepository freightAreaDetailRepository;
    @Resource
    private RegionService regionService;
    @Resource
    private RestrictedAreaRepository restrictedAreaRepository;

    /**
     * 计算店铺运费
     * <p>
     *     1. 首先看店铺是否免邮，通过店铺配置的免邮金额来判断
     *     2. 再看店铺是否在包邮区域内，如果在包邮区域内，直接返回0
     *     3. 根据店铺运费规则计算店铺运费
     * </p>
     * <AUTHOR>
     * @param calShop
     * @param dbShop
     * @param freeAreaList
     * java.math.BigDecimal
     */
    public BigDecimal calculateShopFreight(String shippingRegionPath,
                                           CalculateFreightShopBo calShop,
                                           Shop dbShop,
                                           List<ShopFreeShippingArea> freeAreaList) {
        if (dbShop == null) {
            log.warn("【运费计算】店铺信息为空");
            return BigDecimal.ZERO;
        }
        BigDecimal orderAmount = calShop.getTotalAmount();
        // 校验是否在包邮区域内
        if (CollectionUtils.isNotEmpty(freeAreaList)) {
            // 包邮区域配置到 省市，用户选择的收货地址是到 省市区
            boolean regionInFree = freeAreaList.stream()
                .anyMatch(freeArea -> RegionHelper.checkShippingPathMatchTarget(shippingRegionPath, freeArea.getRegionPath()));
            if (regionInFree) {
                log.info("【运费计算】店铺满足包邮区域条件, shopId={}, 发货地址路径={}, 免邮区域={}",
                        dbShop.getId(), shippingRegionPath, JsonUtil.toJsonString(freeAreaList));
                return BigDecimal.ZERO;
            }
        }
        // 根据店铺运费规则计算店铺运费
        long quantitySum = calShop.getProductList().stream()
                .mapToLong(CalculateFreightProductBo::getBuyCount)
                .sum();
        boolean useAmountFreight = dbShop.getAmountFreightCondition() != null &&
                dbShop.getAmountFreightCondition().compareTo(BigDecimal.ZERO) != 0 &&
                orderAmount.compareTo(dbShop.getAmountFreightCondition()) >= 0;
        boolean useQuantityFreight = dbShop.getQuantityFreightCondition() != null &&
                dbShop.getQuantityFreightCondition() != 0 &&
                quantitySum >= dbShop.getQuantityFreightCondition();
        log.info("【运费计算】店铺运费条件, shopId={}, orderAmount={}, quantitySum={}, amountCondition={}, quantityCondition={}, amountFreight={}, quantityFreight={}",
                dbShop.getId(), orderAmount, quantitySum, dbShop.getAmountFreightCondition(), dbShop.getQuantityFreightCondition(), dbShop.getAmountFreight(), dbShop.getQuantityFreight());
        // 金额条件和数量条件都满足，取最小值
        if (useAmountFreight && useQuantityFreight) {
            BigDecimal min = dbShop.getAmountFreight().min(dbShop.getQuantityFreight());
            log.info("【运费计算】店铺满足金额和数量条件, shopId={}, finalFreightAmount={}", dbShop.getId(), min);
            return min;
        }
        if (useAmountFreight) {
            log.info("【运费计算】店铺满足金额条件, shopId={}, finalFreightAmount={}", dbShop.getId(), dbShop.getAmountFreight());
            return dbShop.getAmountFreight();
        }
        if (useQuantityFreight) {
            log.info("【运费计算】店铺满足数量条件, shopId={}, finalFreightAmount={}", dbShop.getId(), dbShop.getQuantityFreight());
            return dbShop.getQuantityFreight();
        }
        // 特殊标记，表示不满足任何条件，直接取商品运费
        return null;
    }

    /**
     * 计算店铺下商品的运费
     * <AUTHOR>
     * @param shippingRegionPath 收货地址的区域路径
     * @param shopBo 店铺及商品列表
     * @param dbShop 店铺信息
     * java.math.BigDecimal
     */
    public BigDecimal calculateShopProductFreight(String shippingRegionPath, CalculateFreightShopBo shopBo, Shop dbShop, boolean ignoreForbiddenArea) {
        // 需要过滤掉模板为空的
        List<CalculateFreightProductBo> productList = shopBo.getProductList().stream()
                .filter(product -> product.getTemplateId() != null)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(productList)) {
            log.info("【运费计算】店铺ID={}的商品列表为空", dbShop.getId());
            return BigDecimal.ZERO;
        }
        // 当前店铺商品涉及的运费模板
        List<Long> templateIdList = productList.stream()
                .map(CalculateFreightProductBo::getTemplateId)
                .distinct()
                .collect(Collectors.toList());
        // 先校验是否禁售区域，并根据标识处理是否过滤掉禁售区域的商品
        this.checkIfAreaForbiddenAndRemoveProductIfNecessary(shippingRegionPath, productList, templateIdList, ignoreForbiddenArea);
        // 如果商品列表为空，说明当前商品都在禁售区域且忽略
        if (CollectionUtils.isEmpty(productList)) {
            log.info("【运费计算】店铺ID={}的商品都在禁售区域且忽略", dbShop.getId());
            return BigDecimal.ZERO;
        }
        // 根据模板计算商品运费
        // 首先判断模板的免邮区域和条件，不满足再计算运费
        List<ShippingFreeRegion> templateFreeRegionList = this.getShippingFreeRegion(templateIdList);
        Map<Long, List<ShippingFreeRegion>> templateFreeRegionMap = templateFreeRegionList
                .stream()
                .collect(Collectors.groupingBy(ShippingFreeRegion::getTemplateId));
        List<ShippingFreeGroup> templateFreeGroupList = shippingFreeGroupRepository.getByTemplateIdList(templateIdList);
        Map<Long, ShippingFreeGroup> freeGroupMap = templateFreeGroupList
                .stream()
                .collect(Collectors.toMap(ShippingFreeGroup::getId, Function.identity()));
        // 根据模板包邮的情况，过滤掉不需要计算运费的商品
        removeFreeProduct(
                shippingRegionPath,
                productList,
                templateFreeRegionMap,
                freeGroupMap
        );
        // 如果商品列表为空，说明当前店铺的商品都满足包邮条件，直接返回0，否则继续计算
        if (CollectionUtils.isEmpty(productList)) {
            log.info("【运费计算】店铺ID={}的商品都满足包邮条件", dbShop.getId());
            return BigDecimal.ZERO;
        }
        // 剩下的商品，根据商品的运费模板计算运费
        return calculateProductFreight(shippingRegionPath, productList, templateIdList);
    }

    /**
     * 移除免邮的商品
     * <p>从待计算的商品列表中，根据运费模板的规则，如果符合免邮的则从商品列表中移除掉，后续基于剩下的商品继续计算运费</p>
     * <AUTHOR>
     * @param shippingRegionPath
     * @param productList
     * @param templateFreeRegionMap
     * @param freeGroupMap
     * void
     */
    private void removeFreeProduct(String shippingRegionPath,
                                   List<CalculateFreightProductBo> productList,
                                   Map<Long, List<ShippingFreeRegion>> templateFreeRegionMap,
                                   Map<Long, ShippingFreeGroup> freeGroupMap) {
        // 先将商品信息按照模板计算汇总
        Map<Long, Map<Long, ProductSummaryByTemplate>> prodSumMap = groupByTemplateAndThenProduct(productList);
        // 以模板为维度，校验模板包邮情况，过滤掉包邮商品
        for (Map.Entry<Long, Map<Long, ProductSummaryByTemplate>> et : prodSumMap.entrySet()) {
            Long templateId = et.getKey();
            Map<Long, ProductSummaryByTemplate> prodSumByTemplateMap = et.getValue();
            // 获取模板的包邮区域配置，可能会有多个包邮区域配置满足，还要计算满足条件下的包邮组配置，所以得遍历
            List<ShippingFreeRegion> freeRegionList = templateFreeRegionMap.get(templateId);
            log.info("【运费计算】shippingRegionPath={}, 模板ID={}的包邮区域配置={}, 包邮组配置={}",
                    shippingRegionPath, templateId, JsonUtil.toJsonString(freeRegionList), JsonUtil.toJsonString(prodSumByTemplateMap));
            // 校验每个模板的包邮区域配置，如果满足条件，将商品从商品列表中移除
            removeProductIfMatchFree(shippingRegionPath, productList, prodSumByTemplateMap, freeRegionList, freeGroupMap);
            log.info("【运费计算】过滤后的商品列表={}", JsonUtil.toJsonString(productList));
        }
    }

    /**
     * 校验每个模板的包邮区域配置，根据标识如果需要将商品从商品列表中移除
     * <p>将商品从商品列表移除的目的是：计算商品运费的逻辑，预览订单页加载数据时，如果有默认的收货地址，也会需要计算运费，
     * 此时如果直接报错，用户看不到数据，这个情况下将禁售区域的商品过滤掉，计算其他商品的运费，不影响用户进入预览订单页和修改地址，提交订单时，会再次校验，此时就可以报错了</p>
     * <AUTHOR>
     * @param shippingRegionPath 收货地址区域路径
     * @param productList 商品列表
     * @param templateIdList 运费模板ID列表
     * @param ignoreForbidden 是否忽略禁售区域商品，该标识需要调用方按需传入
     */
    private void checkIfAreaForbiddenAndRemoveProductIfNecessary(String shippingRegionPath, List<CalculateFreightProductBo> productList,
                                                                 List<Long> templateIdList, boolean ignoreForbidden) {
        // 获取禁售区域，如果在仅售区域，则抛出异常
        Map<Long, List<RestrictedArea>> forbiddenAreaMap = getForbiddenAreaToMap(templateIdList);
        // 遍历商品列表，如果在禁售区域，根据标识判断是否需要移除商品或者抛出异常
        Iterator<CalculateFreightProductBo> it = productList.iterator();
        while (it.hasNext()) {
            CalculateFreightProductBo product = it.next();
            List<RestrictedArea> forbiddenAreaList = forbiddenAreaMap.get(product.getTemplateId());
            if (CollUtil.isEmpty(forbiddenAreaList)) {
                continue;
            }
            RestrictedArea matchForbiddenArea = forbiddenAreaList.stream()
                    .filter(fa -> StrUtil.isNotBlank(fa.getRegionPath()))
                .filter(fa -> RegionHelper.checkShippingPathMatchTarget(shippingRegionPath, fa.getRegionPath()))
                    .findFirst()
                    .orElse(null);
            if (matchForbiddenArea != null) {
                // 必须明确指定为true，才会忽略，默认抛出异常
                if (Boolean.TRUE.equals(ignoreForbidden)) {
                    log.info("【运费计算】商品={}，地址={}，在禁售区域={}，移除商品", product.getProductName(), shippingRegionPath, matchForbiddenArea.getRegionPath());
                    it.remove();
                }
                else {
                    throw new BusinessException(String.format(CommonConstant.MESSAGE_PATTERN_SHIPPING_ADDRESS_FORBIDDEN, product.getProductName()));
                }
            }
        }
    }

    /**
     * 根据运费模板分组汇总，然后根据商品ID汇总商品数量和金额
     * <p>用户提交的数据，是以SKU为维度的，但运费的计算是以运费模板，然后是商品为维度，所以需要汇总商品</p>
     * <AUTHOR>
     * @param productList
     */
    private Map<Long/*templateId*/, Map<Long/*productId*/, ProductSummaryByTemplate>> groupByTemplateAndThenProduct(List<CalculateFreightProductBo> productList) {
        return productList.stream()
                .filter(product -> product.getTemplateId() != null && product.getProductId() != null)
                .collect(Collectors.groupingBy(
                        CalculateFreightProductBo::getTemplateId,
                        Collectors.toMap(
                                CalculateFreightProductBo::getProductId,
                                product -> new ProductSummaryByTemplate(
                                        product.getProductAmount(),
                                        product.getBuyCount()
                                ),
                                (existing, replacement) -> new ProductSummaryByTemplate(
                                        existing.getAmount().add(replacement.getAmount()),
                                        existing.getCount() + replacement.getCount()
                                )
                        )
                ));
    }

    /**
     * 如果匹配运费免邮配置，则从商品列表移除
     * <AUTHOR>
     * @param shippingRegionPath
     * @param productList
     * @param prodSumByTemplateMap
     * @param freeRegionList
     * @param freeGroupMap
     * void
     */
    private void removeProductIfMatchFree(String shippingRegionPath,
                                          List<CalculateFreightProductBo> productList,
                                          Map<Long, ProductSummaryByTemplate> prodSumByTemplateMap,
                                          List<ShippingFreeRegion> freeRegionList,
                                          Map<Long, ShippingFreeGroup> freeGroupMap) {
        if (CollectionUtils.isEmpty(freeRegionList)) {
            return;
        }
        for (ShippingFreeRegion region : freeRegionList) {
            // 如果当前包邮区域与收货地址不匹配，则跳过
            if (!RegionHelper.checkShippingPathMatchTarget(shippingRegionPath, region.getRegionPath())) {
                continue;
            }
            ShippingFreeGroup freeGroup = freeGroupMap.get(region.getGroupId());
            if (freeGroup == null) {
                continue;
            }
            // 继续遍历运费模板下的商品，满足包邮条件的商品从商品列表中移除
            prodSumByTemplateMap.forEach((prodId, prodSum) -> {
                // 如果当前商品的数量满足包邮条件，则从商品列表中移除
                if (FreightEnum.FreeGroupConditionType.BUY_COUNT.getCode().equals(freeGroup.getConditionType())) {
                    if (prodSum.getCount() >= Long.parseLong(freeGroup.getConditionNumber())) {
                        // 从商品列表中移除
                        productList.removeIf(prod -> prod.getProductId().equals(prodId));
                    }
                } else if (FreightEnum.FreeGroupConditionType.AMOUNT.getCode().equals(freeGroup.getConditionType())) {
                    if (prodSum.getAmount().compareTo(new BigDecimal(freeGroup.getConditionNumber())) >= 0) {
                        // 从商品列表中移除
                        productList.removeIf(prod -> prod.getProductId().equals(prodId));
                    }
                } else if (FreightEnum.FreeGroupConditionType.BOTH_COUNT_AND_AMOUNT.getCode().equals(freeGroup.getConditionType())) {
                    // 同时满足的情况下，两个条件是用 $ 分隔的
                    String[] condValueArr = freeGroup.getConditionNumber().split(CommonConstant.SPLITTER_FREIGHT_FREE_GROUP);
                    // 默认值处理防止为空
                    Long condCount = condValueArr.length > 0 ? Long.parseLong(condValueArr[0]) : 0L;
                    BigDecimal condAmount = condValueArr.length > 1 ? new BigDecimal(condValueArr[1]) : BigDecimal.ZERO;
                    if (prodSum.getCount() >= condCount && prodSum.getAmount().compareTo(condAmount) >= 0) {
                        // 从商品列表中移除
                        productList.removeIf(prod -> prod.getProductId().equals(prodId));
                    }
                }
            });
        }
    }

    /**
     * 计算商品运费
     * <AUTHOR>
     * @param productList
	 * @param templateIdList
     * java.math.BigDecimal
     */
    private BigDecimal calculateProductFreight(String shippingRegionPath, List<CalculateFreightProductBo> productList, List<Long> templateIdList) {
        // 运费模板
        List<FreightTemplate> templateList = freightTemplateRepository.listByIds(templateIdList);
        Map<Long, FreightTemplate> templateMap = templateList.stream()
                .collect(Collectors.toMap(FreightTemplate::getId, Function.identity()));
        // 待计算商品
        Map<Long, List<CalculateFreightProductBo>> productMap = productList.stream()
                .collect(Collectors.groupingBy(CalculateFreightProductBo::getTemplateId));
        // 运费模板的区域配置
        List<FreightAreaContent> areaList = freightAreaContentRepository.getByTemplateIdList(templateIdList);
        Map<Long, List<FreightAreaContent>> areaMap = areaList.stream()
                .collect(Collectors.groupingBy(FreightAreaContent::getFreightTemplateId));
        // 运费模板的区域详情
        List<FreightAreaDetail> areaDetailList = freightAreaDetailRepository.getByTemplateIdList(templateIdList);
        Map<Long, List<FreightAreaDetail>> areaDetailMap = areaDetailList.stream()
                .collect(Collectors.groupingBy(FreightAreaDetail::getFreightTemplateId));
        // 虽然理论上运费模板一定可以计算出运费，防止数据问题，设置为null代表所有的商品都无法计算运费
        BigDecimal freight = null;
        for (Map.Entry<Long, List<CalculateFreightProductBo>> et : productMap.entrySet()) {
            FreightTemplate template = templateMap.get(et.getKey());
            log.info("【运费计算】运费模板为{}", JsonUtil.toJsonString(template));
            // 理论上不会有这种数据，以防万一以及后续可能的不设置运费模板的商品情况
            if (template == null) {
                continue;
            }
            if (FreightEnum.TemplateFreeType.FREE.getCode().equals(template.getWhetherFree())) {
                log.info("【运费计算】模板ID={}的运费模板为免邮", template.getId());
                continue;
            }
            // 获取匹配的运费模板地址
            FreightAreaDetail matchDetail = findMatchRegionAreaDetail(shippingRegionPath, areaDetailMap.get(template.getId()));
            log.info("【运费计算】模板ID={}, 匹配的区域配置为{}", template.getId(), JsonUtil.toJsonString(matchDetail));
            FreightAreaContent config = getFreightConfig(matchDetail, areaMap.get(template.getId()));
            log.info("【运费计算】模板ID={}的运费模板的区域配置为{}, 匹配的区域配置为{}",
                    template.getId(), JsonUtil.toJsonString(config), JsonUtil.toJsonString(matchDetail));
            if (config == null) {
                continue;
            }
            // 计算模板下商品的运费
            BigDecimal templateProductFreight = calculateTemplateFreight(template, config, et.getValue());
            log.info("【运费计算】模板ID={}的运费模板的运费为{}", template.getId(), templateProductFreight);
            freight = cn.hutool.core.util.NumberUtil.nullToZero(freight).add(templateProductFreight);
        }
        return freight;
    }

    /**
     * 从模板的区域配置中获取匹配的区域。
     * <p>下单时，用户选择的收货地址，只能选择到省市区，所以先从区级开始判断</p>
     * <AUTHOR>
     * @param shippingRegionPath
	 * @param detailList
     */
    private FreightAreaDetail findMatchRegionAreaDetail(String shippingRegionPath, List<FreightAreaDetail> detailList) {
        if (StrUtil.isBlank(shippingRegionPath)) {
            return null;
        }
        // 配送地址的选择是到区级，所以先从区级开始判断
        String[] regionPathArr = shippingRegionPath.split(",");
        Integer provinceId = regionPathArr.length >= 1 ? (isRegionNull(regionPathArr[0]) ? CommonConstant.DEFAULT_REGION_ID : Integer.parseInt(regionPathArr[0])) : CommonConstant.DEFAULT_REGION_ID;
        Integer cityId = regionPathArr.length >= 2 ? (isRegionNull(regionPathArr[1]) ? CommonConstant.DEFAULT_REGION_ID : Integer.parseInt(regionPathArr[1])) : CommonConstant.DEFAULT_REGION_ID;
        Integer countyId = regionPathArr.length >= 3 ? (isRegionNull(regionPathArr[2]) ? CommonConstant.DEFAULT_REGION_ID : Integer.parseInt(regionPathArr[2])) : CommonConstant.DEFAULT_REGION_ID;
        log.info("【运费计算】配送地址的选择是到区级，所以先从区级开始判断, provinceId={}, cityId={}, countyId={}", provinceId, cityId, countyId);
        if (CollUtil.isEmpty(detailList)) {
            return null;
        }
        // 要比对地址，至少省ID是必须的，所以先过滤出省ID相同的配置，减少遍历次数
        // 然后根据区域完整度，省市区/省市/省 排序，配置了省市区的排前面
        List<FreightAreaDetail> provinceDetailList = detailList.stream()
                .filter(detail -> provinceId.equals(detail.getProvinceId()))
                .sorted(Comparator.comparingInt(FreightCalculateAssistant::getAreaLevel))
                .collect(Collectors.toList());
        log.info("【运费计算】模板的省ID={}的区域配置为{}", provinceId, JsonUtil.toJsonString(provinceDetailList));
        return provinceDetailList.stream()
                .peek(detail -> log.info("【运费计算】模板的区域配置为{}", JsonUtil.toJsonString(detail)))
                // 前面排序后，此时一定是会优先判断第三级区域
                .filter(detail -> {
                    int areaLevel = getAreaLevel(detail);
                    if (PROVINCE_CITY_COUNTY == areaLevel) {
                        return countyId.equals(detail.getCountyId());
                    }
                    if (PROVINCE_CITY == areaLevel) {
                        return cityId.equals(detail.getCityId());
                    }
                    return provinceId.equals(detail.getProvinceId());
                })
                .findFirst()
                .orElse(null);
    }

    private FreightAreaContent getFreightConfig(FreightAreaDetail areaDetail, List<FreightAreaContent> configList) {
        log.info("【运费计算】获取与区域配置匹配的配置, 区域配置={}, 配置列表={}", JsonUtil.toJsonString(areaDetail), JsonUtil.toJsonString(configList));
        if (CollectionUtils.isEmpty(configList)) {
            return null;
        }
        // 如果没有匹配的区域配置，取默认的计算运费
        if (areaDetail == null) {
            return configList.stream()
                    .filter(FreightAreaContent::getWhetherDefault)
                    .findFirst()
                    .orElse(null);
        }
        // 获取与区域配置匹配的配置
        return configList.stream()
                .filter(config -> config.getId().equals(areaDetail.getFreightAreaId()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 按照模板计算商品的运费
     * <AUTHOR>
     * @param template
	 * @param config
	 * @param templateProductList
     * java.math.BigDecimal
     */
    private BigDecimal calculateTemplateFreight(FreightTemplate template,
                                                FreightAreaContent config,
                                                List<CalculateFreightProductBo> templateProductList) {
        // 汇总模板下商品的总的计算单位
        BigDecimal calUnit = sumCalculateUnit(template, templateProductList);
        log.info("【运费计算】模板ID={}的运费模板的计算单位为{}", template.getId(), calUnit);
        // 根据规则计算运费
        return calculateByType(
                calUnit, // 计算单位
                config.getFirstUnit(), // 首件
                config.getFirstUnitMonry(), // 首件运费
                config.getAccumulationUnit(), // 续件
                config.getAccumulationUnitMoney() // 续件运费
        );
    }

    /**
     * 汇总模板下商品的计算单位，根据重量、体积、数量分别计算
     * <AUTHOR>
     * @param template
	 * @param templateProductList
     * java.math.BigDecimal
     */
    private BigDecimal sumCalculateUnit(FreightTemplate template, List<CalculateFreightProductBo> templateProductList) {
        if (FreightEnum.TemplateValuationMethod.WEIGHT.getCode().equals(template.getValuationMethod())) {
            return templateProductList.stream()
                    .map(prod -> NumberUtil.multiply(prod.getWeight(), prod.getBuyCount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        if (FreightEnum.TemplateValuationMethod.VOLUME.getCode().equals(template.getValuationMethod())) {
            return templateProductList.stream()
                    .map(prod -> NumberUtil.multiply(prod.getVolume(), prod.getBuyCount()))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return templateProductList.stream()
                .map(CalculateFreightProductBo::getBuyCount)
                .map(BigDecimal::new)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private BigDecimal calculateByType(BigDecimal typeAmount,
                                       int firstUnit, BigDecimal firstUnitMoney,
                                       int accumulationUnit, BigDecimal accumulationUnitMoney) {
        log.info("【运费计算】计算单位的数值为{}, 首件为{}, 首件运费为{}, 续件为{}, 续件运费为{}",
                typeAmount, firstUnit, firstUnitMoney, accumulationUnit, accumulationUnitMoney);
        // 如果计算单位的数值时0，则直接返回运费0
        if (typeAmount == null || BigDecimal.ZERO.compareTo(typeAmount) == 0) {
            log.info("【运费计算】计算单位的数值时0，直接返回运费0");
            return BigDecimal.ZERO;
        }
        BigDecimal firstUnitDecimal = new BigDecimal(firstUnit);
        // 如果小于首单位配置，则取首单位运费
        if (typeAmount.compareTo(firstUnitDecimal) <= 0) {
            log.info("【运费计算】小于首单位配置，则取首单位运费");
            return firstUnitMoney;
        }
        // 首单位的金额与续的规则可能不一样，所以需要把首单位的单独计算
        BigDecimal overFirstAmount = typeAmount.subtract(firstUnitDecimal);
        if (BigDecimal.ZERO.compareTo(overFirstAmount) == 0) {
            log.info("【运费计算】超过首单位的部分为0，直接返回首单位运费");
            return firstUnitMoney;
        }
        // 超过部分按续单位计算，不足单位的按一单位计算，所以计算的时候直接向上取整
        int overCount = overFirstAmount.divide(new BigDecimal(accumulationUnit), 0, RoundingMode.UP).intValue();
        BigDecimal overAmount = NumberUtil.multiply(accumulationUnitMoney, overCount);
        log.info("【运费计算】超过首单位的部分为{}, 续单位的金额为{}", overFirstAmount, overAmount);
        return firstUnitMoney.add(overAmount);
    }

    /**
     * 运费模板免邮的获取，由于区域全路径不一定有值，所以可能需要重新查询设置
     * @param templateIdList 运费模板ID
     * @return 模板对应的免邮区域
     */
    private List<ShippingFreeRegion> getShippingFreeRegion(List<Long> templateIdList) {
        List<ShippingFreeRegion> templateFreeRegionList = shippingFreeRegionRepository.getByTemplateIdList(templateIdList);
        List<Integer> nonPathRegionId = templateFreeRegionList.stream()
                .filter(r -> StrUtil.isBlank(r.getRegionPath()))
                .map(ShippingFreeRegion::getRegionId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(nonPathRegionId)) {
            return templateFreeRegionList;
        }
        RegionIdsReq regionIdsReq = new RegionIdsReq();
        regionIdsReq.setRegionIds(nonPathRegionId);
        Map<String, AllPathRegionResp> regionRespMap = regionService.getAllPathRegions(regionIdsReq);

        return templateFreeRegionList.stream()
                .peek(r -> {
                    // 有path的不需要处理
                    if (StrUtil.isNotBlank(r.getRegionPath()) || r.getRegionId() == null) {
                        return;
                    }
                    AllPathRegionResp region = regionRespMap.get(r.getRegionId().toString());
                    // 防止配置了的地址被删除了
                    if (region != null) {
                        String path = RegionHelper.assembleRegionPath(region.getProvinceId(), region.getCityId(), region.getCountyId(), region.getTownIds());
                        r.setRegionPath(path);
                    }
                })
                // 如果配置了免邮的区域找不到基础地址，则认为不是免邮区域
                .filter(r -> StrUtil.isNotBlank(r.getRegionPath()))
                .collect(Collectors.toList());
    }

    private Map<Long, List<RestrictedArea>> getForbiddenAreaToMap(List<Long> templateIdList) {
        List<RestrictedArea> list = restrictedAreaRepository.getByTemplateId(templateIdList);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.groupingBy(RestrictedArea::getTemplateId));
    }


    @Getter
    @Setter
    @AllArgsConstructor
    static class ProductSummaryByTemplate {

        private BigDecimal amount;
        private Long count;
    }

    // 内部使用的静态常量，代表省市区的完整度，没放公共常量类
    private static final int PROVINCE_CITY_COUNTY = 1;
    private static final int PROVINCE_CITY = 2;
    private static final int PROVINCE = 3;
    // 4 仅会出现在排序时使用
    private static final int NONE = 4;

    /**
     * 用于排序的辅助方法
     * 根据字段的存在性返回排序键值
     * 如果三个字段都有值，返回1
     * 如果有两个字段有值，返回2
     * 如果只有一个provinceId字段有值，返回3
     * <AUTHOR>
     *  
     */
    public static int getAreaLevel(FreightAreaDetail area) {
        if (isRegionIdNotEmpty(area.getProvinceId()) && isRegionIdNotEmpty(area.getCityId()) && isRegionIdNotEmpty(area.getCountyId())) {
            return PROVINCE_CITY_COUNTY;
        }
        else if (isRegionIdNotEmpty(area.getProvinceId()) && isRegionIdNotEmpty(area.getCityId())) {
            return PROVINCE_CITY;
        }
        else if (isRegionIdNotEmpty(area.getProvinceId())) {
            return PROVINCE;
        }
        // 如果所有字段都为空，返回一个较大的值
        return NONE;
    }

    private static boolean isRegionNull(String regionId) {
        return StrUtil.isBlank(regionId) || "null".equals(regionId);
    }

    private static boolean isRegionIdNotEmpty(Integer regionId) {
        return regionId != null && regionId > 0;
    }

}
