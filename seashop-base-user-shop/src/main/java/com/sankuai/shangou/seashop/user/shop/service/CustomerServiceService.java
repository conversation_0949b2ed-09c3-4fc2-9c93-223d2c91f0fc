package com.sankuai.shangou.seashop.user.shop.service;

import java.util.List;

import org.springframework.stereotype.Service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CustomerServiceCmdReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CustomerServiceQueryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.LoginHiChatReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.CustomerServiceResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.CustomerServiceRespList;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopOpenApiSettingResp;

@Service
public interface CustomerServiceService {

    /**
     * 查询客服列表
     * @param baseIdReq 请求
     * @return 客服列表
     */
    BasePageResp<CustomerServiceResp> queryPage(CustomerServiceQueryPageReq baseIdReq);

    /**
     * 查询客服列表
     * @param baseIdReq 请求
     * @return 客服列表
     */
    List<CustomerServiceRespList> queryListByIds(CustomerServiceQueryPageReq baseIdReq);

    /**
     * 查询客服详情
     * @param baseIdReq 请求
     * @return 客服详情
     */
    CustomerServiceResp queryDetail(BaseIdReq baseIdReq);

    /**
     * 添加客服
     * @param customerServiceCmdReq 请求
     * @return 添加结果
     */
    BaseResp add(CustomerServiceCmdReq customerServiceCmdReq);

    /**
     * 更新客服
     * @param customerServiceCmdReq 请求
     * @return 更新结果
     */
    BaseResp update(CustomerServiceCmdReq customerServiceCmdReq);

    /**
     * 删除客服
     * @param customerServiceCmdReq 请求
     * @return 删除结果
     */
    BaseResp delete(CustomerServiceCmdReq customerServiceCmdReq);

    /**
     * 登录HiChat
     * @param loginHiChatReq 请求
     * @return 登录结果
     */
    String loginHiChat(LoginHiChatReq loginHiChatReq);

    /**
     * 查询店铺开放平台设置
     * @param appKey 请求
     * @return 店铺开放平台设置
     */
    ShopOpenApiSettingResp queryOpenApiSetting(String appKey);

    /**
     * 查询店铺开放平台设置
     * @param req 请求
     * @return 店铺开放平台设置
     */
    ShopOpenApiSettingResp queryOpenApiSettingByShop(BaseIdReq req);

    /**
     * 查询未读消息数量
     * @param managerId 管理员Id
     * @return 未读消息数量
     */
    Long getHiChatUnReadCount(Long managerId);
}
