package com.sankuai.shangou.seashop.user.shop.service;

import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryUserInvisibleShopReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopEsQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.es.ShopEsCombinationResp;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/18/018
 * @description:
 */
public interface ShopEsService {

    /**
     * 店铺ES查询
     *
     * @param request
     * @return
     */
    ShopEsCombinationResp searchShopEs(ShopEsQueryReq request);

    List<Long> getUserInvisibleShop(QueryUserInvisibleShopReq queryReq);
}
