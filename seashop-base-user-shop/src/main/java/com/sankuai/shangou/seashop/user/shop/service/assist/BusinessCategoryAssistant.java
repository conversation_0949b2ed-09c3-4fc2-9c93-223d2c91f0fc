package com.sankuai.shangou.seashop.user.shop.service.assist;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.common.remote.base.model.RemoteFormBo;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryApplyDetail;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryForm;
import com.sankuai.shangou.seashop.user.dao.shop.repository.BusinessCategoryApplyDetailRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.BusinessCategoryFormRepository;
import com.sankuai.shangou.seashop.user.shop.convert.CustomerFormConverter;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormCompareBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormFieldBo;
import com.sankuai.shangou.seashop.user.shop.service.model.CategoryBindFormBo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/11 19:18
 */
@Component
@Slf4j
public class BusinessCategoryAssistant {

    @Resource
    private CategoryAssistant categoryAssistant;
    @Resource
    private FormAssistant formAssistant;
    @Resource
    private BusinessCategoryFormRepository businessCategoryFormRepository;
    @Resource
    private BusinessCategoryApplyDetailRepository businessCategoryApplyDetailRepository;
    @Resource
    private CustomerFormConverter customerFormConverter;

    /**
     * 计算经营类目申请记录下的表单变更情况
     *
     * @param applyIds 申请记录id集合
     * @param shopId   店铺id
     * @return 对比结果 key: 申请记录id value: 对比结果
     */
    public Map<Long, BusinessCategoryFormCompareBo> getFormChangeResult(List<Long> applyIds, Long shopId) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.EMPTY_MAP;
        }

        List<BusinessCategoryApplyDetail> applyDetailList = businessCategoryApplyDetailRepository.listByApplyIds(applyIds);
        Map<Long, List<BusinessCategoryApplyDetail>> applyDetailMap = applyDetailList.stream().collect(Collectors.groupingBy(BusinessCategoryApplyDetail::getApplyId));

        // 获取类目和表单最新绑定关系
        List<Long> applyCategoryIds = applyDetailList.stream().map(BusinessCategoryApplyDetail::getCategoryId).collect(Collectors.toList());
        Map<Long, CategoryBindFormBo> newFormMap = categoryAssistant.getCategoryFormMap(applyCategoryIds);
        List<Long> newFormIds = newFormMap.values().stream().map(CategoryBindFormBo::getFormId).collect(Collectors.toList());

        // 查询历史表单信息
        Map<Long, BusinessCategoryFormBo> dbFormMap = getDbFormMap(newFormIds, shopId);

        // 计算是否需要补充资料
        Map<Long, BusinessCategoryFormCompareBo> result = new HashMap<>();
        applyIds.forEach(applyId -> {
            List<BusinessCategoryApplyDetail> applyDetaiList = applyDetailMap.get(applyId);
            if (CollectionUtils.isEmpty(applyDetaiList)) {
                result.put(applyId, BusinessCategoryFormCompareBo.ofEmpty());
                return;
            }

            // 拿出申请的类目id
            List<Long> curApplyCategoryIds = applyDetaiList.stream().map(BusinessCategoryApplyDetail::getCategoryId).collect(Collectors.toList());
            // 申请的类目 所需要关联的最新的表单信息
            BusinessCategoryFormCompareBo compareBo = getFormChangeByCategoryId(newFormMap, dbFormMap, curApplyCategoryIds);
            result.put(applyId, compareBo);
        });
        return result;
    }

    /**
     * 计算已申请经营类目下的表单变更情况
     *
     * @param categoryIds 申请记录id集合
     * @param shopId   店铺id
     * @return 对比结果 key: 申请记录id value: 对比结果
     */
    public boolean getFormChangeFlag(List<Long> categoryIds, Long shopId ) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }
        // 获取类目和表单最新绑定关系
        Map<Long, CategoryBindFormBo> newFormMap = categoryAssistant.getCategoryFormMap(categoryIds);
        if (MapUtil.isEmpty(newFormMap)){
            return false;
        }
        List<Long> newFormIds = newFormMap.values().stream().map(CategoryBindFormBo::getFormId).collect(Collectors.toList());

        // 查询历史表单信息
        Map<Long, BusinessCategoryFormBo> dbFormMap = getDbFormMap(newFormIds, shopId);

        // 计算是否需要补充资料
        // 申请的类目 所需要关联的最新的表单信息
        BusinessCategoryFormCompareBo compareBo = getFormChangeByCategoryId(newFormMap, dbFormMap, categoryIds);
        log.info("getFormChangeFlag compareBo:{}", compareBo);
        return CollUtil.isNotEmpty(compareBo.getChangeFormList());
    }

    /**
     * 计算申请类目下的表单变动情况
     *
     * @param newFormMap       最新的表单信息
     * @param dbFormMap        数据库中的表单信息
     * @param applyCategoryIds 申请的类目id集合
     * @return 对比结果
     */
    public BusinessCategoryFormCompareBo getFormChangeByCategoryId(Map<Long, CategoryBindFormBo> newFormMap,
                                                                   Map<Long, BusinessCategoryFormBo> dbFormMap,
                                                                   List<Long> applyCategoryIds) {
        if (CollectionUtils.isEmpty(applyCategoryIds)) {
            return BusinessCategoryFormCompareBo.ofEmpty();
        }

        // 申请的类目 所需要关联的最新的表单信息
        List<BusinessCategoryFormBo> curNewFormList = getCategoryNewFormList(newFormMap, applyCategoryIds);
        List<Long> curNewFormIds = curNewFormList.stream().map(BusinessCategoryFormBo::getFormId).collect(Collectors.toList());
        List<BusinessCategoryFormBo> curOldFormList = getCategoryOldFormList(curNewFormIds, dbFormMap);
        List<BusinessCategoryFormBo> changeFormList = getCategoryChangeFormList(curNewFormList, curOldFormList);
        BusinessCategoryFormCompareBo compareBo = new BusinessCategoryFormCompareBo();
        compareBo.setNewFormList(curNewFormList);
        compareBo.setOldFormList(curOldFormList);
        compareBo.setChangeFormList(changeFormList);
        return compareBo;
    }

    /**
     * 获取本次申请的类目下需要补充的表单信息
     *
     * @param applyCategoryIds 申请类目id集合
     * @param shopId           店铺id
     * @return 需要补充的表单信息
     */
    public List<BusinessCategoryFormBo> getNeedSupplyForm(List<Long> applyCategoryIds, Long shopId) {
        if (CollectionUtils.isEmpty(applyCategoryIds)) {
            return Collections.EMPTY_LIST;
        }

        // 计算需要补充的表单
        Map<Long, CategoryBindFormBo> newFormMap = categoryAssistant.getCategoryFormMap(applyCategoryIds);
        List<Long> newFormIds = newFormMap.values().stream().map(CategoryBindFormBo::getFormId).collect(Collectors.toList());
        // 查询历史表单信息
        Map<Long, BusinessCategoryFormBo> dbFormMap = getDbFormMap(newFormIds, shopId);
        BusinessCategoryFormCompareBo compareBo = getFormChangeByCategoryId(newFormMap, dbFormMap, applyCategoryIds);
        return compareBo.getChangeFormList();
    }

    /**
     * 查询是否需要补充表单
     *
     * @param categoryIds 类目id集合
     * @param shopId      店铺id
     * @return 是否需要补充表单
     */
    public Boolean isNeedSupplyForm(List<Long> categoryIds, Long shopId) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return false;
        }

        return CollectionUtils.isNotEmpty(getNeedSupplyForm(categoryIds, shopId));
    }

    /**
     * 获取数据库中的历史表单信息
     *
     * @param formIds 表单id集合
     * @param shopId  店铺id
     * @return 表单信息
     */
    public Map<Long, BusinessCategoryFormBo> getDbFormMap(Map<Long, CategoryBindFormBo> categoryBindFormBoMap, Long shopId) {
        if (CollUtil.isEmpty(categoryBindFormBoMap)) {
            return Collections.EMPTY_MAP;
        }
//        获取所有的formID
        List<Long> formIds = categoryBindFormBoMap.values().stream().map(CategoryBindFormBo::getFormId).collect(Collectors.toList());
        List<BusinessCategoryForm> dbFormList = businessCategoryFormRepository.listByShopIdAndFormIds(shopId, formIds);
        List<BusinessCategoryFormBo> oldFormList = formAssistant.transBusinessCategoryFormToBo(dbFormList, categoryBindFormBoMap);
        Map<Long, BusinessCategoryFormBo> dbFormMap = oldFormList.stream().collect(Collectors.toMap(BusinessCategoryFormBo::getFormId, Function.identity(), (k1, k2) -> k2));
        return dbFormMap;
    }


    /**
     * 获取数据库中的历史表单信息
     *
     * @param formIds 表单id集合
     * @param shopId  店铺id
     * @return 表单信息
     */
    public Map<Long, BusinessCategoryFormBo> getDbFormMap(List<Long> formIds, Long shopId) {
        if (CollectionUtils.isEmpty(formIds)) {
            return Collections.EMPTY_MAP;
        }
        //formIds去重
        List<Long> tempFormIds = formIds.stream().distinct().collect(Collectors.toList());
        List<BusinessCategoryForm> dbFormList = businessCategoryFormRepository.listByShopIdAndFormIds(shopId, tempFormIds);
        List<BusinessCategoryFormBo> oldFormList = formAssistant.transBusinessCategoryFormToBo(dbFormList);
        Map<Long, BusinessCategoryFormBo> dbFormMap = oldFormList.stream().collect(Collectors.toMap(BusinessCategoryFormBo::getFormId, Function.identity(), (k1, k2) -> k2));
        return dbFormMap;
    }

    /**
     * 获取类目最新绑定的表单集合
     *
     * @param categoryFormMap 类目表单绑定关系map
     * @param categoryIds     类目id集合
     * @return 表单集合
     */
    private List<BusinessCategoryFormBo> getCategoryNewFormList(Map<Long, CategoryBindFormBo> categoryFormMap, List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds) || MapUtils.isEmpty(categoryFormMap)) {
            return Collections.EMPTY_LIST;
        }

        List<BusinessCategoryFormBo> formBoList = new ArrayList<>();
        categoryIds.forEach(categoryId -> {
            CategoryBindFormBo categoryBindFormBo = categoryFormMap.get(categoryId);
            if (categoryBindFormBo == null) {
                return;
            }
            RemoteFormBo form = categoryBindFormBo.getForm();
            if (form == null) {
                return;
            }
            BusinessCategoryFormBo formBo = customerFormConverter.convertToFormBo(form);
            formBo.setCategoryId(categoryBindFormBo.getFirstCategoryId());
            formBo.setSourceCategoryId(categoryId);
            formBoList.add(formBo);
        });

        // 根据id对formList 去重, 然后返回
        Set<Long> formIdSet = new HashSet<>();
        return formBoList.stream().filter(form -> formIdSet.add(form.getFormId())).collect(Collectors.toList());
    }

    /**
     * 从表单map中获取表单id下绑定的历史表单信息
     *
     * @param formIds   表单id的集合
     * @param dbFormMap 数据库中绑定的表单map
     * @return 表单集合
     */
    private List<BusinessCategoryFormBo> getCategoryOldFormList(List<Long> formIds, Map<Long, BusinessCategoryFormBo> dbFormMap) {
        if (CollectionUtils.isEmpty(formIds) || MapUtils.isEmpty(dbFormMap)) {
            return Collections.EMPTY_LIST;
        }

        Set<Long> formIdSet = new HashSet<>();
        return dbFormMap.values().stream()
                .filter(form -> formIds.contains(form.getFormId()))
                .filter(form -> formIdSet.add(form.getFormId()))
                .collect(Collectors.toList());
    }

    /**
     * 获取表单变动结果
     *
     * @param newFormList 新表单
     * @param oldFormList 历史表单
     * @return 表单变动集合
     */
    private List<BusinessCategoryFormBo> getCategoryChangeFormList(List<BusinessCategoryFormBo> newFormList,
                                                                   List<BusinessCategoryFormBo> oldFormList) {
        if (CollectionUtils.isEmpty(newFormList)) {
            return Collections.EMPTY_LIST;
        }

        if (CollectionUtils.isEmpty(oldFormList)) {
            return newFormList;
        }

        newFormList = JsonUtil.copyList(newFormList, BusinessCategoryFormBo.class);
        oldFormList = JsonUtil.copyList(oldFormList, BusinessCategoryFormBo.class);
        // 比较字段变动, formId相同认定为同一表单, 如果字段变动则认定为表单变动
        CompareHelper.CompareResult<BusinessCategoryFormBo> compareFormResult = CompareHelper.compare(newFormList, oldFormList,
                BusinessCategoryFormBo::getFormId, (newForm, oldForm) -> {
                    List<BusinessCategoryFormFieldBo> changeFieldList = compareFormField(newForm, oldForm);
                    newForm.setFieldList(changeFieldList);
                    return CollectionUtils.isEmpty(changeFieldList);
                });

        List<BusinessCategoryFormBo> result = new ArrayList<>();
        result.addAll(compareFormResult.getAddList());
        result.addAll(compareFormResult.getUpdateList());
        return result;
    }

    /**
     * 对比表单下的字段
     * 同一表单下 字段名称相同的字段认定为同一字段
     * 只要旧表单内的字段完全包含新表单内的字段, 则认定为表单没有变动
     *
     * @param newForm 新表单
     * @param oldForm 历史表单
     * @return 字段变动集合
     */
    private List<BusinessCategoryFormFieldBo> compareFormField(BusinessCategoryFormBo newForm, BusinessCategoryFormBo oldForm) {
        CompareHelper.CompareResult<BusinessCategoryFormFieldBo> fieldCompareResult = CompareHelper.compare(newForm.getFieldList(), oldForm.getFieldList(),
                BusinessCategoryFormFieldBo::getFieldName, (newField, oldField) -> Boolean.TRUE);
        return fieldCompareResult.getAddList();
    }

    /**
     * 获取类目需要补充的字段集合
     *
     * @param applyCategoryIds 申请类目id集合
     * @param shopId           店铺id
     * @return 类目需要补充的字段集合
     */
    public Map<Long, List<BusinessCategoryFormFieldBo>> getCategorySupplyFields(List<Long> applyCategoryIds, Long shopId) {
        List<BusinessCategoryFormBo> formList = getNeedSupplyForm(applyCategoryIds, shopId);
        if (CollectionUtils.isEmpty(formList)) {
            return Collections.EMPTY_MAP;
        }
        return formList.stream().collect(Collectors.toMap(BusinessCategoryFormBo::getCategoryId, BusinessCategoryFormBo::getFieldList, (k1, k2) -> k2));
    }

}
