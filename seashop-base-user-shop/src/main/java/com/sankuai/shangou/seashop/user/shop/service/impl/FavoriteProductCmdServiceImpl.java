package com.sankuai.shangou.seashop.user.shop.service.impl;

import java.util.Arrays;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.dao.account.domain.Favorite;
import com.sankuai.shangou.seashop.user.dao.shop.repository.FavoriteProductRepository;
import com.sankuai.shangou.seashop.user.shop.mq.model.ProductFavoriteMessage;
import com.sankuai.shangou.seashop.user.shop.mq.publisher.ProductFavoritePublisher;
import com.sankuai.shangou.seashop.user.shop.service.FavoriteProductCmdService;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AddFavoriteProductReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteFavoriteProductReq;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author： liweisong
 * @create： 2023/11/28 9:53
 */
@Service
@Slf4j
public class FavoriteProductCmdServiceImpl implements FavoriteProductCmdService {

    @Resource
    private FavoriteProductRepository favoriteProductRepository;
    @Resource
    private ProductFavoritePublisher productFavoritePublisher;

    @Override
    public void addFavoriteProduct(AddFavoriteProductReq addFavoriteProductReq) {
        Long productId = Long.parseLong(addFavoriteProductReq.getProductId());

        Favorite param = new Favorite();
        param.setUserId(addFavoriteProductReq.getUserId());
        param.setProductId(productId);
        Page<Favorite> page = favoriteProductRepository.pageFavoriteProduct(param, 1, 1);
        if (page.getTotal() > 0) {
            throw new BusinessException("您已经收藏过该商品了!");
        }
        Favorite favorite = new Favorite();
        favorite.setUserId(addFavoriteProductReq.getUserId());
        favorite.setProductId(productId);
        favorite.setDate(new Date());
        favorite.setCreateTime(new Date());
        favorite.setUpdateTime(new Date());
        favoriteProductRepository.insert(favorite);

        try {
            ProductFavoriteMessage event = buildEvent(Arrays.asList(productId), addFavoriteProductReq.getUserId(), 0);
            productFavoritePublisher.sendFavoriteProductEvent(event);
        } catch (Exception e) {
            log.error("[商品收藏事件] 发送商品收藏事件失败, body: {}", JsonUtil.toJsonString(addFavoriteProductReq), e);
        }
    }

    private ProductFavoriteMessage buildEvent(List<Long> productIds, Long userId, Integer type) {
        ProductFavoriteMessage event = new ProductFavoriteMessage();
        event.setProductIds(productIds);
        event.setUserId(userId);
        event.setType(type);
        event.setCreateTime(new Date());
        return event;
    }

    @Override
    public void deleteFavoriteProduct(DeleteFavoriteProductReq deleteFavoriteProductReq) {
        Set<Long> productIds = new HashSet<>();

        if(!CollectionUtils.isEmpty(deleteFavoriteProductReq.getIds())){
            List<Favorite> favorites = favoriteProductRepository.listByIds(deleteFavoriteProductReq.getIds());
            if (CollUtil.isNotEmpty(favorites)) {
                productIds.addAll(favorites.stream().map(Favorite::getProductId).collect(Collectors.toList()));
                favoriteProductRepository.deleteBatchIds(deleteFavoriteProductReq.getIds());
            }
        }
        if(!StringUtils.isEmpty(deleteFavoriteProductReq.getProductId())){
            favoriteProductRepository.deleteByProductIdAndUserId(deleteFavoriteProductReq.getProductId(), deleteFavoriteProductReq.getUserId());
            productIds.add(Long.parseLong(deleteFavoriteProductReq.getProductId()));
        }
        if (!CollectionUtils.isEmpty(deleteFavoriteProductReq.getProductIdList())) {
            favoriteProductRepository.delete(deleteFavoriteProductReq.getProductIdList());
            productIds.addAll(deleteFavoriteProductReq.getProductIdList().stream().map(Long::parseLong).collect(Collectors.toList()));
        }

        try {
            List<Long> productIdList = productIds.stream().distinct().collect(Collectors.toList());
            ProductFavoriteMessage event = buildEvent(productIdList, deleteFavoriteProductReq.getUserId(), 1);
            productFavoritePublisher.sendFavoriteProductEvent(event);
        } catch (Exception e) {
            log.error("[商品收藏事件] 发送商品收藏事件失败, body: {}", JsonUtil.toJsonString(deleteFavoriteProductReq), e);
        }
    }
}
