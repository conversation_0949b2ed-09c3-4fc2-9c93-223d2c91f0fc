package com.sankuai.shangou.seashop.user.shop.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFavoriteProductReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFavoriteProductResp;

/**
 * @author： liweisong
 * @create： 2023/11/28 9:50
 */
public interface FavoriteProductQueryService {

    BasePageResp<QueryFavoriteProductResp> pageFavoriteProduct(QueryFavoriteProductReq queryFavoriteProductReq);

    Boolean queryFavoriteProductStatus(Long productId, Long userId);

    Integer queryFavoriteProductCount(Long productId);
}
