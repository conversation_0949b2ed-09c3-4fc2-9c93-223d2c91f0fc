package com.sankuai.shangou.seashop.user.shop.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.user.common.enums.SmsEnum;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.thrift.shop.request.*;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ProductShopInfoResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShippingSettingsResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopEsResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopIdsResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopIntroductionResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopLogoResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopRespList;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopUserCountResp;

/**
 * @description: 供应商服务类
 * @author: LXH
 **/
public interface ShopService {

    /**
     * 获取店铺ID列表
     *
     * @param queryShopReq 查询参数
     * @return 店铺ID列表
     */
    ShopIdsResp getShopIds(QueryShopReq queryShopReq);

    /**
     * 供应商入驻申请
     *
     * @param cmdAgreementReq 供应商入驻申请参数
     * @return 供应商入驻申请
     */
    String residencyApplication(CmdAgreementReq cmdAgreementReq);

    /**
     * 创建空的供应商
     *
     * @param member 商家信息
     * @return 供应商入驻申请
     */
    Shop createEmptyShop(Member member);

    /**
     * 供应商入驻申请第一步(基本信息)
     *
     * @param cmdShopStepsOneReq 供应商入驻申请参数第一步
     * @return 供应商入驻申请
     */
    Long editBaseInfo(CmdShopStepsOneReq cmdShopStepsOneReq);

    /**
     * 供应商入驻申请第二步(银行信息)
     *
     * @param cmdShopStepsTwoReq 供应商入驻申请参数第二步
     * @return 供应商入驻申请
     */
    Long editBankInfo(CmdShopStepsTwoReq cmdShopStepsTwoReq);

    /**
     * 供应商入驻申请第三步(类目信息)
     *
     * @param cmdShopStepsThreeReq 供应商入驻申请参数第三步
     * @return 供应商入驻申请
     */
    Long editCategoryInfo(CmdShopStepsThreeReq cmdShopStepsThreeReq);

    /**
     * 运费设置保存
     *
     * @param request
     */
    void saveShippingSettings(ShippingSettingsSaveReq request);

    /**
     * 获取运费设置
     *
     * @param shopId
     * @return
     */
    ShippingSettingsResp getShippingSettings(Long shopId);

    /**
     * 分页查询店铺信息
     *
     * @param request 请求体
     * @return 查询结果
     */
    BasePageResp<ShopResp> queryPage(ShopQueryPagerReq request);

    //查询店铺类目详情
    String queryShopCategoryDetail(BaseIdReq shopId);

    /**
     * 查询店铺详情
     *
     * @param shopId 店铺ID
     * @return 店铺详情
     */
    ShopDetailResp queryDetail(BaseIdReq shopId);

    /**
     * 审核店铺
     *
     * @param shopId 店铺ID
     * @return 审核结果
     */
    BaseResp auditing(CmdShopStatusReq shopId);

    /**
     * 编辑个人供应商信息
     *
     * @param cmdShopReq 请求体
     * @return 编辑结果
     */
    Long editShopPersonal(CmdShopReq cmdShopReq);

    /**
     * 编辑店铺供应商信息
     *
     * @param cmdShopReq 请求体
     * @return 编辑结果
     */
    Long editShopEnterprise(CmdShopReq cmdShopReq);

    /**
     * 冻结店铺
     *
     * @param cmdShopStatusReq 请求体
     * @return 冻结结果
     */
    Long freezeShop(CmdShopStatusReq cmdShopStatusReq);

    String createQR(CmdCreateQRReq cmdCreateQRReq, String logo);


    /**
     * 提醒保证金
     *
     * @param baseIdReq 请求体
     * @return 发送结果
     */
    BaseResp sendDepositRemind(BaseIdReq baseIdReq);

    BaseResp batchSendSms(List<Long> longs, SmsEnum.Template template);

    /**
     * 根据店铺ID获取店铺信息
     *
     * @param queryShopPageReq 店铺ID
     * @return 店铺信息
     */
    ShopRespList getShopList(QueryShopPageReq queryShopPageReq);

    /**
     * 根据店铺ID获取店铺信息
     *
     * @param request 店铺ID
     * @return 店铺信息
     */
    ShopSimpleListResp querySimpleList(ShopSimpleQueryReq request);

    /**
     * 根据店铺ID获取店铺信息
     *
     * @param baseIdReq 店铺ID
     * @return 店铺信息
     */
    ProductShopInfoResp queryProductShop(ProductShopQueryReq baseIdReq);

    void updateSettleAccount(Shop shop);

    /**
     * 根据店铺ID集合查询店铺基本信息集合（最多一次性查200个）
     *
     * @param request
     * @return
     */
    List<ShopResp> queryShopsByIds(ShopQueryReq request);

    String getCellPhone(Long id);

    /**
     * 统计平台用户
     *
     * @return 统计结果
     */
    ShopUserCountResp countShopUser();

    /**
     * 发送验证码
     *
     * @return 统计结果
     */
    BaseResp sendCode(CmdSendCodeReq cmdSendCodeReq);

    /**
     * 审核店铺
     *
     * @param shopStatusReq 店铺ID
     * @return 审核结果
     */
    BaseResp adaAuditing(CmdShopStatusReq shopStatusReq);

    /**
     * 通过ES查询店铺信息（主要是商城店铺查询使用）
     *
     * @param request
     * @return
     */
    BasePageResp<ShopEsResp> queryByShopEs(ShopEsQueryReq request);

    /**
     * 获取店铺昨日UV
     *
     * @param shopId 店铺ID
     * @return 店铺昨日UV
     */
    Long getYesterdayShopUV(Long shopId);

    /**
     * 通过名称或者id查询已签约店铺列表（id、名称、需要缴纳的最大保证金）
     *
     * @param queryPagerReq 请求体
     * @return 查询结果
     */
    BasePageResp<ShopSimpleResp> querySimplePage(ShopQueryPagerReq queryPagerReq);

    /**
     * 根据店铺ID获取店铺信息
     *
     * @param req 店铺ID
     * @return 店铺信息
     */
    BaseResp setShopType(ShopTypeCmdReq req);

    /**
     * 根据店铺ID创建店铺二维码
     *
     * @param cmdCreateQRReq 店铺ID
     * @return 店铺信息
     */
    String createQR(CmdCreateQRReq cmdCreateQRReq);

    /**
     * 根据店铺ID获取店铺信息
     *
     * @param baseIdReq 店铺ID
     * @return 店铺信息
     */
    ShopIntroductionResp shopIntroduction(BaseIdReq baseIdReq);

    /**
     * 查询自营店铺介绍
     *
     * @return 自营店铺介绍
     */
    ShopSimpleResp selfShopInfo();

    /**
     * 编辑店铺供应商信息
     *
     * @param r 请求体
     * @return 编辑结果
     */
    Long modifyBankInfo(CmdShopStepsTwoReq r);

    /**
     * 编辑店铺供应商信息
     *
     * @param r 请求体
     * @return 编辑结果
     */
    Long modifyBaseInfo(CmdShopStepsOneReq r);

    /**
     * 编辑店铺供应商信息
     *
     * @param r 请求体
     * @return 编辑结果
     */
    Long modifyManagerInfo(CmdShopManagerReq r);

    /**
     * 编辑店铺供应商信息
     *
     * @param baseIdReq 请求体
     * @return 编辑结果
     */
    ShopSimpleResp shopInfoByUserId(BaseIdReq baseIdReq);

    //    批量更新序号
    BaseResp updateSeq(CmdSingleShopSeqReq cmdShopSeqReq);

    /**
     * 查询店铺详情(包括冻结的店铺)
     *
     * @param shopId 店铺ID
     * @return 店铺详情
     */
    ShopDetailResp queryDetailIncludeFreeze(BaseIdReq shopId);

    /**
     * 查询店铺和经营类目ID查询保证金是否足够
     * @param enoughCashFlagReq
     * @return
     */
    Boolean enoughCashFlag(EnoughCashFlagReq enoughCashFlagReq);


    void initFrom();

    /**
     * 查询店铺详情
     * @param shopId shopId
     * @return 店铺详情
     */
    ShopResp queryShop(BaseIdReq shopId);

    /**
     * 注册电子面单平台
     * @param shopId
     * @return
     */
    Boolean setRegisterState(Long shopId);

    /**
     *获取电子面单平台地址
     */
    String goExpressBills(Long shopId);

    void saveShopLogo(SaveShopLogoReq req);

    ShopLogoResp getShopLogo(Long shopId);

    Long getSelfShopId();

    BaseResp bindMemberPhone(CmdBindMemberPhoneReq req);

    ShopResp querySelfShopInfo();
}
