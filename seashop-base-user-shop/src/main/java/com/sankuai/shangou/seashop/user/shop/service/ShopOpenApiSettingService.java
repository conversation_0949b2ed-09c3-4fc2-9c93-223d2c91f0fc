package com.sankuai.shangou.seashop.user.shop.service;

import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopOpenApiSetting;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopOpenApiSettingResp;

import java.util.List;

/**
 * @description: 供应商hi客服配置服务类
 * @author: LXH
 **/
public interface ShopOpenApiSettingService {
    String makeAppKey(Long shopId);

    String makeAppSecret();

    ShopOpenApiSetting getShopOpenApiSettingByShopId(Long shopId);

    ShopOpenApiSetting create(Shop shop);

    ShopOpenApiSettingResp queryOpenApiSetting(String appKey);

    ShopOpenApiSettingResp queryOpenApiSettingByShop(Long id);

    List<ShopOpenApiSettingResp> queryOpenApiSettingByShopIds(List<Long> shopIds);
}
