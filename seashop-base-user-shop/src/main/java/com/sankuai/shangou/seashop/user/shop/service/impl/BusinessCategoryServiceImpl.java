package com.sankuai.shangou.seashop.user.shop.service.impl;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.base.eimport.ImportResult;
import com.sankuai.shangou.seashop.base.eimport.context.ImportContextHolder;
import com.sankuai.shangou.seashop.base.eimport.input.ImportWay;
import com.sankuai.shangou.seashop.base.s3plus.S3plusStorageService;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.thrift.core.enums.ShowStatusEnum;
import com.sankuai.shangou.seashop.user.common.constant.BusinessCategoryConstant;
import com.sankuai.shangou.seashop.user.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.user.common.enums.BusinessCategoryChangeEnum;
import com.sankuai.shangou.seashop.user.common.remote.base.model.RemoteFormBo;
import com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService;
import com.sankuai.shangou.seashop.user.common.remote.product.model.CategoryTreeNodeBo;
import com.sankuai.shangou.seashop.user.common.remote.product.model.RemoteCategoryBo;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategory;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.repository.BusinessCategoryApplyRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.BusinessCategoryRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.sankuai.shangou.seashop.user.shop.excel.read.dto.BusinessCategoryReadReq;
import com.sankuai.shangou.seashop.user.shop.excel.read.dto.CategoryImportContext;
import com.sankuai.shangou.seashop.user.shop.excel.read.enums.BizTypeEnum;
import com.sankuai.shangou.seashop.user.shop.excel.read.listener.CategoryImportListener;
import com.sankuai.shangou.seashop.user.shop.mq.model.BusinessCategoryEvent;
import com.sankuai.shangou.seashop.user.shop.mq.publisher.BusinessCategoryPublisher;
import com.sankuai.shangou.seashop.user.shop.service.BusinessCategoryService;
import com.sankuai.shangou.seashop.user.shop.service.assist.BusinessCategoryAssistant;
import com.sankuai.shangou.seashop.user.shop.service.assist.CategoryAssistant;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormFieldBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryQueryBo;
import com.sankuai.shangou.seashop.user.shop.service.model.CategoryBindFormBo;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdBusinessCategoryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdBusinessCategoryReqList;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdImportCategoryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryRespList;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryLastCategoryResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ValidBusinessCategoryIdsResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormFieldDto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
@SuppressWarnings("all")
public class BusinessCategoryServiceImpl implements BusinessCategoryService {
    @Resource
    private BusinessCategoryRepository businessCategoryRepository;
    @Resource
    private RemoteCategoryService remoteCategoryService;
    @Resource
    private CategoryAssistant categoryAssistant;
    @Resource
    private BusinessCategoryApplyRepository businessCategoryApplyRepository;
    @Resource
    private BusinessCategoryAssistant businessCategoryAssistant;
    @Resource
    private ImportWay importWay;
    @Resource
    private S3plusStorageService service;
    @Value("${s3plus.hostName:http://msstest.sankuai.com}")
    private String hostName;
    @Value("${s3plus.bucketName}")
    private String bucketName;
    @Value("${s3plus.downloadUrlHeader}")
    private String downloadUrlHeader;
    @Resource
    private ShopRepository shopRepository;
    @Resource
    private BaseLogAssist baseLogAssist;
    @Resource
    private BusinessCategoryPublisher businessCategoryPublisher;

    @Override
    public BasePageResp<BusinessCategoryResp> queryPage(QueryBusinessCategoryPageReq queryBusinessCategoryPageReq) {
        BasePageParam pageParam = queryBusinessCategoryPageReq.buildPage();
        BusinessCategoryQueryBo queryBo = JsonUtil.copy(queryBusinessCategoryPageReq, BusinessCategoryQueryBo.class);

        // 将类目名称转换成类目id 去查询
        if (StringUtils.isNotBlank(queryBusinessCategoryPageReq.getCategoryName())) {
            //根据类目名称查询类目id
            List<Long> categoryIds = remoteCategoryService.getCategoryIdByName(queryBusinessCategoryPageReq.getCategoryName());
            if (CollectionUtil.isEmpty(categoryIds)) {
                return PageResultHelper.defaultEmpty(pageParam);
            }
            AssertUtil.throwIfTrue(categoryIds.size() > CommonConstant.BATCH_QUERY_SIZE, "请输入更精准的类目名称");
            queryBo.setCategoryIds(categoryIds);
        }
        if (queryBusinessCategoryPageReq.getCategoryId() != null) {
            queryBo.setCategoryIds(remoteCategoryService.getLastCategoryIds(queryBusinessCategoryPageReq.getCategoryId()));
        }
        List<Long> alreadyRemoveCategoryIds = remoteCategoryService.queryAlreadyRemoveCategoryIds();
        queryBo.setAlreadyRemoveIds(alreadyRemoveCategoryIds);
        QueryWrapper<BusinessCategory> wrapper = commonWrapperBuilder(queryBo);
        Page<BusinessCategory> page = PageHelper.startPage(pageParam);
        List<BusinessCategory> applies = businessCategoryRepository.list(wrapper);
        if (CollectionUtil.isEmpty(applies)) {
            return PageResultHelper.defaultEmpty(pageParam);
        }

        // 补充类目全名
        BasePageResp<BusinessCategoryResp> resp = PageResultHelper.transfer(page, BusinessCategoryResp.class);
        List<BusinessCategoryResp> dataList = resp.getData();
        setCategoryName(dataList);

        // 默认不需要补充个资料
        dataList.forEach(data -> data.setNeedSupply(Boolean.FALSE));
        // 查询类目的申请记录 如果是针对店铺查询的 去计算一下是否需要补充资料
        if (queryBusinessCategoryPageReq.getShopId() != null) {
            List<Long> categoryIds = dataList.stream().map(BusinessCategoryResp::getCategoryId).collect(Collectors.toList());
            List<RemoteCategoryBo> categoryList = remoteCategoryService.queryCategoryList(categoryIds);
            Map<Long, RemoteCategoryBo> categoryMap = categoryList.stream()
                    .collect(Collectors.toMap(RemoteCategoryBo::getId, Function.identity(), (k1, k2) -> k1));

            Map<Long, List<BusinessCategoryFormFieldBo>> fieldMap =
                    businessCategoryAssistant.getCategorySupplyFields(categoryIds, queryBusinessCategoryPageReq.getShopId());
            dataList.forEach(data -> {
                RemoteCategoryBo category = categoryMap.get(data.getCategoryId());
                if (category == null || CollUtil.isEmpty(category.getCategoryIds())) {
                    return;
                }

                data.setNeedSupply(CollectionUtils.isNotEmpty(fieldMap.get(category.getCategoryIds().get(0))));
            });
        }
        return resp;
    }

    @Override
    public void setCategoryName(List<BusinessCategoryResp> basePageResp) {
        //判空
        if (CollectionUtil.isEmpty(basePageResp)) {
            return;
        }
        //获取记录中的类目列表
        //获取申请记录中的类目Id列表
        List<Long> categoryIds = basePageResp.stream().map(BusinessCategoryResp::getCategoryId).collect(Collectors.toList());
        //根据类目Id列表查询出类目全名
        List<RemoteCategoryBo> remoteCategoryBoList = remoteCategoryService.queryCategoryList(categoryIds);
        //判空
        if (CollectionUtil.isNotEmpty(remoteCategoryBoList)) {
            //remoteCategoryBoList转换为map
            Map<Long, RemoteCategoryBo> remoteCategoryBoMap =
                    remoteCategoryBoList.stream().collect(Collectors.toMap(RemoteCategoryBo::getId, Function.identity(), (k1, k2) -> k1));
            //遍历申请记录
            basePageResp.forEach(v -> {
                //获取类目全名
                RemoteCategoryBo remoteCategoryBo = remoteCategoryBoMap.get(v.getCategoryId());
                //判空
                if (remoteCategoryBo != null) {
                    //设置类目名
                    v.setCategoryName(remoteCategoryBo.getName());
                    //设置类目全名
                    String fullCategoryName = StrUtil.replace(remoteCategoryBo.getFullCategoryName(), StrUtil.COMMA, BusinessCategoryConstant.CATEGORY_NAME_SPACER);
                    v.setFullCategoryName(fullCategoryName);
                }
            });
            //删除没有名称的类目
            basePageResp.removeIf(v -> StrUtil.isBlank(v.getCategoryName()));
        }
    }

    @Override
    public BaseResp updateBusinessCategory(CmdBusinessCategoryReqList reqList) {
        List<CmdBusinessCategoryReq> reqListReqList = reqList.getReqList();
        //判空
        if (CollectionUtil.isEmpty(reqListReqList)) {
            return null;
        }
        //获取更新Id列表
        List<Long> businessCategoryIds = reqListReqList.stream().map(CmdBusinessCategoryReq::getId).collect(java.util.stream.Collectors.toList());
        //根据Id列表查询出申请记录
        List<BusinessCategory> applies = MybatisUtil.queryBatch(ids ->
                businessCategoryRepository.lambdaQuery().in(BusinessCategory::getId, ids).list(), businessCategoryIds);
        log.info("updateBusinessCategory applies:{}", applies);
        //获取老的数据
        List<CmdBusinessCategoryReq> olds = applies.stream().map(e -> JsonUtil.copy(e, CmdBusinessCategoryReq.class)).collect(Collectors.toList());
        //获取店铺id
        Long shopId = applies.get(0).getShopId();
        //获取店铺类目map
        CmdBusinessCategoryReqList oldReqList = new CmdBusinessCategoryReqList(olds, shopId);
        reqList.setShopId(shopId);
        //判空
        if (CollectionUtil.isEmpty(applies)) {
            return BaseResp.of();
        }

        //reqListReqList转换为map
        Map<Long, CmdBusinessCategoryReq> reqMap = reqListReqList.stream().collect(Collectors.toMap(CmdBusinessCategoryReq::getId, Function.identity()));
        //需更新的集合
        List<BusinessCategory> updateList = new ArrayList<>();
        //遍历申请记录
        applies.forEach(v -> {
            //获取更新参数
            CmdBusinessCategoryReq cmdBusinessCategoryReq = reqMap.get(v.getId());
            //判空
            if (cmdBusinessCategoryReq != null) {
                //判断分佣比例和保证金是否有变化
                if (v.getCommissionRate().compareTo(cmdBusinessCategoryReq.getCommissionRate()) != 0 || v.getBond().compareTo(cmdBusinessCategoryReq.getBond()) != 0) {
                    //有变化则更新
                    v.setCommissionRate(cmdBusinessCategoryReq.getCommissionRate());
                    v.setBond(cmdBusinessCategoryReq.getBond());
                    updateList.add(v);
                }
            }
        });

        if (CollectionUtil.isEmpty(updateList)) {
            return BaseResp.of();
        }
        log.info("updateList:{}", updateList);
        TransactionHelper.doInTransaction(() -> {
            businessCategoryRepository.updateBatchById(updateList);
        });

        // 记录操作日志
        baseLogAssist.recordLog(ExaminModelEnum.USER, ExaProEnum.MODIFY,
                "批量修改店铺类目信息",
                reqList.getOperationUserId(),
                reqList.getOperationShopId(),
                oldReqList, reqList);

        // 发送经营类目变更事件
        List<Long> categoryIds = updateList.stream().map(BusinessCategory::getCategoryId).collect(Collectors.toList());
        BusinessCategoryEvent event = BusinessCategoryEvent.of(shopId, categoryIds, BusinessCategoryChangeEnum.EDIT.getCode());
        businessCategoryPublisher.sendEvent(event);
        return BaseResp.of();
    }

    @Override
    public List<BusinessCategoryResp> queryListByShopId(Long id) {
        List<BusinessCategory> businessCategories = businessCategoryRepository.lambdaQuery().eq(BusinessCategory::getShopId, id).list();
        List<BusinessCategoryResp> respList = JsonUtil.copyList(businessCategories, BusinessCategoryResp.class);
        setCategoryName(respList);
        return respList;
    }

    @Override
    public List<Long> queryIdListByShopId(Long id) {
        List<BusinessCategory> businessCategories = businessCategoryRepository.lambdaQuery().eq(BusinessCategory::getShopId, id).list();
        List<BusinessCategoryResp> respList = JsonUtil.copyList(businessCategories, BusinessCategoryResp.class);
        return respList.stream().map(BusinessCategoryResp::getCategoryId).collect(Collectors.toList());
    }

    @Override
    public List<Long> queryIdListByShopIdLimit(Long id) {
        List<BusinessCategory> businessCategories = businessCategoryRepository.lambdaQuery().eq(BusinessCategory::getShopId, id)
                .last("limit 3")
                .list();
        List<BusinessCategoryResp> respList = JsonUtil.copyList(businessCategories, BusinessCategoryResp.class);
        return respList.stream().map(BusinessCategoryResp::getCategoryId).collect(Collectors.toList());
    }


    @Override
    public List<BusinessCategory> queryShopMaxBondList(List<Long> collect) {
        //查询所有类目
        List<BusinessCategory> categories = businessCategoryRepository.lambdaQuery().in(BusinessCategory::getShopId, collect).list();
        //过滤出每个shopId下的最大保证金
        return categories.stream().collect(Collectors.groupingBy(BusinessCategory::getShopId)).values().stream().map(v -> {
            //获取最大保证金
            BusinessCategory maxBond = v.stream().max(Comparator.comparing(BusinessCategory::getBond)).get();
            //设置是否冻结
            maxBond.setWhetherFrozen(v.stream().anyMatch(BusinessCategory::getWhetherFrozen));
            return maxBond;
        }).collect(Collectors.toList());
    }

    @Override
    public BusinessCategory queryShopMaxBond(Long shopId) {
        //查询所有类目
        List<BusinessCategory> categories = businessCategoryRepository.lambdaQuery().eq(BusinessCategory::getShopId, shopId).list();
        if (CollectionUtil.isEmpty(categories)) {
            return null;
        }
        //过滤出最大保证金
        return categories.stream().max(Comparator.comparing(BusinessCategory::getBond)).get();
    }

    /**
     * 因为只是一条关联记录, 直接使用物理删除
     *
     * @param id 经营分类id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteBusinessCategory(BaseIdReq id) {
        BusinessCategory businessCategory = businessCategoryRepository.getById(id.getId());
        if (businessCategory == null) {
            throw new BusinessException("经营类目不存在");
        }

        // 删除经营类目信息
        businessCategoryRepository.removeById(id.getId());
        // 记录操作日志
        baseLogAssist.recordLog(ExaminModelEnum.USER, ExaProEnum.MOVE,
                "删除店铺类目信息",
                id.getOperationUserId(),
                id.getOperationShopId(),
                new CmdBusinessCategoryReq(id), new CmdBusinessCategoryReq(id));

        // 发送类目删除消息
        BusinessCategoryEvent event = BusinessCategoryEvent.of(businessCategory.getShopId(),
                Collections.singletonList(businessCategory.getCategoryId()), BusinessCategoryChangeEnum.DELETE.getCode());
        businessCategoryPublisher.sendEvent(event);
    }

    @Override
    public List<CategoryTreeNodeBo> queryBusinessCategoryTree(Long shopId) {
        Shop shop = shopRepository.getById(shopId);
        if (shop == null) {
            return Collections.EMPTY_LIST;
        }

        // 如果是官方自营店 则查询所有类目
        if (shop.getWhetherSelf()) {
            return remoteCategoryService.queryCategoryTree(ShowStatusEnum.SHOW_ALL, true);
        }


        List<Long> categoryIds = businessCategoryRepository.listValidCategoryIdsByShopId(shopId);
        return remoteCategoryService.queryCategoryTreeWithParent(categoryIds);
    }

    @Override
    public BusinessCategory queryOne(Long shopId, Long categoryId) {
        return businessCategoryRepository.lambdaQuery()
                .eq(BusinessCategory::getShopId, shopId)
                .eq(BusinessCategory::getCategoryId, categoryId)
                .orderByDesc(BusinessCategory::getId)
                .one();
    }

    @Override
    public List<BusinessCategory> queryListByShopIdAndCategoryIds(Long shopId, List<Long> thirdCategoryIds) {
        return businessCategoryRepository.lambdaQuery()
                .eq(BusinessCategory::getShopId, shopId)
                .in(BusinessCategory::getCategoryId, thirdCategoryIds)
                .list();
    }


    @Override
    public List<BusinessCategory> queryListByCategoryIds(List<Long> thirdCategoryIds) {
        return businessCategoryRepository.lambdaQuery()
                .in(BusinessCategory::getCategoryId, thirdCategoryIds)
                .list();
    }

    @Override
    public void updateBatchById(List<BusinessCategory> businessCategoryList) {
        businessCategoryRepository.updateBatchById(businessCategoryList);
    }

    @Override
    public BusinessCategoryRespList queryList(BaseBatchIdReq req) {
        //todo in 批次
        return new BusinessCategoryRespList(JsonUtil.copyList(businessCategoryRepository.lambdaQuery().in(BusinessCategory::getShopId, req.getId()).list(), BusinessCategoryResp.class));
    }

    @Override
    public ValidBusinessCategoryIdsResp queryValidBusinessCategoryIds(Long shopId) {
        // 查询有效的类目id的集合
        List<Long> categoryIds = businessCategoryRepository.listObjs(new LambdaQueryWrapper<BusinessCategory>()
                        .eq(BusinessCategory::getShopId, shopId)
                        .eq(BusinessCategory::getWhetherFrozen, Boolean.FALSE)
                        .select(BusinessCategory::getCategoryId),
                obj -> (Long) obj);

        // 查询待审核的类目id的集合
        List<Long> waitAuditCategoryIds = businessCategoryRepository.listWaitAuditCategoryIdsByShopId(shopId);

        ValidBusinessCategoryIdsResp resp = new ValidBusinessCategoryIdsResp();
        resp.setCategoryIds(categoryIds);
        resp.setWaitAuditCategoryIds(waitAuditCategoryIds);
        return resp;
    }

    @Override
    public QueryLastCategoryResp queryById(BaseBatchIdReq id) {
        Map<Long, CategoryBindFormBo> categoryBindFormBoMap = categoryAssistant.getCategoryFormMap(id.getId());
        List<CategoryApplyFormDto> categoryApplyFormDtoList = new ArrayList<>();
        categoryBindFormBoMap.forEach((k, v1) -> {
            RemoteFormBo v = v1.getForm();
            if (Objects.isNull(v)) {
                return;
            }
            CategoryApplyFormDto categoryApplyFormDto = new CategoryApplyFormDto();
            categoryApplyFormDto.setCategoryId(k);
            categoryApplyFormDto.setFormId(v.getId());
            categoryApplyFormDto.setFormName(v.getName());
            if (CollUtil.isNotEmpty(v.getFormFields())) {
                categoryApplyFormDto.setFieldList(v.getFormFields().stream().map(field -> {
                    CategoryApplyFormFieldDto categoryApplyFormFieldDto = new CategoryApplyFormFieldDto();
                    categoryApplyFormFieldDto.setFieldId(field.getId());
                    categoryApplyFormFieldDto.setFieldName(field.getFieldName());
                    categoryApplyFormFieldDto.setType(field.getType());
                    categoryApplyFormFieldDto.setFormat(field.getFormat());
                    categoryApplyFormFieldDto.setOption(field.getOption());
                    categoryApplyFormFieldDto.setIsRequired(field.getIsRequired());
                    categoryApplyFormFieldDto.setRequired(field.getIsRequired());
                    categoryApplyFormFieldDto.setDisplaySequence(field.getDisplaySequence());
                    return categoryApplyFormFieldDto;
                }).collect(Collectors.toList()));
            }
            categoryApplyFormDtoList.add(categoryApplyFormDto);
        });
        QueryLastCategoryResp queryLastCategoryResp = new QueryLastCategoryResp();
        queryLastCategoryResp.setFormList(categoryApplyFormDtoList);
        return queryLastCategoryResp;
    }

    @Override
    public ImportResult importBusinessCategory(CmdImportCategoryReq req) {
        if (StrUtil.isNotEmpty(req.getPath()) && !req.getPath().startsWith("http")) {
            req.setPath(downloadUrlHeader + req.getPath());
        }
        ImportContextHolder.set(CategoryImportContext.builder().shopId(req.getShopId()).build());
        // 记录操作日志
        baseLogAssist.recordLog(ExaminModelEnum.USER, ExaProEnum.MODIFY,
                "导入店铺类目信息",
                req.getOperationUserId(),
                req.getOperationShopId(),
                req, req);
        return importWay.importData(BizTypeEnum.CATEGORY_IMPORT, req.getPath(), BusinessCategoryReadReq.class, new CategoryImportListener(), CommonConstant.EXCEL_IMPORT_MAX_SIZE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeByCategoryIds(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return;
        }

        MybatisUtil.executeBatch(curIds -> businessCategoryRepository
                .remove(new LambdaQueryWrapper<>(BusinessCategory.class).in(BusinessCategory::getCategoryId, curIds)), categoryIds);
    }

    @Override
    public List<Long> queryShopIdListByCategoryAndShop(Long shopId, List<Long> categoryIds) {
        return businessCategoryRepository.listByShopAndCategory(shopId, categoryIds);
    }

    @Override
    public List<Long> queryShopIdListByCategory(List<Long> categoryIds) {
        return businessCategoryRepository.listByCategory(categoryIds);
    }

    /**
     * 通用查询条件构造器
     *
     * @param queryBo 查询条件
     * @return 查询条件构造器
     */
    private QueryWrapper<BusinessCategory> commonWrapperBuilder(BusinessCategoryQueryBo queryBo) {
        QueryWrapper<BusinessCategory> wrapper = new QueryWrapper<>();
        if (queryBo.getShopId() != null) {
            wrapper.lambda().eq(BusinessCategory::getShopId, queryBo.getShopId());
        }
        if (CollectionUtil.isNotEmpty(queryBo.getCategoryIds())) {
            wrapper.lambda().in(BusinessCategory::getCategoryId, queryBo.getCategoryIds());
        }
        if (CollectionUtil.isNotEmpty(queryBo.getAlreadyRemoveIds())) {
            wrapper.lambda().notIn(BusinessCategory::getCategoryId, queryBo.getAlreadyRemoveIds());
        }
        wrapper.lambda().orderByDesc(BusinessCategory::getId);
        return wrapper;
    }
}
