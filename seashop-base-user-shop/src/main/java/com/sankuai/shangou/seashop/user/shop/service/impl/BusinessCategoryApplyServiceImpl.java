package com.sankuai.shangou.seashop.user.shop.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.lock.DistributedLockService;
import com.sankuai.shangou.seashop.base.lock.model.LockKey;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.product.thrift.core.response.category.CategoryResp;
import com.sankuai.shangou.seashop.user.common.constant.BusinessCategoryConstant;
import com.sankuai.shangou.seashop.user.common.constant.CommonConstant;
import com.sankuai.shangou.seashop.user.common.constant.LockConstant;
import com.sankuai.shangou.seashop.user.common.enums.BusinessCategoryChangeEnum;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService;
import com.sankuai.shangou.seashop.user.common.remote.product.model.RemoteCategoryBo;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategory;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryApply;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryApplyDetail;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryForm;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.repository.BusinessCategoryApplyDetailRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.BusinessCategoryApplyRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.BusinessCategoryFormRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.BusinessCategoryRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.sankuai.shangou.seashop.user.shop.mq.model.BusinessCategoryEvent;
import com.sankuai.shangou.seashop.user.shop.mq.publisher.BusinessCategoryPublisher;
import com.sankuai.shangou.seashop.user.shop.service.BusinessCategoryApplyService;
import com.sankuai.shangou.seashop.user.shop.service.assist.BusinessCategoryAssistant;
import com.sankuai.shangou.seashop.user.shop.service.assist.FormAssistant;
import com.sankuai.shangou.seashop.user.shop.service.assist.FormFieldAssistant;
import com.sankuai.shangou.seashop.user.shop.service.model.ApplyCategoryAndFormDataBo;
import com.sankuai.shangou.seashop.user.shop.service.model.AuditBusinessCategoryApplyBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryApplyBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryApplyDetailBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormCompareBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormFieldBo;
import com.sankuai.shangou.seashop.user.shop.service.model.CategoryBo;
import com.sankuai.shangou.seashop.user.shop.service.model.QueryApplyCategoryAndFormDataBo;
import com.sankuai.shangou.seashop.user.shop.service.model.QueryBusinessCategoryApplyDetailBo;
import com.sankuai.shangou.seashop.user.shop.service.model.QueryWaitFinishContractNumBo;
import com.sankuai.shangou.seashop.user.shop.service.model.SupplyCustomFormBo;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.BusinessCategoryEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.enums.ShopEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryResp;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 权限服务类
 * @author: LXH
 **/
@Service
@Slf4j
public class BusinessCategoryApplyServiceImpl implements BusinessCategoryApplyService {
    @Resource
    private ShopRepository shopRepository;
    @Resource
    private BusinessCategoryRepository businessCategoryRepository;
    @Resource
    private BusinessCategoryFormRepository businessCategoryFormRepository;
    @Resource
    private BusinessCategoryApplyRepository businessCategoryApplyRepository;
    @Resource
    private BusinessCategoryApplyDetailRepository businessCategoryApplyDetailRepository;
    @Resource
    private FormAssistant formAssistant;
    @Resource
    private FormFieldAssistant formFieldAssistant;

    @Resource
    private RemoteCategoryService remoteCategoryService;
    @Resource
    private BusinessCategoryAssistant businessCategoryAssistant;
    @Resource
    private DistributedLockService distributedLockService;
    @Resource
    private BusinessCategoryPublisher businessCategoryPublisher;

    @Override
    public Long addApply(BusinessCategoryApplyBo applyBo) {
        Long shopId = applyBo.getShopId();
        String lockKey = LockConstant.BUSINESS_CATEGORY_APPLY_AUDIT_LOCK + shopId;
        String scene = LockConstant.BUSINESS_CATEGORY_APPLY_AUDIT_SCENE + shopId;
        try {
            return distributedLockService.tryLock(new LockKey(lockKey, scene), () -> {
                List<Long> categoryIdList = applyBo.getCategoryIdList();
                if (CollectionUtil.isEmpty(categoryIdList)) {
                    throw new BusinessException(UserResultCodeEnum.FAILED_TO_ADD_CATEGORY);
                }

                long count = businessCategoryRepository.count(new LambdaQueryWrapper<BusinessCategory>()
                        .eq(BusinessCategory::getShopId, shopId).in(BusinessCategory::getCategoryId, categoryIdList));
                AssertUtil.throwIfTrue(count > 0, UserResultCodeEnum.BUSINESS_CATEGORY_ALREADY_EXISTS);

                // 检验是否有待审核的类目
                List<Long> waitAuditCategoryIds = businessCategoryRepository.listWaitAuditCategoryIdsByShopId(shopId);
                categoryIdList.forEach(categoryId -> {
                    if (!waitAuditCategoryIds.contains(categoryId)) {
                        return;
                    }

                    CategoryResp category = remoteCategoryService.queryCategoryById(categoryId);
                    AssertUtil.throwIfTrue(ObjectUtil.isNotNull(category), String.format("[%s]正在审核中", category.getName()));
                });

                // 校验店铺和类目信息 申请的类目只能为三级
                Shop shop = shopRepository.getById(shopId);
                AssertUtil.throwIfNull(shop, "店铺信息不存在");
                List<RemoteCategoryBo> categoryList = remoteCategoryService.queryCategoryList(categoryIdList);
                List<RemoteCategoryBo> lastCategoryList = categoryList.stream()
                        .filter(category -> category.getDepth().equals(CommonConstant.APPLY_BUSINESS_CATEGORY_DEPTH)).collect(Collectors.toList());
                AssertUtil.throwIfTrue(CollectionUtils.isEmpty(lastCategoryList), "申请的类目只能为三级类目");

                // 保存申请记录
                return TransactionHelper.doInTransaction(() -> {
                    Long applyId = saveBusinessCategoryApply(shop);
                    // 保存类目申请详情
                    saveBusinessCategoryApplyDetail(lastCategoryList, applyId);
                    // 保存自定义表单信息
                    supplyCustomForm(shopId, applyBo.getFormList());

                    // 发送经营类目变动事件
                    List<Long> categoryIds = lastCategoryList.stream().map(RemoteCategoryBo::getId).distinct().collect(Collectors.toList());
                    BusinessCategoryEvent event = BusinessCategoryEvent.of(shopId, categoryIds, BusinessCategoryChangeEnum.APPLY.getCode());
                    businessCategoryPublisher.sendEvent(event);
                    return applyId;
                });
            });
        } catch (Throwable e) {
            log.error("【经营类目申请】申请失败,applyBo={}", applyBo, e);
            throw new BusinessException(UserResultCodeEnum.FAILED_TO_ADD_CATEGORY.getCode(), e.getMessage());
        }
    }

    @Override
    public BusinessCategoryApply getByShopIdOnAudit(Long shopId) {
        QueryWrapper<BusinessCategoryApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BusinessCategoryApply::getShopId, shopId).eq(BusinessCategoryApply::getAuditedStatus, BusinessCategoryEnum.ApplyStatus.UnAudited.getCode());
        //根据id倒叙排序
        queryWrapper.lambda().orderByDesc(BusinessCategoryApply::getId);
        return businessCategoryApplyRepository.getOne(queryWrapper);
    }

    @Override
    public BasePageResp<BusinessCategoryApplyResp> queryPage(QueryBusinessCategoryApplyPageReq queryBusinessCategoryApplyPageReq) {
        Page<BusinessCategoryApply> applyPage = PageHelper.startPage(queryBusinessCategoryApplyPageReq.getPageNo(), queryBusinessCategoryApplyPageReq.getPageSize());
        //先根据店铺名称,审核状态和协议状态查询出申请记录
        BusinessCategoryApply apply = new BusinessCategoryApply();
        apply.setShopName(queryBusinessCategoryApplyPageReq.getShopName());
        apply.setAuditedStatus(queryBusinessCategoryApplyPageReq.getAuditedStatus());
        apply.setAgreementStatus(queryBusinessCategoryApplyPageReq.getAgreementStatus());
        apply.setShopId(queryBusinessCategoryApplyPageReq.getShopId());
        businessCategoryApplyRepository.selectList(apply);
        return PageResultHelper.transfer(applyPage, BusinessCategoryApplyResp.class, (db, res) -> {
            res.setAuditedStatusDesc(BusinessCategoryEnum.ApplyStatus.getDescByCode(db.getAuditedStatus()));
            res.setAgreementStatusDesc(BusinessCategoryEnum.AgreementStatus.getDescByCode(db.getAgreementStatus()));
        });
    }

    @Override
    public BasePageResp<BusinessCategoryApplyResp> queryPageForSeller(QueryBusinessCategoryApplyPageReq request) {
        BasePageResp<BusinessCategoryApplyResp> resp = queryPage(request);
        List<BusinessCategoryApplyResp> applyList = resp.getData();
        if (CollectionUtils.isEmpty(applyList)) {
            return resp;
        }

        List<Long> applyIds = applyList.stream()
                .filter(item -> item.getAuditedStatus().equals(BusinessCategoryEnum.ApplyStatus.Audited.getCode()))
                .map(BusinessCategoryApplyResp::getId).collect(Collectors.toList());
        Long shopId = request.getShopId();
        Map<Long, BusinessCategoryFormCompareBo> newOldFormCompareMap = businessCategoryAssistant.getFormChangeResult(applyIds, shopId);
        applyList.forEach(apply -> {
            BusinessCategoryFormCompareBo compareBo = newOldFormCompareMap.get(apply.getId());
            if (compareBo == null) {
                apply.setNeedSupply(Boolean.FALSE);
                return;
            }

            apply.setNeedSupply(CollectionUtils.isNotEmpty(compareBo.getChangeFormList()));
        });
        return resp;
    }

    @Override
    public BusinessCategoryApplyDetailBo queryDetail(QueryBusinessCategoryApplyDetailBo queryBo) {
//        queryBo.setShopId(null);
        return queryDetailForSeller(queryBo);
    }

    @Override
    public BusinessCategoryApplyDetailBo queryDetailForSeller(QueryBusinessCategoryApplyDetailBo queryBo) {
        BusinessCategoryApply applyDetail = businessCategoryApplyRepository.getById(queryBo.getId());
        AssertUtil.throwIfNull(applyDetail, "申请记录不存在");
        if (queryBo.getShopId() != null) {
            AssertUtil.throwIfTrue(!applyDetail.getShopId().equals(queryBo.getShopId()), "无权查看");
        }

        BusinessCategoryApplyDetailBo result = JsonUtil.copy(applyDetail, BusinessCategoryApplyDetailBo.class);
        Map<Long, BusinessCategoryFormCompareBo> compareResult =
                businessCategoryAssistant.getFormChangeResult(Arrays.asList(applyDetail.getId()), applyDetail.getShopId());
        BusinessCategoryFormCompareBo curCompareResult = compareResult.get(applyDetail.getId());
        // 拷贝旧表单的值到新表单
        assignFieldValue(curCompareResult.getNewFormList(), curCompareResult.getOldFormList());
        result.setFormList(curCompareResult.getNewFormList());
        result.setChangeFormList(curCompareResult.getChangeFormList());
        result.setNeedSupply(CollectionUtils.isNotEmpty(curCompareResult.getChangeFormList()));

        // 查询申请的类目信息
        List<BusinessCategoryApplyDetail> applyDetailList = businessCategoryApplyDetailRepository.listByApplyIds(Arrays.asList(applyDetail.getId()));
        Map<Long, BusinessCategoryApplyDetail> applyDetailMap = applyDetailList.stream()
                .collect(Collectors.toMap(BusinessCategoryApplyDetail::getCategoryId, Function.identity(), (k1, k2) -> k1));
        List<RemoteCategoryBo> categoryList = remoteCategoryService.queryCategoryList(applyDetailMap.keySet().stream().collect(Collectors.toList()));
        // 查询类目全路径 替换分佣比例
        categoryList.forEach(category -> {
            BusinessCategoryApplyDetail detail = applyDetailMap.get(category.getId());
            category.setCommissionRate(detail == null ? category.getCommissionRate() : detail.getCommissionRate());
            category.setFullCategoryName(category.getFullCategoryName().replace(StrUtil.COMMA, BusinessCategoryConstant.CATEGORY_NAME_SPACER));
        });
        result.setApplyCategoryList(JsonUtil.copyList(categoryList, BusinessCategoryBo.class));
        result.setAuditedStatusDesc(BusinessCategoryEnum.ApplyStatus.getDescByCode(applyDetail.getAuditedStatus()));
        result.setAgreementStatusDesc(BusinessCategoryEnum.AgreementStatus.getDescByCode(applyDetail.getAgreementStatus()));
        return result;
    }

    @Override
    public Long supplyCustomForm(SupplyCustomFormBo supplyForm) {
        Long shopId = supplyForm.getShopId();
        String lockKey = LockConstant.BUSINESS_CATEGORY_APPLY_AUDIT_LOCK + shopId;
        String scene = LockConstant.BUSINESS_CATEGORY_APPLY_AUDIT_SCENE + shopId;
        try {
            return distributedLockService.tryLock(new LockKey(lockKey, scene), () -> {
                BusinessCategoryApply apply = businessCategoryApplyRepository.getById(supplyForm.getId());
                AssertUtil.throwIfTrue(!apply.getShopId().equals(supplyForm.getShopId()), "无权操作");

                TransactionHelper.doInTransaction(() -> {
                    // 补充资料
                    supplyCustomForm(shopId, supplyForm.getFormList(), apply, req -> {
                        // 冻结经营类目
                        List<Long> categoryIds = freezeOrUnfreezeCategory(req, true);
                        // 更新状态未待审核
                        BusinessCategoryApply updApply = new BusinessCategoryApply();
                        updApply.setId(req.getId());
                        updApply.setAuditedStatus(BusinessCategoryEnum.ApplyStatus.UnAudited.getCode());
                        businessCategoryApplyRepository.updateById(updApply);

                        // 发送经营类目变动事件
                        BusinessCategoryEvent event = BusinessCategoryEvent.of(shopId, categoryIds, BusinessCategoryChangeEnum.SUPPLY_DATA.getCode());
                        businessCategoryPublisher.sendEvent(event);
                    });

                });
                return apply.getId();
            });
        } catch (Throwable e) {
            log.error("【经营类目申请】补充自定义表单失败,supplyForm={}", JsonUtil.toJsonString(supplyForm), e);
            throw new BusinessException("补充表单信息失败");
        }
    }

    /**
     * 冻结或者解冻该申请下的经营类目
     *
     * @param apply  申请记录
     * @param freeze 是否冻结
     */
    private List<Long> freezeOrUnfreezeCategory(BusinessCategoryApply apply, Boolean freeze) {
        List<BusinessCategoryApplyDetail> applyDetailList = businessCategoryApplyDetailRepository.getByApplyId(apply.getId());
        if (CollectionUtils.isEmpty(applyDetailList)) {
            return new ArrayList<>();
        }

        List<Long> categoryIds = applyDetailList.stream().map(BusinessCategoryApplyDetail::getCategoryId).collect(Collectors.toList());
        businessCategoryRepository.freezeOrUnfreezeCategory(apply.getShopId(), categoryIds, freeze);
        return categoryIds;
    }

    @Override
    public Integer queryWaitFinishContractNum(QueryWaitFinishContractNumBo queryBo) {
        // 供应商需要计算需要补充表单的申请数量
        if (queryBo.getShopId() != null) {
            return queryWaitFinishContractNumIncludeSupply(queryBo.getShopId());
        }

        LambdaQueryWrapper<BusinessCategoryApply> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(queryBo.getShopId() != null, BusinessCategoryApply::getShopId, queryBo.getShopId())
                .and(w -> w.eq(BusinessCategoryApply::getAuditedStatus, BusinessCategoryEnum.ApplyStatus.UnAudited.getCode()));
        return Math.toIntExact(businessCategoryApplyRepository.count(wrapper));
    }

    private Integer queryWaitFinishContractNumIncludeSupply(Long shopId) {
        Shop shop = shopRepository.getById(shopId);
        List<Integer> validShopStatus = Arrays.asList(ShopEnum.AuditStatus.Freeze.getCode(), ShopEnum.AuditStatus.Open.getCode());
        // 店铺不存在 或者不在有效的状态 不统计
        if (shop == null || !validShopStatus.contains(shop.getShopStatus())) {
            return 0;
        }

        List<BusinessCategoryApply> applyList = businessCategoryApplyRepository
                .list(new LambdaQueryWrapper<BusinessCategoryApply>().eq(BusinessCategoryApply::getShopId, shopId));
        // 过滤出待审核和待签署的申请记录
        List<Long> waitDealIdList = applyList.stream()
                .filter(apply -> apply.getAuditedStatus().equals(BusinessCategoryEnum.ApplyStatus.UnAudited.getCode())
                        || apply.getAgreementStatus().equals(BusinessCategoryEnum.AgreementStatus.UnAudited.getCode()))
                .map(BusinessCategoryApply::getId)
                .collect(Collectors.toList());

        // 获取审核通过的记录(通过的记录中 需要去计算待补充资料的)
        List<Long> passApplyIdList = applyList.stream()
                .filter(apply -> apply.getAuditedStatus().equals(BusinessCategoryEnum.ApplyStatus.Audited.getCode()))
                .map(BusinessCategoryApply::getId)
                .collect(Collectors.toList());
        Map<Long, BusinessCategoryFormCompareBo> changeMap = businessCategoryAssistant.getFormChangeResult(passApplyIdList, shopId);
        changeMap.forEach((applyId, compareResult) -> {
            if (compareResult != null && CollectionUtils.isNotEmpty(compareResult.getChangeFormList())) {
                waitDealIdList.add(applyId);
            }
        });
        Long waitDealCount = waitDealIdList.stream().distinct().count();
        return waitDealCount.intValue();
    }

    @Override
    public Long auditBusinessCategoryApply(AuditBusinessCategoryApplyBo auditBo) {
        BusinessCategoryApply apply = businessCategoryApplyRepository.getById(auditBo.getId());
        AssertUtil.throwIfNull(apply, "审核记录不存在");
        Long shopId = apply.getShopId();

        String lockKey = LockConstant.BUSINESS_CATEGORY_APPLY_AUDIT_LOCK + shopId;
        String scene = LockConstant.BUSINESS_CATEGORY_APPLY_AUDIT_SCENE + shopId;
        distributedLockService.tryLock(new LockKey(lockKey, scene), () -> {
            BusinessCategoryApply businessCategoryApply = businessCategoryApplyRepository.getById(auditBo.getId());
            AssertUtil.throwIfNull(businessCategoryApply, "审核记录不存在");
            AssertUtil.throwIfTrue(!businessCategoryApply.getAuditedStatus().equals(BusinessCategoryEnum.ApplyStatus.UnAudited.getCode()), "该审核记录已审核");

            // 获取本次申请的类目信息, 过滤掉已经有权限的类目
            List<BusinessCategoryApplyDetail> applyDetailList = businessCategoryApplyDetailRepository.getByApplyId(businessCategoryApply.getId());
            List<Long> applyCategoryIds = applyDetailList.stream().map(BusinessCategoryApplyDetail::getCategoryId).collect(Collectors.toList());
            List<RemoteCategoryBo> categoryList = remoteCategoryService.queryCategoryList(applyCategoryIds);

            TransactionHelper.doInTransaction(() -> {
                // 更新审核信息
                BusinessCategoryApply updApply = new BusinessCategoryApply();
                updApply.setId(businessCategoryApply.getId());
                updApply.setAuditedStatus(auditBo.getPass() ? BusinessCategoryEnum.ApplyStatus.Audited.getCode() : BusinessCategoryEnum.ApplyStatus.Refused.getCode());
                updApply.setRefuseReason(auditBo.getRefuseReason());
                updApply.setAuditedDate(new Date());
                businessCategoryApplyRepository.updateById(updApply);

                List<Long> categoryIds = categoryList.stream().map(RemoteCategoryBo::getId).collect(Collectors.toList());

                // 绑定用户经营类目
                if (auditBo.getPass()) {
                    this.bindUserBusinessCategory(businessCategoryApply.getShopId(), categoryList);
                }

                // 发送事件
                Integer changeType = auditBo.getPass() ?
                        BusinessCategoryChangeEnum.AUDIT_PASS.getCode() : BusinessCategoryChangeEnum.AUDIT_REFUSE.getCode();
                BusinessCategoryEvent event = BusinessCategoryEvent.of(shopId, categoryIds, changeType);
                businessCategoryPublisher.sendEvent(event);
            });
        });
        return apply.getId();
    }

    @Override
    public ApplyCategoryAndFormDataBo queryApplyCategoryAndFormData(QueryApplyCategoryAndFormDataBo queryBo) {
        Long shopId = queryBo.getShopId();
        List<RemoteCategoryBo> remoteCategoryBoList = remoteCategoryService.queryLastCategoryList(queryBo.getCategoryId());
        if (CollectionUtils.isEmpty(remoteCategoryBoList)) {
            log.info("未查询出类目，categoryId:{}", queryBo.getCategoryId());
            return ApplyCategoryAndFormDataBo.ofEmpty();
        }

        // 过滤掉已经申请并通过审的类目
        List<Long> applyCategoryIds = businessCategoryApplyRepository.listCategoryApplyIdsByShopId(queryBo.getShopId());
        if (!CollectionUtils.isEmpty(applyCategoryIds)) {
            log.info("查出已过审类目ID：{}", JsonUtil.toJsonString(applyCategoryIds));
            List<BusinessCategoryApplyDetail> applyDetailList = businessCategoryApplyDetailRepository.listByApplyIds(applyCategoryIds);
            if (!CollectionUtils.isEmpty(applyDetailList)) {
                List<Long> existCategoryIds = applyDetailList.stream().map(BusinessCategoryApplyDetail::getCategoryId).collect(Collectors.toList());
                remoteCategoryBoList = remoteCategoryBoList.stream().filter(category -> !existCategoryIds.contains(category.getId())).collect(Collectors.toList());
                log.info("已过审类目过滤后：{}", JsonUtil.toJsonString(remoteCategoryBoList));
            }
        }

        applyCategoryIds = remoteCategoryBoList.stream().map(RemoteCategoryBo::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(applyCategoryIds)) {
            log.info("未查询出类目，applyCategoryIds:{}", JsonUtil.toJsonString(applyCategoryIds));
            return ApplyCategoryAndFormDataBo.ofEmpty();
        }

        List<BusinessCategoryFormBo> formList = businessCategoryAssistant.getNeedSupplyForm(applyCategoryIds, shopId);
        return ApplyCategoryAndFormDataBo.builder()
                .categoryList(JsonUtil.copyList(remoteCategoryBoList, CategoryBo.class))
                .formList(formList)
                .build();
    }

    @Override
    public List<BusinessCategoryResp> queryDetailByShopId(Long id) {
        List<BusinessCategoryApply> applyList = businessCategoryApplyRepository.lambdaQuery().eq(BusinessCategoryApply::getShopId, id).list();
        if (CollectionUtils.isEmpty(applyList)) {
            return new ArrayList<>();
        }
        List<BusinessCategoryApplyDetail> applyDetailList = businessCategoryApplyDetailRepository.listByApplyIds(applyList.stream().map(BusinessCategoryApply::getId).collect(Collectors.toList()));
        return JsonUtil.copyList(applyDetailList, BusinessCategoryResp.class);
    }

    @Override
    public List<BusinessCategoryResp> queryDetailByShopIdLimit(Long id) {
        List<BusinessCategoryApply> applyList = businessCategoryApplyRepository.lambdaQuery()
                .last("limit 3")
                .eq(BusinessCategoryApply::getShopId, id).list();
        if (CollectionUtils.isEmpty(applyList)) {
            return new ArrayList<>();
        }
        List<BusinessCategoryApplyDetail> applyDetailList = businessCategoryApplyDetailRepository.listByApplyIds(applyList.stream().map(BusinessCategoryApply::getId).collect(Collectors.toList()));
        return JsonUtil.copyList(applyDetailList, BusinessCategoryResp.class);
    }

    @Override
    public void deleteApply(Long shopId) {
//        重新申请的申请记录需要删除
        log.info("删除申请记录,shopId:{}", shopId);
//        获取所有申请Id
        List<Long> applyIds = businessCategoryApplyRepository.lambdaQuery()
                .eq(BusinessCategoryApply::getShopId, shopId).list().stream().map(BusinessCategoryApply::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(applyIds)) {
            return;
        }
        //删除申请记录
        businessCategoryApplyRepository.lambdaUpdate().eq(BusinessCategoryApply::getShopId, shopId).remove();
        //删除申请详情
        businessCategoryApplyDetailRepository.lambdaUpdate().in(BusinessCategoryApplyDetail::getApplyId, applyIds).remove();
        //删除自定义表单
        businessCategoryFormRepository.lambdaUpdate().eq(BusinessCategoryForm::getShopId, shopId).remove();
    }

    private void supplyCustomForm(Long shopId, List<BusinessCategoryFormBo> newFormList) {
        supplyCustomForm(shopId, newFormList, null, null);
    }

    /**
     * 补充表单信息
     * 匹配到商家之前填写过的同一表单信息, 有则新增， 无则编辑
     *
     * @param shopId      店铺id
     * @param newFormList 新的表单信息
     */
    private void supplyCustomForm(Long shopId, List<BusinessCategoryFormBo> newFormList, BusinessCategoryApply apply,
                                  Consumer<BusinessCategoryApply> successCallback) {
        if (CollectionUtils.isEmpty(newFormList)) {
            return;
        }

        List<Long> formIds = newFormList.stream().map(BusinessCategoryFormBo::getFormId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(formIds)) {
            return;
        }
        List<BusinessCategoryForm> dbFormList = businessCategoryFormRepository.listByShopIdAndFormIds(shopId, formIds);
        List<BusinessCategoryFormBo> oldFormList = formAssistant.transBusinessCategoryFormToBo(dbFormList);
        List<BusinessCategoryForm> saveFormList = new ArrayList<>();

        // 对比新旧表单, 计算是新增还是合并
        Map<Long, BusinessCategoryFormBo> oldFormMap = oldFormList.stream()
                .collect(Collectors.toMap(BusinessCategoryFormBo::getFormId, Function.identity(), (k1, k2) -> k2));
        newFormList.forEach(newForm -> {
            BusinessCategoryFormBo oldForm = oldFormMap.get(newForm.getFormId());

            BusinessCategoryForm saveForm = new BusinessCategoryForm();
            saveForm.setCategoryId(newForm.getCategoryId());
            saveForm.setShopId(shopId);
            saveForm.setFormId(newForm.getFormId());
            if (oldForm == null) {
                saveForm.setFormData(formFieldAssistant.transToFormString(newForm.getFieldList()));
            } else {
                saveForm.setId(oldForm.getId());
                saveForm.setFormData(formFieldAssistant.transToFormString(unionFields(oldForm.getFieldList(), newForm.getFieldList())));
            }
            saveFormList.add(saveForm);
        });

        TransactionHelper.doInTransaction(() -> {
            if (!CollectionUtils.isEmpty(saveFormList)) {
                businessCategoryFormRepository.saveOrUpdateBatch(saveFormList);
            }
            if (apply != null) {
                successCallback.accept(apply);
            }
//            补充完成店铺改为不需要补充资料
            shopRepository.lambdaUpdate()
                    .eq(Shop::getId, shopId)
                    .set(Shop::getWhetherSupply, false).update();
        });
    }

    /**
     * 合并表单字段
     *
     * @param oldFieldList 旧字段列表
     * @param newFieldList 新字段列表
     * @return 合并后的字段列表
     */
    private List<BusinessCategoryFormFieldBo> unionFields(List<BusinessCategoryFormFieldBo> oldFieldList,
                                                          List<BusinessCategoryFormFieldBo> newFieldList) {
        if (CollectionUtils.isEmpty(oldFieldList)) {
            return newFieldList;
        }

        if (CollectionUtils.isEmpty(newFieldList)) {
            return oldFieldList;
        }

        List<BusinessCategoryFormFieldBo> fieldList = new ArrayList<>(oldFieldList);
        Map<String, BusinessCategoryFormFieldBo> fieldMap = fieldList.stream()
                .collect(Collectors.toMap(BusinessCategoryFormFieldBo::getFieldName, Function.identity(), (k1, k2) -> k2));
        newFieldList.forEach(newField -> {
            BusinessCategoryFormFieldBo field = fieldMap.get(newField.getFieldName());
            if (field != null) {
                field.setFieldValue(newField.getFieldValue());
                return;
            }
            fieldList.add(newField);
        });
        return fieldList;
    }

    /**
     * 保存类目申请记录
     *
     * @param shop 店铺信息
     * @return 申请id
     */
    private Long saveBusinessCategoryApply(@NonNull Shop shop) {
        // 增加审核记录
        BusinessCategoryApply businessCategoryApply = new BusinessCategoryApply();
        businessCategoryApply.setShopId(shop.getId());
        businessCategoryApply.setShopName(shop.getShopName());
        businessCategoryApply.setApplyDate(new Date());
        businessCategoryApply.setAuditedStatus(BusinessCategoryEnum.ApplyStatus.UnAudited.getCode());
        businessCategoryApply.setAgreementStatus(BusinessCategoryEnum.AgreementStatus.UnAudited.getCode());
        businessCategoryApplyRepository.save(businessCategoryApply);
        return businessCategoryApply.getId();
    }

    /**
     * 保存类目申请详情
     *
     * @param categoryList 类目信息
     * @param applyId      申请id
     */
    private void saveBusinessCategoryApplyDetail(List<RemoteCategoryBo> categoryList, Long applyId) {
        List<BusinessCategoryApplyDetail> businessCategories = new ArrayList<>();
        categoryList.forEach(categoryResp -> {
            BusinessCategoryApplyDetail businessCategory = new BusinessCategoryApplyDetail();
            businessCategory.setApplyId(applyId);
            businessCategory.setCategoryId(categoryResp.getId());
            businessCategory.setCommissionRate(categoryResp.getCommissionRate());
            businessCategories.add(businessCategory);
        });

        //增加审核详情记录
        businessCategoryApplyDetailRepository.saveBatch(businessCategories);
    }

    /**
     * 表单字段赋值(旧表单的value赋值到新表单)
     *
     * @param targetFormList 新表单
     * @param sourceFormList 旧表单
     */
    private void assignFieldValue(List<BusinessCategoryFormBo> targetFormList, List<BusinessCategoryFormBo> sourceFormList) {
        if (CollectionUtils.isEmpty(targetFormList) || CollectionUtils.isEmpty(sourceFormList)) {
            return;
        }

        Map<Long, BusinessCategoryFormBo> oldFormMap = sourceFormList.stream()
                .collect(Collectors.toMap(BusinessCategoryFormBo::getFormId, Function.identity(), (k1, k2) -> k2));
        targetFormList.forEach(newForm -> {
            BusinessCategoryFormBo oldForm = oldFormMap.get(newForm.getFormId());
            if (oldForm == null) {
                return;
            }

            List<BusinessCategoryFormFieldBo> newFieldList = newForm.getFieldList();
            List<BusinessCategoryFormFieldBo> oldFieldList = oldForm.getFieldList();
            if (CollectionUtils.isEmpty(newFieldList) || CollectionUtils.isEmpty(oldFieldList)) {
                return;
            }

            Map<String, BusinessCategoryFormFieldBo> oldFieldMap = oldFieldList.stream()
                    .collect(Collectors.toMap(BusinessCategoryFormFieldBo::getFieldName, Function.identity(), (k1, k2) -> k2));
            newFieldList.forEach(newField -> {
                BusinessCategoryFormFieldBo oldField = oldFieldMap.get(newField.getFieldName());
                if (oldField == null) {
                    return;
                }

                newField.setFieldValue(oldField.getFieldValue());
            });
        });
    }

    /**
     * 绑定用户经营类目
     *
     * @param shopId       店铺id
     * @param categoryList 类目id集合
     */
    @Override
    public void bindUserBusinessCategory(Long shopId, List<RemoteCategoryBo> categoryList) {
        if (CollectionUtils.isEmpty(categoryList)) {
            return;
        }

        List<BusinessCategory> existBusinessCategoryList = businessCategoryRepository.listByShopId(shopId);
        Map<Long, BusinessCategory> businessCategoryMap = existBusinessCategoryList.stream()
                .collect(Collectors.toMap(BusinessCategory::getCategoryId, Function.identity(), (k1, k2) -> k2));

        List<BusinessCategory> addList = new ArrayList<>();
        List<BusinessCategory> updList = new ArrayList<>();
        categoryList.forEach(category -> {
            BusinessCategory dbBusinessCategory = businessCategoryMap.get(category.getId());
            // 新增
            if (dbBusinessCategory == null) {
                BusinessCategory addBusinessCategory = new BusinessCategory();
                addBusinessCategory.setShopId(shopId);
                addBusinessCategory.setCategoryId(category.getId());
                addBusinessCategory.setCommissionRate(category.getCommissionRate());
                addBusinessCategory.setBond(category.getCashDeposit());
                addList.add(addBusinessCategory);
                return;
            }

            // 编辑
            BusinessCategory updBusinessCategory = new BusinessCategory();
            updBusinessCategory.setId(dbBusinessCategory.getId());
            updBusinessCategory.setWhetherFrozen(Boolean.FALSE);
            updList.add(updBusinessCategory);
        });

        if (CollectionUtils.isNotEmpty(addList)) {
            businessCategoryRepository.saveBatch(addList);
        }
        if (CollectionUtils.isNotEmpty(updList)) {
            businessCategoryRepository.updateBatchById(updList);
        }
    }

}
