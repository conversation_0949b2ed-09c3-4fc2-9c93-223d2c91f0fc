package com.sankuai.shangou.seashop.user.shop.service.model.shop;

import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryBo;
import lombok.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/4/004
 * @description:
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopEsBo {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺名称
     */
    private String shopName;

    /**
     * 店铺状态
     */
    private Integer shopStatus;

    /**
     * 商品销量
     */
    private Long productSaleCount;

    /**
     * 品牌列表
     */
    private List<ShopBrandBo> shopBrandList;

    /**
     * 类目列表
     */
    private List<BusinessCategoryBo> businessCategoryList;

    /**
     * 评分数量
     */
    private Long markCount;

    /**
     * 包装评分
     */
    private BigDecimal packMark;

    /**
     * 服务评分
     */
    private BigDecimal serviceMark;

    /**
     * 综合评分
     */
    private BigDecimal comprehensiveMark;
}
