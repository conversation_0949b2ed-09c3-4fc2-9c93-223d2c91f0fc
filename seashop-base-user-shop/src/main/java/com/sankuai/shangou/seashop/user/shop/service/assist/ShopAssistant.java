package com.sankuai.shangou.seashop.user.shop.service.assist;

import com.sankuai.shangou.seashop.base.boot.utils.AesUtil;
import com.sankuai.shangou.seashop.user.common.config.EncryptConfig;
import com.sankuai.shangou.seashop.user.dao.account.domain.Manager;
import com.sankuai.shangou.seashop.user.dao.account.repository.ManagerRepository;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopExt;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopExtRepository;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopRepository;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CmdShopReq;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/12/11 19:18
 */
@Component
public class ShopAssistant {

    @Resource
    private ShopRepository shopRepository;
    @Resource
    private ShopExtRepository shopExtRepository;
    @Resource
    private ManagerRepository managerRepository;
    @Resource
    private EncryptConfig encryptConfig;


    public void setShopExt(CmdShopReq cmdShopReq, ShopExt shopExt) {
        shopExt.setCompanyName(cmdShopReq.getCompanyName());
        shopExt.setCompanyAddress(cmdShopReq.getCompanyAddress());
        shopExt.setCompanyRegionId(cmdShopReq.getCompanyRegionId());
        shopExt.setBusinessLicenseNumber(cmdShopReq.getBusinessLicenseNumber());
        shopExt.setBusinessSphere(cmdShopReq.getBusinessSphere());
        shopExt.setCompanyRegionId(cmdShopReq.getCompanyRegionId());
        shopExt.setLegalPerson(cmdShopReq.getLegalPerson());
        shopExt.setBusinessLicenseNumberPhoto(cmdShopReq.getBusinessLicenseNumberPhoto());
        //判空
        if (cmdShopReq.getBusinessLicenseStart() != null) {
            shopExt.setBusinessLicenseStart(cmdShopReq.getBusinessLicenseStart());
        }
        if (cmdShopReq.getBusinessLicenseEnd() != null) {
            shopExt.setBusinessLicenseEnd(cmdShopReq.getBusinessLicenseEnd());
        }
        shopExtRepository.updateById(shopExt);
    }

    public void setPersonalShopExt(CmdShopReq cmdShopReq, ShopExt shopExt) {
        shopExt.setCompanyName(cmdShopReq.getCompanyName());
        shopExt.setCompanyAddress(cmdShopReq.getCompanyAddress());
        shopExt.setCompanyRegionId(cmdShopReq.getCompanyRegionId());

        shopExtRepository.updateById(shopExt);
    }

    /**
     * 设置公共属性
     *
     * @param cmdShopReq 入参
     * @param shop       供应商信息
     * @param manager    管理员信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void setCommonAttr(CmdShopReq cmdShopReq, Shop shop, Manager manager) {
        shop.setIdCard(AesUtil.encrypt(cmdShopReq.getIdCard(), encryptConfig.getAesSecret()));
        shop.setIdCardurl(cmdShopReq.getIdCardUrl());
        shop.setIdCardurl2(cmdShopReq.getIdCardUrl2());
        //判空
        if (cmdShopReq.getIdCardStartDate() != null) {
            shop.setIdCardStartDate(cmdShopReq.getIdCardStartDate());
        }
        if (cmdShopReq.getIdCardEndDate() != null) {
            shop.setIdCardEndDate(cmdShopReq.getIdCardEndDate());
        }
        shop.setShopName(cmdShopReq.getShopName());
        shop.setContactsName(cmdShopReq.getContactsName());
        shop.setContactsPhone(cmdShopReq.getContactsPhone());
        shop.setContactsEmail(cmdShopReq.getContactsEmail());
        shop.setBankPhoto(cmdShopReq.getBankPhoto());
        shop.setLogo(cmdShopReq.getLogo());
        shop.setIdCardExpireType(cmdShopReq.getIdCardExpireType());
        //管理员
        manager.setRealName(cmdShopReq.getAccountName());
        shopRepository.updateById(shop);
        managerRepository.updateById(manager);
    }

}
