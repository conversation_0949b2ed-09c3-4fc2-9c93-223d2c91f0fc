package com.sankuai.shangou.seashop.user.shop.service;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description:
 */
public interface EsShopBuildService {

    /**
     * 通过店铺 构建es
     *
     * @param shopId 店铺id
     */
    void buildByShop(Long shopId);

    /**
     * 通过店铺品牌 构建es
     *
     * @param shopId 店铺id
     */
    void buildByShopBrand(Long shopId);

    /**
     * 通过店铺类目 构建es
     *
     * @param shopId 店铺id
     */
    void buildByBusinessCategory(Long shopId);

    /**
     * 通过店铺订单评价 构建es
     *
     * @param shopId 店铺id
     */
    void buildByOrderComment(Long shopId);

    /**
     * 通过店铺订单(已完成的订单) 构建es
     *
     * @param orderId 订单ID
     */
    void buildByFinishOrder(String orderId);

    /**
     * 通过店铺专属商家 构建es
     *
     * @param shopId 店铺id
     */
    void buildByExclusiveMember(Long shopId);

    /**
     * 通过店铺商品，构建销量信息
     */
    void buildByProductSaleCount(Long shopId);

    /**
     * 更新店铺es(全量更新)
     */
    void refreshShopEs(Long shopId);
}
