package com.sankuai.shangou.seashop.user.shop.service.assist;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.common.remote.base.model.RemoteFormBo;
import com.sankuai.shangou.seashop.user.common.remote.base.model.RemoteFormFieldBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormFieldBo;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/11/30 13:34
 */
@Component
@Slf4j
public class FormFieldAssistant {

    @Resource
    private CustomFormAssistant customFormAssistant;

    /**
     * 字段前缀 用来拼接字段Id
     */
    private static final String FIELD_PREFIX = "Field_";
    /**
     * 空json字符
     */
    private static final String EMPTY_JSON = "{}";

    /**
     * 将字段列表转换为json字符串
     *
     * @param fieldList 字段列表
     * @return json字符串
     */
    public String transToFormString(List<BusinessCategoryFormFieldBo> fieldList) {
        if (CollectionUtils.isEmpty(fieldList)) {
            return EMPTY_JSON;
        }
        Map<String, String> fieldMap = new HashMap<>();
        fieldList.forEach(field -> {
            fieldMap.put(FIELD_PREFIX + field.getFieldId(), StringUtils.defaultString(field.getFieldValue(), ""));
        });
        return JsonUtil.toJsonString(fieldMap);
    }

    /**
     * 将json字符串转换为字段列表
     *
     * @param fieldJson json字符串
     * @return 字段列表
     */
    public List<BusinessCategoryFormFieldBo> transToFieldList(String fieldJson) {
        if (StringUtils.isBlank(fieldJson) || !JsonUtil.isJson(fieldJson)) {
            return Collections.EMPTY_LIST;
        }

        LinkedHashMap<String, String> fieldMap = JsonUtil.parseObject(fieldJson, LinkedHashMap.class);
        if (CollectionUtils.isEmpty(fieldMap)) {
            return Collections.EMPTY_LIST;
        }
        log.info("fieldJson:{}", fieldJson);
        List<BusinessCategoryFormFieldBo> fieldList = new ArrayList<>(fieldMap.size());
        fieldMap.forEach((key, value) -> {
            if (StringUtils.startsWith(key, FIELD_PREFIX)||StringUtils.startsWithIgnoreCase(key, FIELD_PREFIX)) {
                BusinessCategoryFormFieldBo field = new BusinessCategoryFormFieldBo();
                field.setFieldId(Long.valueOf(StringUtils.removeStartIgnoreCase(key, FIELD_PREFIX)));
                field.setFieldValue(value);
                log.info("fieldId: {}, fieldValue: {}", key.toString(), value.toString());
                fieldList.add(field);
            }
        });
        return fieldList;
    }

    /**
     * 填充字段名称 (目前db只存储了fieldId)
     *
     * @param fieldList 字段集合
     */
    public void fillFieldName(List<BusinessCategoryFormFieldBo> fieldList) {
        if (CollectionUtils.isEmpty(fieldList)) {
            return;
        }

        List<Long> fieldIds = fieldList.stream().map(BusinessCategoryFormFieldBo::getFieldId).distinct().collect(Collectors.toList());
        Map<Long, String> fieldNameMap = customFormAssistant.getCustomerFieldNameMap(fieldIds);
        fieldList.forEach(field -> {
            field.setFieldName(fieldNameMap.getOrDefault(field.getFieldId(), ""));
        });
    }

    public void fillFieldAttr(List<BusinessCategoryFormFieldBo> fieldList) {
        if (CollectionUtils.isEmpty(fieldList)) {
            return;
        }

        List<Long> fieldIds = fieldList.stream().map(BusinessCategoryFormFieldBo::getFieldId).distinct().collect(Collectors.toList());
        Map<Long, RemoteFormFieldBo> fieldNameMap = customFormAssistant.getCustomerFieldMap(fieldIds);
        fieldList.forEach(field -> {
            RemoteFormFieldBo remoteFormFieldBo = fieldNameMap.get(field.getFieldId());
            if (remoteFormFieldBo == null) {
                return;
            }
            //设置名称相同字段
            field.setFieldName(remoteFormFieldBo.getFieldName());
            field.setFormat(remoteFormFieldBo.getFormat());
            field.setIsRequired(remoteFormFieldBo.getIsRequired());
            field.setOption(remoteFormFieldBo.getOption());
            field.setType(remoteFormFieldBo.getType());
            field.setDisplaySequence(remoteFormFieldBo.getDisplaySequence());
        });
    }

    public void fillFormName(List<BusinessCategoryFormBo> boList) {
        if (CollectionUtils.isEmpty(boList)) {
            return;
        }

        List<Long> formIds = boList.stream().map(BusinessCategoryFormBo::getFormId).distinct().collect(Collectors.toList());
        List<RemoteFormBo> remoteFormBos = customFormAssistant.listCustomerForms(formIds);
        Map<Long, String> formNameMap = remoteFormBos.stream().collect(Collectors.toMap(RemoteFormBo::getId, RemoteFormBo::getName, (k1, k2) -> k2));
        boList.forEach(bo -> {
            bo.setFormName(formNameMap.getOrDefault(bo.getFormId(), ""));
        });
    }
}
