package com.sankuai.shangou.seashop.user.shop.service.model;

import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class CalculateFreightShopBo {

    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 店铺商品总金额
     */
    private BigDecimal totalAmount;
    /**
     * 店铺下的商品列表
     */
    private List<CalculateFreightProductBo> productList;

}
