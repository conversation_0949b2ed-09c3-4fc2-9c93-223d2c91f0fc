package com.sankuai.shangou.seashop.user.shop.service.model;

import java.util.Collections;
import java.util.List;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/30 9:26
 */
@Getter
@Setter
@Builder
public class ApplyCategoryAndFormDataBo {

    /**
     * 类目信息
     */
    private List<CategoryBo> categoryList;

    /**
     * 表单信息
     */
    private List<BusinessCategoryFormBo> formList;

    public static ApplyCategoryAndFormDataBo ofEmpty() {
        return ApplyCategoryAndFormDataBo.builder()
                .categoryList(Collections.EMPTY_LIST)
                .formList(Collections.EMPTY_LIST)
                .build();
    }

}
