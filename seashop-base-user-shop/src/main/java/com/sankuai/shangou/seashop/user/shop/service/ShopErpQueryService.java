package com.sankuai.shangou.seashop.user.shop.service;

import java.util.List;

import com.sankuai.shangou.seashop.user.thrift.shop.enums.ErpTypeEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryJstToExpireShopErpPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopErpByJstReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopErpReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopErpDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopErpResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.WdtTokenResp;

/**
 * @author： liweisong
 * @create： 2023/11/28 15:08
 */
public interface ShopErpQueryService {

    // 查看ERP
    QueryShopErpResp queryShopErp(QueryShopErpReq queryShopErpReq);

    /**
     * 通过 erpType+token 反查获取商户id
     *
     * @param erpType erpType
     * @param token   三方erp token
     * @return 配置信息
     */
    QueryShopErpResp queryShopByToken(ErpTypeEnum erpType, String token);

    /**
     * 查询聚水潭临过期商户
     *
     * @param req 查询条件
     * @return 分页商户列表
     */
    List<QueryShopErpDetailResp> queryJstToExpireShopErp(QueryJstToExpireShopErpPageReq req);

    /**
     * 根据聚水潭查询erp
     *
     * @param req
     * @return
     */
    List<QueryShopErpDetailResp> queryErpByJst(QueryShopErpByJstReq req);

    /**
     * 查询旺店通授权码
     * @param queryShopErpReq
     * @return
     */
    WdtTokenResp queryWdtTokenByShopId(QueryShopErpReq queryShopErpReq);
}
