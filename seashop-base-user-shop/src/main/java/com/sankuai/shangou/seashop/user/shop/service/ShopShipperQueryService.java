package com.sankuai.shangou.seashop.user.shop.service;

import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryShopShipperRespDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopDefaultShipperResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopShipperResp;

import java.util.List;
import java.util.Map;

/**
 * @author： liweisong
 * @create： 2023/11/27 10:39
 */
public interface ShopShipperQueryService {

    /**
     * 查询
     * @param queryShopShipperReq
     * @return
     */
    QueryShopShipperResp queryShopShipperList(QueryShopShipperReq queryShopShipperReq);

    /**
     * 查询默认
     * @param queryShopShipperReq
     * @return
     */
    QueryShopDefaultShipperResp queryShopDefaultShipperList(QueryShopShipperReq queryShopShipperReq);

    /**
     * 批量查询
     * @param shopIds
     * @return
     */
    Map<Long,List<QueryShopShipperRespDto>> queryBatchShopShipperList(List<Long> shopIds);
}
