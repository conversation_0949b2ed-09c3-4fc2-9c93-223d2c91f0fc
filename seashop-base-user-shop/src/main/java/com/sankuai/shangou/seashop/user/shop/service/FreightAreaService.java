package com.sankuai.shangou.seashop.user.shop.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.request.BaseBatchIdReq;
import com.sankuai.shangou.seashop.user.thrift.shop.dto.QueryFreightTemplateDto;
import com.sankuai.shangou.seashop.user.thrift.shop.request.AddFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.CopyFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryForbiddenAreaTplReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateDetailReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFreightTemplateReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryRestrictedRegionReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateFreightReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateDetailResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFreightTemplateResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryRestrictedRegionResp;

public interface FreightAreaService {

    /**
     * 运费模版管理查询接口
     * @param queryFreightTemplateReq
     * @return
     */
    QueryFreightTemplateResp queryFreightTemplateList(QueryFreightTemplateReq queryFreightTemplateReq);

    /**
     * 删除运费模版接口
     * @param cmdFreightTemplateReq
     * @return
     */
    void deleteFreightTemplate(DeleteFreightTemplateReq cmdFreightTemplateReq);

    /**
     * 新增运费模版
     * @param addFreightReq
     * @return
     */
    void addFreightTemplate(AddFreightReq addFreightReq);

    /**
     * 修改运费模版
     * @param updateFreightReq
     * @return
     */
    void updateFreightTemplate(UpdateFreightReq updateFreightReq);

    /**
     * 运费模版查询详情接口
     * @param queryFreightTemplateDetailReq
     * @return
     */
    QueryFreightTemplateDetailResp queryFreightTemplateDetail(QueryFreightTemplateDetailReq queryFreightTemplateDetailReq);

    /**
     * 复制模板
     * @param copyFreightReq
     * @return
     */
    void copyFreightTemplate(CopyFreightReq copyFreightReq);

    /**
     * 查询受限区域
     * @param queryRestrictedRegionReq
     * @return
     */
    QueryRestrictedRegionResp queryRestrictedRegion(QueryRestrictedRegionReq queryRestrictedRegionReq);

    /**
     * 根据ID查询运费模版列表
     * @param req 参数
     */
    List<QueryFreightTemplateDto> queryTplByIdList(BaseBatchIdReq req);

    /**
     * 查询开启非销售区域和地址在非销售区域的运费模板
     * @param req 参数
     */
    List<Long> getForbiddenOnAndAddrInForbiddenArea(QueryForbiddenAreaTplReq req);
}
