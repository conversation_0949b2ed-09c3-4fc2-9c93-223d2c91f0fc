package com.sankuai.shangou.seashop.user.shop.service;

import java.util.List;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.user.common.remote.product.model.RemoteCategoryBo;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryApply;
import com.sankuai.shangou.seashop.user.shop.service.model.ApplyCategoryAndFormDataBo;
import com.sankuai.shangou.seashop.user.shop.service.model.AuditBusinessCategoryApplyBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryApplyBo;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryApplyDetailBo;
import com.sankuai.shangou.seashop.user.shop.service.model.QueryApplyCategoryAndFormDataBo;
import com.sankuai.shangou.seashop.user.shop.service.model.QueryBusinessCategoryApplyDetailBo;
import com.sankuai.shangou.seashop.user.shop.service.model.QueryWaitFinishContractNumBo;
import com.sankuai.shangou.seashop.user.shop.service.model.SupplyCustomFormBo;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBusinessCategoryApplyPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryApplyResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.BusinessCategoryResp;

/**
 * @description: 供应商类目服务类
 * @author: LXH
 **/
public interface BusinessCategoryApplyService {
    /**
     * 添加类目申请
     *
     * @param applyBo 申请信息
     * @return 申请id
     */
    Long addApply(BusinessCategoryApplyBo applyBo);

    /**
     * 根据店铺id查询类目申请信息
     *
     * @param shopId 店铺id
     * @return 申请信息
     */
    BusinessCategoryApply getByShopIdOnAudit(Long shopId);

    /**
     * 根据条件分页查询类目申请信息(平台端)
     *
     * @param queryBusinessCategoryApplyPageReq 查询条件
     * @return 申请信息
     */
    BasePageResp<BusinessCategoryApplyResp> queryPage(QueryBusinessCategoryApplyPageReq queryBusinessCategoryApplyPageReq);

    /**
     * 根据条件分页查询类目申请信息(供应商端)
     *
     * @param request 查询条件
     * @return 申请信息
     */
    BasePageResp<BusinessCategoryApplyResp> queryPageForSeller(QueryBusinessCategoryApplyPageReq request);

    /**
     * 根据条件查询类目申请详情(平台端)
     *
     * @param queryBo 查询条件
     * @return 申请信息
     */
    BusinessCategoryApplyDetailBo queryDetail(QueryBusinessCategoryApplyDetailBo queryBo);

    /**
     * 根据条件查询类目申请详情(供应商端)
     *
     * @param queryBo 查询条件
     * @return 申请信息
     */
    BusinessCategoryApplyDetailBo queryDetailForSeller(QueryBusinessCategoryApplyDetailBo queryBo);

    /**
     * 供应商补充自定义表单
     *
     * @param supplyForm 补充信息
     * @return 申请id
     */
    Long supplyCustomForm(SupplyCustomFormBo supplyForm);

    /**
     * 查询待审核和待签署合同的申请数
     *
     * @param queryBo 查询条件
     * @return 申请数
     */
    Integer queryWaitFinishContractNum(QueryWaitFinishContractNumBo queryBo);

    /**
     * 审核类目申请
     *
     * @param auditBo 审核请求
     * @return 审核id
     */
    Long auditBusinessCategoryApply(AuditBusinessCategoryApplyBo auditBo);

    /**
     * 查询申请类目和表单信息
     *
     * @param queryBo 查询条件
     * @return 申请类目和表单信息
     */
    ApplyCategoryAndFormDataBo queryApplyCategoryAndFormData(QueryApplyCategoryAndFormDataBo queryBo);

    /**
     * 查询店铺类目
     *
     * @param id 店铺id
     * @return 类目信息
     */
    List<BusinessCategoryResp> queryDetailByShopId(Long id);

    /**
     * 查询店铺类目
     *
     * @param id 店铺id
     * @return 类目信息
     */
    List<BusinessCategoryResp> queryDetailByShopIdLimit(Long id);

    /**
     * 删除申请
     * @param id 店铺Id
     */
    void deleteApply(Long id);

    /**
     * 绑定用户经营类目绑定用户经营类目
     * @param shopId
     * @param categoryList
     */
    void bindUserBusinessCategory(Long shopId, List<RemoteCategoryBo> categoryList);
}
