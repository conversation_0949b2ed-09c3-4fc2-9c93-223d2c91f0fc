package com.sankuai.shangou.seashop.user.shop.service.impl;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.dao.shop.domain.OrderSetting;
import com.sankuai.shangou.seashop.user.dao.shop.repository.OrderSettingRepository;
import com.sankuai.shangou.seashop.user.shop.service.OrderSettingQueryService;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBatchOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryOrderSettingResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * @author： liweisong
 * @create： 2023/11/27 16:43
 */
@Service
public class OrderSettingQueryServiceImpl implements OrderSettingQueryService {

    @Resource
    private OrderSettingRepository orderSettingRepository;

    @Override
    public QueryOrderSettingResp queryOrderSetting(QueryOrderSettingReq queryOrderSettingReq) {
        QueryOrderSettingResp result = new QueryOrderSettingResp();
        OrderSetting orderSetting = new OrderSetting();
        orderSetting.setShopId(queryOrderSettingReq.getShopId());
        OrderSetting resp = orderSettingRepository.queryOrderSetting(orderSetting);
        if(Objects.isNull(resp)){
            return result;
        }
        result.setId(resp.getId());
        result.setShopId(resp.getShopId());
        result.setPurchaseMinValidType(resp.getPurchaseMinValidType());
        result.setPurchaseMinQuantity(resp.getPurchaseMinQuantity());
        result.setPurchaseMinPrice(resp.getPurchaseMinPrice());
        result.setCreateTime(resp.getCreateTime());
        result.setUpdateTime(resp.getUpdateTime());
        result.setCreateUser(resp.getCreateUser());
        result.setUpdateUser(resp.getUpdateUser());
        return result;
    }

    @Override
    public List<QueryOrderSettingResp> queryBatchOrderSetting(QueryBatchOrderSettingReq request) {
        List<OrderSetting> respList = orderSettingRepository.queryBatchOrderSetting(request.getShopIdList());
        return JsonUtil.copyList(respList, QueryOrderSettingResp.class);
    }
}
