package com.sankuai.shangou.seashop.user.shop.service.model;

import java.util.Collections;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/12/02 23:06
 */
@Getter
@Setter
public class BusinessCategoryFormCompareBo {

    private List<BusinessCategoryFormBo> newFormList;

    private List<BusinessCategoryFormBo> oldFormList;

    private List<BusinessCategoryFormBo> changeFormList;

    public static BusinessCategoryFormCompareBo ofEmpty() {
        BusinessCategoryFormCompareBo businessCategoryFormCompareBo = new BusinessCategoryFormCompareBo();
        businessCategoryFormCompareBo.setNewFormList(Collections.EMPTY_LIST);
        businessCategoryFormCompareBo.setOldFormList(Collections.EMPTY_LIST);
        businessCategoryFormCompareBo.setChangeFormList(Collections.EMPTY_LIST);
        return businessCategoryFormCompareBo;
    }
}
