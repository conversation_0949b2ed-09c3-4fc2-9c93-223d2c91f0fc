package com.sankuai.shangou.seashop.user.shop.service.model;

import java.math.BigDecimal;
import java.util.List;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2023/11/29 20:26
 */
@Getter
@Setter
public class CategoryBo extends BaseThriftDto {

    /**
     * 主键
     */
    private Long id;

    /**
     * 类目名称
     */
    private String name;

    /**
     * 类目图标
     */
    private String icon;

    /**
     * 排序
     */
    private Long displaySequence;

    /**
     * 上级类目id
     */
    private Long parentCategoryId;

    /**
     * 分佣比例
     */
    private BigDecimal commissionRate;

    /**
     * 类目的深度
     */
    private Integer depth;

    /**
     * 类目的路径（以|分离）
     */
    private String path;

    /**
     * 规格模板Id
     */
    private Long specTemplateId;

    /**
     * 自定义表单Id
     */
    private Long customFormId;

    /**
     * 类目全路径
     */
    private String fullCategoryName;

    /**
     * 保证金
     */
    private BigDecimal cashDeposit;

    /**
     * 需要补充的表单
     */
    private List<BusinessCategoryFormBo> formList;

}
