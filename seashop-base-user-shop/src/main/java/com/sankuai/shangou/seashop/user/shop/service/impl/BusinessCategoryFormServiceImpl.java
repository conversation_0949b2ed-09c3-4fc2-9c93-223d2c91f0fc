package com.sankuai.shangou.seashop.user.shop.service.impl;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryForm;
import com.sankuai.shangou.seashop.user.dao.shop.repository.BusinessCategoryFormRepository;
import com.sankuai.shangou.seashop.user.shop.service.BusinessCategoryApplyService;
import com.sankuai.shangou.seashop.user.shop.service.BusinessCategoryFormService;
import com.sankuai.shangou.seashop.user.shop.service.assist.FormAssistant;
import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormBo;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@Service
@Slf4j
public class BusinessCategoryFormServiceImpl implements BusinessCategoryFormService {
    @Resource
    private BusinessCategoryFormRepository businessCategoryFormRepository;
    @Resource
    private BusinessCategoryApplyService businessCategoryApplyService;
    @Resource
    private FormAssistant formAssistant;

    @Override
    public List<CategoryApplyFormDto> queryFormListByShopId(Long id, List<Long> categoryIds) {
        // 查询表单字段
        List<BusinessCategoryForm> formList = businessCategoryFormRepository.listByShopIdAndCategoryIds(id, categoryIds);
        List<BusinessCategoryFormBo> formBoList = formAssistant.transBusinessCategoryFormToBoAll(formList);
        // 转化为dto
        return JsonUtil.copyList(formBoList, CategoryApplyFormDto.class);
    }


}
