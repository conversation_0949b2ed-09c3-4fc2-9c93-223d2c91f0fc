package com.sankuai.shangou.seashop.user.shop.service;

import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryBatchOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryOrderSettingReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryOrderSettingResp;

import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/27 16:43
 */
public interface OrderSettingQueryService {

    // 查询店铺交易设置
    QueryOrderSettingResp queryOrderSetting(QueryOrderSettingReq request);

    List<QueryOrderSettingResp> queryBatchOrderSetting(QueryBatchOrderSettingReq request);
}
