package com.sankuai.shangou.seashop.user.shop.service;

import java.math.BigDecimal;
import java.util.Map;

import com.sankuai.shangou.seashop.user.shop.service.model.CalculateFreightBo;

/**
 * <AUTHOR>
 */
public interface FreightService {

    /**
     * 计算运费
     * <AUTHOR>
     * @param calculateFreightBo 店铺以及商品列表信息
     * @return 每个店铺的运费
     */
    Map<Long, BigDecimal> calculateFreight(CalculateFreightBo calculateFreightBo);

}
