package com.sankuai.shangou.seashop.user.shop.service;

import com.sankuai.shangou.seashop.user.thrift.shop.request.AddShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.DeleteShopShipperReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateShopShipperDefaultReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.UpdateShopShipperReq;

/**
 * @author： liweisong
 * @create： 2023/11/27 10:38
 */
public interface ShopShipperCmdService {

    /**
     * 新增
     * @param addShopShipperReq
     */
    void addShopShipper(AddShopShipperReq addShopShipperReq);

    /**
     * 修改
     * @param updateShopShipperReq
     */
    void updateShopShipper(UpdateShopShipperReq updateShopShipperReq);

    /**
     * 修改默认值
     * @param updateShopShipperDefaultReq
     */
    void updateShopShipperDefault(UpdateShopShipperDefaultReq updateShopShipperDefaultReq);

    /**
     * 删除
     * @param deleteShopShipperReq
     */
    void deleteShopShipper(DeleteShopShipperReq deleteShopShipperReq);
}
