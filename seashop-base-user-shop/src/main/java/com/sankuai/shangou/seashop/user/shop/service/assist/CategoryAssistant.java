package com.sankuai.shangou.seashop.user.shop.service.assist;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.sankuai.shangou.seashop.user.common.remote.base.model.RemoteFormBo;
import com.sankuai.shangou.seashop.user.common.remote.product.RemoteCategoryService;
import com.sankuai.shangou.seashop.user.common.remote.product.model.RemoteCategoryBo;
import com.sankuai.shangou.seashop.user.shop.service.model.CategoryBindFormBo;

/**
 * <AUTHOR>
 * @date 2023/12/01 8:46
 */
@Component
public class CategoryAssistant {

    @Resource
    private RemoteCategoryService remoteCategoryService;
    @Resource
    private CustomFormAssistant customFormAssistant;

    /**
     * 查询类目下绑定的表单信息
     * 只有一级类目下才会绑定表单信息, 如果传入的类目不是一级类目, 则需要转换成一级类目去获取表单信息
     *
     * @param categoryIds 类目id 的集合
     * @return 类目信息map
     */
    public Map<Long, CategoryBindFormBo> getCategoryFormMap(List<Long> categoryIds) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return MapUtils.EMPTY_MAP;
        }

        // 当前的分类集合
        categoryIds = categoryIds.stream().distinct().collect(Collectors.toList());
        List<RemoteCategoryBo> categoryList = remoteCategoryService.queryCategoryList(categoryIds);

        // 查询出一级分类信息
        List<RemoteCategoryBo> firstCategoryList = remoteCategoryService.queryFirstCategoryList(categoryIds);
        Map<Long, RemoteCategoryBo> firstCategoryMap = firstCategoryList.stream().collect(Collectors.toMap(RemoteCategoryBo::getId, Function.identity(), (k1, k2) -> k2));

        // 查询一级分类绑定的表单信息
        List<Long> formIds = firstCategoryList.stream().map(RemoteCategoryBo::getCustomFormId).collect(Collectors.toList());
        List<RemoteFormBo> formList = customFormAssistant.listCustomerForms(formIds);
        Map<Long, RemoteFormBo> formMap = formList.stream().collect(Collectors.toMap(RemoteFormBo::getId, Function.identity(), (k1, k2) -> k2));

        Map<Long, CategoryBindFormBo> categoryFormMap = new HashMap<>(categoryList.size());
        categoryList.forEach(category -> {
            List<Long> curCategoryIds = category.getCategoryIds();
            // 理论上该集合不可能为空, 除非存储的数据异常
            if (CollectionUtils.isEmpty(curCategoryIds)) {
                return;
            }

            CategoryBindFormBo categoryBindFormBo = new CategoryBindFormBo();
            categoryBindFormBo.setCategoryId(category.getId());
            categoryBindFormBo.setFirstCategoryId(curCategoryIds.get(0));
            RemoteCategoryBo firstCategory = firstCategoryMap.get(categoryBindFormBo.getFirstCategoryId());
            if (firstCategory == null) {
                return;
            }
            categoryBindFormBo.setCategoryId(firstCategory.getId());
            categoryBindFormBo.setFormId(firstCategory.getCustomFormId());
            categoryBindFormBo.setForm(formMap.get(firstCategory.getCustomFormId()));

            categoryFormMap.put(category.getId(), categoryBindFormBo);
        });
        return categoryFormMap;
    }

}
