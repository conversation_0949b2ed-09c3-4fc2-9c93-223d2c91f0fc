package com.sankuai.shangou.seashop.user.shop.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.user.common.constant.ShopOpenApiSettingConstant;
import com.sankuai.shangou.seashop.user.common.enums.UserResultCodeEnum;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopOpenApiSetting;
import com.sankuai.shangou.seashop.user.dao.shop.repository.ShopOpenApiSettingRepository;
import com.sankuai.shangou.seashop.user.shop.service.ShopOpenApiSettingService;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopOpenApiSettingResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class ShopOpenApiSettingServiceImpl implements ShopOpenApiSettingService {
    @Resource
    private ShopOpenApiSettingRepository shopOpenApiSettingRepository;

    @Override
    public String makeAppKey(Long shopId) {
        String result = ShopOpenApiSettingConstant.APP_KEY_PREFIX;
        if (shopId != null && shopId > 0) {
            result = result + shopId;
        }
        for (int i = 0; i < ShopOpenApiSettingConstant.APP_KEY_LENGTH; i++) {
            result = result + RandomUtil.randomString(ShopOpenApiSettingConstant.APP_KEY_BASE, ShopOpenApiSettingConstant.APP_KEY_LENGTH);
            //根据appKey查询是否存在
            ShopOpenApiSetting shopOpenApiSetting = shopOpenApiSettingRepository.lambdaQuery().eq(ShopOpenApiSetting::getAppKey, result).one();
            if (shopOpenApiSetting == null) {
                break;
            }
        }
        return result;
    }

    @Override
    public String makeAppSecret() {
        String result = null;
        for (int i = 0; i < ShopOpenApiSettingConstant.APP_KEY_LENGTH; i++) {
            result = ShopOpenApiSettingConstant.APP_SECRET_PREFIX + RandomUtil.randomString(ShopOpenApiSettingConstant.APP_KEY_BASE, ShopOpenApiSettingConstant.APP_SECRET_LENGTH);
            //根据appKey查询是否存在
            ShopOpenApiSetting shopOpenApiSetting = shopOpenApiSettingRepository.lambdaQuery().eq(ShopOpenApiSetting::getAppSecret, result).one();
            if (shopOpenApiSetting == null) {
                break;
            }
        }
        if (result == null) {
            throw new BusinessException(UserResultCodeEnum.FAILED_TO_GENERATE_APP_SECRET);
        }
        return result;
    }

    @Override
    public ShopOpenApiSetting getShopOpenApiSettingByShopId(Long shopId) {
        return shopOpenApiSettingRepository.lambdaQuery().eq(ShopOpenApiSetting::getShopId, shopId).one();
    }

    @Override
    public ShopOpenApiSetting create(Shop shop) {
        ShopOpenApiSetting shopOpenApiSetting = getShopOpenApiSettingByShopId(shop.getId());
        if (shopOpenApiSetting != null) {
            throw new BusinessException("店铺已生成AppKey,不可以重复生成"); //TODO 异常
        }
        ShopOpenApiSetting result = new ShopOpenApiSetting();
        result.setShopId(shop.getId());
        result.setAppKey(makeAppKey(shop.getId()));
        result.setAppSecret(makeAppSecret());
        result.setAddDate(new Date());
        result.setLastEditDate(new Date());
        result.setWhetherEnable(false);
        result.setWhetherRegistered(false);
        shopOpenApiSettingRepository.save(result);
        return result;

    }

    @Override
    public ShopOpenApiSettingResp queryOpenApiSetting(String appKey) {
        ShopOpenApiSetting shopOpenApiSetting = shopOpenApiSettingRepository.lambdaQuery().eq(ShopOpenApiSetting::getAppKey, appKey).one();
        return JsonUtil.copy(shopOpenApiSetting, ShopOpenApiSettingResp.class);
    }

    @Override
    public ShopOpenApiSettingResp queryOpenApiSettingByShop(Long id) {
        ShopOpenApiSetting shopOpenApiSetting = shopOpenApiSettingRepository.lambdaQuery().eq(ShopOpenApiSetting::getShopId, id).one();
        return JsonUtil.copy(shopOpenApiSetting, ShopOpenApiSettingResp.class);
    }

    @Override
    public List<ShopOpenApiSettingResp> queryOpenApiSettingByShopIds(List<Long> shopIds) {
        List<ShopOpenApiSetting> shopOpenApiSettings = shopOpenApiSettingRepository.lambdaQuery().in(ShopOpenApiSetting::getShopId, shopIds).list();
        return JsonUtil.copyList(shopOpenApiSettings, ShopOpenApiSettingResp.class);
    }
}
