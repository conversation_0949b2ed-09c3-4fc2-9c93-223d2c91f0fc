package com.sankuai.shangou.seashop.user.shop.service;

import com.sankuai.shangou.seashop.user.shop.service.model.BusinessCategoryFormBo;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormDto;
import com.sankuai.shangou.seashop.user.thrift.shop.response.dto.CategoryApplyFormFieldDto;

import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
public interface BusinessCategoryFormService {

    List<CategoryApplyFormDto> queryFormListByShopId(Long id, List<Long> categoryIds);
}
