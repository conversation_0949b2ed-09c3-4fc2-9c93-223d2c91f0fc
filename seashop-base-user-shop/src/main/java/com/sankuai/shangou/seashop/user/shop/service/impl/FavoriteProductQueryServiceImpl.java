package com.sankuai.shangou.seashop.user.shop.service.impl;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.trade.thrift.core.TradeProductQueryFeign;
import com.sankuai.shangou.seashop.trade.thrift.core.request.ProductIdsReq;
import com.sankuai.shangou.seashop.trade.thrift.core.response.product.QueryProductByIdListResp;
import com.sankuai.shangou.seashop.user.common.enums.shop.StockStatusEmun;
import com.sankuai.shangou.seashop.user.dao.account.domain.Favorite;
import com.sankuai.shangou.seashop.user.dao.shop.repository.FavoriteProductRepository;
import com.sankuai.shangou.seashop.user.shop.service.FavoriteProductQueryService;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryFavoriteProductReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryFavoriteProductResp;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author： liweisong
 * @create： 2023/11/28 9:53
 */
@Service
@Slf4j
public class FavoriteProductQueryServiceImpl implements FavoriteProductQueryService {

    @Resource
    private FavoriteProductRepository favoriteProductRepository;
    @Resource
    private TradeProductQueryFeign tradeProductQueryFeign;

    @Override
    public BasePageResp<QueryFavoriteProductResp> pageFavoriteProduct(QueryFavoriteProductReq queryFavoriteProductReq) {
        Favorite favorite = new Favorite();
        favorite.setUserId(queryFavoriteProductReq.getUserId());
        Page<Favorite> favoritePageInfo = favoriteProductRepository.pageFavoriteProduct(favorite, queryFavoriteProductReq.getPageNo(), queryFavoriteProductReq.getPageSize());
        BasePageResp<QueryFavoriteProductResp> basePageResp = PageResultHelper.transfer(favoritePageInfo, QueryFavoriteProductResp.class);
        List<QueryFavoriteProductResp> list = basePageResp.getData();
        if(CollectionUtil.isEmpty(list)){
            return new BasePageResp(queryFavoriteProductReq.getPageSize(), queryFavoriteProductReq.getPageNo(), 0, 0L, null);
        }
        List<String> productIds = list.stream().map(QueryFavoriteProductResp::getProductId).collect(Collectors.toList());
        ProductIdsReq request = new ProductIdsReq();
        request.setProductIds(productIds);
        List<QueryProductByIdListResp> productList = ThriftResponseHelper.executeThriftCall(()->tradeProductQueryFeign.queryProductByIdList(request));
        if(CollectionUtil.isEmpty(productList)){
            return new BasePageResp(queryFavoriteProductReq.getPageSize(), queryFavoriteProductReq.getPageNo(), 0, 0L, null);
        }
        Map<String, QueryProductByIdListResp> map = productList.stream().collect(Collectors.toMap(QueryProductByIdListResp::getProductId, Function.identity(), (k1, k2) -> k1));
        List<String> productIdList = new ArrayList<>();
        list.forEach(queryFavoriteProductResp -> {
            QueryProductByIdListResp queryProductByIdListResp = map.get(queryFavoriteProductResp.getProductId());
            if(Objects.isNull(queryProductByIdListResp)){
                productIdList.add(queryFavoriteProductResp.getProductId());
                return;
            }
            queryFavoriteProductResp.setMinSalePrice(queryProductByIdListResp.getMinSalePrice());
            queryFavoriteProductResp.setMarketPrice(queryProductByIdListResp.getMarketPrice());
            queryFavoriteProductResp.setAuditStatus(queryProductByIdListResp.getAuditStatus());
            queryFavoriteProductResp.setAuditStatusName(queryProductByIdListResp.getAuditStatusName());
            queryFavoriteProductResp.setSaleStatus(queryProductByIdListResp.getSaleStatus());
            queryFavoriteProductResp.setSaleStatusName(queryProductByIdListResp.getSaleStatusName());
            queryFavoriteProductResp.setMainImagePath(queryProductByIdListResp.getMainImagePath());
            queryFavoriteProductResp.setProductName(queryProductByIdListResp.getProductName());
            queryFavoriteProductResp.setTotalCount(queryProductByIdListResp.getTotalCount());
            queryFavoriteProductResp.setSinglePrice(queryProductByIdListResp.getSinglePrice());
            boolean offShelf = queryProductByIdListResp.getAuditStatus() == 4 || queryProductByIdListResp.getSaleStatus() == 2;
            queryFavoriteProductResp.setOffShelf(offShelf ? 1:0);
            boolean hasStock = queryProductByIdListResp.getTotalStock()==null ? false : queryProductByIdListResp.getTotalStock() > 0L;
            queryFavoriteProductResp.setHasStock(hasStock ? 1:0);
            StockStatusEmun stockStatusEmun = !hasStock ? StockStatusEmun.SOLD_OUT : StockStatusEmun.getEmunBySaleStatusAndAuditStatus(queryProductByIdListResp.getAuditStatus(),queryProductByIdListResp.getSaleStatus());
            queryFavoriteProductResp.setStockStatus(stockStatusEmun.getCode());
            queryFavoriteProductResp.setStockStatusName(stockStatusEmun.getName());
        });
        return basePageResp;
    }

    @Override
    public Boolean queryFavoriteProductStatus(Long productId, Long userId) {
        return favoriteProductRepository.count(new LambdaQueryWrapper<Favorite>().eq(Favorite::getProductId, productId).eq(Favorite::getUserId, userId)) > 0;
    }

    @Override
    public Integer queryFavoriteProductCount(Long productId) {
        return Math.toIntExact(favoriteProductRepository.count(new LambdaQueryWrapper<Favorite>().eq(Favorite::getProductId, productId)));
    }
}
