package com.sankuai.shangou.seashop.user.shop.service;

import com.sankuai.shangou.seashop.user.thrift.shop.request.BatchQueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopInvoiceReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.QueryShopInvoiceResp;

/**
 * @author： liweisong
 * @create： 2023/11/29 9:02
 */
public interface ShopInvoiceQueryService {

    // 查看发票
    QueryShopInvoiceResp queryShopInvoice(QueryShopInvoiceReq queryShopInvoiceReq);

    /**
     * 批量查询发票设置
     * @param queryShopInvoiceReq 查询入参
     */
    QueryShopInvoiceListResp batchQueryInvoiceSetting(BatchQueryShopInvoiceReq queryShopInvoiceReq);
}
