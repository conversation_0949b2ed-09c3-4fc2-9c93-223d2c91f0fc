<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.hishop.himall</groupId>
        <artifactId>himall-base</artifactId>
        <version>1.0.3-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>seashop-base-user-shop</artifactId>
    <version>${base.version}</version>
    <packaging>jar</packaging>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>

        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-dao</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-user-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-trade-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-util</artifactId>
        </dependency>
        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-miniapp</artifactId>
            <version>4.6.0</version>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-user-account</artifactId>
        </dependency>
        <dependency>
            <groupId>com.hishop.himall</groupId>
            <artifactId>seashop-base-core</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>hishop-xxl-job-client-boot-starter</artifactId>
                    <groupId>com.hishop.starter</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.hishop.starter</groupId>
            <artifactId>hishop-s3-spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>com.hishop.starter</groupId>
            <artifactId>hishop-xxl-job-client-boot-starter</artifactId>
            <version>2.0.1-SNAPSHOT</version>
        </dependency>

    </dependencies>

</project>
