package com.sankuai.shangou.seashop.order.dao.core.repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.order.dao.core.domain.ProductComment;
import com.sankuai.shangou.seashop.order.dao.core.mapper.ProductCommentMapper;
import com.sankuai.shangou.seashop.order.dao.core.model.ReviewMarkModel;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/12/04 10:38
 */
@Repository
@Slf4j
public class ProductCommentRepository extends ServiceImpl<ProductCommentMapper, ProductComment> {

    /**
     * 根据商品评价id查询
     *
     * @param productCommentId 商品评价id
     * @return 商品评价
     */
    public ProductComment getByProductCommentId(Long productCommentId) {
        return getOne(new LambdaQueryWrapper<ProductComment>().eq(ProductComment::getProductCommentId, productCommentId).eq(ProductComment::getDeleted, Boolean.FALSE));
    }

    /**
     * 根据商品评价id批量查询
     *
     * @param productCommentIds 商品评价id的集合
     * @return 商品评价
     */
    public List<ProductComment> listByProductCommentIds(List<Long> productCommentIds) {
        if (CollectionUtils.isEmpty(productCommentIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<ProductComment>().in(ProductComment::getProductCommentId, ids).eq(ProductComment::getDeleted, Boolean.FALSE)), productCommentIds);
    }

    /**
     * 根据订单id查询
     *
     * @param orderId 订单id
     * @return 商品评价
     */
    public List<ProductComment> listByOrderId(String orderId) {
        return list(new LambdaQueryWrapper<ProductComment>().eq(ProductComment::getOrderId, orderId).eq(ProductComment::getDeleted, Boolean.FALSE));
    }

    /**
     * 根据订单id查询
     *
     * @param orderIdList 订单id
     * @return 商品评价
     */
    public List<ProductComment> listByOrderIdList(List<String> orderIdList) {
        return list(new LambdaQueryWrapper<ProductComment>().in(ProductComment::getOrderId, orderIdList).eq(ProductComment::getDeleted, Boolean.FALSE));
    }

    /**
     * 获取商品评分map
     *
     * @param productId 商品id
     * @return 商品评分map
     */
    public Map<Integer, Integer> getReviewMarkMap(Long productId) {
        List<ReviewMarkModel> dtoList = baseMapper.listReviewMark(productId);
        return dtoList.stream().collect(Collectors.toMap(ReviewMarkModel::getReviewMark, ReviewMarkModel::getCount, (k1, k2) -> k2));
    }

    /**
     * 统计追加评论数
     *
     * @param productId 商品id
     * @return 追加评论数
     */
    public Integer countAppendComment(Long productId) {
        return Math.toIntExact(count(new LambdaQueryWrapper<ProductComment>().
                eq(ProductComment::getProductId, productId)
                .isNotNull(ProductComment::getAppendDate)
                .eq(ProductComment::getHasHidden, Boolean.FALSE)
                .eq(ProductComment::getDeleted, Boolean.FALSE)
        ));
    }

    /**
     * 统计有图评价数
     *
     * @param productId 商品id
     * @return 有图评价数
     */
    public Integer countHasImage(Long productId) {
        return Math.toIntExact(count(new LambdaQueryWrapper<ProductComment>()
                .eq(ProductComment::getProductId, productId)
                .eq(ProductComment::getHasHidden, Boolean.FALSE)
                .eq(ProductComment::getDeleted, Boolean.FALSE)
                .and(wrapper -> wrapper.eq(ProductComment::getHasImage, Boolean.TRUE)
                        .or()
                        .eq(ProductComment::getAppendHasImage, Boolean.TRUE))));

    }

    /**
     * 统计待回复评价数
     *
     * @param shopId 店铺id
     * @return 待回复评价数
     */
    public Integer countWaitReply(Long shopId) {
        // 有待处理的评价或者追评(风控已经通过的)
        return Math.toIntExact(count(new LambdaQueryWrapper<ProductComment>()
                .eq(ProductComment::getShopId, shopId)
                .eq(ProductComment::getDeleted, Boolean.FALSE)
                .and(comment -> comment.and(re -> re.isNull(ProductComment::getReplyDate))
                        .or(ap -> ap.isNotNull(ProductComment::getAppendDate).isNull(ProductComment::getReplyAppendDate)))
        ));
    }

    public void removeByProductCommentIds(List<Long> productCommentIds) {
        if (CollectionUtils.isEmpty(productCommentIds)) {
            return;
        }

        productCommentIds = productCommentIds.stream().distinct().collect(Collectors.toList());
        MybatisUtil.executeBatch(ids -> remove(new LambdaQueryWrapper<ProductComment>().in(ProductComment::getProductCommentId, ids)), productCommentIds);
    }

    public ProductComment selectById(Long productCommentId) {
        return getOne(new LambdaQueryWrapper<ProductComment>().eq(ProductComment::getProductCommentId, productCommentId));
    }
}
