package com.sankuai.shangou.seashop.pay.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 汇付交互表
 * </p>
 *
 * <AUTHOR> @since 2024-04-29
 */
@TableName("pay_exchange_log")
public class ExchangeLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1:创建汇付支付单，2汇付支付成功回调
     */
    @TableField("type")
    private Integer type;

    /**
     * 方法
     */
    @TableField("method")
    private String method;

    /**
     * 接口请求入参
     */
    @TableField("param")
    private String param;

    /**
     * 接口返回返参
     */
    @TableField("result")
    private String result;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getParam() {
        return param;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public String getResult() {
        return result;
    }

    public void setResult(String result) {
        this.result = result;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Override
    public String toString() {
        return "ExchangeLog{" +
        "id=" + id +
        ", type=" + type +
        ", method=" + method +
        ", param=" + param +
        ", result=" + result +
        ", createTime=" + createTime +
        "}";
    }
}
