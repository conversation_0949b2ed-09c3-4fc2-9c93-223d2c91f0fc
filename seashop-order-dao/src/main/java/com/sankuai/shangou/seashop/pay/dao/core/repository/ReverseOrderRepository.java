package com.sankuai.shangou.seashop.pay.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ReverseOrder;
import com.sankuai.shangou.seashop.pay.dao.core.mapper.ReverseOrderMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@Repository
@Slf4j
public class ReverseOrderRepository extends ServiceImpl<ReverseOrderMapper, ReverseOrder> {

    /**
     * 根据退款订单号查询退款订单
     *
     * @param reverseId
     * @return
     */
    public ReverseOrder getByReverseId(String reverseId) {
        LambdaQueryWrapper<ReverseOrder> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ReverseOrder::getReverseId, reverseId);
        return this.getOne(wrapper);
    }

    /**
     * 根据退款订单类型查询退款中的订单
     *
     * @param reverseType
     * @return
     */
    public List<ReverseOrder> getReverseIngByType(Integer reverseType, Integer reverseState) {
        LambdaQueryWrapper<ReverseOrder> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ReverseOrder::getReverseType, reverseType);
        wrapper.eq(ReverseOrder::getReverseState, reverseState);
        wrapper.isNotNull(ReverseOrder::getChannelRefundId);
        return this.list(wrapper);
    }

    /**
     * 根据渠道支付ID和状态查询退款订单数
     */
    public int countByChannelPayIdAndStatus(String channelPayId, List<Integer> reverseStateList) {
        LambdaQueryWrapper<ReverseOrder> wrapper = new LambdaQueryWrapper();
        wrapper.eq(ReverseOrder::getChannelPayId, channelPayId);
        wrapper.in(ReverseOrder::getReverseState, reverseStateList);
        return Math.toIntExact(this.count(wrapper));
    }
}
