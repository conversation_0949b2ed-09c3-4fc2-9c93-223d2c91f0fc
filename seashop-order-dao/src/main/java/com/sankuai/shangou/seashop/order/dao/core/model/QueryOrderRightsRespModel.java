package com.sankuai.shangou.seashop.order.dao.core.model;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/22 10:05
 */
@Data
public class QueryOrderRightsRespModel implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 下单时间
     */
    private Date createTime;

    /**
     * 订单详情的商品主图地址
     */
    private List<Map<String,String>> imagePath;

}
