package com.sankuai.shangou.seashop.pay.dao.core.mapper;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单支付记录表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-11-20
 */
public interface OrderPayMapper extends EnhancedMapper<OrderPay> {

    void updateUnPaid(@Param("list") List<String> list,@Param("status")  Integer status);
}
