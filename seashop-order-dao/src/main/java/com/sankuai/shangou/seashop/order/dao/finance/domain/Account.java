package com.sankuai.shangou.seashop.order.dao.finance.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 财务结算记录表
 * </p>
 *
 * <AUTHOR> @since 2023-12-02
 */
@TableName("finance_account")
public class Account implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 出账日期
     */
    @TableField("account_date")
    private Date accountDate;

    /**
     * 开始时间
     */
    @TableField("start_date")
    private Date startDate;

    /**
     * 结束时间
     */
    @TableField("end_date")
    private Date endDate;

    /**
     * 枚举 0未结账，1已结账
     */
    @TableField("status")
    private Integer status;

    /**
     * 商品实付总额
     */
    @TableField("product_actual_paid_amount")
    private BigDecimal productActualPaidAmount;

    /**
     * 运费
     */
    @TableField("freight_amount")
    private BigDecimal freightAmount;

    /**
     * 佣金
     */
    @TableField("commission_amount")
    private BigDecimal commissionAmount;

    /**
     * 退还佣金
     */
    @TableField("refund_commission_amount")
    private BigDecimal refundCommissionAmount;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 预付款总额
     */
    @TableField("advance_payment_amount")
    private BigDecimal advancePaymentAmount;

    /**
     * 本期应结
     */
    @TableField("period_settlement")
    private BigDecimal periodSettlement;

    @TableField("remark")
    private String remark;

    /**
     * 支付渠道 1汇付天下
     */
    @TableField("payment_channel")
    private Integer paymentChannel;

    /**
     * 渠道手续费
     */
    @TableField("channel_amount")
    private BigDecimal channelAmount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Date getAccountDate() {
        return accountDate;
    }

    public void setAccountDate(Date accountDate) {
        this.accountDate = accountDate;
    }

    public Date getStartDate() {
        return startDate;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public Date getEndDate() {
        return endDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getProductActualPaidAmount() {
        return productActualPaidAmount;
    }

    public void setProductActualPaidAmount(BigDecimal productActualPaidAmount) {
        this.productActualPaidAmount = productActualPaidAmount;
    }

    public BigDecimal getFreightAmount() {
        return freightAmount;
    }

    public void setFreightAmount(BigDecimal freightAmount) {
        this.freightAmount = freightAmount;
    }

    public BigDecimal getCommissionAmount() {
        return commissionAmount;
    }

    public void setCommissionAmount(BigDecimal commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    public BigDecimal getRefundCommissionAmount() {
        return refundCommissionAmount;
    }

    public void setRefundCommissionAmount(BigDecimal refundCommissionAmount) {
        this.refundCommissionAmount = refundCommissionAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public BigDecimal getAdvancePaymentAmount() {
        return advancePaymentAmount;
    }

    public void setAdvancePaymentAmount(BigDecimal advancePaymentAmount) {
        this.advancePaymentAmount = advancePaymentAmount;
    }

    public BigDecimal getPeriodSettlement() {
        return periodSettlement;
    }

    public void setPeriodSettlement(BigDecimal periodSettlement) {
        this.periodSettlement = periodSettlement;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(Integer paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    public BigDecimal getChannelAmount() {
        return channelAmount;
    }

    public void setChannelAmount(BigDecimal channelAmount) {
        this.channelAmount = channelAmount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "Account{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", shopName=" + shopName +
        ", accountDate=" + accountDate +
        ", startDate=" + startDate +
        ", endDate=" + endDate +
        ", status=" + status +
        ", productActualPaidAmount=" + productActualPaidAmount +
        ", freightAmount=" + freightAmount +
        ", commissionAmount=" + commissionAmount +
        ", refundCommissionAmount=" + refundCommissionAmount +
        ", refundAmount=" + refundAmount +
        ", advancePaymentAmount=" + advancePaymentAmount +
        ", periodSettlement=" + periodSettlement +
        ", remark=" + remark +
        ", paymentChannel=" + paymentChannel +
        ", channelAmount=" + channelAmount +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
