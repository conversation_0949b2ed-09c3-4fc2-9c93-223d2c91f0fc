package com.sankuai.shangou.seashop.order.dao.finance.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 财务结算明细表
 * </p>
 *
 * <AUTHOR> @since 2023-12-06
 */
@TableName("finance_account_detail")
public class AccountDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 结算记录外键
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    @TableField("shop_name")
    private String shopName;

    /**
     * 完成日期
     */
    @TableField("date")
    private Date date;

    /**
     * 订单下单日期
     */
    @TableField("order_date")
    private Date orderDate;

    /**
     * 付款日期
     */
    @TableField("pay_date")
    private Date payDate;

    /**
     * 订单完成日期
     */
    @TableField("order_finish_date")
    private Date orderFinishDate;

    /**
     * 枚举 完成订单1，退订单0
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 订单金额
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;

    /**
     * 商品实付总额
     */
    @TableField("product_actual_paid_amount")
    private BigDecimal productActualPaidAmount;

    /**
     * 运费金额
     */
    @TableField("freight_amount")
    private BigDecimal freightAmount;

    /**
     * 税费
     */
    @TableField("tax_amount")
    private BigDecimal taxAmount;

    /**
     * 佣金
     */
    @TableField("commission_amount")
    private BigDecimal commissionAmount;

    /**
     * 退款金额
     */
    @TableField("refund_total_amount")
    private BigDecimal refundTotalAmount;

    /**
     * 退还佣金
     */
    @TableField("refund_commis_amount")
    private BigDecimal refundCommisAmount;

    /**
     * 退单的日期集合以;分隔
     */
    @TableField("order_refunds_dates")
    private String orderRefundsDates;

    /**
     * 结算金额
     */
    @TableField("settlement_amount")
    private BigDecimal settlementAmount;

    /**
     * 支付类型名称
     */
    @TableField("payment_type_name")
    private String paymentTypeName;

    /**
     * 平台优惠券抵扣金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 平台优惠券退还金额
     */
    @TableField("discount_amount_return")
    private BigDecimal discountAmountReturn;

    /**
     * 支付渠道 1汇付天下
     */
    @TableField("payment_channel")
    private Integer paymentChannel;

    /**
     * 支付方式。1：支付宝扫码；2：支付宝H5；3：微信小程序；4：微信H5；5：企业网银；6：个人网银
     */
    @TableField("payment_type")
    private Integer paymentType;

    /**
     * 渠道手续费
     */
    @TableField("channel_amount")
    private BigDecimal channelAmount;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Date getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(Date orderDate) {
        this.orderDate = orderDate;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public Date getOrderFinishDate() {
        return orderFinishDate;
    }

    public void setOrderFinishDate(Date orderFinishDate) {
        this.orderFinishDate = orderFinishDate;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getProductActualPaidAmount() {
        return productActualPaidAmount;
    }

    public void setProductActualPaidAmount(BigDecimal productActualPaidAmount) {
        this.productActualPaidAmount = productActualPaidAmount;
    }

    public BigDecimal getFreightAmount() {
        return freightAmount;
    }

    public void setFreightAmount(BigDecimal freightAmount) {
        this.freightAmount = freightAmount;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getCommissionAmount() {
        return commissionAmount;
    }

    public void setCommissionAmount(BigDecimal commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    public BigDecimal getRefundTotalAmount() {
        return refundTotalAmount;
    }

    public void setRefundTotalAmount(BigDecimal refundTotalAmount) {
        this.refundTotalAmount = refundTotalAmount;
    }

    public BigDecimal getRefundCommisAmount() {
        return refundCommisAmount;
    }

    public void setRefundCommisAmount(BigDecimal refundCommisAmount) {
        this.refundCommisAmount = refundCommisAmount;
    }

    public String getOrderRefundsDates() {
        return orderRefundsDates;
    }

    public void setOrderRefundsDates(String orderRefundsDates) {
        this.orderRefundsDates = orderRefundsDates;
    }

    public BigDecimal getSettlementAmount() {
        return settlementAmount;
    }

    public void setSettlementAmount(BigDecimal settlementAmount) {
        this.settlementAmount = settlementAmount;
    }

    public String getPaymentTypeName() {
        return paymentTypeName;
    }

    public void setPaymentTypeName(String paymentTypeName) {
        this.paymentTypeName = paymentTypeName;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getDiscountAmountReturn() {
        return discountAmountReturn;
    }

    public void setDiscountAmountReturn(BigDecimal discountAmountReturn) {
        this.discountAmountReturn = discountAmountReturn;
    }

    public Integer getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(Integer paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public BigDecimal getChannelAmount() {
        return channelAmount;
    }

    public void setChannelAmount(BigDecimal channelAmount) {
        this.channelAmount = channelAmount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "AccountDetail{" +
        "id=" + id +
        ", accountId=" + accountId +
        ", shopId=" + shopId +
        ", shopName=" + shopName +
        ", date=" + date +
        ", orderDate=" + orderDate +
        ", payDate=" + payDate +
        ", orderFinishDate=" + orderFinishDate +
        ", orderType=" + orderType +
        ", orderId=" + orderId +
        ", orderAmount=" + orderAmount +
        ", productActualPaidAmount=" + productActualPaidAmount +
        ", freightAmount=" + freightAmount +
        ", taxAmount=" + taxAmount +
        ", commissionAmount=" + commissionAmount +
        ", refundTotalAmount=" + refundTotalAmount +
        ", refundCommisAmount=" + refundCommisAmount +
        ", orderRefundsDates=" + orderRefundsDates +
        ", settlementAmount=" + settlementAmount +
        ", paymentTypeName=" + paymentTypeName +
        ", discountAmount=" + discountAmount +
        ", discountAmountReturn=" + discountAmountReturn +
        ", paymentChannel=" + paymentChannel +
        ", paymentType=" + paymentType +
        ", channelAmount=" + channelAmount +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
