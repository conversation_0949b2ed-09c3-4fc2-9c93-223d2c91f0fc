package com.sankuai.shangou.seashop.order.dao.finance.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 财务中间表
 * </p>
 *
 * <AUTHOR> @since 2023-12-02
 */
@TableName("finance_finance")
public class Finance implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单号
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 支付单号，保证金订单号和支付单号一致
     */
    @TableField("pay_id")
    private String payId;

    /**
     * 汇付支付流水号
     */
    @TableField("adapay_id")
    private String adapayId;

    /**
     * 交易类型 [Description("支付")]Pay = 1,[Description("退款")]Refund =  2,[Description("扣款")]Deduction = 3
     */
    @TableField("type")
    private Integer type;

    /**
     * 创建时间
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 会员ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 会员名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 交易流水号
     */
    @TableField("transaction_id")
    private String transactionId;

    /**
     * 金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 退款类型,1:订单完成前退款，2：订单完成后退款
     */
    @TableField("refund_type")
    private Integer refundType;

    /**
     * 运费
     */
    @TableField("freight")
    private BigDecimal freight;

    /**
     * 原价
     */
    @TableField("product_amount")
    private BigDecimal productAmount;

    /**
     * 针对该订单的优惠金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 平台优惠券抵扣金额
     */
    @TableField("plat_discount_amount")
    private BigDecimal platDiscountAmount;

    /**
     * 满额减金额
     */
    @TableField("full_discount")
    private BigDecimal fullDiscount;

    /**
     * 满减活动优惠金额
     */
    @TableField("money_off")
    private BigDecimal moneyOff;

    /**
     * 积分优惠金额
     */
    @TableField("integral_discount")
    private BigDecimal integralDiscount;

    /**
     * 汇付手续费
     */
    @TableField("service_amount")
    private BigDecimal serviceAmount;

    /**
     * 供应商结算金额
     */
    @TableField("settlement_amount")
    private BigDecimal settlementAmount;

    /**
     * 佣金
     */
    @TableField("commission_amount")
    private BigDecimal commissionAmount;

    /**
     * 扣款类型，1：罚款；2：代收代付；
     */
    @TableField("deduction_type")
    private Integer deductionType;

    /**
     * 扣款手续费
     */
    @TableField("service_fee")
    private BigDecimal serviceFee;

    /**
     * 退款Id
     */
    @TableField("order_refund_id")
    private Long orderRefundId;

    /**
     * 订单实付金额
     */
    @TableField("actual_pay_amount")
    private BigDecimal actualPayAmount;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getAdapayId() {
        return adapayId;
    }

    public void setAdapayId(String adapayId) {
        this.adapayId = adapayId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public Integer getRefundType() {
        return refundType;
    }

    public void setRefundType(Integer refundType) {
        this.refundType = refundType;
    }

    public BigDecimal getFreight() {
        return freight;
    }

    public void setFreight(BigDecimal freight) {
        this.freight = freight;
    }

    public BigDecimal getProductAmount() {
        return productAmount;
    }

    public void setProductAmount(BigDecimal productAmount) {
        this.productAmount = productAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getPlatDiscountAmount() {
        return platDiscountAmount;
    }

    public void setPlatDiscountAmount(BigDecimal platDiscountAmount) {
        this.platDiscountAmount = platDiscountAmount;
    }

    public BigDecimal getFullDiscount() {
        return fullDiscount;
    }

    public void setFullDiscount(BigDecimal fullDiscount) {
        this.fullDiscount = fullDiscount;
    }

    public BigDecimal getMoneyOff() {
        return moneyOff;
    }

    public void setMoneyOff(BigDecimal moneyOff) {
        this.moneyOff = moneyOff;
    }

    public BigDecimal getIntegralDiscount() {
        return integralDiscount;
    }

    public void setIntegralDiscount(BigDecimal integralDiscount) {
        this.integralDiscount = integralDiscount;
    }

    public BigDecimal getServiceAmount() {
        return serviceAmount;
    }

    public void setServiceAmount(BigDecimal serviceAmount) {
        this.serviceAmount = serviceAmount;
    }

    public BigDecimal getSettlementAmount() {
        return settlementAmount;
    }

    public void setSettlementAmount(BigDecimal settlementAmount) {
        this.settlementAmount = settlementAmount;
    }

    public BigDecimal getCommissionAmount() {
        return commissionAmount;
    }

    public void setCommissionAmount(BigDecimal commissionAmount) {
        this.commissionAmount = commissionAmount;
    }

    public Integer getDeductionType() {
        return deductionType;
    }

    public void setDeductionType(Integer deductionType) {
        this.deductionType = deductionType;
    }

    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    public Long getOrderRefundId() {
        return orderRefundId;
    }

    public void setOrderRefundId(Long orderRefundId) {
        this.orderRefundId = orderRefundId;
    }

    public BigDecimal getActualPayAmount() {
        return actualPayAmount;
    }

    public void setActualPayAmount(BigDecimal actualPayAmount) {
        this.actualPayAmount = actualPayAmount;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "Finance{" +
                "id=" + id +
                ", orderId=" + orderId +
                ", payId=" + payId +
                ", adapayId=" + adapayId +
                ", type=" + type +
                ", createDate=" + createDate +
                ", shopId=" + shopId +
                ", shopName=" + shopName +
                ", userId=" + userId +
                ", userName=" + userName +
                ", transactionId=" + transactionId +
                ", totalAmount=" + totalAmount +
                ", refundType=" + refundType +
                ", freight=" + freight +
                ", productAmount=" + productAmount +
                ", discountAmount=" + discountAmount +
                ", platDiscountAmount=" + platDiscountAmount +
                ", fullDiscount=" + fullDiscount +
                ", moneyOff=" + moneyOff +
                ", integralDiscount=" + integralDiscount +
                ", serviceAmount=" + serviceAmount +
                ", settlementAmount=" + settlementAmount +
                ", commissionAmount=" + commissionAmount +
                ", deductionType=" + deductionType +
                ", serviceFee=" + serviceFee +
                ", orderRefundId=" + orderRefundId +
                ", actualPayAmount=" + actualPayAmount +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
