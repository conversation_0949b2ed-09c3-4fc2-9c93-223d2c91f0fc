package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 订单商品评论图片表
 * </p>
 *
 * <AUTHOR> @since 2023-12-04
 */
@TableName("order_product_comment_image")
public class ProductCommentImage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 评论图片
     */
    @TableField("comment_image")
    private String commentImage;

    /**
     * 商品评论id(order_product_comment.product_comment_id)
     */
    @TableField("product_comment_id")
    private Long productCommentId;

    /**
     * 评论类型（首次评论/追加评论）
     */
    @TableField("comment_type")
    private Integer commentType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCommentImage() {
        return commentImage;
    }

    public void setCommentImage(String commentImage) {
        this.commentImage = commentImage;
    }

    public Long getProductCommentId() {
        return productCommentId;
    }

    public void setProductCommentId(Long productCommentId) {
        this.productCommentId = productCommentId;
    }

    public Integer getCommentType() {
        return commentType;
    }

    public void setCommentType(Integer commentType) {
        this.commentType = commentType;
    }


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ProductCommentImage{" +
        "id=" + id +
        ", commentImage=" + commentImage +
        ", productCommentId=" + productCommentId +
        ", commentType=" + commentType +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
