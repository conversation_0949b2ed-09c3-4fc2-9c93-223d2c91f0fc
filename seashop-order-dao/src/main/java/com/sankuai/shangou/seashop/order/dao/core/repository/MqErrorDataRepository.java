package com.sankuai.shangou.seashop.order.dao.core.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.core.domain.MqErrorData;
import com.sankuai.shangou.seashop.order.dao.core.mapper.MqErrorDataMapper;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonMqErrorDataQueryParamPo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class MqErrorDataRepository extends ServiceImpl<MqErrorDataMapper, MqErrorData> {

    public MqErrorData getByBizTypeAndBizNo(Integer bizType, String bizNo) {
        LambdaQueryWrapper<MqErrorData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MqErrorData::getBizType, bizType)
                .eq(MqErrorData::getBizNo, bizNo);
        return super.getOne(wrapper);
    }

    public List<MqErrorData> getByCondition(CommonMqErrorDataQueryParamPo param) {
        LambdaQueryWrapper<MqErrorData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(param.getIdEq() != null, MqErrorData::getId, param.getIdEq())
                .eq(param.getBizTypeEq() != null, MqErrorData::getBizType, param.getBizTypeEq())
                .eq(param.getBizNoEq() != null, MqErrorData::getBizNo, param.getBizNoEq());
        return super.list(wrapper);
    }

    public void updateHandleStatusById(Long id, Integer handleStatus, String reExeErrMsg) {
        LambdaUpdateWrapper<MqErrorData> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MqErrorData::getId, id)
                .set(MqErrorData::getHandleStatus, handleStatus)
                .set(MqErrorData::getReExeErrorMsg, StrUtil.nullToEmpty(reExeErrMsg));
        super.update(wrapper);
    }


}
