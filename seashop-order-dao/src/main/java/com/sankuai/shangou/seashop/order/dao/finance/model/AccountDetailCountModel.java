package com.sankuai.shangou.seashop.order.dao.finance.model;

import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2024/3/25/025
 * @description: 明细统计
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class AccountDetailCountModel {

    private Long shopId;

    private Long detailId;

    private BigDecimal settleAmount;

    private BigDecimal commissionAmount;

}
