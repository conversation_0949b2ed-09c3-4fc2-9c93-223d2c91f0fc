package com.sankuai.shangou.seashop.order.dao.finance.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 保证金明细表
 * </p>
 *
 * <AUTHOR> @since 2023-11-28
 */
@TableName("finance_cash_deposit_detail")
public class CashDepositDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 保证金表id
     */
    @TableField("cash_deposit_id")
    private Long cashDepositId;

    /**
     * 时间
     */
    @TableField("add_date")
    private Date addDate;

    /**
     * 金额
     */
    @TableField("balance")
    private BigDecimal balance;

    /**
     * 操作类型
     */
    @TableField("operator")
    private String operator;

    /**
     * 说明
     */
    @TableField("description")
    private String description;

    /**
     * 充值类型（银联、支付宝之类的）
     */
    @TableField("recharge_way")
    private Integer rechargeWay;

    /**
     * 类型；1，付款；2，扣款；3，退款
     */
    @TableField("operator_type")
    private Integer operatorType;

    /**
     * 渠道支付单号
     */
    @TableField("channel_order_id")
    private String channelOrderId;

    /**
     * 平台扣款金额
     */
    @TableField("platform_deduction")
    private BigDecimal platformDeduction;

    /**
     * 冻结金额
     */
    @TableField("forzen_amount")
    private BigDecimal forzenAmount;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 交易流水号
     */
    @TableField("trade_no")
    private String tradeNo;

    /**
     * 渠道ID
     */
    @TableField("channel_id")
    private String channelId;

    /**
     * 扣款类型，1：罚款；2：代收代付；
     */
    @TableField("deduction_type")
    private Integer deductionType;

    /**
     * 扣款手续费
     */
    @TableField("deduction_fee")
    private BigDecimal deductionFee;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCashDepositId() {
        return cashDepositId;
    }

    public void setCashDepositId(Long cashDepositId) {
        this.cashDepositId = cashDepositId;
    }

    public Date getAddDate() {
        return addDate;
    }

    public void setAddDate(Date addDate) {
        this.addDate = addDate;
    }

    public BigDecimal getBalance() {
        return balance;
    }

    public void setBalance(BigDecimal balance) {
        this.balance = balance;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getRechargeWay() {
        return rechargeWay;
    }

    public void setRechargeWay(Integer rechargeWay) {
        this.rechargeWay = rechargeWay;
    }

    public Integer getOperatorType() {
        return operatorType;
    }

    public void setOperatorType(Integer operatorType) {
        this.operatorType = operatorType;
    }

    public String getChannelOrderId() {
        return channelOrderId;
    }

    public void setChannelOrderId(String channelOrderId) {
        this.channelOrderId = channelOrderId;
    }

    public BigDecimal getPlatformDeduction() {
        return platformDeduction;
    }

    public void setPlatformDeduction(BigDecimal platformDeduction) {
        this.platformDeduction = platformDeduction;
    }

    public BigDecimal getForzenAmount() {
        return forzenAmount;
    }

    public void setForzenAmount(BigDecimal forzenAmount) {
        this.forzenAmount = forzenAmount;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getTradeNo() {
        return tradeNo;
    }

    public void setTradeNo(String tradeNo) {
        this.tradeNo = tradeNo;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public Integer getDeductionType() {
        return deductionType;
    }

    public void setDeductionType(Integer deductionType) {
        this.deductionType = deductionType;
    }

    public BigDecimal getDeductionFee() {
        return deductionFee;
    }

    public void setDeductionFee(BigDecimal deductionFee) {
        this.deductionFee = deductionFee;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CashDepositDetail{" +
                "id=" + id +
                ", cashDepositId=" + cashDepositId +
                ", addDate=" + addDate +
                ", balance=" + balance +
                ", operator=" + operator +
                ", description=" + description +
                ", rechargeWay=" + rechargeWay +
                ", operatorType=" + operatorType +
                ", channelOrderId=" + channelOrderId +
                ", platformDeduction=" + platformDeduction +
                ", forzenAmount=" + forzenAmount +
                ", refundAmount=" + refundAmount +
                ", tradeNo=" + tradeNo +
                ", channelId=" + channelId +
                ", deductionType=" + deductionType +
                ", deductionFee=" + deductionFee +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
