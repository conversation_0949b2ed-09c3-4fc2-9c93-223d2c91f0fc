package com.sankuai.shangou.seashop.order.dao.core.domain;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Getter;
import lombok.Setter;

/**
 * 三方推送事件记录
 *
 * <AUTHOR>
 */
@Getter
@Setter
@TableName("third_push_event")
public class ThirdPushEvent {
    /**
     * 主键ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 推送编号 可以为订单id或者 productId
     */
    @TableField("send_code")
    private String sendCode;
    /**
     * 事件类型 0:订单事件 1:商品事件 2:退货单
     */
    @TableField("event_type")
    private Integer eventType;
    /**
     * 发送目标 1：旺店通 2：聚水潭 3：网店管家 4：吉客云
     */
    @TableField("send_target")
    private Integer sendTarget;
    /**
     * 推送状态 0:待推送 1：推送成功 2：推送失败，3：推送中
     */
    @TableField("send_state")
    private Integer sendState;
    /**
     * 事件内容
     */
    @TableField("event_body")
    private String eventBody;
    /**
     * 推送返回的错误消息
     */
    @TableField("send_msg")
    private String sendMsg;
    /**
     * 数据核验 0/1 默认1
     */
    @TableField("valid")
    private Integer valid;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
}
