package com.sankuai.shangou.seashop.order.dao.core.po;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;

/**
 * 通用的订单售后查询参数
 * <p>Notice: 主要用于一些定时任务等非正常业务场景，正常业务可能越权慎用</p>
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class CommonRefundQueryParamPo {

    /**
     * 供应商审核状态 =
     */
    private Integer sellerStatusEq;
    /**
     * 平台审核状态 =
     */
    private Integer platformStatusEq;
    /**
     * 申请日期 <
     * eg: applyDate < now()
     */
    private Date applyDateLt;
    /**
     * 是否取消 =
     */
    private Boolean isCancelEq;
    /**
     * 供应商审核日期 <
     */
    private Date sellerAuditDateLt;
    /**
     * 买家寄货日期 <
     */
    private Date buyerDeliveryDateLt;
    /**
     * 售后提交时间 <
     */
    private Date refundPostTimeDateLt;
    /**
     * 主键ID：>=
     */
    private Long idGe;
    /**
     * 主键ID：<=
     */
    private Long idLe;
    /**
     * 更新时间：>=
     */
    private Date updateTimeGe;
    /**
     * 更新时间：<=
     */
    private Date updateTimeLe;

}
