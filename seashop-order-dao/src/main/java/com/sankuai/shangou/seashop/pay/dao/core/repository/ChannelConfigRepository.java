package com.sankuai.shangou.seashop.pay.dao.core.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.pay.dao.core.domain.ChannelConfig;
import com.sankuai.shangou.seashop.pay.dao.core.mapper.ChannelConfigMapper;
import com.sankuai.shangou.seashop.pay.dao.core.model.AliPayConfigModel;
import com.sankuai.shangou.seashop.pay.dao.core.model.ChannelConfigParamModel;
import com.sankuai.shangou.seashop.pay.dao.core.model.PayOpenStateModel;
import com.sankuai.shangou.seashop.pay.dao.core.model.WxPayConfigModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@Repository
@Slf4j
public class ChannelConfigRepository extends ServiceImpl<ChannelConfigMapper, ChannelConfig> {

    public List<ChannelConfig> queryByChannelAndKey(ChannelConfigParamModel param) {
        LambdaQueryWrapper<ChannelConfig> queryWrapper = new LambdaQueryWrapper<>();
        if (null != param.getPaymentChannel()) {
            queryWrapper.eq(ChannelConfig::getPaymentChannel, param.getPaymentChannel());
        }
        if (StrUtil.isNotBlank(param.getConfigKey())) {
            queryWrapper.eq(ChannelConfig::getConfigKey, param.getConfigKey());
        }
        if (CollUtil.isNotEmpty(param.getConfigKeyList())) {
            queryWrapper.in(ChannelConfig::getConfigKey, param.getConfigKeyList());
        }
        return baseMapper.selectList(queryWrapper);
    }

    public WxPayConfigModel getWxPayConfig() {
        return baseMapper.getWxPayConfig();
    }

    public AliPayConfigModel getAliPayConfig() {
        return baseMapper.getAliPayConfig();
    }

    public PayOpenStateModel getPayOpenState() {
        return baseMapper.getPayOpenState();
    }

    public void updateValue(Integer paymentChannel, String configKey, String configValue) {
        LambdaQueryWrapper<ChannelConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ChannelConfig::getPaymentChannel, paymentChannel);
        queryWrapper.eq(ChannelConfig::getConfigKey, configKey);
        ChannelConfig channelConfig = new ChannelConfig();
        channelConfig.setConfigValue(configValue);
        baseMapper.update(channelConfig, queryWrapper);
    }

    public void updateNonNullValue(Integer paymentChannel, String configKey, Object configValue) {
        if (configValue == null) {
            return;
        }
        updateValue(paymentChannel, configKey, configValue.toString());
    }

}
