package com.sankuai.shangou.seashop.order.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 订单商品评论表
 * </p>
 *
 * <AUTHOR> @since 2024-01-08
 */
@TableName("order_product_comment")
public class ProductComment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商品评论id(美团id组件生成, 与其他表的关联使用该字段)
     */
    @TableField("product_comment_id")
    private Long productCommentId;

    /**
     * 订单id, 对应order.order_id
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 订单详细id，对应order_item.id
     */
    @TableField("sub_order_id")
    private Long subOrderId;

    /**
     * 商品id
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * 规格1别名
     */
    @TableField("spec1_alias")
    private String spec1Alias;

    /**
     * 规格2别名
     */
    @TableField("spec2_alias")
    private String spec2Alias;

    /**
     * 规格3别名
     */
    @TableField("spec3_alias")
    private String spec3Alias;

    /**
     * 规格1名称
     */
    @TableField("spec1_value")
    private String spec1Value;

    /**
     * 规格2名称
     */
    @TableField("spec2_value")
    private String spec2Value;

    /**
     * 规格3名称
     */
    @TableField("spec3_value")
    private String spec3Value;

    /**
     * 缩略图
     */
    @TableField("thumbnails_url")
    private String thumbnailsUrl;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 商家id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 商家名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 商家email
     */
    @TableField("email")
    private String email;

    /**
     * 商家手机号
     */
    @TableField("user_mobile")
    private String userMobile;

    /**
     * 评价内容
     */
    @TableField("review_content")
    private String reviewContent;

    /**
     * 评价日期
     */
    @TableField("review_date")
    private Date reviewDate;

    /**
     * 评分
     */
    @TableField("review_mark")
    private Integer reviewMark;

    /**
     * 回复内容
     */
    @TableField("reply_content")
    private String replyContent;

    /**
     * 回复日期
     */
    @TableField("reply_date")
    private Date replyDate;

    /**
     * 是否回复了图片
     */
    @TableField("has_image")
    private Boolean hasImage;

    /**
     * 追加内容
     */
    @TableField("append_content")
    private String appendContent;

    /**
     * 追加时间
     */
    @TableField("append_date")
    private Date appendDate;

    /**
     * 是否追加了图片
     */
    @TableField("append_has_image")
    private Boolean appendHasImage;

    /**
     * 追加评论回复
     */
    @TableField("reply_append_content")
    private String replyAppendContent;

    /**
     * 追加评论回复时间
     */
    @TableField("reply_append_date")
    private Date replyAppendDate;

    /**
     * 是否隐藏(前端不显示)
     */
    @TableField("has_hidden")
    private Boolean hasHidden;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 规格ID
     */
    @TableField("sku_id")
    private String skuId;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Boolean deleted;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getProductCommentId() {
        return productCommentId;
    }

    public void setProductCommentId(Long productCommentId) {
        this.productCommentId = productCommentId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Long getSubOrderId() {
        return subOrderId;
    }

    public void setSubOrderId(Long subOrderId) {
        this.subOrderId = subOrderId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSpec1Alias() {
        return spec1Alias;
    }

    public void setSpec1Alias(String spec1Alias) {
        this.spec1Alias = spec1Alias;
    }

    public String getSpec2Alias() {
        return spec2Alias;
    }

    public void setSpec2Alias(String spec2Alias) {
        this.spec2Alias = spec2Alias;
    }

    public String getSpec3Alias() {
        return spec3Alias;
    }

    public void setSpec3Alias(String spec3Alias) {
        this.spec3Alias = spec3Alias;
    }

    public String getSpec1Value() {
        return spec1Value;
    }

    public void setSpec1Value(String spec1Value) {
        this.spec1Value = spec1Value;
    }

    public String getSpec2Value() {
        return spec2Value;
    }

    public void setSpec2Value(String spec2Value) {
        this.spec2Value = spec2Value;
    }

    public String getSpec3Value() {
        return spec3Value;
    }

    public void setSpec3Value(String spec3Value) {
        this.spec3Value = spec3Value;
    }

    public String getThumbnailsUrl() {
        return thumbnailsUrl;
    }

    public void setThumbnailsUrl(String thumbnailsUrl) {
        this.thumbnailsUrl = thumbnailsUrl;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getUserMobile() {
        return userMobile;
    }

    public void setUserMobile(String userMobile) {
        this.userMobile = userMobile;
    }

    public String getReviewContent() {
        return reviewContent;
    }

    public void setReviewContent(String reviewContent) {
        this.reviewContent = reviewContent;
    }

    public Date getReviewDate() {
        return reviewDate;
    }

    public void setReviewDate(Date reviewDate) {
        this.reviewDate = reviewDate;
    }

    public Integer getReviewMark() {
        return reviewMark;
    }

    public void setReviewMark(Integer reviewMark) {
        this.reviewMark = reviewMark;
    }

    public String getReplyContent() {
        return replyContent;
    }

    public void setReplyContent(String replyContent) {
        this.replyContent = replyContent;
    }

    public Date getReplyDate() {
        return replyDate;
    }

    public void setReplyDate(Date replyDate) {
        this.replyDate = replyDate;
    }

    public Boolean getHasImage() {
        return hasImage;
    }

    public void setHasImage(Boolean hasImage) {
        this.hasImage = hasImage;
    }

    public String getAppendContent() {
        return appendContent;
    }

    public void setAppendContent(String appendContent) {
        this.appendContent = appendContent;
    }

    public Date getAppendDate() {
        return appendDate;
    }

    public void setAppendDate(Date appendDate) {
        this.appendDate = appendDate;
    }

    public Boolean getAppendHasImage() {
        return appendHasImage;
    }

    public void setAppendHasImage(Boolean appendHasImage) {
        this.appendHasImage = appendHasImage;
    }

    public String getReplyAppendContent() {
        return replyAppendContent;
    }

    public void setReplyAppendContent(String replyAppendContent) {
        this.replyAppendContent = replyAppendContent;
    }

    public Date getReplyAppendDate() {
        return replyAppendDate;
    }

    public void setReplyAppendDate(Date replyAppendDate) {
        this.replyAppendDate = replyAppendDate;
    }

    public Boolean getHasHidden() {
        return hasHidden;
    }

    public void setHasHidden(Boolean hasHidden) {
        this.hasHidden = hasHidden;
    }


    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    @Override
    public String toString() {
        return "ProductComment{" +
        "id=" + id +
        ", productCommentId=" + productCommentId +
        ", orderId=" + orderId +
        ", subOrderId=" + subOrderId +
        ", productId=" + productId +
        ", productName=" + productName +
        ", spec1Alias=" + spec1Alias +
        ", spec2Alias=" + spec2Alias +
        ", spec3Alias=" + spec3Alias +
        ", spec1Value=" + spec1Value +
        ", spec2Value=" + spec2Value +
        ", spec3Value=" + spec3Value +
        ", thumbnailsUrl=" + thumbnailsUrl +
        ", shopId=" + shopId +
        ", shopName=" + shopName +
        ", userId=" + userId +
        ", userName=" + userName +
        ", email=" + email +
        ", userMobile=" + userMobile +
        ", reviewContent=" + reviewContent +
        ", reviewDate=" + reviewDate +
        ", reviewMark=" + reviewMark +
        ", replyContent=" + replyContent +
        ", replyDate=" + replyDate +
        ", hasImage=" + hasImage +
        ", appendContent=" + appendContent +
        ", appendDate=" + appendDate +
        ", appendHasImage=" + appendHasImage +
        ", replyAppendContent=" + replyAppendContent +
        ", replyAppendDate=" + replyAppendDate +
        ", hasHidden=" + hasHidden +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", skuId=" + skuId +
        ", deleted=" + deleted +
        "}";
    }
}
