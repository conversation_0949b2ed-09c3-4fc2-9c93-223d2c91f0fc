package com.sankuai.shangou.seashop.order.dao.core.po;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 通用的订单支付记录查询参数
 * <p>Notice: 主要用于一些定时任务等非正常业务场景，正常业务可能越权慎用</p>
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class CommonPayRecordQueryParamBo {

    /**
     * 支付状态：=
     * 0：关闭；1：支付中；2：支付成功；3：支付失败
     */
    private Integer payStatusEq;
    /**
     * 订单ID：in
     */
    private List<String> orderIdListIn;
    /**
     * 支付单号，汇付返回的：in
     */
    private List<String> payNoListIn;
    /**
     * 外部交易单号，第三方返回：in
     */
    private List<String> outTransIdList;

}
