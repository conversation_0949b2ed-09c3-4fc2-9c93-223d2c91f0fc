package com.sankuai.shangou.seashop.order.dao.core.po;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 通用的查询参数
 * <p>Notice: 主要用于一些定时任务等非正常业务场景，正常业务可能越权慎用</p>
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class CommonMqErrorDataQueryParamPo {

    /**
     * 主键id =
     */
    private Long idEq;
    /**
     * 业务类型 =
     */
    private Integer bizTypeEq;
    /**
     * 业务编码 =
     */
    private String bizNoEq;
    /**
     * 处理状态 in
     */
    private List<Integer> statusIn;

}
