package com.sankuai.shangou.seashop.order.dao.core.mapper;

import com.sankuai.shangou.seashop.order.dao.core.domain.OrderComplaint;
import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.order.dao.core.model.QueryOrderRightsReqModel;
import com.sankuai.shangou.seashop.order.dao.core.model.QueryOrderRightsRespModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 订单投诉表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
public interface OrderComplaintMapper extends EnhancedMapper<OrderComplaint> {

    List<QueryOrderRightsRespModel> pageQueryOrderRights(@Param("req") QueryOrderRightsReqModel req);

}
