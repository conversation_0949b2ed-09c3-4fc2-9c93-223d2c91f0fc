package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 订单服务中MQ消息处理异常的数据
 * </p>
 *
 * <AUTHOR> @since 2024-04-07
 */
@TableName("order_mq_error_data")
public class MqErrorData implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务类型。1：订单状态变更；2：支付回调；3：售后状态变更；4：售后回调
     */
    @TableField("biz_type")
    private Integer bizType;

    /**
     * 业务类型描述
     */
    @TableField("biz_type_desc")
    private String bizTypeDesc;

    /**
     * 业务编号，业务层面控制业务类型下编号唯一
     */
    @TableField("biz_no")
    private String bizNo;

    /**
     * MQ消息ID
     */
    @TableField("message_id")
    private String messageId;

    /**
     * MQ处理时的traceId，用于排查
     */
    @TableField("trace_id")
    private String traceId;

    /**
     * 异常信息
     */
    @TableField("error_message")
    private String errorMessage;

    /**
     * MQ消息内容。注意长度
     */
    @TableField("data_content")
    private String dataContent;

    /**
     * 处理状态。1：待处理；2：处理中；3：已处理；4：已取消；5：执行失败
     */
    @TableField("handle_status")
    private Integer handleStatus;
    /**
     * 重新执行时的异常内容。handle_status=5 时可能有值
     */
    @TableField("re_exe_error_msg")
    private String reExeErrorMsg;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public String getBizTypeDesc() {
        return bizTypeDesc;
    }

    public void setBizTypeDesc(String bizTypeDesc) {
        this.bizTypeDesc = bizTypeDesc;
    }

    public String getBizNo() {
        return bizNo;
    }

    public void setBizNo(String bizNo) {
        this.bizNo = bizNo;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public String getDataContent() {
        return dataContent;
    }

    public void setDataContent(String dataContent) {
        this.dataContent = dataContent;
    }

    public Integer getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(Integer handleStatus) {
        this.handleStatus = handleStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getReExeErrorMsg() {
        return reExeErrorMsg;
    }

    public void setReExeErrorMsg(String reExeErrorMsg) {
        this.reExeErrorMsg = reExeErrorMsg;
    }

    @Override
    public String toString() {
        return "MqErrorData{" +
        "id=" + id +
        ", bizType=" + bizType +
        ", bizTypeDesc=" + bizTypeDesc +
        ", bizNo=" + bizNo +
        ", messageId=" + messageId +
        ", traceId=" + traceId +
        ", errorMessage=" + errorMessage +
        ", dataContent=" + dataContent +
        ", handleStatus=" + handleStatus +
                ", reExeErrorMsg=" + reExeErrorMsg +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
