package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 订单快递单号信息
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
@TableName("order_way_bill")
public class OrderWayBill implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 快递公司名称
     */
    @TableField("express_company_name")
    private String expressCompanyName;

    /**
     * 快递公司编码
     */
    @TableField("express_company_code")
    private String expressCompanyCode;

    /**
     * 物流单号
     */
    @TableField("ship_order_number")
    private String shipOrderNumber;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getExpressCompanyName() {
        return expressCompanyName;
    }

    public void setExpressCompanyName(String expressCompanyName) {
        this.expressCompanyName = expressCompanyName;
    }

    public String getShipOrderNumber() {
        return shipOrderNumber;
    }

    public void setShipOrderNumber(String shipOrderNumber) {
        this.shipOrderNumber = shipOrderNumber;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getExpressCompanyCode() {
        return expressCompanyCode;
    }

    public void setExpressCompanyCode(String expressCompanyCode) {
        this.expressCompanyCode = expressCompanyCode;
    }

    @Override
    public String toString() {
        return "WayBill{" +
        "id=" + id +
        ", orderId=" + orderId +
        ", expressCompanyName=" + expressCompanyName +
        ", shipOrderNumber=" + shipOrderNumber +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
                ", expressCompanyCode=" + expressCompanyCode +
        "}";
    }
}
