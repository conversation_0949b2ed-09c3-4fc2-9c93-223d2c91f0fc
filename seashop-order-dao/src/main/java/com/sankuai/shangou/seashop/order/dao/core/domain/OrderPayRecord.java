package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单支付记录表，用于记录订单与支付单之间的映射
 * </p>
 */
@TableName("order_pay_record")
public class OrderPayRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId("id")
    private Long id;

    /**
     * 支付批次单号。多笔订单一起支付时batch_no一致
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 订单号。与 order 表 order_id 一致
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 支付渠道的唯一标识。目前时汇付的支付ID
     */
    @TableField("pay_no")
    private String payNo;

    /**
     * 支付渠道。目前固定为7：汇付天下支付
     */
    @TableField("pay_channel")
    private Integer payChannel;

    /**
     * 支付方式。1：支付宝扫码；2：支付宝H5；3：微信小程序；4：微信H5；5：企业网银；6：个人网银
     */
    @TableField("pay_method")
    private Integer payMethod;

    /**
     * 订单需要付款金额
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;

    /**
     * 支付金额。可能在订单需付金额基础上还有渠道优惠
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付状态。0：关闭；1：支付中；2：支付成功；3：支付失败
     */
    @TableField("pay_status")
    private Integer payStatus;
    /**
     * 外部交易单号，比如支付宝的支付流水号
     */
    @TableField("out_trans_id")
    private String outTransId;
    /**
     * 支付时间，支付回调时设置
     */
    @TableField("pay_time")
    private Date payTime;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    @Override
    public String toString() {
        return "OrderPayRecord{" +
                "id=" + id +
                ", batchNo='" + batchNo +
                ", orderId='" + orderId +
                ", payNo='" + payNo +
                ", payChannel=" + payChannel +
                ", payMethod=" + payMethod +
                ", payStatus=" + payStatus +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                ", payTime=" + payTime +
                ", remark=" + remark +
                ", orderAmount=" + orderAmount +
                ", outTransId=" + outTransId +
                ", payAmount=" + payAmount +
                '}';
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public Integer getPayChannel() {
        return payChannel;
    }

    public void setPayChannel(Integer payChannel) {
        this.payChannel = payChannel;
    }

    public Integer getPayMethod() {
        return payMethod;
    }

    public void setPayMethod(Integer payMethod) {
        this.payMethod = payMethod;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public String getOutTransId() {
        return outTransId;
    }

    public void setOutTransId(String outTransId) {
        this.outTransId = outTransId;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }
}
