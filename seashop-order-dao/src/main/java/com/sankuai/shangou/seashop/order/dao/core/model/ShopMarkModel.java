package com.sankuai.shangou.seashop.order.dao.core.model;

import com.facebook.swift.codec.ThriftStruct;
import lombok.*;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description: 店铺评分返回值
 */
@ThriftStruct
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class ShopMarkModel {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 评分数量
     */
    private Long markCount;

    /**
     * 包装评分
     */
    private BigDecimal packMark;

    /**
     * 服务评分
     */
    private BigDecimal serviceMark;

    /**
     * 综合评分
     */
    private BigDecimal comprehensiveMark;

}
