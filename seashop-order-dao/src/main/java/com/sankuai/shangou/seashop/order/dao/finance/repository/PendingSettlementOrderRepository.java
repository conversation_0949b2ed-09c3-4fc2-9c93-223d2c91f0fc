package com.sankuai.shangou.seashop.order.dao.finance.repository;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.order.common.constant.FieldSortConstant;
import com.sankuai.shangou.seashop.order.common.enums.PendingSettlementOrderSortEnum;
import com.sankuai.shangou.seashop.order.dao.finance.domain.PendingSettlementOrder;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.PendingSettlementOrderMapper;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PendingSettlementOrderExtMapper;
import com.sankuai.shangou.seashop.order.dao.finance.model.PendingSettlementModel;
import com.sankuai.shangou.seashop.order.dao.finance.model.PendingSettlementOrderQryModel;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Repository
public class PendingSettlementOrderRepository extends ServiceImpl<PendingSettlementOrderMapper, PendingSettlementOrder> {

    @Resource
    private PendingSettlementOrderExtMapper pendingSettlementOrderExtMapper;

    /**
     * 查询待结算订单总金额
     *
     * @param shopId
     * @return
     */
    public BigDecimal getPendingSettlementAmount(Long shopId) {
        return pendingSettlementOrderExtMapper.sumSettlementAmount(
                PendingSettlementOrderQryModel.builder().shopId(shopId).build()
        );
    }

    /**
     * 查询待结算订单总金额
     *
     * @return
     */
    public BigDecimal getPlatCommission() {
        return pendingSettlementOrderExtMapper.getPlatCommission();
    }

    public BasePageResp<PendingSettlementModel> getPendingSettlementList(BasePageParam paramPage, PendingSettlementOrderQryModel param) {
        Page<PendingSettlementModel> page = PageHelper.startPage(paramPage);
        List<FieldSortReq> sortList = param.getSortList();
        if (CollUtil.isNotEmpty(sortList)) {
            for (FieldSortReq sortReq : sortList) {
                String sort = sortReq.getSort();
                if (PendingSettlementOrderSortEnum.SETTLEMENT_AMOUNT.getSort().equalsIgnoreCase(sort)) {
                    param.setSettlementAmountSort(sortReq.getIzAsc());
                }
            }
        } else {
            param.setSettlementAmountSort(Boolean.FALSE);
        }
        page.doSelectPage(() -> pendingSettlementOrderExtMapper.getPendingSettlementList(param));
        return PageResultHelper.transfer(page, PendingSettlementModel.class);
    }

    /**
     * 查询待结算订单列表
     *
     * @param paramPage
     * @param param
     * @return
     */
    public Page<PendingSettlementOrder> pageList(BasePageParam paramPage, PendingSettlementOrderQryModel param) {
        Page<PendingSettlementOrder> page = PageHelper.startPage(paramPage);
        LambdaQueryWrapper<PendingSettlementOrder> queryWrapper = new LambdaQueryWrapper<>();
        // 过滤调删除的
        queryWrapper.eq(PendingSettlementOrder::getDeleteFlag, Boolean.FALSE);
        if (null != param.getShopId()) {
            queryWrapper.eq(PendingSettlementOrder::getShopId, param.getShopId());
        }

        // 处理开始时间和结算时间的三种情况
        if (null != param.getStartCreateTime() && null != param.getEndCreateTime()) {
            queryWrapper.between(PendingSettlementOrder::getCreateTime, param.getStartCreateTime(), param.getEndCreateTime());
        }
        if (null != param.getStartCreateTime() && null == param.getEndCreateTime()) {
            queryWrapper.ge(PendingSettlementOrder::getCreateTime, param.getStartCreateTime());
        }
        if (null == param.getStartCreateTime() && null != param.getEndCreateTime()) {
            queryWrapper.le(PendingSettlementOrder::getCreateTime, param.getEndCreateTime());
        }

        if (null != param.getOrderId()) {
            queryWrapper.eq(PendingSettlementOrder::getOrderId, param.getOrderId());
        }
        if (null != param.getPaymentType()) {
            queryWrapper.eq(PendingSettlementOrder::getPaymentType, param.getPaymentType());
        }

        // 处理订单完成时间的三种情况
        if (null != param.getStartFinishTime() && null != param.getEndFinishTime()) {
            queryWrapper.between(PendingSettlementOrder::getOrderFinishTime, param.getStartFinishTime(), param.getEndFinishTime());
        }
        if (null != param.getStartFinishTime() && null == param.getEndFinishTime()) {
            queryWrapper.ge(PendingSettlementOrder::getOrderFinishTime, param.getStartFinishTime());
        }
        if (null == param.getStartFinishTime() && null != param.getEndFinishTime()) {
            queryWrapper.le(PendingSettlementOrder::getOrderFinishTime, param.getEndFinishTime());
        }

        // 处理支付时间的三种情况
        if (null != param.getStartPayTime() && null != param.getEndPayTime()) {
            queryWrapper.between(PendingSettlementOrder::getPayDate, param.getStartPayTime(), param.getEndPayTime());
        }
        if (null != param.getStartPayTime() && null == param.getEndPayTime()) {
            queryWrapper.ge(PendingSettlementOrder::getPayDate, param.getStartPayTime());
        }
        if (null == param.getStartPayTime() && null != param.getEndPayTime()) {
            queryWrapper.le(PendingSettlementOrder::getPayDate, param.getEndPayTime());
        }

        // 排序
        List<FieldSortReq> sortList = param.getSortList();
        if (CollUtil.isNotEmpty(sortList)) {
            for (FieldSortReq fieldSortReq : sortList) {
                String sort = fieldSortReq.getSort();
                if (FieldSortConstant.PRODUCTS_AMOUNT.equals(sort)) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), PendingSettlementOrder::getProductsAmount);
                }
            }
        } else {
            queryWrapper.orderByDesc(PendingSettlementOrder::getPayDate);
        }
        page.doSelectPage(() -> this.list(queryWrapper));
        return page;
    }

    public PendingSettlementOrder getByOrderId(String orderId) {
        LambdaQueryWrapper<PendingSettlementOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PendingSettlementOrder::getOrderId, orderId);
        return this.getOne(queryWrapper);
    }

    public PendingSettlementOrder getByOrderIdForceMaster(String orderId) {
        LambdaQueryWrapper<PendingSettlementOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PendingSettlementOrder::getOrderId, orderId);
        return this.getOne(queryWrapper);
    }

    public List<PendingSettlementOrder> listByOrderIdList(List<String> orderIdList) {
        LambdaQueryWrapper<PendingSettlementOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(PendingSettlementOrder::getOrderId, orderIdList)
                .eq(PendingSettlementOrder::getDeleteFlag, Boolean.FALSE);
        return this.list(queryWrapper);
    }

    /**
     * 根据订单完成时间查询待结算订单
     *
     * @param endTime
     * @return
     */
    public List<PendingSettlementOrder> getByFinishTime(Date endTime) {
        LambdaQueryWrapper<PendingSettlementOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNotNull(PendingSettlementOrder::getOrderFinishTime);
        queryWrapper.le(PendingSettlementOrder::getOrderFinishTime, endTime);
        queryWrapper.eq(PendingSettlementOrder::getDeleteFlag, Boolean.FALSE);
        queryWrapper.orderByDesc(PendingSettlementOrder::getOrderFinishTime);
        return this.list(queryWrapper);
    }


    public boolean updateOrderFinishTime(String orderId, Date orderFinishDate) {
        LambdaUpdateWrapper<PendingSettlementOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PendingSettlementOrder::getOrderId, orderId)
                .set(PendingSettlementOrder::getOrderFinishTime, orderFinishDate);
        return this.update(wrapper);
    }

    /**
     * 通过订单ID查询待结算订单
     */
    public PendingSettlementOrder getByOrderIdAndShopId(String orderId) {
        LambdaQueryWrapper<PendingSettlementOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PendingSettlementOrder::getOrderId, orderId);
        return this.getOne(queryWrapper);
    }

}
