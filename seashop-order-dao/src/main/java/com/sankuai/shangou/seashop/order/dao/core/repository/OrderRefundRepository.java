package com.sankuai.shangou.seashop.order.dao.core.repository;

import java.util.*;
import java.util.stream.Collectors;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderRefundReportDto;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderRefundMapper;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonRefundQueryParamPo;
import com.sankuai.shangou.seashop.order.dao.core.po.QueryOrderRefundPagePo;
import com.sankuai.shangou.seashop.order.dao.core.po.UserDeliverRefundItemParamPo;

import cn.hutool.core.collection.CollectionUtil;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Repository
public class OrderRefundRepository extends ServiceImpl<OrderRefundMapper, OrderRefund> {

    @Resource
    private  OrderItemRepository orderItemRepository;

    @Resource
    private OrderRepository orderRepository;
    public OrderRefund getById(Long refundId) {
        LambdaQueryWrapper<OrderRefund> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRefund::getId, refundId)
//                .eq(OrderRefund::getIsDelete, false)
        ;
        return super.getOne(wrapper);
    }

    public OrderRefund getByIdForceMaster(Long refundId) {
        LambdaQueryWrapper<OrderRefund> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRefund::getId, refundId)
//                .eq(OrderRefund::getIsDelete, false)
        ;
        return super.getOne(wrapper);
    }

    public OrderRefund getByIdIncludeDelete(Long refundId) {
        return super.getById(refundId);
    }

    public List<OrderRefund> getByOrderIdList(List<String> orderIdList) {
        return lambdaQuery().in(OrderRefund::getOrderId, orderIdList).list();
    }

    public List<OrderRefund> getBySourceRefundId(String sourceRefundId) {
        return lambdaQuery().eq(OrderRefund::getSourceRefundId, sourceRefundId).list();
    }

    public List<OrderRefund> getByOrderIdListOrderByApplyTimeDesc(List<String> orderIdList) {
        return lambdaQuery()
                .in(OrderRefund::getOrderId, orderIdList)
                .orderByDesc(OrderRefund::getApplyDate)
//                .eq(OrderRefund::getIsDelete, false)
                .list();
    }

    public Page<OrderRefund> queryOrderRefundPage(QueryOrderRefundPagePo po) {
        LambdaQueryWrapper<OrderRefund> wrapper = Wrappers.<OrderRefund>lambdaQuery()
                .eq(po.getOrderId() != null, OrderRefund::getOrderId, po.getOrderId())
                .eq(po.getShopId() != null, OrderRefund::getShopId, po.getShopId())
                .eq(po.getUserId() != null, OrderRefund::getUserId, po.getUserId());

        if (po.getAuditStatus() != null) {
            if (po.getAuditStatus() >= 6) {
                wrapper.eq(OrderRefund::getSellerAuditStatus, 5)
                        .eq(OrderRefund::getManagerConfirmStatus, po.getAuditStatus());
            }
            else {
                wrapper.eq(OrderRefund::getSellerAuditStatus, po.getAuditStatus());
            }
        }
        wrapper.in(CollectionUtil.isNotEmpty(po.getRefundModes()), OrderRefund::getRefundMode,
                po.getRefundModes());
        wrapper.in(CollectionUtil.isNotEmpty(po.getStatusList()), OrderRefund::getStatus,
                po.getStatusList());

        if (po.getStartCreateTime() != null || po.getEndCreateTime() != null) {
            wrapper.orderByDesc(OrderRefund::getCreateTime)
                    .ge(po.getStartCreateTime() != null, OrderRefund::getCreateTime,
                            po.getStartCreateTime())
                    .le(po.getEndCreateTime() != null, OrderRefund::getCreateTime,
                            po.getEndCreateTime());
        }
        else if (po.getStartUpdateTime() != null || po.getEndUpdateTime() != null) {
            wrapper.orderByDesc(OrderRefund::getUpdateTime)
                    .ge(po.getStartUpdateTime() != null, OrderRefund::getUpdateTime,
                            po.getStartUpdateTime())
                    .le(po.getEndUpdateTime() != null, OrderRefund::getUpdateTime,
                            po.getEndUpdateTime());
        }
        else {
            wrapper.orderByDesc(OrderRefund::getId);
        }

        return PageHelper.startPage(po.getPageNo(), po.getPageSize())
                .doSelectPage(() -> baseMapper.selectList(wrapper));
    }

    public List<OrderRefund> getByOrderId(String orderId) {
        return lambdaQuery().eq(OrderRefund::getOrderId, orderId)
//                .eq(OrderRefund::getIsDelete, false)
                .list();
    }

    /**
     * 根据状态查询可用的
     *
     * @param orderId
     * @param statusList
     * @return
     */
    public List<OrderRefund> getByOrderIdAndStatus(String orderId, Set<Integer> statusList) {
        return lambdaQuery().eq(OrderRefund::getOrderId, orderId)
//                .eq(OrderRefund::getIsDelete, false)
                .eq(OrderRefund::getHasCancel, false)
                .in(OrderRefund::getStatus, statusList)
                .orderByDesc(OrderRefund::getLastModifyTime)
                .list();
    }

    public boolean cancelOrderRefund(Long refundId, Date cancelDate, Integer targetStatus) {
        return lambdaUpdate().eq(OrderRefund::getId, refundId)
                .set(OrderRefund::getHasCancel, true)
                .set(OrderRefund::getCancelDate, cancelDate)
                .set(OrderRefund::getStatus, targetStatus)
                .update();
    }

    /*public boolean resetOrderRefund(Long refundId, OrderRefund refund) {
        return lambdaUpdate().eq(OrderRefund::getId, refundId)
                .set(OrderRefund::getHasCancel, refund.getHasCancel())
                .set(OrderRefund::getContactCellPhone, refund.getContactCellPhone())
                .set(OrderRefund::getContactPerson, refund.getContactPerson())
                .set(OrderRefund::getAmount, refund.getAmount())
                .set(OrderRefund::getApplyNumber, refund.getApplyNumber())
                .set(OrderRefund::getApplyDate, refund.getApplyDate())
                .set(OrderRefund::getReason, refund.getReason())
                .set(OrderRefund::getReasonDetail, refund.getReasonDetail())
                .set(OrderRefund::getSellerAuditStatus, refund.getSellerAuditStatus())
                .set(OrderRefund::getSellerAuditDate, refund.getSellerAuditDate())
                .set(OrderRefund::getSellerRemark, refund.getSellerRemark())
                .set(OrderRefund::getManagerConfirmStatus, refund.getManagerConfirmStatus())
                .set(OrderRefund::getManagerConfirmDate, refund.getManagerConfirmDate())
                .set(OrderRefund::getManagerRemark, refund.getManagerRemark())
                .set(OrderRefund::getHasReturn, refund.getHasReturn())
                .set(OrderRefund::getRefundMode, refund.getRefundMode())
                .set(OrderRefund::getRefundPayStatus, refund.getRefundPayStatus())
                .set(OrderRefund::getRefundPayType, refund.getRefundPayType())
                .set(OrderRefund::getReturnQuantity, refund.getReturnQuantity())
                .set(OrderRefund::getReturnPlatCommission, refund.getReturnPlatCommission())
                .set(OrderRefund::getCertPic1, refund.getCertPic1())
                .set(OrderRefund::getCertPic2, refund.getCertPic2())
                .set(OrderRefund::getCertPic3, refund.getCertPic3())
                .set(OrderRefund::getHasAllReturn, refund.getHasAllReturn())
                .set(OrderRefund::getLastModifyTime, refund.getLastModifyTime())
                .set(OrderRefund::getStatus, refund.getStatus())
                .update();
    }*/

    public void saveUserDeliveryData(UserDeliverRefundItemParamPo paramPo) {
        lambdaUpdate().eq(OrderRefund::getId, paramPo.getRefundId())
                .set(OrderRefund::getSellerAuditStatus, paramPo.getSellerAuditStatus())
                .set(OrderRefund::getExpressCompanyCode, paramPo.getExpressCompanyCode())
                .set(OrderRefund::getExpressCompanyName, paramPo.getExpressCompanyName())
                .set(OrderRefund::getShipOrderNumber, paramPo.getShipOrderNumber())
                .set(OrderRefund::getBuyerDeliverDate, paramPo.getDeliveryDate())
                .set(OrderRefund::getStatus, paramPo.getSellerAuditStatus())
                .update();
    }

    public void updatePlatformAuditStatus(Long refundId, Integer platformAuditStatus, String remark) {
        Date now = new Date();
        lambdaUpdate().eq(OrderRefund::getId, refundId)
                .set(OrderRefund::getManagerConfirmStatus, platformAuditStatus)
                .set(OrderRefund::getManagerRemark, remark)
                .set(OrderRefund::getManagerConfirmDate, now)
                .set(OrderRefund::getLastModifyTime, now)
                .set(OrderRefund::getStatus, platformAuditStatus)
                .update();
    }

    public void saveRefundPayStatus(Long refundId, String batchNo, Integer refundPayStatus) {
        Date now = new Date();
        lambdaUpdate().eq(OrderRefund::getId, refundId)
                .set(OrderRefund::getRefundPostTime, now)
                .set(OrderRefund::getRefundBatchNo, batchNo)
                .set(OrderRefund::getRefundPayStatus, refundPayStatus)
                .set(OrderRefund::getLastModifyTime, now)
                .update();
    }

    /**
     * 查询退款中的退款订单
     *
     * @param refundingStatus
     */
    public List<OrderRefund> queryRefundingList(Integer refundingStatus) {
        LambdaQueryWrapper<OrderRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefund::getManagerConfirmStatus, refundingStatus)
//                .eq(OrderRefund::getIsDelete, false)
        ;
        queryWrapper.ne(OrderRefund::getRefundBatchNo, "");
        return this.list(queryWrapper);
    }

    public OrderRefund getByBatchNo(String batchNo) {
        LambdaQueryWrapper<OrderRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefund::getRefundBatchNo, batchNo)
//                .eq(OrderRefund::getIsDelete, false)
        ;
        return this.getOne(queryWrapper);
    }

    public List<OrderRefund> getByCondition(CommonRefundQueryParamPo param) {
        LambdaQueryWrapper<OrderRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper
//                .eq(OrderRefund::getIsDelete, false)
                .eq(param.getSellerStatusEq() != null, OrderRefund::getSellerAuditStatus, param.getSellerStatusEq())
                .eq(param.getPlatformStatusEq() != null, OrderRefund::getManagerConfirmStatus, param.getPlatformStatusEq())
                .lt(param.getApplyDateLt() != null, OrderRefund::getApplyDate, param.getApplyDateLt())
                .eq(param.getIsCancelEq() != null, OrderRefund::getHasCancel, param.getIsCancelEq())
                .lt(param.getSellerAuditDateLt() != null, OrderRefund::getSellerAuditDate, param.getSellerAuditDateLt())
                .lt(param.getBuyerDeliveryDateLt() != null, OrderRefund::getBuyerDeliverDate, param.getBuyerDeliveryDateLt())
                .lt(param.getRefundPostTimeDateLt() != null, OrderRefund::getRefundPostTime, param.getRefundPostTimeDateLt())
                .ge(param.getIdGe() != null, OrderRefund::getId, param.getIdGe())
                .le(param.getIdLe() != null, OrderRefund::getId, param.getIdLe())
                .ge(param.getUpdateTimeGe() != null, OrderRefund::getUpdateTime, param.getUpdateTimeGe())
                .le(param.getUpdateTimeLe() != null, OrderRefund::getUpdateTime, param.getUpdateTimeLe());
        return this.list(queryWrapper);
    }

    public int updatePartialById(Long id, OrderRefund updateRefund) {
        LambdaUpdateWrapper<OrderRefund> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderRefund::getId, id);
        return this.baseMapper.update(updateRefund, wrapper);
    }


    public void removeById(Long id) {
        LambdaUpdateWrapper<OrderRefund> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderRefund::getId, id).set(OrderRefund::getIsDelete, true);
        super.update(wrapper);
    }
    public List<OrderRefundReportDto> getByUpdateTime(Date updateTime) {
        LambdaQueryWrapper<OrderRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.ne(OrderRefund::getOrderItemId, 0L);
        if (updateTime != null) {
            queryWrapper.ge(OrderRefund::getUpdateTime, updateTime);
        }
        List<OrderRefund> refunds = baseMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(refunds)) {
            return Collections.emptyList();
        }

        List<OrderRefundReportDto> result = JsonUtil.copyList(refunds, OrderRefundReportDto.class);
        List<Long> orderItemIds = refunds.stream().map(t -> t.getOrderItemId()).collect(Collectors.toList());

        List<String> orderIds = refunds.stream().map(t -> t.getOrderId()).collect(Collectors.toList());

        List<OrderItem> orderItems = orderItemRepository.getByIdList(orderItemIds);
        List<Order> orders = orderRepository.getByOrderIdList(orderIds);
        for (OrderRefundReportDto refundReportDto : result) {
            boolean hasItem = orderItems.stream().filter(t -> t.getId().equals(refundReportDto.getOrderItemId())).findFirst().isPresent();
            if (hasItem) {
                OrderItem item = orderItems.stream().filter(t -> t.getId().equals(refundReportDto.getOrderItemId())).findFirst().get();
                refundReportDto.setProductId(item.getProductId());
                refundReportDto.setSkuId(item.getSkuId());
            }

            boolean hasOrder = orders.stream().filter(t -> t.getOrderId().equals(refundReportDto.getOrderId())).findFirst().isPresent();
            if (hasOrder) {
                Order item = orders.stream().filter(t -> t.getOrderId().equals(refundReportDto.getOrderId())).findFirst().get();

                refundReportDto.setPlatform(item.getPlatform());
            }
        }

        List<OrderRefundReportDto> result2 = getByUpdateTime2(updateTime);
        result.addAll(result2);
        return result;
    }

    public List<OrderRefundReportDto> getByUpdateTime2(Date updateTime) {
        LambdaQueryWrapper<OrderRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderRefund::getOrderItemId, 0L);
        if (updateTime != null) {
            queryWrapper.ge(OrderRefund::getUpdateTime, updateTime);
        }
        List<OrderRefund> refunds = baseMapper.selectList(queryWrapper);
        if (CollectionUtil.isEmpty(refunds)) {
            return Collections.emptyList();
        }
        List<OrderRefundReportDto> result = new ArrayList<>();

        List<String> orderIds = refunds.stream().map(t -> t.getOrderId()).collect(Collectors.toList());
        List<OrderItem> orderItems = orderItemRepository.getByOrderIdList(orderIds);
        List<Order> orders = orderRepository.getByOrderIdList(orderIds);
        for (OrderRefund refund : refunds) {
            List<OrderItem> itemOrderItems = orderItems.stream().filter(t -> t.getOrderId().equals(refund.getOrderId())).collect(Collectors.toList());
            for (OrderItem orderItem : itemOrderItems) {
                OrderRefundReportDto orderRefundReportDto = JsonUtil.copy(refund, OrderRefundReportDto.class);
                orderRefundReportDto.setOrderItemId(orderItem.getId());
                orderRefundReportDto.setAmount(orderItem.getRealTotalPrice());
                orderRefundReportDto.setApplyQuantity(orderItem.getQuantity());
                orderRefundReportDto.setReturnQuantity(orderItem.getQuantity());
                orderRefundReportDto.setProductId(orderItem.getProductId());
                orderRefundReportDto.setSkuId(orderItem.getSkuId());
                boolean hasOrder = orders.stream().filter(t -> t.getOrderId().equals(refund.getOrderId())).findFirst().isPresent();
                if (hasOrder) {
                    Order item = orders.stream().filter(t -> t.getOrderId().equals(refund.getOrderId())).findFirst().get();
                    orderRefundReportDto.setPlatform(item.getPlatform());
                }
                result.add(orderRefundReportDto);
            }
        }
        return result;
    }
}
