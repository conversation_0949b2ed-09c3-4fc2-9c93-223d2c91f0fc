package com.sankuai.shangou.seashop.order.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefundLog;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderRefundLogMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class OrderRefundLogRepository extends ServiceImpl<OrderRefundLogMapper, OrderRefundLog> {

    public List<OrderRefundLog> getByRefundIdAndSortByOperateDateDesc(Long refundId) {
        return lambdaQuery()
                .eq(OrderRefundLog::getRefundId, refundId)
                .orderByDesc(OrderRefundLog::getOperateDate)
                .list();
    }

    /**
     * 重新关联售后ID，因为要做逻辑删除
     * <AUTHOR>
     * @param originRefundId
	 * @param newRefundId
     * void
     */
    public void updateRefundId(Long originRefundId, Long newRefundId) {
        LambdaUpdateWrapper<OrderRefundLog> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderRefundLog::getRefundId, originRefundId)
                .set(OrderRefundLog::getRefundId, newRefundId);
        super.update(wrapper);
    }

}
