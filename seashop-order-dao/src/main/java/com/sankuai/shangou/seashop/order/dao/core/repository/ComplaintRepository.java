package com.sankuai.shangou.seashop.order.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderComplaint;
import com.sankuai.shangou.seashop.order.dao.core.model.OrderComplaintExt;
import com.sankuai.shangou.seashop.order.dao.core.model.QueryOrderRightsReqModel;
import com.sankuai.shangou.seashop.order.dao.core.model.QueryOrderRightsRespModel;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderComplaintMapper;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description：TODO
 * @author： liweisong
 * @create： 2023/11/21 19:30
 */
@Repository
@Slf4j
public class ComplaintRepository {

    @Resource
    private OrderComplaintMapper orderComplaintMapper;

    public OrderComplaint selectById(Long id){
        return orderComplaintMapper.selectById(id);
    }

    public Page<OrderComplaint> pageQueryOrderComplaint(OrderComplaintExt orderComplaintExt, Integer pageNo, Integer pageSize){
        Page<OrderComplaint> orderComplaintPage = PageHelper.startPage(pageNo, pageSize);
        QueryWrapper<OrderComplaint> queryWrapper = new QueryWrapper<>();
        if(orderComplaintExt.getId() != null){
            queryWrapper.lambda().eq(OrderComplaint::getId, orderComplaintExt.getId());
        }
        if(StringUtils.isNotEmpty(orderComplaintExt.getOrderId())){
            queryWrapper.lambda().eq(OrderComplaint::getOrderId, orderComplaintExt.getOrderId());
        }
        if(orderComplaintExt.getStatus() != null){
            queryWrapper.lambda().eq(OrderComplaint::getStatus, orderComplaintExt.getStatus());
        }
        if(orderComplaintExt.getShopId() != null){
            queryWrapper.lambda().eq(OrderComplaint::getShopId, orderComplaintExt.getShopId());
        }
        if(StringUtils.isNotEmpty(orderComplaintExt.getShopName())){
            queryWrapper.lambda().like(OrderComplaint::getShopName, orderComplaintExt.getShopName());
        }
        if(orderComplaintExt.getUserId() != null){
            queryWrapper.lambda().eq(OrderComplaint::getUserId, orderComplaintExt.getUserId());
        }
        if(StringUtils.isNotEmpty(orderComplaintExt.getUserName())){
            queryWrapper.lambda().like(OrderComplaint::getUserName, orderComplaintExt.getUserName());
        }
        if(orderComplaintExt.getCreateTimeStart() != null){
            queryWrapper.lambda().ge(OrderComplaint::getCreateTime, orderComplaintExt.getCreateTimeStart());
        }
        if(orderComplaintExt.getCreateTimeEnd() != null){
            queryWrapper.lambda().lt(OrderComplaint::getCreateTime, orderComplaintExt.getCreateTimeEnd());
        }
        queryWrapper.lambda().orderByDesc(OrderComplaint::getCreateTime);
        List<OrderComplaint> list = orderComplaintMapper.selectList(queryWrapper);
        return orderComplaintPage;
    }

    public Page<QueryOrderRightsRespModel> pageQueryOrderRights(QueryOrderRightsReqModel queryOrderRightsReqDto, Integer pageNo, Integer pageSize){
        Page<QueryOrderRightsRespModel> orderRightsRespDtos = PageHelper.startPage(pageNo, pageSize);
        List<QueryOrderRightsRespModel> result = orderComplaintMapper.pageQueryOrderRights(queryOrderRightsReqDto);
        return orderRightsRespDtos;
    }
    public int insert(OrderComplaint orderComplaint){
        LambdaQueryWrapper<OrderComplaint> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderComplaint::getId, orderComplaint.getId());
        boolean exists = orderComplaintMapper.exists(queryWrapper);
        if(exists){
            return orderComplaintMapper.updateById(orderComplaint);
        }
        return orderComplaintMapper.insert(orderComplaint);
    }

    public int updateById(OrderComplaint orderComplaint){
        return orderComplaintMapper.updateById(orderComplaint);
    }

    /**
     * 根据状态统计数量
     * @param status 状态
     * @return 数量
     */
    public long statsForStatusForPlatform(Integer status) {
        QueryWrapper<OrderComplaint> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderComplaint::getStatus, status);
        return orderComplaintMapper.selectCount(queryWrapper);
    }

    /**
     * 根据店铺统计状态数量
     * @param status 状态
     * @return 数量
     */
    public long statsForStatusForSeller(Long shopId, Integer status) {
        QueryWrapper<OrderComplaint> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderComplaint::getShopId, shopId)
                .eq(OrderComplaint::getStatus, status);
        return orderComplaintMapper.selectCount(queryWrapper);
    }

}
