package com.sankuai.shangou.seashop.order.dao.core.repository;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderWayBill;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderWayBillMapper;

/**
 * <AUTHOR>
 */
@Repository
public class OrderWayBillRepository extends ServiceImpl<OrderWayBillMapper, OrderWayBill> {

    /**
     * 日志记录使用
     */
    public OrderWayBill selectById(Long id) {
        return super.getById(id);
    }

    public List<OrderWayBill> getByOrderIdList(List<String> orderIdList) {
        LambdaQueryWrapper<OrderWayBill> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderWayBill::getOrderId, orderIdList);
        return baseMapper.selectList(wrapper);
    }

    public List<OrderWayBill> getByOrderId(String orderId) {
        LambdaQueryWrapper<OrderWayBill> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderWayBill::getOrderId, orderId);
        return baseMapper.selectList(wrapper);
    }

    public int removeByOrderId(String orderId) {
        LambdaQueryWrapper<OrderWayBill> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderWayBill::getOrderId, orderId);
        return baseMapper.delete(wrapper);
    }
    public List<OrderWayBill> getByUpdateTime(Date updateTime) {
        LambdaQueryWrapper<OrderWayBill> queryWrapper = new LambdaQueryWrapper<>();
        if (updateTime != null) {
            queryWrapper.ge(OrderWayBill::getUpdateTime, updateTime);
        }

        return baseMapper.selectList(queryWrapper);
    }
}
