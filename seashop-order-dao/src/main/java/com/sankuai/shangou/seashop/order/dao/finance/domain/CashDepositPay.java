package com.sankuai.shangou.seashop.order.dao.finance.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 保证金支付表
 * </p>
 *
 * <AUTHOR> @since 2023-11-28
 */
@TableName("finance_cash_deposit_pay")
public class CashDepositPay implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "Id", type = IdType.AUTO)
    private Long Id;

    /**
     * 保证金订单ID
     */
    @TableField("pay_id")
    private String payId;

    /**
     * 汇付ID
     */
    @TableField("adapay_id")
    private String adapayId;

    /**
     * 支付时间
     */
    @TableField("pay_date")
    private Date payDate;

    /**
     * 支付状态
     */
    @TableField("pay_status")
    private Integer payStatus;

    /**
     * 支付方式
     */
    @TableField("payment")
    private String payment;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return Id;
    }

    public void setId(Long Id) {
        this.Id = Id;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getAdapayId() {
        return adapayId;
    }

    public void setAdapayId(String adapayId) {
        this.adapayId = adapayId;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public Integer getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(Integer payStatus) {
        this.payStatus = payStatus;
    }

    public String getPayment() {
        return payment;
    }

    public void setPayment(String payment) {
        this.payment = payment;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CashDepositPay{" +
        "Id=" + Id +
        ", payId=" + payId +
        ", adapayId=" + adapayId +
        ", payDate=" + payDate +
        ", payStatus=" + payStatus +
        ", payment=" + payment +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
