package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("order_promotion_snapshot")
public class OrderPromotionSnapshot {

    /**
     * 自增主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 订单号。与 order 表 order_id 一致
     */
    private String orderId;
    /**
     * 业务类型。1：订单营销；2：商品营销
     */
    private Integer bizType;
    /**
     * 商品ID。bizType=2时有值，否则默认0
     */
    private Long productId;
    /**
     * skuId，下单维度是sku，bizType=2时有值，否则默认空
     */
    private String skuId;
    /**
     * 满足的营销活动内容，异构数据，用json字符串保存
     */
    private String promotionContent;
    /**
     * 创建时间
     */
    private Date createTime;

}
