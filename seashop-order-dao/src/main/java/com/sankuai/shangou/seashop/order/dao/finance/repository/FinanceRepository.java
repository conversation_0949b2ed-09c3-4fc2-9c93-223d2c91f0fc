package com.sankuai.shangou.seashop.order.dao.finance.repository;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Finance;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.FinanceMapper;

/**
 * @author: lhx
 * @date: 2023/12/2/002
 * @description:
 */
@Repository
public class FinanceRepository extends ServiceImpl<FinanceMapper, Finance> {

    /**
     * 通过订单号和类型获取财务记录
     *
     * @param orderId
     * @param type
     * @return
     */
    public List<Finance> listByOrderId(String orderId, Integer type) {
        LambdaQueryWrapper<Finance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Finance::getOrderId, orderId);
        wrapper.eq(Finance::getType, type);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 通过订单号列表和类型获取财务记录
     *
     * @param orderIds
     * @param type
     * @return
     */
    public List<Finance> listByOrderIdList(List<String> orderIds, Integer type) {
        LambdaQueryWrapper<Finance> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(Finance::getOrderId, orderIds);
        wrapper.eq(Finance::getType, type);
        return baseMapper.selectList(wrapper);
    }

    public int countByOrderIdAndType(String orderId, Integer type) {
        LambdaQueryWrapper<Finance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Finance::getOrderId, orderId)
                .eq(Finance::getType, type);
        return Math.toIntExact(this.count(wrapper));
    }


    /**
     * 通过退款单号和类型获取财务记录
     *
     * @param refundId
     * @param type
     * @return
     */
    public Finance getByRefundId(Long refundId, Integer type) {
        LambdaQueryWrapper<Finance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Finance::getOrderRefundId, refundId);
        wrapper.eq(Finance::getType, type);
        return baseMapper.selectOne(wrapper);
    }


    public Finance getByOrderIdAndPayNoForeMaster(String orderId, String payNo) {
        LambdaQueryWrapper<Finance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Finance::getOrderId, orderId)
                .eq(Finance::getAdapayId, payNo);
        return baseMapper.selectOne(wrapper);
    }
}
