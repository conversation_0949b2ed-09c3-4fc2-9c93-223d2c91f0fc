package com.sankuai.shangou.seashop.order.dao.finance.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 店铺资金表
 * </p>
 *
 * <AUTHOR> @since 2023-12-02
 */
@TableName("finance_shop_account")
public class ShopAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺Id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 待结算
     */
    @TableField("pending_settlement")
    private BigDecimal pendingSettlement;

    /**
     * 已结算
     */
    @TableField("settled")
    private BigDecimal settled;

    @TableField("remark")
    private String remark;

    /**
     * 汇付天下结算账户ID
     */
    @TableField("settle_account_id")
    private String settleAccountId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public BigDecimal getPendingSettlement() {
        return pendingSettlement;
    }

    public void setPendingSettlement(BigDecimal pendingSettlement) {
        this.pendingSettlement = pendingSettlement;
    }

    public BigDecimal getSettled() {
        return settled;
    }

    public void setSettled(BigDecimal settled) {
        this.settled = settled;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSettleAccountId() {
        return settleAccountId;
    }

    public void setSettleAccountId(String settleAccountId) {
        this.settleAccountId = settleAccountId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ShopAccount{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", shopName=" + shopName +
        ", pendingSettlement=" + pendingSettlement +
        ", settled=" + settled +
        ", remark=" + remark +
        ", settleAccountId=" + settleAccountId +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
