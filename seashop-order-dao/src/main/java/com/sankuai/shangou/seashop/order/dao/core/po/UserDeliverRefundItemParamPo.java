package com.sankuai.shangou.seashop.order.dao.core.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class UserDeliverRefundItemParamPo {

    private Long refundId;
    private Integer sellerAuditStatus;
    private String expressCompanyCode;
    private String expressCompanyName;
    private String shipOrderNumber;
    private Date deliveryDate;

}
