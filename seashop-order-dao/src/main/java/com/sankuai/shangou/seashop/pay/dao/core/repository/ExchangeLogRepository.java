package com.sankuai.shangou.seashop.pay.dao.core.repository;

import com.sankuai.shangou.seashop.pay.dao.core.domain.ExchangeLog;
import com.sankuai.shangou.seashop.pay.dao.core.mapper.ExchangeLogMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 16:20
 */
@Repository
public class ExchangeLogRepository {

    @Resource
    private ExchangeLogMapper exchangeLogMapper;

    public void insetExchangeLog(ExchangeLog req){
        exchangeLogMapper.insert(req);
    }
}
