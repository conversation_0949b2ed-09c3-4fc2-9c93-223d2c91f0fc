package com.sankuai.shangou.seashop.pay.dao.core.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay;
import com.sankuai.shangou.seashop.pay.dao.core.mapper.OrderPayMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description:
 */
@Repository
@Slf4j
public class OrderPayRepository extends ServiceImpl<OrderPayMapper, OrderPay> {

//    public List<OrderPay> queryByOrderId(OrderPayParamModel param) {
//        LambdaQueryWrapper<OrderPay> queryWrapper = new LambdaQueryWrapper<>();
//        if (StrUtil.isNotBlank(param.getOrderId())) {
//            queryWrapper.eq(OrderPay::getOrderId, param.getOrderId());
//        }
//        return this.list(queryWrapper);
//    }

    public OrderPay queryListByNo(OrderPay param) {
        LambdaQueryWrapper<OrderPay> queryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(param.getOrderId())) {
            queryWrapper.eq(OrderPay::getOrderId, param.getOrderId());
        }
        if (StrUtil.isNotBlank(param.getPayId())) {
            queryWrapper.eq(OrderPay::getPayId, param.getPayId());
        }
        if (StrUtil.isNotBlank(param.getChannelPayId())) {
            queryWrapper.eq(OrderPay::getChannelPayId, param.getChannelPayId());
        }
        return this.getOne(queryWrapper);
    }

    public OrderPay getByOrderId(String orderId) {
        LambdaQueryWrapper<OrderPay> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPay::getOrderId, orderId);
        return this.getOne(queryWrapper);
    }

    public void updateUnPaid(List<String> updateOrderIdList, Integer status){
        this.baseMapper.updateUnPaid(updateOrderIdList, status);
    }

    public List<OrderPay> queryPayOrderPayList(List<String> batchNos){
        if(CollectionUtils.isEmpty(batchNos)){
            return Collections.EMPTY_LIST;
        }
        LambdaQueryWrapper<OrderPay> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(OrderPay::getOrderId, batchNos);
        return this.baseMapper.selectList(queryWrapper);
    }

    public OrderPay getByPayId(String payId) {
        LambdaQueryWrapper<OrderPay> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPay::getPayId, payId);
        return this.getOne(queryWrapper);
    }

    public OrderPay getByChannelPayId(String channelPayId) {
        LambdaQueryWrapper<OrderPay> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(OrderPay::getChannelPayId, channelPayId);
        return this.getOne(queryWrapper);
    }
}
