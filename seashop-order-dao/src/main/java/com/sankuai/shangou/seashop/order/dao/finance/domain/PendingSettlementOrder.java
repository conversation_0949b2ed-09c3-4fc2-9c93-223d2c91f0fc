package com.sankuai.shangou.seashop.order.dao.finance.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 待结算订单表
 * </p>
 *
 * <AUTHOR> @since 2023-12-08
 */
@TableName("finance_pending_settlement_order")
public class PendingSettlementOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 订单号
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 订单类型
     */
    @TableField("order_type")
    private Integer orderType;

    /**
     * 订单金额
     */
    @TableField("order_amount")
    private BigDecimal orderAmount;

    /**
     * 商品实付金额
     */
    @TableField("products_amount")
    private BigDecimal productsAmount;

    /**
     * 运费
     */
    @TableField("freight_amount")
    private BigDecimal freightAmount;

    /**
     * 税费
     */
    @TableField("tax_amount")
    private BigDecimal taxAmount;

    /**
     * 平台佣金
     */
    @TableField("plat_commission")
    private BigDecimal platCommission;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 退款时间
     */
    @TableField("refund_date")
    private Date refundDate;

    /**
     * 平台佣金退还
     */
    @TableField("plat_commission_return")
    private BigDecimal platCommissionReturn;

    /**
     * 结算金额
     */
    @TableField("settlement_amount")
    private BigDecimal settlementAmount;

    /**
     * 付款日期
     */
    @TableField("pay_date")
    private Date payDate;

    /**
     * 订单完成时间
     */
    @TableField("order_finish_time")
    private Date orderFinishTime;

    /**
     * 支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银，
     */
    @TableField("payment_type")
    private Integer paymentType;

    @TableField("payment_type_name")
    private String paymentTypeName;

    /**
     * 平台优惠券抵扣金额
     */
    @TableField("discount_amount")
    private BigDecimal discountAmount;

    /**
     * 平台优惠券退还金额
     */
    @TableField("discount_amount_return")
    private BigDecimal discountAmountReturn;

    /**
     * 支付渠道 1汇付天下
     */
    @TableField("payment_channel")
    private Integer paymentChannel;

    /**
     * 渠道手续费
     */
    @TableField("channel_amount")
    private BigDecimal channelAmount;

    /**
     * 结算备注
     */
    @TableField("settelement_remark")
    private String settelementRemark;

    /**
     * 是否自营店结算订单，0代表否，1代表是
     */
    @TableField("self_flag")
    private Boolean selfFlag;

    /**
     * 是否删除
     */
    @TableField("delete_flag")
    private Boolean deleteFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getOrderType() {
        return orderType;
    }

    public void setOrderType(Integer orderType) {
        this.orderType = orderType;
    }

    public BigDecimal getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(BigDecimal orderAmount) {
        this.orderAmount = orderAmount;
    }

    public BigDecimal getProductsAmount() {
        return productsAmount;
    }

    public void setProductsAmount(BigDecimal productsAmount) {
        this.productsAmount = productsAmount;
    }

    public BigDecimal getFreightAmount() {
        return freightAmount;
    }

    public void setFreightAmount(BigDecimal freightAmount) {
        this.freightAmount = freightAmount;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getPlatCommission() {
        return platCommission;
    }

    public void setPlatCommission(BigDecimal platCommission) {
        this.platCommission = platCommission;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public Date getRefundDate() {
        return refundDate;
    }

    public void setRefundDate(Date refundDate) {
        this.refundDate = refundDate;
    }

    public BigDecimal getPlatCommissionReturn() {
        return platCommissionReturn;
    }

    public void setPlatCommissionReturn(BigDecimal platCommissionReturn) {
        this.platCommissionReturn = platCommissionReturn;
    }

    public BigDecimal getSettlementAmount() {
        return settlementAmount;
    }

    public void setSettlementAmount(BigDecimal settlementAmount) {
        this.settlementAmount = settlementAmount;
    }

    public Date getPayDate() {
        return payDate;
    }

    public void setPayDate(Date payDate) {
        this.payDate = payDate;
    }

    public Date getOrderFinishTime() {
        return orderFinishTime;
    }

    public void setOrderFinishTime(Date orderFinishTime) {
        this.orderFinishTime = orderFinishTime;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public String getPaymentTypeName() {
        return paymentTypeName;
    }

    public void setPaymentTypeName(String paymentTypeName) {
        this.paymentTypeName = paymentTypeName;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getDiscountAmountReturn() {
        return discountAmountReturn;
    }

    public void setDiscountAmountReturn(BigDecimal discountAmountReturn) {
        this.discountAmountReturn = discountAmountReturn;
    }

    public Integer getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(Integer paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    public BigDecimal getChannelAmount() {
        return channelAmount;
    }

    public void setChannelAmount(BigDecimal channelAmount) {
        this.channelAmount = channelAmount;
    }

    public String getSettelementRemark() {
        return settelementRemark;
    }

    public void setSettelementRemark(String settelementRemark) {
        this.settelementRemark = settelementRemark;
    }

    public Boolean getSelfFlag() {
        return selfFlag;
    }

    public void setSelfFlag(Boolean selfFlag) {
        this.selfFlag = selfFlag;
    }

    public Boolean getDeleteFlag() {
        return deleteFlag;
    }

    public void setDeleteFlag(Boolean deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "PendingSettlementOrder{" +
                "id=" + id +
                ", shopId=" + shopId +
                ", shopName=" + shopName +
                ", orderId=" + orderId +
                ", orderType=" + orderType +
                ", orderAmount=" + orderAmount +
                ", productsAmount=" + productsAmount +
                ", freightAmount=" + freightAmount +
                ", taxAmount=" + taxAmount +
                ", platCommission=" + platCommission +
                ", refundAmount=" + refundAmount +
                ", refundDate=" + refundDate +
                ", platCommissionReturn=" + platCommissionReturn +
                ", settlementAmount=" + settlementAmount +
                ", payDate=" + payDate +
                ", orderFinishTime=" + orderFinishTime +
                ", paymentType=" + paymentType +
                ", paymentTypeName=" + paymentTypeName +
                ", discountAmount=" + discountAmount +
                ", discountAmountReturn=" + discountAmountReturn +
                ", paymentChannel=" + paymentChannel +
                ", channelAmount=" + channelAmount +
                ", settelementRemark=" + settelementRemark +
                ", selfFlag=" + selfFlag +
                ", deleteFlag=" + deleteFlag +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
