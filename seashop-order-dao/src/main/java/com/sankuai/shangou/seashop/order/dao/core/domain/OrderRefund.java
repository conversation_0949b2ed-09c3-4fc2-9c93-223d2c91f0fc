package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单退款主表
 * </p>
 *
 * @since 2023-11-15
 */
@ToString
@Getter
@Setter
@TableName("order_refund")
public class OrderRefund implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    @TableField("order_id")
    private String orderId;
    /**
     * 三方售后单号
     */
    @TableField("source_refund_id")
    private String sourceRefundId;

    /**
     * 订单详情id
     */
    @TableField("order_item_id")
    private Long orderItemId;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 商家id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 申请人
     */
    @TableField("applicant")
    private String applicant;

    /**
     * 联系人
     */
    @TableField("contact_person")
    private String contactPerson;

    /**
     * 联系电话
     */
    @TableField("contact_cell_phone")
    private String contactCellPhone;

    /**
     * 申请时间
     */
    @TableField("apply_date")
    private Date applyDate;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 退款原因
     */
    @TableField("reason")
    private String reason;

    /**
     * 退款详情
     */
    @TableField("reason_detail")
    private String reasonDetail;

    /**
     * 供应商审核状态(1:待供应商审核,2:待买家寄货,3:待供应商收货,4:供应商拒绝,5:供应商通过审核)
     */
    @TableField("seller_audit_status")
    private Integer sellerAuditStatus;

    /**
     * 供应商审核时间
     */
    @TableField("seller_audit_date")
    private Date sellerAuditDate;

    /**
     * 供应商注释
     */
    @TableField("seller_remark")
    private String sellerRemark;

    /**
     * 状态。根据供应商状态和平台状态，以及是否取消综合得到。供应商和平台的状态值保持一致。默认值0是还没有初始化数据的
     * 1:待供应商审核,2:待买家寄货,3:待供应商收货,4:供应商拒绝,5:供应商通过审核,6:待平台确认,7:退款成功,8:平台驳回,9:退款中,-1:取消售后
     */
    @TableField("status")
    private Integer status;

    /**
     * 平台审核状态(6:待平台确认,7:退款成功,8:平台驳回)
     */
    @TableField("manager_confirm_status")
    private Integer managerConfirmStatus;

    /**
     * 平台审核时间
     */
    @TableField("manager_confirm_date")
    private Date managerConfirmDate;

    /**
     * 平台注释
     */
    @TableField("manager_remark")
    private String managerRemark;

    /**
     * 是否需要退货
     */
    @TableField("is_return")
    private Boolean hasReturn;

    /**
     * 快递公司
     */
    @TableField("express_company_name")
    private String expressCompanyName;

    /**
     * 快递公司编码
     */
    @TableField("express_company_code")
    private String expressCompanyCode;

    /**
     * 快递单号
     */
    @TableField("ship_order_number")
    private String shipOrderNumber;

    /**
     * 退款方式(1:订单退款,2:货品退款,3:退货退款)
     */
    @TableField("refund_mode")
    private Integer refundMode;

    /**
     * 退款支付状态
     */
    @TableField("refund_pay_status")
    private Integer refundPayStatus;

    /**
     * 退款支付类型
     */
    @TableField("refund_pay_type")
    private Integer refundPayType;

    /**
     * 买家发货时间
     */
    @TableField("buyer_deliver_date")
    private Date buyerDeliverDate;

    /**
     * 卖家确认到货时间
     */
    @TableField("seller_confirm_arrival_date")
    private Date sellerConfirmArrivalDate;

    /**
     * 退款批次号
     */
    @TableField("refund_batch_no")
    private String refundBatchNo;

    /**
     * 退款异步提交时间
     */
    @TableField("refund_post_time")
    private Date refundPostTime;

    /**
     * 申请售后数量
     */
    @TableField("apply_quantity")
    private Long applyQuantity;

    /**
     * 退货数量
     */
    @TableField("return_quantity")
    private Long returnQuantity;

    /**
     * 平台佣金退还
     */
    @TableField("return_plat_commission")
    private BigDecimal returnPlatCommission;

    /**
     * 申请次数
     */
    @TableField("apply_number")
    private Integer applyNumber;

    /**
     * 凭证图片1
     */
    @TableField("cert_pic1")
    private String certPic1;

    /**
     * 凭证图片2
     */
    @TableField("cert_pic2")
    private String certPic2;

    /**
     * 凭证图片3
     */
    @TableField("cert_pic3")
    private String certPic3;

    /**
     * 是否订单全部退
     */
    @TableField("is_all_return")
    private Boolean hasAllReturn;

    /**
     * 退运费
     */
    @TableField("return_freight")
    private BigDecimal returnFreight;

    /**
     * 最后修改时间
     */
    @TableField("last_modify_time")
    private Date lastModifyTime;

    /**
     * 是否取消申请
     */
    @TableField("is_cancel")
    private Boolean hasCancel;

    /**
     * 取消申请时间
     */
    @TableField("cancel_date")
    private Date cancelDate;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 重新申请对应的原始ID。0代表非重新申请的原始数据
     */
    @TableField("reapply_origin_id")
    private Long reapplyOriginId;
    /**
     * 是否删除。0：否；1：是
     */
    @TableField("is_delete")
    private Integer isDelete;

}
