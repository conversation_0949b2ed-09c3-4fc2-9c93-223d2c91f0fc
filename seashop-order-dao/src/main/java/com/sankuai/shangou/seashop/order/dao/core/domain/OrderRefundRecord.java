package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 退款记录表
 * </p>
 *
 * <AUTHOR> @since 2024-01-08
 */
public class OrderRefundRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联id;订单默认关联orderId表
     */
    @TableField("relate_id")
    private String relateId;

    /**
     * 退款金额
     */
    @TableField("refund_amount")
    private BigDecimal refundAmount;

    /**
     * 支付时支付单号
     */
    @TableField("pay_no")
    private String payNo;

    /**
     * 支付时渠道订单号
     */
    @TableField("pay_channel_no")
    private String payChannelNo;

    /**
     * 退款订单号
     */
    @TableField("refund_no")
    private String refundNo;

    /**
     * 退款类型 1:超付退款
     */
    @TableField("refund_type")
    private Integer refundType;

    /**
     * 业务类型 1：订单,4：保证金
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 退款状态 0: 退款中,1: 退款成功,2: 退款失败
     */
    @TableField("refund_status")
    private Integer refundStatus;

    /**
     * 异常描述
     */
    @TableField("err_msg")
    private String errMsg;

    /**
     * 操作人ID
     */
    @TableField("operator_id")
    private Long operatorId;

    /**
     * 操作人账号
     */
    @TableField("operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRelateId() {
        return relateId;
    }

    public void setRelateId(String relateId) {
        this.relateId = relateId;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public String getPayChannelNo() {
        return payChannelNo;
    }

    public void setPayChannelNo(String payChannelNo) {
        this.payChannelNo = payChannelNo;
    }

    public String getRefundNo() {
        return refundNo;
    }

    public void setRefundNo(String refundNo) {
        this.refundNo = refundNo;
    }

    public Integer getRefundType() {
        return refundType;
    }

    public void setRefundType(Integer refundType) {
        this.refundType = refundType;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(Integer refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "OrderRefundRecord{" +
                "id=" + id +
                ", relateId=" + relateId +
                ", refundAmount=" + refundAmount +
                ", payNo=" + payNo +
                ", payChannelNo=" + payChannelNo +
                ", refundNo=" + refundNo +
                ", refundType=" + refundType +
                ", businessType=" + businessType +
                ", refundStatus=" + refundStatus +
                ", errMsg=" + errMsg +
                ", operatorId=" + operatorId +
                ", operatorName=" + operatorName +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
