package com.sankuai.shangou.seashop.order.dao.finance.mapper.ext;

import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2024/1/3/003
 * @description:
 */
public interface ShopAccountExtMapper {

    /**
     * 更新店铺待结算金额
     *
     * @param shopId
     * @param amount
     * @return
     */
    int updatePendingSettlement(@Param("shopId") Long shopId, @Param("amount") BigDecimal amount);
}
