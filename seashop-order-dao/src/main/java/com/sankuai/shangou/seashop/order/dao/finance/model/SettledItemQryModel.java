package com.sankuai.shangou.seashop.order.dao.finance.model;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description: 已结算明细列表请求参数
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ToString
@Getter
@Setter
public class SettledItemQryModel extends BasePageReq {

    // 结算id
    private Long detailId;

    // 店铺id
    private Long shopId;

    // 订单完成时间-开始时间
    private Date startOrderFinishTime;

    // 订单完成时间-结束时间
    private Date endOrderFinishTime;

    // 结算时间-开始时间
    private Date startSettlementTime;

    // 结算时间-结束时间
    private Date endSettlementTime;

    // 订单id
    private String orderId;

    // 支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银
    private Integer paymentType;

    // 支付时间-开始时间
    private Date startPayTime;

    // 支付时间-结束时间
    private Date endPayTime;


    private String shopName;
}
