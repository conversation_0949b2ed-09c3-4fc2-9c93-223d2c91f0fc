package com.sankuai.shangou.seashop.order.dao.core.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.core.domain.ExceptionOrder;
import com.sankuai.shangou.seashop.order.dao.core.mapper.ExceptionOrderMapper;
import com.sankuai.shangou.seashop.order.dao.core.po.QueryExceptionOrderPo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class ExceptionOrderRepository extends ServiceImpl<ExceptionOrderMapper, ExceptionOrder> {

    /**
     * 日志记录使用
     */
    public ExceptionOrder selectById(Long id){
        return super.getById(id);
    }

    public Page<ExceptionOrder> pageList(Page<ExceptionOrder> pageInfo, QueryExceptionOrderPo param) {
        LambdaQueryWrapper<ExceptionOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(param.getOrderId()), ExceptionOrder::getOrderId, param.getOrderId())
                .eq(param.getRefundStatus() != null, ExceptionOrder::getRefundStatus, param.getRefundStatus());
        return baseMapper.selectPage(pageInfo, wrapper);

    }

    public List<ExceptionOrder> getList(QueryExceptionOrderPo param) {
        LambdaQueryWrapper<ExceptionOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StrUtil.isNotBlank(param.getOrderId()), ExceptionOrder::getOrderId, param.getOrderId())
                .eq(param.getRefundStatus() != null, ExceptionOrder::getRefundStatus, param.getRefundStatus())
                .orderByDesc(ExceptionOrder::getId);
        return baseMapper.selectList(wrapper);
    }

    public List<ExceptionOrder> getByOrderIdListForceMaster(List<String> orderIdList) {
        LambdaQueryWrapper<ExceptionOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(ExceptionOrder::getOrderId, orderIdList);
        return baseMapper.selectList(wrapper);
    }

    public void updateRefundInfoByBatchNo(String batchNo, ExceptionOrder updateOrder) {
        LambdaQueryWrapper<ExceptionOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExceptionOrder::getRefundBatchNo, batchNo);
        baseMapper.update(updateOrder, wrapper);
    }

    public void updateById(Long id, ExceptionOrder updateOrder) {
        LambdaQueryWrapper<ExceptionOrder> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExceptionOrder::getId, id);
        baseMapper.update(updateOrder, wrapper);
    }


}
