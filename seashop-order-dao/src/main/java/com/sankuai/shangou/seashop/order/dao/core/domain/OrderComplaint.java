package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 订单投诉表
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
@TableName("order_complaint")
public class OrderComplaint implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 审核状态(1:等待供应商处理,2:供应商处理完成,3:等待平台介入,4:已结束)
     */
    @TableField("status")
    private Integer status;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 店铺联系方式
     */
    @TableField("shop_phone")
    private String shopPhone;

    /**
     * 商家id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 商家名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 商家联系方式
     */
    @TableField("user_phone")
    private String userPhone;

    /**
     * 投诉日期
     */
    @TableField("complaint_date")
    private Date complaintDate;

    /**
     * 投诉原因
     */
    @TableField("complaint_reason")
    private String complaintReason;

    /**
     * 店铺反馈信息
     */
    @TableField("seller_reply")
    private String sellerReply;

    /**
     * 投诉备注
     */
    @TableField("plat_remark")
    private String platRemark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopPhone() {
        return shopPhone;
    }

    public void setShopPhone(String shopPhone) {
        this.shopPhone = shopPhone;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getUserPhone() {
        return userPhone;
    }

    public void setUserPhone(String userPhone) {
        this.userPhone = userPhone;
    }

    public Date getComplaintDate() {
        return complaintDate;
    }

    public void setComplaintDate(Date complaintDate) {
        this.complaintDate = complaintDate;
    }

    public String getComplaintReason() {
        return complaintReason;
    }

    public void setComplaintReason(String complaintReason) {
        this.complaintReason = complaintReason;
    }

    public String getSellerReply() {
        return sellerReply;
    }

    public void setSellerReply(String sellerReply) {
        this.sellerReply = sellerReply;
    }

    public String getPlatRemark() {
        return platRemark;
    }

    public void setPlatRemark(String platRemark) {
        this.platRemark = platRemark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "Complaint{" +
        "id=" + id +
        ", orderId=" + orderId +
        ", status=" + status +
        ", shopId=" + shopId +
        ", shopName=" + shopName +
        ", shopPhone=" + shopPhone +
        ", userId=" + userId +
        ", userName=" + userName +
        ", userPhone=" + userPhone +
        ", complaintDate=" + complaintDate +
        ", complaintReason=" + complaintReason +
        ", sellerReply=" + sellerReply +
        ", platRemark=" + platRemark +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
