package com.sankuai.shangou.seashop.order.dao.finance.mapper;

import com.sankuai.shangou.seashop.order.dao.finance.domain.AccountDetail;
import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.order.dao.finance.model.AccountDetailCountModel;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 财务结算明细表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-12-06
 */
public interface AccountDetailMapper extends EnhancedMapper<AccountDetail> {

    /**
     * 明细统计
     *
     * @param shopId
     * @param detailId
     * @return
     */
    AccountDetailCountModel detailCount(@Param("shopId") Long shopId, @Param("detailId") Long detailId);
}
