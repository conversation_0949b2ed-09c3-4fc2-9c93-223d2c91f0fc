package com.sankuai.shangou.seashop.order.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 订单评价表
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
@TableName("order_comment")
public class OrderComment implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单id
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 用户id
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 用户名称
     */
    @TableField("user_name")
    private String userName;

    /**
     * 评价日期
     */
    @TableField("comment_date")
    private Date commentDate;

    /**
     * 包装评分
     */
    @TableField("pack_mark")
    private Integer packMark;

    /**
     * 物流评分
     */
    @TableField("delivery_mark")
    private Integer deliveryMark;

    /**
     * 服务评分
     */
    @TableField("service_mark")
    private Integer serviceMark;

    /**
     * 是否删除
     */
    @TableField("deleted")
    private Boolean deleted;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public Date getCommentDate() {
        return commentDate;
    }

    public void setCommentDate(Date commentDate) {
        this.commentDate = commentDate;
    }

    public Integer getPackMark() {
        return packMark;
    }

    public void setPackMark(Integer packMark) {
        this.packMark = packMark;
    }

    public Integer getDeliveryMark() {
        return deliveryMark;
    }

    public void setDeliveryMark(Integer deliveryMark) {
        this.deliveryMark = deliveryMark;
    }

    public Integer getServiceMark() {
        return serviceMark;
    }

    public void setServiceMark(Integer serviceMark) {
        this.serviceMark = serviceMark;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "OrderComment{" +
                "id=" + id +
                ", orderId='" + orderId + '\'' +
                ", shopId=" + shopId +
                ", shopName='" + shopName + '\'' +
                ", userId=" + userId +
                ", userName='" + userName + '\'' +
                ", commentDate=" + commentDate +
                ", packMark=" + packMark +
                ", deliveryMark=" + deliveryMark +
                ", serviceMark=" + serviceMark +
                ", deleted=" + deleted +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                '}';
    }
}
