package com.sankuai.shangou.seashop.order.dao.finance.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 保证金退款表
 * </p>
 *
 * <AUTHOR> @since 2023-11-28
 */
@TableName("finance_cash_deposit_refund")
public class CashDepositRefund implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("cash_deposit_detail_id")
    private Long cashDepositDetailId;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 状态 0，待审核；1，已通过；2，已拒绝；3，退款处理中；
     */
    @TableField("status")
    private Integer status;

    /**
     * 保证金
     */
    @TableField("bond")
    private BigDecimal bond;

    /**
     * 扣除金额
     */
    @TableField("deduction")
    private BigDecimal deduction;

    /**
     * 退款金额
     */
    @TableField("refund")
    private BigDecimal refund;

    /**
     * 申请时间
     */
    @TableField("apply_date")
    private Date applyDate;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 支付撤销处理ID
     */
    @TableField("refund_order_id")
    private String refundOrderId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCashDepositDetailId() {
        return cashDepositDetailId;
    }

    public void setCashDepositDetailId(Long cashDepositDetailId) {
        this.cashDepositDetailId = cashDepositDetailId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public BigDecimal getBond() {
        return bond;
    }

    public void setBond(BigDecimal bond) {
        this.bond = bond;
    }

    public BigDecimal getDeduction() {
        return deduction;
    }

    public void setDeduction(BigDecimal deduction) {
        this.deduction = deduction;
    }

    public BigDecimal getRefund() {
        return refund;
    }

    public void setRefund(BigDecimal refund) {
        this.refund = refund;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getRefundOrderId() {
        return refundOrderId;
    }

    public void setRefundOrderId(String refundOrderId) {
        this.refundOrderId = refundOrderId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CashDepositRefund{" +
                "id=" + id +
                ", cashDepositDetailId=" + cashDepositDetailId +
                ", shopId=" + shopId +
                ", status=" + status +
                ", bond=" + bond +
                ", deduction=" + deduction +
                ", refund=" + refund +
                ", applyDate=" + applyDate +
                ", remark=" + remark +
                ", refundOrderId=" + refundOrderId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
