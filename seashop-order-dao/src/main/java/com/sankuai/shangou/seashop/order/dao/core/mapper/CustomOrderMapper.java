package com.sankuai.shangou.seashop.order.dao.core.mapper;

import com.sankuai.shangou.seashop.order.dao.core.model.OrderStatisticsModel;
import com.sankuai.shangou.seashop.order.dao.core.po.OrderStatisticsPo;
import com.sankuai.shangou.seashop.order.dao.core.po.UpdateItemPricePo;
import com.sankuai.shangou.seashop.order.dao.core.po.UpdateReceiverPo;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 自定义订单mapper
 * </p>
 */
public interface CustomOrderMapper {


    int batchUpdateItemCouponAmount(List<UpdateItemPricePo> updateItemPricePoList);

    int updateOrderShipInfo(UpdateReceiverPo updateReceiverPo);

    int updateReceiveDelay(@Param("orderId") String orderId, @Param("receiveDelay") Integer receiveDelay);

    int updateOrderReceived(@Param("orderId") String orderId, @Param("originStatus") Integer originStatus,
                            @Param("targetStatus") Integer targetStatus, @Param("finishTime") Date finishTime,
                            @Param("closeReason") String closeReason);

    int updateLastModifyTime(@Param("orderId") String orderId, @Param("lastModifyTime") Date lastModifyTime);

    List<OrderStatisticsModel> orderStatistics(OrderStatisticsPo param);

    BigDecimal sumOrderAmount(OrderStatisticsPo param);

    int increaseApplyRefundQuantity(@Param("id") Long id, @Param("applyRefundQuantity") Long applyRefundQuantity);

    int decreaseApplyRefundQuantity(@Param("id") Long id, @Param("applyRefundQuantity") Long applyRefundQuantity);

    BigDecimal sumOrderAmountMember(OrderStatisticsPo param);
}
