package com.sankuai.shangou.seashop.order.dao.core.model;

import com.sankuai.shangou.seashop.order.dao.core.domain.OrderComplaint;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/15 14:30
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class OrderRefundModel {

    /**
     * 用户id
     */
    private Long userId;

    /**
     * in 状态集合
     */
    private List<Integer> inStatusList;

    /**
     * not in 状态集合
     */
    private List<Integer> notInStatusList;

    /**
     * 是否已经评论
     */
    private Integer hasCommented;
}
