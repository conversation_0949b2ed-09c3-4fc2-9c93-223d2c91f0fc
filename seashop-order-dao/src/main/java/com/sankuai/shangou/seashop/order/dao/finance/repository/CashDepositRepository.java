package com.sankuai.shangou.seashop.order.dao.finance.repository;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.order.common.enums.CashDepositSortEnum;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDeposit;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.CashDepositMapper;
import com.sankuai.shangou.seashop.order.dao.finance.model.ShopIdListModel;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Repository
public class CashDepositRepository extends ServiceImpl<CashDepositMapper, CashDeposit> {

    public BasePageResp<CashDeposit> queryByShopIdList(BasePageParam paramPage, ShopIdListModel param) {

        Page page = PageHelper.startPage(paramPage);

        // 店铺id列表
        LambdaQueryWrapper<CashDeposit> queryWrapper = new LambdaQueryWrapper<>();
        if (CollUtil.isNotEmpty(param.getShopIdList())) {
            queryWrapper.in(CashDeposit::getShopId, param.getShopIdList());
        }

        // 排序数组
        List<FieldSortReq> sortList = param.getSortList();
        if (CollUtil.isNotEmpty(sortList)) {
            for (FieldSortReq fieldSortReq : sortList) {
                String sort = fieldSortReq.getSort();
                if (CashDepositSortEnum.CURRENT_BALANCE.getSort().equals(sort)) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), CashDeposit::getCurrentBalance);
                }
                if (CashDepositSortEnum.TOTAL_BALANCE.getSort().equals(sort)) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), CashDeposit::getTotalBalance);
                }
                if (CashDepositSortEnum.DATE.getSort().equals(sort)) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), CashDeposit::getDate);
                }
            }
        } else {
            queryWrapper.orderByDesc(CashDeposit::getShopId);
        }
        page.doSelectPage(() -> this.list(queryWrapper));
        return PageResultHelper.transfer(page, CashDeposit.class);
    }

    public CashDeposit queryOneByShopId(Long shopId) {
        LambdaQueryWrapper<CashDeposit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashDeposit::getShopId, shopId);
        return this.getOne(queryWrapper);
    }

    public List<CashDeposit> queryByShopIdList(List<Long> shopIdList) {
        LambdaQueryWrapper<CashDeposit> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CashDeposit::getShopId, shopIdList);
        return this.list(queryWrapper);
    }

    public int insert(CashDeposit cashDeposit){
        return this.baseMapper.insert(cashDeposit);
    }
}
