package com.sankuai.shangou.seashop.order.dao.finance.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Account;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.AccountMapper;
import org.springframework.stereotype.Repository;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@Repository
public class AccountRepository extends ServiceImpl<AccountMapper, Account> {

    /**
     * 获取最新的一条数据
     */
    public Account getLatestOne() {
        LambdaQueryWrapper<Account> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(Account::getId).last("limit 1");
        return this.getOne(queryWrapper);
    }

}
