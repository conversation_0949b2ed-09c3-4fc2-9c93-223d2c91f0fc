package com.sankuai.shangou.seashop.order.dao.core.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.order.dao.core.domain.ProductComment;
import com.sankuai.shangou.seashop.order.dao.core.model.ReviewMarkModel;

/**
 * <p>
 * 订单商品评论表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-12-07
 */
public interface ProductCommentMapper extends EnhancedMapper<ProductComment> {

    /**
     * 根据商品id 查询评分集合
     *
     * @return 商品评分集合
     */
    List<ReviewMarkModel> listReviewMark(@Param("productId") Long productId);
}
