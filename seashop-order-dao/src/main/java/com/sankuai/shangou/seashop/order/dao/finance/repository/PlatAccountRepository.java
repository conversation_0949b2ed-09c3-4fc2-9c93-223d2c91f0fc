package com.sankuai.shangou.seashop.order.dao.finance.repository;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.finance.domain.PlatAccount;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.PlatAccountMapper;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PlatAccountExtMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/2/002
 * @description:
 */
@Repository
public class PlatAccountRepository extends ServiceImpl<PlatAccountMapper, PlatAccount> {

    @Resource
    private PlatAccountExtMapper platAccountExtMapper;

    public PlatAccount getPlatAccount() {
        PlatAccount platAccount;
        List<PlatAccount> platAccounts = this.list();
        if (CollUtil.isNotEmpty(platAccounts)) {
            platAccount = platAccounts.get(0);
        } else {
            platAccount = new PlatAccount();
            platAccount.setSettled(BigDecimal.ZERO);
            platAccount.setPendingSettlement(BigDecimal.ZERO);
            // 平台账户信息，有且只有一个，如果没有，则初始化一个
            this.save(platAccount);
        }
        return platAccount;
    }

    public int updatePendingSettlement(BigDecimal amount) {
        PlatAccount platAccount = getPlatAccount();
        return platAccountExtMapper.updatePendingSettlement(platAccount.getId(), amount);
    }
}
