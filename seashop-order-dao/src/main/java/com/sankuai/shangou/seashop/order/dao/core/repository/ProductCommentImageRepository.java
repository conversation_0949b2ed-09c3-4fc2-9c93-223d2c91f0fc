package com.sankuai.shangou.seashop.order.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.order.dao.core.domain.ProductCommentImage;
import com.sankuai.shangou.seashop.order.dao.core.mapper.ProductCommentImageMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/04 10:38
 */
@Repository
@Slf4j
public class ProductCommentImageRepository extends ServiceImpl<ProductCommentImageMapper, ProductCommentImage> {

    /**
     * 根据商品评价id查询图片
     *
     * @param productCommentIdList 商品评价id的集合
     * @return 商品评价图片
     */
    public List<ProductCommentImage> listByProductCommentIdList(List<Long> productCommentIdList) {
        if (CollectionUtils.isEmpty(productCommentIdList)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<ProductCommentImage>()
                .in(ProductCommentImage::getProductCommentId, ids)), productCommentIdList);
    }

    public void deleteProductImage(List<Long> productCommentIdList, Integer commentType) {
        if (CollectionUtils.isEmpty(productCommentIdList)) {
            return;
        }

        productCommentIdList = productCommentIdList.stream().distinct().collect(Collectors.toList());
        MybatisUtil.executeBatch(commentIds -> {
            remove(new LambdaQueryWrapper<ProductCommentImage>()
                    .in(ProductCommentImage::getProductCommentId, commentIds)
                    .eq(commentType != null, ProductCommentImage::getCommentType, commentType));
        }, productCommentIdList);
    }
}
