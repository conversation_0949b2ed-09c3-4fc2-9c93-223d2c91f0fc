package com.sankuai.shangou.seashop.order.dao.finance.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositPay;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.CashDepositPayMapper;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description:
 */
@Repository
public class CashDepositPayRepository extends ServiceImpl<CashDepositPayMapper, CashDepositPay> {

    public CashDepositPay getByPayId(String payId) {
        return lambdaQuery().eq(CashDepositPay::getPayId, payId).one();
    }

    public List<CashDepositPay> queryByPayIdList(List<String> payIdList) {
        if(CollectionUtils.isEmpty(payIdList) || payIdList.size() == 0){
            return Lists.newArrayList();
        }
        return lambdaQuery().in(CashDepositPay::getPayId, payIdList).list();
    }
}
