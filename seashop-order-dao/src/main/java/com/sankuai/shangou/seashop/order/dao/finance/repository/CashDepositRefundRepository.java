package com.sankuai.shangou.seashop.order.dao.finance.repository;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.enums.CashDepositRefundSortEnum;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositRefund;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.CashDepositRefundMapper;
import com.sankuai.shangou.seashop.order.dao.finance.model.CashDepositRefundQueryModel;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description:
 */
@Repository
public class CashDepositRefundRepository extends ServiceImpl<CashDepositRefundMapper, CashDepositRefund> {

    public List<CashDepositRefund> selectCashDepositRefundByDetailIds(List<Long> detailIds) {
        LambdaQueryWrapper<CashDepositRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CashDepositRefund::getCashDepositDetailId, detailIds);
        queryWrapper.ne(CashDepositRefund::getStatus, 2);
        List<CashDepositRefund> result =  this.list(queryWrapper);
        return JsonUtil.copyList(result, CashDepositRefund.class);
    }

    /**
     * 退款申请分页查询
     *
     * @param paramPage
     * @param param
     * @return
     */
    public BasePageResp<CashDepositRefund> pageList(BasePageParam paramPage, CashDepositRefundQueryModel param) {
        Page page = PageHelper.startPage(paramPage);
        LambdaQueryWrapper<CashDepositRefund> queryWrapper = new LambdaQueryWrapper<>();
        if (CollUtil.isNotEmpty(param.getShopIdList())) {
            queryWrapper.in(CashDepositRefund::getShopId, param.getShopIdList());
        }
        if (null != param.getStatus()) {
            queryWrapper.eq(CashDepositRefund::getStatus, param.getStatus());
        }
        if (CollUtil.isNotEmpty(param.getSortList())) {
            for (FieldSortReq fieldSortReq : param.getSortList()) {
                if (CashDepositRefundSortEnum.APPLY_DATE.getSort().equals(fieldSortReq.getSort())) {
                    queryWrapper.orderBy(true, fieldSortReq.getIzAsc(), CashDepositRefund::getApplyDate);
                }
            }
        }
        queryWrapper.orderByDesc(CashDepositRefund::getId);
        page.doSelectPage(() -> this.list(queryWrapper));
        return PageResultHelper.transfer(page, CashDepositRefund.class);
    }

    /**
     * 通过refundOrderId查询保证金退款记录
     *
     * @param refundOrderId
     * @return
     */
    public CashDepositRefund queryByRefundOrderId(String refundOrderId) {
        LambdaQueryWrapper<CashDepositRefund> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CashDepositRefund::getRefundOrderId, refundOrderId);
        return this.getOne(queryWrapper);
    }
}
