package com.sankuai.shangou.seashop.order.dao.core.po;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * 通用的订单查询参数
 * <p>Notice: 主要用于一些定时任务等非正常业务场景，正常业务可能越权慎用</p>
 * <AUTHOR>
 */
@Getter
@Setter
@Builder
public class CommonOrderQueryParamBo {

    /**
     * 订单状态：=
     */
    private Integer orderStatusEq;
    /**
     * 订单状态：in
     */
    private List<Integer> orderStatusIn;
    /**
     * 下单时间: <=
     */
    private Date orderDateLe;
    /**
     * 下单时间: <
     */
    private Date orderDateLt;
    /**
     * 下单时间：>=
     */
    private Date orderDateGe;
    /**
     * 是否发送短信：=
     */
    private Boolean isSendEq;
    /**
     * 商家寄货时间：<
     */
    private Date shippingDateLt;
    /**
     * 主键ID：>=
     */
    private Long idGe;
    /**
     * 主键ID：<=
     */
    private Long idLe;
    /**
     * 更新时间：>=
     */
    private Date updateTimeGe;
    /**
     * 更新时间：<=
     */
    private Date updateTimeLe;
    /**
     * 店铺ID
     */
    private Long shopId;

}
