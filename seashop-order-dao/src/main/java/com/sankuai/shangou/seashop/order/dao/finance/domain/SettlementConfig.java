package com.sankuai.shangou.seashop.order.dao.finance.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 结算配置表
 * </p>
 *
 * <AUTHOR> @since 2023-12-04
 */
@TableName("finance_settlement_config")
public class SettlementConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 当前结算周期(天)
     */
    @TableField("settlement_interval")
    private Integer settlementInterval;

    /**
     * 结算手续费率（%）
     */
    @TableField("settlement_fee_rate")
    private BigDecimal settlementFeeRate;

    /**
     * 微信结算手续费率（%）
     */
    @TableField("wx_fee_rate")
    private BigDecimal wxFeeRate;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSettlementInterval() {
        return settlementInterval;
    }

    public void setSettlementInterval(Integer settlementInterval) {
        this.settlementInterval = settlementInterval;
    }

    public BigDecimal getSettlementFeeRate() {
        return settlementFeeRate;
    }

    public void setSettlementFeeRate(BigDecimal settlementFeeRate) {
        this.settlementFeeRate = settlementFeeRate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public BigDecimal getWxFeeRate() {
        return wxFeeRate;
    }

    public void setWxFeeRate(BigDecimal wxFeeRate) {
        this.wxFeeRate = wxFeeRate;
    }

    @Override
    public String toString() {
        return "SettlementConfig{" +
        "id=" + id +
        ", settlementInterval=" + settlementInterval +
        ", settlementFeeRate=" + settlementFeeRate +
                ", wxFeeRate=" + wxFeeRate +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
