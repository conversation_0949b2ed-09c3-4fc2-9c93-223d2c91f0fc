package com.sankuai.shangou.seashop.pay.dao.core.model;

import lombok.*;

/**
 * @author: lhx
 * @date: 2023/11/20/020
 * @description: 汇付支付配置参数对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Getter
@Setter
@Builder
@ToString
public class AdaPayConfigModel {

    private Boolean izOpen;

    private String appId;

    private String apiKey;

    private String apiMockKey;

    private String rsaPrivateKey;

    private String rsaProductKey;

    private String deviceId;

    private Boolean izDebug;

    private Boolean prodMode;

    public static final String IZ_OPEN = "izOpen";
    public static final String APP_ID = "appId";
    public static final String API_KEY = "apiKey";
    public static final String API_MOCK_KEY = "apiMockKey";
    public static final String RSA_PRIVATE_KEY = "rsaPrivateKey";
    public static final String RSA_PRODUCT_KEY = "rsaProductKey";
    public static final String DEVICE_ID = "deviceId";
    public static final String IZ_DEBUG = "izDebug";
    public static final String PROD_MODE = "prodMode";
}
