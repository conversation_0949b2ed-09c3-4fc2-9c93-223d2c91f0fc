package com.sankuai.shangou.seashop.order.dao.core.repository;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderInvoice;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderInvoiceMapper;

/**
 * <AUTHOR>
 */
@Repository
public class OrderInvoiceRepository extends ServiceImpl<OrderInvoiceMapper, OrderInvoice> {

    public List<OrderInvoice> getByOrderIdList(List<String> orderIdList) {
        LambdaQueryWrapper<OrderInvoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderInvoice::getOrderId, orderIdList);
        return baseMapper.selectList(wrapper);
    }

    public OrderInvoice getByOrderId(String orderId) {
        LambdaQueryWrapper<OrderInvoice> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderInvoice::getOrderId, orderId);
        return baseMapper.selectOne(wrapper);
    }
}
