package com.sankuai.shangou.seashop.order.dao.finance.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 财务中间项表
 * </p>
 *
 * <AUTHOR> @since 2023-12-02
 */
@TableName("finance_finance_item")
public class FinanceItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 中间表ID
     */
    @TableField("finance_id")
    private Long financeId;

    /**
     * 订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 商品ID
     */
    @TableField("product_id")
    private Long productId;

    /**
     * 商品名称
     */
    @TableField("product_name")
    private String productName;

    /**
     * SKU
     */
    @TableField("sku")
    private String sku;

    /**
     * 购买数量/退货数量
     */
    @TableField("quantity")
    private Long quantity;

    /**
     * 原价
     */
    @TableField("original_price")
    private BigDecimal originalPrice;

    /**
     * 售价
     */
    @TableField("sale_price")
    private BigDecimal salePrice;

    /**
     * 实际应付金额
     */
    @TableField("total_amount")
    private BigDecimal totalAmount;

    /**
     * 优惠金额
     */
    @TableField("discount_price")
    private BigDecimal discountPrice;

    /**
     * 满额减平摊到订单项的金额
     */
    @TableField("full_discount")
    private BigDecimal fullDiscount;

    /**
     * 满减活动平摊到订单项金额
     */
    @TableField("money_off")
    private BigDecimal moneyOff;

    /**
     * 优惠券抵扣金额
     */
    @TableField("coupon_discount")
    private BigDecimal couponDiscount;

    /**
     * 平台优惠券抵扣金额
     */
    @TableField("plat_coupon_discount")
    private BigDecimal platCouponDiscount;

    /**
     * 抽佣比例
     */
    @TableField("commis_rate")
    private BigDecimal commisRate;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFinanceId() {
        return financeId;
    }

    public void setFinanceId(Long financeId) {
        this.financeId = financeId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getSku() {
        return sku;
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public Long getQuantity() {
        return quantity;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    public BigDecimal getSalePrice() {
        return salePrice;
    }

    public void setSalePrice(BigDecimal salePrice) {
        this.salePrice = salePrice;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getDiscountPrice() {
        return discountPrice;
    }

    public void setDiscountPrice(BigDecimal discountPrice) {
        this.discountPrice = discountPrice;
    }

    public BigDecimal getFullDiscount() {
        return fullDiscount;
    }

    public void setFullDiscount(BigDecimal fullDiscount) {
        this.fullDiscount = fullDiscount;
    }

    public BigDecimal getMoneyOff() {
        return moneyOff;
    }

    public void setMoneyOff(BigDecimal moneyOff) {
        this.moneyOff = moneyOff;
    }

    public BigDecimal getCouponDiscount() {
        return couponDiscount;
    }

    public void setCouponDiscount(BigDecimal couponDiscount) {
        this.couponDiscount = couponDiscount;
    }

    public BigDecimal getPlatCouponDiscount() {
        return platCouponDiscount;
    }

    public void setPlatCouponDiscount(BigDecimal platCouponDiscount) {
        this.platCouponDiscount = platCouponDiscount;
    }

    public BigDecimal getCommisRate() {
        return commisRate;
    }

    public void setCommisRate(BigDecimal commisRate) {
        this.commisRate = commisRate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "FinanceItem{" +
        "id=" + id +
        ", financeId=" + financeId +
        ", orderId=" + orderId +
        ", productId=" + productId +
        ", productName=" + productName +
        ", sku=" + sku +
        ", quantity=" + quantity +
        ", originalPrice=" + originalPrice +
        ", salePrice=" + salePrice +
        ", totalAmount=" + totalAmount +
        ", discountPrice=" + discountPrice +
        ", fullDiscount=" + fullDiscount +
        ", moneyOff=" + moneyOff +
        ", couponDiscount=" + couponDiscount +
        ", platCouponDiscount=" + platCouponDiscount +
        ", commisRate=" + commisRate +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
