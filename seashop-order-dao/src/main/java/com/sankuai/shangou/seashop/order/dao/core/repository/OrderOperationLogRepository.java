package com.sankuai.shangou.seashop.order.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderOperationLog;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderOperationLogMapper;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class OrderOperationLogRepository extends ServiceImpl<OrderOperationLogMapper, OrderOperationLog> {

    public void save(String orderId, String userName, String operationContent) {
        OrderOperationLog orderOperationLog = new OrderOperationLog();
        orderOperationLog.setOperator(userName);
        orderOperationLog.setOrderId(orderId);
        orderOperationLog.setOperateDate(new Date());
        orderOperationLog.setOperateContent(operationContent);
        orderOperationLog.setCreateTime(new Date());
        orderOperationLog.setUpdateTime(new Date());
        this.baseMapper.insert(orderOperationLog);
    }

    public List<OrderOperationLog> getByOrderId(String orderId) {
        LambdaQueryWrapper<OrderOperationLog> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderOperationLog::getOrderId, orderId)
                .orderByDesc(OrderOperationLog::getOperateDate);
        return this.baseMapper.selectList(wrapper);
    }

}
