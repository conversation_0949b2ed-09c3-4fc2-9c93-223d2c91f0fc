package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 订单发票信息
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
@TableName("order_invoice")
public class OrderInvoice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
     */
    @TableField("invoice_type")
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    @TableField("invoice_title")
    private String invoiceTitle;

    /**
     * 税号
     */
    @TableField("invoice_code")
    private String invoiceCode;

    /**
     * 发票内容(发票明细、商品类别)
     */
    @TableField("invoice_context")
    private String invoiceContext;

    /**
     * 注册地址
     */
    @TableField("register_address")
    private String registerAddress;

    /**
     * 注册电话
     */
    @TableField("register_phone")
    private String registerPhone;

    /**
     * 开户银行
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 银行帐号
     */
    @TableField("bank_no")
    private String bankNo;

    /**
     * 收票人姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 收票人手机号
     */
    @TableField("cell_phone")
    private String cellPhone;

    /**
     * 收票人邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 收票人地址区域id
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 收票人详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 订单完成后多少天开具增值税发票
     */
    @TableField("vat_invoice_day")
    private Integer vatInvoiceDay;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getInvoiceTitle() {
        return invoiceTitle;
    }

    public void setInvoiceTitle(String invoiceTitle) {
        this.invoiceTitle = invoiceTitle;
    }

    public String getInvoiceCode() {
        return invoiceCode;
    }

    public void setInvoiceCode(String invoiceCode) {
        this.invoiceCode = invoiceCode;
    }

    public String getInvoiceContext() {
        return invoiceContext;
    }

    public void setInvoiceContext(String invoiceContext) {
        this.invoiceContext = invoiceContext;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public String getRegisterPhone() {
        return registerPhone;
    }

    public void setRegisterPhone(String registerPhone) {
        this.registerPhone = registerPhone;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getVatInvoiceDay() {
        return vatInvoiceDay;
    }

    public void setVatInvoiceDay(Integer vatInvoiceDay) {
        this.vatInvoiceDay = vatInvoiceDay;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "Invoice{" +
        "id=" + id +
        ", orderId=" + orderId +
        ", invoiceType=" + invoiceType +
        ", invoiceTitle=" + invoiceTitle +
        ", invoiceCode=" + invoiceCode +
        ", invoiceContext=" + invoiceContext +
        ", registerAddress=" + registerAddress +
        ", registerPhone=" + registerPhone +
        ", bankName=" + bankName +
        ", bankNo=" + bankNo +
        ", realName=" + realName +
        ", cellPhone=" + cellPhone +
        ", email=" + email +
        ", regionId=" + regionId +
        ", address=" + address +
        ", vatInvoiceDay=" + vatInvoiceDay +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
