package com.sankuai.shangou.seashop.order.dao.core.repository;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.core.domain.ThirdNotice;
import com.sankuai.shangou.seashop.order.dao.core.mapper.ThirdNoticeMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/01/29 17:54
 */
@Repository
@Slf4j
public class ThirdNoticeRepository extends ServiceImpl<ThirdNoticeMapper, ThirdNotice> {

    public ThirdNotice getByNoticeCode(String noticeCode) {
        return getOne(new LambdaQueryWrapper<ThirdNotice>().eq(ThirdNotice::getNoticeCode, noticeCode));
    }

}
