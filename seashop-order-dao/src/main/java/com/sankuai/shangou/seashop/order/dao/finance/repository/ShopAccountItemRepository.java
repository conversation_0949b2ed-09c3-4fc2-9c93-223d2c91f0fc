package com.sankuai.shangou.seashop.order.dao.finance.repository;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.request.FieldSortReq;
import com.sankuai.shangou.seashop.order.common.enums.ShopAccountItemSortEnum;
import com.sankuai.shangou.seashop.order.dao.finance.domain.ShopAccountItem;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.ShopAccountItemMapper;
import com.sankuai.shangou.seashop.order.dao.finance.model.SettledQryModel;
import org.springframework.stereotype.Repository;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@Repository
public class ShopAccountItemRepository extends ServiceImpl<ShopAccountItemMapper, ShopAccountItem> {

    public Page<ShopAccountItem> pageList(BasePageParam basePageParam, SettledQryModel qryModel) {
        Page<ShopAccountItem> page = PageHelper.startPage(basePageParam);
        LambdaQueryWrapper<ShopAccountItem> queryWrapper = new LambdaQueryWrapper<>();
        if (null != qryModel.getShopId()) {
            queryWrapper.eq(ShopAccountItem::getShopId, qryModel.getShopId());
        }
        if (null != qryModel.getShopIdList() && qryModel.getShopIdList().size() > 0) {
            queryWrapper.in(ShopAccountItem::getShopId, qryModel.getShopIdList());
        }
        if (null != qryModel.getStartSettlementTime() && null != qryModel.getEndSettlementTime()) {
            queryWrapper.between(ShopAccountItem::getCreateTime, qryModel.getStartSettlementTime(), qryModel.getEndSettlementTime());
        }
        if (null != qryModel.getStartSettlementTime() && null == qryModel.getEndSettlementTime()) {
            queryWrapper.ge(ShopAccountItem::getCreateTime, qryModel.getStartSettlementTime());
        }
        if (null == qryModel.getStartSettlementTime() && null != qryModel.getEndSettlementTime()) {
            queryWrapper.le(ShopAccountItem::getCreateTime, qryModel.getEndSettlementTime());
        }
        if (null != qryModel.getIncomeFlag()) {
            queryWrapper.eq(ShopAccountItem::getIncomeFlag, qryModel.getIncomeFlag());
        }
        if(CollUtil.isNotEmpty(qryModel.getSortList())){
            for(FieldSortReq fieldSortReq : qryModel.getSortList()){
               if(ShopAccountItemSortEnum.SETTLEMENT_TIME.getSort().equals(fieldSortReq.getSort())){
                   queryWrapper.orderBy(true,fieldSortReq.getIzAsc(),ShopAccountItem::getCreateTime);
               }
                if(ShopAccountItemSortEnum.SETTLEMENT_AMOUNT.getSort().equals(fieldSortReq.getSort())){
                     queryWrapper.orderBy(true,fieldSortReq.getIzAsc(),ShopAccountItem::getAmount);
                }
            }
        }else{
            queryWrapper.orderByDesc(ShopAccountItem::getCreateTime);
        }
        page.doSelectPage(() -> this.list(queryWrapper));
        return page;
    }

}
