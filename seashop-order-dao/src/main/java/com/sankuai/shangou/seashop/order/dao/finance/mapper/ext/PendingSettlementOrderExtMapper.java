package com.sankuai.shangou.seashop.order.dao.finance.mapper.ext;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.sankuai.shangou.seashop.order.dao.finance.model.PendingSettlementModel;
import com.sankuai.shangou.seashop.order.dao.finance.model.PendingSettlementOrderQryModel;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
public interface PendingSettlementOrderExtMapper {

    /**
     * 查询待结算订单总金额
     *
     * @param model
     * @return
     */
    BigDecimal sumSettlementAmount(PendingSettlementOrderQryModel model);

    /**
     * 获取平台佣金总额
     *
     * @return 平台佣金总额
     */
    BigDecimal getPlatCommission();


    /**
     * 查询待结算列表
     *
     * @param page
     * @param model
     * @return
     */
    Page<PendingSettlementModel> getPendingSettlementList(Page page, @Param("query") PendingSettlementOrderQryModel model);

    /**
     * 查询待结算列表
     *
     * @param model
     * @return
     */
    List<PendingSettlementModel> getPendingSettlementList(@Param("query") PendingSettlementOrderQryModel model);
}
