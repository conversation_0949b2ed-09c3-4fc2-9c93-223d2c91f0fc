package com.sankuai.shangou.seashop.order.dao.core.model;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/6/006
 * @description:
 */
@Data
public class OrderStatisticsModel {
    /**
     * SELECT
     * 	DATE( pay_date ),
     * 	ifnull( sum( product_total_amount ), 0 ),
     * 	ifnull( sum( freight ), 0 ),
     * 	ifnull( sum( tax ), 0 ),
     * 	ifnull( sum( coupon_amount ), 0 ),
     * 	ifnull( sum( discount_amount ), 0 ),
     * 	ifnull( sum( money_off_amount ), 0 )
     * FROM
     * 	`order`
     * WHERE
     * 	pay_date BETWEEN '2023-11-01 00:00:00'
     * 	AND '2023-12-06 00:00:00'
     * GROUP BY
     * 	DATE(pay_date)
     */

    private String payDate;

    private BigDecimal productTotalAmount;

    private BigDecimal freight;

    private BigDecimal tax;

    private BigDecimal couponAmount;

    private BigDecimal discountAmount;

    private BigDecimal moneyOffAmount;
}
