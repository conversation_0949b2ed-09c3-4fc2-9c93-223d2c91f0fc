package com.sankuai.shangou.seashop.order.dao.core.model;

import com.facebook.swift.codec.ThriftStruct;
import lombok.*;

/**
 * @author: lhx
 * @date: 2024/1/5/005
 * @description: 统计完成订单和商品数量返回值
 */
@ThriftStruct
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class CountFlashOrderAndProductModel {

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 完成订单数量
     */
    private Long countFlashOrder;

    /**
     * 完成商品数量
     */
    private Long countFlashProduct;

}
