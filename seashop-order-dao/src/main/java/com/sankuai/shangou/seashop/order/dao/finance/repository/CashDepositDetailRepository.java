package com.sankuai.shangou.seashop.order.dao.finance.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.order.common.enums.CashDepositOperatorTypeEnum;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositDetail;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.CashDepositDetailMapper;
import com.sankuai.shangou.seashop.order.dao.finance.model.CashDepositDetailQueryModel;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@Repository
public class CashDepositDetailRepository extends ServiceImpl<CashDepositDetailMapper, CashDepositDetail> {

    @Resource
    @Lazy
    private CashDepositRepository cashDepositRepository;

    public List<CashDepositDetail> getPayListByCashDepositId(Long id) {
        return this.list(
                lambdaQuery()
                        .eq(CashDepositDetail::getCashDepositId, id)
                        .eq(CashDepositDetail::getOperatorType, CashDepositOperatorTypeEnum.PAY.getType())
                        .orderByDesc(CashDepositDetail::getAddDate)
        );
    }

    public BasePageResp<CashDepositDetail> pageList(BasePageParam paramPage, CashDepositDetailQueryModel param) {
        Page page = PageHelper.startPage(paramPage);
        LambdaQueryWrapper<CashDepositDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (null != param.getCashDepositId()) {
            queryWrapper.eq(CashDepositDetail::getCashDepositId, param.getCashDepositId());
        }
        if (StrUtil.isNotBlank(param.getOperator())) {
            queryWrapper.like(CashDepositDetail::getOperator, param.getOperator());
        }
        if (null != param.getStartTime() && null != param.getEndTime()) {
            queryWrapper.between(CashDepositDetail::getAddDate, param.getStartTime(), param.getEndTime());
        }
        if (null != param.getStartTime() && null == param.getEndTime()) {
            queryWrapper.ge(CashDepositDetail::getAddDate, param.getStartTime());
        }
        if (null == param.getStartTime() && null != param.getEndTime()) {
            queryWrapper.le(CashDepositDetail::getAddDate, param.getEndTime());
        }
        queryWrapper.orderByDesc(CashDepositDetail::getAddDate);
        page.doSelectPage(() -> this.list(queryWrapper));
        return PageResultHelper.transfer(page, CashDepositDetail.class);
    }
}
