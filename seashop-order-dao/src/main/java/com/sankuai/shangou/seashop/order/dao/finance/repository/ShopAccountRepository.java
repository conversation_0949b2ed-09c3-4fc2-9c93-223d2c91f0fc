package com.sankuai.shangou.seashop.order.dao.finance.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.finance.domain.ShopAccount;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.ShopAccountMapper;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.ShopAccountExtMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Repository
public class ShopAccountRepository extends ServiceImpl<ShopAccountMapper, ShopAccount> {

    @Resource
    private ShopAccountExtMapper shopAccountExtMapper;

    public ShopAccount getShopAccount(Long shopId) {
        LambdaQueryWrapper<ShopAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopAccount::getShopId, shopId);
        return this.getOne(queryWrapper);
    }

    /**
     * 通过店铺Id列表查询店铺账户列表
     */
    public List<ShopAccount> getListByShopIdList(List<Long> shopIdList) {
        LambdaQueryWrapper<ShopAccount> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ShopAccount::getShopId, shopIdList);
        return this.list(queryWrapper);
    }

    /**
     * 更新店铺待结算金额
     *
     * @param shopId
     * @param amount
     * @return
     */
    public int updatePendingSettlement(Long shopId, BigDecimal amount) {
        return shopAccountExtMapper.updatePendingSettlement(shopId, amount);
    }
}
