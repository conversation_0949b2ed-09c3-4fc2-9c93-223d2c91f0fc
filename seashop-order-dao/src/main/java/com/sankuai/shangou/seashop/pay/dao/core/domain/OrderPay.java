package com.sankuai.shangou.seashop.pay.dao.core.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 订单支付记录表
 * </p>
 *
 * <AUTHOR> @since 2023-11-20
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@TableName("pay_order_pay")
public class OrderPay implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 支付流水ID
     */
    @TableField("pay_id")
    private String payId;

    /**
     * 来源订单ID
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 支付金额
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 支付状态  0: 未支付, 1: 支付成功, 2: 支付失败
     */
    @TableField("pay_state")
    private Integer payState;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private Date payTime;

    /**
     * 支付渠道 1：汇付天下
     */
    @TableField("payment_channel")
    private Integer paymentChannel;

    /**
     * 支付方式 1: 支付宝扫码，2: 支付宝H5，3: 微信小程序，4: 微信H5，5: 企业网银，6: 个人网银
     */
    @TableField("payment_type")
    private Integer paymentType;

    /**
     * 业务类型 1：订单,4：保证金
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 银行编码
     */
    @TableField("bank_code")
    private String bankCode;

    /**
     * 渠道确认ID(分账返回ID)
     */
    @TableField("channel_confirm_id")
    private String channelConfirmId;

    /**
     * 渠道支付ID
     */
    @TableField("channel_pay_id")
    private String channelPayId;

    /**
     * 错误描述
     */
    @TableField("channel_pay_msg")
    private String channelPayMsg;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public Integer getPayState() {
        return payState;
    }

    public void setPayState(Integer payState) {
        this.payState = payState;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public Integer getPaymentChannel() {
        return paymentChannel;
    }

    public void setPaymentChannel(Integer paymentChannel) {
        this.paymentChannel = paymentChannel;
    }

    public Integer getPaymentType() {
        return paymentType;
    }

    public void setPaymentType(Integer paymentType) {
        this.paymentType = paymentType;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getChannelConfirmId() {
        return channelConfirmId;
    }

    public void setChannelConfirmId(String channelConfirmId) {
        this.channelConfirmId = channelConfirmId;
    }

    public String getChannelPayId() {
        return channelPayId;
    }

    public void setChannelPayId(String channelPayId) {
        this.channelPayId = channelPayId;
    }

    public String getChannelPayMsg() {
        return channelPayMsg;
    }

    public void setChannelPayMsg(String channelPayMsg) {
        this.channelPayMsg = channelPayMsg;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "OrderPay{" +
                "id=" + id +
                ", payId=" + payId +
                ", orderId=" + orderId +
                ", payAmount=" + payAmount +
                ", payState=" + payState +
                ", payTime=" + payTime +
                ", paymentChannel=" + paymentChannel +
                ", paymentType=" + paymentType +
                ", businessType=" + businessType +
                ", bankCode=" + bankCode +
                ", channelConfirmId=" + channelConfirmId +
                ", channelPayId=" + channelPayId +
                ", channelPayMsg=" + channelPayMsg +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
