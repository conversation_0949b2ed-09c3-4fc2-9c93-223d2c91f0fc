package com.sankuai.shangou.seashop.pay.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 退款订单表
 * </p>
 *
 * <AUTHOR> @since 2023-11-23
 */
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("pay_reverse_order")
public class ReverseOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 渠道支付ID
     */
    @TableField("channel_pay_id")
    private String channelPayId;

    /**
     * 退款流水ID
     */
    @TableField("reverse_id")
    private String reverseId;

    /**
     * 退款金额
     */
    @TableField("reverse_amount")
    private BigDecimal reverseAmount;

    /**
     * 退款状态 0: 退款中,1: 退款成功,2: 退款失败
     */
    @TableField("reverse_state")
    private Integer reverseState;

    /**
     * 退款时间
     */
    @TableField("reverse_time")
    private Date reverseTime;

    /**
     * 渠道退款ID
     */
    @TableField("channel_refund_id")
    private String channelRefundId;

    /**
     * 错误描述
     */
    @TableField("channel_refund_msg")
    private String channelRefundMsg;

    /**
     * 退款类型 0：未结算退款；1：已结算退款
     */
    @TableField("reverse_type")
    private Integer reverseType;

    /**
     * 业务类型 1：订单,4：保证金
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 业务状态类型：1：正常订单退款；2：异常订单退款；3：订单补偿退款（支付金额大于订单金额）
     */
    @TableField("business_status_type")
    private Integer businessStatusType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",exist = false)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",exist = false)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getChannelPayId() {
        return channelPayId;
    }

    public void setChannelPayId(String channelPayId) {
        this.channelPayId = channelPayId;
    }

    public String getReverseId() {
        return reverseId;
    }

    public void setReverseId(String reverseId) {
        this.reverseId = reverseId;
    }

    public BigDecimal getReverseAmount() {
        return reverseAmount;
    }

    public void setReverseAmount(BigDecimal reverseAmount) {
        this.reverseAmount = reverseAmount;
    }

    public Integer getReverseState() {
        return reverseState;
    }

    public void setReverseState(Integer reverseState) {
        this.reverseState = reverseState;
    }

    public Date getReverseTime() {
        return reverseTime;
    }

    public void setReverseTime(Date reverseTime) {
        this.reverseTime = reverseTime;
    }

    public String getChannelRefundId() {
        return channelRefundId;
    }

    public void setChannelRefundId(String channelRefundId) {
        this.channelRefundId = channelRefundId;
    }

    public String getChannelRefundMsg() {
        return channelRefundMsg;
    }

    public void setChannelRefundMsg(String channelRefundMsg) {
        this.channelRefundMsg = channelRefundMsg;
    }

    public Integer getReverseType() {
        return reverseType;
    }

    public void setReverseType(Integer reverseType) {
        this.reverseType = reverseType;
    }

    public Integer getBusinessType() {
        return businessType;
    }

    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    public Integer getBusinessStatusType() {
        return businessStatusType;
    }

    public void setBusinessStatusType(Integer businessStatusType) {
        this.businessStatusType = businessStatusType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ReverseOrder{" +
                "id=" + id +
                ", channelPayId=" + channelPayId +
                ", reverseId=" + reverseId +
                ", reverseAmount=" + reverseAmount +
                ", reverseState=" + reverseState +
                ", reverseTime=" + reverseTime +
                ", channelRefundId=" + channelRefundId +
                ", channelRefundMsg=" + channelRefundMsg +
                ", reverseType=" + reverseType +
                ", businessType=" + businessType +
                ", businessStatusType=" + businessStatusType +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
