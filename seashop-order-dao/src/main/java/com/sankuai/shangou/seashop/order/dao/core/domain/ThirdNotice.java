package com.sankuai.shangou.seashop.order.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

/**
 * <p>
 * 三方推送通知表
 * </p>
 *
 * <AUTHOR> @since 2024-01-29
 */
public class ThirdNotice implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 通知id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 业务编号 可以为订单id/productId/售后单id
     */
    @TableField("biz_code")
    private String bizCode;

    /**
     * 通知类型 0-9:订单通知 10-19:商品通知 20-29:售后单通知
     */
    @TableField("notice_type")
    private Integer noticeType;

    /**
     * 通知来源 0: 牵牛花 1：旺店通 2：聚水潭 3：网店管家 4：吉客云 5: 易久批
     */
    @TableField("notice_source")
    private Integer noticeSource;

    /**
     * 通知唯一编码, 消息体 + 时间戳生成, 用来保证某一时间段内同一消息不重复处理
     */
    @TableField("notice_code")
    private String noticeCode;

    /**
     * 通知内容
     */
    @TableField("notice_body")
    private String noticeBody;

    /**
     * 处理状态 0-待处理 1-处理中 2-处理成功 3-处理失败
     */
    @TableField("handle_status")
    private Integer handleStatus;

    /**
     * 处理结果
     */
    @TableField("handle_result")
    private String handleResult;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBizCode() {
        return bizCode;
    }

    public void setBizCode(String bizCode) {
        this.bizCode = bizCode;
    }

    public Integer getNoticeType() {
        return noticeType;
    }

    public void setNoticeType(Integer noticeType) {
        this.noticeType = noticeType;
    }

    public Integer getNoticeSource() {
        return noticeSource;
    }

    public void setNoticeSource(Integer noticeSource) {
        this.noticeSource = noticeSource;
    }

    public String getNoticeCode() {
        return noticeCode;
    }

    public void setNoticeCode(String noticeCode) {
        this.noticeCode = noticeCode;
    }

    public String getNoticeBody() {
        return noticeBody;
    }

    public void setNoticeBody(String noticeBody) {
        this.noticeBody = noticeBody;
    }

    public Integer getHandleStatus() {
        return handleStatus;
    }

    public void setHandleStatus(Integer handleStatus) {
        this.handleStatus = handleStatus;
    }

    public String getHandleResult() {
        return handleResult;
    }

    public void setHandleResult(String handleResult) {
        this.handleResult = handleResult;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ThirdNotice{" +
        "id=" + id +
        ", bizCode=" + bizCode +
        ", noticeType=" + noticeType +
        ", noticeSource=" + noticeSource +
        ", noticeCode=" + noticeCode +
        ", noticeBody=" + noticeBody +
        ", handleStatus=" + handleStatus +
        ", handleResult=" + handleResult +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
