package com.sankuai.shangou.seashop.order.dao.finance.model;

import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description: 已结算列表请求参数
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ThriftStruct
@ToString
@Getter
@Setter
public class SettledQryModel extends BasePageReq {

    // 结算时间-开始时间
    private Date startSettlementTime;

    // 结算时间-结束时间
    private Date endSettlementTime;

    // 店铺id
    private Long shopId;

    // 店铺名称
    private List<Long> shopIdList;

    // 是否是收入
    private Boolean incomeFlag;
}
