package com.sankuai.shangou.seashop.order.dao.finance.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 店铺资金流水表
 * </p>
 *
 * <AUTHOR> @since 2023-12-05
 */
@TableName("finance_shop_account_item")
public class ShopAccountItem implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 交易流水号
     */
    @TableField("account_no")
    private String accountNo;

    /**
     * 关联资金编号
     */
    @TableField("account_id")
    private Long accountId;

    /**
     * 金额
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 交易类型
     */
    @TableField("trade_type")
    private Integer tradeType;

    /**
     * 是否收入
     */
    @TableField("income_flag")
    private Boolean incomeFlag;

    /**
     * 交易备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 详情ID
     */
    @TableField("detail_id")
    private String detailId;

    /**
     * 结算周期(以天为单位)(冗余字段)
     */
    @TableField("settlement_cycle")
    private Integer settlementCycle;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getAccountNo() {
        return accountNo;
    }

    public void setAccountNo(String accountNo) {
        this.accountNo = accountNo;
    }

    public Long getAccountId() {
        return accountId;
    }

    public void setAccountId(Long accountId) {
        this.accountId = accountId;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getTradeType() {
        return tradeType;
    }

    public void setTradeType(Integer tradeType) {
        this.tradeType = tradeType;
    }

    public Boolean getIncomeFlag() {
        return incomeFlag;
    }

    public void setIncomeFlag(Boolean incomeFlag) {
        this.incomeFlag = incomeFlag;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getDetailId() {
        return detailId;
    }

    public void setDetailId(String detailId) {
        this.detailId = detailId;
    }

    public Integer getSettlementCycle() {
        return settlementCycle;
    }

    public void setSettlementCycle(Integer settlementCycle) {
        this.settlementCycle = settlementCycle;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ShopAccountItem{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", shopName=" + shopName +
        ", accountNo=" + accountNo +
        ", accountId=" + accountId +
        ", amount=" + amount +
        ", tradeType=" + tradeType +
        ", incomeFlag=" + incomeFlag +
        ", remark=" + remark +
        ", detailId=" + detailId +
        ", settlementCycle=" + settlementCycle +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
