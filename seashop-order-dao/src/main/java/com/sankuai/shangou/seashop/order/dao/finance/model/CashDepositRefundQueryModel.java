package com.sankuai.shangou.seashop.order.dao.finance.model;

import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description: 保证金退款查询条件对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ThriftStruct
@ToString
@Getter
@Setter
public class CashDepositRefundQueryModel extends BasePageReq {

    /**
     * 店铺id列表
     */
    private List<Long> shopIdList;

    /**
     * 退款状态
     */
    private Integer status;

}
