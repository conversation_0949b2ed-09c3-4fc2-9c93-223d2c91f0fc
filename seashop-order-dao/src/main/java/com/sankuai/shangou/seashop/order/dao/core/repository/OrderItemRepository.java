package com.sankuai.shangou.seashop.order.dao.core.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.mapper.CustomOrderMapper;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderItemMapper;
import com.sankuai.shangou.seashop.order.thrift.core.dto.OrderItemReportDto;
import io.micrometer.core.instrument.util.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Repository
public class OrderItemRepository extends ServiceImpl<OrderItemMapper, OrderItem> {

    @Resource
    private CustomOrderMapper customOrderMapper;

    @Resource
    private OrderRepository orderRepository;

    /**
     * 日志记录使用
     */
    public OrderItem selectById(Long id) {
        return super.getById(id);
    }

    public List<OrderItem> getByOrderIdList(List<String> orderIdList) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderItem::getOrderId, orderIdList);
        return baseMapper.selectList(wrapper);
    }

    public List<OrderItem> getByOrderId(String orderId) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderItem::getOrderId, orderId);
        return baseMapper.selectList(wrapper);
    }

    public int updateById(Long id, OrderItem orderItem) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderItem::getId, id);
        return baseMapper.update(orderItem, wrapper);
    }

    public int increaseApplyRefundQuantity(Long id, Long applyRefundQuantity) {
        return customOrderMapper.increaseApplyRefundQuantity(id, applyRefundQuantity);
    }

    public int decreaseApplyRefundQuantity(Long id, Long applyRefundQuantity) {
        return customOrderMapper.decreaseApplyRefundQuantity(id, applyRefundQuantity);
    }

    public List<OrderItemReportDto> getByUpdateTime(Date updateTime) {
        LambdaQueryWrapper<OrderItem> queryWrapper = new LambdaQueryWrapper<>();
        if (updateTime != null) {
            queryWrapper.ge(OrderItem::getUpdateTime, updateTime);
        }

        List<OrderItem> items = baseMapper.selectList(queryWrapper);
        List<OrderItemReportDto> result = JsonUtil.copyList(items, OrderItemReportDto.class);

        if (CollectionUtil.isEmpty(result)) {
            return result;
        }

        List<String> orderIds = result.stream().map(t -> t.getOrderId()).collect(Collectors.toList());
        List<Order> orders = orderRepository.getByOrderIdList(orderIds);

        for (OrderItemReportDto item : result) {
            boolean hasOrder = orders.stream().filter(t -> t.getOrderId().equals(item.getOrderId())).findFirst().isPresent();
            if (hasOrder) {
                Order order = orders.stream().filter(t -> t.getOrderId().equals(item.getOrderId())).findFirst().get();
                item.setPaymentTime(order.getPayDate());
                item.setOrderTime(order.getCreateTime());
                item.setUserId(order.getUserId());
                item.setShopId(order.getShopId());
                item.setPlatform(order.getPlatform());
            }
        }
        return result;
    }

    public List<OrderItem> getByIdList(List<Long> ids) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderItem::getId, ids);
        return baseMapper.selectList(wrapper);
    }

    public List<OrderItem> getListByString(String productIdOrSkuId, String productName,long shopId) {
        LambdaQueryWrapper<OrderItem> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderItem::getShopId, shopId);
        if (!StringUtils.isEmpty(productName)) {
            wrapper.like(OrderItem::getProductName, productName);
        }
        if (!StringUtils.isEmpty(productIdOrSkuId)) {
            wrapper.and(t -> t.likeRight(OrderItem::getProductId, productIdOrSkuId).or().eq(OrderItem::getSkuId, productIdOrSkuId));
        }
        return baseMapper.selectList(wrapper);
    }

}
