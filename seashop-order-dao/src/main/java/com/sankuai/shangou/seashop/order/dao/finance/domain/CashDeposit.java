package com.sankuai.shangou.seashop.order.dao.finance.domain;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 保证金表
 * </p>
 *
 * <AUTHOR> @since 2023-11-28
 */
@TableName("finance_cash_deposit")
public class CashDeposit implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * Shop表外键
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 可用金额
     */
    @TableField("current_balance")
    private BigDecimal currentBalance;

    /**
     * 已缴纳金额
     */
    @TableField("total_balance")
    private BigDecimal totalBalance;

    /**
     * 最后一次缴纳时间
     */
    @TableField("date")
    private Date date;

    /**
     * 是否显示标志，只有保证金欠费该字段才有用，默认显示
     */
    @TableField("enable_labels")
    private Boolean enableLabels;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public BigDecimal getCurrentBalance() {
        return currentBalance;
    }

    public void setCurrentBalance(BigDecimal currentBalance) {
        this.currentBalance = currentBalance;
    }

    public BigDecimal getTotalBalance() {
        return totalBalance;
    }

    public void setTotalBalance(BigDecimal totalBalance) {
        this.totalBalance = totalBalance;
    }

    public Date getDate() {
        return date;
    }

    public void setDate(Date date) {
        this.date = date;
    }

    public Boolean getEnableLabels() {
        return enableLabels;
    }

    public void setEnableLabels(Boolean enableLabels) {
        this.enableLabels = enableLabels;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "CashDeposit{" +
                "id=" + id +
                ", shopId=" + shopId +
                ", currentBalance=" + currentBalance +
                ", totalBalance=" + totalBalance +
                ", date=" + date +
                ", enableLabels=" + enableLabels +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
