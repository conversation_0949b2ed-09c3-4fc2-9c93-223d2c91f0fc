package com.sankuai.shangou.seashop.order.dao.core.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/6/006
 * @description:
 */
@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderStatisticsPo {

    private Date startPayDate;

    private Date endPayDate;

    private Long shopId;

    private Date startOrderDate;

    private Date endOrderDate;
}
