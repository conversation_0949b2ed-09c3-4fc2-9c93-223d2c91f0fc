package com.sankuai.shangou.seashop.pay.dao.core.model;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 * @date 2024/09/02 9:06
 */
@Getter
@Setter
public class WxPayConfigModel extends BasePayConfigModel {

    /**
     * 是否开启服务商模式。
     */
    private Boolean serviceMode;

    /**
     * 服务商商户号。
     */
    private String serviceMchId;

    /**
     * 服务商应用ID。
     */
    private String serviceAppId;

    /**
     * API V3秘钥值。
     */
    private String apiV3Key;

    /**
     * 服务商P12证书地址。
     */
    private String serviceP12KeyPath;

    /**
     * 服务商P12证书内容。
     */
    private byte[] serviceP12KeyContent;

    /**
     * 商户号。
     */
    private String mchId;

    /**
     * P12证书地址。
     */
    private String p12KeyPath;

    /**
     * P12证书内容。
     */
    private byte[] p12KeyContent;

    /**
     * 是否开启小程序支付。
     */
    private Boolean enableApplet;

    /**
     * 小程序支付应用ID。
     */
    private String miniProgramAppId;

    /**
     * 是否开启H5/微商城支付。
     */
    private Boolean enableH5;

    /**
     * 公众号支付应用ID。
     */
    private String officialAccountAppId;

    /**
     * 是否开启Native支付。
     */
    private Boolean enableNative;

    /**
     * Native支付应用ID。
     */
    private String nativeAppId;

    /**
     * 是否开启App支付。
     */
    private Boolean enableApp;

    /**
     * App支付应用AppId。
     */
    private String applicationAppId;

    /**
     * 支付回调地址
     */
    private String callBackUrl;

    /**
     * 退款回调地址
     */
    private String refundCallBackUrl;

}
