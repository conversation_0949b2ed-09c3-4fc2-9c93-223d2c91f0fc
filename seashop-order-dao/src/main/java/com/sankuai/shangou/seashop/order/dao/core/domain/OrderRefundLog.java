package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 订单售后日志表
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
@TableName("order_refund_log")
public class OrderRefundLog implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 售后编号
     */
    @TableField("refund_id")
    private Long refundId;

    /**
     * 操作者
     */
    @TableField("operator")
    private String operator;

    /**
     * 操作日期
     */
    @TableField("operate_date")
    private Date operateDate;

    /**
     * 操作内容
     */
    @TableField("operate_content")
    private String operateContent;

    /**
     * 申请次数
     */
    @TableField("apply_number")
    private Integer applyNumber;

    /**
     * 退款步聚(枚举:common_model.enum.order_refund_step)
     */
    @TableField("step")
    private Integer step;

    /**
     * 备注(买家留言/商家留言/商家拒绝原因/平台退款备注)
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRefundId() {
        return refundId;
    }

    public void setRefundId(Long refundId) {
        this.refundId = refundId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Date getOperateDate() {
        return operateDate;
    }

    public void setOperateDate(Date operateDate) {
        this.operateDate = operateDate;
    }

    public String getOperateContent() {
        return operateContent;
    }

    public void setOperateContent(String operateContent) {
        this.operateContent = operateContent;
    }

    public Integer getApplyNumber() {
        return applyNumber;
    }

    public void setApplyNumber(Integer applyNumber) {
        this.applyNumber = applyNumber;
    }

    public Integer getStep() {
        return step;
    }

    public void setStep(Integer step) {
        this.step = step;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "RefundLog{" +
        "id=" + id +
        ", refundId=" + refundId +
        ", operator=" + operator +
        ", operateDate=" + operateDate +
        ", operateContent=" + operateContent +
        ", applyNumber=" + applyNumber +
        ", step=" + step +
        ", remark=" + remark +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
