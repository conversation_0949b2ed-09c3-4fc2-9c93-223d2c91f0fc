package com.sankuai.shangou.seashop.order.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.core.domain.ShoppingCart;
import com.sankuai.shangou.seashop.order.dao.core.mapper.ShoppingCartMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 购物车仓储
 * <AUTHOR>
 */
@Repository
@Slf4j
public class ShoppingCartRepository extends ServiceImpl<ShoppingCartMapper, ShoppingCart> {

    public void removeByUserIdAndSku(Long userId, List<String> skuIdList) {
        LambdaQueryWrapper<ShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShoppingCart::getUserId, userId)
                .in(ShoppingCart::getSkuId, skuIdList);
        baseMapper.delete(queryWrapper);
    }

    public List<ShoppingCart> getByUserIdAndSku(Long userId, List<String> skuIdList) {
        LambdaQueryWrapper<ShoppingCart> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShoppingCart::getUserId, userId)
                .in(ShoppingCart::getSkuId, skuIdList);
        return baseMapper.selectList(queryWrapper);
    }

}
