package com.sankuai.shangou.seashop.order.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefundRecord;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderRefundRecordMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2024/1/8/008
 * @description:
 */
@Repository
public class OrderRefundRecordRepository extends ServiceImpl<OrderRefundRecordMapper, OrderRefundRecord> {

    /**
     * 通过关联id和状态列表查询退款记录
     *
     * @param relateId   关联id
     * @param statusList 状态列表
     * @return
     */
    public List<OrderRefundRecord> listByRelateIdAndStatus(Long relateId, List<Integer> statusList) {
        LambdaQueryWrapper<OrderRefundRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRefundRecord::getRelateId, relateId);
        if (statusList != null && statusList.size() > 0) {
            wrapper.in(OrderRefundRecord::getRefundStatus, statusList);
        }
        return this.list(wrapper);
    }

    /**
     * 通过状态列表查询退款记录
     */
    public List<OrderRefundRecord> listByStatus(Integer status) {
        LambdaQueryWrapper<OrderRefundRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRefundRecord::getRefundStatus, status);
        return this.list(wrapper);
    }


    /**
     * 通过refundNo查询退款记录
     */
    public OrderRefundRecord getByRefundNo(String refundNo) {
        LambdaQueryWrapper<OrderRefundRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderRefundRecord::getRefundNo, refundNo);
        return this.getOne(wrapper);
    }
}
