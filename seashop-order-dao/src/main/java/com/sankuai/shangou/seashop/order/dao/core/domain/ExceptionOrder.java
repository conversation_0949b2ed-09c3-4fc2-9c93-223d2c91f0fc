package com.sankuai.shangou.seashop.order.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 异常订单
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
@TableName("order_exception_order")
public class ExceptionOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单编号
     */
    @TableField("order_id")
    private String orderId;

    /**
     * 支付批次号，多笔订单同时支付时批次号相同
     */
    @TableField("batch_no")
    private String batchNo;

    /**
     * 支付渠道对应的唯一标识
     */
    @TableField("pay_no")
    private String payNo;

    /**
     * 支付时间
     */
    @TableField("pay_time")
    private Date payTime;

    /**
     * 支付金额
     */
    @TableField("pay_amount")
    private BigDecimal payAmount;

    /**
     * 异常类型，1，重复支付；2，超时关闭；
     */
    @TableField("error_type")
    private Integer errorType;

    /**
     * 退款时间
     */
    @TableField("refund_time")
    private Date refundTime;

    /**
     * 退款状态。0:待退款；1:退款中；2:退款完成；3:退款失败；
     */
    @TableField("refund_status")
    private Integer refundStatus;

    /**
     * 退款失败原因
     */
    @TableField("refund_fail_reason")
    private String refundFailReason;

    /**
     * 退款批次号
     */
    @TableField("refund_batch_no")
    private String refundBatchNo;

    /**
     * 退款操作员
     */
    @TableField("refund_manager")
    private String refundManager;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public Date getPayTime() {
        return payTime;
    }

    public void setPayTime(Date payTime) {
        this.payTime = payTime;
    }

    public BigDecimal getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(BigDecimal payAmount) {
        this.payAmount = payAmount;
    }

    public Integer getErrorType() {
        return errorType;
    }

    public void setErrorType(Integer errorType) {
        this.errorType = errorType;
    }

    public Date getRefundTime() {
        return refundTime;
    }

    public void setRefundTime(Date refundTime) {
        this.refundTime = refundTime;
    }

    public Integer getRefundStatus() {
        return refundStatus;
    }

    public void setRefundStatus(Integer refundStatus) {
        this.refundStatus = refundStatus;
    }

    public String getRefundFailReason() {
        return refundFailReason;
    }

    public void setRefundFailReason(String refundFailReason) {
        this.refundFailReason = refundFailReason;
    }

    public String getRefundBatchNo() {
        return refundBatchNo;
    }

    public void setRefundBatchNo(String refundBatchNo) {
        this.refundBatchNo = refundBatchNo;
    }

    public String getRefundManager() {
        return refundManager;
    }

    public void setRefundManager(String refundManager) {
        this.refundManager = refundManager;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ExceptionOrder{" +
        "id=" + id +
        ", orderId=" + orderId +
        ", batchNo=" + batchNo +
        ", payNo=" + payNo +
        ", payTime=" + payTime +
        ", payAmount=" + payAmount +
        ", errorType=" + errorType +
        ", refundTime=" + refundTime +
        ", refundStatus=" + refundStatus +
        ", refundFailReason=" + refundFailReason +
        ", refundBatchNo=" + refundBatchNo +
        ", refundManager=" + refundManager +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
