package com.sankuai.shangou.seashop.order.dao.finance.model;

import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description: 待结算订单查询条件对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ThriftStruct
@ToString
@Getter
@Setter
public class PendingSettlementOrderQryModel extends BasePageReq {

    /**
     * 店铺id
     */
    private Long shopId;

    /**
     * 店铺id列表
     */
    private List<Long> shopIdList;

    /**
     * 结算金额排序
     */
    private Boolean settlementAmountSort;

    /**
     * 创建时间-开始时间
     */
    private Date startCreateTime;

    /**
     * 创建时间-结束时间
     */
    private Date endCreateTime;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 支付方式
     * ALIPAY_SCAN(1, "支付宝扫码"),
     * ALIPAY_H5(2, "支付宝H5"),
     * WECHAT_APPLET(3, "微信小程序"),
     * WECHAT_H5(4, "微信H5"),
     * COMPANY_BANK(5, "企业网银"),
     * PERSON_BANK(6, "个人网银")
     */
    private Integer paymentType;

    /**
     * 完成时间-开始时间
     */
    private Date startFinishTime;

    /**
     * 完成时间-结束时间
     */
    private Date endFinishTime;

    /**
     * 支付时间-开始时间
     */
    private Date startPayTime;

    /**
     * 支付时间-结束时间
     */
    private Date endPayTime;
}
