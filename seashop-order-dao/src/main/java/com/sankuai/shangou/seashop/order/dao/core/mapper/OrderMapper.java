package com.sankuai.shangou.seashop.order.dao.core.mapper;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.IntegralRedemOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.order.IntegralRedemOrderResp;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 订单主表 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
public interface OrderMapper extends EnhancedMapper<Order> {

    List<Long> queryPayOrderUserId(@Param("payDate") Date payDate, @Param("targetUserIdList") List<Long> targetUserIdList);

    List<Long> listHaveOrderUserId(@Param("orderDate") Date orderDate, @Param("targetUserIdList") List<Long> targetUserIdList);

    List<Long> queryEffectivePayNumberUserId(@Param("effectivePayOperation") Integer effectivePayOperation, @Param("effectivePayValue") Integer effectivePayValue,
                                             @Param("effectivePayValue2") Integer effectivePayValue2, @Param("targetUserIdList") List<Long> targetUserIdList);

    List<Long> queryPayNumberUserId(@Param("payOperation") Integer payOperation, @Param("payValue") Integer payValue,
                                    @Param("payValue2") Integer payValue2, @Param("targetUserIdList") List<Long> targetUserIdList);

    List<Long> queryEffectivePayAmountUserId(@Param("effectivePayAmountOperation") Integer effectivePayAmountOperation,
                                             @Param("effectivePayAmount") BigDecimal effectivePayAmount,
                                             @Param("effectivePayAmount2") BigDecimal effectivePayAmount2,
                                             @Param("targetUserIdList") List<Long> targetUserIdList);

    List<Long> queryAveragePriceUserId(@Param("averagePriceOperation") Integer averagePriceOperation,
                                       @Param("averagePriceValue") BigDecimal averagePriceValue,
                                       @Param("averagePriceValue2") BigDecimal averagePriceValue2,
                                       @Param("targetUserIdList") List<Long> targetUserIdList);

    List<Long> queryPayProductUserId(@Param("productIds") List<Long> productIds, @Param("targetUserIdList") List<Long> targetUserIdList);

    List<Long> queryNoRefundUserId(@Param("noRefundOperation") Integer noRefundOperation, @Param("noRefundValue") Integer noRefundValue,
                                   @Param("noRefundValue2") Integer noRefundValue2, @Param("targetUserIdList") List<Long> targetUserIdList);

    List<IntegralRedemOrderResp> queryIntegralRedemOrder(@Param("query") IntegralRedemOrderReq request);


    int countIntegralBought(@Param("userId") Long userId, @Param("integralRedeemId") Long integralRedeemId);


    int decrementRefundIntegral(@Param("orderId") String orderId, @Param("refundIntegral") Long refundIntegral);

}
