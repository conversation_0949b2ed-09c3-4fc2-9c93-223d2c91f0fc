package com.sankuai.shangou.seashop.order.dao.core.po;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class UpdateItemPricePo {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 优惠券分摊金额
     */
    private BigDecimal couponDiscount;

}
