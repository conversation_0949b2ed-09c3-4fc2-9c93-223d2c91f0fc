package com.sankuai.shangou.seashop.order.dao.finance.repository;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.finance.domain.SettlementConfig;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.SettlementConfigMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Repository
public class SettlementConfigRepository extends ServiceImpl<SettlementConfigMapper, SettlementConfig> {

    /**
     * 获取结算配置
     *
     * @return
     */
    public SettlementConfig getConfig() {
        SettlementConfig config;
        List<SettlementConfig> list = this.list();
        if (CollUtil.isNotEmpty(list)) {
            config = list.get(0);
        } else {
            config = new SettlementConfig();
            this.save(config);
        }
        return config;
    }
}
