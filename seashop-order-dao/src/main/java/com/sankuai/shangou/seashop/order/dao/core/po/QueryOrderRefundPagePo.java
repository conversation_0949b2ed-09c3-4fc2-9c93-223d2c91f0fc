package com.sankuai.shangou.seashop.order.dao.core.po;

import java.util.Date;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class QueryOrderRefundPagePo {
    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 每页大小
     */
    private Integer pageSize;

    /**
     * 店铺ID
     */
    private Long shopId;
    /**
     * 审核状态
     */
    private Integer auditStatus;
    /**
     * 售后类型
     */
    private List<Integer> refundModes;
    /**
     * 创建时间开始
     */
    private Date startCreateTime;
    /**
     * 创建时间结束
     */
    private Date endCreateTime;
    /**
     * 修改时间开始
     */
    private Date startUpdateTime;
    /**
     * 修改时间结束
     */
    private Date endUpdateTime;

    private Long userId;

    private String orderId;

    /**
     * 综合状态筛选
     */
    private List<Integer> statusList;

}
