package com.sankuai.shangou.seashop.order.dao.finance.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.order.dao.finance.domain.AccountDetail;
import com.sankuai.shangou.seashop.order.dao.finance.mapper.AccountDetailMapper;
import com.sankuai.shangou.seashop.order.dao.finance.model.AccountDetailCountModel;
import com.sankuai.shangou.seashop.order.dao.finance.model.SettledItemQryModel;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@Repository
public class AccountDetailRepository extends ServiceImpl<AccountDetailMapper, AccountDetail> {

    public List<AccountDetail> getByAccountIds(List<String> accountIds) {
        LambdaQueryWrapper<AccountDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AccountDetail::getAccountId, accountIds);
        return this.list(queryWrapper);
    }

    public AccountDetail getByOrderId(String orderId) {
        LambdaQueryWrapper<AccountDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AccountDetail::getOrderId, orderId);
        return this.getOne(queryWrapper);
    }

    public List<AccountDetail> listByOrderIdList(List<String> orderIdList) {
        LambdaQueryWrapper<AccountDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(AccountDetail::getOrderId, orderIdList);
        return this.list(queryWrapper);
    }

    public Page<AccountDetail> itemPageList(BasePageParam paramPage, SettledItemQryModel param) {
        Page<AccountDetail> page = PageHelper.startPage(paramPage);
        LambdaQueryWrapper<AccountDetail> queryWrapper = new LambdaQueryWrapper<>();
        if (null != param.getShopId()) {
            queryWrapper.eq(AccountDetail::getShopId, param.getShopId());
        }
        if (StrUtil.isNotBlank(param.getShopName())) {
            queryWrapper.like(AccountDetail::getShopName, param.getShopName());
        }
        if (null != param.getDetailId()) {
            // 前端看到的是列表返回的detailId，对于AccountDetail表来说是accountId
            queryWrapper.eq(AccountDetail::getAccountId, param.getDetailId());
        }
        if (StrUtil.isNotBlank(param.getOrderId())) {
            queryWrapper.eq(AccountDetail::getOrderId, param.getOrderId());
        }

        // 处理订单完成时间
        if (null != param.getStartOrderFinishTime() && null != param.getEndOrderFinishTime()) {
            queryWrapper.between(AccountDetail::getOrderFinishDate, param.getStartOrderFinishTime(), param.getEndOrderFinishTime());
        }
        if (null != param.getStartOrderFinishTime() && null == param.getEndOrderFinishTime()) {
            queryWrapper.ge(AccountDetail::getOrderFinishDate, param.getStartOrderFinishTime());
        }
        if (null == param.getStartOrderFinishTime() && null != param.getEndOrderFinishTime()) {
            queryWrapper.le(AccountDetail::getOrderFinishDate, param.getEndOrderFinishTime());
        }

        // 处理结算时间
        if (null != param.getStartSettlementTime() && null != param.getEndSettlementTime()) {
            queryWrapper.between(AccountDetail::getDate, param.getStartSettlementTime(), param.getEndSettlementTime());
        }
        if (null != param.getStartSettlementTime() && null == param.getEndSettlementTime()) {
            queryWrapper.ge(AccountDetail::getDate, param.getStartSettlementTime());
        }
        if (null == param.getStartSettlementTime() && null != param.getEndSettlementTime()) {
            queryWrapper.le(AccountDetail::getDate, param.getEndSettlementTime());
        }

        // 处理支付时间
        if (null != param.getStartPayTime() && null != param.getEndPayTime()) {
            queryWrapper.between(AccountDetail::getPayDate, param.getStartPayTime(), param.getEndPayTime());
        }
        if (null != param.getStartPayTime() && null == param.getEndPayTime()) {
            queryWrapper.ge(AccountDetail::getPayDate, param.getStartPayTime());
        }
        if (null == param.getStartPayTime() && null != param.getEndPayTime()) {
            queryWrapper.le(AccountDetail::getPayDate, param.getEndPayTime());
        }

        if (null != param.getPaymentType()) {
            queryWrapper.eq(AccountDetail::getPaymentType, param.getPaymentType());
        }

        queryWrapper.orderByDesc(AccountDetail::getId);
        page.doSelectPage(() -> this.list(queryWrapper));
        return page;
    }

    public AccountDetailCountModel detailCount(Long shopId, Long detailId) {
        return this.baseMapper.detailCount(shopId, detailId);
    }
}
