package com.sankuai.shangou.seashop.order.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderComment;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderCommentMapper;
import com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderCommentExtMapper;
import com.sankuai.shangou.seashop.order.dao.core.model.ShopMarkModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/12/04 10:38
 */
@Repository
@Slf4j
public class OrderCommentRepository extends ServiceImpl<OrderCommentMapper, OrderComment> {


    @Resource
    private OrderCommentExtMapper orderCommentExtMapper;

    /**
     * 根据订单id判断是否已经评价过
     *
     * @param orderId 订单id
     * @return true 已经评价过
     */
    public Boolean existByOrderId(String orderId) {
        return count(new LambdaQueryWrapper<OrderComment>().eq(OrderComment::getOrderId, orderId)) > 0;
    }

    /**
     * 根据店铺id获取店铺评分
     *
     * @param shopId 店铺id
     * @return 评价信息
     */
    public ShopMarkModel queryShopMarkByShopId(Long shopId) {
        ShopMarkModel shopMarkDto = orderCommentExtMapper.getShopMarkByShopId(shopId);
        shopMarkDto.setShopId(shopId);
        return shopMarkDto;
    }

    /**
     * 根据订单id查询评价信息
     *
     * @param orderId 订单id
     * @return 评价信息
     */
    public OrderComment getByOrderId(String orderId) {
        return getOne(new LambdaQueryWrapper<OrderComment>().eq(OrderComment::getOrderId, orderId));
    }
    
    public List<OrderComment> getByOrderIdList(List<String> orderIdList) {
        LambdaQueryWrapper<OrderComment> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderComment::getOrderId, orderIdList);
        return super.list(wrapper);
    }
}
