package com.sankuai.shangou.seashop.order.dao.finance.domain;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 平台资金表
 * </p>
 *
 * <AUTHOR> @since 2023-12-02
 */
@TableName("finance_plat_account")
public class PlatAccount implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 待结算
     */
    @TableField("pending_settlement")
    private BigDecimal pendingSettlement;

    /**
     * 已结算
     */
    @TableField("settled")
    private BigDecimal settled;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getPendingSettlement() {
        return pendingSettlement;
    }

    public void setPendingSettlement(BigDecimal pendingSettlement) {
        this.pendingSettlement = pendingSettlement;
    }

    public BigDecimal getSettled() {
        return settled;
    }

    public void setSettled(BigDecimal settled) {
        this.settled = settled;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "PlatAccount{" +
        "id=" + id +
        ", pendingSettlement=" + pendingSettlement +
        ", settled=" + settled +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
