package com.sankuai.shangou.seashop.order.dao.core.repository;

import java.util.Date;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.mapper.OrderPayRecordMapper;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonPayRecordQueryParamBo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;

/**
 * <AUTHOR>
 */
@Repository
public class OrderPayRecordRepository extends ServiceImpl<OrderPayRecordMapper, OrderPayRecord> {

    public List<OrderPayRecord> queryOrderPayRecordList(List<String> batchNoList){
        if(CollectionUtil.isEmpty(batchNoList)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderPayRecord::getBatchNo, batchNoList);
        return this.baseMapper.selectList(wrapper);
    }

    public List<OrderPayRecord> queryOrderPayRecordByOrderIds(List<String> orderIds){
        if(CollectionUtil.isEmpty(orderIds)){
            return Lists.newArrayList();
        }
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderPayRecord::getOrderId, orderIds);
        return this.baseMapper.selectList(wrapper);
    }

    /**
     * 通过订单号和支付状态获取最近的一条，主要是获取支付成功的
     *
     * @param orderId
     * @param status
     * <AUTHOR>
     */
    public OrderPayRecord getLatestByOrderIdAndStatus(String orderId, Integer status) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderPayRecord::getOrderId, orderId)
                .eq(OrderPayRecord::getPayStatus, status)
                .orderByDesc(OrderPayRecord::getCreateTime);
        return baseMapper.selectOne(wrapper);
    }

    /**
     * 通过订单号和支付状态获取最近的一条，主要是获取支付成功的
     *
     * @param orderIdList 订单号列表
     * @param payStatusList   支付状态，枚举定义在api，所以状态用传入
     * @return
     */
    public List<OrderPayRecord> getByOrderIdListAndPayStatusForceMaster(List<String> orderIdList, List<Integer> payStatusList) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderPayRecord::getOrderId, orderIdList)
                .in(OrderPayRecord::getPayStatus, payStatusList);
        return baseMapper.selectList(wrapper);
    }

    /**
     * 通过订单号列表和支付状态，查询支付记录列表
     *
     * @param orderIdList
     * @param status
     * @return
     */
    public List<OrderPayRecord> listByOrderIdAndStatus(List<String> orderIdList, Integer status) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OrderPayRecord::getOrderId, orderIdList)
                .eq(OrderPayRecord::getPayStatus, status);
        return this.list(wrapper);
    }

    /**
     * 通过支付批次号查询支付记录
     * 因为合并支付，所以一个支付批次号可能对应多个订单，所以返回数组
     * <AUTHOR>
     * @param batchNo 支付批次号
     */
    public List<OrderPayRecord> getByBatchNo(String batchNo) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderPayRecord::getBatchNo, batchNo);
        return this.list(wrapper);
    }

    /**
     * 通过订单号+支付批次号查询支付记录，理论上是唯一的
     * 如果是重复支付的，支付批次号是不一样的，支付时会先判断是否有有效的支付记录，所以理论上是唯一的
     * <AUTHOR>
     * @param orderId 订单号
	 * @param batchNo 支付批次号
     */
    public OrderPayRecord getBydOrderIdAndBatchNoAndStatus(String orderId, String batchNo) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderPayRecord::getBatchNo, batchNo)
                .eq(OrderPayRecord::getOrderId, orderId);
        return this.getOne(wrapper);
    }

    /**
     * 通过订单号+支付流水号查询支付记录，理论上是唯一的
     * 如果是重复支付的，是多笔支付，所以支付流水也是不一样的，所以也是理论上唯一
     * <AUTHOR>
     * @param orderId 订单号
     * @param outTransId 外部支付流水号，实际支付渠道的
     * @param status 支付状态
     */
    public OrderPayRecord getByOrderIdAndOutTransIdAndStatus(String orderId, String outTransId, Integer status) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderPayRecord::getOutTransId, outTransId)
                .eq(OrderPayRecord::getOrderId, orderId)
                .eq(OrderPayRecord::getPayStatus, status);
        return this.getOne(wrapper);
    }

    public List<OrderPayRecord> getByBatchNoAndStatus(String batchNo, Integer status) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderPayRecord::getBatchNo, batchNo)
                .eq(OrderPayRecord::getPayStatus, status);
        return this.list(wrapper);
    }

    /**
     * 通过时间，查询支付成功的记录
     */
    public List<OrderPayRecord> listByTimeAndStatus(Date startTime, Date endTime, Integer payStatus) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.between(OrderPayRecord::getPayTime, startTime, endTime)
                .eq(OrderPayRecord::getPayStatus, payStatus);
        return this.list(wrapper);
    }

    /**
     * 订单ID、支付号、支付状态，查询支付记录
     * （主要查询订单支付成功的支付记录）
     *
     * @param orderId
     * @param payNo
     * @param payStatus
     * @return
     */
    public OrderPayRecord getByOrderIdAndPayNo(String orderId, String payNo, Integer payStatus) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderPayRecord::getOrderId, orderId)
//                .eq(OrderPayRecord::getOutTransId, payNo)
                .eq(OrderPayRecord::getPayNo, payNo)
                .eq(OrderPayRecord::getPayStatus, payStatus);
        return this.getOne(wrapper);
    }


    public List<OrderPayRecord> getByCondition(CommonPayRecordQueryParamBo param) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(param.getPayStatusEq() != null, OrderPayRecord::getPayStatus, param.getPayStatusEq())
                .in(CollUtil.isNotEmpty(param.getOrderIdListIn()), OrderPayRecord::getOrderId, param.getOrderIdListIn())
                .in(CollUtil.isNotEmpty(param.getPayNoListIn()), OrderPayRecord::getPayNo, param.getPayNoListIn())
                .in(CollUtil.isNotEmpty(param.getOutTransIdList()), OrderPayRecord::getOutTransId, param.getOutTransIdList());
        return this.list(wrapper);
    }

    public boolean updatePayNoByBatchNo(String batchNo, String adaPayId) {
        LambdaUpdateWrapper<OrderPayRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(OrderPayRecord::getBatchNo, batchNo)
                .set(OrderPayRecord::getPayNo, adaPayId);
        return this.update(wrapper);
    }

    public List<OrderPayRecord> getByBatchNoForceMaster(String batchNo) {
        LambdaQueryWrapper<OrderPayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OrderPayRecord::getBatchNo, batchNo);
        return this.list(wrapper);
    }

}
