package com.sankuai.shangou.seashop.order.dao.finance.model;

import com.facebook.swift.codec.ThriftStruct;
import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.*;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description: 保证金明细查询条件对象
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ThriftStruct
@ToString
@Getter
@Setter
public class CashDepositDetailQueryModel extends BasePageReq {

    /**
     * 保证金ID
     */
    private Long cashDepositId;

    /**
     * 操作人

     */
    private String operator;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

}
