package com.sankuai.shangou.seashop.order.dao.core.po;

import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
public class QueryOrderParamPo {

    private Long userId;
    /**
     * 搜索关键字，支持 商品名称，订单编号，规格ID，商品ID
     */
    private String searchKey;
    /**
     * 商品ID列表。商品名称、商品ID、规则ID这些需要先查出商品ID列表
     */
    private List<String> orderIdList;
    /**
     * 订单状态
     */
    private Integer orderStatus;
    /**
     * 下单时间-开始
     */
    private Date orderStartTime;
    /**
     * 下单时间-结束
     */
    private Date orderEndTime;
    /**
     * 完成时间-开始
     */
    private Date finishStartTime;
    /**
     * 完成时间-结束
     */
    private Date finishEndTime;
    /**
     * 查询来源。1-商家小程序；2-商家PC端；3-卖家PC端；4-平台PC端
     */
    private Integer queryFrom;

}
