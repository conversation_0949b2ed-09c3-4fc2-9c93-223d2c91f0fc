<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
  <context id="MybatisGenerator" targetRuntime="MyBatis3">

    <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin" />

    <commentGenerator>
        <property name="suppressDate" value="true"/>
        <property name="suppressAllComments" value="true"/>
        <property name="javaFileEncoding" value="UTF-8"/>
    </commentGenerator>

    <!--替换成自己数据库的jdbcRef -->
    <zebra jdbcRef="fsparch_test_xframe_demo_test"/>

    <javaModelGenerator targetPackage="com.sankuai.shangou.seashop.order.dao.core.domain,com.sankuai.shangou.seashop.order.dao.finance.domain" targetProject="src/main/java">
        <property name="enableSubPackages" value="true" />
        <property name="trimStrings" value="true" />
    </javaModelGenerator>

    <sqlMapGenerator targetPackage="com.sankuai.shangou.seashop.order.dao.core.mapper,com.sankuai.shangou.seashop.order.dao.finance.mapper" targetProject="src/main/resources">
        <property name="enableSubPackages" value="true" />
    </sqlMapGenerator>

    <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.shangou.seashop.order.dao.core.mapper,com.sankuai.shangou.seashop.order.dao.finance.mapper"  targetProject="src/main/java">
        <property name="enableSubPackages" value="true" />
    </javaClientGenerator>

    <!-- 示例 -->
<!--     <table tableName="xxxxx_xxxxx" domainObjectName="XxxxXxxx"> -->
<!--        <generatedKey column="id" sqlStatement="MySql" identity="true"/> -->
<!--     </table> -->

  </context>
</generatorConfiguration>