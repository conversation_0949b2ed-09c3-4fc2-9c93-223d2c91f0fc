<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.OrderPayRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord">
        <id column="id" property="id" />
        <result column="batch_no" property="batchNo" />
        <result column="order_id" property="orderId" />
        <result column="pay_channel" property="payChannel" />
        <result column="pay_no" property="payNo" />
        <result column="pay_method" property="payMethod" />
        <result column="pay_status" property="payStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="pay_time" property="payTime" />
        <result column="order_amount" property="orderAmount" />
        <result column="out_trans_id" property="outTransId" />
        <result column="pay_amount" property="payAmount" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, batch_no, order_id, pay_channel, pay_no, pay_method, out_trans_id, create_time, update_time, pay_time,order_amount,pay_amount
    </sql>

</mapper>
