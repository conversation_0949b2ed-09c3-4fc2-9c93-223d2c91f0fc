<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.OrderOperationLogMapper">

<!--    &lt;!&ndash; 通用查询映射结果 &ndash;&gt;-->
<!--    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.OrderOperationLog">-->
<!--        <id column="id" property="id" />-->
<!--        <result column="order_id" property="orderId" />-->
<!--        <result column="operator" property="operator" />-->
<!--        <result column="operate_date" property="operateDate" />-->
<!--        <result column="operate_content" property="operateContent" />-->
<!--        <result column="create_time" property="createTime" />-->
<!--        <result column="update_time" property="updateTime" />-->
<!--    </resultMap>-->

<!--    &lt;!&ndash; 通用查询结果列 &ndash;&gt;-->
<!--    <sql id="Base_Column_List">-->
<!--        id, order_id, operator, operate_date, operate_content, create_time, update_time-->
<!--    </sql>-->

</mapper>
