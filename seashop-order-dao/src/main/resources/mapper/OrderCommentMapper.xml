<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.OrderCommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.OrderComment">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="comment_date" property="commentDate" />
        <result column="pack_mark" property="packMark" />
        <result column="delivery_mark" property="deliveryMark" />
        <result column="service_mark" property="serviceMark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, shop_id, shop_name, user_id, user_name, comment_date, pack_mark, delivery_mark, service_mark, create_time, update_time
    </sql>

</mapper>
