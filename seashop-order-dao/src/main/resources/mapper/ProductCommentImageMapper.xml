<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.ProductCommentImageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.ProductCommentImage">
        <id column="id" property="id" />
        <result column="comment_image" property="commentImage" />
        <result column="product_comment_id" property="productCommentId" />
        <result column="comment_type" property="commentType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, comment_image, product_comment_id, comment_type,create_time, update_time
    </sql>

</mapper>
