<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.ShoppingCartMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.ShoppingCart">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="product_id" property="productId" />
        <result column="sku_id" property="skuId" />
        <result column="quantity" property="quantity" />
        <result column="add_time" property="addTime" />
        <result column="whether_select" property="whetherSelect" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, product_id, sku_id, quantity, add_time, whether_select, create_time, update_time
    </sql>

</mapper>
