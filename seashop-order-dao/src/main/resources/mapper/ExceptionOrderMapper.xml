<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.ExceptionOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.ExceptionOrder">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="batch_no" property="batchNo" />
        <result column="pay_no" property="payNo" />
        <result column="pay_time" property="payTime" />
        <result column="pay_amount" property="payAmount" />
        <result column="error_type" property="errorType" />
        <result column="refund_time" property="refundTime" />
        <result column="refund_status" property="refundStatus" />
        <result column="refund_fail_reason" property="refundFailReason" />
        <result column="refund_batch_no" property="refundBatchNo" />
        <result column="refund_manager" property="refundManager" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, batch_no, pay_no, pay_time, pay_amount, error_type, refund_time, refund_status, refund_fail_reason, refund_batch_no, refund_manager, create_time, update_time
    </sql>

</mapper>
