<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.OrderRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="order_item_id" property="orderItemId" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="user_id" property="userId" />
        <result column="applicant" property="applicant" />
        <result column="contact_person" property="contactPerson" />
        <result column="contact_cell_phone" property="contactCellPhone" />
        <result column="apply_date" property="applyDate" />
        <result column="amount" property="amount" />
        <result column="reason" property="reason" />
        <result column="reason_detail" property="reasonDetail" />
        <result column="seller_audit_status" property="sellerAuditStatus" />
        <result column="seller_audit_date" property="sellerAuditDate" />
        <result column="seller_remark" property="sellerRemark" />
        <result column="manager_confirm_status" property="managerConfirmStatus" />
        <result column="manager_confirm_date" property="managerConfirmDate" />
        <result column="manager_remark" property="managerRemark" />
        <result column="is_return" property="hasReturn" />
        <result column="express_company_name" property="expressCompanyName" />
        <result column="ship_order_number" property="shipOrderNumber" />
        <result column="refund_mode" property="refundMode" />
        <result column="refund_pay_status" property="refundPayStatus" />
        <result column="refund_pay_type" property="refundPayType" />
        <result column="buyer_deliver_date" property="buyerDeliverDate" />
        <result column="seller_confirm_arrival_date" property="sellerConfirmArrivalDate" />
        <result column="refund_batch_no" property="refundBatchNo" />
        <result column="refund_post_time" property="refundPostTime" />
        <result column="return_quantity" property="returnQuantity" />
        <result column="return_plat_commission" property="returnPlatCommission" />
        <result column="apply_number" property="applyNumber" />
        <result column="cert_pic1" property="certPic1" />
        <result column="cert_pic2" property="certPic2" />
        <result column="cert_pic3" property="certPic3" />
        <result column="is_all_return" property="hasAllReturn" />
        <result column="return_freight" property="returnFreight" />
        <result column="last_modify_time" property="lastModifyTime" />
        <result column="is_cancel" property="hasCancel" />
        <result column="cancel_date" property="cancelDate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, order_item_id, shop_id, shop_name, user_id, applicant, contact_person, contact_cell_phone, apply_date, amount, reason, reason_detail, seller_audit_status, seller_audit_date, seller_remark, manager_confirm_status, manager_confirm_date, manager_remark, hasReturn, express_company_name, ship_order_number, refund_mode, refund_pay_status, refund_pay_type, buyer_deliver_date, seller_confirm_arrival_date, refund_batch_no, refund_post_time, return_quantity, return_plat_commission, apply_number, cert_pic1, cert_pic2, cert_pic3, hasAllReturn, return_freight,, last_modify_time, hasCancel, cancel_date, create_time, update_time
    </sql>

</mapper>
