<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.OrderMapper">
    <update id="decrementRefundIntegral">
        update `order`
        set refund_integral= refund_integral + #{refundIntegral}
        where order_id = #{orderId}
          and refund_integral + #{refundIntegral} <![CDATA[ <= ]]> integral

    </update>


    <select id="queryPayOrderUserId" resultType="java.lang.Long">
        select DISTINCT o.user_id
        from `order` o join order_pay_record opr on o.order_id = opr.order_id
        where opr.pay_time &gt;= #{payDate}
        <if test="targetUserIdList!= null and targetUserIds.size()>0">
            and o.user_id in
            <foreach collection="targetUserIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="listHaveOrderUserId" resultType="java.lang.Long">
        select DISTINCT o.user_id
        from `order` o
        where o.order_date &gt;= #{payDate}
        <if test="targetUserIdList!= null and targetUserIds.size()>0">
            and o.user_id in
            <foreach collection="targetUserIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
    </select>
    <select id="queryEffectivePayNumberUserId" resultType="java.lang.Long">
        select user_id from `order`
        where order_status in(2,3,5)
        <if test="targetUserIdList!= null and targetUserIds.size()>0">
            and user_id in
            <foreach collection="targetUserIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        group by user_id
        <if test="effectivePayOperation == 1">
            having count(*) &gt; #{effectivePayValue}
        </if>
        <if test="effectivePayOperation == 2">
            having count(*) &lt; #{effectivePayValue}
        </if>
        <if test="effectivePayOperation == 3">
            having count(*) BETWEEN #{effectivePayValue} AND #{effectivePayValue2}
        </if>

    </select>
    <select id="queryEffectivePayAmountUserId" resultType="java.lang.Long">
        select user_id from `order`
        where order_status in(2,3,5)
        <if test="targetUserIdList!= null and targetUserIds.size()>0">
            and user_id in
            <foreach collection="targetUserIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        group by user_id
        <if test="effectivePayOperation == 1">
            having sum(actual_pay_amount) &gt; #{effectivePayValue}
        </if>
        <if test="effectivePayOperation == 2">
            having sum(actual_pay_amount) &lt; #{effectivePayValue}
        </if>
        <if test="effectivePayOperation == 3">
            having sum(actual_pay_amount) BETWEEN #{effectivePayValue} AND #{effectivePayValue2}
        </if>

    </select>
    <select id="queryPayNumberUserId" resultType="java.lang.Long">
        select user_id from `order`
        where pay_date not null
        <if test="targetUserIdList!= null and targetUserIds.size()>0">
            and user_id in
            <foreach collection="targetUserIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        group by user_id
        <if test="payOperation == 1">
            having count(*) &gt; #{payValue}
        </if>
        <if test="payOperation == 2">
            having count(*) &lt; #{payValue}
        </if>
        <if test="payOperation == 3">
            having count(*) BETWEEN #{payValue} AND #{payValue2}
        </if>

    </select>
    <select id="queryAveragePriceUserId" resultType="java.lang.Long">
        select user_id
        from `order`
        where pay_date not null
        <if test="targetUserIdList!= null and targetUserIds.size()>0">
            and user_id in
            <foreach collection="targetUserIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        group by user_id
        <if test="averagePriceOperation == 1">
            having sum(actual_pay_amount) &gt; #{averagePriceValue}
        </if>
        <if test="averagePriceOperation == 2">
            having sum(actual_pay_amount) &lt; #{averagePriceValue}
        </if>
        <if test="averagePriceOperation == 3">
            having sum(actual_pay_amount) BETWEEN #{averagePriceValue} AND #{averagePriceValue2}
        </if>

    </select>
    <select id="queryPayProductUserId" resultType="java.lang.Long">
        select DISTINCT user_id from order_item
        where pay_date not null
        <if test="targetUserIdList!= null and targetUserIds.size()>0">
            and user_id in
            <foreach collection="targetUserIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        and product_id in
        <foreach collection="productIds" item="productId" separator="," open="(" close=")">
            #{productId}
        </foreach>
    </select>
    <select id="queryNoRefundUserId" resultType="java.lang.Long">
        select o.user_id
        from `order` o
        where o.order_status not in(1,4)
        <if test="targetUserIdList!= null and targetUserIds.size()>0">
            and o.user_id in
            <foreach collection="targetUserIdList" item="userId" separator="," open="(" close=")">
                #{userId}
            </foreach>
        </if>
        and not exists(select 1 from order_refund orf where o.order_id=orf.order_id)
        group by o.user_id
        <if test="noRefundOperation == 1">
            having count(*) &gt; #{noRefundValue}
        </if>
        <if test="noRefundOperation == 2">
            having count(*) &lt; #{noRefundValue}
        </if>
        <if test="noRefundOperation == 3">
            having count(*) BETWEEN #{noRefundValue} and #{noRefundValue2}
        </if>
    </select>
    <select id="queryIntegralRedemOrder"
            resultType="com.sankuai.shangou.seashop.order.thrift.core.response.order.IntegralRedemOrderResp">
        select o.order_id, o.total_amount, o.user_name, order_date, order_status, sum(oi.quantity) 'totalQty'
        from `order` o
        left join `order_item` oi on o.order_id = oi.order_id
        where oi.redem_id = #{query.redemId}
        <if test="query.orderId != null and query.orderId != '' ">
            and o.order_id = #{query.orderId}
        </if>
        <if test="query.orderDateStart != null">
            and o.order_date &gt;= #{query.orderId}
        </if>
        <if test="query.orderDateEnd != null">
            and o.order_date &lt;= #{query.orderDateEnd}
        </if>
        <if test="query.orderStatus != null">
            and o.order_status = #{query.orderStatus}
        </if>
        <if test="query.userName != null">
            and o.user_name like concat('%', #{query.userName}, '%')
        </if>
        group by o.order_id
        order by o.order_date desc
    </select>
    <select id="countIntegralBought" resultType="java.lang.Integer">
        select ifnull(sum(oi.quantity), 0) number
        from `order` o
                 join order_item oi on oi.order_id = o.id
        where o.order_type = 3
          and o.order_status not in (4, 5)
          and o.user_id = #{userId}

          and oi.integral_redeem_id = #{integralRedeemId}

    </select>
</mapper>
