<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.OrderRefundRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefundRecord">
        <id column="id" property="id" />
        <result column="relate_id" property="relateId" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_no" property="refundNo" />
        <result column="refund_type" property="refundType" />
        <result column="business_type" property="businessType" />
        <result column="refund_status" property="refundStatus" />
        <result column="err_msg" property="errMsg" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_name" property="operatorName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, relate_id, refund_amount, refund_no, refund_type, business_type, refund_status, err_msg, operator_id, operator_name, create_time, update_time
    </sql>

</mapper>
