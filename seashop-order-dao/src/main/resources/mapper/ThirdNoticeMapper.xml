<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.ThirdNoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.ThirdNotice">
        <id column="id" property="id" />
        <result column="biz_code" property="bizCode" />
        <result column="notice_type" property="noticeType" />
        <result column="notice_source" property="noticeSource" />
        <result column="notice_code" property="noticeCode" />
        <result column="notice_body" property="noticeBody" />
        <result column="handle_status" property="handleStatus" />
        <result column="handle_result" property="handleResult" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, biz_code, notice_type, notice_source, notice_code, notice_body, handle_status, handle_result, create_time, update_time
    </sql>

</mapper>
