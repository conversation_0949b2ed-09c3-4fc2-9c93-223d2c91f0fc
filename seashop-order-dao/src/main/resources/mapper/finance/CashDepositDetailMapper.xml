<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.CashDepositDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositDetail">
        <id column="id" property="id" />
        <result column="cash_deposit_id" property="cashDepositId" />
        <result column="add_date" property="addDate" />
        <result column="balance" property="balance" />
        <result column="operator" property="operator" />
        <result column="description" property="description" />
        <result column="recharge_way" property="rechargeWay" />
        <result column="operator_type" property="operatorType" />
        <result column="channel_order_id" property="channelOrderId" />
        <result column="platform_deduction" property="platformDeduction" />
        <result column="forzen_amount" property="forzenAmount" />
        <result column="refund_amount" property="refundAmount" />
        <result column="trade_no" property="tradeNo" />
        <result column="channel_id" property="channelId" />
        <result column="deduction_type" property="deductionType" />
        <result column="deduction_fee" property="deductionFee" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cash_deposit_id, add_date, balance, operator, description, recharge_way, operator_type, channel_order_id, platform_deduction, forzen_amount, refund_amount, trade_no, channel_id, deduction_type, deduction_fee, create_time, update_time
    </sql>

</mapper>
