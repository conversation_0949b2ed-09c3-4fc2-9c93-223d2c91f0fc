<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.ShopAccountItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.finance.domain.ShopAccountItem">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="account_no" property="accountNo" />
        <result column="account_id" property="accountId" />
        <result column="amount" property="amount" />
        <result column="trade_type" property="tradeType" />
        <result column="income_flag" property="incomeFlag" />
        <result column="remark" property="remark" />
        <result column="detail_id" property="detailId" />
        <result column="settlement_cycle" property="settlementCycle" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, shop_name, account_no, account_id, amount, trade_type, income_flag, remark, detail_id, settlement_cycle, create_time, update_time
    </sql>

</mapper>
