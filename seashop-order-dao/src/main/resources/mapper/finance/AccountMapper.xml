<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.AccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.finance.domain.Account">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="account_date" property="accountDate" />
        <result column="start_date" property="startDate" />
        <result column="end_date" property="endDate" />
        <result column="status" property="status" />
        <result column="product_actual_paid_amount" property="productActualPaidAmount" />
        <result column="freight_amount" property="freightAmount" />
        <result column="commission_amount" property="commissionAmount" />
        <result column="refund_commission_amount" property="refundCommissionAmount" />
        <result column="refund_amount" property="refundAmount" />
        <result column="advance_payment_amount" property="advancePaymentAmount" />
        <result column="period_settlement" property="periodSettlement" />
        <result column="remark" property="remark" />
        <result column="payment_channel" property="paymentChannel" />
        <result column="channel_amount" property="channelAmount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, shop_name, account_date, start_date, end_date, status, product_actual_paid_amount, freight_amount, commission_amount, refund_commission_amount, refund_amount, advance_payment_amount, period_settlement, remark, payment_channel, channel_amount, create_time, update_time
    </sql>

</mapper>
