<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.FinanceItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.finance.domain.FinanceItem">
        <id column="id" property="id" />
        <result column="finance_id" property="financeId" />
        <result column="order_id" property="orderId" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="sku" property="sku" />
        <result column="quantity" property="quantity" />
        <result column="original_price" property="originalPrice" />
        <result column="sale_price" property="salePrice" />
        <result column="total_amount" property="totalAmount" />
        <result column="discount_price" property="discountPrice" />
        <result column="full_discount" property="fullDiscount" />
        <result column="money_off" property="moneyOff" />
        <result column="coupon_discount" property="couponDiscount" />
        <result column="plat_coupon_discount" property="platCouponDiscount" />
        <result column="commis_rate" property="commisRate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, finance_id, order_id, product_id, product_name, sku, quantity, original_price, sale_price, total_amount, discount_price, full_discount, money_off, coupon_discount, plat_coupon_discount, commis_rate, create_time, update_time
    </sql>

</mapper>
