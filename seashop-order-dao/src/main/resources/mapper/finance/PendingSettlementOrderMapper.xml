<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.PendingSettlementOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.finance.domain.PendingSettlementOrder">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="order_id" property="orderId" />
        <result column="order_type" property="orderType" />
        <result column="order_amount" property="orderAmount" />
        <result column="products_amount" property="productsAmount" />
        <result column="freight_amount" property="freightAmount" />
        <result column="tax_amount" property="taxAmount" />
        <result column="plat_commission" property="platCommission" />
        <result column="refund_amount" property="refundAmount" />
        <result column="refund_date" property="refundDate" />
        <result column="plat_commission_return" property="platCommissionReturn" />
        <result column="settlement_amount" property="settlementAmount" />
        <result column="pay_date" property="payDate" />
        <result column="order_finish_time" property="orderFinishTime" />
        <result column="payment_type" property="paymentType" />
        <result column="payment_type_name" property="paymentTypeName" />
        <result column="discount_amount" property="discountAmount" />
        <result column="discount_amount_return" property="discountAmountReturn" />
        <result column="payment_channel" property="paymentChannel" />
        <result column="channel_amount" property="channelAmount" />
        <result column="settelement_remark" property="settelementRemark" />
        <result column="self_flag" property="selfFlag" />
        <result column="delete_flag" property="deleteFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, shop_name, order_id, order_type, order_amount, products_amount, freight_amount, tax_amount, plat_commission, refund_amount, refund_date, plat_commission_return, settlement_amount, pay_date, order_finish_time, payment_type, payment_type_name, discount_amount, discount_amount_return, payment_channel, channel_amount, settelement_remark, self_flag, delete_flag, create_time, update_time
    </sql>

</mapper>
