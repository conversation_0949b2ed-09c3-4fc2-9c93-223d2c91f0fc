<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.CashDepositRefundMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositRefund">
        <id column="id" property="id" />
        <result column="cash_deposit_detail_id" property="cashDepositDetailId" />
        <result column="shop_id" property="shopId" />
        <result column="status" property="status" />
        <result column="bond" property="bond" />
        <result column="deduction" property="deduction" />
        <result column="refund" property="refund" />
        <result column="apply_date" property="applyDate" />
        <result column="remark" property="remark" />
        <result column="refund_order_id" property="refundOrderId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, cash_deposit_detail_id, shop_id, status, bond, deduction, refund, apply_date, remark, refund_order_id, create_time, update_time
    </sql>

</mapper>
