<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.CashDepositPayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositPay">
        <id column="id" property="id" />
        <result column="pay_id" property="payId" />
        <result column="adapay_id" property="adapayId" />
        <result column="pay_date" property="payDate" />
        <result column="pay_status" property="payStatus" />
        <result column="payment" property="payment" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        Id, pay_id, adapay_id, pay_date, pay_status, payment, create_time, update_time
    </sql>

</mapper>
