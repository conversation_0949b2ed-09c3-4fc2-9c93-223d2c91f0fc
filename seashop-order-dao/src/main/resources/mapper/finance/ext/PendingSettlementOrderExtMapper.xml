<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.ext.PendingSettlementOrderExtMapper">


    <select id="sumSettlementAmount" resultType="java.math.BigDecimal">
        select
        ifnull(sum(settlement_amount),0)
        from finance_pending_settlement_order
        where delete_flag = 0
        <if test="shopId != null">
            and shop_id = #{shopId}
        </if>
    </select>

    <select id="getPendingSettlementList"
            resultType="com.sankuai.shangou.seashop.order.dao.finance.model.PendingSettlementModel">

        SELECT
        shop_id as shopId,
        ifnull(sum( settlement_amount ),0) as settlementAmount
        FROM
        finance_pending_settlement_order
        <where>
            delete_flag = 0
            <if test="query.shopId !=null">
                and shop_id = #{query.shopId}
            </if>
            <if test="query.shopIdList !=null and query.shopIdList.size > 0">
                and shop_id in
                <foreach collection="query.shopIdList" item="shopId" open="(" separator="," close=")">
                    #{shopId}
                </foreach>
            </if>
        </where>
        GROUP BY
        shop_id
        <if test="query.settlementAmountSort == true">
            order by sum( settlement_amount ) asc
        </if>
        <if test="query.settlementAmountSort == false">
            order by sum( settlement_amount ) desc
        </if>

    </select>

    <select id="getPlatCommission" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(plat_commission), 0)
        FROM finance_pending_settlement_order
        WHERE delete_flag = 0
    </select>
</mapper>
