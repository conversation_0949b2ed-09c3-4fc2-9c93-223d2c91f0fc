<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.CashDepositMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.finance.domain.CashDeposit">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="current_balance" property="currentBalance" />
        <result column="total_balance" property="totalBalance" />
        <result column="date" property="date" />
        <result column="enable_labels" property="enableLabels" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, current_balance, total_balance, date, enable_labels, create_time, update_time
    </sql>

</mapper>
