<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.AccountDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.finance.domain.AccountDetail">
        <id column="id" property="id" />
        <result column="account_id" property="accountId" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="date" property="date" />
        <result column="order_date" property="orderDate" />
        <result column="pay_date" property="payDate" />
        <result column="order_finish_date" property="orderFinishDate" />
        <result column="order_type" property="orderType" />
        <result column="order_id" property="orderId" />
        <result column="order_amount" property="orderAmount" />
        <result column="product_actual_paid_amount" property="productActualPaidAmount" />
        <result column="freight_amount" property="freightAmount" />
        <result column="tax_amount" property="taxAmount" />
        <result column="commission_amount" property="commissionAmount" />
        <result column="refund_total_amount" property="refundTotalAmount" />
        <result column="refund_commis_amount" property="refundCommisAmount" />
        <result column="order_refunds_dates" property="orderRefundsDates" />
        <result column="settlement_amount" property="settlementAmount" />
        <result column="payment_type_name" property="paymentTypeName" />
        <result column="discount_amount" property="discountAmount" />
        <result column="discount_amount_return" property="discountAmountReturn" />
        <result column="payment_channel" property="paymentChannel" />
        <result column="payment_type" property="paymentType" />
        <result column="channel_amount" property="channelAmount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, account_id, shop_id, shop_name, date, order_date, pay_date, order_finish_date, order_type, order_id, order_amount, product_actual_paid_amount, freight_amount, tax_amount, commission_amount, refund_total_amount, refund_commis_amount, order_refunds_dates, settlement_amount, payment_type_name, discount_amount, discount_amount_return, payment_channel, payment_type, channel_amount, create_time, update_time
    </sql>

    <select id="detailCount"
            resultType="com.sankuai.shangou.seashop.order.dao.finance.model.AccountDetailCountModel">
        SELECT
            shop_id as shopId,
            account_id as detailId,
            sum( settlement_amount ) as settleAmount,
            sum( commission_amount ) as commissionAmount
        FROM
            finance_account_detail
        WHERE
            shop_id = #{shopId}
          AND account_id = #{detailId}
        GROUP BY
            shop_id,
            account_id

    </select>

</mapper>
