<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.finance.mapper.FinanceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.finance.domain.Finance">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="pay_id" property="payId" />
        <result column="adapay_id" property="adapayId" />
        <result column="type" property="type" />
        <result column="create_date" property="createDate" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="transaction_id" property="transactionId" />
        <result column="total_amount" property="totalAmount" />
        <result column="refund_type" property="refundType" />
        <result column="freight" property="freight" />
        <result column="product_amount" property="productAmount" />
        <result column="discount_amount" property="discountAmount" />
        <result column="plat_discount_amount" property="platDiscountAmount" />
        <result column="full_discount" property="fullDiscount" />
        <result column="money_off" property="moneyOff" />
        <result column="integral_discount" property="integralDiscount" />
        <result column="service_amount" property="serviceAmount" />
        <result column="settlement_amount" property="settlementAmount" />
        <result column="commission_amount" property="commissionAmount" />
        <result column="deduction_type" property="deductionType" />
        <result column="service_fee" property="serviceFee" />
        <result column="order_refund_id" property="orderRefundId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, pay_id, adapay_id, type, create_date, shop_id, shop_name, user_id, user_name, transaction_id, total_amount, refund_type, freight, product_amount, discount_amount, plat_discount_amount, full_discount, money_off, integral_discount, service_amount, settlement_amount, commission_amount, deduction_type, service_fee, order_refund_id, create_time, update_time
    </sql>

</mapper>
