<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.CustomOrderMapper">


    <update id="batchUpdateItemCouponAmount">
        update `order_item`
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="coupon_discount=case" suffix="end,">
                <foreach collection="list" item="item" index="index">
                    <if test="item.couponDiscount != null">
                        when id=#{item.id} then #{item.couponDiscount}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="list" separator="or" item="item" index="index" >
            id = #{item.id}
        </foreach>
    </update>

    <update id="updateOrderShipInfo">
        update `order`
        set ship_to=#{shipTo}, cell_phone=#{cellPhone}, top_region_id=#{topRegionId},
            region_id=#{regionId}, region_full_name=#{regionFullName}, address=#{address}
        where order_id = #{orderId}
    </update>

    <update id="updateReceiveDelay">
        update `order`
        set receive_delay=#{receiveDelay}
        where order_id = #{orderId}
    </update>

    <update id="updateOrderReceived">
        update `order`
        <set>
            order_status=#{targetStatus}, finish_date=#{finishTime}, last_modify_time=#{finishTime}
            <if test="closeReason != null">
                ,close_reason=#{closeReason}
            </if>
        </set>
        where order_id = #{orderId} and order_status = #{originStatus}
    </update>

    <update id="updateLastModifyTime">
        update `order`
        set last_modify_time=#{lastModifyTime}
        where order_id = #{orderId}
    </update>

    <select id="orderStatistics"
            resultType="com.sankuai.shangou.seashop.order.dao.core.model.OrderStatisticsModel">
        SELECT
            DATE( pay_date ) as payDate,
            ifnull( sum( product_total_amount ), 0 ) as productTotalAmount,
            ifnull( sum( freight ), 0 ) as freight,
            ifnull( sum( tax ), 0 ) as tax,
            ifnull( sum( coupon_amount ), 0 ) as couponAmount,
            ifnull( sum( discount_amount ), 0 ) as discountAmount,
            ifnull( sum( money_off_amount ), 0 ) as moneyOffAmount
        FROM
            `order`
        WHERE
            1 = 1
            <if test = " startPayDate != null and endPayDate != null">
                AND pay_date BETWEEN #{startPayDate} AND #{endPayDate}
            </if>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
        GROUP BY
            DATE(pay_date)
    </select>

    <select id="sumOrderAmount" resultType="java.math.BigDecimal"
            parameterType="com.sankuai.shangou.seashop.order.dao.core.po.OrderStatisticsPo">
        SELECT
            ifnull(sum(
                ifnull( product_total_amount, 0 )
                + ifnull( freight, 0 )
                + ifnull( tax, 0 )
                - ifnull( coupon_amount, 0 )
                - ifnull( discount_amount, 0 )
                - ifnull( 	money_off_amount, 0 )
            ),0)
        FROM
            `order`
        WHERE
            1 = 1
            <if test = " startPayDate != null and endPayDate != null">
                AND pay_date BETWEEN #{startPayDate} AND #{endPayDate}
            </if>
            <if test="shopId != null">
                AND shop_id = #{shopId}
            </if>
    </select>

    <select id="sumOrderAmountMember" resultType="java.math.BigDecimal"
            parameterType="com.sankuai.shangou.seashop.order.dao.core.po.OrderStatisticsPo">
        SELECT
        ifnull(sum(
            ifnull( product_total_amount, 0 )
                + ifnull( freight, 0 )
                + ifnull( tax, 0 )
                - ifnull( coupon_amount, 0 )
                - ifnull( discount_amount, 0 )
                - ifnull( 	money_off_amount, 0 )
        ),0)
        FROM
        `order`
        WHERE
        1 = 1
        <if test = " startPayDate != null and endPayDate != null">
            AND pay_date BETWEEN #{startPayDate} AND #{endPayDate}
        </if>
        <if test="shopId != null">
            AND user_id = #{shopId}
        </if>
    </select>

    <update id="increaseApplyRefundQuantity">
        update `order_item`
        set apply_refund_quantity = apply_refund_quantity + #{applyRefundQuantity}
        where id = #{id} and apply_refund_quantity + #{applyRefundQuantity} <![CDATA[ <= ]]> quantity
    </update>

    <update id="decreaseApplyRefundQuantity">
        update `order_item`
        set apply_refund_quantity = apply_refund_quantity - #{applyRefundQuantity}
        where id = #{id} and apply_refund_quantity - #{applyRefundQuantity} >= 0
    </update>

</mapper>
