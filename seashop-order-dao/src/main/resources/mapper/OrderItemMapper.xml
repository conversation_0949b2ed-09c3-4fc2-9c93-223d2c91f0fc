<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.OrderItemMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="shop_id" property="shopId" />
        <result column="product_id" property="productId" />
        <result column="sku_id" property="skuId" />
        <result column="sku" property="sku" />
        <result column="quantity" property="quantity" />
        <result column="return_quantity" property="returnQuantity" />
        <result column="cost_price" property="costPrice" />
        <result column="sale_price" property="salePrice" />
        <result column="discount_amount" property="discountAmount" />
        <result column="real_total_price" property="realTotalPrice" />
        <result column="refund_price" property="refundPrice" />
        <result column="product_name" property="productName" />
        <result column="color" property="color" />
        <result column="size" property="size" />
        <result column="version" property="version" />
        <result column="thumbnails_url" property="thumbnailsUrl" />
        <result column="commis_rate" property="commisRate" />
        <result column="enabled_refund_amount" property="enabledRefundAmount" />
        <result column="apply_refund_quantity" property="applyRefundQuantity" />
        <result column="is_limit_buy" property="isLimitBuy" />
        <result column="coupon_discount" property="couponDiscount" />
        <result column="full_discount" property="fullDiscount" />
        <result column="money_off" property="moneyOff" />
        <result column="active_id" property="activeId" />
        <result column="flash_sale_id" property="flashSaleId" />
        <result column="sku_auto_id" property="skuAutoId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, shop_id, product_id, sku_id, sku, quantity, return_quantity, cost_price, sale_price, discount_amount, real_total_price, refund_price, product_name, color, size, version, thumbnails_url, commis_rate, enabled_refund_amount, apply_refund_quantity, is_limit_buy, coupon_discount, full_discount, money_off, active_id, flash_sale_id, sku_auto_id, create_time, update_time
    </sql>

</mapper>
