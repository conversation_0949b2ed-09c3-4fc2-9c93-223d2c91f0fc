<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.MqErrorDataMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.MqErrorData">
        <id column="id" property="id" />
        <result column="biz_type" property="bizType" />
        <result column="biz_type_desc" property="bizTypeDesc" />
        <result column="biz_no" property="bizNo" />
        <result column="message_id" property="messageId" />
        <result column="trace_id" property="traceId" />
        <result column="error_message" property="errorMessage" />
        <result column="data_content" property="dataContent" />
        <result column="handle_status" property="handleStatus" />
        <result column="re_exe_error_msg" property="reExeErrorMsg" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, biz_type, biz_type_desc, biz_no, message_id, trace_id, error_message, data_content, handle_status, re_exe_error_msg, create_time, update_time
    </sql>

</mapper>
