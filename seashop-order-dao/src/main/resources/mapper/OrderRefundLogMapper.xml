<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.OrderRefundLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefundLog">
        <id column="id" property="id" />
        <result column="refund_id" property="refundId" />
        <result column="operator" property="operator" />
        <result column="operate_date" property="operateDate" />
        <result column="operate_content" property="operateContent" />
        <result column="apply_number" property="applyNumber" />
        <result column="step" property="step" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, refund_id, operator, operate_date, operate_content, apply_number, step, remark, create_time, update_time
    </sql>

</mapper>
