<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.pay.dao.core.mapper.OrderPayMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.pay.dao.core.domain.OrderPay">
        <id column="id" property="id"/>
        <result column="pay_id" property="payId"/>
        <result column="order_id" property="orderId"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_state" property="payState"/>
        <result column="pay_time" property="payTime"/>
        <result column="payment_channel" property="paymentChannel"/>
        <result column="payment_type" property="paymentType"/>
        <result column="business_type" property="businessType"/>
        <result column="channel_confirm_id" property="channelConfirmId"/>
        <result column="channel_pay_id" property="channelPayId"/>
        <result column="channel_pay_msg" property="channelPayMsg"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , pay_id, order_id, pay_amount, pay_state, pay_time, payment_channel, payment_type, business_type, channel_confirm_id, channel_pay_id, channel_pay_msg, create_time, update_time
    </sql>

    <select id="updateUnPaid" parameterType="java.lang.String">
        UPDATE pay_order_pay
        SET pay_state = #{status}
        WHERE order_id IN
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
