<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.pay.dao.core.mapper.ReverseOrderMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.pay.dao.core.domain.ReverseOrder">
        <id column="id" property="id" />
        <result column="channel_pay_id" property="channelPayId" />
        <result column="reverse_id" property="reverseId" />
        <result column="reverse_amount" property="reverseAmount" />
        <result column="reverse_state" property="reverseState" />
        <result column="reverse_time" property="reverseTime" />
        <result column="channel_refund_id" property="channelRefundId" />
        <result column="channel_refund_msg" property="channelRefundMsg" />
        <result column="reverse_type" property="reverseType" />
        <result column="business_type" property="businessType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, channel_pay_id, reverse_id, reverse_amount, reverse_state, reverse_time, channel_refund_id, channel_refund_msg, reverse_type, business_type, create_time, update_time
    </sql>

</mapper>
