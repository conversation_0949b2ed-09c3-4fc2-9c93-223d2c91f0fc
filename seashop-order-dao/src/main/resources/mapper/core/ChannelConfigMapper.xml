<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.pay.dao.core.mapper.ChannelConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.pay.dao.core.domain.ChannelConfig">
        <id column="id" property="id" />
        <result column="payment_channel" property="paymentChannel" />
        <result column="config_name" property="configName" />
        <result column="config_key" property="configKey" />
        <result column="config_value" property="configValue" />
        <result column="config_desc" property="configDesc" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, payment_channel, config_name, config_key, config_value, config_desc, create_time, update_time
    </sql>

    <select id="getWxPayConfig" resultType="com.sankuai.shangou.seashop.pay.dao.core.model.WxPayConfigModel">
        SELECT
            MAX(CASE WHEN config_key = 'serviceMode' THEN config_value ELSE false END) AS serviceMode,
            MAX(CASE WHEN config_key = 'serviceMchId' THEN config_value ELSE NULL END) AS serviceMchId,
            MAX(CASE WHEN config_key = 'serviceAppId' THEN config_value ELSE NULL END) AS serviceAppId,
            MAX(CASE WHEN config_key = 'apiV3Key' THEN config_value ELSE NULL END) AS apiV3Key,
            MAX(CASE WHEN config_key = 'serviceP12KeyPath' THEN config_value ELSE NULL END) AS serviceP12KeyPath,
            MAX(CASE WHEN config_key = 'mchId' THEN config_value ELSE NULL END) AS mchId,
            MAX(CASE WHEN config_key = 'p12KeyPath' THEN config_value ELSE NULL END) AS p12KeyPath,
            MAX(CASE WHEN config_key = 'enableApplet' THEN config_value ELSE false END) AS enableApplet,
            MAX(CASE WHEN config_key = 'miniProgramAppId' THEN config_value ELSE NULL END) AS miniProgramAppId,
            MAX(CASE WHEN config_key = 'enableH5' THEN config_value ELSE false END) AS enableH5,
            MAX(CASE WHEN config_key = 'officialAccountAppId' THEN config_value ELSE NULL END) AS officialAccountAppId,
            MAX(CASE WHEN config_key = 'enableNative' THEN config_value ELSE false END) AS enableNative,
            MAX(CASE WHEN config_key = 'nativeAppId' THEN config_value ELSE NULL END) AS nativeAppId,
            MAX(CASE WHEN config_key = 'enableApp' THEN config_value ELSE false END) AS enableApp,
            MAX(CASE WHEN config_key = 'applicationAppId' THEN config_value ELSE NULL END) AS applicationAppId
        FROM
            pay_channel_config
        WHERE
            payment_channel = 2;
    </select>

    <select id="getAliPayConfig" resultType="com.sankuai.shangou.seashop.pay.dao.core.model.AliPayConfigModel">
        SELECT
            MAX(CASE WHEN config_key = 'aliPayAppSecret' THEN config_value ELSE false END) AS aliPayAppSecret,
            MAX(CASE WHEN config_key = 'aliPayCertPath' THEN config_value ELSE NULL END) AS aliPayCertPath,
            MAX(CASE WHEN config_key = 'aliPayAppCertPath' THEN config_value ELSE NULL END) AS aliPayAppCertPath,
            MAX(CASE WHEN config_key = 'aliPayRootCertPath' THEN config_value ELSE NULL END) AS aliPayRootCertPath,
            MAX(CASE WHEN config_key = 'aliPayAppId' THEN config_value ELSE NULL END) AS aliPayAppId
        FROM
            pay_channel_config
        WHERE
            payment_channel = 3;
    </select>

    <select id="getPayOpenState" resultType="com.sankuai.shangou.seashop.pay.dao.core.model.PayOpenStateModel">
        SELECT
            MAX(CASE WHEN payment_channel = '1' THEN config_value ELSE false END) AS `openAdaPay`,
            MAX(CASE WHEN payment_channel = '2' THEN config_value ELSE false END) AS `openWxPay`,
            MAX(CASE WHEN payment_channel = '3' THEN config_value ELSE false END) AS `openAliPay`
        FROM
            pay_channel_config
        WHERE
            config_key = 'izOpen';
    </select>

</mapper>
