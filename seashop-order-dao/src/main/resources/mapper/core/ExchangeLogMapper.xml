<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.pay.dao.core.mapper.ExchangeLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.pay.dao.core.domain.ExchangeLog">
        <id column="id" property="id" />
        <result column="type" property="type" />
        <result column="method" property="method" />
        <result column="param" property="param" />
        <result column="result" property="result" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, type, method, param, result, create_time
    </sql>

</mapper>
