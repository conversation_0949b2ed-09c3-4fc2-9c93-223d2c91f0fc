<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.ProductCommentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.ProductComment">
        <id column="id" property="id" />
        <result column="product_comment_id" property="productCommentId" />
        <result column="order_id" property="orderId" />
        <result column="sub_order_id" property="subOrderId" />
        <result column="product_id" property="productId" />
        <result column="product_name" property="productName" />
        <result column="thumbnails_url" property="thumbnailsUrl" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="email" property="email" />
        <result column="review_content" property="reviewContent" />
        <result column="review_date" property="reviewDate" />
        <result column="review_mark" property="reviewMark" />
        <result column="reply_content" property="replyContent" />
        <result column="reply_date" property="replyDate" />
        <result column="append_content" property="appendContent" />
        <result column="append_date" property="appendDate" />
        <result column="reply_append_content" property="replyAppendContent" />
        <result column="reply_append_date" property="replyAppendDate" />
        <result column="has_hidden" property="hasHidden" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="sku_id" property="skuId" />
        <result column="deleted" property="deleted" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, product_comment_id, order_id, sub_order_id, product_id, product_name, thumbnails_url, shop_id, shop_name, user_id, user_name, email, review_content, review_date, review_mark, reply_content, reply_date, append_content, append_date, reply_append_content, reply_append_date, has_hidden, create_time, update_time, sku_id, deleted
    </sql>

    <select id="listReviewMark" resultType="com.sankuai.shangou.seashop.order.dao.core.model.ReviewMarkModel">
        SELECT
            review_mark `reviewMark`,
            count( id ) `count`
        FROM
            order_product_comment
        WHERE
            product_id = #{productId}
        AND deleted = 0
        AND has_hidden = 0
        GROUP BY
            review_mark
    </select>

</mapper>
