<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderCommentExtMapper">


    <select id="getShopMarkByShopId" resultType="com.sankuai.shangou.seashop.order.dao.core.model.ShopMarkModel">
        select
            IFNULL(t.packMark,5) as packMark,
            IFNULL(t.serviceMark,5) as serviceMark,
            IFNULL(t.comprehensiveMark,5) as comprehensiveMark,
            t.countNum as markCount
        from (
                 SELECT
                     ROUND(sum(pack_mark)/count(1),1) as packMark,
                     ROUND(sum(delivery_mark)/count(1),1) as serviceMark,
                     ROUND(sum(service_mark)/count(1),1) as comprehensiveMark,
                     count(1) as countNum
                 FROM
                     order_comment
                 where shop_id = #{shopId} and deleted = false
             ) as t;
    </select>
</mapper>
