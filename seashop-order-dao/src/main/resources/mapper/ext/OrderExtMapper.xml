<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.ext.OrderExtMapper">

    <select id="countProduct" resultType="java.lang.Long">
        SELECT
            sum( t2.quantity - t2.return_quantity )
        FROM
            `order` t1
            INNER JOIN order_item t2 ON t1.order_id = t2.order_id
        WHERE
            t1.order_status = #{status}
          and t1.shop_id = #{shopId}
    </select>

</mapper>
