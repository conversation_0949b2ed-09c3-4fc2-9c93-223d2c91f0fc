<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.order.dao.core.mapper.OrderComplaintMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.order.dao.core.domain.OrderComplaint">
        <id column="id" property="id" />
        <result column="order_id" property="orderId" />
        <result column="status" property="status" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="shop_phone" property="shopPhone" />
        <result column="user_id" property="userId" />
        <result column="user_name" property="userName" />
        <result column="user_phone" property="userPhone" />
        <result column="complaint_date" property="complaintDate" />
        <result column="complaint_reason" property="complaintReason" />
        <result column="seller_reply" property="sellerReply" />
        <result column="plat_remark" property="platRemark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, order_id, status, shop_id, shop_name, shop_phone, user_id, user_name, user_phone, complaint_date, complaint_reason, seller_reply, plat_remark, create_time, update_time
    </sql>

    <select id="pageQueryOrderRights" parameterType="com.sankuai.shangou.seashop.order.dao.core.model.QueryOrderRightsReqModel" resultType="com.sankuai.shangou.seashop.order.dao.core.model.QueryOrderRightsRespModel">
        SELECT
            t.order_id AS orderId,
            t.create_time AS createTime
        FROM
        (
            SELECT
                a.order_id, a.create_time
            FROM
                `order` a
            WHERE 1 = 1 AND a.order_status = 5
            <if test="req.userId != null and req.userId != ''">
                AND a.user_id = #{req.userId}
            </if>
            AND NOT EXISTS ( SELECT 1 FROM order_complaint b WHERE a.order_id = b.order_id )
            UNION ALL
            SELECT
                a.order_id, a.create_time
            FROM
                `order` a
            WHERE 1 = 1 AND a.order_status = 5
            <if test="req.userId != null and req.userId != ''">
                AND a.user_id = #{req.userId}
            </if>
            AND EXISTS ( SELECT 1 FROM order_complaint b WHERE a.order_id = b.order_id AND b.STATUS = 5
                        AND b.order_id NOT IN ( SELECT c.order_id FROM order_complaint c WHERE c.order_id = b.order_id AND c.STATUS != 5 )
                    )
        ) t
        ORDER BY t.create_time DESC
    </select>
</mapper>
