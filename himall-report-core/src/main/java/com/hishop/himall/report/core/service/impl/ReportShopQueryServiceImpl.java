package com.hishop.himall.report.core.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.himall.report.api.enums.RangeEnum;
import com.hishop.himall.report.api.request.ReportReq;
import com.hishop.himall.report.api.request.ShopReq;
import com.hishop.himall.report.api.request.TradeReq;
import com.hishop.himall.report.api.response.EchartSeriesPieData;
import com.hishop.himall.report.api.response.Echarts;
import com.hishop.himall.report.api.response.EchartSeries;
import com.hishop.himall.report.api.response.ShopReportResp;
import com.hishop.himall.report.api.response.TradeResp;
import com.hishop.himall.report.api.response.TradeSummaryResp;
import com.hishop.himall.report.core.service.ReportShopQueryService;
import com.hishop.himall.report.dao.domain.ReportShopTrade;
import com.hishop.himall.report.dao.mapper.ReportShopTradeMapper;
import com.hishop.himall.report.dao.models.IncreaseSource;
import com.hishop.himall.report.dao.models.ProvinceSource;
import com.hishop.himall.report.dao.models.ShopTrade;
import com.hishop.himall.report.dao.models.TradeSummary;
import com.hishop.starter.util.model.AbstractPageUtilReq;
import com.hishop.starter.util.model.PageResult;
import com.hishop.starter.web.util.PageUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;


@Service
public class ReportShopQueryServiceImpl implements ReportShopQueryService {

    @Resource
    private ReportShopTradeMapper reportShopTradeMapper;

    /**
     * 店铺新增趋势
     *
     * @param reportReq
     * @return
     */
    @Override public Echarts getNewShopEchats(ReportReq reportReq) {
        reportReq.getPrev();
        Echarts echarts = new Echarts();
        EchartSeries series = echarts.addSeries("新增店铺数");

        List<IncreaseSource> source = reportShopTradeMapper.getNewShops(reportReq.getRange().toString(),
                                                                        reportReq.getStartDate(), reportReq.getEndDate());

        return echarts.each(reportReq.getRange().toString(), reportReq.getStart(), reportReq.getEnd(), (date) -> {
            Integer num = 0;
            if(CollectionUtils.isNotEmpty(source)) {
                // num 等于 source中的count之和
                num = source.stream().mapToInt(IncreaseSource::getCount).sum();
            }
            series.add(num);
        });
    }

    /**
     * 店铺省份分布
     *
     * @param reportReq
     */
    @Override public Echarts getProvinceWithShop(ReportReq reportReq) {
        reportReq.getPrev();
        Echarts echarts = new Echarts();
        EchartSeries series = echarts.addSeries("店铺总数");

        List<ProvinceSource> source = reportShopTradeMapper.getProvinceShops(reportReq.getRange().toString(),
                                                                             reportReq.getStartDate(),
                                                                             reportReq.getEndDate());
        for (ProvinceSource provinceSource : source) {
            EchartSeriesPieData data = new EchartSeriesPieData();
            data.setName(provinceSource.getProvinceName());
            data.setValue(BigDecimal.valueOf(provinceSource.getShops()));
            series.getData().add(data);
        }

        return echarts;
    }

    /**
     * 店铺交易导出
     *
     * @param shopReq
     */
    @Override public void exportShopTrade(ShopReq shopReq) {
        List<ShopTrade> source = reportShopTradeMapper.getShopTrades(shopReq.getRange().toString(), shopReq.getStart(),
                                                                     shopReq.getEnd(), shopReq.getShopName());
    }

    /**
     * 店铺交易
     *
     * @param shopReq
     */
    @Override public PageResult<ShopReportResp> getShopTrade(ShopReq shopReq) {
        Page<ShopTrade> source = reportShopTradeMapper.getShopTrades(Page.of(shopReq.getPageNo(), shopReq.getPageSize(), true),
                                                                     shopReq.getRange().toString(), shopReq.getStart(),
                                                                     shopReq.getEnd(), shopReq.getShopName());
        return PageUtil.buildPage(source, ShopReportResp.class);
    }

    /**
     * 店铺交易趋势
     *
     * @param req
     */
    @Override public TradeSummaryResp getTradeSummary(ReportReq req) {
        //日期参数转换
        req.getPrev();
        TradeSummary current = reportShopTradeMapper.getTradeSummary(req.getShopId(), req.getRange().toString(),
                                                                     req.getStartDate(), req.getEndDate());
        TradeSummaryResp tradeSummaryResp = Map(current);

        TradeSummary prev = reportShopTradeMapper.getTradeSummary(req.getShopId(), req.getRange().toString(),
                                                                  req.getStartDate(), req.getEndDate());
        tradeSummaryResp.setPrev(Map(prev));
        return tradeSummaryResp;
    }

    private TradeSummaryResp Map(TradeSummary summary) {
        TradeSummaryResp result = new TradeSummaryResp();
        if(summary == null)
            return result;

        result.setVisitsCount(summary.getVisitsCount());
        result.setVisitsUsers(summary.getVisitsUsers());
        result.setOrderAmount(summary.getOrderAmount());
        result.setOrderOrders(summary.getOrderOrders());
        result.setOrderUsers(summary.getOrderUsers());
        result.setPaymentAmount(summary.getPaymentAmount());
        result.setPaymentQuantity(summary.getPaymentQuantity());
        result.setPaymentOrders(summary.getPaymentOrders());
        result.setPaymentUsers(summary.getPaymentUsers());
        result.setUnitPrice(summary.getUnitPrice());
        result.setOrderVisitsRate(summary.getOrderVisitsRate());
        result.setPaymentOrderRate(summary.getPaymentOrderRate());
        result.setPaymentVisitsRate(summary.getPaymentVisitsRate());
        return result;
    }


    /**
     * 店铺交易趋势
     *
     * @param reportReq
     * @return
     */
    @Override public Echarts getTradeEchats(ReportReq reportReq) {
        reportReq.getPrev();
        reportReq.setRange(RangeEnum.DAY);//图表强制为日
        List<TradeSummary> summaries = reportShopTradeMapper.getTrades(reportReq.getRange().toString(),
                                                                       reportReq.getStartDate(), reportReq.getEndDate(),reportReq.getShopId());
        Echarts echarts = new Echarts();
        EchartSeries paymentAmountSeries = echarts.addSeries("支付金额");
        EchartSeries paymentUserSeries = echarts.addSeries("支付人数");
        EchartSeries paymentQuantitySeries = echarts.addSeries("支付件数");
        EchartSeries orderVisitsSeries = echarts.addSeries("订单-访客转化率");
        EchartSeries paymentOrderSeries =  echarts.addSeries("支付-下单转化率");
        EchartSeries paymentVisitsSeries = echarts.addSeries("支付-访客转化率");

        return echarts.each(reportReq.getRange().toString(), reportReq.getStart(), reportReq.getEnd(), (date) -> {
            TradeSummary current = summaries.stream()
                    .filter(item -> item.getDate().equals(date))
                    .findFirst()
                    .orElse(new TradeSummary());

            paymentAmountSeries.add(current.getPaymentAmount());
            paymentUserSeries.add(current.getPaymentUsers());
            paymentQuantitySeries.add(current.getPaymentQuantity());
            orderVisitsSeries.add(current.getOrderVisitsRate());
            paymentOrderSeries.add(current.getPaymentOrderRate());
            paymentVisitsSeries.add(current.getPaymentVisitsRate());
        });
    }

    /**
     * 省份交易趋势
     *
     * @param reportReq
     * @return
     */
    @Override public Echarts getProvinceTradeEchats(ReportReq reportReq) {
        reportReq.FillDate();
        Echarts echarts = new Echarts();
        EchartSeries paymentAmountSeries = echarts.addSeries("支付金额");
        EchartSeries paymentOrderSeries = echarts.addSeries("支付订单笔数");
        EchartSeries paymentUserSeries = echarts.addSeries("支付人数");
        List<ProvinceSource> sources = reportShopTradeMapper.getPriovinceTrades(reportReq.getRange().toString(),
                                                                                reportReq.getStartDate(),
                                                                                reportReq.getEndDate(),
                                                                                reportReq.getShopId());
        for (ProvinceSource provinceSource : sources) {
            paymentAmountSeries.add(provinceSource.getProvinceName(), provinceSource.getPaymentAmount());
            paymentOrderSeries.add(provinceSource.getProvinceName(),
                                   BigDecimal.valueOf(provinceSource.getPaymentOrders()));
            paymentUserSeries.add(provinceSource.getProvinceName(),
                                  BigDecimal.valueOf(provinceSource.getPaymentUsers()));
        }
        paymentAmountSeries.sort();
        paymentOrderSeries.sort();
        paymentUserSeries.sort();
        return echarts;
    }

    /**
     * 获取交易列表
     *
     * @param tradeReq
     * @return
     */
    @Override public PageResult<TradeResp> getTrades(TradeReq tradeReq) {
        Page<ReportShopTrade> source = reportShopTradeMapper.selectPage(PageUtil.toPage(tradeReq, true),
                                                                       new QueryWrapper<ReportShopTrade>()
                                                                            .eq("`range`", tradeReq.getRange().toString())
                                                                            .eq("shop_id", tradeReq.getShopId())
                                                                            .between("date",  tradeReq.getStart(), tradeReq.getEnd()));
        return PageUtil.buildPage(source,TradeResp.class);
    }

    /**
     * 导出交易报表
     *
     * @param tradeReq
     */
    @Override public void exportTrades(TradeReq tradeReq) {
        Date end = tradeReq.getEnd();
        tradeReq.setEnd(end);
        List<TradeSummary> summaries = reportShopTradeMapper.getTrades(tradeReq.getRange().toString(),
                                                                       tradeReq.getStart(), tradeReq.getEnd(),tradeReq.getShopId());

    }

}
