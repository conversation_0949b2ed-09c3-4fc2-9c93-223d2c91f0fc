<?xml version="1.0" encoding="UTF-8"?>
<configuration status="debug">
    <appenders>
        <!--异步磁盘appender，默认按天&按512M文件大小切分日志，默认最多保留30个日志文件，默认为noblocking写日志模式-->
        <XMDFile name="requestLog" fileName="request.log" rolloverMax="30"/>

        <!--ERROR日志、WARN日志单独输出到一个文件-->
        <XMDFile name="errorLog" fileName="error.log">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
        </XMDFile>
        <XMDFile name="warnLog" fileName="warn.log">
            <Filters>
                <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
                <ThresholdFilter level="warn" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
        </XMDFile>

        <CatAppender name="catAppender"/>

        <!--异步日志上报远程配置-->
        <!-- 如果期望将上报日志从 异步 改为 同步，容忍同步时延，避免丢失，请修改blocking=true参数 -->
        <AsyncScribe name="ScribeAppender" blocking="false">
            <!-- 在指定日志名方面，scribeCategory 和 appkey 两者至少存在一种，且 scribeCategory 高于 appkey。-->
            <!-- <Property name="scribeCategory">data_update_test_lc</Property> -->
            <Property name="scribeCategory">com.sankuai.sgb2b.openapi</Property>
            <Property name="checkLoss">true</Property> <!--该标记表示是否开启丢失率检测，true为开启，false为不开启，默认为false-->
            <LcLayout/>
        </AsyncScribe>

        <!--服务启动耗时统计-->
        <XFrameAppender name="xframeAppender">
            <ThresholdFilter level="trace" onMatch="ACCEPT" onMismatch="DENY"/>
        </XFrameAppender>

    </appenders>

    <loggers>
        <logger name="com.sankuai.shangou.seashop" level="info"/>

        <logger name="org.springframework" level="info"/>
        <logger name="com.meituan.mafka" level="warn"/>
        <logger name="com.meituan.kafka" level="warn"/>
        <logger name="com.dianping.lion" level="warn"/>
        <logger name="com.cip.crane" level="warn"/>
        <logger name="com.dianping.zebra" level="warn"/>
        <logger name="com.dianping.squrriel" level="warn"/>

        <logger name="org.springframework.beans.factory.support.DefaultListableBeanFactory" level="trace" additivity="false" >
            <appender-ref ref="xframeAppender"/>
        </logger>

        <root level="info">
            <appender-ref ref="requestLog"/>
            <appender-ref ref="warnLog"/>
            <appender-ref ref="errorLog"/>
            <appender-ref ref="ScribeAppender"/>
            <appender-ref ref="catAppender"/>
        </root>
    </loggers>
</configuration>