#日志配置
logging:
  level.com.sankuai.shangou.seashop.order.core.mapper: debug # 打印mybatis的sql日志
  level.com.sankuai.shangou.seashop.order.finance.mapper: debug # 打印mybatis的sql日志

zebra:
  #  jdbcRef: waimaistoremanagement_shangou_sgb2b_seashop_order_test # test环境的jdbcRef
  jdbcRef: sgb2b_shangou_sgb2b_seashop_order_product

#分布式锁cerberus
cerberus:
  lock:
    env: online
    category: sg-seashop-cache

#缓存
squirrel:
  category: sg-seashop-cache
  # Squirrel属性配置
  clusterName: redis-sg-common_product

# ES
eagle:
  clusterName: shangou_eaglenode-es-shandiancang_default
  #访问集群的密钥，配置在kms中
  access-key-kms-key: eagleAccessKey

venus:
  env: prod
