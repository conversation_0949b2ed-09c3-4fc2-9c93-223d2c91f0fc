#日志配置
logging:
  level.com.sankuai.shangou.seashop.order.core.mapper: debug # 打印mybatis的sql日志
  level.com.sankuai.shangou.seashop.order.finance.mapper: debug # 打印mybatis的sql日志

zebra:
#  jdbcRef: waimaistoremanagement_shangou_sgb2b_seashop_order_test # test环境的jdbcRef
  jdbcRef: shangousgb2bseashop_shangou_sgb2b_seashop_order_test

#分布式锁cerberus
cerberus:
  lock:
    env: offline
    category: sg-seashop-cache

#缓存
squirrel:
  category: sg-seashop-cache
  # Squirrel属性配置
  clusterName: redis-sg-common_qa

# ES
eagle:
  clusterName: shangou_shandiancang_default
  #访问集群的密钥，配置在kms中
  access-key-kms-key: eagleAccessKey

venus:
  env: test


es:
  index:
    order: order_index_qianyi
    refund: order_refund_index_qianyi
    orderItem: order_item_index_qianyi
    productComment: product_comment_qianyi_index
