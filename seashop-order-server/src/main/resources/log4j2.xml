<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <properties>
        <property name="project_name" value="himall-order"/>
        <property name="LOG_HOME" value="logs"/>
        <!-- 文件输出格式 -->
        <property name="PATTERN">%d{yyyy-MM-dd HH:mm:ss.SSS} |-%-5level [%thread][%X{traceId}] -  %c [%L] -| %msg%n</property>

        <!-- 日志根目录 -->
        <property name="BASEDIR">${LOG_HOME}</property>
        <Property name="rollingLogSize">1000 MB</Property>
        <Property name="rollingLogMaxNum">10</Property>
    </properties>

    <appenders>
        <Console name="CONSOLE" target="system_out">
            <PatternLayout pattern="${PATTERN}" />
        </Console>
        <!-- 所有日志 -->
        <RollingRandomAccessFile fileName="${BASEDIR}/${project_name}.log" filePattern="${BASEDIR}/%d{yyyyMMdd}/${project_name}.%d{yyyyMMdd}-%i.log"
                                 immediateFlush="true" name="ALL_FILE">
            <PatternLayout pattern="${PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="${rollingLogSize}"/>
            </Policies>
            <DefaultRolloverStrategy max="${rollingLogMaxNum}">
                <Delete basePath="${BASEDIR}" maxDepth="2">
                    <IfFileName glob=" ${project_name}.*.log"/>
                    <IfLastModified age="5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
        <!-- ERROR级别日志 -->
        <RollingRandomAccessFile fileName="${BASEDIR}/${project_name}error.log" filePattern="${BASEDIR}/%d{yyyyMMdd}/${project_name}-error.%d{yyyyMMdd}-%i.log"
                                 immediateFlush="true" name="ERROR_FILE">
            <Filters>
                <ThresholdFilter level="ERROR" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="${rollingLogSize}"/>
            </Policies>
            <DefaultRolloverStrategy max="${rollingLogMaxNum}">
                <Delete basePath="${BASEDIR}" maxDepth="2">
                    <IfFileName glob=" ${project.name}-error.*.log"/>
                    <IfLastModified age="5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>

        <RollingRandomAccessFile fileName="${BASEDIR}/${project_name}-warning.log" filePattern="${BASEDIR}/%d{yyyyMMdd}/${project_name}-warning.%d{yyyyMMdd}-%i.log"
                                 immediateFlush="true" name="WARN_FILE">
            <Filters>
                <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            </Filters>
            <PatternLayout pattern="${PATTERN}"/>
            <Policies>
                <TimeBasedTriggeringPolicy interval="1" modulate="true"/>
                <SizeBasedTriggeringPolicy size="${rollingLogSize}"/>
            </Policies>
            <DefaultRolloverStrategy max="${rollingLogMaxNum}">
                <Delete basePath="${BASEDIR}" maxDepth="2">
                    <IfFileName glob=" ${project_name}-error.*.log"/>
                    <IfLastModified age="5D"/>
                </Delete>
            </DefaultRolloverStrategy>
        </RollingRandomAccessFile>
    </appenders>

    <loggers>
        <logger name="org.springframework" level="WARN" />
        <logger name="org.apache" level="WARN" />
        <logger name="io.netty" level="WARN" />

        <root level="INFO">
            <!-- 若不注释该打印,将可能会出现两次打印的情况,调试时使用 -->
            <appenderref ref="CONSOLE" />
            <appenderref ref="ALL_FILE" />
            <appenderref ref="WARN_FILE" />
            <appender-ref ref="ERROR_FILE"/>
        </root>
    </loggers>

</configuration>