server:
  port: 8083
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
        slave:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
  elasticsearch:
    uris: http://124.71.221.117:9200


#分布式锁cerberus
cerberus:
  lock:
    env: offline
    category: sg-seashop-cache

#缓存
squirrel:
  category: sg-seashop-cache
  # Squirrel属性配置
  clusterName: redis-sg-common_qa

# ES
eagle:
  clusterName: shangou_shandiancang_default
  #访问集群的密钥，配置在kms中
  access-key-kms-key: eagleAccessKey

cash:
  deposit:
    callback:
      url:



logging:
  level:
    org.apache.rocketmq: debug
    com.baomidou.mybatisplus: info
    com.sankuai.shangou.seashop.order: debug

mybatis-plus:
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sankuai.shangou.seashop.order.dao;com.sankuai.shangou.seashop.pay.dao;
  global-config:
    banner: true
  check-config-location: true
#消息通知配置
messageConfigMap: "{\"MERCHANT_ORDER_DELIVER\":{\"messageType\":3,\"smsTemplateCode\":2007583,\"emailContentPattern\":\"尊敬的商家:%s,您的订单%s已发货，请注意查收。%s\"},\"SUPPLIER_ORDER_DELIVER\":{\"messageType\":10,\"smsTemplateCode\":2009070,\"emailContentPattern\":\"您的店铺有订单已支付，订单号：%s，请及时发货!%s\"},\"SUPPLIER_AFTER_SALE\":{\"messageType\":11,\"smsTemplateCode\":2007565,\"emailContentPattern\":\"您的店铺有订单申请售后，订单号：%s，请及时处理!%s\"},\"MERCHANT_WAIT_PAY\":{\"messageType\":1,\"smsTemplateCode\":2007557,\"emailContentPattern\":\"尊敬的商家:%s,您还有订单未成功付款，尽快支付别让好货错过！%s\"},\"MERCHANT_RETURN_AGREE\":{\"messageType\":8,\"smsTemplateCode\":2007564,\"emailContentPattern\":\"尊敬的商家:%s,您的退货已审核通过，请及时发货。%s\"},\"SUPPLIER_DELIVER_TIMEOUT\":{\"messageType\":-1,\"smsTemplateCode\":2007559,\"emailContentPattern\":\"%s店铺，您好，贵店铺有%s笔订单付款已超过%s小时没发货，请务必保证及时发出。!%s\"},\"MERCHANT_REFUND_REFUSE\":{\"messageType\":6,\"smsTemplateCode\":2007562,\"emailContentPattern\":\"尊敬的商家:%s,您的退款申请被拒绝，请知悉。%s\"},\"MERCHANT_PAY_SUCCESS\":{\"messageType\":2,\"smsTemplateCode\":2007558,\"emailContentPattern\":\"尊敬的商家:%s,您的订单%s已支付成功，我们会尽快为您发货。%s\"},\"MERCHANT_APPLY_RETURN\":{\"messageType\":7,\"smsTemplateCode\":2007563,\"emailContentPattern\":\"尊敬的商家:%s,您的退货申请正在受理中,请至个人中心查看。%s\"},\"MERCHANT_APPLY_REFUND\":{\"messageType\":4,\"smsTemplateCode\":2007560,\"emailContentPattern\":\"尊敬的商家:%s,您的退款申请正在受理中,请至个人中心查看。%s\"},\"MERCHANT_RETURN_REFUSE\":{\"messageType\":9,\"smsTemplateCode\":2009071,\"emailContentPattern\":\"尊敬的商家:%s,您的退货申请被拒绝，如有疑问请联系卖家。%s\"},\"MERCHANT_REFUND_SUCCESS\":{\"messageType\":5,\"smsTemplateCode\":2007561,\"emailContentPattern\":\"尊敬的商家:%s,您的订单已经完成退款，请留意查收。%s\"}}"

#一些系统开关，可以为空，开关对象设置allowFlag为false时不允许进行业务
seashop:
  order:
    switch:
      systemSwitch: "{\"allowCreateOrder\":{\"allowFlag\":\"true\",\"forbiddenMessage\":\"系统当前不允许提交订单，如有疑问请联系系统管理员\"},\"allowRefundPlatformConfirm\":{\"allowFlag\":\"true\",\"forbiddenMessage\":\"系统当前不允许平台确认退款，如有疑问请联系系统管理员\"},\"allowInitPay\":{\"allowFlag\":\"true\",\"forbiddenMessage\":\"系统当前不允许发起支付，如有疑问请联系系统管理员\"}}"

finance:
  #平台保证金扣款开关
  deduction: T
  #供应商保证金申请退款开关
  apply:
    refund: T
  refund:
    #平台保证金退款审批拒绝
    refuse:
      switch: T
    #平台保证金退款审批通过
    confirm:
      switch: T

deposit:
  callback: https://himall-obs.35hiw.com/test/app/seller-pc/admin/index.html#/shop/deposit
# 惠支付 链接回调  TODO 需要修改
adapay:
  callback:
    url: https://himall.cce.35hiw.com/himall-pay/payCallBack/adaPayCallback
alipay:
  callback:
    url: https://himall.cce.35hiw.com/himall-pay/payCallBack/aliPayCallback
hishop:
  storage:
    storage-type: OBS
    bucket-name: himall-test
    endpoint: https://obs.cn-south-1.myhuaweicloud.com
    access-key: UEDFC3T2O7J1RXQQIAVO
    secret-key: FzRPLhtiitbYO3MTrXxArFLUT3KBIkIdmw9MIKEL
    domain: https://himall-obs.35hiw.com
    base-path: /himall-base/rs/${spring.application.name}