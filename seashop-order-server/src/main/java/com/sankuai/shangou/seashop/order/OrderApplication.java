package com.sankuai.shangou.seashop.order;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

//@SpringBootApplication(exclude = {ElasticsearchAutoConfiguration.class, RestClientAutoConfiguration.class, DataSourceAutoConfiguration.class}, scanBasePackages = "com.sankuai.shangou.seashop.order")
@SpringBootApplication(scanBasePackages = "com.sankuai.shangou.seashop")
@MapperScan({
        "com.sankuai.shangou.seashop.order.dao.core.mapper",
        "com.sankuai.shangou.seashop.order.dao.core.mapper.ext",
        "com.sankuai.shangou.seashop.order.dao.finance.mapper",
        "com.sankuai.shangou.seashop.order.dao.finance.mapper.ext",
        "com.sankuai.shangou.seashop.pay.dao.core.mapper",

})
@EnableTransactionManagement
@EnableConfigurationProperties
@EnableFeignClients(basePackages = {
        "com.sankuai.shangou.seashop",
        "com.hishop.himall.report.api"
})
@EnableDiscoveryClient
@EnableScheduling
public class OrderApplication {
    private static final Logger log = LoggerFactory.getLogger(OrderApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(OrderApplication.class, args);
        log.info("服务启动成功！");
    }
}