package com.sankuai.shangou.seashop.order;

import static org.junit.Assert.assertSame;

import org.apache.thrift.TException;
import org.springframework.boot.test.context.SpringBootTest;

import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@SpringBootTest(classes = OrderApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
public class StartAppTest {


    protected <Req extends BaseParamReq> Req parseJson(Class<Req> reqClass, String req, Object... params) {
        Req exeReq = null;
        if (req != null) {
            exeReq = JsonUtil.parseObject(StrUtil.format(req, params), reqClass);
            exeReq.checkParameter();
        }
        return exeReq;
    }


    protected <Req extends BaseParamReq, Resp> Resp executeBiz(ErpBizExecuteFun<Req, Resp> thriftService, Class<Req> reqClass, String req, Object... params) throws TException {
        log.info("[INPUT]-{}:{} {}", thriftService, req, params);
        Req exeReq = parseJson(reqClass, req, params);
        ResultDto<Resp> result = thriftService.execute(exeReq);
        log.info("[OUTPUT]-{}:{}", thriftService, JsonUtil.toJsonString(result));
        assertSame("[BIZ]返回结果异常", 0, result.getCode());
        return result.getData();
    }


}
