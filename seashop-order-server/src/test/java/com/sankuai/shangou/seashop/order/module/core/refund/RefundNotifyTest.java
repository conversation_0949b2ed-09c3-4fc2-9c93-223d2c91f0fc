package com.sankuai.shangou.seashop.order.module.core.refund;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.StartAppTest;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.RefundResultNotifyBo;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.RefundResultNotifyAssist;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest
@Slf4j
public class RefundNotifyTest extends StartAppTest {

    @Resource
    private RefundResultNotifyAssist refundResultNotifyAssist;

    @Test
    public void testReceiveRefundNotify() {
        String content = "{\"refundId\":\"EOPR20240313114609\",\"channelRefundId\":\"002212024031311461010613833431285149696\",\"orderNo\":\"1766028794100981824\",\"refundedAmt\":\"0.05\",\"payStatus\":1,\"errorMessage\":\"\",\"type\":0,\"businessType\":1,\"businessStatusType\":2}";
        RefundResultNotifyBo payResultBo = JsonUtil.parseObject(content, RefundResultNotifyBo.class);
        if (payResultBo == null) {
            log.error("【退款回调】MQ消息转换对象失败,消息内容为:{}", payResultBo);
            return;
        }
        Integer businessType = payResultBo.getBusinessType();
        // 只消费订单的消息
        if (BusinessTypeEnum.ORDER.getType().equals(businessType)) {
            log.info("【退款回调】MQ消息转换对象后的内容为:{}", JsonUtil.toJsonString(payResultBo));
            refundResultNotifyAssist.handleRefundResult(payResultBo);
        } else {
            log.info("【退款回调】不是订单的消息，不消费.【body】:{}", payResultBo);
        }
    }

}
