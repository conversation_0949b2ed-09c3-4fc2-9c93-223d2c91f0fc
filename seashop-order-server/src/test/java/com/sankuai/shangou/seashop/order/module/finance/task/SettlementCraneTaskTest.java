package com.sankuai.shangou.seashop.order.module.finance.task;

import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class SettlementCraneTaskTest {

    @Resource
    private SettlementCraneTask settlementCraneTask;

    @Test
    public void testOrderSplitting() {
        settlementCraneTask.orderSplitting();
    }

    @Test
    public void testOrderSettlement() {
        settlementCraneTask.orderSettlement();
    }

}
