package com.sankuai.shangou.seashop.order.module.core.message;

import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.common.enums.AppletMessageEventEnum;
import com.sankuai.shangou.seashop.order.common.remote.MessageRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.message.RefundApprovedAppletMessage;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class MessageTest {

    @Resource
    private MessageRemoteService messageRemoteService;

    @Test
    public void testSendAppletMessage() {
        RefundApprovedAppletMessage appletMessage = new RefundApprovedAppletMessage();
        appletMessage.setThing6("refund.getReason()");
        appletMessage.setDate4(DateUtil.format(new Date(), "yyyy年MM月dd日 HH:mm"));
        appletMessage.setAmount5("0.01元");
        messageRemoteService.sendAppletMessage(1L,
                AppletMessageEventEnum.MEMBER_REFUND_APPROVED, appletMessage);
    }

}
