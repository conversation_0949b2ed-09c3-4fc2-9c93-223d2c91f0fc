package com.sankuai.shangou.seashop.order;

import cn.hutool.core.date.DateUtil;
import com.sankuai.shangou.seashop.order.core.service.assit.BizNoGenerator;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentChannelEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 模拟支付相关测试
 *
 * <AUTHOR> Lee
 * @see MockPayTest
 * @since 2025/6/16 14:44
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(classes = OrderApplication.class)
public class MockPayTest {

    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private BizNoGenerator bizNoGenerator;

    @Test
    public void orderRecordTest() {
        OrderPayResp request = new OrderPayResp();
        request.setOrderId("12345");
        request.setPayAmount(new BigDecimal("10.01"));

        // 保存支付记录
        OrderPayRecord record = new OrderPayRecord();
        // 生成批次号
        String batchNo = bizNoGenerator.generatePayNo();
        record.setOrderId(request.getOrderId());
        record.setBatchNo(batchNo);
        record.setOrderAmount(request.getPayAmount());
        record.setPayChannel(PaymentChannelEnum.MOCKPAY.getCode());
        record.setPayMethod(9);
        record.setPayStatus(PayStatusEnum.PAYING.getCode());
        Date now = DateUtil.date();
        record.setCreateTime(now);
        record.setUpdateTime(now);
        orderPayRecordRepository.save(record);
    }

}
