package com.sankuai.shangou.seashop.order.module.core.external;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.junit.Test;

import com.sankuai.shangou.seashop.order.StartAppTest;
import com.sankuai.shangou.seashop.order.thrift.core.ThirdEventCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.UpdatePushEventReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.UpdatePushEventResp;

/**
 * <AUTHOR>
 */
public class ThirdEventCmdThriftServiceTest extends StartAppTest {

    @Resource
    private ThirdEventCmdFeign thirdEventCmdThriftService;

    @Test
    public void updateThirdPushEvent() throws TException {
        String req = "{\"sendCode\":\"1750696875855454285\",\"eventType\":\"PRODUCT_PASS_EVENT\",\"sendTarget\":\"MT\",\"sendState\":\"SENDING\"}";
        UpdatePushEventResp resp = executeBiz(thirdEventCmdThriftService::updateThirdPushEvent, UpdatePushEventReq.class, req);
    }


}