package com.sankuai.shangou.seashop.order.module.core.service;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.core.service.assit.BizNoGenerator;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
public class OrderBizAssistTest {

    @Resource
    private BizNoGenerator bizNoGenerator;

    @Test
    public void testGenOrderNo() {
        List<String> ordreNoList = bizNoGenerator.generateOrderNo(10);
        System.out.println(JsonUtil.toJsonString(ordreNoList));
    }

}
