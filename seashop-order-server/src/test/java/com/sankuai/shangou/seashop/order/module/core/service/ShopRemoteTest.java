package com.sankuai.shangou.seashop.order.module.core.service;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * @description:
 * @author: LXH
 **/
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class ShopRemoteTest {
    @Resource
    ShopRemoteService shopRemoteService;

    // 测试getshopUv方法
    @Test
    public void getYesterdayShopUV(){
        log.error(JSONUtil.toJsonStr(shopRemoteService.getYesterdayShopUV(101L)));
    }
}
