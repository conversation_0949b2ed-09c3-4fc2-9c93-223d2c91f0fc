package com.sankuai.shangou.seashop.order.module.core;

import com.alibaba.fastjson.JSON;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.finance.service.ShopAccountService;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreateAccountReq;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class ShopAccountTest {

    @Resource
    private ShopAccountService shopAccountService;


    @Test
    public void test() {
        String str = "{\"shopId\":2426,\"shopName\":\"\",\"accountId\":\"****************\",\"operationShopId\":0}";
        CreateAccountReq req = JSON.parseObject(str, CreateAccountReq.class);
        shopAccountService.createAccount(req);
    }
}
