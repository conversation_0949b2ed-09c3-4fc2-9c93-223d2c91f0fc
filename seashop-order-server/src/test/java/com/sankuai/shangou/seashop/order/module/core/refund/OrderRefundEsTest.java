package com.sankuai.shangou.seashop.order.module.core.refund;

import cn.hutool.core.date.StopWatch;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.common.config.EsIndexProps;
import com.sankuai.shangou.seashop.order.core.service.OrderRefundSearchService;
import com.sankuai.shangou.seashop.order.core.service.model.refund.UserQueryRefundBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.MatchAllQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class OrderRefundEsTest {

    @Resource
    private OrderRefundSearchService orderRefundSearchService;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private RestHighLevelClient restHighLevelClient;
    @Resource
    private EsIndexProps esIndexProps;

    /**
     * 测试清空索引数据，用的底层方法，正常业务中目前不涉及，是迁移数据时使用的
     */
    @Test
    public void clearIndex() {
        // 创建匹配所有文档的查询
        MatchAllQueryBuilder matchAllQuery = QueryBuilders.matchAllQuery();
        // 创建DeleteByQueryRequest
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(esIndexProps.getIdxRefund()).setQuery(matchAllQuery);
        // 异步执行请求并等待响应
        try {
            BulkByScrollResponse response = restHighLevelClient.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
            // 获取删除的文档数量
            long deleted = response.getDeleted();
            log.info("refund Deleted documents: " + deleted);
        } catch (Exception e) {
            log.error("refund delete error", e);
        }
    }

    @Test
    public void initEsRefund() {
        clearIndex();

        LambdaQueryWrapper<OrderRefund> wrapper = new LambdaQueryWrapper<OrderRefund>().orderByDesc(OrderRefund::getId).last("limit 10");
        List<OrderRefund> list = orderRefundRepository.list(wrapper);
        log.info("构建售后ES, size={}", list.size());
        ThreadPoolExecutor pool = new ThreadPoolExecutor(10, 20, 10, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(50000));
        CountDownLatch countDownLatch = new CountDownLatch(list.size());
        int i = 1;
        StopWatch stopWatch = StopWatch.create("build es refund");
        stopWatch.start();
        for (OrderRefund orderRefund : list) {
            log.info("构建售后ES， refundId={}， 第 {} 条", orderRefund.getId(), i++);
            pool.submit(() -> {
                orderRefundSearchService.buildEsRefund(orderRefund.getId());
                countDownLatch.countDown();
            });
        }
        try {
            countDownLatch.await();
            stopWatch.stop();
            log.info("构建售后ES, cost={}", stopWatch.prettyPrint(TimeUnit.SECONDS));
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void testBuildRefundEs() {
        Long[] refundIdArr = {44L};
        for (Long refundId : refundIdArr) {
            orderRefundSearchService.buildEsRefund(refundId);
        }
    }

    @Test
    public void testUserQueryPage() {
        String param = "{\"user\":{\"userId\":136478,\"userName\":\"NFSQ321\"},\"searchKey\":\"测试评论商品\",\"tab\":\"ALL_APPLY\",\"pageSize\":10,\"pageNo\":1}";
        UserQueryRefundBo query = JsonUtil.parseObject(param, UserQueryRefundBo.class);
        orderRefundSearchService.userQueryPage(query);
    }

}
