package com.sankuai.shangou.seashop.order.task;

import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.core.task.OrderCraneTask;
import com.sankuai.shangou.seashop.order.finance.service.ShopAccountService;
import com.sankuai.shangou.seashop.order.finance.task.SettlementCraneTask;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class OrderTaskTest {

    @Resource
    private OrderCraneTask orderCraneTask;

    @Resource
    private ShopAccountService shopAccountService;

    @Resource
    private SettlementCraneTask settlementCraneTask;

    @Test
    public void testOrder() {
        orderCraneTask.reportOrder("");
    }

    @Test
    public void testRefundingCheck() {
        orderCraneTask.orderRefundStatusTask();
    }

    @Test
    public void getAccount() {
        log.info(shopAccountService.getShopAccountByShopId(2419L));
    }

    @Test
    public void orderSettlement() {
        settlementCraneTask.orderSettlement();
    }


    @Test
    public void orderSplitting() {
        settlementCraneTask.orderSplitting();
    }

}
