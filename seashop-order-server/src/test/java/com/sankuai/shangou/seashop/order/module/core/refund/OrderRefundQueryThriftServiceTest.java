package com.sankuai.shangou.seashop.order.module.core.refund;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.StartAppTest;
import com.sankuai.shangou.seashop.order.thrift.core.OrderRefundQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.refund.ErpQueryRefundPageReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.refund.PlatformRefundDetailResp;
import org.apache.thrift.TException;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
public class OrderRefundQueryThriftServiceTest extends StartAppTest {

    @Resource
    private OrderRefundQueryFeign orderRefundQueryThriftService;

    @Test
    public void testQueryErpRefundPage() throws TException {
        String req = "{\"pageSize\":10,\"pageNo\":1,\"timeType\":\"UPDATE_TIME\",\"startTime\":\"2023-01-01 00:00:00\",\"endTime\":\"2024-01-24 00:00:00\",\"auditStatus\":7," + "\"refundModes\":[2,3]}";
        BasePageResp<PlatformRefundDetailResp> result = executeBiz(orderRefundQueryThriftService::queryErpRefundPage, ErpQueryRefundPageReq.class, req);
    }
}