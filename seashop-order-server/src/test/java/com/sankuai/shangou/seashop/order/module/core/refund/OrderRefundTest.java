package com.sankuai.shangou.seashop.order.module.core.refund;

import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.PlatformConfirmMessageHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
public class OrderRefundTest {

    @Resource
    private PlatformConfirmMessageHandler platformConfirmMessageHandler;

    @Test
    public void testPlatformConfirm() {
        OrderRefundMessage message = OrderRefundMessage.builder()
                .orderId("2024012410003034")
                .refundId(45L)
                .refundEventName("PLATFORM_CONFIRM")
                .build();
        platformConfirmMessageHandler.handle(message);
    }

}
