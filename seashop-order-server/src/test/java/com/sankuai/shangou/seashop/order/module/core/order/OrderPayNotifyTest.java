package com.sankuai.shangou.seashop.order.module.core.order;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.StartAppTest;
import com.sankuai.shangou.seashop.order.core.mq.model.pay.PayResultBo;
import com.sankuai.shangou.seashop.order.core.service.assit.pay.OrderPayResultHandlerAssist;
import com.sankuai.shangou.seashop.pay.core.service.adapay.impl.PaymentCallBackServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
public class OrderPayNotifyTest extends StartAppTest {

    @Resource
    private OrderPayResultHandlerAssist orderPayResultHandlerAssist;

    @Resource
    private PaymentCallBackServiceImpl payment;

    @Test
    public void testReceiveOrderPayNotify() {
        String content = "{\"orderId\":\"1765567139520978964\",\"payAmount\":0.01,\"payStatus\":1,\"payTime\":*************,\"errorMsg\":\"\",\"outTransId\":\"2024030722001481261445874465\",\"payId\":\"002212024030710362310611641543613534208\",\"businessType\":1,\"bankCode\":\"\"}";
        PayResultBo payResultBo = JsonUtil.parseObject(content, PayResultBo.class);
        orderPayResultHandlerAssist.handlePayResult(payResultBo);
    }

    @Test
    public void payment() {
        String str = "{\"app_id\":\"app_39b69a1c-d9ad-4e30-bb29-d80833b86885\",\"business_mode\":\"00\",\"created_time\":\"**************\",\"description\":\"\",\"end_time\":\"**************\",\"expend\":{\"buyer_logon_id\":\"187****3233\",\"cashPayAmt\":\"0.00\",\"couponInfos\":\"[{\\\"activeId\\\":\\\"\\\",\\\"addnInfo\\\":\\\"\\\",\\\"couponAmt\\\":\\\"0.01\\\",\\\"couponId\\\":\\\"\\\",\\\"couponName\\\":\\\"到店通用红包\\\",\\\"couponRange\\\":\\\"\\\",\\\"couponType\\\":\\\"ALIPAY_CASH_VOUCHER\\\",\\\"goodsInfo\\\":[],\\\"merchantContribute\\\":\\\"0.00\\\",\\\"otherContribute\\\":\\\"0.01\\\"}]\",\"discountAmt\":\"0.01\",\"sub_open_id\":\"****************\"},\"fee_amt\":\"0.00\",\"id\":\"00221**************10697510356769419264\",\"order_no\":\"PO641931950659223562\",\"out_trans_id\":\"2024103022001462771407032055\",\"party_order_id\":\"02242410303410426903332\",\"pay_amt\":\"0.01\",\"pay_channel\":\"alipay_qr\",\"real_amt\":\"0.01\",\"share_eq\":\"Y\",\"status\":\"succeeded\",\"trans_response_add_info\":\"{\\\"fund_bill_list\\\":[{\\\"amount\\\":\\\"0.01\\\",\\\"fund_channel\\\":\\\"DISCOUNT\\\"}]}\"}";
        payment.callback(str);
    }

}
