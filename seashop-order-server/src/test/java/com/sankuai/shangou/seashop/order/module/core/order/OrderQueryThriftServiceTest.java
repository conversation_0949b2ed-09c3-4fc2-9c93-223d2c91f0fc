package com.sankuai.shangou.seashop.order.module.core.order;


import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.StartAppTest;
import com.sankuai.shangou.seashop.order.thrift.core.OrderQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryErpOrderDetailReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.QueryErpPageOrderReq;
import com.sankuai.shangou.seashop.order.thrift.core.response.ErpOrderDetailResp;
import org.apache.thrift.TException;
import org.junit.Test;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
public class OrderQueryThriftServiceTest extends StartAppTest {

    @Resource
    private OrderQueryFeign orderQueryThriftService;


    @Test
    public void pageQueryErpOrder() throws TException {
        String req = "{\"orderId\":\"2302221420437725\"}";
        BasePageResp<ErpOrderDetailResp> result = executeBiz(orderQueryThriftService::pageQueryErpOrder, QueryErpPageOrderReq.class, req);
    }

    @Test
    public void queryErpDetail() throws TException {
        String req = "{\"orderId\":\"2302221420437725\"}";
        ErpOrderDetailResp result = executeBiz(orderQueryThriftService::queryErpDetail, QueryErpOrderDetailReq.class, req);
    }
}