package com.sankuai.shangou.seashop.order;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.core.service.OrderService;
import com.sankuai.shangou.seashop.order.core.service.model.order.CreateOrderBo;
import com.sankuai.shangou.seashop.order.thrift.core.response.CreateOrderResp;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = OrderApplication.class)
public class OrderTest {
    @Resource
    private OrderService orderService;

    @Test
    public void createOrder() {
        String str = "{\"userInfo\":{\"userId\":136470,\"userName\":\"18513378018\",\"userPhone\":\"18513378018\",\"operationUserId\":null,\"operationShopId\":0},\"uniqueId\":\"cada022dc-87322e3-cc5563f4e1c2\",\"shippingAddress\":{\"id\":2,\"userId\":136470,\"regionId\":22561,\"shipTo\":\"snow\",\"address\":\"通城花园\",\"addressDetail\":\"\",\"phone\":\"18888888888\",\"regionPath\":\"21265,22391,22557,22561\",\"regionFullName\":\"湖南省 长沙市 芙蓉区 朝阳街街道\",\"provinceId\":21265,\"provinceName\":\"湖南省\",\"cityId\":22391,\"cityName\":\"长沙市\",\"districtId\":null,\"districtName\":\"芙蓉区\",\"receiveLongitude\":null,\"receiveLatitude\":null,\"operationUserId\":null,\"operationShopId\":0},\"totalAmount\":0.01,\"shopProductList\":[{\"shop\":{\"shopId\":162,\"shopName\":\"官方自营店\",\"selectedTotalAmount\":0.01,\"shopAndProductPromotion\":{\"shopPromotionList\":null,\"productPromotionMap\":{\"14293_0_0_0\":{\"productId\":14293,\"skuId\":\"14293_0_0_0\",\"promotionList\":[]}}},\"productTotalAmount\":0.01,\"productQuantity\":null},\"productList\":[{\"productId\":14293,\"productName\":\"小客测试商品001\",\"skuId\":\"14293_0_0_0\",\"quantity\":1,\"mainImagePath\":\"https://himall-obs.35hiw.com/himall-base/rs/himall-gw/6864a275e4b0a866e7cfbb5e.jpg\",\"skuStock\":998,\"originSalePrice\":0.01,\"discountSalePrice\":null,\"realSalePrice\":0.01,\"totalAmount\":0.01,\"color\":\"\",\"size\":\"\",\"version\":\"\",\"skuAutoId\":584,\"categoryId\":15,\"finalSalePrice\":0.01,\"discountActivityId\":null,\"splitReductionAmount\":null,\"splitDiscountAmount\":null,\"splitCouponAmount\":null,\"couponId\":null,\"reductionActivityId\":null,\"sku\":\"43522855\",\"oeCode\":\"OE780135\",\"brandCode\":\"AC179326\",\"adaptableCar\":\"\",\"partSpec\":\"\",\"brandId\":11,\"brandName\":\"兔八哥\"}],\"additional\":{\"deliveryType\":1,\"couponId\":null,\"couponAmount\":0,\"discountAmount\":null,\"reductionAmount\":null,\"freightAmount\":0,\"remark\":null,\"taxAmount\":0,\"taxRate\":null,\"invoice\":null,\"reductionConditionAmount\":null,\"couponRecordId\":null,\"reductionActivityId\":null,\"sourceOrderId\":null}}],\"flashSaleId\":null,\"collocationId\":null,\"platform\":\"PC\",\"whetherBuyNow\":null,\"createTime\":null,\"operationUserId\":null,\"operationShopId\":0}";
        CreateOrderBo order = JsonUtil.parseObject(str, CreateOrderBo.class);
        CreateOrderResp resp = orderService.createOrder(order);
        System.out.println(resp);
    }
}
