package com.sankuai.shangou.seashop.order.module.common.base;

import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.order.OrderApplication;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
public class IdGeneratorTest {

    @Resource
    private LeafService leafService;

    @Test
    public void testSnowflake() {
        System.out.println(leafService.generateNoBySnowFlake("com.sankuai.seachshop.order.test.snowflake"));
    }

    @Test
    public void testDefault() {
        System.out.println(leafService.generateNo("com.sankuai.sgb2b.seashop.order.order.no"));
    }

    @Test
    public void testBatchSnowflake() {
        System.out.println(leafService.batchGenerateNoBySnowFlake("com.sankuai.seachshop.order.test.snowflake", 5));
    }

    @Test
    public void testBatchDefault() {
        System.out.println(leafService.batchGenerateNo("com.sankuai.sgb2b.seashop.order.order.no", 5));
    }

}
