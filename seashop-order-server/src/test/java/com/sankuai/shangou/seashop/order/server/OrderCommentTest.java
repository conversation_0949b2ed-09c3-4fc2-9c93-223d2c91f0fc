package com.sankuai.shangou.seashop.order.server;

import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.core.service.OrderCommentService;
import com.sankuai.shangou.seashop.order.core.task.OrderCraneTask;
import com.sankuai.shangou.seashop.order.finance.service.ShopAccountService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class OrderCommentTest {

    @Resource
    private OrderCommentService orderCommentService;

    @Test
    public void queryShopMarkByShopId() {
        orderCommentService.queryShopMarkByShopId(1L);
    }

}
