package com.sankuai.shangou.seashop.order.module.core.order;

import cn.hutool.crypto.digest.MD5;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.common.enums.MqErrorDataBizTypeEnum;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.OrderRefundMessage;
import com.sankuai.shangou.seashop.order.core.service.assit.MqErrorDataAssist;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.OrderRefundMessageAssist;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class MqErrorDataTest {

    @Resource
    private OrderRefundMessageAssist orderRefundMessageAssist;
    @Resource
    private MqErrorDataAssist mqErrorDataAssist;

    @Test
    public void testConsumeMqError() {
        String body = "2";
        log.info("【mafka消费】【订单售后】消息内容为: {}", body);
        String mockMessageId = UUID.randomUUID().toString();
        String tracerId = mockMessageId;
        // 售后消息处理，不同的事件会有不同的处理器
        OrderRefundMessage refundMessage = null;
        try {
            refundMessage = JsonUtil.parseObject(body, OrderRefundMessage.class);
        } catch (Exception e) {
            log.error("【mafka消费】【订单售后】消息转换失败", e);
            mqErrorDataAssist.saveErrorData(MqErrorDataBizTypeEnum.REFUND_CHANGE, MD5.create().digestHex(body), mockMessageId, tracerId, e.getMessage(), body);
        }
    }

}
