package com.sankuai.shangou.seashop.order.module.core.order;

import cn.hutool.core.date.StopWatch;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.common.config.EsIndexProps;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import com.sankuai.shangou.seashop.order.core.service.OrderExportService;
import com.sankuai.shangou.seashop.order.core.service.OrderStatsService;
import com.sankuai.shangou.seashop.order.core.service.model.order.*;
import com.sankuai.shangou.seashop.order.core.service.model.stats.*;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.po.CommonOrderQueryParamBo;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.thrift.core.request.EsScrollQueryReq;
import com.sankuai.shangou.seashop.order.thrift.core.request.order.QueryProductBuyCountReq;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.MatchAllQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class OrderEsTest {

    @Resource
    private EsOrderService esOrderService;
    @Resource
    private OrderStatsService orderStatsService;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderExportService orderExportService;
    @Resource
    private RestHighLevelClient restHighLevelClient;
    @Resource
    private EsIndexProps esIndexProps;

    /**
     * 测试清空索引数据，用的底层方法，正常业务中目前不涉及，是迁移数据时使用的
     */
    @Test
    public void clearIndex() {
        // 创建匹配所有文档的查询
        MatchAllQueryBuilder matchAllQuery = QueryBuilders.matchAllQuery();
        // 创建DeleteByQueryRequest
        DeleteByQueryRequest deleteByQueryRequest = new DeleteByQueryRequest(esIndexProps.getIdxOrder()).setQuery(matchAllQuery);
        // 异步执行请求并等待响应
        try {
            BulkByScrollResponse response = restHighLevelClient.deleteByQuery(deleteByQueryRequest, RequestOptions.DEFAULT);
            // 获取删除的文档数量
            long deleted = response.getDeleted();
            log.info("order Deleted documents: " + deleted);
        } catch (Exception e) {
            log.error("order delete error", e);
        }

        // 创建匹配所有文档的查询
        MatchAllQueryBuilder itemDeleteQuery = QueryBuilders.matchAllQuery();
        // 创建DeleteByQueryRequest
        DeleteByQueryRequest itemDeleteRequest = new DeleteByQueryRequest(esIndexProps.getIdxOrderItem()).setQuery(itemDeleteQuery);
        // 异步执行请求并等待响应
        try {
            BulkByScrollResponse response = restHighLevelClient.deleteByQuery(itemDeleteRequest, RequestOptions.DEFAULT);
            // 获取删除的文档数量
            long deleted = response.getDeleted();
            log.info("orderItem Deleted documents: " + deleted);
        } catch (Exception e) {
            log.error("item delete error", e);
        }
    }

    @Test
    public void initAll() {
        CommonOrderQueryParamBo param = CommonOrderQueryParamBo.builder()
                //.orderDateGe(DateUtil.parseDate("2024-01-01 00:00:00"))
                .build();
        List<Order> orderList = orderRepository.getByCondition(param);
        log.info("构建订单ES, size={}", orderList.size());
        ThreadPoolExecutor pool = new ThreadPoolExecutor(10, 20, 10, TimeUnit.SECONDS,
                new ArrayBlockingQueue<>(200000));
        CountDownLatch countDownLatch = new CountDownLatch(orderList.size());
        int i = 1;
        StopWatch stopWatch = StopWatch.create("build es order");
        stopWatch.start();
        for (Order order : orderList) {
            log.info("构建订单ES， orderId={}， 第 {} 条", order.getOrderId(), i++);
            pool.submit(() -> {
                esOrderService.buildEsOrder(order.getOrderId());
                countDownLatch.countDown();
            });
        }
        try {
            countDownLatch.await();
            stopWatch.stop();
            log.info("构建订单ES, cost={}", stopWatch.prettyPrint(TimeUnit.SECONDS));
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void initByOrderId() {
        String[] orderIdArr = {"2024082614001173", "2024082614001176", "2024082614001177"};
        for (String orderId : orderIdArr) {
            esOrderService.buildEsOrder(orderId);
        }
    }

    /**
     * <pre>
     *     {
     *   "query": {
     *     "bool": {
     *       "filter": [
     *         {
     *           "term": {
     *             "orderId": "2203090943394975"
     *           }
     *         },
     *         {
     *           "nested":{
     *             "path":"orderItems",
     *             "query":[{
     *               "wildcard":{
     *                 "orderItems.productName":{
     *                   "value":"*七分袖*"
     *                 }
     *               }
     *             }]
     *           }
     *         }
     *       ]
     *     }
     *   }
     * }
     * </pre>
     */
    @Test
    public void testBuildOrderEs() {
        // "2021122256343620", "2021122301658626", "2203090943394975", "2302271447127114",
        //                "2301291604431027", "2203081140572243", "2203081139524880", "2301111431384336",
        String[] orderIdArr = {
                "2024010814001061", "2024010814001060"};
        for (String orderId : orderIdArr) {
            esOrderService.buildEsOrder(orderId);
        }
    }

    /**
     * <pre>
     *     {
     *   "query": {
     *     "bool": {
     *       "filter": [
     *         {
     *           "term": {
     *             "userId": {
     *               "value": 574,
     *               "boost": 1.0
     *             }
     *           }
     *         }
     *       ]
     *     }
     *   },
     *   "aggs": {
     *     "orderCount": {
     *       "terms": {
     *         "field": "userId"
     *       }
     *     },
     *     "orderAmount": {
     *       "sum": {
     *         "field": "totalAmount"
     *       }
     *     },
     *     "productAgg": {
     *       "nested": {
     *         "path": "orderItems"
     *       },
     *       "aggs": {
     *         "productCount": {
     *           "sum": {
     *             "field": "orderItems.quantity"
     *           }
     *         }
     *       }
     *     }
     *   }
     * }
     * </pre>
     */
    @Test
    public void testSearch() {
        QueryUserOrderBo searchBo = new QueryUserOrderBo();
        searchBo.setPageNo(1);
        searchBo.setPageSize(10);
        searchBo.setUserId(136478L);
        searchBo.setOrderId("2024031219003381");
        //searchBo.setQueryUnCommented(true);
        BasePageResp<OrderInfoBo> resp = esOrderService.searchForUser(searchBo);
        log.info("【订单搜索】搜索结果, resp={}", JsonUtil.toJsonString(resp));
    }

    @Test
    public void testUserPurchaseSku() {
        StatsUserPurchaseSkuParamBo searchBo = new StatsUserPurchaseSkuParamBo();
        searchBo.setPageNo(1);
        searchBo.setPageSize(10);
        searchBo.setUserId(136478L);
        searchBo.setProductName("测试评论商品");
        //searchBo.setProductId(1769652220715868235L);
        UserPurchaseSkuStatsBo resp = esOrderService.pageUserPurchaseSku(searchBo);
        log.info("【订单搜索】搜索结果, resp={}", JsonUtil.toJsonString(resp));
    }

    @Test
    public void testPlatformStatsTradeData() {
        PlatformStatsTradeDataBo statsBO = orderStatsService.statsPlatformIndexTradeData();
        log.info("【订单统计】平台统计交易数据, statsBO={}", JsonUtil.toJsonString(statsBO));
    }

    @Test
    public void testStatsSellerIndexTradeData() {
        SellerIndexTradeStatsBo statsBO = orderStatsService.statsSellerIndexTradeData(167L);
        log.info("【订单统计】供应商首页统计交易数据, statsBO={}", JsonUtil.toJsonString(statsBO));
    }

    /**
     * <pre>
     *     {
     *   "size": 0,
     *   "aggs": {
     *     "top_products": {
     *       "nested": {
     *         "path": "orderItems"
     *       },
     *       "aggs": {
     *         "product_terms": {
     *           "terms": {
     *             "field": "orderItems.productId",
     *             "size": 8,
     *             "order": {
     *               "total_sales": "desc"
     *             }
     *           },
     *           "aggs": {
     *             "total_sales": {
     *               "sum": {
     *                 "field": "orderItems.realTotalPrice"
     *               }
     *             },
     *             "topNAmount": {
     *               "top_hits": {
     *                 "_source": {
     *                   "includes": [
     *                     "orderItems.productId",
     *                     "orderItems.productName",
     *                     "orderItems.realTotalPrice"
     *                   ]
     *                 }
     *               }
     *             }
     *           }
     *         }
     *       }
     *     }
     *   }
     * }
     * </pre>
     */
    @Test
    public void testShopTopNSaleProduct() {
        StatsShopTopNSaleProductParamBo statsBo = new StatsShopTopNSaleProductParamBo();
        statsBo.setTopN(8);
        TopProductSaleStatsBo result = orderStatsService.statsTopNSaleProduct(statsBo);
        log.info("【订单统计】店铺TOPN销售商品, result={}", JsonUtil.toJsonString(result));
    }

    @Test
    public void testSearchProductCount() {
        QueryProductBuyCountReq req = new QueryProductBuyCountReq();
        req.setUserId(136478L);
        req.setProductIdList(Arrays.asList(1767008536451096665L));
        orderStatsService.searchUserProductBuyCount(req);
    }

    @Test
    public void countFlashSaleByProduct() {
        Long count = esOrderService.countFlashSaleByProduct(570L, 258L, 1000015L);
        log.info("【订单统计】限时抢购商品统计, count={}", count);
        Assert.assertNotNull(count);
    }

    @Test
    public void testSearchOrderByScroll() throws Exception {
        QueryPlatformOrderBo param = new QueryPlatformOrderBo();
        param.setPageNo(1);
        param.setPageSize(50);
        param.setShopId(185L);
        //param.setOrderId("2024040915003850");
        param.setOrderStatus(-1);
        OrderAndItemScrollBo scroll = orderExportService.getScrollIdForPlatformExport(param);
        String scrollId = scroll.getScrollId();
        log.info("【订单导出】获取scrollId, scrollId={}", scrollId);
        log.info("【订单导出】查询结果, orderExportBos={}", JsonUtil.toJsonString(scroll.getDataList()));
        int i = 0;
        EsScrollQueryReq queryReq = new EsScrollQueryReq();
        queryReq.setScrollId(scrollId);
        queryReq.setTimeValueMinutes(2L);
        while (i++ < 2) {
            List<OrderExportBo> orderExportBos = orderExportService.searchByScrollId(queryReq);
            log.info("【订单导出】查询结果, orderExportBos={}", JsonUtil.toJsonString(orderExportBos));
        }
        orderExportService.clearScrollId(scrollId);
    }

}
