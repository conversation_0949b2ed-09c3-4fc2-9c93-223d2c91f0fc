package com.sankuai.shangou.seashop.order.task;

import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.core.task.OrderCraneTask;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname ReportTaskTest
 * Description //TODO
 * @date 2024/12/5 09:49
 */
@SpringBootTest(classes = OrderApplication.class)
public class ReportTaskTest {

    @Resource
    private OrderCraneTask orderCraneTask;

    @Test
    public void reportOrder(){
        orderCraneTask.reportOrder("1");
    }
}
