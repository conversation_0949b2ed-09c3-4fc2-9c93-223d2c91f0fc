package com.sankuai.shangou.seashop.order.server;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.common.config.MessageProps;
import com.sankuai.shangou.seashop.order.common.enums.PlatformMessageTemplateEnum;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class MessagePropsTest {

    @Resource
    private MessageProps messageProps;

    public static void main(String[] args){
        Map<String, MessageProps.MessageConfig> config = new HashMap<>();
        for (PlatformMessageTemplateEnum value : PlatformMessageTemplateEnum.values()) {
            MessageProps.MessageConfig mc = new MessageProps.MessageConfig();
            mc.setMessageType(value.getCode());
            mc.setSmsTemplateCode(value.getSmsTplCode());
            mc.setEmailContentPattern(value.getMsgPattern());

            config.put(value.name(), mc);
        }
        log.info("config: {}", JsonUtil.toJsonString(config));
    }

    @Test
    public void testLoadMessageProps() {
        log.info("messageProps: {}", JsonUtil.toJsonString(messageProps.getMessageConfigMap()));
    }

}
