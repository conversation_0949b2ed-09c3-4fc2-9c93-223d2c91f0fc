package com.sankuai.shangou.seashop.order.module.core.comment;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.common.es.service.EsProductCommentService;
import com.sankuai.shangou.seashop.order.core.service.EsOrderService;
import com.sankuai.shangou.seashop.order.core.service.OrderCommentService;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.OrderItemInfoBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.ProductCommentBo;
import com.sankuai.shangou.seashop.order.core.service.model.order.QueryPlatformOrderBo;
import com.sankuai.shangou.seashop.order.core.task.CommentTask;
import com.sankuai.shangou.seashop.order.core.task.param.ProductCommentEsParam;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/04/19 17:34
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
@Slf4j
public class ProductCommentEsTest {

    @Resource
    private CommentTask commentTask;
    @Resource
    private EsProductCommentService esProductCommentService;
    @Resource
    private EsOrderService esOrderService;
    @Resource
    private OrderCommentService orderCommentService;

    @Test
    public void testBuild() {
        esProductCommentService.clearIndex();
        ProductCommentEsParam param = new ProductCommentEsParam();
        commentTask.refreshProductCommentEs(null);
    }

    @Test
    public void autoComment() {
        QueryPlatformOrderBo request = new QueryPlatformOrderBo();
        request.setOrderStatus(OrderStatusEnum.FINISHED.getCode());
        request.setHasComment(Boolean.FALSE);
        request.setOrderId("2404031134296082");


        BasePageResp<OrderInfoBo> pageResult = esOrderService.searchForPlatform(request);
        List<OrderInfoBo> orderList = pageResult.getData();

        // 3. 调用评价服务进行评价
        saveOrderComment(orderList.get(0));
    }

    private void saveOrderComment(OrderInfoBo order) {
        try {
            log.info("自动订单评价, orderId:{}", order.getOrderId());
            OrderCommentBo orderCommentBo = orderCommentBuild(order);
            orderCommentService.saveOrderComment(orderCommentBo);
            log.info("自动订单评价成功, orderId:{}", order.getOrderId());
        }
        catch (Exception e) {
            log.error("自动订单评价失败,orderId:{}", order.getOrderId(), e);
        }
    }

    private OrderCommentBo orderCommentBuild(OrderInfoBo order) {
        OrderCommentBo comment = new OrderCommentBo();
        comment.setOrderId(order.getOrderId());
        comment.setUserId(order.getUserId());
        comment.setPackMark(5);
        comment.setDeliveryMark(5);
        comment.setServiceMark(5);

        List<OrderItemInfoBo> orderItemList = order.getItemList();
        List<ProductCommentBo> productCommentList = orderItemList.stream().map(orderItem -> {
            ProductCommentBo productComment = new ProductCommentBo();
            productComment.setSubOrderId(orderItem.getOrderItemId());
            productComment.setReviewMark(5);
            productComment.setReviewContent("123");
            return productComment;
        }).collect(Collectors.toList());
        comment.setProductCommentList(productCommentList);
        return comment;
    }
}
