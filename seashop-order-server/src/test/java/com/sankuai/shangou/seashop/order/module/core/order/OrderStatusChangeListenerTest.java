package com.sankuai.shangou.seashop.order.module.core.order;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.core.mq.model.order.OrderMessage;
import com.sankuai.shangou.seashop.order.core.service.assit.order.OrderChangeMessageAssist;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
public class OrderStatusChangeListenerTest {

    @Resource
    private OrderChangeMessageAssist orderChangeMessageAssist;

    @Test
    public void testReceiveMessage() {
        String body = "{\"orderId\":\"2024041216005001\",\"orderEventName\":\"DELIVERY_ORDER\"}";
        OrderMessage orderMessage = JsonUtil.parseObject(body, OrderMessage.class);
        // 事件处理
        orderChangeMessageAssist.handleRefundMessage(orderMessage);
    }

}
