package com.sankuai.shangou.seashop.order.module.core.refund;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.OrderApplication;
import com.sankuai.shangou.seashop.order.core.mq.model.refund.RefundResultNotifyBo;
import com.sankuai.shangou.seashop.order.core.service.assit.refund.RefundResultNotifyAssist;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = OrderApplication.class)
@RunWith(SpringRunner.class)
public class RefundResultNotifyAssistTest {

    @Resource
    private RefundResultNotifyAssist refundResultNotifyAssist;
    
    @Test
    public void testNotify() {
        String body = "{\"refundId\":\"1784473196368502788\",\"orderNo\":\"1784426199263485963\",\"channelRefundId\":\"002212024042814421810630547600183316480\",\"refundedAmt\":\"0.07\",\"payStatus\":1,\"errorMessage\":\"\",\"type\":0,\"businessType\":1,\"businessStatusType\":1}";
        RefundResultNotifyBo payResultBo = JsonUtil.parseObject(body, RefundResultNotifyBo.class);
        refundResultNotifyAssist.handleRefundResult(payResultBo);
    }

}
