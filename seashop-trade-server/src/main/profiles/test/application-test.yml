#日志配置
logging:
  level.com.sankuai.shangou.seashop.trade.dao.core.mapper: debug # 打印mybatis的sql日志

zebra:
  jdbcRef: waimaistoremanagement_shangou_sgb2b_seashop_order_test # TODO 替换为自己test环境的jdbcRef

# ES
eagle:
  clusterName: shangou_shandiancang_default
  #访问集群的密钥，配置在kms中
  access-key-kms-key: eagleAccessKey

squirrel:
  category: sg-seashop-cache
  # Squirrel属性配置
  clusterName: redis-sg-common_qa

#分布式锁cerberus
cerberus:
  lock:
    env: offline
    category: sg-seashop-cache



