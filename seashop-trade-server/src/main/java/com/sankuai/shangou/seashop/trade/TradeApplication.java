package com.sankuai.shangou.seashop.trade;

import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.transaction.annotation.EnableTransactionManagement;

//@SpringBootApplication(exclude = {ElasticsearchAutoConfiguration.class, RestClientAutoConfiguration.class, DataSourceAutoConfiguration.class}, scanBasePackages = "com.sankuai.shangou.seashop.trade")
@SpringBootApplication(scanBasePackages = "com.sankuai.shangou.seashop")
@MapperScan(basePackages = {
        "com.sankuai.shangou.seashop.trade.dao.core.mapper",
        "com.sankuai.shangou.seashop.product.**.mapper",
        "com.sankuai.shangou.seashop.promotion.dao.core.mapper"
})
@EnableFeignClients(basePackages = {
        "com.sankuai.shangou.seashop",
        "com.hishop.himall.report.api.service"
})
@EnableTransactionManagement
@EnableDiscoveryClient
@EnableAsync
public class TradeApplication {
    private static final Logger log = LoggerFactory.getLogger(TradeApplication.class);

    public static void main(String[] args) {
        SpringApplication.run(TradeApplication.class, args);
        log.info("服务启动成功！");
    }
}
