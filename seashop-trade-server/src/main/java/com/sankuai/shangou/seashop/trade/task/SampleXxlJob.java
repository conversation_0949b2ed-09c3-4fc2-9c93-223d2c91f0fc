package com.sankuai.shangou.seashop.trade.task;
//

import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

// SampleXxlJob任务执行类
@Component
@Slf4j
public class SampleXxlJob {
    @XxlJob("demoJobHandler")
    @XxlRegister(cron = "0 0 * * * ? ",
            author = "snow",
            jobDesc = "测试job")
    public ReturnT<String> demoJobHandler(String param) throws Exception {
        log.info("--收到测试task调度");
        // 任务逻辑
        return ReturnT.SUCCESS;
    }
}