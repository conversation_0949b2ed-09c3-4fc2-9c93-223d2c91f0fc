server:
  port: 8084
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
        slave:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **************************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
  elasticsearch:
    uris: http://**************:9200

squirrel:
  category: sg-seashop-cache
  # Squirrel属性配置
  clusterName: redis-sg-common_qa

#分布式锁cerberus
cerberus:
  lock:
    env: offline
    category: sg-seashop-cache
venus:
  hostName: http://p0.inf.test.sankuai.com/seashopimagetest/
  bucket: seashopimagetest
  clientId: 8scckx46q4cjn4kq000000000054eb54
# s3配置
s3plus:
  hostName: http://msstest.vip.sankuai.com
  bucketName: seashop-test
  downloadUrlHeader: http://msstest.vip.sankuai.com/seashop-test/


logging:
  level:
    org.apache.rocketmq: debug
    com.baomidou.mybatisplus: info
    com.sankuai.shangou.seashop.trade: debug

mybatis-plus:
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sankuai.shangou.seashop.trade.dao,com.sankuai.shangou.seashop.promotion.dao,com.sankuai.shangou.seashop.product.dao
  global-config:
    banner: true
  check-config-location: true
hishop:
  storage:
    storage-type: OSS
    bucket-name: chengpeikeji
    endpoint: https://oss-cn-hangzhou.aliyuncs.com
    access-key: LTAI5tEbUHz2YUzmLGt1LWiN
    secret-key: ******************************
    domain: https://chengpeikeji.oss-cn-hangzhou.aliyuncs.com
    base-path: /himall-base/rs/${spring.application.name}
flash:
  sale:
    url: /home-page/product-detail?productId=
#rocketmq:
  #name-server: **************:9876
  #producer:
    #group: seashop_operation_log_consumer
    #group: ${spring.application.name}
chenpei:
  api:
    url: http://************:1886/
himall-trade:
  dev:
    url: http://localhost:8084
himall-order:
  dev:
    url: http://localhost:8083
himall-base:
  dev:
    url: http://localhost:8082
himall-report:
  dev:
    url: http://localhost:8085
