# web服务端口号
server:
  port: 8080

spring:
  application:
    name: himall-trade
  profiles:
    active: chengpei_local
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  redis:
    database: 1
  cloud:
    nacos:
      server-addr: ${NACOS_SERVER:124.71.221.117:8848}
      username: ${NACOS_USERNAME:nacos}
      password: ${NACOS_PASSWORD:hishop}
      config:
        namespace: ${spring.profiles.active}
        group: 1.0.0
      discovery:
        namespace: ${spring.profiles.active}
        group: 1.0.0
  config:
    import:
      - optional:nacos:himall-common.yml
      - optional:nacos:${spring.application.name}.yml
  dynamic:
    tp:
      enabled: true
      enabled-collect: false
      executors:
        - thread-pool-name: tradeAsyncExecutor
          thread-pool-alias-name: trade.async
          core-pool-size: 10
          maximum-pool-size: 20
          keep-alive-time: 60000
          queue-capacity: 500000
          thread-name-prefix: trade-asyncApi
          task-wrapper-names:
            - ttl
            - mdc
        - thread-pool-name: esBuildOrderExecutor
          thread-pool-alias-name: esBuild.order
          core-pool-size: 10
          maximum-pool-size: 20
          keep-alive-time: 60000
          queue-capacity: 500000
          thread-name-prefix: es-build-order
          task-wrapper-names:
            - ttl
            - mdc
        - thread-pool-name: esBuildRefundExecutor
          thread-pool-alias-name: esBuild.refund
          core-pool-size: 10
          maximum-pool-size: 20
          keep-alive-time: 60000
          queue-capacity: 500000
          thread-name-prefix: es-build-refund
          task-wrapper-names:
            - ttl
            - mdc
nfs:
  cos:
    enabled: true
    access-key-id: AKIDvf9NL3UhfNoztWsxoPjpleNtBE4g290a
    access-key-secret: NWP1Vs7B6oHTO2Xu8FDyUkjSu52j9H8i
    bucketName: meituan-1254179351
    region: ap-chongqing
    domain: https://meituan-1254179351.cos.ap-chongging.nygcloud. con/tester
    prefix: tester/

#xxl:
#  job:
#    executor:
#      # appName
#      appname: chengpei_${spring.application.name}
#      # 执行器名称
#      title: chengpei_${spring.application.name}
#    #      title: 示例执行器
#    admin:
#      # 登录用户名
#      username: admin
#      # 登录密码
#      password: 123456
#      # xxl-job 地址
#      addresses: https://himall-dev.cce.35hiw.com/xxl-job-admin
##      ip: *************
##      port: 8080
