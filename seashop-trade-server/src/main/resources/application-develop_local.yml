server:
  port: 8084
spring:
  datasource:
    dynamic:
      primary: master
      datasource:
        master:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
        slave:
          driver-class-name: com.mysql.cj.jdbc.Driver
          url: **********************************************************************************************************************************
          username: himall
          password: bozIRn5S7hH6C1
  elasticsearch:
    uris: http://**************:9200

squirrel:
  category: sg-seashop-cache
  # Squirrel属性配置
  clusterName: redis-sg-common_qa

#分布式锁cerberus
cerberus:
  lock:
    env: offline
    category: sg-seashop-cache
venus:
  hostName: http://p0.inf.test.sankuai.com/seashopimagetest/
  bucket: seashopimagetest
  clientId: 8scckx46q4cjn4kq000000000054eb54
# s3配置
s3plus:
  hostName: http://msstest.vip.sankuai.com
  bucketName: seashop-test
  downloadUrlHeader: http://msstest.vip.sankuai.com/seashop-test/



logging:
  level:
    org.apache.rocketmq: debug
    com.baomidou.mybatisplus: info
    com.sankuai.shangou.seashop.trade: debug

mybatis-plus:
  #实体扫描，多个package用逗号或者分号分隔
  typeAliasesPackage: com.sankuai.shangou.seashop.trade.dao
  global-config:
    banner: true
  check-config-location: true

hishop:
  storage:
    storage-type: OBS
    bucket-name: himall-test
    endpoint: https://obs.cn-south-1.myhuaweicloud.com
    access-key: UEDFC3T2O7J1RXQQIAVO
    secret-key: FzRPLhtiitbYO3MTrXxArFLUT3KBIkIdmw9MIKEL
    domain: https://himall-obs.35hiw.com
    base-path: /${spring.profiles.active}/rs/himall-base
flash:
  sale:
    url: /home-page/product-detail?productId=
# xxl:
#   job:
#     server:
#       #演示环境，不需要xxljob，这里配置错误地址
#       #addresses: https://histore.cce.35hiw.com/xxl-job-server
#       addresses: https://localhost:18081/xxl-job-server
#       accessToken: 'default_token'
#     executor:
#       appname: 'sss'

#本地联调，调用自己服务用http://localhost:端口，调用test远程服务用https://himall.cce.35hiw.com
#himall-base：8082
#himall-order: 8083
#himall-report ：8085
#himall-gw：8081
#himall-trade：8084
himall-order:
  dev:
    url: http://127.0.0.1:8083
himall-base:
  dev:
    url: http://himall.cce.35hiw.com
himall-report:
  dev:
    url: http://himall.cce.35hiw.com
himall-trade:
  dev:
    url: http://localhost:8084
xxl:
  job:
    executor:
      # appName
      appname: demo_${spring.application.name}
      # 执行器名称
      title: demo_${spring.application.name}
    #      title: 示例执行器
    admin:
      # 登录用户名
      username: admin
      # 登录密码
      password: 123456
      # xxl-job 地址
      addresses: https://himall-dev.cce.35hiw.com/xxl-job-admin