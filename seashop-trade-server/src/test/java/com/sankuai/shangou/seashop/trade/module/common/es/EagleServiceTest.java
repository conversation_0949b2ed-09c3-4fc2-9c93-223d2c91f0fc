package com.sankuai.shangou.seashop.trade.module.common.es;

import com.sankuai.shangou.seashop.trade.TradeApplication;
import com.sankuai.shangou.seashop.trade.common.constant.EsConst;
import com.sankuai.shangou.seashop.trade.common.es.EagleService;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = TradeApplication.class)
public class EagleServiceTest {

    @Resource
    private EagleService eagleService;

    @Test
    public void testDeleteDoc() {
        String indexTradeProduct = EsConst.INDEX_TRADE_PRODUCT;
        System.out.println(indexTradeProduct);
//        eagleService.batchDelete(EsConst.INDEX_TRADE_PRODUCT, Arrays.asList("1", "2", "3"));
    }


}
