package com.sankuai.shangou.seashop.trade;

import com.sankuai.shangou.seashop.product.core.service.ProductEsBuildService;
import com.sankuai.shangou.seashop.product.dao.core.domain.Product;
import com.sankuai.shangou.seashop.product.dao.core.domain.ProductAudit;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductAuditRepository;
import com.sankuai.shangou.seashop.product.dao.core.repository.ProductRepository;
import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/18 15:48
 */
@Slf4j
@SpringBootTest(classes = TradeApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
public class EsBuildTest {

    @Resource
    private TradeProductService tradeProductService;

    @Resource
    private ProductRepository productRepository;
    @Resource
    private ProductAuditRepository productAuditRepository;

    @Resource
    private ProductEsBuildService productEsBuildService;

    @Test
    public void buildAllProduct() {
        List<Product> list = productRepository.list();
        for (Product product : list) {
            log.info("开始构建商品索引:{}", product.getProductId());
            productEsBuildService.buildProductEs(product.getProductId());
        }

    }

    @Test
    public void buildAllAuditProduct() {
        List<ProductAudit> list = productAuditRepository.list();
        for (ProductAudit product : list) {
            log.info("开始构建商品索引:{}", product.getProductId());
            productEsBuildService.buildProductAuditEs(product.getProductId());
        }

    }
}
