package com.sankuai.shangou.seashop.trade.module.common.utils;


import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 */

public class ReflectUtilTest {

    @Test
    public void testGetField() {
        /*List<String> PRODUCT_INDEX_FIELD = Arrays.asList("allImagePath", "brandId", "categoryId",
                "categoryPath", "marketPrice", "commentCount", "commentScoreSum", "mainImagePath", "onsaleTime",
                "productAttribute", "productCode", "productId", "productName", "saleCount", "shopId");
        ProductDto productDto = ProductDto.builder()
                .productId(1L)
                .shopId(1L)
                .brandId(5L)
                .categoryId(1L)
                .categoryPath("1-1-1")
                .marketPrice(new BigDecimal("100"))
                .imagePath("http://www.baidu.com")
                .checkTime(new Date())
                .productCode("123456")
                .productName("测试商品")
                .saleCounts(100L)
                .build();
        Map<String*//*字段名称*//*, Object*//*字段值*//*> doc = new HashMap<>(PRODUCT_INDEX_FIELD.size());
        PRODUCT_INDEX_FIELD.forEach(field -> {
            Field declaredField = ReflectionUtils.findField(ProductDto.class, field);
            if (Objects.isNull(declaredField)) {
                return;
            }
            ReflectionUtils.makeAccessible(declaredField);
            Object value = ReflectionUtils.getField(declaredField, productDto);
            if (Objects.nonNull(value)) {
                doc.put(field, value);
            }
        });
        Assert.assertEquals(doc.get("brandId"), 5L);*/
    }


}
