package com.sankuai.shangou.seashop.trade.module.core.service;

/*import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.AttributeDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.AttributeValueDto;
import com.sankuai.shangou.seashop.product.thrift.core.response.product.model.ProductDto;*/

import com.sankuai.shangou.seashop.trade.TradeApplication;
import com.sankuai.shangou.seashop.trade.common.es.EagleService;
import com.sankuai.shangou.seashop.trade.core.service.TradeProductService;
import com.sankuai.shangou.seashop.trade.core.service.model.product.SearchProductBo;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = TradeApplication.class)
public class TradeProductServiceTest {

    @Resource
    private TradeProductService tradeProductService;
    @Resource
    private EagleService eagleService;

    @Test
    public void testInvokeEsBuildTradeProduct() {
        /*Stream.of(1L, 2L, 3L)
                .forEach(num -> {
                    ProductDto productDto = buildProductDto(num);
                    Mockito.when(productRemoteService.queryProduct(num)).thenReturn(productDto);
                    tradeProductService.build(num);
                });*/

    }

    /**
     * <pre>
     * 一、商品名称全文搜索
     * ```json
     * {
     *   "query": {
     *     "match": {
     *       "productName.text": "伊利"
     *     }
     *   }
     * }
     * ```
     * </pre>
     * <AUTHOR>
     */
    @Test
    public void testSearch() {

    }

    /**
     * <pre>
     * {
     *   "query": {
     *     "bool":{
     *       "must":[{
     *         "match": {
     *           "productName.text": "金典2"
     *         }
     *       },
     *       {
     *           "nested": {
     *             "path": "productAttribute",
     *             "query": {
     *               "term": {
     *                 "productAttribute.attributeValues": "6存"
     *               }
     *             }
     *           }
     *         }]
     *     }
     *   },
     *   "from" : 0,
     *   "size" : 1,
     *   "aggs": {
     *     "attributes": {
     *       "nested": {
     *         "path": "productAttribute"
     *       },
     *       "aggs": {
     *         "attributeName": {
     *           "terms": {
     *             "field": "productAttribute.attributeName"
     *           },
     *           "aggs": {
     *             "attributeValues": {
     *               "terms": {
     *                 "field": "productAttribute.attributeValues"
     *               }
     *             }
     *           }
     *         }
     *       }
     *     },
     *     "brandIdAggregation": {
     *       "terms": {
     *         "field": "brandId"
     *       }
     *     }
     *   }
     * }
     * </pre>
     * <AUTHOR>
     */
    @Test
    public void testSearchAndAgg() {
        SearchProductBo searchBo = new SearchProductBo();
        searchBo.setSearchKey("康师傅蔓越莓饮料");
        searchBo.setPageNo(1);
        searchBo.setPageSize(2);
        tradeProductService.search(searchBo);
    }


    /*private ProductDto buildProductDto(Long productId) {
        return ProductDto.builder()
                .productId(productId)
                .shopId(productId + 1)
                .brandId(productId + 2)
                .categoryId(productId + 3)
                .categoryPath("3-3-3")
                .marketPrice(new BigDecimal("100"))
                .imagePath("http://www.baidu.com")
                .checkTime(new Date())
                .productCode("6900100" + productId)
                .productName("威露士抗菌洗衣液" + NAME_MAP.get(productId))
                .saleCounts(200L)
                .imgList(Arrays.asList("http://www.baidu.com", "http://www.sina.com"))
                .attributeList(attributeList(productId + 1))
                .build();
    }

    private final static Map<Long, String> NAME_MAP = MapUtils.putAll(new HashMap<>(), new Object[][]{
            {1L, "金典"},
            {2L, "金典2"},
            {3L, "伊利"}
    });
    private final static List<AttributeDto> ATTRIBUTE_DTO_LIST = Arrays.asList(
            AttributeDto.builder()
                    .id(1L)
                    .name("颜色")
                    .attributeValueList(Arrays.asList(
                            AttributeValueDto.builder().attributeId(1L).id(1L).value("红色").build(),
                            AttributeValueDto.builder().attributeId(1L).id(2L).value("蓝色").build())
                    )
                    .build(),
            AttributeDto.builder()
                    .id(2L)
                    .name("材质")
                    .attributeValueList(Arrays.asList(
                            AttributeValueDto.builder().attributeId(2L).id(3L).value("羊毛").build(),
                            AttributeValueDto.builder().attributeId(2L).id(4L).value("呢绒").build())
                    )
                    .build(),
            AttributeDto.builder()
                    .id(3L)
                    .name("尺寸")
                    .attributeValueList(Arrays.asList(
                            AttributeValueDto.builder().attributeId(3L).id(5L).value("5存").build(),
                            AttributeValueDto.builder().attributeId(3L).id(6L).value("6存").build())
                    )
                    .build(),
            AttributeDto.builder()
                    .id(4L)
                    .name("功能")
                    .attributeValueList(Arrays.asList(
                            AttributeValueDto.builder().attributeId(4L).id(7L).value("A").build(),
                            AttributeValueDto.builder().attributeId(4L).id(8L).value("B").build())
                    )
                    .build()
    );
    private List<AttributeDto> attributeList(long num) {
        return ATTRIBUTE_DTO_LIST.subList(0, (int) num);
    }*/

}
