package com.sankuai.shangou.seashop.trade;

import com.sankuai.shangou.seashop.product.core.task.ProductEsTask;
import com.sankuai.shangou.seashop.promotion.core.task.ReportCouponTask;
import com.sankuai.shangou.seashop.trade.core.task.ReportTask;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

/**
 * <AUTHOR> snow.xu
 * @version 1.0.0
 * @Classname ReportTaskTest
 * Description //TODO
 * @date 2024/12/6 09:22
 */
@SpringBootTest(classes = TradeApplication.class)
public class ProductEsTaskTest {

    @Resource
    private ProductEsTask productEsTask;

    @Test
    public void test() {
        productEsTask.refreshProductEs("{}");
    }

}
