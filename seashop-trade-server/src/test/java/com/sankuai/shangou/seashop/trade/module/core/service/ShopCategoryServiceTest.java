package com.sankuai.shangou.seashop.trade.module.core.service;

import com.sankuai.shangou.seashop.product.core.service.ShopCategoryService;
import com.sankuai.shangou.seashop.product.core.service.model.TransferProductBo;
import com.sankuai.shangou.seashop.trade.TradeApplication;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;

@SpringBootTest(classes = TradeApplication.class)
public class ShopCategoryServiceTest {

    @Resource
    private ShopCategoryService shopCategoryService;

    @Test
    public  void  transferProductTest( ){
        TransferProductBo productBo = new TransferProductBo(148L,149L);

        shopCategoryService.transferProduct(productBo);
    }
}
