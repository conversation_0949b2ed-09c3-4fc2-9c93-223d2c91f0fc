package com.sankuai.shangou.seashop.trade.module.common.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;

import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.trade.TradeApplication;
import com.sankuai.shangou.seashop.trade.common.constant.CacheConst;

/**
 * <AUTHOR>
 */
@SpringBootTest(classes = TradeApplication.class)
public class SquirrelTest {

    @Resource
    private SquirrelUtil squirrelUtil;

    private static final String userId = "123";
    private static final String token = UUID.randomUUID().toString();

    @Test
    public void testSet() {
        String key = CacheConst.CACHE_ORDER_SUBMIT_TOKEN + token;
        // token随机，这样同一个账号可以分开提交订单
        // 考虑用户可能停留在页面的时间，这里的预览订单页可以修改数量等，设置有效时间为 60 分钟
        squirrelUtil.set(key, userId, CacheConst.EXPIRE_SUBMIT_ORDER_TOKEN_60_MIN);
        assertEquals(userId, squirrelUtil.get(key));
    }

    @Test
    public void testParallelDelete() {
        String key = CacheConst.CACHE_ORDER_SUBMIT_TOKEN + token;
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        for (int i = 0; i < 6; i++) {
            executorService.execute(() -> {
                Boolean result = squirrelUtil.compareAndDelete(key, userId);
                System.out.println("threadName=" + Thread.currentThread().getName() + "result = " + result);
            });
        }

    }

}
