package com.sankuai.shangou.seashop.trade.module.product;

import javax.annotation.Resource;

import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.product.core.mq.model.OrderMessage;
import com.sankuai.shangou.seashop.product.core.service.assist.event.OrderEventFactory;
import com.sankuai.shangou.seashop.trade.TradeApplication;

/**
 * <AUTHOR>
 * @date 2024/10/31 11:13
 */
@SpringBootTest(classes = TradeApplication.class)
public class OrderEventFactoryTest {

    @Resource
    private OrderEventFactory orderEventFactory;

    @Test
    public void test() {
        OrderMessage orderMessage = JsonUtil.parseObject("{\"orderId\":\"2024103111002071\",\"orderEventName\":\"COMMENT_ORDER\"}",
            OrderMessage.class);
        orderEventFactory.handle(orderMessage);
    }

}
