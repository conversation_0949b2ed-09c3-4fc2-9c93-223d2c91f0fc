package com.sankuai.shangou.seashop.trade;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

public class FileDocTest {

    private static final String IMPORT_STATEMENTS = "import io.swagger.v3.oas.annotations.media.Schema;\n";
    private static final String FIELD_DOC_IMPORT = "import com.meituan.servicecatalog.api.annotations.FieldDoc;";
    private static final String REQUIREDNESS_IMPORT = "import com.meituan.servicecatalog.api.annotations.Requiredness;";
    private static final String TYPE_DOC_IMPORT = "import com.meituan.servicecatalog.api.annotations.TypeDoc;";
    public static void main(String[] args) throws IOException {
        FileDocTest replacer = new FileDocTest();
        replacer.replaceAnnotationsInDirectory("D:\\himallWorkspace\\himall-trade\\seashop-trade-api\\src\\main\\java\\com\\sankuai\\shangou\\seashop\\trade\\thrift\\core\\dto");
        //replacer.replaceAnnotationsInFile(Paths.get("D:\\himallWorkspace\\himall-trade\\seashop-trade-api\\src\\main\\java\\com\\sankuai\\shangou\\seashop\\product\\thrift\\core\\request\\brand\\AuditBrandApplyReq.java"));
    }
    private static final String JAVA_FILE_EXTENSION = ".java";

    public void replaceAnnotationsInDirectory(String directoryPath) throws IOException {
        try {
            Stream<Path> paths = Files.walk(Paths.get(directoryPath));
            paths.filter(Files::isRegularFile)
                    //.filter(path -> path.toString().endsWith(JAVA_FILE_EXTENSION))
                    .forEach(this::replaceAnnotationsInFile);
        }catch (IOException e){
            e.printStackTrace();
        }
    }

    public void replaceAnnotationsInFile(Path filePath) {
        try {
            String content = new String(Files.readAllBytes(filePath));
            String modelContent = replaceModelAnnotations(content);
            String modifiedContent = replaceAnnotations(modelContent);

            if (StringUtils.isNotBlank(modifiedContent) && !content.equals(modifiedContent)) {
                Files.write(filePath, modifiedContent.getBytes());
                System.out.println("已完成修改: " + filePath);
            }else {
                System.out.println("不需要修改: " + filePath);
            }
        } catch (IOException e) {
            System.err.println("Error processing file: " + filePath);
            e.printStackTrace();
        }
    }

    private String replaceAnnotations(String content) {
        // 替换注解
        //Pattern pattern = Pattern.compile("@FieldDoc\\s*\\(\\s*description\\s*=\\s*\"([^\"]+)\"\\s*(?:,\\s*requiredness\\s*=\\s*Requiredness\\.([A-Z_]+)\\s*)?\\)");
        Pattern pattern = Pattern.compile("@FieldDoc\\s*\\(\\s*description\\s*=\\s*\"([^\"]+)\"\\s*(?:,\\s*requiredness\\s*=\\s*([^,\\s)]+))?\\s*\\)");
        Matcher matcher = pattern.matcher(content);
        StringBuffer sb = new StringBuffer();
        boolean found = false;
        while (matcher.find()) {
            found = true;
            String description = matcher.group(1);
            String requiredness = matcher.group(2);
            String replacement = "@Schema(description = \"" + description + "\"";
            if (StringUtils.isNotBlank(requiredness) && requiredness.contains("REQUIRED")) {
                replacement += ", required = true";
            }
            replacement += ")";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        if (!found) {
            return null;
        }
        matcher.appendTail(sb);

        String modifiedContent = sb.toString();

        // 删除指定的导入语句
        modifiedContent = removeImports(modifiedContent);

        // 在第三行添加导入语句
        String[] lines = modifiedContent.split("\n", 3);
        if (lines.length > 2) {
            modifiedContent = lines[0] + "\n" + lines[1] + "\n" + IMPORT_STATEMENTS + lines[2];
        } else if (lines.length == 2) {
            // 如果文件只有两行，就在末尾添加导入语句
            modifiedContent = lines[0] + "\n" + lines[1] + "\n" + IMPORT_STATEMENTS;
        } else if (lines.length == 1) {
            // 如果文件只有一行，就在末尾添加导入语句
            modifiedContent = lines[0] + "\n\n" + IMPORT_STATEMENTS;
        }

        return modifiedContent;
    }
    private String replaceModelAnnotations(String content) {
        // 替换注解
        Pattern pattern = Pattern.compile("@TypeDoc\\s*\\(\\s*description\\s*=\\s*\"([^\"]+)\"[^)]*\\)");
        Matcher matcher = pattern.matcher(content);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            String description = matcher.group(1);
            String replacement = "@Schema(description = \"" + description + "\"" + ")";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);
        return sb.toString();
    }

    private String removeImports(String content) {
        String[] lines = content.split("\n");
        List<String> newLines = new ArrayList<>();

        for (String line : lines) {
            if (!line.trim().equals(FIELD_DOC_IMPORT) && !line.trim().equals(REQUIREDNESS_IMPORT) && !line.trim().equals(TYPE_DOC_IMPORT)
            && !line.startsWith("@TypeDoc")) {
                newLines.add(line);
            }
        }

        return String.join("\n", newLines);
    }
}