package com.sankuai.shangou.seashop.trade;

import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

public class FileDocForModelTest {

    private static final String FIELD_DOC_IMPORT = "import com.meituan.servicecatalog.api.annotations.FieldDoc;";
    private static final String REQUIREDNESS_IMPORT = "import com.meituan.servicecatalog.api.annotations.Requiredness;";
    private static final String TYPE_DOC_IMPORT = "import com.meituan.servicecatalog.api.annotations.TypeDoc;";
    public static void main(String[] args) throws IOException {
        FileDocForModelTest replacer = new FileDocForModelTest();
        replacer.replaceAnnotationsInDirectory("D:\\himallWorkspace\\himall-trade\\seashop-trade-core\\src\\main\\java\\com\\sankuai\\shangou\\seashop\\promotion\\core\\model");
        //replacer.replaceAnnotationsInFile(Paths.get("D:\\himallWorkspace\\himall-trade\\seashop-trade-api\\src\\main\\java\\com\\sankuai\\shangou\\seashop\\product\\thrift\\core\\request\\brand\\AuditBrandApplyReq.java"));
    }
    private static final String JAVA_FILE_EXTENSION = ".java";

    public void replaceAnnotationsInDirectory(String directoryPath) throws IOException {
        try {
            Stream<Path> paths = Files.walk(Paths.get(directoryPath));
            paths.filter(Files::isRegularFile)
                    //.filter(path -> path.toString().endsWith(JAVA_FILE_EXTENSION))
                    .forEach(this::replaceAnnotationsInFile);
        }catch (IOException e){
            e.printStackTrace();
        }
    }

    public void replaceAnnotationsInFile(Path filePath) {
        try {
            String content = new String(Files.readAllBytes(filePath));
            String modifiedContent = replaceAnnotations(content);

            if (StringUtils.isNotBlank(modifiedContent) && !content.equals(modifiedContent)) {
                Files.write(filePath, modifiedContent.getBytes());
                System.out.println("已完成修改: " + filePath);
            }else {
                System.out.println("不需要修改: " + filePath);
            }
        } catch (IOException e) {
            System.err.println("Error processing file: " + filePath);
            e.printStackTrace();
        }
    }

    private String replaceAnnotations(String content) {
        // 替换注解
        //Pattern pattern = Pattern.compile("@FieldDoc\\s*\\(\\s*description\\s*=\\s*\"([^\"]+)\"\\s*(?:,\\s*requiredness\\s*=\\s*Requiredness\\.([A-Z_]+)\\s*)?\\)");
        Pattern pattern = Pattern.compile("@FieldDoc\\s*\\(\\s*description\\s*=\\s*\"([^\"]+)\"\\s*(?:,\\s*requiredness\\s*=\\s*([^,\\s)]+))?\\s*\\)");
        Matcher matcher = pattern.matcher(content);
        StringBuffer sb = new StringBuffer();
        boolean found = false;
        while (matcher.find()) {
            found = true;
            String description = matcher.group(1);
            String replacement = "//" + description;
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        if (!found) {
            return null;
        }
        matcher.appendTail(sb);

        String modifiedContent = sb.toString();

        // 删除指定的导入语句
        modifiedContent = removeImports(modifiedContent);
        return modifiedContent;
    }

    private String removeImports(String content) {
        String[] lines = content.split("\n");
        List<String> newLines = new ArrayList<>();

        for (String line : lines) {
            if (!line.trim().equals(FIELD_DOC_IMPORT) && !line.trim().equals(REQUIREDNESS_IMPORT) && !line.trim().equals(TYPE_DOC_IMPORT)
            && !line.startsWith("@TypeDoc")) {
                newLines.add(line);
            }
        }

        return String.join("\n", newLines);
    }
}