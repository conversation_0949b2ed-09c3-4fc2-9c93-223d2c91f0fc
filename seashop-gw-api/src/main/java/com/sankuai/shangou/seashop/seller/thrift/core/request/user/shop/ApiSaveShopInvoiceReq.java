package com.sankuai.shangou.seashop.seller.thrift.core.request.user.shop;

import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;
import com.sankuai.shangou.seashop.base.boot.request.BaseParamReq;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/11/29 8:54
 */
@Data
public class ApiSaveShopInvoiceReq extends BaseParamReq {

    @FieldDoc(description = "主键")
    @PrimaryField
    private Long id;

    @FieldDoc(description = "店铺ID")
    @ExaminField
    private Long shopId;

    @FieldDoc(description = "是否提供发票")
    @ExaminField
    private Boolean whetherInvoice;

    @FieldDoc(description = "是否提供普通发票")
    @ExaminField
    private Boolean whetherPlainInvoice;

    @FieldDoc(description = "是否提供电子发票")
    @ExaminField
    private Boolean whetherElectronicInvoice;

    @FieldDoc(description = "普通发票税率")
    @ExaminField
    private BigDecimal plainInvoiceRate;

    @FieldDoc(description = "是否提供增值税发票")
    @ExaminField
    private Boolean whetherVatInvoice;

    @FieldDoc(description = "订单完成后多少天开具增值税发票")
    @ExaminField
    private Integer vatInvoiceDay;

    @FieldDoc(description = "增值税税率")
    @ExaminField
    private BigDecimal vatInvoiceRate;

    @FieldDoc(description = "创建时间")
    private Date createTime;


    public void checkParameter() {
        this.initDefaultValue();
        if (whetherInvoice == null) {
            throw new IllegalArgumentException("whetherInvoice是否提供发票不能为空");
        }
    }

    private void initDefaultValue() {
        if (this.whetherInvoice) {
            if (this.plainInvoiceRate == null) {
                this.plainInvoiceRate = BigDecimal.ZERO;
            }
            if (this.vatInvoiceRate == null) {
                this.vatInvoiceRate = BigDecimal.ZERO;
            }
            if (this.vatInvoiceDay == null) {
                this.vatInvoiceDay = 0;
            }
        }
    }

    public String getPlainInvoiceRateString() {
        return this.bigDecimal2String(this.plainInvoiceRate);
    }


    public void setPlainInvoiceRateString(String plainInvoiceRate) {
        this.plainInvoiceRate = this.string2BigDecimal(plainInvoiceRate);
    }


    public String getVatInvoiceRateString() {
        return this.bigDecimal2String(this.vatInvoiceRate);
    }


    public void setVatInvoiceRateString(String vatInvoiceRate) {
        this.vatInvoiceRate = this.string2BigDecimal(vatInvoiceRate);
    }


    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }


    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }
}
