package com.sankuai.sgb2b.seashop.mall.api.thrift.response.base;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.meituan.servicecatalog.api.annotations.FieldDoc;
import com.meituan.servicecatalog.api.annotations.TypeDoc;
import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

@JsonSerialize
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ToString
@TypeDoc(description = "地区返回")
public class ApiBaseRegionRes extends BaseThriftDto {

    @FieldDoc(description = "地址编号")
    private Long id;


    @FieldDoc(description = "美团区域编号")
    private String code;


    @FieldDoc(description = "区域名称")
    private String name;


    @FieldDoc(description = "区域简称")
    private String shortName;


    @FieldDoc(description = "状态 0：正常 9：删除")
    private int status;

    @FieldDoc(description = "父id")
    private Long parentId;
    @FieldDoc(description = "地址级别")
    private int regionLevel;

    @Schema(description = "子地址数量")
    private Integer subCount;
}
