package com.sankuai.shangou.seashop.order.finance.remote;

import com.sankuai.shangou.seashop.order.finance.service.CashDepositPayService;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositPayReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositPayResp;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class OrderQueryFinanceRemoteService {
    @Resource
    private CashDepositPayService cashDepositPayService;
    public List<CashDepositPayResp> selectCashDepositPay(CashDepositPayReq request) {
        return cashDepositPayService.selectCashDepositPay(request);
    }


}
