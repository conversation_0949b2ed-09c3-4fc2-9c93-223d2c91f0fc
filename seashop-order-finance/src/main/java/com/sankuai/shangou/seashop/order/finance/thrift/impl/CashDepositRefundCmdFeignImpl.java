package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.order.common.constant.FinanceLockConst;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositRefundService;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositRefundCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundConfirmReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundRefuseReq;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description:
 */
@RestController
@RequestMapping("/finance/cashDepositRefundCmd")
public class CashDepositRefundCmdFeignImpl implements CashDepositRefundCmdFeign {

    @Resource
    private CashDepositRefundService cashDepositRefundService;


    @PostMapping(value = "/refuse", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> refuse(@RequestBody CashDepositRefundRefuseReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("refuse", request, req -> {
            req.checkParameter();
            String lockKey = String.format(FinanceLockConst.LOCK_CASH_DEPOSIT_DETAIL, req.getId());
            LockHelper.lock(lockKey, () -> {
                cashDepositRefundService.refuse(req);
            });
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/confirm", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> confirm(@RequestBody CashDepositRefundConfirmReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("confirm", request, req -> {
            req.checkParameter();
            String lockKey = String.format(FinanceLockConst.LOCK_CASH_DEPOSIT_REFUND_DETAIL, req.getId());
            LockHelper.lock(lockKey, () -> {
                cashDepositRefundService.confirm(req);
            });
            return BaseResp.of();
        });
    }
}
