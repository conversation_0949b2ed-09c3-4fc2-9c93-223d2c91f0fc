package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.order.common.constant.FinanceLockConst;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositService;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayCreateResp;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreatePaymentReq;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@RestController
@RequestMapping("/finance/cashDeposit")
public class CashDepositCmdFeignImpl implements CashDepositCmdFeign {

    @Resource
    private CashDepositService cashDepositService;

    @PostMapping(value = "/createPayment",consumes = "application/json")
    @Override
    public ResultDto<OrderPayCreateResp> createPayment(@RequestBody CreatePaymentReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createPayment", request, req -> {
            req.checkParameter();
            String lockKey = String.format(FinanceLockConst.LOCK_CREATE_PAYMENT, req.getShopId());
            OrderPayCreateResp payPaymentCreateResp = LockHelper.lock(lockKey, FinanceLockConst.LOCK_TIME, () ->
                    cashDepositService.createPayment(req)
            );
            return payPaymentCreateResp;
        });
    }
}
