package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.finance.service.FinanceService;
import com.sankuai.shangou.seashop.order.thrift.finance.FinanceQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.FinanceQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceIndexResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceResp;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@RestController
@RequestMapping("/finance/financeQuery")
public class FinanceQueryFeignImpl implements FinanceQueryFeign {

    @Resource
    private FinanceService financeService;

    @PostMapping(value = "/getFinanceIndex", consumes = "application/json")
    @Override
    public ResultDto<FinanceIndexResp> getFinanceIndex(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getFinanceIndex", request, req ->
                financeService.getFinanceIndex(req)
        );
    }

    @PostMapping(value = "/getFinance", consumes = "application/json")
    @Override
    public ResultDto<FinanceResp> getFinance(@RequestBody FinanceQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getFinance", request, req ->
                financeService.getFinance(req));
    }
}
