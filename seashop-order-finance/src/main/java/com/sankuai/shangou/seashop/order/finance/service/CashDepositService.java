package com.sankuai.shangou.seashop.order.finance.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayCreateResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreatePaymentReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdListReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositResp;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
public interface CashDepositService {

    /**
     * 通过ShopId查询保证金信息
     *
     * @param req
     * @return
     */
    BasePageResp<CashDepositResp> queryListByShopId(ShopIdListReq req);

    /**
     * 通过ShopId查询保证金信息
     *
     * @param shopId
     * @return
     */
    CashDepositResp queryOneByShopId(Long shopId);

    /**
     * 创建支付
     *
     * @param request
     * @return
     */
    OrderPayCreateResp createPayment(CreatePaymentReq request);

    /**
     * 通过ShopId列表查询保证金信息
     *
     * @param req 请求参数
     */
    List<CashDepositResp> queryByShopId(ShopIdListReq req);
}
