package com.sankuai.shangou.seashop.order.finance.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.OrderIdQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementOrderResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PlatCommissionResp;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
public interface PendingSettlementService {

    /**
     * 获取待结算列表
     *
     * @param request 查询条件
     * @return 待结算订单列表
     */
    BasePageResp<PendingSettlementResp> getPendingSettlementList(PendingSettlementQryReq request);

    /**
     * 获取平台佣金总额
     *
     * @return 平台佣金总额
     */
    PlatCommissionResp getPlatCommission();

    /**
     * 获取待结算订单列表
     *
     * @param request 查询条件
     * @return 待结算订单列表
     */
    BasePageResp<PendingSettlementOrderResp> pageList(PendingSettlementOrderQryReq request);

    /**
     * 通过订单id查询待结算订单详情
     *
     * @param request 查询条件
     * @return 待结算订单详情
     */
    PendingSettlementOrderResp getDetailByOrderId(OrderIdQryReq request);
}
