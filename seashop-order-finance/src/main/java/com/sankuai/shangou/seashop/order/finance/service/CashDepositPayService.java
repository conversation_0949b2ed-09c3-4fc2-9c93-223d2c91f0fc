package com.sankuai.shangou.seashop.order.finance.service;

import com.sankuai.shangou.seashop.order.finance.mq.model.PaymentResultModel;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositPayReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositPayResp;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
public interface CashDepositPayService {

    /**
     * 更新保证金支付状态
     *
     * @param model
     */
    void updateStatus(PaymentResultModel model);

    /**
     * 保证金支付记录
     * @param req
     * @return
     */
    List<CashDepositPayResp> selectCashDepositPay(CashDepositPayReq req);
}
