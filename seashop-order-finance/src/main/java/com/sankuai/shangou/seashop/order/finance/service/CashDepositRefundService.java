package com.sankuai.shangou.seashop.order.finance.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundConfirmReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundRefuseReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundResp;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description:
 */
public interface CashDepositRefundService {

    /**
     * 保证金退款列表
     *
     * @param request
     * @return
     */
    BasePageResp<CashDepositRefundResp> refundList(CashDepositRefundQueryReq request);

    /**
     * 通过ID查询审核信息
     * @param id
     * @return
     */
    CashDepositRefundDetailResp refundDetail(Long id);

    /**
     * 保证金退款审批拒绝
     *
     * @param request
     * @return
     */
    void refuse(CashDepositRefundRefuseReq request);

    /**
     * 保证金退款审批通过
     *
     * @param request
     * @return
     */
    void confirm(CashDepositRefundConfirmReq request);


}
