package com.sankuai.shangou.seashop.order.finance.mq.model;

import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: lhx
 * @date: 2024/1/25/025
 * @description:
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderSettleMsgModel {

    private String orderId;

    /**
     * 类型 {@link TransactionTypesEnum}
     * <p>
     * PAY(1, "支付"),
     * REFUND(2, "退款"),
     * DEDUCTION(3, "扣款"),
     * FINISH(4, "订单完成"),
     * SETTLEMENT(5, "结算"),
     * ERROR_ORDER_REFUND(6, "异常订单退款"),
     * EXCESS_PAYMENT_REFUND(7, "超支退款")
     */
    private Integer type;

    /**
     * 店铺ID
     */
    private Long shopId;
}
