package com.sankuai.shangou.seashop.order.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.constant.FinanceLeafConst;
import com.sankuai.shangou.seashop.order.common.constant.OrderConst;
import com.sankuai.shangou.seashop.order.common.enums.FeeFlagEnum;
import com.sankuai.shangou.seashop.order.common.enums.FinanceResultCodeEnum;
import com.sankuai.shangou.seashop.order.common.remote.PayRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.dao.finance.domain.*;
import com.sankuai.shangou.seashop.order.dao.finance.model.CashDepositDetailQueryModel;
import com.sankuai.shangou.seashop.order.dao.finance.repository.*;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositDetailService;
import com.sankuai.shangou.seashop.order.thrift.core.enums.CashDepositPayTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.finance.CashDepositOperatorTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.CashDepositRefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ApplyRefundReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.DeductionLog;
import com.sankuai.shangou.seashop.order.thrift.finance.request.DeductionReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositDetailResp;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaymentConfirmCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.PaymentConfirmDivMembersDto;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PaymentConfirmCreateResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@Service
@Slf4j
public class CashDepositDetailServiceImpl implements CashDepositDetailService {

    @Resource
    private CashDepositDetailRepository cashDepositDetailRepository;
    @Resource
    private LeafService leafService;
    @Resource
    private CashDepositRepository cashDepositRepository;
    @Resource
    private CashDepositRefundRepository cashDepositRefundRepository;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private FinanceRepository financeRepository;
    @Resource
    private PlatAccountRepository platAccountRepository;
    @Resource
    private PayRemoteService payRemoteService;
    @Resource
    private CashDepositPayRepository cashDepositPayRepository;
    @Resource
    private BaseLogAssist baseLogAssist;
    /**
     * 平台保证金扣款开关
     */
    @Value("${finance.deduction}")
    private String financeDeductionSwitch;

    /**
     * 供应商保证金申请退款开关
     */
    @Value("${finance.apply.refund}")
    private String financeApplyRefundSwitch;

    @Override
    public List<CashDepositDetailResp> getPayListByCashDepositId(Long id) {
        List<CashDepositDetail> depositDetails = cashDepositDetailRepository.getPayListByCashDepositId(id);
        if (CollUtil.isNotEmpty(depositDetails)) {
            return JsonUtil.copyList(depositDetails, CashDepositDetailResp.class);
        }
        return CollUtil.newArrayList();
    }

    @Override
    public BasePageResp<CashDepositDetailResp> pageList(CashDepositDetailQueryReq request) {
        // 防止越权问题
        CashDeposit cashDeposit = cashDepositRepository.queryOneByShopId(request.getShopId());
        if(request.getSourceFrom() != null && request.getSourceFrom() != 1){
            if(Objects.isNull(cashDeposit)){
                return PageResultHelper.defaultEmpty(request);
            }
            request.setCashDepositId(cashDeposit.getId());
        }
        BasePageResp<CashDepositDetail> detailPage = cashDepositDetailRepository.pageList(request.buildPage(), JsonUtil.copy(request, CashDepositDetailQueryModel.class));
        if (null == detailPage || CollUtil.isEmpty(detailPage.getData())) {
            return PageResultHelper.defaultEmpty(request);
        }
        List<String> channelIdList = detailPage.getData().stream().filter(d -> d.getOperatorType().equals(CashDepositOperatorTypeEnum.PAY.getType())
                && StrUtil.isNotBlank(d.getChannelOrderId())).map(CashDepositDetail::getChannelOrderId).collect(Collectors.toList());

        List<CashDepositPay> cashDepositPays = cashDepositPayRepository.queryByPayIdList(channelIdList);
        List<CashDepositRefund> refundModels = cashDepositRefundRepository.selectCashDepositRefundByDetailIds(detailPage.getData().stream().map(CashDepositDetail::getId).collect(Collectors.toList()));

        return PageResultHelper.transfer(detailPage, CashDepositDetailResp.class, d -> {
            if (CollUtil.isNotEmpty(cashDepositPays) || cashDepositPays.size() > 0) {
                CashDepositPay cashDepositPay = cashDepositPays.stream().filter(c -> c.getPayId().equals(d.getChannelOrderId())).findFirst().orElse(null);
                if (null != cashDepositPay) {
                    String payment = cashDepositPay.getPayment();
                    if (CashDepositPayTypeEnum.ALIPAY.getType().equals(payment)) {
                        d.setRechargeWay(CashDepositPayTypeEnum.ALIPAY.getCode());
                    } else {
                        d.setRechargeWay(CashDepositPayTypeEnum.OTHER.getCode());
                    }
                }
            }
            // 先默认false
            d.setFlagRefund(false);
            if(!CollectionUtils.isEmpty(refundModels)){
                Map<Long, CashDepositRefund> refundModelMap = refundModels.stream().collect(Collectors.toMap(CashDepositRefund::getCashDepositDetailId, Function.identity(), (o1, o2) -> o1));
                CashDepositRefund refundModel = refundModelMap.get(d.getId());
                if(!Objects.isNull(refundModel)){
                    d.setFlagRefund(true);
                }
            }
            // 如果平台已经扣减完了，申请退款的按钮也应该隐藏
            if (d.getBalance()
                    .subtract(d.getForzenAmount())
                    .subtract(d.getRefundAmount())
                    .subtract(d.getPlatformDeduction())
                    .compareTo(BigDecimal.ZERO) == 0) {
                d.setFlagRefund(true);
            }
        });
    }

    @Override
    public void deduction(DeductionReq request) {
        AssertUtil.throwIfTrue("F".equals(financeDeductionSwitch), "平台保证金扣款开关已关闭，无法发起扣款");
        log.info("deduction，请求参数：{}", JSONUtil.toJsonStr(request));

        Long cashDepositDetailId = request.getCashDepositDetailId();
        CashDepositDetail cashDepositDetail = cashDepositDetailRepository.getById(cashDepositDetailId);
        if (null == cashDepositDetail) {
            throw new BusinessException(FinanceResultCodeEnum.CASH_DEPOSIT_DETAIL_NOT_EXIST.getCode(), FinanceResultCodeEnum.CASH_DEPOSIT_DETAIL_NOT_EXIST.getMsg());
        }

        // 判断余额是否足够：余额-冻结金额-退款金额-平台扣款金额-本次扣款金额
        if (cashDepositDetail.getBalance()
                .subtract(cashDepositDetail.getForzenAmount())
                .subtract(cashDepositDetail.getRefundAmount())
                .subtract(cashDepositDetail.getPlatformDeduction())
                .subtract(request.getDeductionAmount()).compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(FinanceResultCodeEnum.CASH_DEPOSIT_BALANCE_NOT_ENOUGH.getCode(), FinanceResultCodeEnum.CASH_DEPOSIT_BALANCE_NOT_ENOUGH.getMsg());
        }

        /*
         *  处理数据，调用分账逻辑
         */
        PaymentConfirmCreateReq paymentConfirmCreateReq = getAdaPaymentConfirmCreateReq(request, cashDepositDetail);

        log.info("deduction调用分账接口，请求参数：{}", JSONUtil.toJsonStr(paymentConfirmCreateReq));
        PaymentConfirmCreateResp paymentConfirmCreateResp = ThriftResponseHelper.executeThriftCall(() -> payRemoteService.createPaymentConfirm(paymentConfirmCreateReq));
        log.info("deduction调用分账接口，响应参数：{}", JSONUtil.toJsonStr(paymentConfirmCreateResp));

        CashDeposit cashDeposit = cashDepositRepository.getById(cashDepositDetail.getCashDepositId());
        Date now = new Date();
        DeductionLog oldValue = new DeductionLog();
        oldValue.setId(cashDepositDetail.getCashDepositId());
        oldValue.setCurrentBalance(cashDeposit.getCurrentBalance());

        DeductionLog newValue = new DeductionLog();
        newValue.setId(cashDepositDetail.getCashDepositId());
        newValue.setCurrentBalance(cashDeposit.getCurrentBalance().subtract(request.getDeductionAmount()));

        try {
            // 事务处理数据库操作
            TransactionHelper.doInTransaction(
                    () -> {
                        /*
                         * 保存扣款明细
                         */
                        CashDepositDetail newCashDepositDetail = new CashDepositDetail();
                        newCashDepositDetail.setAddDate(now);
                        // 取扣款的负数
                        newCashDepositDetail.setBalance(request.getDeductionAmount().negate());
                        newCashDepositDetail.setCashDepositId(cashDepositDetail.getCashDepositId());
                        newCashDepositDetail.setDescription(request.getDescription() + OrderConst.CASH_DEPOSIT_DEDUCTION_DESC + cashDepositDetail.getChannelOrderId());
                        newCashDepositDetail.setOperator(request.getOperator());
                        newCashDepositDetail.setDeductionType(request.getDeductionType());
                        newCashDepositDetail.setOperatorType(CashDepositOperatorTypeEnum.DEDUCTION.getType());
                        newCashDepositDetail.setDeductionFee(request.getDeductionFee());
                        newCashDepositDetail.setTradeNo(paymentConfirmCreateReq.getOrderNo());
                        log.info("保存扣款明细，参数：{}", JSONUtil.toJsonStr(newCashDepositDetail));
                        cashDepositDetailRepository.save(newCashDepositDetail);

                        // 保证金表处理
                        cashDeposit.setCurrentBalance(cashDeposit.getCurrentBalance().subtract(request.getDeductionAmount()));
                        // 已缴纳保证金不需要扣减，这个字段只增不减
//                        cashDeposit.setTotalBalance(cashDeposit.getTotalBalance().subtract(request.getDeductionAmount()));
                        cashDeposit.setDate(now);
                        log.info("保证金表处理，参数：{}", JSONUtil.toJsonStr(cashDeposit));
                        cashDepositRepository.updateById(cashDeposit);

                        // 保证金明细表处理
                        cashDepositDetail.setPlatformDeduction(cashDepositDetail.getPlatformDeduction().add(request.getDeductionAmount()));
                        log.info("保证金明细表处理，参数：{}", JSONUtil.toJsonStr(cashDepositDetail));
                        cashDepositDetailRepository.updateById(cashDepositDetail);

                        String channelOrderId = cashDepositDetail.getChannelOrderId();
                        // 截取订单号20位开始，获得shopId
                        Long shopId = Long.valueOf(channelOrderId.substring(FinanceLeafConst.CASH_DEPOSIT_PAY_ORDER_ID_PREFIX_LENGTH));
                        ShopDetailResp shopDetailResp = shopRemoteService.queryDetail(shopId);

                        oldValue.setShopName(shopDetailResp.getShopName());
                        newValue.setShopName(shopDetailResp.getShopName());
                        // 手动写日志
                        baseLogAssist.recordLog(ExaminModelEnum.ORDER, ExaProEnum.MODIFY,"平台保证金扣款",
                                request.getOperationUserId(), request.getOperationShopId(),
                                oldValue, newValue);
                        // 财务中间表处理
                        Finance finance = new Finance();
                        finance.setOrderId(paymentConfirmCreateReq.getOrderNo());
                        finance.setAdapayId(cashDepositDetail.getChannelId());
                        finance.setPayId(cashDepositDetail.getChannelOrderId());
                        finance.setType(TransactionTypesEnum.DEDUCTION.getCode());
                        finance.setShopId(shopId);
                        finance.setShopName(null != shopDetailResp ? shopDetailResp.getShopName() : "");
                        finance.setTransactionId(StrUtil.isNotEmpty(cashDepositDetail.getTradeNo()) ? cashDepositDetail.getTradeNo() : "");
                        finance.setTotalAmount(request.getDeductionAmount());
                        finance.setServiceAmount(paymentConfirmCreateResp.getFeeAmt());
                        finance.setDeductionType(request.getDeductionType());
                        finance.setServiceFee(request.getDeductionFee());
                        financeRepository.save(finance);

                        // 平台收入处理(平台收入只有一条记录)
                        PlatAccount platAccount = platAccountRepository.getPlatAccount();
                        platAccountRepository.saveOrUpdate(platAccount);
                    }
            );
            shopRemoteService.checkShopArrears(cashDeposit.getShopId());
        } catch (Exception e) {
            log.error("【非常重要的异常】调分账成功，处理数据库异常，deduction事务处理异常，参数：{}", JSONUtil.toJsonStr(request), e);
            throw e;
        }
    }

    /**
     * 获取分账请求参数
     *
     * @param request
     * @param cashDepositDetail
     * @return
     */
    @NotNull
    private PaymentConfirmCreateReq getAdaPaymentConfirmCreateReq(DeductionReq request, CashDepositDetail cashDepositDetail) {
        PaymentConfirmCreateReq paymentConfirmCreateReq = new PaymentConfirmCreateReq();
        paymentConfirmCreateReq.setSourceOrderId(cashDepositDetail.getChannelOrderId());
        paymentConfirmCreateReq.setOrderNo(FinanceLeafConst.CASH_DEPOSIT_DEDUCTION_ORDER_ID_PREFIX + leafService.generateNoBySnowFlake(FinanceLeafConst.CASH_DEPOSIT_DEDUCTION_ORDER_ID));
        paymentConfirmCreateReq.setConfirmAmount(request.getDeductionAmount());
        paymentConfirmCreateReq.setDescription(request.getDescription());

        List<PaymentConfirmDivMembersDto> divMembers = new ArrayList<>();
        PaymentConfirmDivMembersDto paymentConfirmDivMembersDto = new PaymentConfirmDivMembersDto();
        paymentConfirmDivMembersDto.setMemberId(OrderConst.PLATFORM_ADAPAY_ID);
        paymentConfirmDivMembersDto.setAmount(request.getDeductionAmount());
        paymentConfirmDivMembersDto.setFeeFlag(FeeFlagEnum.YES.getFlag());
        divMembers.add(paymentConfirmDivMembersDto);

        paymentConfirmCreateReq.setDivMembers(divMembers);
        return paymentConfirmCreateReq;
    }

    @Override
    public void applyRefund(ApplyRefundReq req) {
        AssertUtil.throwIfTrue("F".equals(financeApplyRefundSwitch), "供应商保证金申请退款开关已关闭，无法申请退款");
        Long id = req.getId();
        CashDepositDetail cashDepositDetail = cashDepositDetailRepository.getById(id);
        if (null == cashDepositDetail) {
            throw new BusinessException(FinanceResultCodeEnum.CASH_DEPOSIT_DETAIL_NOT_EXIST.getCode(), FinanceResultCodeEnum.CASH_DEPOSIT_DETAIL_NOT_EXIST.getMsg());
        }
        CashDeposit cashDeposit = cashDepositRepository.getById(cashDepositDetail.getCashDepositId());
        log.info("lws,,applyRefund,req.getShopId()={}, cashDeposit.getShopId()={}", req.getShopId(), cashDeposit.getShopId());
        if(req.getShopId() != null && !req.getShopId().equals(cashDeposit.getShopId())){
            throw new BusinessException("无权操作，这笔数据不是自己的数据");
        }
        // 判断余额是否足够：余额-冻结金额-退款金额-平台扣款金额
        BigDecimal refundAmount = cashDepositDetail.getBalance().subtract(cashDepositDetail.getForzenAmount())
                .subtract(cashDepositDetail.getRefundAmount()).subtract(cashDepositDetail.getPlatformDeduction());
        if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new BusinessException(FinanceResultCodeEnum.CASH_DEPOSIT_REFUND_AMOUNT_NOT_ENOUGH.getCode(), FinanceResultCodeEnum.CASH_DEPOSIT_REFUND_AMOUNT_NOT_ENOUGH.getMsg());
        }

        // 判断这个详情有没有申请过退款
        List<CashDepositRefund> refunds = cashDepositRefundRepository.selectCashDepositRefundByDetailIds(Arrays.asList(id));
        AssertUtil.throwIfTrue(!CollectionUtils.isEmpty(refunds), "这笔保证金明细已有退款记录");

        CashDepositRefund cashDepositRefund = new CashDepositRefund();
        cashDepositRefund.setApplyDate(new Date());
        cashDepositRefund.setShopId(cashDeposit.getShopId());
        cashDepositRefund.setBond(cashDepositDetail.getBalance());
        cashDepositRefund.setDeduction(cashDepositDetail.getPlatformDeduction());
        cashDepositRefund.setRefund(refundAmount);
        cashDepositRefund.setCashDepositDetailId(id);
        cashDepositRefund.setStatus(CashDepositRefundStatusEnum.TO_AUDIT.getStatus());

        cashDepositRefundRepository.save(cashDepositRefund);

        cashDepositDetail.setForzenAmount(cashDepositDetail.getForzenAmount().add(refundAmount));
        cashDepositDetailRepository.updateById(cashDepositDetail);
    }
}
