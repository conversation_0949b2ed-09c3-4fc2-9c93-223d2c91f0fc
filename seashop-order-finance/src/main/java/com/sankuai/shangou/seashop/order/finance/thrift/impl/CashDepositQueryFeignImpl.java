package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositService;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdListReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositListResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Slf4j
@RestController
@RequestMapping("/finance/cashDeposit")
public class CashDepositQueryFeignImpl implements CashDepositQueryFeign {

    @Resource
    private CashDepositService cashDepositService;

    @PostMapping(value = "/queryListByShopId", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<CashDepositResp>> queryListByShopId(@RequestBody ShopIdListReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryListByShopId", request, req -> {
            req.checkParameter();
            return cashDepositService.queryListByShopId(req);
        });
    }

    @PostMapping(value = "/queryOneByShopId", consumes = "application/json")
    @Override
    public ResultDto<CashDepositResp> queryOneByShopId(@RequestBody ShopIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("queryOneByShopId", request, req -> {
            req.checkParameter();
            return cashDepositService.queryOneByShopId(req.getShopId());
        });
    }

    @PostMapping(value = "/queryByShopIdList", consumes = "application/json")
    @Override
    public ResultDto<CashDepositListResp> queryByShopIdList(@RequestBody ShopIdListReq request) throws TException {
        log.info("【保证金查询】通过shopId列表查询保证金信息, 请求参数={}", JsonUtil.toJsonString(request));
        return ThriftResponseHelper.responseInvoke("queryByShopIdList", request, req -> {
            req.checkParameter();
            List<CashDepositResp> list = cashDepositService.queryByShopId(req);
            CashDepositListResp resp = new CashDepositListResp();
            resp.setList(list);
            return resp;
        });
    }
}
