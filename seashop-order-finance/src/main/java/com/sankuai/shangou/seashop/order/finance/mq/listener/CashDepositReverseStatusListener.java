package com.sankuai.shangou.seashop.order.finance.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.FinanceMafkaConst;
import com.sankuai.shangou.seashop.order.finance.mq.model.PayReverseResultModel;
import com.sankuai.shangou.seashop.order.finance.service.CommonService;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/18/018
 * @description:
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = FinanceMafkaConst.CASH_DEPOSIT_REVERSE_STATUS_CHANGE_TOPIC,
//        group = FinanceMafkaConst.CASH_DEPOSIT_REVERSE_STATUS_CHANGE_CONSUMER)
@RocketMQMessageListener(topic = FinanceMafkaConst.CASH_DEPOSIT_REVERSE_STATUS_CHANGE_TOPIC + "_${spring.profiles.active}"
        , consumerGroup = FinanceMafkaConst.CASH_DEPOSIT_REVERSE_STATUS_CHANGE_CONSUMER + "_${spring.profiles.active}")
public class CashDepositReverseStatusListener implements RocketMQListener<MessageExt> {

    @Resource
    private CommonService commonService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【撤销支付<退款>】消息内容为: {}", body);
        try {
            PayReverseResultModel payReverseResultModel = JsonUtil.parseObject(body, PayReverseResultModel.class);
            Integer businessType = payReverseResultModel.getBusinessType();
            // 只消费保证金的消息
            if (BusinessTypeEnum.BAIL.getType().equals(businessType)) {
                commonService.updateRefundStatus(payReverseResultModel.getRefundId(), payReverseResultModel.getPayStatus(), payReverseResultModel.getErrorMessage());
            } else {
                log.info("【mafka消费】【撤销支付<退款>】消息内容,不是保证金的消息，不消费.【body】:{}", body);
            }
        } catch (Exception e) {
            log.error("【mafka消费】【撤销支付<退款>】消息内容解析失败.", e);
            throw new RuntimeException(e);
        }
    }
}
