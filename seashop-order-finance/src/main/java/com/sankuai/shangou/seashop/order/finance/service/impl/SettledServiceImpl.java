package com.sankuai.shangou.seashop.order.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Account;
import com.sankuai.shangou.seashop.order.dao.finance.domain.AccountDetail;
import com.sankuai.shangou.seashop.order.dao.finance.domain.ShopAccountItem;
import com.sankuai.shangou.seashop.order.dao.finance.model.SettledItemQryModel;
import com.sankuai.shangou.seashop.order.dao.finance.model.SettledQryModel;
import com.sankuai.shangou.seashop.order.dao.finance.repository.AccountDetailRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.AccountRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.ShopAccountItemRepository;
import com.sankuai.shangou.seashop.order.finance.service.SettledService;
import com.sankuai.shangou.seashop.order.thrift.finance.request.OrderIdQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemCountQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettlementDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemCountResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledResp;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@Service
@Slf4j
public class SettledServiceImpl implements SettledService {

    @Resource
    private ShopAccountItemRepository shopAccountItemRepository;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private AccountRepository accountRepository;
    @Resource
    private AccountDetailRepository accountDetailRepository;

    /**
     * 查询已结算列表
     * <p>
     * 1、通过店铺名称模糊查询店铺id
     * 2、通过结算时间进行范围筛选
     *
     * @param request
     * @return
     */
    @Override
    public BasePageResp<SettledResp> pageList(SettledQryReq request) {

        List<ShopSimpleResp> shopSimpleRespList = new ArrayList<>();

        String shopName = request.getShopName();
        if (StrUtil.isNotBlank(shopName)) {
            ShopSimpleListResp shopSimpleListResp = shopRemoteService.querySimpleList(
                    ShopSimpleQueryReq.builder()
                            .shopName(shopName).build()
            );
            if (null == shopSimpleListResp || CollUtil.isEmpty(shopSimpleListResp.getList())) {
                return PageResultHelper.defaultEmpty(request.buildPage());
            }
            shopSimpleRespList = shopSimpleListResp.getList();
        }

        SettledQryModel qryModel = JsonUtil.copy(request, SettledQryModel.class);
        // 默认查询收入
        qryModel.setIncomeFlag(Boolean.TRUE);

        List<Long> shopIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(shopSimpleRespList)) {
            // 拿到过滤后的店铺id
            shopIds = shopSimpleRespList.stream().map(ShopSimpleResp::getId).collect(Collectors.toList());
            qryModel.setShopIdList(shopIds);
        }

        BasePageParam basePageParam = request.buildPage();

        Page<ShopAccountItem> shopAccountItemsPage = shopAccountItemRepository.pageList(basePageParam, qryModel);
        if (null == shopAccountItemsPage || CollUtil.isEmpty(shopAccountItemsPage.getResult())) {
            return PageResultHelper.defaultEmpty(request.buildPage());
        }

        List<ShopAccountItem> shopAccountItemList = shopAccountItemsPage.getResult();
        if (CollUtil.isEmpty(shopSimpleRespList)) {
            // 没数据说明之前没查过店铺信息，需要去查一下店铺信息
            shopIds = shopAccountItemList.stream().map(ShopAccountItem::getShopId).collect(Collectors.toList());
            ShopSimpleListResp shopSimpleListResp = shopRemoteService.querySimpleList(
                    ShopSimpleQueryReq.builder()
                            .shopIdList(shopIds).build()
            );
            shopSimpleRespList = shopSimpleListResp.getList();
        }
        final List<ShopSimpleResp> shopSimpleModelsFinal = shopSimpleRespList;

        List<String> detailIdList = shopAccountItemList.stream().map(ShopAccountItem::getDetailId).collect(Collectors.toList());
        final List<Account> accountList = accountRepository.listByIds(detailIdList);
        final List<AccountDetail> accountDetailList = accountDetailRepository.getByAccountIds(detailIdList);

        return PageResultHelper.transfer(shopAccountItemsPage, SettledResp.class, (shopAccountItem, settledResp) -> {
            Account account = accountList.stream()
                    .filter(a -> a.getId().toString().equals(shopAccountItem.getDetailId())).findFirst().orElse(null);
            // 解释三目运算最后一个时间：取创建时间，减去结算周期，并去一天的开始时间（即时间为等于00:00:00）-结算是凌晨执行
            Date startCycleTime = null != account ? account.getStartDate() : DateUtil.beginOfDay(DateUtil.offsetDay(shopAccountItem.getCreateTime(), -shopAccountItem.getSettlementCycle()));
            // 解释三目运算最后一个时间：取创建时间的开始时间（即时间为等于00:00:00）-结算是凌晨执行
            Date endCycleTime = null != account ? account.getEndDate() : DateUtil.beginOfDay(shopAccountItem.getCreateTime());

            settledResp.setStartCycleTime(startCycleTime);
            settledResp.setEndCycleTime(endCycleTime);

            settledResp.setSettleAmount(shopAccountItem.getAmount());
            settledResp.setSettleTime(shopAccountItem.getCreateTime());

            // 金额处理
            List<AccountDetail> accountDetails = accountDetailList.stream()
                    .filter(a -> a.getAccountId().toString().equals(shopAccountItem.getDetailId()) && a.getShopId().equals(shopAccountItem.getShopId())).collect(Collectors.toList());
            settledResp.setOrderAmount(accountDetails.stream().map(AccountDetail::getOrderAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            settledResp.setRefundAmount(accountDetails.stream().map(AccountDetail::getRefundTotalAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            settledResp.setCommissionAmount(accountDetails.stream().map(AccountDetail::getCommissionAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
            settledResp.setChannelAmount(accountDetails.stream().map(AccountDetail::getChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add));

            if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
                shopSimpleModelsFinal.stream().filter(s -> s.getId().equals(shopAccountItem.getShopId())).findFirst().ifPresent(shopSimpleResp -> {
                    settledResp.setShopName(shopSimpleResp.getShopName());
                });
            }
        });
    }

    /**
     * 查询已结算明细列表
     * <p>
     * 1、通过各个时间范围进行筛选
     *
     * @param request
     * @return
     */
    @Override
    public BasePageResp<SettledItemResp> itemPageList(SettledItemQryReq request) {

        BasePageParam basePageParam = request.buildPage();
        SettledItemQryModel settledItemQryModel = JsonUtil.copy(request, SettledItemQryModel.class);
        Page<AccountDetail> accountDetailsPage = accountDetailRepository.itemPageList(basePageParam, settledItemQryModel);
        if (null == accountDetailsPage || CollUtil.isEmpty(accountDetailsPage.getResult())) {
            return PageResultHelper.defaultEmpty(request.buildPage());
        }

        List<AccountDetail> accountDetailList = accountDetailsPage.getResult();
        List<Long> shopIds = accountDetailList.stream().map(AccountDetail::getShopId).collect(Collectors.toList());
        // 查询店铺信息
        ShopSimpleListResp shopSimpleListResp = shopRemoteService.querySimpleList(
                ShopSimpleQueryReq.builder()
                        .shopIdList(shopIds).build()
        );
        final List<ShopSimpleResp> shopSimpleModelsFinal = null != shopSimpleListResp ? shopSimpleListResp.getList() : new ArrayList<>();


        return PageResultHelper.transfer(accountDetailsPage, SettledItemResp.class, (accountDetail, settledItemResp) -> {
            settledItemResp.setOrderId(accountDetail.getOrderId());
            settledItemResp.setShopId(accountDetail.getShopId());
            if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
                shopSimpleModelsFinal.stream().filter(s -> s.getId().equals(accountDetail.getShopId())).findFirst().ifPresent(shopSimpleResp -> {
                    settledItemResp.setShopName(shopSimpleResp.getShopName());
                });
            }
            settledItemResp.setSettleTime(accountDetail.getDate());
            settledItemResp.setSettleAmount(accountDetail.getSettlementAmount());
            settledItemResp.setChannelAmount(accountDetail.getChannelAmount());
            settledItemResp.setOrderAmount(accountDetail.getOrderAmount());
            settledItemResp.setCommissionAmount(accountDetail.getCommissionAmount());
            settledItemResp.setRefundAmount(accountDetail.getRefundTotalAmount());
            settledItemResp.setPayTime(accountDetail.getPayDate());
            settledItemResp.setOrderFinishTime(accountDetail.getOrderFinishDate());
            settledItemResp.setPaymentType(accountDetail.getPaymentType());
            PaymentTypeEnum paymentTypeEnum = PaymentTypeEnum.getByType(accountDetail.getPaymentType());
            settledItemResp.setPaymentTypeName(null != paymentTypeEnum ? paymentTypeEnum.getName() : "");
        });
    }

    @Override
    public SettledItemCountResp itemCount(SettledItemCountQryReq request) {
        SettledItemCountResp settledItemCountResp = new SettledItemCountResp();
        settledItemCountResp.setDetailId(request.getDetailId());
        settledItemCountResp.setShopId(request.getShopId());
        settledItemCountResp.setSettleAmount(BigDecimal.ZERO);
        settledItemCountResp.setCommissionAmount(BigDecimal.ZERO);

        if (request.getDetailId() == null) {
            return settledItemCountResp;
        }

        /*AccountDetailCountModel accountDetailCountModel = accountDetailRepository.detailCount(request.getShopId(), request.getDetailId());
        if (null != accountDetailCountModel) {
            settledItemCountResp.setSettleAmount(accountDetailCountModel.getSettleAmount());
            settledItemCountResp.setCommissionAmount(accountDetailCountModel.getCommissionAmount());
        }*/

        List<AccountDetail> queryList = accountDetailRepository.lambdaQuery()
                .eq(AccountDetail::getAccountId, request.getDetailId())
                .eq(request.getShopId() != null, AccountDetail::getShopId, request.getShopId())
                .like(StringUtils.isNotBlank(request.getShopName()), AccountDetail::getShopName, request.getShopName())
                .list();

        queryList.forEach(accountDetail -> {
            settledItemCountResp.setSettleAmount(settledItemCountResp.getSettleAmount().add(accountDetail.getSettlementAmount()));
            settledItemCountResp.setCommissionAmount(settledItemCountResp.getCommissionAmount().add(accountDetail.getCommissionAmount()));
        });
        return settledItemCountResp;
    }

    /**
     * 通过订单ID进行明细查询
     *
     * @param request
     * @return
     */
    @Override
    public SettlementDetailResp getDetailByOrderId(OrderIdQryReq request) {
        AccountDetail accountDetail = accountDetailRepository.getByOrderId(request.getOrderId());
        if (null != accountDetail) {
            SettlementDetailResp settlementDetailResp = new SettlementDetailResp();
            settlementDetailResp.setOrderAmount(accountDetail.getOrderAmount());
            settlementDetailResp.setChannelAmount(accountDetail.getChannelAmount());
            settlementDetailResp.setCommissionAmount(accountDetail.getCommissionAmount());
            settlementDetailResp.setPayTime(accountDetail.getPayDate());
            return settlementDetailResp;
        }
        return null;
    }
}
