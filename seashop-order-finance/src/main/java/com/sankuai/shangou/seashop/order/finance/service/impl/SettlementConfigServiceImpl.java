package com.sankuai.shangou.seashop.order.finance.service.impl;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.SquirrelUtil;
import com.sankuai.shangou.seashop.order.common.constant.CacheConst;
import com.sankuai.shangou.seashop.order.common.constant.FinanceSquirrelConst;
import com.sankuai.shangou.seashop.order.dao.finance.domain.SettlementConfig;
import com.sankuai.shangou.seashop.order.dao.finance.repository.SettlementConfigRepository;
import com.sankuai.shangou.seashop.order.finance.service.SettlementConfigService;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettlementConfigReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettlementConfigResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Service
@Slf4j
public class SettlementConfigServiceImpl implements SettlementConfigService {

    @Resource
    private SettlementConfigRepository settlementConfigRepository;
    @Resource
    private SquirrelUtil squirrelUtil;

    @Override
    public SettlementConfigResp getConfig() {
        Object obj = squirrelUtil.get(FinanceSquirrelConst.SETTLEMENT_CONFIG_KEY);
        if (obj != null) {
            return JsonUtil.copy(obj, SettlementConfigResp.class);
        }
        SettlementConfig config = settlementConfigRepository.getConfig();
        squirrelUtil.set(FinanceSquirrelConst.SETTLEMENT_CONFIG_KEY, config, CacheConst.HOUR_EXPIRE);
        return JsonUtil.copy(config, SettlementConfigResp.class);
    }

    @Override
    public void update(SettlementConfigReq request) {
        SettlementConfig config = settlementConfigRepository.getConfig();
        config.setSettlementInterval(request.getSettlementInterval());
        config.setSettlementFeeRate(request.getSettlementFeeRate());
        config.setWxFeeRate(request.getWxFeeRate());
        settlementConfigRepository.updateById(config);
        // 缓存一天
        squirrelUtil.set(FinanceSquirrelConst.SETTLEMENT_CONFIG_KEY, config, CacheConst.EXPIRE_SETTLE_CONFIG_CACHE);
    }
}
