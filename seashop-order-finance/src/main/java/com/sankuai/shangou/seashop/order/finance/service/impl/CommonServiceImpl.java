package com.sankuai.shangou.seashop.order.finance.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSON;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.constant.OrderConst;
import com.sankuai.shangou.seashop.order.thrift.core.enums.finance.CashDepositOperatorTypeEnum;
import com.sankuai.shangou.seashop.order.common.enums.FinanceResultCodeEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDeposit;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositDetail;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositRefund;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Finance;
import com.sankuai.shangou.seashop.order.dao.finance.repository.CashDepositDetailRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.CashDepositRefundRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.CashDepositRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceRepository;
import com.sankuai.shangou.seashop.order.finance.service.CommonService;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.CashDepositRefundStatusEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.ReverseStateEnums;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/19/019
 * @description:
 */
@Service
@Slf4j
public class CommonServiceImpl implements CommonService {

    @Resource
    private CashDepositRefundRepository cashDepositRefundRepository;
    @Resource
    private CashDepositDetailRepository cashDepositDetailRepository;
    @Resource
    private CashDepositRepository cashDepositRepository;
    @Resource
    private FinanceRepository financeRepository;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    @Lazy
    private CommonService commonService;

    /**
     * 更新退款状态
     *
     * @param refundOrderId 退款订单号
     * @param refundStatus  退款状态
     * @param reason        原因
     */
    @Override
    public void updateRefundStatus(String refundOrderId, Integer refundStatus, String reason) {
        log.info("updateRefundStatus更新退款状态，请求参数：refundOrderId：{}，refundStatus：{}", refundOrderId, refundStatus);
        CashDepositRefund cashDepositRefund = cashDepositRefundRepository.queryByRefundOrderId(refundOrderId);
        log.info("updateRefundStatus更新退款状态，查询到的退款申请单：{}", JsonUtil.toJsonString(cashDepositRefund));
        if (cashDepositRefund == null || !cashDepositRefund.getStatus().equals(CashDepositRefundStatusEnum.REFUNDING.getStatus())) {
            log.info("updateRefundStatus更新退款状态，退款申请单不存在或者状态不是退款中，不做处理");
            return;
        }

        ShopDetailResp shopDetailResp = shopRemoteService.queryDetail(cashDepositRefund.getShopId());
        Long cashDepositDetailId = cashDepositRefund.getCashDepositDetailId();
        CashDepositDetail cashDepositDetail = cashDepositDetailRepository.getById(cashDepositDetailId);

        Date now = new Date();

        // 退款成功逻辑
        if (ReverseStateEnums.REVERSE_SUCCESS.getStatus().equals(refundStatus)) {

            cashDepositRefund.setStatus(CashDepositRefundStatusEnum.PASS.getStatus());
            TransactionHelper.doInTransaction(() -> {
                cashDepositRefundRepository.updateById(cashDepositRefund);

                CashDepositDetail addCashDepositDetail = new CashDepositDetail();
                addCashDepositDetail.setAddDate(now);
                addCashDepositDetail.setBalance(cashDepositRefund.getRefund().negate());
                addCashDepositDetail.setCashDepositId(cashDepositDetail.getCashDepositId());
                addCashDepositDetail.setDescription(OrderConst.CASH_DEPOSIT_REFUND_SUCCESS_DESC + cashDepositDetail.getChannelOrderId());
                addCashDepositDetail.setOperator(StrUtil.isNotBlank(cashDepositRefund.getRemark()) ? cashDepositRefund.getRemark() : "");
                addCashDepositDetail.setOperatorType(CashDepositOperatorTypeEnum.REFUND.getType());

                commonService.addCashDepositDetails(addCashDepositDetail, cashDepositDetailId, Boolean.TRUE);

                Finance finance = new Finance();
                finance.setOrderId(cashDepositDetail.getChannelOrderId());
                finance.setAdapayId(cashDepositDetail.getChannelId());
                finance.setPayId(cashDepositDetail.getChannelOrderId());
                finance.setType(TransactionTypesEnum.REFUND.getCode());
                finance.setCreateDate(now);
                finance.setShopId(cashDepositRefund.getShopId());
                finance.setShopName(null != shopDetailResp ? shopDetailResp.getShopName() : "");
                finance.setUserId(CommonConst.USER_ID_DEFAULT_ZERO);
                finance.setUserName("");
                finance.setTransactionId(StrUtil.isNotBlank(cashDepositDetail.getTradeNo()) ? cashDepositDetail.getTradeNo() : "");
                finance.setTotalAmount(cashDepositDetail.getRefundAmount());

                financeRepository.save(finance);
            });

            //通知重算是否欠费
            shopRemoteService.checkShopArrears(cashDepositRefund.getShopId());
            return;
        }

        // 退款失败逻辑
        if (ReverseStateEnums.REVERSE_ERROR.getStatus().equals(refundStatus)) {
            cashDepositRefund.setStatus(CashDepositRefundStatusEnum.FAIL.getStatus());
            cashDepositRefund.setRemark(reason);
            TransactionHelper.doInTransaction(() -> {
                cashDepositRefundRepository.updateById(cashDepositRefund);

                CashDepositDetail addCashDepositDetail = new CashDepositDetail();
                addCashDepositDetail.setAddDate(now);
                addCashDepositDetail.setBalance(cashDepositRefund.getRefund().negate());
                addCashDepositDetail.setCashDepositId(cashDepositDetail.getCashDepositId());
                addCashDepositDetail.setDescription(OrderConst.CASH_DEPOSIT_REFUND_FAIL_DESC + reason);
                addCashDepositDetail.setOperator(cashDepositRefund.getRemark());
                addCashDepositDetail.setOperatorType(CashDepositOperatorTypeEnum.REFUND.getType());
                cashDepositDetailRepository.save(addCashDepositDetail);
            });

            return;
        }
        log.info("updateRefundStatus更新退款状态，退款状态不是成功或者失败，不做处理");
    }

    @Override
    public void addCashDepositDetails(CashDepositDetail addCashDepositDetail, Long cashDepositDetailId, Boolean frozenFlag) {
        addCashDepositDetails(addCashDepositDetail, cashDepositDetailId, frozenFlag, Boolean.TRUE);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void addCashDepositDetails(CashDepositDetail addCashDepositDetail, Long cashDepositDetailId, Boolean frozenFlag, Boolean refundFlag) {
        cashDepositDetailRepository.save(addCashDepositDetail);

        CashDeposit cashDeposit = cashDepositRepository.getById(addCashDepositDetail.getCashDepositId());
        if (addCashDepositDetail.getBalance().compareTo(BigDecimal.ZERO) < 0
                && cashDeposit.getCurrentBalance().add(addCashDepositDetail.getBalance()).compareTo(BigDecimal.ZERO) < 0) {
            throw new BusinessException(FinanceResultCodeEnum.CASH_DEPOSIT_DEDUCT_AMOUNT_NOT_ENOUGH.getCode(),
                    FinanceResultCodeEnum.CASH_DEPOSIT_DEDUCT_AMOUNT_NOT_ENOUGH.getMsg());
        }

        cashDeposit.setCurrentBalance(cashDeposit.getCurrentBalance().add(addCashDepositDetail.getBalance()));
        if (addCashDepositDetail.getBalance().compareTo(BigDecimal.ZERO) > 0) {
            cashDeposit.setEnableLabels(Boolean.TRUE);
            cashDeposit.setTotalBalance(cashDeposit.getTotalBalance().add(addCashDepositDetail.getBalance()));
            cashDeposit.setDate(new Date());
        }
        cashDepositRepository.updateById(cashDeposit);

        CashDepositDetail cashDepositDetail = cashDepositDetailRepository.getById(cashDepositDetailId);
        log.info("lws,,CommonServiceImpl.cashDepositDetail1={}", JsonUtil.toJsonString(cashDepositDetail));
        if (null != refundFlag && refundFlag) {
            cashDepositDetail.setRefundAmount(cashDepositDetail.getRefundAmount().add(addCashDepositDetail.getBalance().abs()));
        } else {
            cashDepositDetail.setPlatformDeduction(cashDepositDetail.getPlatformDeduction().add(addCashDepositDetail.getBalance().abs()));
        }
        if (null != frozenFlag && frozenFlag) {
            cashDepositDetail.setForzenAmount(cashDepositDetail.getForzenAmount().subtract(addCashDepositDetail.getBalance().abs()));
        }
        log.info("lws,,CommonServiceImpl.addCashDepositDetail={}", JsonUtil.toJsonString(addCashDepositDetail));
        log.info("lws,,CommonServiceImpl.cashDepositDetail2={}", JsonUtil.toJsonString(cashDepositDetail));
        cashDepositDetailRepository.updateById(cashDepositDetail);
    }
}
