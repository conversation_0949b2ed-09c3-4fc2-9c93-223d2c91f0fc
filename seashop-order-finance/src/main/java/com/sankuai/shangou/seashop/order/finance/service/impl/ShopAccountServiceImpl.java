package com.sankuai.shangou.seashop.order.finance.service.impl;

import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.order.dao.finance.domain.ShopAccount;
import com.sankuai.shangou.seashop.order.dao.finance.repository.ShopAccountRepository;
import com.sankuai.shangou.seashop.order.finance.service.ShopAccountService;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreateAccountReq;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Service
@Slf4j
public class ShopAccountServiceImpl implements ShopAccountService {
    @Resource
    private ShopAccountRepository shopAccountRepository;

    @Override
    public BaseResp createAccount(CreateAccountReq createAccountReq) {
        // 设置账户信息
        ShopAccount shopAccount = shopAccountRepository.lambdaQuery().eq(ShopAccount::getShopId, createAccountReq.getShopId()).one();
        if (shopAccount == null) {
            shopAccount = new ShopAccount();
            shopAccount.setShopId(createAccountReq.getShopId());
            shopAccount.setPendingSettlement(BigDecimal.ZERO);
            shopAccount.setSettled(BigDecimal.ZERO);
            shopAccount.setRemark(StrUtil.EMPTY);
            shopAccount.setShopName(createAccountReq.getShopName());
            shopAccount.setSettleAccountId(createAccountReq.getAccountId());
            shopAccountRepository.save(shopAccount);
            return BaseResp.of();
        }
        shopAccount.setShopName(createAccountReq.getShopName());
        shopAccount.setSettleAccountId(createAccountReq.getAccountId());
        // 保存账户信息
        shopAccountRepository.updateById(shopAccount);
        return BaseResp.of();
    }

    @Override
    public String getShopAccountByShopId(Long req) {
        ShopAccount shopAccount = shopAccountRepository.lambdaQuery().eq(ShopAccount::getShopId, req).one();
        return shopAccount==null?"":shopAccount.getSettleAccountId();
    }
}
