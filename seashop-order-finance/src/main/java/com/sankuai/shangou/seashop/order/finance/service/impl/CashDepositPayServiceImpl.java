package com.sankuai.shangou.seashop.order.finance.service.impl;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.constant.OrderConst;
import com.sankuai.shangou.seashop.order.thrift.core.enums.finance.CashDepositOperatorTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDeposit;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositDetail;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositPay;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Finance;
import com.sankuai.shangou.seashop.order.dao.finance.repository.CashDepositDetailRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.CashDepositPayRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.CashDepositRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceRepository;
import com.sankuai.shangou.seashop.order.finance.mq.model.PaymentResultModel;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositPayService;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositPayReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositPayResp;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@Service
@Slf4j
public class CashDepositPayServiceImpl implements CashDepositPayService {

    @Resource
    private CashDepositPayRepository cashDepositPayRepository;
    @Resource
    private CashDepositRepository cashDepositRepository;
    @Resource
    private CashDepositDetailRepository cashDepositDetailRepository;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private FinanceRepository financeRepository;

    @Override
    public void updateStatus(PaymentResultModel model) {
        log.info("updateStatus修改保证金支付状态.model:{}", JSONUtil.toJsonStr(model));
        Integer payStatus = model.getPayStatus();
        String orderId = model.getOrderId();
        CashDepositPay cashDepositPay = cashDepositPayRepository.getByPayId(orderId);
        if (cashDepositPay == null) {
            log.info("【保证金支付状态】不存在，不再处理.【cashDepositPay】:{}", JSONUtil.toJsonStr(cashDepositPay));
            return;
        }
        // 先判断状态是否已经处理过
        if (!PayStateEnums.UNPAID.getStatus().equals(cashDepositPay.getPayStatus())) {
            log.info("【保证金支付状态】已经处理过，不再处理.【cashDepositPay】:{}", JSONUtil.toJsonStr(cashDepositPay));
            return;
        }

        // 如果支付失败，简单处理
        if (PayStateEnums.PAY_FAILED.getStatus().equals(payStatus)) {
            cashDepositPay.setPayStatus(payStatus);
            cashDepositPayRepository.updateById(cashDepositPay);
            log.info("【保证金支付状态】支付失败，修改保证金支付状态.【cashDepositPay】:{}", JSONUtil.toJsonStr(cashDepositPay));
            return;
        }

        // orderId截取从20位开始的字符串,得到shopId
        Long shopId = Long.valueOf(orderId.substring(OrderConst.CASH_DEPOSIT_PAY_ORDER_ID_PREFIX_LENGTH));

        // 如果支付成功,需要处理逻辑
        if (PayStateEnums.PAID.getStatus().equals(payStatus)) {

            // 用户店铺信息
            ShopDetailResp shopDetailResp = shopRemoteService.queryDetail(shopId);

            TransactionHelper.doInTransaction(
                    () -> {
                        // 修改保证金支付状态
                        cashDepositPay.setPayStatus(payStatus);
                        cashDepositPay.setPayDate(model.getPayTime());
                        cashDepositPayRepository.updateById(cashDepositPay);

                        // 查询保证金信息
                        CashDeposit cashDeposit = cashDepositRepository.queryOneByShopId(shopId);
                        if (cashDeposit == null) {
                            // 如果不存在，初始化一个
                            cashDeposit = new CashDeposit();
                            cashDeposit.setShopId(shopId);
                            cashDeposit.setCurrentBalance(model.getPayAmount());
                            cashDeposit.setTotalBalance(model.getPayAmount());

                        } else {
                            cashDeposit.setCurrentBalance(cashDeposit.getCurrentBalance().add(model.getPayAmount()));
                            cashDeposit.setTotalBalance(cashDeposit.getTotalBalance().add(model.getPayAmount()));
                        }
                        cashDeposit.setDate(model.getPayTime());
                        cashDeposit.setEnableLabels(Boolean.TRUE);
                        log.info("lws,,cashDeposit = {}", JSONUtil.toJsonStr(cashDeposit));
                        cashDepositRepository.saveOrUpdate(cashDeposit);

                        CashDepositDetail cashDepositDetail = new CashDepositDetail();
                        cashDepositDetail.setAddDate(model.getPayTime());
                        cashDepositDetail.setBalance(model.getPayAmount());
                        cashDepositDetail.setDescription(OrderConst.CASH_DEPOSIT_PAY_GOODS_DESC);
                        cashDepositDetail.setOperator(null != shopDetailResp ? shopDetailResp.getShopName() : "");
                        cashDepositDetail.setOperatorType(CashDepositOperatorTypeEnum.PAY.getType());
                        cashDepositDetail.setChannelOrderId(orderId);
                        cashDepositDetail.setTradeNo(model.getOutTransId());
                        cashDepositDetail.setChannelId(cashDepositPay.getAdapayId());
                        cashDepositDetail.setCashDepositId(cashDeposit.getId());
                        log.info("lws,,cashDepositDetail = {}", JSONUtil.toJsonStr(cashDepositDetail));
                        cashDepositDetailRepository.save(cashDepositDetail);

                        // 财务中间表处理
                        Finance finance = new Finance();
                        finance.setOrderId(orderId);
                        finance.setAdapayId(cashDepositPay.getAdapayId());
                        finance.setPayId(orderId);
                        finance.setType(TransactionTypesEnum.PAY.getCode());
                        finance.setShopId(shopId);
                        finance.setShopName(null != shopDetailResp ? shopDetailResp.getShopName() : "");
                        finance.setTransactionId(model.getOutTransId());
                        finance.setTotalAmount(model.getPayAmount());
                        finance.setActualPayAmount(model.getPayAmount());
                        financeRepository.save(finance);
                    }
            );
            //通知店铺重新计算欠费
            shopRemoteService.checkShopArrears(shopId);
        }
    }

    @Override
    public List<CashDepositPayResp> selectCashDepositPay(CashDepositPayReq req) {
        List<CashDepositPay> payList = cashDepositPayRepository.queryByPayIdList(req.getPayIdList());
        return JsonUtil.copyList(payList, CashDepositPayResp.class);
    }
}
