package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositRefundService;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositRefundQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundResp;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description:
 */
@RestController
@RequestMapping("/finance/cashDepositRefund")
public class CashDepositRefundQueryFeignImpl implements CashDepositRefundQueryFeign {

    @Resource
    private CashDepositRefundService cashDepositRefundService;

    @PostMapping(value = "/refundList", consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<CashDepositRefundResp>> refundList(@RequestBody CashDepositRefundQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("refundList", request, req ->
                cashDepositRefundService.refundList(req)
        );
    }

    @RequestMapping("/refundDetail")
    @Override
    public ResultDto<CashDepositRefundDetailResp> refundDetail(@RequestParam Long id) throws TException {
        return ThriftResponseHelper.responseInvoke("refundDetail", id, req ->
                cashDepositRefundService.refundDetail(req)
        );
    }
}
