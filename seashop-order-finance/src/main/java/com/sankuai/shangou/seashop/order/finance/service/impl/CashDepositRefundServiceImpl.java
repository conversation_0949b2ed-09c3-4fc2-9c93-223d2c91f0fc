package com.sankuai.shangou.seashop.order.finance.service.impl;

import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.log.assist.BaseLogAssist;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaProEnum;
import com.sankuai.shangou.seashop.base.boot.log.enums.ExaminModelEnum;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.constant.CommonConst;
import com.sankuai.shangou.seashop.order.common.constant.FinanceLeafConst;
import com.sankuai.shangou.seashop.order.common.constant.OrderConst;
import com.sankuai.shangou.seashop.order.common.enums.FeeFlagEnum;
import com.sankuai.shangou.seashop.order.common.enums.FinanceResultCodeEnum;
import com.sankuai.shangou.seashop.order.common.remote.PayRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.dao.finance.domain.*;
import com.sankuai.shangou.seashop.order.dao.finance.model.CashDepositRefundQueryModel;
import com.sankuai.shangou.seashop.order.dao.finance.repository.*;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositRefundService;
import com.sankuai.shangou.seashop.order.thrift.core.enums.CashDepositPayTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.finance.CashDepositOperatorTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.CashDepositRefundStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundConfirmReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundRefuseLog;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositRefundRefuseReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositRefundResp;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayReverseCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaymentConfirmCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.PaymentConfirmDivMembersDto;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayReverseCreateResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PaymentConfirmCreateResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/29/029
 * @description:
 */
@Service
@Slf4j
public class CashDepositRefundServiceImpl implements CashDepositRefundService {

    @Resource
    private CashDepositRefundRepository cashDepositRefundRepository;
    @Resource
    private CashDepositDetailRepository cashDepositDetailRepository;
    @Resource
    private CashDepositRepository cashDepositRepository;
    @Resource
    private CashDepositPayRepository cashDepositPayRepository;
    @Resource
    private PayRemoteService payRemoteService;
    @Resource
    private LeafService leafService;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private FinanceRepository financeRepository;
    @Resource
    private BaseLogAssist baseLogAssist;

    /**
     * 平台保证金退款审批拒绝
     */
    @Value("${finance.refund.refuse.switch}")
    private String financeRefundRefuseSwitch;

    /**
     * 平台保证金退款审批通过
     */
    @Value("${finance.refund.confirm.switch}")
    private String financeRefundConfirmSwitch;

    @Override
    public BasePageResp<CashDepositRefundResp> refundList(CashDepositRefundQueryReq request) {
        BasePageResp<CashDepositRefund> cashDepositRefundModelPage = cashDepositRefundRepository.pageList(request.buildPage(), JsonUtil.copy(request, CashDepositRefundQueryModel.class));
        return PageResultHelper.transfer(cashDepositRefundModelPage, CashDepositRefundResp.class);
    }

    @Override
    public CashDepositRefundDetailResp refundDetail(Long id) {
        return JsonUtil.copy(cashDepositRefundRepository.getById(id), CashDepositRefundDetailResp.class);
    }

    @Override
    public void refuse(CashDepositRefundRefuseReq request) {
        AssertUtil.throwIfTrue("F".equals(financeRefundRefuseSwitch), "平台保证金退款拒绝审批开关已关闭，无法发起拒绝审批");
        Long id = request.getId();
        CashDepositRefund cashDepositRefund = getCashDepositRefund(id);

        // 退款对应的保证金明细
        Long cashDepositDetailId = cashDepositRefund.getCashDepositDetailId();
        CashDepositDetail cashDepositDetail = cashDepositDetailRepository.getById(cashDepositDetailId);

        ShopDetailResp shopDetailResp = shopRemoteService.queryDetail(cashDepositRefund.getShopId());
        CashDepositRefundRefuseLog oldValue = new CashDepositRefundRefuseLog();
        oldValue.setId(id);
        oldValue.setStatus(cashDepositRefund.getStatus());
        oldValue.setShopName(shopDetailResp.getShopName());
        CashDepositRefundRefuseLog newValue = new CashDepositRefundRefuseLog();
        newValue.setId(id);
        newValue.setStatus(CashDepositRefundStatusEnum.REFUSE.getStatus());
        newValue.setRefuseReason(request.getRefuseReason());
        newValue.setShopName(shopDetailResp.getShopName());
        TransactionHelper.doInTransaction(
                () -> {

                    // 手动写日志
                    baseLogAssist.recordLog(ExaminModelEnum.ORDER, ExaProEnum.MODIFY,"保证金退款审批拒绝",
                            request.getOperationUserId(), request.getOperationShopId(),
                            oldValue, newValue);

                    // 退款申请单设置成拒绝
                    cashDepositRefund.setStatus(CashDepositRefundStatusEnum.REFUSE.getStatus());
                    cashDepositRefund.setRemark(request.getRefuseReason());
                    cashDepositRefundRepository.updateById(cashDepositRefund);

                    // 返回冻结金额
                    cashDepositDetail.setForzenAmount(cashDepositDetail.getForzenAmount().subtract(cashDepositRefund.getRefund()));
                    cashDepositDetailRepository.updateById(cashDepositDetail);

                    // 新增一条退款明细记录
                    CashDepositDetail newCashDepositDetail = new CashDepositDetail();
                    newCashDepositDetail.setAddDate(new Date());
                    newCashDepositDetail.setBalance(cashDepositRefund.getRefund().negate());
                    newCashDepositDetail.setOperatorType(CashDepositOperatorTypeEnum.REFUND.getType());
                    newCashDepositDetail.setDescription(OrderConst.CASH_DEPOSIT_REFUND_FAIL_DESC + request.getRefuseReason());
                    newCashDepositDetail.setOperator(request.getOperator());
                    newCashDepositDetail.setCashDepositId(cashDepositDetail.getCashDepositId());
                    cashDepositDetailRepository.save(newCashDepositDetail);
                }
        );
    }

    @Override
    public void confirm(CashDepositRefundConfirmReq request) {
        AssertUtil.throwIfTrue("F".equals(financeRefundConfirmSwitch), "平台保证金退款通过审批开关已关闭，无法发起通过审批");
        log.info("confirm退款逻辑中，请求参数：{}", JsonUtil.toJsonString(request));

        Long id = request.getId();
        // 退款申请单
        CashDepositRefund cashDepositRefund = getCashDepositRefund(id);

        // 退款对应的保证金明细
        Long cashDepositDetailId = cashDepositRefund.getCashDepositDetailId();
        CashDepositDetail cashDepositDetail = cashDepositDetailRepository.getById(cashDepositDetailId);

        /*
         * 判断保证金支付时间
         * 小于178天，未扣款，调用支付撤销接口
         * 小于178天，已扣款，调用退款接口
         * 大于178天，调用分账接口
         *
         * .net代码逻辑
         * 支付宝：178
         * 工商银行、广发银行和浦发银行：90
         * 其他银行：360
         */
        String orderNo = FinanceLeafConst.CASH_DEPOSIT_DEDUCTION_ORDER_ID_PREFIX + leafService.generateNoBySnowFlake(FinanceLeafConst.CASH_DEPOSIT_DEDUCTION_ORDER_ID);
        ShopDetailResp shopDetailResp = shopRemoteService.queryDetail(cashDepositRefund.getShopId());

        CashDepositRefundRefuseLog oldValue = new CashDepositRefundRefuseLog();
        oldValue.setId(id);
        oldValue.setStatus(cashDepositRefund.getStatus());
        oldValue.setShopName(shopDetailResp.getShopName());
        CashDepositRefundRefuseLog newValue = new CashDepositRefundRefuseLog();
        newValue.setId(id);
        newValue.setStatus(CashDepositRefundStatusEnum.PASS.getStatus());
        newValue.setRefundOrderId(orderNo);
        newValue.setShopName(shopDetailResp.getShopName());

        // 保证金充值时间距离现在的天数
        Long betweenDay = DateUtil.between(new Date(), cashDepositDetail.getAddDate(), DateUnit.DAY) + 1;
        CashDepositPay cashDepositPay = cashDepositPayRepository.getByPayId(cashDepositDetail.getChannelOrderId());
        Integer refundDay = CashDepositPayTypeEnum.getRefundDay(cashDepositPay.getPayment());
        if (betweenDay > refundDay) {
            //调用分账接口
            CashDeposit cashDeposit = cashDepositRepository.getById(cashDepositDetail.getCashDepositId());

            PaymentConfirmCreateReq paymentConfirmCreateReq = new PaymentConfirmCreateReq();
            paymentConfirmCreateReq.setOrderNo(orderNo);
            paymentConfirmCreateReq.setSourceOrderId(cashDepositDetail.getChannelOrderId());
            paymentConfirmCreateReq.setConfirmAmount(cashDepositRefund.getRefund());

            List<PaymentConfirmDivMembersDto> divMembers = new ArrayList<>(1);
            PaymentConfirmDivMembersDto paymentConfirmDivMembersDto = new PaymentConfirmDivMembersDto();
            paymentConfirmDivMembersDto.setMemberId(cashDeposit.getShopId().toString());
            paymentConfirmDivMembersDto.setAmount(cashDepositRefund.getRefund());
            paymentConfirmDivMembersDto.setFeeFlag(FeeFlagEnum.YES.getFlag());
            divMembers.add(paymentConfirmDivMembersDto);

            paymentConfirmCreateReq.setDivMembers(divMembers);

            log.info("confirm退款逻辑中调用分账接口，请求参数：{}", JsonUtil.toJsonString(paymentConfirmCreateReq));
            PaymentConfirmCreateResp paymentConfirmCreateResp = ThriftResponseHelper.executeThriftCall(() -> payRemoteService.createPaymentConfirm(paymentConfirmCreateReq));
            log.info("confirm退款逻辑中调用分账接口，响应参数：{}", JsonUtil.toJsonString(paymentConfirmCreateResp));

            try {
                TransactionHelper.doInTransaction(() -> {
                    // 修改状态
                    cashDepositRefund.setStatus(CashDepositRefundStatusEnum.PASS.getStatus());
                    cashDepositRefund.setRefundOrderId(orderNo);
                    cashDepositRefundRepository.updateById(cashDepositRefund);

                    // 新增一条退款明细记录
                    CashDepositDetail newCashDepositDetail = new CashDepositDetail();
                    newCashDepositDetail.setAddDate(new Date());
                    newCashDepositDetail.setBalance(cashDepositRefund.getRefund().negate());
                    newCashDepositDetail.setDescription(OrderConst.CASH_DEPOSIT_REFUND_SUCCESS_DESC + cashDepositDetail.getChannelOrderId());
                    newCashDepositDetail.setCashDepositId(cashDepositDetail.getCashDepositId());
                    newCashDepositDetail.setOperator(request.getOperator());
                    newCashDepositDetail.setOperatorType(CashDepositOperatorTypeEnum.REFUND.getType());

                    cashDepositDetailRepository.save(newCashDepositDetail);

                    // 修改保证金表可用余额
                    cashDeposit.setCurrentBalance(cashDeposit.getCurrentBalance().subtract(cashDepositRefund.getRefund()));
                    cashDepositRepository.updateById(cashDeposit);

                    // 修改原始支付记录的退款金额
                    cashDepositDetail.setRefundAmount(cashDepositDetail.getRefundAmount().add(cashDepositRefund.getRefund()));
                    cashDepositDetailRepository.updateById(cashDepositDetail);

                    Finance finance = new Finance();
                    finance.setOrderId(cashDepositDetail.getChannelOrderId());
                    finance.setAdapayId(cashDepositDetail.getChannelId());
                    finance.setPayId(cashDepositDetail.getChannelOrderId());
                    finance.setType(TransactionTypesEnum.REFUND.getCode());
                    finance.setCreateDate(new Date());
                    finance.setShopId(cashDepositRefund.getShopId());
                    finance.setShopName(null != shopDetailResp ? shopDetailResp.getShopName() : "");
                    finance.setUserId(CommonConst.USER_ID_DEFAULT_ZERO);
                    finance.setUserName("");
                    finance.setTransactionId(StrUtil.isNotBlank(cashDepositDetail.getTradeNo()) ? cashDepositDetail.getTradeNo() : "");
                    finance.setTotalAmount(cashDepositDetail.getRefundAmount());
                    finance.setOrderRefundId(cashDepositRefund.getId());
                    financeRepository.save(finance);
                });
            } catch (Exception e) {
                log.error("【非常重要的异常】调用分账成功,数据库处理失败，confirm退款逻辑中调用分账接口，事务回滚，请求参数：{}", JsonUtil.toJsonString(request), e);
                throw e;
            }
        } else {
            //调用支付撤销接口
            PayReverseCreateReq payReverseCreateReq = new PayReverseCreateReq();
            payReverseCreateReq.setReverseAmount(cashDepositRefund.getRefund());
            payReverseCreateReq.setOrderId(cashDepositDetail.getChannelOrderId());
            payReverseCreateReq.setReverseId(orderNo);

            log.info("confirm退款逻辑中调用支付撤销接口，请求参数：{}", JsonUtil.toJsonString(payReverseCreateReq));
            PayReverseCreateResp payReverseCreateResp = payRemoteService.createPaymentReverse(payReverseCreateReq);
            log.info("confirm退款逻辑中调用支付撤销接口，响应参数：{}", JsonUtil.toJsonString(payReverseCreateResp));

            cashDepositRefund.setRefundOrderId(orderNo);
            cashDepositRefund.setStatus(CashDepositRefundStatusEnum.REFUNDING.getStatus());
            // 把操作人存在remark字段，后面那个监听类从remark里面取
            cashDepositRefund.setRemark(request.getOperator());
            newValue.setStatus(CashDepositRefundStatusEnum.REFUNDING.getStatus());
            cashDepositRefundRepository.updateById(cashDepositRefund);
        }

        // 通知用户服务更新店铺欠费状态
        
        // 手动写日志
        baseLogAssist.recordLog(ExaminModelEnum.ORDER, ExaProEnum.MODIFY,"保证金退款审批通过",
                request.getOperationUserId(), request.getOperationShopId(),
                oldValue, newValue);
    }

    @NotNull
    private CashDepositRefund getCashDepositRefund(Long id) {
        // 退款申请单
        CashDepositRefund cashDepositRefund = cashDepositRefundRepository.getById(id);
        if (cashDepositRefund == null) {
            throw new BusinessException(FinanceResultCodeEnum.CASH_DEPOSIT_REFUND_NOT_EXIST.getCode(), FinanceResultCodeEnum.CASH_DEPOSIT_REFUND_NOT_EXIST.getMsg());
        }
        Integer status = cashDepositRefund.getStatus();
        if (!status.equals(CashDepositRefundStatusEnum.TO_AUDIT.getStatus())) {
            throw new BusinessException(FinanceResultCodeEnum.CASH_DEPOSIT_REFUND_STATUS_ERROR.getCode(), FinanceResultCodeEnum.CASH_DEPOSIT_REFUND_STATUS_ERROR.getMsg());
        }
        return cashDepositRefund;
    }
}

