package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.finance.service.ShopAccountService;
import com.sankuai.shangou.seashop.order.thrift.finance.ShopAccountQueryFeign;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@RestController
@RequestMapping("/finance/shopAccountQuery")
public class ShopAccountQueryFeignImpl implements ShopAccountQueryFeign {

    @Resource
    private ShopAccountService shopAccountService;

    @PostMapping(value = "/getShopAccountByShopId", consumes = "application/json")
    @Override
    public ResultDto<String> getShopAccountByShopId(@RequestParam("shopId") Long shopId) throws TException {
        return ThriftResponseHelper.responseInvoke("shopAccountService.getShopAccountByShopId", shopId,req->shopAccountService.getShopAccountByShopId(req));
    }
}
