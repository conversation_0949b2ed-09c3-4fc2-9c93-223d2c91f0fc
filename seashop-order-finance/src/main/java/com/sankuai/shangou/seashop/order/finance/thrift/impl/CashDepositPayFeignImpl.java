package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositPayService;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositPayFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositPayReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositPayResp;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/29 14:11
 */
@RestController
@RequestMapping("/finance/cashDepositPay")
public class CashDepositPayFeignImpl implements CashDepositPayFeign {

    @Resource
    private CashDepositPayService cashDepositPayService;

    @PostMapping(value = "selectCashDepositPay", consumes = "application/json")
    @Override
    public ResultDto<List<CashDepositPayResp>> selectCashDepositPay(@RequestBody CashDepositPayReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("selectCashDepositPay", request, req -> {
            req.checkParameter();
            return cashDepositPayService.selectCashDepositPay(req);
        });
    }
}
