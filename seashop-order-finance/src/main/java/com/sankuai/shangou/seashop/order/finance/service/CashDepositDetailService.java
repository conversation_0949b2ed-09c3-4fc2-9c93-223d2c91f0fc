package com.sankuai.shangou.seashop.order.finance.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ApplyRefundReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.DeductionReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositDetailResp;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
public interface CashDepositDetailService {

    /**
     * 通过ShopId查询支付成功的保证金支付明细列表信息
     *
     * @param id
     * @return
     */
    List<CashDepositDetailResp> getPayListByCashDepositId(Long id);

    /**
     * 通过条件查询保证金明细列表
     *
     * @param request
     * @return
     */
    BasePageResp<CashDepositDetailResp> pageList(CashDepositDetailQueryReq request);

    /**
     * 保证金扣款
     *
     * @param request
     * @return
     */
    void deduction(DeductionReq request);

    /**
     * 保证金申请退款
     *
     * @param req
     * @return
     */
    void applyRefund(ApplyRefundReq req);
}
