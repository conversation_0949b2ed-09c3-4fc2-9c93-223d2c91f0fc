package com.sankuai.shangou.seashop.order.finance.mq.listener;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.core.RocketMQListener;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2024/1/20/020
 * @description: 监听店铺结算账号变更
 */
@Slf4j
//@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = FinanceMafkaConst.CASH_DEPOSIT_REVERSE_STATUS_CHANGE_TOPIC,
//        group = FinanceMafkaConst.CASH_DEPOSIT_REVERSE_STATUS_CHANGE_CONSUMER)
public class ShopAccountListener implements RocketMQListener<MessageExt> {

//    @Override
//    public ConsumeStatus recvMessage(MafkaMessage mafkaMessage, MessagetContext messagetContext) {
//        return null;
//    }

    @Override
    public void onMessage(MessageExt mafkaMessage) {
        return ;
    }

}
