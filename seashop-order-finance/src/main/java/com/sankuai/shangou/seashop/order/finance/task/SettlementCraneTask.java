package com.sankuai.shangou.seashop.order.finance.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.constant.FinanceConstant;
import com.sankuai.shangou.seashop.order.common.constant.OrderConst;
import com.sankuai.shangou.seashop.order.common.enums.FeeFlagEnum;
import com.sankuai.shangou.seashop.order.common.remote.PayRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.SettingRemoteService;
import com.sankuai.shangou.seashop.order.common.remote.model.base.TradeSiteSettingBo;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderRefund;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRefundRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.domain.*;
import com.sankuai.shangou.seashop.order.dao.finance.repository.*;
import com.sankuai.shangou.seashop.order.finance.mq.model.OrderSettleMsgModel;
import com.sankuai.shangou.seashop.order.finance.mq.publisher.OrderSettleMsgProducer;
import com.sankuai.shangou.seashop.order.finance.service.SettlementConfigService;
import com.sankuai.shangou.seashop.order.finance.util.OrderNoUtil;
import com.sankuai.shangou.seashop.order.thrift.core.enums.order.OrderStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.refund.RefundAuditStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.AccountOrderTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.AccountStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.AccountTypeEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettlementConfigResp;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayResultCodeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PaymentConfirmCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.PaymentConfirmDivMembersDto;
import com.sankuai.shangou.seashop.pay.thrift.core.response.OrderPayResp;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PaymentConfirmCreateResp;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/12/20/020
 * @description: 结算相关定时任务
 */
@Slf4j
@Configuration
public class SettlementCraneTask {

    @Resource
    private SettingRemoteService settingRemoteService;
    @Resource
    private SettlementConfigService settlementConfigService;
    @Resource
    private AccountRepository accountRepository;
    @Resource
    private PendingSettlementOrderRepository pendingSettlementOrderRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private OrderRefundRepository orderRefundRepository;
    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private PayRemoteService payRemoteService;
    @Resource
    private AccountDetailRepository accountDetailRepository;
    @Resource
    private PlatAccountRepository platAccountRepository;
    @Resource
    private ShopAccountRepository shopAccountRepository;
    @Resource
    private ShopAccountItemRepository shopAccountItemRepository;
    @Resource
    private FinanceRepository financeRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private FinanceItemRepository financeItemRepository;
    @Resource
    private OrderSettleMsgProducer orderSettleMsgProducer;

    @Value("${finance.default.min.time:2025-07-20 00:00:00}")
    private String DEFAULT_MIN_TIME_STR;

    /**
     * 订单分账
     */
    //    @Crane("OrderSplitting")
    // @XxlJob("orderSplitting")
//    @Scheduled(cron = "0 0/10 0,1 * * ?")
    @XxlJob("orderSplitting")
    @XxlRegister(cron = "0 0/10 0,1 * * ?",
            author = "snow",
            jobDesc = "订单分账")
    public void orderSplitting() {
        log.info("OrderSplitting start");
        // 查询结算时间
        SettlementConfigResp settlementConfig = settlementConfigService.getConfig();
        log.info("OrderSettlement-结算配置：{}", JsonUtil.toJsonString(settlementConfig));
        Integer settlementInterval = null != settlementConfig && null != settlementConfig.getSettlementInterval() ? settlementConfig.getSettlementInterval().intValue() : 0;
        if (settlementInterval < 1) {
            log.error("OrderSplitting-结算时间未配置或配置错误 settlementConfig={}", settlementConfig);
            return;
        }

        // 查询售后维权期
        Integer salesReturnTimeout = getSalesReturnTimeout();
        log.info("OrderSplitting-售后维权期：{}", salesReturnTimeout);

        // 给了一个默认值
        Date checkDate = getCheckDate();

        //开始分账
        Date startDate = checkDate;
        Date endDate = DateUtil.offsetDay(startDate, settlementInterval);
        log.info("OrderSplitting-分账时间-startDate={},endDate={}", startDate, endDate);

        Date now = new Date();
        // 0元订单或者已关闭订单直接逻辑删除
        List<PendingSettlementOrder> zeroPendingDatas = new ArrayList<>();
        while (endDate.before(now)) {

            List<PendingSettlementOrder> pendingSettlementData = this.queryPendingSettlementData(endDate, salesReturnTimeout, zeroPendingDatas);
            log.info("OrderSettlement-successPendingData={}", pendingSettlementData);
            // 处理下一个结算周期的时间
            startDate = endDate;
            endDate = DateUtil.offsetDay(startDate, settlementInterval);
            if (CollUtil.isEmpty(pendingSettlementData)) {
                continue;
            }

            // 调用结算：汇付分账
            log.info("OrderSplitting-result-pendingSettlementData={}", pendingSettlementData);
            List<String> orderIds = pendingSettlementData.stream().map(PendingSettlementOrder::getOrderId).collect(Collectors.toList());
            List<OrderPayRecord> orderPayRecords = orderPayRecordRepository.listByOrderIdAndStatus(orderIds, PayStatusEnum.PAY_SUCCESS.getCode());
            if (CollUtil.isEmpty(orderPayRecords)) {
                // 正常数据应该不会出现在这里
                log.error("OrderSplitting-没有查询到支付成功的数据，orderIds={}", orderIds);
                continue;
            }
            for (PendingSettlementOrder pendingSettlementOrder : pendingSettlementData) {
                List<OrderPayRecord> orderPayRecordList = orderPayRecords.stream().filter(orderPayRecord -> orderPayRecord.getOrderId().equals(pendingSettlementOrder.getOrderId())).collect(Collectors.toList());
                if (CollUtil.isEmpty(orderPayRecordList)) {
                    // 理论上不会进这里，如果进了，说明数据存在问题
                    log.error("OrderSplitting-pendingSettlementOrder={}，数据存在问题，需要检查（没有查询到支付成功的数据）", pendingSettlementOrder);
                    continue;
                }
                // 结算备注信息
                String settlementRemark = pendingSettlementOrder.getSettelementRemark();
                if (StrUtil.isNotBlank(settlementRemark) && (settlementRemark.contains("order_no_used") || settlementRemark.contains("confirm_amt_over_limit"))) {
                    //分账订单号已使用或者分账金额超出可分账金额时视为分账成功（这两种情况基本是重复发起分账了）
                    log.info("OrderSplitting-订单号：{},分账订单号已使用或者分账金额超出可分账金额时视为分账成功", pendingSettlementOrder.getOrderId());
                    pendingSettlementOrder.setSettelementRemark(FinanceConstant.SETTLEMENT_SUCCESS_DESC);
                    pendingSettlementOrderRepository.updateById(pendingSettlementOrder);
                    continue;
                }
                if (StrUtil.isNotBlank(settlementRemark) && settlementRemark.equals(FinanceConstant.SETTLEMENT_SUCCESS_DESC)) {
                    // 理论上不会进这里，如果进了，说明数据存在问题
                    log.info("OrderSplitting-pendingSettlementOrder={}，结算备注信息不为空且是结算成功", pendingSettlementOrder);
                    continue;
                }
                // 订单金额
                BigDecimal orderAmount = null != pendingSettlementOrder.getOrderAmount() ? pendingSettlementOrder.getOrderAmount() : BigDecimal.ZERO;
                // 退款金额
                BigDecimal refundAmount = null != pendingSettlementOrder.getRefundAmount() ? pendingSettlementOrder.getRefundAmount() : BigDecimal.ZERO;
                // 结算金额
                BigDecimal settlementAmount = null != pendingSettlementOrder.getSettlementAmount() ? pendingSettlementOrder.getSettlementAmount() : BigDecimal.ZERO;
                // 渠道手续费
                BigDecimal channelAmount = null != pendingSettlementOrder.getChannelAmount() ? pendingSettlementOrder.getChannelAmount() : BigDecimal.ZERO;
                // 平台佣金
                BigDecimal platCommission = null != pendingSettlementOrder.getPlatCommission() ? pendingSettlementOrder.getPlatCommission() : BigDecimal.ZERO;
                // 总额
                BigDecimal totalAmount = settlementAmount.add(channelAmount).add(platCommission);

                if (orderAmount.compareTo(refundAmount) <= 0 || orderAmount.compareTo(BigDecimal.ZERO) <= 0 || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
                    // 订单已全部退款，订单金额为0，或者总额为0，不进行分账
                    log.info("OrderSplitting-订单已全部退款，订单号：{},订单信息：{}", pendingSettlementOrder.getOrderId(), JsonUtil.toJsonString(pendingSettlementOrder));
                    pendingSettlementOrder.setDeleteFlag(Boolean.TRUE);
                    zeroPendingDatas.add(pendingSettlementOrder);
                    continue;
                }

                TransactionHelper.doInTransaction(() -> {
                            // 封装分账对象
                            List<PaymentConfirmDivMembersDto> divMembers = getPaymentConfirmDivMembersDtos(pendingSettlementOrder, settlementAmount, channelAmount, platCommission, totalAmount);
                            log.info("OrderSplitting-订单号：{},divMembers={}", pendingSettlementOrder.getOrderId(), JsonUtil.toJsonString(divMembers));

                            PaymentConfirmCreateReq request = getPaymentConfirmCreateReq(pendingSettlementOrder, orderPayRecordList, totalAmount, divMembers);
                            log.info("OrderSplitting-订单号：{},PaymentConfirmCreateReq={}", pendingSettlementOrder.getOrderId(), JsonUtil.toJsonString(request));

                            ResultDto<PaymentConfirmCreateResp> paymentConfirmResult = payRemoteService.createPaymentConfirm(request);
                            log.info("OrderSplitting-订单号：{},paymentConfirmResult={}", pendingSettlementOrder.getOrderId(), JsonUtil.toJsonString(paymentConfirmResult));
                            if (!paymentConfirmResult.isSuccess()) {
                                // 处理汇付返回的异常：PAY_PAYMENT_CONFIRM_CREATE_CHANNEL_ERROR
                                if (paymentConfirmResult.getCode().equals(PayResultCodeEnum.PAY_PAYMENT_CONFIRM_CREATE_CHANNEL_ERROR.getCode())) {
                                    //错误码, order_no_used:分账订单号已使用, confirm_amt_over_limit:分账金额超出可分账金额
                                    String message = paymentConfirmResult.getMessage();
                                    Map<String, String> errMap = JsonUtil.parseObject(message, new TypeReference<Map<String, String>>() {
                                    });

                                    String errorCode = errMap.get(FinanceConstant.ERROR_CODE);
                                    //分账订单号已使用或者分账金额超出可分账金额时视为分账成功（这两种情况基本是重复发起分账了）
                                    if (FinanceConstant.ORDER_NO_USED.equals(errorCode) || FinanceConstant.CONFIRM_AMT_OVER_LIMIT.equals(errorCode)) {
                                        pendingSettlementOrder.setSettelementRemark(FinanceConstant.SETTLEMENT_SUCCESS_DESC);
                                        pendingSettlementOrderRepository.updateById(pendingSettlementOrder);
                                    } else {
                                        //可能本次job还未执行，下次job已经执行了，再次查询判断，避免将已经成功的数据写成失败
                                        PendingSettlementOrder settlementOrder = pendingSettlementOrderRepository.getById(pendingSettlementOrder.getId());
                                        if (null != settlementOrder && !settlementOrder.getSelfFlag()
                                                && (StrUtil.isBlank(settlementOrder.getSettelementRemark()) || !settlementOrder.getSettelementRemark().equals(FinanceConstant.SETTLEMENT_SUCCESS_DESC))) {
                                            String errorMsg = errMap.get(FinanceConstant.ERROR_MSG);
                                            pendingSettlementOrder.setSettelementRemark(String.format(FinanceConstant.SETTLEMENT_FAIL_DESC, errorCode, errorMsg));
                                            pendingSettlementOrderRepository.updateById(pendingSettlementOrder);
                                        }
                                    }
                                    log.error("汇付分账返回错误：汇付异常报错,订单号{}:{}", pendingSettlementOrder.getOrderId(), pendingSettlementOrder.getSettelementRemark());
                                } else {
                                    // 非汇付返回的异常，直接记录日志
                                    log.error("汇付分账返回错误：非汇付异常报错,订单号{}:{}", pendingSettlementOrder.getOrderId(), paymentConfirmResult.getMessage());
                                }
                            } else {
                                PaymentConfirmCreateResp paymentConfirmCreateResp = paymentConfirmResult.getData();
                                BigDecimal feeAmt = paymentConfirmCreateResp.getFeeAmt();
                                if (feeAmt.compareTo(channelAmount) != 0) {
                                    BigDecimal confirmAmount = settlementAmount.add(channelAmount);
                                    pendingSettlementOrder.setSettlementAmount(confirmAmount.subtract(feeAmt));
                                    pendingSettlementOrder.setChannelAmount(feeAmt);
                                }
                                pendingSettlementOrder.setSettelementRemark(FinanceConstant.SETTLEMENT_SUCCESS_DESC);
                                pendingSettlementOrderRepository.updateById(pendingSettlementOrder);
                            }
                        }
                );
            }
        }
        if (!CollectionUtils.isEmpty(zeroPendingDatas)) {
            // 0元订单逻辑删除
            zeroPendingDatas = zeroPendingDatas.stream().collect(Collectors.toMap(PendingSettlementOrder::getOrderId, p -> p, (p1, p2) -> p2)).values().stream().collect(Collectors.toList());
            pendingSettlementOrderRepository.updateBatchById(zeroPendingDatas);
        }
    }

    /**
     * 订单结算
     */
    //    @Crane("OrderSettlement")
    @XxlJob("orderSettlement")
//    @Scheduled(cron = "0 0 3 * * ?")
    @XxlRegister(cron = "0 3 * * * ?",
            author = "snow",
            jobDesc = "订单结算")
    public void orderSettlement() {
        log.info("OrderSettlement start");
        // 查询结算时间
        SettlementConfigResp settlementConfig = settlementConfigService.getConfig();
        log.info("OrderSettlement-结算配置：{}", JsonUtil.toJsonString(settlementConfig));
        Integer settlementInterval = null != settlementConfig && null != settlementConfig.getSettlementInterval() ? settlementConfig.getSettlementInterval().intValue() : 0;
        if (settlementInterval < 1) {
            log.error("OrderSplitting-结算时间未配置或配置错误 settlementConfig={}", settlementConfig);
            return;
        }

        // 查询售后维权期
        Integer salesReturnTimeout = getSalesReturnTimeout();
        log.info("OrderSettlement-售后维权期：{}", salesReturnTimeout);
        // 给了一个默认值
        Date checkDate = getCheckDate();

        //开始分账
        Date startDate = checkDate;
        Date endDate = DateUtil.offsetDay(startDate, settlementInterval);

        log.info("OrderSettlement startDate={},endDate={}", startDate, endDate);

        // 结算推送消息
        List<OrderSettleMsgModel> orderSettleMsgModels = new ArrayList<>();

        Date now = new Date();
        while (endDate.before(now)) {
            List<PendingSettlementOrder> successPendingData = this.querySuccessPendingData(endDate, salesReturnTimeout);
            log.info("OrderSettlement-successPendingData={}", successPendingData);
            if (CollUtil.isEmpty(successPendingData)) {
                startDate = endDate;
                endDate = DateUtil.offsetDay(startDate, settlementInterval);
                continue;
            }

            final Date finalStartDate = startDate;
            final Date finalEndDate = endDate;

            TransactionHelper.doInTransaction(() -> {
                Account addAccount = getAccount(finalStartDate, finalEndDate, successPendingData);
                accountRepository.save(addAccount);

                List<AccountDetail> accountDetails = getAccountDetails(successPendingData, addAccount);
                accountDetailRepository.saveBatch(accountDetails);

                // 平台账户表操作
                PlatAccount platAccount = platAccountRepository.getPlatAccount();
                platAccount.setPendingSettlement(platAccount.getPendingSettlement().subtract(addAccount.getPeriodSettlement()));
                platAccount.setSettled(platAccount.getSettled().add(addAccount.getPeriodSettlement()));
                platAccountRepository.updateById(platAccount);

                List<Long> shopIdList = successPendingData.stream().map(PendingSettlementOrder::getShopId).distinct().collect(Collectors.toList());
                List<ShopAccount> shopAccountList = shopAccountRepository.getListByShopIdList(shopIdList);

                Map<Long, List<PendingSettlementOrder>> shopPendingOrderMap = successPendingData.stream().collect(Collectors.groupingBy(PendingSettlementOrder::getShopId));
                shopPendingOrderMap.forEach((key, value) -> {
                    ShopAccount shopAccount = null;
                    if (CollUtil.isNotEmpty(shopAccountList)) {
                        shopAccount = shopAccountList.stream().filter(sAccount -> sAccount.getShopId().equals(key)).findFirst().orElse(null);
                    }
                    if (Objects.isNull(shopAccount)) {
                        shopAccount = new ShopAccount();
                        shopAccount.setShopId(key);
                        shopAccount.setShopName(value.get(0).getShopName());
                        shopAccount.setPendingSettlement(BigDecimal.ZERO);
                        shopAccount.setSettled(BigDecimal.ZERO);
                        // 这里保存，为了方便下面能取到ShopAccount的ID
                        shopAccountRepository.save(shopAccount);
                    }

                    ShopAccountItem shopAccountItem = getShopAccountItem(settlementInterval, addAccount, value, shopAccount);
                    shopAccountItemRepository.save(shopAccountItem);

                    shopAccount.setPendingSettlement(shopAccount.getPendingSettlement().subtract(shopAccountItem.getAmount()));
                    if (shopAccount.getPendingSettlement().compareTo(BigDecimal.ZERO) <= 0) {
                        shopAccount.setPendingSettlement(BigDecimal.ZERO);
                    }
                    shopAccount.setSettled(shopAccount.getSettled().add(shopAccountItem.getAmount()));
                    shopAccountRepository.updateById(shopAccount);
                });

                //写入中间表
                List<String> successOrderIds = successPendingData.stream().map(PendingSettlementOrder::getOrderId).collect(Collectors.toList());

                List<OrderPayRecord> successOrderPays = orderPayRecordRepository.listByOrderIdAndStatus(successOrderIds, PayStatusEnum.PAY_SUCCESS.getCode());
                List<Order> successOrders = orderRepository.getByOrderIdList(successOrderIds);

                List<String> batchNoList = successOrderPays.stream().map(OrderPayRecord::getBatchNo).distinct().collect(Collectors.toList());
                Map<String, OrderPayResp> payRespMap = payRemoteService.queryPayResultNoOutTransId(batchNoList);

                for (PendingSettlementOrder pendingSettlementOrder : successPendingData) {
                    Order order = successOrders.stream().filter(o -> o.getOrderId().equals(pendingSettlementOrder.getOrderId())).findFirst().orElse(null);
                    OrderPayRecord orderPayRecord = successOrderPays.stream().filter(o -> o.getOrderId().equals(pendingSettlementOrder.getOrderId())).findFirst().orElse(null);
                    AccountDetail accountDetail = accountDetails.stream().filter(a -> a.getOrderId().equals(pendingSettlementOrder.getOrderId())).findFirst().orElse(null);
                    if (Objects.isNull(order) || Objects.isNull(orderPayRecord) || Objects.isNull(accountDetail)) {
                        // 理论上不存在这种数据，如果进了，说明数据存在问题
                        log.error("OrderSettlement-订单或者支付记录不存在，订单号：{},order：{}，orderPayRecord：{}", pendingSettlementOrder.getOrderId(), order, orderPayRecord);
//                        throw new RuntimeException("订单或者支付记录不存在");
                        continue;
                    }
                    if (pendingSettlementOrder.getOrderAmount().compareTo(pendingSettlementOrder.getRefundAmount()) == 0 || pendingSettlementOrder.getOrderAmount().compareTo(BigDecimal.ZERO) <= 0) {
                        log.info("OrderSettlement-订单已全部退款，订单号：{}", pendingSettlementOrder.getOrderId());
                        continue;
                    }
                    List<Finance> finances = financeRepository.listByOrderId(pendingSettlementOrder.getOrderId(), TransactionTypesEnum.SETTLEMENT.getCode());
                    if (CollUtil.isNotEmpty(finances)) {
                        log.info("OrderSettlement-订单已结算，订单号：{}", pendingSettlementOrder.getOrderId());
                        continue;
                    }
                    OrderPayResp payResp = payRespMap.get(orderPayRecord.getBatchNo());
                    if (payResp == null) {
                        log.error("OrderSettlement-支付结果不存在，订单号：{},batchNo：{}", pendingSettlementOrder.getOrderId(), orderPayRecord.getBatchNo());
                        continue;
                    }

                    /*
                     处理财务中间表数据
                     */
                    Finance finance = getFinance(pendingSettlementOrder, order, orderPayRecord, accountDetail, payResp);
                    financeRepository.save(finance);

                    // 处理结算推送消息
                    OrderSettleMsgModel orderSettleMsgModel = OrderSettleMsgModel.builder()
                            .orderId(pendingSettlementOrder.getOrderId())
                            .type(TransactionTypesEnum.SETTLEMENT.getCode())
                            .shopId(pendingSettlementOrder.getShopId())
                            .build();
                    orderSettleMsgModels.add(orderSettleMsgModel);

                    List<FinanceItem> financeItems = getFinanceItems(pendingSettlementOrder, finance);
                    if (CollUtil.isNotEmpty(financeItems)) {
                        financeItemRepository.saveBatch(financeItems);
                    }
                }

                successPendingData.parallelStream().forEach(pendingSettlementOrder -> {
                    pendingSettlementOrder.setDeleteFlag(Boolean.TRUE);
                });
                pendingSettlementOrderRepository.updateBatchById(successPendingData);
            });

            startDate = endDate;
            endDate = DateUtil.offsetDay(startDate, settlementInterval);
        }

        if (CollUtil.isNotEmpty(orderSettleMsgModels)) {
            orderSettleMsgProducer.sendMessage(orderSettleMsgModels);
        }
    }

    @NotNull
    private static PaymentConfirmCreateReq getPaymentConfirmCreateReq(PendingSettlementOrder pendingSettlementOrder, List<OrderPayRecord> orderPayRecordList, BigDecimal totalAmount, List<PaymentConfirmDivMembersDto> divMembers) {
        OrderPayRecord orderPayRecord = orderPayRecordList.get(0);

        PaymentConfirmCreateReq request = new PaymentConfirmCreateReq();
        request.setSourceOrderId(orderPayRecord.getBatchNo());
        request.setOrderNo(pendingSettlementOrder.getOrderId());
        request.setConfirmAmount(totalAmount);
        request.setDivMembers(divMembers);
        return request;
    }

    private Integer getSalesReturnTimeout() {
        TradeSiteSettingBo tradeSiteSetting = settingRemoteService.getTradeSiteSetting();
        Integer salesReturnTimeout = NumberUtil.parseInt(tradeSiteSetting.getSalesReturnTimeout(), 0);
        return salesReturnTimeout;
    }

    private Date getCheckDate() {
        Date checkDate = DateUtil.parse(DEFAULT_MIN_TIME_STR);
        // 获取最后一次的结算时间
        Account account = accountRepository.getLatestOne();
        if (null != account) {
            // 因为要迁移数据，这里正常情况不会进入
            checkDate = DateUtil.beginOfDay(account.getEndDate());
        }
        return checkDate;
    }

    @NotNull
    private static List<PaymentConfirmDivMembersDto> getPaymentConfirmDivMembersDtos(PendingSettlementOrder pendingSettlementOrder, BigDecimal settlementAmount, BigDecimal channelAmount, BigDecimal platCommission, BigDecimal totalAmount) {
        List<PaymentConfirmDivMembersDto> divMembers = new ArrayList<>();
        if (pendingSettlementOrder.getSelfFlag()) {
            log.info("OrderSettlement-自营店，所有金额结算给平台");
            //平台自营店，所有金额结算给平台
            PaymentConfirmDivMembersDto paymentConfirmDivMembersDto = new PaymentConfirmDivMembersDto();
            paymentConfirmDivMembersDto.setMemberId(OrderConst.PLATFORM_ADAPAY_ID);
            paymentConfirmDivMembersDto.setAmount(totalAmount);
            paymentConfirmDivMembersDto.setFeeFlag(FeeFlagEnum.YES.getFlag());
            divMembers.add(paymentConfirmDivMembersDto);

        } else {
            if (settlementAmount.add(channelAmount).compareTo(BigDecimal.ZERO) > 0) {
                PaymentConfirmDivMembersDto paymentConfirmDivMembersDto = new PaymentConfirmDivMembersDto();
                paymentConfirmDivMembersDto.setMemberId(pendingSettlementOrder.getShopId().toString());
                paymentConfirmDivMembersDto.setAmount(settlementAmount.add(channelAmount));
                paymentConfirmDivMembersDto.setFeeFlag(FeeFlagEnum.YES.getFlag());
                divMembers.add(paymentConfirmDivMembersDto);
            }
            if (platCommission.compareTo(BigDecimal.ZERO) > 0) {
                PaymentConfirmDivMembersDto paymentConfirmDivMembersDto = new PaymentConfirmDivMembersDto();
                paymentConfirmDivMembersDto.setMemberId(OrderConst.PLATFORM_ADAPAY_ID);
                paymentConfirmDivMembersDto.setAmount(platCommission);
                paymentConfirmDivMembersDto.setFeeFlag(FeeFlagEnum.NO.getFlag());
                divMembers.add(paymentConfirmDivMembersDto);
            }
        }
        if (divMembers.size() == 1) {
            //如果分账方只有平台时，手续费由平台承担
            PaymentConfirmDivMembersDto paymentConfirmDivMembersDto = divMembers.get(0);
            if (paymentConfirmDivMembersDto.getMemberId().equals(OrderConst.PLATFORM_ADAPAY_ID)) {
                paymentConfirmDivMembersDto.setFeeFlag(FeeFlagEnum.YES.getFlag());
            }
        }
        return divMembers;
    }

    @NotNull
    private static Finance getFinance(PendingSettlementOrder pendingSettlementOrder, Order order, OrderPayRecord orderPayRecord, AccountDetail accountDetail, OrderPayResp payResp) {
        BigDecimal channelAmount = pendingSettlementOrder.getChannelAmount();
        BigDecimal settlementAmount = pendingSettlementOrder.getSettlementAmount();
        BigDecimal platCommission = pendingSettlementOrder.getPlatCommission();

        Finance finance = new Finance();
        finance.setOrderId(pendingSettlementOrder.getOrderId());
        finance.setAdapayId(orderPayRecord.getPayNo());
        finance.setPayId(payResp.getPayId());
        finance.setType(TransactionTypesEnum.SETTLEMENT.getCode());
        finance.setCreateDate(accountDetail.getDate());
        finance.setShopId(order.getShopId());
        finance.setShopName(order.getShopName());
        finance.setUserId(order.getUserId());
        finance.setUserName(order.getUserName());
        finance.setTransactionId(StrUtil.isNotBlank(order.getGatewayOrderId()) ? order.getGatewayOrderId() : "");
        finance.setTotalAmount(order.getTotalAmount());
        finance.setFreight(order.getFreight());
        finance.setProductAmount(order.getProductTotalAmount());
        finance.setDiscountAmount(order.getDiscountAmount());
        finance.setFullDiscount(order.getDiscountAmount());
        finance.setMoneyOff(order.getMoneyOffAmount());
        finance.setServiceAmount(channelAmount);
        finance.setSettlementAmount(settlementAmount);
        finance.setCommissionAmount(platCommission);
        finance.setActualPayAmount(orderPayRecord.getPayAmount());
        return finance;
    }

    @NotNull
    private List<FinanceItem> getFinanceItems(PendingSettlementOrder pendingSettlementOrder, Finance finance) {
        List<OrderItem> orderItems = orderItemRepository.getByOrderId(pendingSettlementOrder.getOrderId());
        List<FinanceItem> financeItems = new ArrayList<>();
        for (OrderItem orderItem : orderItems) {
            FinanceItem financeItem = new FinanceItem();
            financeItem.setFinanceId(finance.getId());
            financeItem.setOrderId(orderItem.getOrderId());
            financeItem.setProductId(orderItem.getProductId());
            financeItem.setProductName(orderItem.getProductName());
            financeItem.setSku(orderItem.getSku());
            financeItem.setQuantity(orderItem.getQuantity());
            financeItem.setOriginalPrice(orderItem.getCostPrice());
            financeItem.setSalePrice(orderItem.getSalePrice());
            financeItem.setTotalAmount(orderItem.getSalePrice().multiply(BigDecimal.valueOf(orderItem.getQuantity())));
            financeItem.setDiscountPrice(orderItem.getDiscountAmount());
            financeItem.setFullDiscount(orderItem.getFullDiscount());
            financeItem.setMoneyOff(orderItem.getMoneyOff());
            financeItem.setCouponDiscount(orderItem.getCouponDiscount());
            financeItem.setCommisRate(orderItem.getCommisRate());
            financeItems.add(financeItem);
        }
        return financeItems;
    }

    @NotNull
    private static ShopAccountItem getShopAccountItem(Integer settlementInterval, Account addAccount, List<PendingSettlementOrder> value, ShopAccount shopAccount) {
        ShopAccountItem shopAccountItem = new ShopAccountItem();
        shopAccountItem.setAccountId(shopAccount.getId());
        shopAccountItem.setAccountNo(OrderNoUtil.getAccountNo(new Date()));
        shopAccountItem.setShopId(shopAccount.getShopId());
        shopAccountItem.setShopName(shopAccount.getShopName());
        shopAccountItem.setCreateTime(new Date());
        shopAccountItem.setAmount(value.stream().map(PendingSettlementOrder::getSettlementAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        shopAccountItem.setTradeType(AccountTypeEnum.SETTLEMENT_INCOME.getType());
        shopAccountItem.setIncomeFlag(Boolean.TRUE);
        shopAccountItem.setRemark("店铺结算明细" + addAccount.getId());
        shopAccountItem.setDetailId(addAccount.getId().toString());
        shopAccountItem.setSettlementCycle(settlementInterval);
        return shopAccountItem;
    }

    @NotNull
    private static List<AccountDetail> getAccountDetails(List<PendingSettlementOrder> successPendingData, Account addAccount) {
        List<AccountDetail> accountDetails = new ArrayList<>();
        for (PendingSettlementOrder pendingSettlementOrder : successPendingData) {
            AccountDetail accountDetail = new AccountDetail();
            accountDetail.setAccountId(addAccount.getId());
            accountDetail.setShopId(pendingSettlementOrder.getShopId());
            accountDetail.setShopName(pendingSettlementOrder.getShopName());
            accountDetail.setOrderType(AccountOrderTypeEnum.FINISHED_ORDER.getType());
            accountDetail.setDate(new Date());
            accountDetail.setOrderFinishDate(pendingSettlementOrder.getOrderFinishTime());
            accountDetail.setOrderId(pendingSettlementOrder.getOrderId());
            accountDetail.setProductActualPaidAmount(pendingSettlementOrder.getProductsAmount());
            accountDetail.setFreightAmount(pendingSettlementOrder.getFreightAmount());
            accountDetail.setCommissionAmount(pendingSettlementOrder.getPlatCommission());
            accountDetail.setRefundCommisAmount(pendingSettlementOrder.getPlatCommissionReturn());
            accountDetail.setOrderRefundsDates(null != pendingSettlementOrder.getRefundDate() ? DateUtil.formatDateTime(pendingSettlementOrder.getRefundDate()) : "");
            accountDetail.setRefundTotalAmount(pendingSettlementOrder.getRefundAmount());
            accountDetail.setOrderAmount(pendingSettlementOrder.getOrderAmount());
            accountDetail.setTaxAmount(pendingSettlementOrder.getTaxAmount());
            accountDetail.setSettlementAmount(pendingSettlementOrder.getSettlementAmount());
            accountDetail.setPaymentTypeName(pendingSettlementOrder.getPaymentTypeName());
            accountDetail.setChannelAmount(pendingSettlementOrder.getChannelAmount());

            accountDetail.setOrderDate(pendingSettlementOrder.getOrderFinishTime());
            accountDetail.setPayDate(pendingSettlementOrder.getPayDate());
            accountDetail.setPaymentType(pendingSettlementOrder.getPaymentType());

            accountDetails.add(accountDetail);
        }
        return accountDetails;
    }

    @NotNull
    private static Account getAccount(Date startDate, Date endDate, List<PendingSettlementOrder> successPendingData) {
        Account addAccount = new Account();
        addAccount.setShopId(FinanceConstant.PLATFORM_ID);
        addAccount.setShopName(FinanceConstant.SYSTEM_SETTLEMENT);
        addAccount.setAccountDate(new Date());
        addAccount.setStartDate(startDate);
        addAccount.setEndDate(endDate);
        addAccount.setStatus(AccountStatusEnum.ACCOUNTED.getStatus());
        addAccount.setProductActualPaidAmount(successPendingData.stream().map(PendingSettlementOrder::getProductsAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        addAccount.setFreightAmount(successPendingData.stream().map(PendingSettlementOrder::getFreightAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        addAccount.setCommissionAmount(successPendingData.stream().map(PendingSettlementOrder::getPlatCommission).reduce(BigDecimal.ZERO, BigDecimal::add));
        addAccount.setRefundCommissionAmount(successPendingData.stream().map(PendingSettlementOrder::getPlatCommissionReturn).reduce(BigDecimal.ZERO, BigDecimal::add));
        addAccount.setRefundAmount(successPendingData.stream().map(PendingSettlementOrder::getRefundAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        addAccount.setAdvancePaymentAmount(BigDecimal.ZERO);
        addAccount.setPeriodSettlement(successPendingData.stream().map(PendingSettlementOrder::getSettlementAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        addAccount.setChannelAmount(successPendingData.stream().map(PendingSettlementOrder::getChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add));
        return addAccount;
    }

    private List<PendingSettlementOrder> querySuccessPendingData(Date endDate, Integer salesReturnTimeout) {
        //结算日期内的待结算订单 不计算开始时间，防止漏单，结算过后，会删除待结算订单
        //完成【时间】< 结算周期结束【日期】（包括未过售后期的）；
        DateTime queryEndDate = DateUtil.offsetDay(endDate, -salesReturnTimeout);
        log.info("查询待结算订单，售后维权期截止日期: {}", queryEndDate);

        List<PendingSettlementOrder> prePendingSettlementData = pendingSettlementOrderRepository.getByFinishTime(endDate);
        if (CollUtil.isEmpty(prePendingSettlementData)) {
            log.info("没有待结算订单");
            return Collections.EMPTY_LIST;
        }

        List<String> preOrderIds = prePendingSettlementData.stream().map(PendingSettlementOrder::getOrderId).collect(Collectors.toList());
        log.info("待结算订单ID：{}", JsonUtil.toJsonString(preOrderIds));
        // 查询本批待结算订单的订单信息
        List<Order> preOrderList = orderRepository.getByOrderIdList(preOrderIds);
        //已关闭的订单ID
        List<String> closeIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(preOrderList)) {
            closeIds.addAll(preOrderList.stream().filter(order -> order.getOrderStatus().equals(OrderStatusEnum.CLOSED.getCode())).map(Order::getOrderId).collect(Collectors.toList()));
        }
        log.info("待结算订单中，已关闭的订单ID：{}", JsonUtil.toJsonString(closeIds));

        //已过售后期、已关闭的订单,已完成的虚拟订单，都可以结算
        List<PendingSettlementOrder> pendingSettlementData = prePendingSettlementData.stream().filter(pendingSettlementOrder ->
                pendingSettlementOrder.getOrderFinishTime().before(queryEndDate)
                        || (CollUtil.isNotEmpty(closeIds) && closeIds.contains(pendingSettlementOrder.getOrderId()))).collect(Collectors.toList());
        if (CollUtil.isEmpty(pendingSettlementData)) {
            log.info("当前待结算订单的结束时间或者关闭状态不匹配，没有需要结算的订单");
            return Collections.EMPTY_LIST;
        }

        /**
         * .net 代码处理结算时，以下逻辑并不存在，目前不确定是否需要，先注释掉
         *
         List<String> pendOrderIds = pendingSettlementData.stream().map(PendingSettlementOrder::getOrderId).collect(Collectors.toList());
         //      原来通过查询： List<Order> pendOrderList = orderRepository.getByOrderIdList(pendOrderIds)，优化成过滤
         List<Order> pendOrderList = preOrderList.stream().filter(order -> pendOrderIds.contains(order.getOrderId())).collect(Collectors.toList());
         //过滤已全部退款订单
         List<String> filterOrderIds = new ArrayList<>();
         if (CollUtil.isNotEmpty(pendOrderList)) {
         filterOrderIds.addAll(pendOrderList.stream().filter(order -> order.getOrderStatus().equals(OrderStatusEnum.CLOSED.getCode()) && order.getActualPayAmount().compareTo(BigDecimal.ZERO) <= 0).map(Order::getOrderId).collect(Collectors.toList()));
         }

         pendingSettlementData = pendingSettlementData.stream().filter(pendingSettlementOrder -> !filterOrderIds.contains(pendingSettlementOrder.getOrderId())).collect(Collectors.toList());
         if (CollUtil.isEmpty(pendingSettlementData)) {
         return Collections.EMPTY_LIST;
         }
         */

        //可结算的订单，是否有未完成的售后
        List<String> refundOrderIds = getRefundOrderIds(pendingSettlementData);

        // 过滤有未完成的售后订单
        pendingSettlementData = pendingSettlementData.stream().filter(pendingSettlementOrder -> !refundOrderIds.contains(pendingSettlementOrder.getOrderId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(pendingSettlementData)) {
            log.info("当前待结算的订单都有未完成的售后");
            return Collections.EMPTY_LIST;
        }
        pendingSettlementData = pendingSettlementData.stream()
                .filter(pendingSettlementOrder -> FinanceConstant.SETTLEMENT_SUCCESS_DESC.equals(pendingSettlementOrder.getSettelementRemark()))
                .collect(Collectors.toList());
        return pendingSettlementData;
    }

    private List<PendingSettlementOrder> queryPendingSettlementData(Date endDate, Integer salesReturnTimeout, List<PendingSettlementOrder> zeroPendingDatas) {
        //结算日期内的待结算订单 不计算开始时间，防止漏单，结算过后，会删除待结算订单
        //完成【时间】< 结算周期结束【日期】（包括未过售后期的）；
        DateTime queryEndDate = DateUtil.offsetDay(endDate, -salesReturnTimeout);
        log.info("查询待结算订单，售后维权期截止日期: {}", queryEndDate);
        List<PendingSettlementOrder> prePendingSettlementData = pendingSettlementOrderRepository.getByFinishTime(endDate);
        if (CollUtil.isEmpty(prePendingSettlementData)) {
            log.info("没有待结算订单");
            return Collections.EMPTY_LIST;
        }

        List<String> preOrderIds = prePendingSettlementData.stream().map(PendingSettlementOrder::getOrderId).collect(Collectors.toList());
        log.info("待结算订单ID：{}", JsonUtil.toJsonString(preOrderIds));
        // 查询本批待结算订单的订单信息
        List<Order> preOrderList = orderRepository.getByOrderIdList(preOrderIds);
        //已关闭的订单ID
        List<String> closeIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(preOrderList)) {
            closeIds.addAll(preOrderList.stream().filter(order -> order.getOrderStatus().equals(OrderStatusEnum.CLOSED.getCode()) && order.getActualPayAmount().compareTo(BigDecimal.ZERO) > 0).map(Order::getOrderId).collect(Collectors.toList()));
        }
        log.info("待结算订单中，已关闭的订单ID：{}", JsonUtil.toJsonString(closeIds));
        //已过售后期、已关闭的订单,已完成的虚拟订单，都可以结算
        List<PendingSettlementOrder> pendingSettlementData = prePendingSettlementData.stream().filter(pendingSettlementOrder ->
                pendingSettlementOrder.getOrderFinishTime().before(queryEndDate)
                        || (CollUtil.isNotEmpty(closeIds) && closeIds.contains(pendingSettlementOrder.getOrderId()))).collect(Collectors.toList());
        if (CollUtil.isEmpty(pendingSettlementData)) {
            log.info("当前待结算订单的结束时间或者关闭状态不匹配，没有需要结算的订单");
            return Collections.EMPTY_LIST;
        }

        List<String> pendOrderIds = pendingSettlementData.stream().map(PendingSettlementOrder::getOrderId).collect(Collectors.toList());
        //      原来通过查询： List<Order> pendOrderList = orderRepository.getByOrderIdList(pendOrderIds)，优化成过滤
        List<Order> pendOrderList = preOrderList.stream().filter(order -> pendOrderIds.contains(order.getOrderId())).collect(Collectors.toList());
        //过滤已全部退款订单
        List<String> filterOrderIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(pendOrderList)) {
            filterOrderIds.addAll(pendOrderList.stream().filter(order -> order.getOrderStatus().equals(OrderStatusEnum.CLOSED.getCode()) && order.getActualPayAmount().compareTo(BigDecimal.ZERO) <= 0).map(Order::getOrderId).collect(Collectors.toList()));
        }
        // 过滤出已全部退款的订单，结算状态改成删除
        List<PendingSettlementOrder> alreadyRefund = pendingSettlementData.stream().filter(pendingSettlementOrder -> filterOrderIds.contains(pendingSettlementOrder.getOrderId())).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(alreadyRefund)) {
            alreadyRefund.forEach(v -> v.setDeleteFlag(Boolean.TRUE));
            zeroPendingDatas.addAll(alreadyRefund);
        }
        pendingSettlementData = pendingSettlementData.stream().filter(pendingSettlementOrder -> !filterOrderIds.contains(pendingSettlementOrder.getOrderId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(pendingSettlementData)) {
            log.info("当前待结算的订单都有未完成的售后");
            return Collections.EMPTY_LIST;
        }

        //可结算的订单，是否有未完成的售后
        List<String> refundOrderIds = getRefundOrderIds(pendingSettlementData);

        // 过滤有未完成的售后订单
        pendingSettlementData = pendingSettlementData.stream().filter(pendingSettlementOrder -> !refundOrderIds.contains(pendingSettlementOrder.getOrderId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(pendingSettlementData)) {
            log.info("当前待结算的订单都有未完成的售后");
            return Collections.EMPTY_LIST;
        }
        pendingSettlementData = pendingSettlementData.stream().filter(pendingSettlementOrder -> StrUtil.isBlank(pendingSettlementOrder.getSettelementRemark())).collect(Collectors.toList());
        return pendingSettlementData;
    }

    @NotNull
    private List<String> getRefundOrderIds(List<PendingSettlementOrder> pendingSettlementData) {
        List<String> orderIds = pendingSettlementData.stream().map(PendingSettlementOrder::getOrderId).collect(Collectors.toList());
        List<OrderRefund> orderRefundList = orderRefundRepository.getByOrderIdList(orderIds);
        List<String> refundOrderIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(orderRefundList)) {
            refundOrderIds.addAll(orderRefundList.stream().filter(
                            orderRefund -> !orderRefund.getSellerAuditStatus().equals(RefundAuditStatusEnum.SUPPLIER_REFUSE.getCode())
                                    && !orderRefund.getManagerConfirmStatus().equals(RefundAuditStatusEnum.PLATFORM_REFUSE.getCode())
                                    && !orderRefund.getManagerConfirmStatus().equals(RefundAuditStatusEnum.REFUND_SUCCESS.getCode())
                                    && orderRefund.getHasCancel().equals(Boolean.FALSE))
                    .map(OrderRefund::getOrderId).collect(Collectors.toList()));
        }
        return refundOrderIds;
    }
}
