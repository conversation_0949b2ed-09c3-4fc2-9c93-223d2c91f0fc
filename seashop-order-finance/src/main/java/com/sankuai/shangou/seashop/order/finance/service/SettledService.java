package com.sankuai.shangou.seashop.order.finance.service;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.OrderIdQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemCountQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettlementDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemCountResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledResp;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
public interface SettledService {

    /**
     * 已结算列表查询
     *
     * @param request
     * @return
     */
    BasePageResp<SettledResp> pageList(SettledQryReq request);


    /**
     * 已结算明细列表查询
     *
     * @param request
     * @return
     */
    BasePageResp<SettledItemResp> itemPageList(SettledItemQryReq request);

    /**
     * 已结算明细统计查询
     *
     * @param request
     * @return
     */
    SettledItemCountResp itemCount(SettledItemCountQryReq request);

    /**
     * 根据订单id查询结算详情
     *
     * @param request
     * @return
     */
    SettlementDetailResp getDetailByOrderId(OrderIdQryReq request);
}
