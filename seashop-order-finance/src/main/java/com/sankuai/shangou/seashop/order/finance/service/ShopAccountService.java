package com.sankuai.shangou.seashop.order.finance.service;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreateAccountReq;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
public interface ShopAccountService {

    /**
     * 创建店铺结算账户
     * @param createAccountReq 创建请求
     * @return 结果
     */
    BaseResp createAccount(CreateAccountReq createAccountReq);

    /**
     * 根据店铺ID获取店铺结算账户
     * @param req 店铺ID
     * @return 结算账户
     */
    String getShopAccountByShopId(Long req);
}
