package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.finance.service.SettledService;
import com.sankuai.shangou.seashop.order.thrift.finance.SettledQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.OrderIdQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemCountQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledItemQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettledQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettlementDetailResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemCountResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledItemResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettledResp;

/**
 * @author: lhx
 * @date: 2023/12/5/005
 * @description:
 */
@RestController
@RequestMapping("/finance/settledQuery")
public class SettledQueryFeignImpl implements SettledQueryFeign {

    @Resource
    private SettledService settledService;

    @PostMapping(value = "/pageList",consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<SettledResp>> pageList(@RequestBody SettledQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            req.valueInit();
            return settledService.pageList(req);
        });
    }

    @PostMapping(value = "/itemPageList",consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<SettledItemResp>> itemPageList(@RequestBody SettledItemQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("itemPageList", request,
                req -> {
//                    req.checkParameter();
                    return settledService.itemPageList(req);
                });
    }

    @PostMapping(value = "/itemCount",consumes = "application/json")
    @Override
    public ResultDto<SettledItemCountResp> itemCount(@RequestBody SettledItemCountQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("itemCount", request,
                req -> {
                    req.checkParameter();
                    return settledService.itemCount(req);
                });
    }

    @PostMapping(value = "/getDetailByOrderId",consumes = "application/json")
    @Override
    public ResultDto<SettlementDetailResp> getDetailByOrderId(@RequestBody OrderIdQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getDetailByOrderId", request,
                req -> {
                    req.checkParameter();
                    return settledService.getDetailByOrderId(req);
                });
    }
}
