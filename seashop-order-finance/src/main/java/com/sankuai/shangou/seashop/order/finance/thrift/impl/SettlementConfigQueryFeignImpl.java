package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.finance.service.SettlementConfigService;
import com.sankuai.shangou.seashop.order.thrift.finance.SettlementConfigQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettlementConfigResp;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@RestController
@RequestMapping("/finance/settlementConfigQuery")
public class SettlementConfigQueryFeignImpl implements SettlementConfigQueryFeign {

    @Resource
    private SettlementConfigService settlementConfigService;

    @PostMapping(value = "/getConfig", consumes = "application/json")
    @Override
    public ResultDto<SettlementConfigResp> getConfig() throws TException {
        return ThriftResponseHelper.responseInvoke("getConfig", null, req ->
                settlementConfigService.getConfig()
        );
    }
}
