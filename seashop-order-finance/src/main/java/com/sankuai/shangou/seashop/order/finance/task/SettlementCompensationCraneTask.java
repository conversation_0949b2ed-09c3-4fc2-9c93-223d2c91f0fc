package com.sankuai.shangou.seashop.order.finance.task;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import com.hishop.xxljob.client.boot.annotation.XxlRegister;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.utils.TransactionHelper;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderItem;
import com.sankuai.shangou.seashop.order.dao.core.domain.OrderPayRecord;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderItemRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderPayRecordRepository;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.domain.AccountDetail;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Finance;
import com.sankuai.shangou.seashop.order.dao.finance.domain.FinanceItem;
import com.sankuai.shangou.seashop.order.dao.finance.domain.PendingSettlementOrder;
import com.sankuai.shangou.seashop.order.dao.finance.repository.AccountDetailRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceItemRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.PendingSettlementOrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.PlatAccountRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.ShopAccountRepository;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayMethodEnum;
import com.sankuai.shangou.seashop.order.thrift.core.enums.pay.PayStatusEnum;
import com.sankuai.shangou.seashop.order.thrift.finance.enums.TransactionTypesEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.QueryShopPageReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopRespList;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2024/1/2/002
 * @description: 结算补偿定时任务
 */
@Slf4j
@Component
public class SettlementCompensationCraneTask {

    @Resource
    private OrderPayRecordRepository orderPayRecordRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private PendingSettlementOrderRepository pendingSettlementOrderRepository;
    @Resource
    private AccountDetailRepository accountDetailRepository;
    @Resource
    private OrderItemRepository orderItemRepository;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private ShopAccountRepository shopAccountRepository;
    @Resource
    private PlatAccountRepository platAccountRepository;
    @Resource
    private FinanceRepository financeRepository;
    @Resource
    private FinanceItemRepository financeItemRepository;

    /**
     * 一个默认没参数的字符串
     */
    private static final String NO_PARAMS = "0";

    /**
     * 补漏待结算数据(名称，用于记录日志)
     */
    private static final String PROCESS_PENDING_SETTLEMENT = "补漏待结算数据";
    /**
     * 补漏财务中间表数据(名称，用于记录日志)
     */
    private static final String PROCESS_FINANCE_ORDER_PAY = "补漏财务中间表数据";


    /**
     * 补漏待结算数据
     *
     * @param params: 可以传入startTime、endTime
     */
//    @Crane("ProcessPendingSettlement")
//    @XxlJob("processPendingSettlement")
//    @XxlRegister(cron = "0 0/10 * * * ?",
//            author = "snow",
//            jobDesc = "补漏待结算数据")
    public void processPendingSettlement(String params) {
        log.info("【补漏待结算数据】定时任务...start...params={}", params);

        List<OrderPayRecord> allOrderPays = getOrderPayRecords(PROCESS_PENDING_SETTLEMENT,params);
        if (CollUtil.isEmpty(allOrderPays)) {
            log.info("【补漏待结算数据】定时任务...allOrderPays没有数据...end...");
            return;
        }
        log.info("【补漏待结算数据】定时任务...allOrderPays={}", allOrderPays);

        //获取所有订单数据(支付成功的)
        List<String> allOrderIds = allOrderPays.stream().map(OrderPayRecord::getOrderId).collect(Collectors.toList());
        List<Order> allOrders = orderRepository.getByOrderIdList(allOrderIds);

        // 获取所有已写入待结算数据
        List<PendingSettlementOrder> allPendingSettlementOrders = pendingSettlementOrderRepository.listByOrderIdList(allOrderIds);
        final List<String> psOrderIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(allPendingSettlementOrders)) {
            psOrderIdList.addAll(allPendingSettlementOrders.stream().map(PendingSettlementOrder::getOrderId).collect(Collectors.toList()));
        }

        // 获取所有已结算数据
        List<AccountDetail> accountDetails = accountDetailRepository.listByOrderIdList(allOrderIds);
        final List<String> adOrderIdList = new ArrayList<>();
        if (CollUtil.isNotEmpty(accountDetails)) {
            adOrderIdList.addAll(accountDetails.stream().map(AccountDetail::getOrderId).collect(Collectors.toList()));
        }

        // 过滤已经结算的订单
        List<Order> orders = allOrders.stream()
                .filter(order -> !psOrderIdList.contains(order.getOrderId()) && !adOrderIdList.contains(order.getOrderId()))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(orders)) {
            log.info("【补漏待结算数据】定时任务...orderList没有数据...end...");
            return;
        }

        List<Long> shopIds = orders.stream().map(Order::getShopId).distinct().collect(Collectors.toList());
        List<String> orderIds = orders.stream().map(Order::getOrderId).collect(Collectors.toList());

        //  自营店铺查询处理
        QueryShopPageReq queryShopPageReq = new QueryShopPageReq();
        queryShopPageReq.setShopIds(shopIds);
        ShopRespList shopList = shopRemoteService.getShopList(queryShopPageReq);
        List<Long> selfShops = shopList.getShopRespList().stream().filter(ShopResp::getWhetherSelf).map(ShopResp::getId).collect(Collectors.toList());

        // 所有订单的明细信息
        List<OrderItem> orderDetails = orderItemRepository.getByOrderIdList(orderIds);

        // 数据处理
        List<PendingSettlementOrder> addList = getPendingSettlementOrders(allOrderPays, allOrders, allPendingSettlementOrders, orders, selfShops, orderDetails);

        log.info("【补漏待结算数据】定时任务...addList={}", addList);
        if (CollUtil.isNotEmpty(addList)) {

            TransactionHelper.doInTransaction(() -> {
                pendingSettlementOrderRepository.saveBatch(addList);

                //更新店铺资金账户
                Map<Long, List<PendingSettlementOrder>> shopPsoMap = addList.stream().collect(Collectors.groupingBy(PendingSettlementOrder::getShopId));
                shopPsoMap.forEach((k, v) -> {
                    BigDecimal amount = v.stream().map(PendingSettlementOrder::getSettlementAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    shopAccountRepository.updatePendingSettlement(k, amount);
                });

                ///更新平台资金账户
                BigDecimal amount = addList.stream().map(PendingSettlementOrder::getSettlementAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                platAccountRepository.updatePendingSettlement(amount);
            });
        }

        log.info("【补漏待结算数据】定时任务...end...");
    }

    /**
     * 补漏财务中间表数据
     *
     * @param params: 可以传入startTime、endTime
     */
//    @Crane("ProcessFinanceOrderPay")
//    @XxlJob("processFinanceOrderPay")
//    @XxlRegister(cron = "0/10 * * * ?",
//            author = "snow",
//            jobDesc = "补漏财务中间表数据")
    public void processFinanceOrderPay(String params) {
        log.info("【补漏财务中间表数据】定时任务...start...params={}", params);

        List<OrderPayRecord> allOrderPays = getOrderPayRecords(PROCESS_FINANCE_ORDER_PAY,params);
        if (CollUtil.isEmpty(allOrderPays)) {
            log.info("【补漏财务中间表数据】定时任务...allOrderPays没有数据...end...");
            return;
        }
        log.info("【补漏财务中间表数据】定时任务...allOrderPays={}", allOrderPays);

        //获取所有订单数据
        List<String> allOrderIds = allOrderPays.stream().map(OrderPayRecord::getOrderId).collect(Collectors.toList());
        if (CollUtil.isEmpty(allOrderIds)) {
            log.info("【补漏财务中间表数据】定时任务...allOrderIds没有数据...end...");
            return;
        }
        List<Order> allOrders = orderRepository.getByOrderIdList(allOrderIds);
        if (CollUtil.isEmpty(allOrders)) {
            log.info("【补漏财务中间表数据】定时任务...allOrders没有数据...end...");
            return;
        }

        //获取所有已写入待结算数据
        List<PendingSettlementOrder> allPendingSettlementOrders = pendingSettlementOrderRepository.listByOrderIdList(allOrderIds);

        //获取所有已写入财务中间表数据
        List<Finance> allFinances = financeRepository.listByOrderIdList(allOrderIds, TransactionTypesEnum.PAY.getCode());
        final List<String> allOrderIdsStr = new ArrayList<>();
        if (CollUtil.isNotEmpty(allFinances)) {
            allOrderIdsStr.addAll(allFinances.stream().map(Finance::getOrderId).collect(Collectors.toList()));
        }

        //获取待写入中间表的订单数据
        List<Order> orders = allOrders.stream().filter(o -> !allOrderIdsStr.contains(o.getOrderId())).collect(Collectors.toList());
        if (CollUtil.isEmpty(orders)) {
            log.info("【补漏财务中间表数据】定时任务...orders没有数据...end...");
            return;
        }

        List<String> orderIds = orders.stream().map(Order::getOrderId).collect(Collectors.toList());
        List<OrderItem> orderDetails = orderItemRepository.getByOrderIdList(orderIds);

        for (Order order : orders) {
            PendingSettlementOrder pendingItem = allPendingSettlementOrders.stream().filter(p -> p.getOrderId().equals(order.getOrderId())).findFirst().orElse(null);
            OrderPayRecord orderPayInfo = allOrderPays.stream().filter(p -> p.getOrderId().equals(order.getOrderId())).findFirst().orElse(null);

            Finance finance = getFinance(order, pendingItem, orderPayInfo);

            TransactionHelper.doInTransaction(() -> {
                financeRepository.save(finance);

                List<OrderItem> orderDetail = orderDetails.stream().filter(item -> item.getOrderId().equals(order.getOrderId())).collect(Collectors.toList());
                List<FinanceItem> addFinanceItemList = new ArrayList<>();
                for (OrderItem item : orderDetail) {
                    FinanceItem financeItem = getFinanceItem(finance.getId(), item);
                    addFinanceItemList.add(financeItem);
                }

                financeItemRepository.saveBatch(addFinanceItemList);
            });
        }

    }

    @Nullable
    private List<OrderPayRecord> getOrderPayRecords(String opName, String params) {
        Date now = new Date();
        Date payStartTime = DateUtil.offsetHour(now, -1);
        Date payEndTime = DateUtil.offsetMinute(now, -3);
        if (StrUtil.isNotBlank(params) && !NO_PARAMS.equals(params)) {
            JSONObject jsonObject = JSONUtil.parseObj(params);
            Object startTime = jsonObject.get("startTime");
            Object endTime = jsonObject.get("endTime");
            if (startTime != null) {
                payStartTime = DateUtil.parseDateTime(startTime.toString());
            }
            if (endTime != null) {
                payEndTime = DateUtil.parseDateTime(endTime.toString());
            }
        }
        log.info("【{}】定时任务...payStartTime={},payEndTime={}", opName, payStartTime, payEndTime);
        return orderPayRecordRepository.listByTimeAndStatus(payStartTime, payEndTime, PayStatusEnum.PAY_SUCCESS.getCode());
    }

    @NotNull
    private static FinanceItem getFinanceItem(Long financeId, OrderItem item) {
        FinanceItem financeItem = new FinanceItem();
        financeItem.setFinanceId(financeId);
        financeItem.setOrderId(item.getOrderId());
        financeItem.setProductId(item.getProductId());
        financeItem.setProductName(item.getProductName());
        financeItem.setSku(item.getSku());
        financeItem.setQuantity(item.getQuantity());
        financeItem.setOriginalPrice(item.getCostPrice());
        financeItem.setSalePrice(item.getSalePrice());
        financeItem.setTotalAmount(item.getSalePrice().multiply(new BigDecimal(item.getQuantity())));
        financeItem.setDiscountPrice(item.getDiscountAmount());
        financeItem.setFullDiscount(item.getFullDiscount());
        financeItem.setMoneyOff(item.getMoneyOff());
        financeItem.setCouponDiscount(item.getCouponDiscount());
        financeItem.setPlatCouponDiscount(BigDecimal.ZERO);
        financeItem.setCommisRate(item.getCommisRate());
        return financeItem;
    }

    @NotNull
    private static Finance getFinance(Order order, PendingSettlementOrder pendingItem, OrderPayRecord orderPayInfo) {
        Finance finance = new Finance();
        finance.setOrderId(order.getOrderId());
        finance.setAdapayId(null != orderPayInfo ? orderPayInfo.getPayNo() : "");
        finance.setPayId(null != orderPayInfo ? orderPayInfo.getId().toString() : "");
        finance.setType(TransactionTypesEnum.PAY.getCode());
        finance.setCreateDate(null != order.getPayDate() ? order.getPayDate() : new Date());
        finance.setShopId(order.getShopId());
        finance.setShopName(order.getShopName());
        finance.setUserId(order.getUserId());
        finance.setUserName(order.getUserName());
        finance.setTransactionId(StrUtil.isNotBlank(order.getGatewayOrderId()) ? order.getGatewayOrderId() : "");
        finance.setTotalAmount(order.getTotalAmount());
        finance.setFreight(order.getFreight());
        finance.setProductAmount(order.getProductTotalAmount());
        finance.setDiscountAmount(order.getCouponAmount());
        finance.setPlatDiscountAmount(BigDecimal.ZERO);
        finance.setFullDiscount(order.getDiscountAmount());
        finance.setMoneyOff(order.getMoneyOffAmount());
        finance.setIntegralDiscount(BigDecimal.ZERO);
        finance.setServiceAmount(null != pendingItem ? pendingItem.getChannelAmount() : BigDecimal.ZERO);
        finance.setSettlementAmount(null != pendingItem ? pendingItem.getSettlementAmount() : BigDecimal.ZERO);
        finance.setCommissionAmount(null != pendingItem ? pendingItem.getPlatCommission() : order.getCommissionTotalAmount());
        finance.setActualPayAmount(orderPayInfo.getPayAmount());
        return finance;
    }

    @NotNull
    private static List<PendingSettlementOrder> getPendingSettlementOrders(List<OrderPayRecord> orderPayRecords, List<Order> allOrders, List<PendingSettlementOrder> pendingSettlementOrders, List<Order> orderList, List<Long> selfShopId, List<OrderItem> orderItemList) {
        List<PendingSettlementOrder> addList = new ArrayList<>();
        for (Order order : orderList) {
            PendingSettlementOrder pendingSettlementOrder = new PendingSettlementOrder();

            // 判断是否是自营店铺
            Boolean selfFlag = selfShopId.contains(order.getShopId());

            pendingSettlementOrder.setSelfFlag(selfFlag);
            pendingSettlementOrder.setShopId(order.getShopId());
            pendingSettlementOrder.setShopName(order.getShopName());
            pendingSettlementOrder.setOrderId(order.getOrderId());
            pendingSettlementOrder.setFreightAmount(order.getFreight());
            pendingSettlementOrder.setTaxAmount(order.getTax());
            pendingSettlementOrder.setOrderType(order.getOrderType());
            pendingSettlementOrder.setRefundAmount(BigDecimal.ZERO);
            pendingSettlementOrder.setPlatCommissionReturn(BigDecimal.ZERO);
            pendingSettlementOrder.setDiscountAmount(BigDecimal.ZERO);

            /*
                 如果是【企业网银】支付的，手续费固定是10；
                 全部分账的时候：支付确认金额/交易总金额*（交易总金额*费率  需要四舍五入），然后得到的值四舍五入
                 部分分账的时候：确认金额/原交易金额*手续费总额（手续费总额=原交易金额×费率 需要四舍五入） ，然后得到的值四舍五入
             */
            BigDecimal charge = BigDecimal.ZERO;

            OrderPayRecord orderPay = orderPayRecords.stream().filter(record -> record.getOrderId().equals(order.getOrderId())).findFirst().orElse(null);
            List<OrderPayRecord> allPays = orderPayRecords.stream().filter(record -> record.getBatchNo().equals(orderPay.getBatchNo())).collect(Collectors.toList());
            if (null != order.getPayment() && order.getPayment().equals(PayMethodEnum.COMPANY_BANK.getCode())) {
                boolean isLast = allPays.get(allPays.size() - 1).getOrderId().equals(order.getOrderId());
                if (allPays.size() == 1) {
                    charge = BigDecimal.TEN;
                } else if (!isLast) {
                    BigDecimal totalAmount = allOrders.stream().filter(o -> allPays.contains(o.getOrderId()))
                            .map(o -> getOrderTotalAmount(o))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal totalCharge = BigDecimal.TEN;
                    charge = order.getActualPayAmount().divide(totalAmount).multiply(totalCharge).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else {
                    BigDecimal hasPendAmount = pendingSettlementOrders.stream().filter(pending -> allPays.contains(pending.getOrderId())).map(PendingSettlementOrder::getChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal hasAddAmount = addList.stream().filter(pending -> allPays.contains(pending.getOrderId())).map(PendingSettlementOrder::getChannelAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
                    charge = BigDecimal.TEN.subtract(hasPendAmount).subtract(hasAddAmount);
                }
            } else {
                if (allPays.size() == 1) {
                    BigDecimal baseCharge = getOrderTotalAmount(order).multiply(order.getSettlementCharge()).setScale(2, BigDecimal.ROUND_HALF_UP);
                    charge = order.getActualPayAmount().divide(getOrderTotalAmount(order)).multiply(baseCharge).setScale(2, BigDecimal.ROUND_HALF_UP);
                } else {
                    BigDecimal totalAmount = allOrders.stream().filter(o -> allPays.contains(o.getOrderId())).map(o -> getOrderTotalAmount(o))
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                    BigDecimal totalCharge = totalAmount.multiply(order.getSettlementCharge());
                    charge = order.getActualPayAmount().divide(totalAmount).multiply(totalCharge).setScale(2, BigDecimal.ROUND_HALF_UP);
                }
            }

            pendingSettlementOrder.setChannelAmount(charge);
            if (order.getActualPayAmount().compareTo(BigDecimal.ZERO) <= 0) {
                pendingSettlementOrder.setChannelAmount(BigDecimal.ZERO);
                charge = BigDecimal.ZERO;
            }
            pendingSettlementOrder.setProductsAmount(order.getProductTotalAmount().subtract(order.getCouponAmount()).subtract(order.getDiscountAmount()).subtract(order.getMoneyOffAmount()));

            List<OrderItem> orderItems = orderItemList.stream().filter(item -> item.getOrderId().equals(order.getOrderId())).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(orderItems)) {
                BigDecimal platCommission = orderItems.stream().map(item -> item.getRealTotalPrice().multiply(item.getCommisRate()).setScale(2, BigDecimal.ROUND_HALF_UP)).reduce(BigDecimal.ZERO, BigDecimal::add);
                pendingSettlementOrder.setPlatCommission(platCommission);
            }

            if (charge.compareTo(BigDecimal.ZERO) > 0) {
                //计算剩余可分账的金额 剩余佣金不够平台分佣,将剩余的结余给平台
                BigDecimal channelCommission = order.getActualPayAmount().subtract(pendingSettlementOrder.getChannelAmount());
                if (pendingSettlementOrder.getPlatCommission().compareTo(channelCommission) >= 0) {
                    pendingSettlementOrder.setPlatCommission(channelCommission);
                }
            }

            pendingSettlementOrder.setSettlementAmount(order.getActualPayAmount().subtract(pendingSettlementOrder.getPlatCommission())
                    .add(pendingSettlementOrder.getPlatCommissionReturn()).subtract(pendingSettlementOrder.getChannelAmount()));
            pendingSettlementOrder.setCreateTime(DateTime.now());
            if (null != order.getFinishDate()) {
                pendingSettlementOrder.setOrderFinishTime(order.getFinishDate());
            }
            pendingSettlementOrder.setPaymentTypeName(order.getPaymentTypeName());
            pendingSettlementOrder.setOrderAmount(order.getTotalAmount());

            addList.add(pendingSettlementOrder);
        }
        return addList;
    }

    /**
     * 商品应付+运费+税 - 优惠券金额 - 积分抵扣金额-满额减金额 (减掉积分抵扣部分)
     *
     * @param o
     * @return
     */
    @NotNull
    private static BigDecimal getOrderTotalAmount(Order o) {
        return o.getProductTotalAmount().add(o.getFreight()).add(o.getTax()).subtract(o.getCouponAmount()).subtract(o.getDiscountAmount()).subtract(o.getMoneyOffAmount());
    }
}
