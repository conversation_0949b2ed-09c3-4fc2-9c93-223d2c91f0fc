package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.finance.service.SettlementConfigService;
import com.sankuai.shangou.seashop.order.thrift.finance.SettlementConfigCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.SettlementConfigReq;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@RestController
@RequestMapping("/finance/settlementConfigCmd")
public class SettlementConfigCmdFeignImpl implements SettlementConfigCmdFeign {

    @Resource
    private SettlementConfigService settlementConfigService;

    @PostMapping(value = "/update", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> update(@RequestBody SettlementConfigReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("update", request, req -> {
            req.checkParameter();
            settlementConfigService.update(req);
            return BaseResp.of();
        });
    }
}
