package com.sankuai.shangou.seashop.order.finance.mq.publisher;

import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.exception.SystemException;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.FinanceMafkaConst;
import com.sankuai.shangou.seashop.order.finance.mq.model.OrderSettleMsgModel;
import com.sankuai.shangou.seashop.rocketmq.DefaultRocketMq;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2024/1/25/025
 * @description:
 */
@Slf4j
@Service
public class OrderSettleMsgProducer {

    /**
     * topic: seashop_order_settle_msg_topic
     * <p>
     * 消费组: seashop_order_settle_msg_consumer
     * <p>
     * 发送端: com.sankuai.sgb2b.seashop.order
     * <p>
     * 消费端: com.sankuai.sgb2b.seashop.erp
     */
//    @MafkaProducer(namespace = MafkaConst.DEFAULT_NAMESPACE, topic = FinanceMafkaConst.ORDER_SETTLE_MSG_TOPIC)
//    private IProducerProcessor producerProcessor;

    @Resource
    private DefaultRocketMq defaultRocketMq;



    public void sendMessage(List<OrderSettleMsgModel> dataList) {
        log.info("【mafka生产】【订单结算】发送订单结算消息, dataList: {}", dataList);
        try {
            List<String> dataStrList = dataList.stream().map(JsonUtil::toJsonString).collect(Collectors.toList());
            SendResult producerResult = defaultRocketMq.syncSend(FinanceMafkaConst.ORDER_SETTLE_MSG_TOPIC,dataStrList);
            log.info("【mafka生产】【订单结算】发送订单结算消息-producerResult：{}", JSONUtil.toJsonStr(producerResult));
        } catch (Exception e) {
            log.error("【mafka生产】【订单结算】发送订单结算消息异常", e);
            throw new SystemException("发送订单结算消息失败");
        }
    }

}
