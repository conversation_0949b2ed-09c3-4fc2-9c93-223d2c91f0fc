package com.sankuai.shangou.seashop.order.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.github.pagehelper.Page;
import com.sankuai.shangou.seashop.base.boot.common.BasePageParam;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.dao.core.domain.Order;
import com.sankuai.shangou.seashop.order.dao.core.repository.OrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.domain.PendingSettlementOrder;
import com.sankuai.shangou.seashop.order.dao.finance.model.PendingSettlementModel;
import com.sankuai.shangou.seashop.order.dao.finance.model.PendingSettlementOrderQryModel;
import com.sankuai.shangou.seashop.order.dao.finance.repository.PendingSettlementOrderRepository;
import com.sankuai.shangou.seashop.order.finance.service.PendingSettlementService;
import com.sankuai.shangou.seashop.order.finance.service.SettlementConfigService;
import com.sankuai.shangou.seashop.order.thrift.finance.request.OrderIdQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementOrderResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PlatCommissionResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.SettlementConfigResp;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.user.thrift.shop.request.ShopSimpleQueryReq;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleListResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopSimpleResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Service
@Slf4j
public class PendingSettlementServiceImpl implements PendingSettlementService {

    @Resource
    private PendingSettlementOrderRepository pendingSettlementOrderRepository;
    @Resource
    private OrderRepository orderRepository;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    @Lazy
    private SettlementConfigService settlementConfigService;

    /**
     * 获取待结算订单合计列表
     * <p>
     * 通过分组查询合并统计店铺维度的待结算订单数据
     *
     * @param request 查询条件
     * @return
     */
    @Override
    public BasePageResp<PendingSettlementResp> getPendingSettlementList(PendingSettlementQryReq request) {
        BasePageResp<PendingSettlementModel> pendingSettlementPage = pendingSettlementOrderRepository.getPendingSettlementList(request.buildPage(), JsonUtil.copy(request, PendingSettlementOrderQryModel.class));
        if (null == pendingSettlementPage || CollUtil.isEmpty(pendingSettlementPage.getData())) {
            return PageResultHelper.defaultEmpty(request);
        }

        // 没数据说明之前没查过店铺信息，需要去查一下店铺信息
        List<Long> shopIds = pendingSettlementPage.getData().stream().map(PendingSettlementModel::getShopId).collect(Collectors.toList());
        ShopSimpleListResp shopSimpleListResp = shopRemoteService.querySimpleList(
                ShopSimpleQueryReq.builder()
                        .shopIdList(shopIds).build()
        );
        final List<ShopSimpleResp> shopSimpleModelsFinal = null != shopSimpleListResp ? shopSimpleListResp.getList() : new ArrayList<>();

        SettlementConfigResp config = settlementConfigService.getConfig();
        Long settlementInterval = config.getSettlementInterval();
        DateTime startDate = DateUtil.date();
        if (null != settlementInterval) {
            DateTime now = DateUtil.date();
            startDate = DateUtil.offsetDay(now, -settlementInterval.intValue());
        }

        String settlementStartTimeStr = DateUtil.format(DateUtil.beginOfDay(DateUtil.offsetDay(startDate, 1)), "yyyy-MM-dd HH:mm:ss");
        DateTime tomorrow = DateUtil.tomorrow();
        String settlementTimeStr = DateUtil.format(DateUtil.beginOfDay(tomorrow), "yyyy/MM/dd HH:mm:ss");
        String settlementEndTimeStr = DateUtil.format(DateUtil.beginOfDay(tomorrow), "yyyy-MM-dd HH:mm:ss");

        return PageResultHelper.transfer(pendingSettlementPage, PendingSettlementResp.class, (source, target) -> {
            target.setShopId(source.getShopId());
            if (CollUtil.isNotEmpty(shopSimpleModelsFinal)) {
                shopSimpleModelsFinal.stream().filter(shopSimpleResp -> shopSimpleResp.getId().equals(source.getShopId())).findFirst().ifPresent(shopSimpleResp -> {
                    target.setShopName(shopSimpleResp.getShopName());
                });
            }
            target.setTotalAmount(source.getSettlementAmount());
            target.setSettlementTimeStr(settlementTimeStr);
            target.setSettlementStartTimeStr(settlementStartTimeStr);
            target.setSettlementEndTimeStr(settlementEndTimeStr);
        });
    }

    @Override
    public PlatCommissionResp getPlatCommission() {
        BigDecimal platCommission = pendingSettlementOrderRepository.getPlatCommission();
        PlatCommissionResp resp = new PlatCommissionResp();
        resp.setPlatCommission(platCommission);
        return resp;
    }

    /**
     * 获取待结算订单列表
     *
     * @param request 查询条件
     * @return
     */
    @Override
    public BasePageResp<PendingSettlementOrderResp> pageList(PendingSettlementOrderQryReq request) {

        PendingSettlementOrderQryModel qryModel = JsonUtil.copy(request, PendingSettlementOrderQryModel.class);
        BasePageParam basePageParam = request.buildPage();

        Page<PendingSettlementOrder> pendingSettlementOrderPage = pendingSettlementOrderRepository.pageList(basePageParam, qryModel);
        List<PendingSettlementOrder> pendingSettlementOrderList = pendingSettlementOrderPage.getResult();
        if (CollUtil.isNotEmpty(pendingSettlementOrderList)) {
            List<String> orderIdList = pendingSettlementOrderList.stream().map(PendingSettlementOrder::getOrderId).collect(Collectors.toList());
            List<Order> orderList = orderRepository.getByOrderIdList(orderIdList);
            return PageResultHelper.transfer(pendingSettlementOrderPage, PendingSettlementOrderResp.class,
                    (pendingSettlementOrder, pendingSettlementOrderResp) -> {
                        orderList.stream().filter(order -> order.getOrderId().equals(pendingSettlementOrderResp.getOrderId())).findFirst().ifPresent(order -> {
                            pendingSettlementOrderResp.setOrderStatus(order.getOrderStatus());
                        });
                        if (null != pendingSettlementOrderResp.getPaymentType()) {
                            PaymentTypeEnum paymentTypeEnum = PaymentTypeEnum.getByType(pendingSettlementOrderResp.getPaymentType());
                            if (null != paymentTypeEnum) {
                                pendingSettlementOrderResp.setPaymentTypeName(paymentTypeEnum.getName());
                            }
                        }
                        pendingSettlementOrderResp.setPayTime(pendingSettlementOrder.getPayDate());
                    });
        }
        return PageResultHelper.defaultEmpty(pendingSettlementOrderPage);
    }

    @Override
    public PendingSettlementOrderResp getDetailByOrderId(OrderIdQryReq request) {
        PendingSettlementOrder pendingSettlementOrder = pendingSettlementOrderRepository.getByOrderId(request.getOrderId());
        if (null != pendingSettlementOrder) {
            PendingSettlementOrderResp resp = JsonUtil.copy(pendingSettlementOrder, PendingSettlementOrderResp.class);
            resp.setPayTime(pendingSettlementOrder.getPayDate());
            return resp;
        }
        return null;
    }
}
