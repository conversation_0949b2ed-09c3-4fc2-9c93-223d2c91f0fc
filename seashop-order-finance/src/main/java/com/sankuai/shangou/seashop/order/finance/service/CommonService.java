package com.sankuai.shangou.seashop.order.finance.service;

import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositDetail;

/**
 * @author: lhx
 * @date: 2023/12/19/019
 * @description: 抽取出来一些公共的方法，用于其他模块调用，主要是为了一定程度的解耦和事务处理
 */
public interface CommonService {

    /**
     * 更新退款状态
     *
     * @param refundOrderId
     * @param refundStatus
     * @param reason
     */
    void updateRefundStatus(String refundOrderId, Integer refundStatus, String reason);

    /**
     * 新增保证金明细
     *
     * @param addCashDepositDetail
     * @param cashDepositDetailId
     * @param frozenFlag
     */
    void addCashDepositDetails(CashDepositDetail addCashDepositDetail, Long cashDepositDetailId, Boolean frozenFlag);

    /**
     * 新增保证金明细
     *
     * @param addCashDepositDetail
     * @param cashDepositDetailId
     * @param frozenFlag
     * @param refundFlag
     */
    void addCashDepositDetails(CashDepositDetail addCashDepositDetail, Long cashDepositDetailId, Boolean frozenFlag, Boolean refundFlag);

}
