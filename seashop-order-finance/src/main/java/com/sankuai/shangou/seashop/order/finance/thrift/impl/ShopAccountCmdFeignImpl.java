package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.finance.service.ShopAccountService;
import com.sankuai.shangou.seashop.order.thrift.finance.ShopAccountCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreateAccountReq;
import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@RestController
@RequestMapping("/finance/shopAccountCmd")
public class ShopAccountCmdFeignImpl implements ShopAccountCmdFeign {

    @Resource
    private ShopAccountService shopAccountService;

    @PostMapping(value = "/createAccount", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> createAccount(@RequestBody CreateAccountReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("createAccount", request, req ->
                shopAccountService.createAccount(req)
        );
    }
}
