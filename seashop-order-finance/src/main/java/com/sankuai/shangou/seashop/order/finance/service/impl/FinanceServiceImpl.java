package com.sankuai.shangou.seashop.order.finance.service.impl;

import java.math.BigDecimal;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.remote.ShopRemoteService;
import com.sankuai.shangou.seashop.order.dao.finance.domain.Finance;
import com.sankuai.shangou.seashop.order.dao.finance.domain.PlatAccount;
import com.sankuai.shangou.seashop.order.dao.finance.domain.ShopAccount;
import com.sankuai.shangou.seashop.order.dao.finance.repository.FinanceRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.PendingSettlementOrderRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.PlatAccountRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.ShopAccountRepository;
import com.sankuai.shangou.seashop.order.finance.service.FinanceService;
import com.sankuai.shangou.seashop.order.thrift.finance.request.FinanceQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceIndexResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceResp;
import com.sankuai.shangou.seashop.user.thrift.shop.response.ShopDetailResp;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@Service
@Slf4j
public class FinanceServiceImpl implements FinanceService {

    @Resource
    private PlatAccountRepository platAccountRepository;
    @Resource
    private ShopAccountRepository shopAccountRepository;
    @Resource
    private ShopRemoteService shopRemoteService;
    @Resource
    private PendingSettlementOrderRepository pendingSettlementOrderRepository;
    @Resource
    private FinanceRepository financeRepository;

    @Override
    public FinanceIndexResp getFinanceIndex(BaseIdReq request) {

        FinanceIndexResp financeIndexResp = new FinanceIndexResp();

        if (null == request || null == request.getId()) {
            PlatAccount platAccount = platAccountRepository.getPlatAccount();
            financeIndexResp.setSettled(platAccount.getSettled());
        } else {
            ShopAccount shopAccount = shopAccountRepository.getShopAccount(request.getId());
            if (null == shopAccount) {
                ShopDetailResp shopDetailResp = shopRemoteService.queryDetail(request.getId());
                shopAccount = new ShopAccount();
                shopAccount.setShopId(request.getId());
                shopAccount.setShopName(null != shopDetailResp ? shopDetailResp.getShopName() : "");
                shopAccount.setPendingSettlement(BigDecimal.ZERO);
                shopAccount.setSettled(BigDecimal.ZERO);
                shopAccount.setRemark("");
                shopAccountRepository.save(shopAccount);
                financeIndexResp.setSettled(BigDecimal.ZERO);
            }else{
                financeIndexResp.setSettled(shopAccount.getSettled());
            }
        }
        // 待结算金额
        BigDecimal pendingSettlementAmount = pendingSettlementOrderRepository.getPendingSettlementAmount(request.getId());
        financeIndexResp.setPendingSettlement(pendingSettlementAmount);

        return financeIndexResp;
    }

    @Override
    public FinanceResp getFinance(FinanceQueryReq req) {
        Finance finance = financeRepository.getOne(new LambdaQueryWrapper<Finance>().eq(Finance::getOrderId, req.getOrderId()).eq(Finance::getType, req.getType()));
        AssertUtil.throwIfNull(finance, "未查询到财务中间表数据");
        return JsonUtil.copy(finance, FinanceResp.class);
    }
}
