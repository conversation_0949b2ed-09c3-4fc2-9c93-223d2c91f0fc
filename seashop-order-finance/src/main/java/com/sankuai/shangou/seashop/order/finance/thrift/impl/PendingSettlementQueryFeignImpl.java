package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.finance.service.PendingSettlementService;
import com.sankuai.shangou.seashop.order.thrift.finance.PendingSettlementQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.OrderIdQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementOrderQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.PendingSettlementQryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementOrderResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PendingSettlementResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.PlatCommissionResp;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
@RestController
@RequestMapping("/finance/pendingSettlementQuery")
public class PendingSettlementQueryFeignImpl implements PendingSettlementQueryFeign {

    @Resource
    private PendingSettlementService pendingSettlementService;

    @PostMapping(value = "/getPendingSettlementList",consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<PendingSettlementResp>> getPendingSettlementList(@RequestBody PendingSettlementQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getPendingSettlementList", request, req ->
                pendingSettlementService.getPendingSettlementList(req));
    }

    @RequestMapping(value = "/getPlatCommission")
    @Override
    public ResultDto<PlatCommissionResp> getPlatCommission() throws TException {
        return ThriftResponseHelper.responseInvoke("getPlatCommission", null, req ->
                pendingSettlementService.getPlatCommission());
    }

    @PostMapping(value = "/pageList",consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<PendingSettlementOrderResp>> pageList(@RequestBody PendingSettlementOrderQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            req.checkParameter();
            return pendingSettlementService.pageList(req);
        });
    }

    @PostMapping(value = "/getDetailByOrderId",consumes = "application/json")
    @Override
    public ResultDto<PendingSettlementOrderResp> getDetailByOrderId(@RequestBody OrderIdQryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getDetailByOrderId", request, req -> {
            req.checkParameter();
            return pendingSettlementService.getDetailByOrderId(req);
        });
    }
}
