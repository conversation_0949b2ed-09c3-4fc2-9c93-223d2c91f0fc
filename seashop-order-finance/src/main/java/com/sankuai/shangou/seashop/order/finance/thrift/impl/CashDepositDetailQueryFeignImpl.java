package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import java.util.List;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositDetailService;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositDetailQueryFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CashDepositDetailQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositDetailListResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositDetailResp;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@RestController
@RequestMapping("/finance/cashDepositDetail")
public class CashDepositDetailQueryFeignImpl implements CashDepositDetailQueryFeign {

    @Resource
    private CashDepositDetailService cashDepositDetailService;

    @PostMapping(value = "/getPayListByCashDepositId",consumes = "application/json")
    @Override
    public ResultDto<CashDepositDetailListResp> getPayListByCashDepositId(@RequestBody BaseIdReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("getPayListByCashDepositId", request, req -> {
            req.checkParameter();
            List<CashDepositDetailResp> cashDepositDetailRespList = cashDepositDetailService.getPayListByCashDepositId(req.getId());
            CashDepositDetailListResp cashDepositDetailListResp = new CashDepositDetailListResp();
            cashDepositDetailListResp.setList(cashDepositDetailRespList);
            return cashDepositDetailListResp;
        });
    }

    @PostMapping(value = "/pageList",consumes = "application/json")
    @Override
    public ResultDto<BasePageResp<CashDepositDetailResp>> pageList(@RequestBody CashDepositDetailQueryReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("pageList", request, req -> {
            req.checkParameter();
            return cashDepositDetailService.pageList(req);
        });
    }

}
