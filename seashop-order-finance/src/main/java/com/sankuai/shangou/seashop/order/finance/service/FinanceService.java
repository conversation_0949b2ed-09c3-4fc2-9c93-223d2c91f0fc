package com.sankuai.shangou.seashop.order.finance.service;

import com.sankuai.shangou.seashop.base.boot.request.BaseIdReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.FinanceQueryReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceIndexResp;
import com.sankuai.shangou.seashop.order.thrift.finance.response.FinanceResp;

/**
 * @author: lhx
 * @date: 2023/12/4/004
 * @description:
 */
public interface FinanceService {

    /**
     * 获取结算账号相关信息
     *
     * @param request
     * @return
     */
    FinanceIndexResp getFinanceIndex(BaseIdReq request);

    /**
     * 获取财务中间表数据
     *
     * @param req 请求体
     * @return 查询结果
     */
    FinanceResp getFinance(FinanceQueryReq req);
}
