package com.sankuai.shangou.seashop.order.finance.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;

import java.util.Date;

/**
 * @author: lhx
 * @date: 2023/12/22/022
 * @description:
 */
public class OrderNoUtil {

    private static final String ORDER_NO_DATE_FORMAT = "yyyyMMddHHmmssSSS";

    /**
     * 默认随机范围
     */
    private static final Long DEFAULT_RANDOM_MIN_RANGE = 1000L;
    private static final Long DEFAULT_RANDOM_MAX_RANGE = 9999L;

    public static String getAccountNo(Date date) {
        return DateUtil.format(date, ORDER_NO_DATE_FORMAT) + RandomUtil.randomLong(DEFAULT_RANDOM_MIN_RANGE, DEFAULT_RANDOM_MAX_RANGE);
    }
}
