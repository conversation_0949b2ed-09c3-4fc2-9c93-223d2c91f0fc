package com.sankuai.shangou.seashop.order.finance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.sankuai.shangou.seashop.base.boot.response.BasePageResp;
import com.sankuai.shangou.seashop.base.boot.response.PageResultHelper;
import com.sankuai.shangou.seashop.base.boot.utils.AssertUtil;
import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.leaf.LeafService;
import com.sankuai.shangou.seashop.order.common.constant.FinanceLeafConst;
import com.sankuai.shangou.seashop.order.common.constant.OrderConst;
import com.sankuai.shangou.seashop.order.common.remote.PayRemoteService;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDeposit;
import com.sankuai.shangou.seashop.order.dao.finance.domain.CashDepositPay;
import com.sankuai.shangou.seashop.order.dao.finance.model.ShopIdListModel;
import com.sankuai.shangou.seashop.order.dao.finance.repository.CashDepositPayRepository;
import com.sankuai.shangou.seashop.order.dao.finance.repository.CashDepositRepository;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositService;
import com.sankuai.shangou.seashop.order.thrift.core.response.OrderPayCreateResp;
import com.sankuai.shangou.seashop.order.thrift.finance.request.CreatePaymentReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ShopIdListReq;
import com.sankuai.shangou.seashop.order.thrift.finance.response.CashDepositResp;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.CardTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PayStateEnums;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.PaymentTypeEnum;
import com.sankuai.shangou.seashop.pay.thrift.core.request.PayPaymentCreateReq;
import com.sankuai.shangou.seashop.pay.thrift.core.request.dto.PayPaymentCreateExpendDto;
import com.sankuai.shangou.seashop.pay.thrift.core.response.PayPaymentCreateResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/27/027
 * @description:
 */
@Service
@Slf4j
public class CashDepositServiceImpl implements CashDepositService {

    @Resource
    private CashDepositRepository cashDepositRepository;
    @Resource
    private LeafService leafService;
    @Resource
    private PayRemoteService payRemoteService;

    @Value("${deposit.callback}")
    private String callBackUrl;

    @Resource
    private CashDepositPayRepository cashDepositPayRepository;

    /**
     * 保证金发起支付开关
     */
    @Value("${finance.create.payment.switch:'T'}")
    private String financeCreatePaymentSwitch;

    @Override
    public BasePageResp<CashDepositResp> queryListByShopId(ShopIdListReq req) {

        BasePageResp<CashDeposit> cashDepositModelBasePageResp = cashDepositRepository.queryByShopIdList(req.buildPage(), JsonUtil.copy(req, ShopIdListModel.class));
        if (null == cashDepositModelBasePageResp) {
            return PageResultHelper.defaultEmpty(req.buildPage());
        }
        return PageResultHelper.transfer(cashDepositModelBasePageResp, CashDepositResp.class);
    }

    @Override
    public CashDepositResp queryOneByShopId(Long shopId) {
        CashDeposit cashDeposit = cashDepositRepository.queryOneByShopId(shopId);
        if (null != cashDeposit) {
            return JsonUtil.copy(cashDeposit, CashDepositResp.class);
        }
        // 如果没有查到，插入一个空对象(之前是在缴纳保证金的地方才插入，查看详情那里会报错没有CashDepositId)，返回一个空的对象
        CashDeposit param = new CashDeposit();
        param.setShopId(shopId);
        param.setCurrentBalance(BigDecimal.ZERO);
        param.setTotalBalance(BigDecimal.ZERO);
        param.setDate(new Date());
        param.setEnableLabels(Boolean.TRUE);
        cashDepositRepository.insert(param);
        return JsonUtil.copy(param, CashDepositResp.class);
    }

    @Override
    public OrderPayCreateResp createPayment(CreatePaymentReq request) {
        AssertUtil.throwIfTrue("F".equals(financeCreatePaymentSwitch), "保证金支付开关已关闭，无法发起支付");
        log.info("createPayment.request：{}", JSONUtil.toJsonStr(request));

        // 由前缀+雪花+店铺ID组成
        String orderNo = FinanceLeafConst.CASH_DEPOSIT_PAY_ORDER_ID_PREFIX
                + leafService.generateNoBySnowFlake(FinanceLeafConst.CASH_DEPOSIT_PAY_ORDER_ID)
                + request.getShopId();

        // 创建支付单
        CashDepositPay cashDepositPay = new CashDepositPay();
        cashDepositPay.setPayId(orderNo);
        cashDepositPay.setPayStatus(PayStateEnums.UNPAID.getStatus());
        cashDepositPay.setPayment(OrderConst.CASH_DEPOSIT_PAY_ALIPAY_NAME);

        PayPaymentCreateReq payPaymentCreateReq = new PayPaymentCreateReq();
        payPaymentCreateReq.setOrderId(orderNo);
        payPaymentCreateReq.setPayAmount(request.getPayAmount());
        payPaymentCreateReq.setPaymentType(request.getPaymentType());
        payPaymentCreateReq.setBusinessType(BusinessTypeEnum.BAIL.getType());
        payPaymentCreateReq.setGoodsTitle(OrderConst.CASH_DEPOSIT_PAY_GOODS_DESC);
        payPaymentCreateReq.setGoodsDesc(OrderConst.CASH_DEPOSIT_PAY_GOODS_DESC);
        payPaymentCreateReq.setDeviceIp(request.getDeviceIp());

        // B2B支付封装对象
        if (PaymentTypeEnum.COMPANY_BANK.getType().equals(request.getPaymentType())) {
            PayPaymentCreateExpendDto payPaymentCreateExpendDto = new PayPaymentCreateExpendDto();
            payPaymentCreateExpendDto.setAcctIssrId(request.getBankCode());
            payPaymentCreateExpendDto.setCardType(CardTypeEnum.DEBIT.getType());
            payPaymentCreateExpendDto.setClientIp(request.getDeviceIp());
            payPaymentCreateExpendDto.setCallbackUrl(callBackUrl);

            payPaymentCreateReq.setExpend(payPaymentCreateExpendDto);

            // B2B支付时，支付方式存银行编码
            cashDepositPay.setPayment(request.getBankCode());
        }

        log.info("createPayment创建保证金支付单paymentCreate，请求参数：{}", JSONUtil.toJsonStr(payPaymentCreateReq));
        PayPaymentCreateResp payPaymentCreateResp = payRemoteService.createPayment(payPaymentCreateReq);
        log.info("createPayment创建保证金支付单paymentCreate，响应参数：{}", JSONUtil.toJsonStr(payPaymentCreateResp));

        cashDepositPay.setAdapayId(payPaymentCreateResp.getChannelPayId());
        cashDepositPayRepository.save(cashDepositPay);

        return JsonUtil.copy(payPaymentCreateResp, OrderPayCreateResp.class);
    }

    @Override
    public List<CashDepositResp> queryByShopId(ShopIdListReq req) {
        if (CollUtil.isEmpty(req.getShopIdList())) {
            return Collections.emptyList();
        }
        List<CashDeposit> dbList = cashDepositRepository.queryByShopIdList(req.getShopIdList());
        return JsonUtil.copyList(dbList, CashDepositResp.class);
    }
}
