package com.sankuai.shangou.seashop.order.finance.thrift.impl;

import javax.annotation.Resource;

import org.apache.thrift.TException;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.sankuai.shangou.seashop.base.boot.response.BaseResp;
import com.sankuai.shangou.seashop.base.boot.response.ResultDto;
import com.sankuai.shangou.seashop.base.boot.response.ThriftResponseHelper;
import com.sankuai.shangou.seashop.base.utils.LockHelper;
import com.sankuai.shangou.seashop.order.common.constant.FinanceLockConst;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositDetailService;
import com.sankuai.shangou.seashop.order.thrift.finance.CashDepositDetailCmdFeign;
import com.sankuai.shangou.seashop.order.thrift.finance.request.ApplyRefundReq;
import com.sankuai.shangou.seashop.order.thrift.finance.request.DeductionReq;

/**
 * @author: lhx
 * @date: 2023/11/28/028
 * @description:
 */
@RestController
@RequestMapping("/finance/cashDepositDetail")
public class CashDepositDetailCmdFeignImpl implements CashDepositDetailCmdFeign {

    @Resource
    private CashDepositDetailService cashDepositDetailService;

    @PostMapping(value = "/deduction", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> deduction(@RequestBody DeductionReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("deduction", request, req -> {
            req.checkParameter();
            String lockKey = String.format(FinanceLockConst.LOCK_CASH_DEPOSIT_DETAIL, req.getCashDepositDetailId());
            LockHelper.lock(lockKey, () -> {
                cashDepositDetailService.deduction(req);
            });
            return BaseResp.of();
        });
    }

    @PostMapping(value = "/applyRefund", consumes = "application/json")
    @Override
    public ResultDto<BaseResp> applyRefund(@RequestBody ApplyRefundReq request) throws TException {
        return ThriftResponseHelper.responseInvoke("applyRefund", request, req -> {
            req.checkParameter();
            String lockKey = String.format(FinanceLockConst.LOCK_CASH_DEPOSIT_DETAIL, req.getId());
            LockHelper.lock(lockKey,()->{
                cashDepositDetailService.applyRefund(req);
            });
            return BaseResp.of();
        });
    }
}
