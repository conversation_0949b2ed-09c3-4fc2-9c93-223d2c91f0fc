package com.sankuai.shangou.seashop.order.finance.mq.listener;

import java.nio.charset.StandardCharsets;

import javax.annotation.Resource;

import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.order.common.constant.FinanceMafkaConst;
import com.sankuai.shangou.seashop.order.finance.mq.model.PaymentResultModel;
import com.sankuai.shangou.seashop.order.finance.service.CashDepositPayService;
import com.sankuai.shangou.seashop.pay.thrift.core.enums.BusinessTypeEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * @author: lhx
 * @date: 2023/11/30/030
 * @description:
 */
@Slf4j
@Component
//@MafkaConsumer(namespace = MafkaConst.DEFAULT_NAMESPACE,
//        topic = FinanceMafkaConst.CASH_DEPOSIT_PAY_STATUS_CHANGE_TOPIC,
//        group = FinanceMafkaConst.CASH_DEPOSIT_PAY_STATUS_CHANGE_CONSUMER)
@RocketMQMessageListener(topic = FinanceMafkaConst.CASH_DEPOSIT_PAY_STATUS_CHANGE_TOPIC  + "_${spring.profiles.active}"
        , consumerGroup = FinanceMafkaConst.CASH_DEPOSIT_PAY_STATUS_CHANGE_CONSUMER + "_${spring.profiles.active}")
public class CashDepositPayStatusListener implements RocketMQListener<MessageExt> {

    @Resource
    private CashDepositPayService cashDepositPayService;

    @Override
    public void onMessage(MessageExt message) {
        String body = new String(message.getBody(), StandardCharsets.UTF_8);
        log.info("【mafka消费】【支付状态】消息内容为: {}", body);
        try {
            PaymentResultModel paymentResultModel = JsonUtil.parseObject(body, PaymentResultModel.class);
            Integer businessType = paymentResultModel.getBusinessType();
            // 只消费保证金的消息
            if (BusinessTypeEnum.BAIL.getType().equals(businessType)) {
                cashDepositPayService.updateStatus(paymentResultModel);
            }
            else {
                log.info("【mafka消费】【支付状态】消息内容,不是保证金的消息，不消费.【body】:{}", body);
            }
        }
        catch (Exception e) {
            log.error("【mafka消费】【支付状态】消息内容解析失败", e);
            throw new RuntimeException(e);
        }
    }
}
