package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseWXMenu;
import com.sankuai.shangou.seashop.base.dao.core.domain.SellerTaskInfo;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseWXMenuMapper;
import com.sankuai.shangou.seashop.base.dao.core.mapper.SellerTaskInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class BaseWXMenuRepository extends ServiceImpl<BaseWXMenuMapper, BaseWXMenu> {

    public Integer create(BaseWXMenu wxMenu) {
        save(wxMenu);
        return Math.toIntExact(wxMenu.getId());
    }

    public boolean update(BaseWXMenu wxMenu) {
        return updateById(wxMenu);
    }

    public BaseWXMenu queryWXMenuById(Long id){
        return getById(id);
    }

    public List<BaseWXMenu> queryWXMenus(){
        return list();
    }

    public Boolean delete(Long id) {
        return removeById(id);
    }
}
