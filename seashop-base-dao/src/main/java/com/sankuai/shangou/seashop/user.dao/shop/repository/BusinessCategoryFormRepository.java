package com.sankuai.shangou.seashop.user.dao.shop.repository;

import java.util.Collections;
import java.util.List;

import com.sankuai.shangou.seashop.user.dao.shop.mapper.ext.BusinessCategoryFormExtMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryForm;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.BusinessCategoryFormMapper;

import javax.annotation.Resource;

/**
 * @description: 分类申请仓库类
 * @author: LXH
 **/
@Repository
public class BusinessCategoryFormRepository extends ServiceImpl<BusinessCategoryFormMapper, BusinessCategoryForm> {
    @Resource
    private BusinessCategoryFormExtMapper businessCategoryFormExtMapper;


    /**
     * 根据店铺id和表单id的集合 查询分类申请表单信息
     *
     * @param shopId  店铺id
     * @param formIds 表单id的集合
     * @return 分类申请表单信息
     */
    public List<BusinessCategoryForm> listByShopIdAndFormIds(Long shopId, List<Long> formIds) {
        if (shopId == null || CollectionUtils.isEmpty(formIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<BusinessCategoryForm>().eq(BusinessCategoryForm::getShopId, shopId)
                .in(BusinessCategoryForm::getFormId, ids)), formIds);
    }


    public List<BusinessCategoryForm> listByShopIdAndCategoryIds(Long id, List<Long> categoryIds) {
        if (id == null || CollectionUtils.isEmpty(categoryIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<BusinessCategoryForm>().eq(BusinessCategoryForm::getShopId, id)
                .in(BusinessCategoryForm::getCategoryId, ids)), categoryIds);
    }

    public List<BusinessCategoryForm> listByApplyStatus(Integer applyStatus) {
        return businessCategoryFormExtMapper.listByApplyStatus(applyStatus);
    }

    public List<BusinessCategoryForm> queryByShopId(Long shopId) {
        return lambdaQuery().eq(BusinessCategoryForm::getShopId, shopId).list();
    }
}
