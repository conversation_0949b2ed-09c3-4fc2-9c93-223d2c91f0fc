package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategory;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategoryExample;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseArticleCategoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class ArticleCategoryRepository {
    @Resource
    private BaseArticleCategoryMapper articleCategoryMapper;

    public Long create(BaseArticleCategory category) {
        articleCategoryMapper.insert(category);
        return category.getId();
    }

    public Boolean update(BaseArticleCategory category) {
        articleCategoryMapper.updateByPrimaryKeySelective(category);
        return true;
    }

    public Boolean deletes(List<Long> ids) {
        BaseArticleCategoryExample example = new BaseArticleCategoryExample();
        BaseArticleCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        articleCategoryMapper.deleteByExample(example);
        return true;
    }

    public List<BaseArticleCategory> query(BaseArticleCategoryExample example) {
        List<BaseArticleCategory> list = articleCategoryMapper.selectByExample(example);
        return list;
    }


    public BaseArticleCategory getById(Long id) {
        BaseArticleCategory result = articleCategoryMapper.selectByPrimaryKey(id);
        return result;
    }

    public List<BaseArticleCategory> getChildsById(Long id) {
        BaseArticleCategoryExample example = new BaseArticleCategoryExample();
        BaseArticleCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andParentCategoryIdEqualTo(id);
        List<BaseArticleCategory> result = articleCategoryMapper.selectByExample(example);
        return result;
    }
}
