package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShippingFreeRegion;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ShippingFreeRegionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@Repository
@Slf4j
public class ShippingFreeRegionRepository extends ServiceImpl<ShippingFreeRegionMapper, ShippingFreeRegion> {

    public List<ShippingFreeRegion> getByTemplateIdList(List<Long> templateIdList) {
        LambdaQueryWrapper<ShippingFreeRegion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ShippingFreeRegion::getTemplateId, templateIdList);
        return baseMapper.selectList(queryWrapper);
    }

    public ShippingFreeRegion selectById(Long id){
        return this.getById(id);
    }
}
