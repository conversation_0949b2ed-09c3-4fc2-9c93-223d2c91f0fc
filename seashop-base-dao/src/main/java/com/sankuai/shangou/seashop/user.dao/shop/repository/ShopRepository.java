package com.sankuai.shangou.seashop.user.dao.shop.repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.util.StringUtil;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.shop.domain.Shop;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopMapper;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ext.ShopManagerMapper;
import com.sankuai.shangou.seashop.user.dao.shop.model.ShopModel;
import com.sankuai.shangou.seashop.user.dao.shop.model.ShopQueryModel;

import cn.hutool.core.collection.CollectionUtil;

/**
 * @description: 店铺仓库类
 * @author: LXH
 **/
@Repository
public class ShopRepository extends ServiceImpl<ShopMapper, Shop> {
    @Resource
    private ShopMapper shopMapper;
    @Resource
    private ShopManagerMapper shopManagerMapper;

    public Shop selectById(Long id ){
        return shopMapper.selectById(id);
    }
    public List<Shop> getList(Shop shop, List<Long> shopIds) {
        QueryWrapper<Shop> queryWrapper = new QueryWrapper<>();
        if (shop != null) {
            if (!StringUtil.isEmpty(shop.getShopName())) {
                queryWrapper.lambda().like(Shop::getShopName, shop.getShopName());
            }
        }
        if (!CollectionUtil.isEmpty(shopIds)) {
            queryWrapper.lambda().in(Shop::getId, shopIds);
        }
        return shopMapper.selectList(queryWrapper);

    }

    public Shop getByShopName(String shopName) {
        QueryWrapper<Shop> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Shop::getShopName, shopName);
        return shopMapper.selectOne(queryWrapper);
    }

    public List<ShopModel> getList(ShopQueryModel shopQueryDto) {
        if (shopQueryDto == null) {
            return null;
        }
        return shopManagerMapper.selectList(shopQueryDto);
    }

    public List<String> getCellPhoneList(List<Long> longs) {
        if (CollectionUtil.isEmpty(longs)) {
            return new ArrayList<>();
        }
        return shopManagerMapper.getCellPhoneList(longs);
    }

    public List<Member> getShopIdsByUserIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return shopManagerMapper.getShopIdsByUserIds(ids);
    }


    public List<Member> getOpenShopIdsByUserIds(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return shopManagerMapper.getOpenShopIdsByUserIds(ids);
    }

    public List<Shop> listByUpdateTime(Date updateTime) {
        LambdaQueryWrapper<Shop> queryWrapper = new LambdaQueryWrapper<>();
        if (updateTime != null) {
            queryWrapper.ge(Shop::getUpdateTime, updateTime);
        }
        return shopMapper.selectList(queryWrapper);
    }
}
