package com.sankuai.shangou.seashop.user.dao.account.mapper.ext;

import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.model.ManagerExtModel;
import com.sankuai.shangou.seashop.user.dao.account.model.ManagerQueryModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ManagerExtMapper {
    List<ManagerExtModel> selectExtList(@Param("query") ManagerQueryModel manager);

    List<Member> getManagerIdsByUserIds(List<Long> userIds);
}