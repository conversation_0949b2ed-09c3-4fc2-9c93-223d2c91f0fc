package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2023-11-29
 */
@Getter
@Setter
@TableName("xxl_job_log")
public class JobLogInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "job_id")
    private Long jobId;

    @TableField("trigger_time")
    private Date triggerTime;

    /**
     * 调度状态
     */
    @TableField("trigger_code")
    private Integer triggerCode;
    @TableField("handle_code")
    private Integer handleCode;

}
