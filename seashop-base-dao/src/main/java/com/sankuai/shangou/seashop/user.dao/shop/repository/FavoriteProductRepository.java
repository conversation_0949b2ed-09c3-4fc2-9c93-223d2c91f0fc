package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.user.dao.account.domain.Favorite;
import com.sankuai.shangou.seashop.user.dao.account.mapper.FavoriteMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/28 9:58
 */
@Repository
public class FavoriteProductRepository extends ServiceImpl<FavoriteMapper, Favorite> {

    @Resource
    private FavoriteMapper favoriteMapper;

    public Favorite selectById(Long id) {
        return favoriteMapper.selectById(id);
    }

    public int insert(Favorite favorite){
        return favoriteMapper.insert(favorite);
    }

    public int deleteBatchIds(List<Long> ids){
        return favoriteMapper.deleteBatchIds(ids);
    }

    public void deleteByProductIdAndUserId(String productId, Long userId){
        QueryWrapper<Favorite> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Favorite::getProductId, productId).eq(Favorite::getUserId, userId);
        favoriteMapper.delete(queryWrapper);
    }

    public void delete(List<String> productIdList) {
        QueryWrapper<Favorite> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(Favorite::getProductId, productIdList);
        favoriteMapper.delete(queryWrapper);
    }

    public Page<Favorite> pageFavoriteProduct(Favorite favorite, Integer pageNo, Integer pageSize){
        Page<Favorite> favoritePage = PageHelper.startPage(pageNo, pageSize);
        QueryWrapper<Favorite> queryWrapper = new QueryWrapper<>();
        if(favorite.getUserId() != null){
            queryWrapper.lambda().eq(Favorite::getUserId, favorite.getUserId());
        }
        if (favorite.getProductId() != null){
            queryWrapper.lambda().eq(Favorite::getProductId, favorite.getProductId());
        }
        queryWrapper.lambda().orderByDesc(Favorite::getDate);
        List<Favorite> list = favoriteMapper.selectList(queryWrapper);
        return favoritePage;
    }

    public int countFavoriteProductByUserId(Long userId) {
        if(userId == null){
            return 0;
        }
        QueryWrapper<Favorite> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(Favorite::getUserId, userId);
        return Math.toIntExact(favoriteMapper.selectCount(queryWrapper));
    }

    public List<Favorite> getByUpdateTime(Date updateTime) {
        LambdaQueryWrapper<Favorite> queryWrapper = new LambdaQueryWrapper<>();
        if (updateTime != null) {
            queryWrapper.ge(Favorite::getUpdateTime, updateTime);
        }

        return baseMapper.selectList(queryWrapper);
    }
}
