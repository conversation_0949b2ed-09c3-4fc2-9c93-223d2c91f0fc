package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseModuleProduct;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseModuleProductExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseModuleProductMapper {
    long countByExample(BaseModuleProductExample example);

    int deleteByExample(BaseModuleProductExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseModuleProduct record);

    int insertSelective(BaseModuleProduct record);

    List<BaseModuleProduct> selectByExample(BaseModuleProductExample example);

    BaseModuleProduct selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BaseModuleProduct record, @Param("example") BaseModuleProductExample example);

    int updateByExample(@Param("record") BaseModuleProduct record, @Param("example") BaseModuleProductExample example);

    int updateByPrimaryKeySelective(BaseModuleProduct record);

    int updateByPrimaryKey(BaseModuleProduct record);
}