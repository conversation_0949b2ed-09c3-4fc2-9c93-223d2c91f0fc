package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticle;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleExample;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseArticleMapper {
    long countByExample(BaseArticleExample example);

    int deleteByExample(BaseArticleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseArticleWithBLOBs record);

    int insertSelective(BaseArticleWithBLOBs record);

    List<BaseArticleWithBLOBs> selectByExampleWithBLOBs(BaseArticleExample example);

    List<BaseArticle> selectByExample(BaseArticleExample example);

    BaseArticleWithBLOBs selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BaseArticleWithBLOBs record, @Param("example") BaseArticleExample example);

    int updateByExampleWithBLOBs(@Param("record") BaseArticleWithBLOBs record, @Param("example") BaseArticleExample example);

    int updateByExample(@Param("record") BaseArticle record, @Param("example") BaseArticleExample example);

    int updateByPrimaryKeySelective(BaseArticleWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(BaseArticleWithBLOBs record);

    int updateByPrimaryKey(BaseArticle record);
}