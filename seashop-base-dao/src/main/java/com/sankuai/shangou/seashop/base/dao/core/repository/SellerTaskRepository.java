package com.sankuai.shangou.seashop.base.dao.core.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.dao.core.domain.SellerTaskInfo;
import com.sankuai.shangou.seashop.base.dao.core.mapper.SellerTaskInfoMapper;
import com.sankuai.shangou.seashop.base.dao.core.po.task.QueryTaskPo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class SellerTaskRepository extends ServiceImpl<SellerTaskInfoMapper, SellerTaskInfo> {

    /**
     * 根据ID修改任务信息，传入的对象是只需要修改的部分字段
     * @param task 任务信息，需要修改的部分字段
     */
    public void updateTaskById(Long taskId, SellerTaskInfo task) {
        LambdaUpdateWrapper<SellerTaskInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(SellerTaskInfo::getId, taskId);
        this.update(task, wrapper);
    }


    public List<SellerTaskInfo> getByCondition(QueryTaskPo queryParam) {
        LambdaQueryWrapper<SellerTaskInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(queryParam.getOperatorIdEq() != null, SellerTaskInfo::getOperatorId, queryParam.getOperatorIdEq())
                .eq(StrUtil.isNotBlank(queryParam.getEnvEq()), SellerTaskInfo::getEnv, queryParam.getEnvEq())
                .eq(queryParam.getBizTypeEq() != null, SellerTaskInfo::getBizType, queryParam.getBizTypeEq())
                .eq(queryParam.getTaskTypeEq() != null, SellerTaskInfo::getTaskType, queryParam.getTaskTypeEq())
                .in(CollUtil.isNotEmpty(queryParam.getTaskStatusListIn()), SellerTaskInfo::getTaskStatus, queryParam.getTaskStatusListIn());
        wrapper.orderByDesc(SellerTaskInfo::getCreateTime);
        return this.list(wrapper);
    }


}
