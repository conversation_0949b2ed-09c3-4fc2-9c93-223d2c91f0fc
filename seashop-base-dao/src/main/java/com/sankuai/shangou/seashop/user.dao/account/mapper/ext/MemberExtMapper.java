package com.sankuai.shangou.seashop.user.dao.account.mapper.ext;

import com.sankuai.shangou.seashop.user.dao.account.domain.MemberExt;
import com.sankuai.shangou.seashop.user.dao.account.model.QueryMemberListModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MemberExtMapper {
    List<MemberExt> queryMember(QueryMemberListModel queryMemberListDto);

    /**
     * 根据手机号模糊查询用户id
     * <p>因为用户表手机号码是加密的，所以是关联联系人表查询的</p>
     * @param mobile 手机号码
     * @return 用户ID列表
     */
    List<Long> queryUserIdByMobile(@Param("mobile") String mobile);
}