package com.sankuai.shangou.seashop.base.dao.core.domain;

public class BasePhotoSpaceCategory {
    private Long id;

    private Long shopId;

    private String photoSpaceCatrgoryName;

    private Long displaysSequence;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getPhotoSpaceCatrgoryName() {
        return photoSpaceCatrgoryName;
    }

    public void setPhotoSpaceCatrgoryName(String photoSpaceCatrgoryName) {
        this.photoSpaceCatrgoryName = photoSpaceCatrgoryName == null ? null : photoSpaceCatrgoryName.trim();
    }

    public Long getDisplaysSequence() {
        return displaysSequence;
    }

    public void setDisplaysSequence(Long displaysSequence) {
        this.displaysSequence = displaysSequence;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BasePhotoSpaceCategory other = (BasePhotoSpaceCategory) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getShopId() == null ? other.getShopId() == null : this.getShopId().equals(other.getShopId()))
            && (this.getPhotoSpaceCatrgoryName() == null ? other.getPhotoSpaceCatrgoryName() == null : this.getPhotoSpaceCatrgoryName().equals(other.getPhotoSpaceCatrgoryName()))
            && (this.getDisplaysSequence() == null ? other.getDisplaysSequence() == null : this.getDisplaysSequence().equals(other.getDisplaysSequence()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getShopId() == null) ? 0 : getShopId().hashCode());
        result = prime * result + ((getPhotoSpaceCatrgoryName() == null) ? 0 : getPhotoSpaceCatrgoryName().hashCode());
        result = prime * result + ((getDisplaysSequence() == null) ? 0 : getDisplaysSequence().hashCode());
        return result;
    }
}