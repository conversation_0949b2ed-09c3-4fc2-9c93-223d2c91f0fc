package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseWeixinMsgTemplate;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseWeixinMsgTemplateMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/29 17:26
 */
@Repository
public class MsgTemplateRepository {

    @Resource
    private BaseWeixinMsgTemplateMapper baseWeixinMsgTemplateMapper;

    public BaseWeixinMsgTemplate selectById(Long id){
        return baseWeixinMsgTemplateMapper.selectById(id);
    }

    public List<BaseWeixinMsgTemplate> selectList(BaseWeixinMsgTemplate req){
        QueryWrapper<BaseWeixinMsgTemplate> queryWrapper = new QueryWrapper<>();
        if(req.getId() != null){
            queryWrapper.lambda().eq(BaseWeixinMsgTemplate::getId, req.getId());
        }
        if(req.getMessageType() != null){
            queryWrapper.lambda().eq(BaseWeixinMsgTemplate::getMessageType, req.getMessageType());
        }
        if(!StringUtils.isEmpty(req.getTemplateNum())){
            queryWrapper.lambda().eq(BaseWeixinMsgTemplate::getTemplateNum, req.getTemplateNum());
        }
        if(!StringUtils.isEmpty(req.getTemplateId())){
            queryWrapper.lambda().eq(BaseWeixinMsgTemplate::getTemplateId, req.getTemplateId());
        }
        return baseWeixinMsgTemplateMapper.selectList(queryWrapper);
    }

    public int update(BaseWeixinMsgTemplate req){
        LambdaUpdateWrapper<BaseWeixinMsgTemplate> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        if(req.getId() != null){
            lambdaUpdateWrapper.eq(BaseWeixinMsgTemplate::getId, req.getId());
        }
        if(!StringUtils.isEmpty(req.getTemplateNum())){
            lambdaUpdateWrapper.eq(BaseWeixinMsgTemplate::getTemplateNum, req.getTemplateNum());
        }
        if(req.getMessageType() != null){
            lambdaUpdateWrapper.eq(BaseWeixinMsgTemplate::getMessageType, req.getMessageType());
        }
        return baseWeixinMsgTemplateMapper.update(req, lambdaUpdateWrapper);
    }
}
