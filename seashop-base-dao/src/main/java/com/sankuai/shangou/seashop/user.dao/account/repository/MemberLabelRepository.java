package com.sankuai.shangou.seashop.user.dao.account.repository;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.account.domain.MemberLabel;
import com.sankuai.shangou.seashop.user.dao.account.mapper.MemberLabelMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 商家标签仓库类
 * @author: LXH
 **/
@Repository
@Slf4j
public class MemberLabelRepository extends ServiceImpl<MemberLabelMapper, MemberLabel> {

    public List<MemberLabel> getMemberLabel(MemberLabel memberLabel) {
        QueryWrapper<MemberLabel> memberLabelQueryWrapper = new QueryWrapper<MemberLabel>();
        if (memberLabel.getMemId() != null) {
            memberLabelQueryWrapper.lambda().eq(MemberLabel::getMemId, memberLabel.getMemId());
        }
        return baseMapper.selectList(memberLabelQueryWrapper);
    }

    public void deleteMemberLabel(MemberLabel memberLabel) {
        QueryWrapper<MemberLabel> memberLabelQueryWrapper = new QueryWrapper<>();
        if (memberLabel.getMemId() != null) {
            memberLabelQueryWrapper.lambda().eq(MemberLabel::getMemId, memberLabel.getMemId());
            baseMapper.delete(memberLabelQueryWrapper);
        }
    }

    public Long setMemberLabel(List<MemberLabel> memberLabels) {
        //判空
        if (CollUtil.isEmpty(memberLabels)) {
            return 0L;
        }
        saveBatch(memberLabels);
        return (long) memberLabels.size();
    }

    public void deleteMemberLabel(List<MemberLabel> memberLabels) {
        //判空
        if (CollUtil.isEmpty(memberLabels)) {
            return;
        }
        LambdaUpdateWrapper<MemberLabel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(MemberLabel::getMemId, memberLabels.stream().map(MemberLabel::getMemId).collect(Collectors.toList()));
        updateWrapper.in(MemberLabel::getLabelId, memberLabels.stream().map(MemberLabel::getLabelId).collect(Collectors.toList()));
        remove(updateWrapper);
    }

    public void batchDeleteMemberLabel(List<MemberLabel> memberLabels) {
        if (CollUtil.isEmpty(memberLabels)) {
            return;
        }
        LambdaUpdateWrapper<MemberLabel> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.in(MemberLabel::getMemId, memberLabels.stream().map(MemberLabel::getMemId).collect(Collectors.toList()));
        updateWrapper.in(MemberLabel::getLabelId, memberLabels.stream().map(MemberLabel::getLabelId).collect(Collectors.toList()));
        remove(updateWrapper);
    }

    public void deleteMemberLabelByLabelId(Long labelId) {
        LambdaQueryWrapper<MemberLabel> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberLabel::getLabelId, labelId);
        remove(queryWrapper);
    }
}
