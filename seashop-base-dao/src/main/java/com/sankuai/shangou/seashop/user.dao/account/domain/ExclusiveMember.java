package com.sankuai.shangou.seashop.user.dao.account.domain;//package com.sankuai.shangou.seashop.user.dao.account.domain;
//
//import com.baomidou.mybatisplus.annotation.TableName;
//import com.baomidou.mybatisplus.annotation.IdType;
//import java.util.Date;
//import com.baomidou.mybatisplus.annotation.TableId;
//import com.baomidou.mybatisplus.annotation.TableField;
//import java.io.Serializable;
//
///**
// * <p>
// * 专属商家列表
// * </p>
// *
// * <AUTHOR> * @since 2023-11-27
// */
//@TableName("user_exclusive_member")
//public class ExclusiveMember implements Serializable {
//
//    private static final long serialVersionUID = 1L;
//
//    /**
//     * 主键
//     */
//    @TableId(value = "id", type = IdType.AUTO)
//    private Long id;
//
//    /**
//     * 供应商Id
//     */
//    @TableField("shop_id")
//    private Long shopId;
//
//    /**
//     * 商家Id
//     */
//    @TableField("user_id")
//    private Long userId;
//
//    /**
//     * 添加时间
//     */
//    @TableField("add_date")
//    private Date addDate;
//
//
//    public Long getId() {
//        return id;
//    }
//
//    public void setId(Long id) {
//        this.id = id;
//    }
//
//    public Long getShopId() {
//        return shopId;
//    }
//
//    public void setShopId(Long shopId) {
//        this.shopId = shopId;
//    }
//
//    public Long getUserId() {
//        return userId;
//    }
//
//    public void setUserId(Long userId) {
//        this.userId = userId;
//    }
//
//    public Date getAddDate() {
//        return addDate;
//    }
//
//    public void setAddDate(Date addDate) {
//        this.addDate = addDate;
//    }
//
//    @Override
//    public String toString() {
//        return "ExclusiveMember{" +
//        "id=" + id +
//        ", shopId=" + shopId +
//        ", userId=" + userId +
//        ", addDate=" + addDate +
//        "}";
//    }
//}
