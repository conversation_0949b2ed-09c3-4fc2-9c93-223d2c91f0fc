package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategory;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryApply;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.BusinessCategoryApplyMapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 分类申请仓库类
 * @author: LXH
 **/
@Repository
public class BusinessCategoryApplyRepository extends ServiceImpl<BusinessCategoryApplyMapper, BusinessCategoryApply> {
    public List<BusinessCategoryApply> selectList(BusinessCategoryApply apply) {
       /* QueryWrapper<BusinessCategoryApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(apply.getAuditedStatus() != null, BusinessCategoryApply::getAuditedStatus, apply.getAuditedStatus())
                .eq(apply.getAgreementStatus() != null, BusinessCategoryApply::getAgreementStatus, apply.getAgreementStatus())
                .eq(apply.getShopId() != null, BusinessCategoryApply::getShopId, apply.getShopId())
                .like(StringUtils.isNotEmpty(apply.getShopName()), BusinessCategoryApply::getShopName, apply.getShopName())
                .orderByDesc(BusinessCategoryApply::getApplyDate);
        return baseMapper.selectList(queryWrapper);*/
        return baseMapper.listBusinessApply(apply);
    }

    public List<Long> listCategoryApplyIdsByShopId(Long shopId) {
        QueryWrapper<BusinessCategoryApply> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BusinessCategoryApply::getShopId, shopId);
        queryWrapper.lambda().eq(BusinessCategoryApply::getAuditedStatus, 1);
        return baseMapper.selectList(queryWrapper).stream().map(BusinessCategoryApply::getId).collect(Collectors.toList());
    }
}
