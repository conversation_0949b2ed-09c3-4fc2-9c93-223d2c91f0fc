package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

@TableName("base_express_company")
public class BaseExpressCompany implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 快递名称
     */
    @TableField("name")
    private String name;

    /**
     * 淘宝编号
     */
    @TableField("taobao_code")
    private String taobaoCode;

    /**
     * 快递100对应物流编号
     */
    @TableField("kuaidi100_code")
    private String kuaidi100Code;

    /**
     * 快递鸟物流公司编号
     */
    @TableField("kuaidiniao_code")
    private String kuaidiniaoCode;

    /**
     * 快递面单宽度
     */
    @TableField("width")
    private Integer width;

    /**
     * 快递面单高度
     */
    @TableField("height")
    private Integer height;

    /**
     * 快递公司logo
     */
    @TableField("logo")
    private String logo;

    /**
     * 快递公司面单背景图片
     */
    @TableField("background_image")
    private String backgroundImage;

    /**
     * 快递公司状态（0：正常，1：删除）
     */
    @TableField("status")
    private Integer status;

    /**
     * 创建日期
     */
    @TableField("create_date")
    private Date createDate;

    /**
     * 旺店通code
     */
    @TableField("wangdiantong_code")
    private String wangdiantongCode;

    /**
     * 聚水潭code
     */
    @TableField("jushuitan_code")
    private String jushuitanCode;

    /**
     * 菠萝派code
     */
    @TableField("boluopai_code")
    private String boluopaiCode;

    /**
     * 美团code
     */
    @TableField("meituan_code")
    private String meituanCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getTaobaoCode() {
        return taobaoCode;
    }

    public void setTaobaoCode(String taobaoCode) {
        this.taobaoCode = taobaoCode;
    }

    public String getKuaidi100Code() {
        return kuaidi100Code;
    }

    public void setKuaidi100Code(String kuaidi100Code) {
        this.kuaidi100Code = kuaidi100Code;
    }

    public String getKuaidiniaoCode() {
        return kuaidiniaoCode;
    }

    public void setKuaidiniaoCode(String kuaidiniaoCode) {
        this.kuaidiniaoCode = kuaidiniaoCode;
    }

    public Integer getWidth() {
        return width;
    }

    public void setWidth(Integer width) {
        this.width = width;
    }

    public Integer getHeight() {
        return height;
    }

    public void setHeight(Integer height) {
        this.height = height;
    }

    public String getLogo() {
        return logo;
    }

    public void setLogo(String logo) {
        this.logo = logo;
    }

    public String getBackgroundImage() {
        return backgroundImage;
    }

    public void setBackgroundImage(String backgroundImage) {
        this.backgroundImage = backgroundImage;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Date getCreateDate() {
        return createDate;
    }

    public void setCreateDate(Date createDate) {
        this.createDate = createDate;
    }

    public String getWangdiantongCode() {
        return wangdiantongCode;
    }

    public void setWangdiantongCode(String wangdiantongCode) {
        this.wangdiantongCode = wangdiantongCode;
    }

    public String getJushuitanCode() {
        return jushuitanCode;
    }

    public void setJushuitanCode(String jushuitanCode) {
        this.jushuitanCode = jushuitanCode;
    }

    public String getBoluopaiCode() {
        return boluopaiCode;
    }

    public void setBoluopaiCode(String boluopaiCode) {
        this.boluopaiCode = boluopaiCode;
    }

    public String getMeituanCode() {
        return meituanCode;
    }

    public void setMeituanCode(String meituanCode) {
        this.meituanCode = meituanCode;
    }
}
