package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageKey;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseTemplatePageMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class BaseTemplatePageRepository {
    @Resource
    private BaseTemplatePageMapper baseTemplatePageMapper;

    public Long create(BaseTemplatePageWithBLOBs templatePageWithBLOBs) {
        baseTemplatePageMapper.insert(templatePageWithBLOBs);
        return templatePageWithBLOBs.getId();
    }

    public Boolean update(BaseTemplatePageWithBLOBs templatePageWithBLOBs) {
        baseTemplatePageMapper.updateByPrimaryKeySelective(templatePageWithBLOBs);
        return true;
    }



    public BaseTemplatePageWithBLOBs getById(BaseTemplatePageKey key) {
        BaseTemplatePageWithBLOBs result = baseTemplatePageMapper.selectByPrimaryKey(key);
        return result;
    }

}
