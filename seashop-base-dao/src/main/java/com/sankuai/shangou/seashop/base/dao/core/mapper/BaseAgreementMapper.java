package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseAgreement;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseAgreementExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseAgreementMapper {
    long countByExample(BaseAgreementExample example);

    int deleteByExample(BaseAgreementExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseAgreement record);

    int insertSelective(BaseAgreement record);

    List<BaseAgreement> selectByExampleWithBLOBs(BaseAgreementExample example);

    List<BaseAgreement> selectByExample(BaseAgreementExample example);

    BaseAgreement selectByPrimaryKey(Long id);

    BaseAgreement selectByType(int agreementType);
    int updateByExampleSelective(@Param("record") BaseAgreement record, @Param("example") BaseAgreementExample example);

    int updateByExampleWithBLOBs(@Param("record") BaseAgreement record, @Param("example") BaseAgreementExample example);

    int updateByExample(@Param("record") BaseAgreement record, @Param("example") BaseAgreementExample example);

    int updateByPrimaryKeySelective(BaseAgreement record);

    int updateByPrimaryKeyWithBLOBs(BaseAgreement record);

    int updateByPrimaryKey(BaseAgreement record);
}