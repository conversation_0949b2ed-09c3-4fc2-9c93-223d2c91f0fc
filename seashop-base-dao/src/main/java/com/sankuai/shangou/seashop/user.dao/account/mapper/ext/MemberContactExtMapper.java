package com.sankuai.shangou.seashop.user.dao.account.mapper.ext;

import com.sankuai.shangou.seashop.user.dao.account.domain.MemberContact;
import com.sankuai.shangou.seashop.user.dao.account.model.MemberContactExtModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MemberContactExtMapper {
    List<MemberContact> queryList(@Param("longs") List<Long> longs, @Param("serviceProvider") String serviceProvider);

    List<MemberContactExtModel> queryExtList( @Param("shopIds") List<Long> shopIds, @Param("userIds") List<Long> userIds);


}
