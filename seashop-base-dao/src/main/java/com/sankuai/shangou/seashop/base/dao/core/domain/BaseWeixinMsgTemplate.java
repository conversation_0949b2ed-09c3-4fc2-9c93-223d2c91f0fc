package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 * 消息配置
 * </p>
 *
 * <AUTHOR> @since 2023-11-29
 */
@TableName("base_weixin_msg_template")
public class BaseWeixinMsgTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息类别
     */
    @TableField("message_type")
    private Integer messageType;

    /**
     * 消息模板编号
     */
    @TableField("template_num")
    private String templateNum;

    /**
     * 消息模板ID
     */
    @TableField("template_id")
    private String templateId;

    /**
     * 是否启用
     */
    @TableField("open_flag")
    private Boolean openFlag;

    /**
     * 是否小程序微信通知
     */
    @TableField("user_in_wx_applet")
    private Integer userInWxApplet;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 模版标题
     */
    @TableField("title")
    private String title;

    /**
     * 场景说明
     */
    @TableField("description")
    private String description;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getMessageType() {
        return messageType;
    }

    public void setMessageType(Integer messageType) {
        this.messageType = messageType;
    }

    public String getTemplateNum() {
        return templateNum;
    }

    public void setTemplateNum(String templateNum) {
        this.templateNum = templateNum;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Boolean getOpenFlag() {
        return openFlag;
    }

    public void setOpenFlag(Boolean openFlag) {
        this.openFlag = openFlag;
    }

    public Integer getUserInWxApplet() {
        return userInWxApplet;
    }

    public void setUserInWxApplet(Integer userInWxApplet) {
        this.userInWxApplet = userInWxApplet;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "BaseWeixinMsgTemplate{" +
        "id=" + id +
        ", messageType=" + messageType +
        ", templateNum=" + templateNum +
        ", templateId=" + templateId +
        ", openFlag=" + openFlag +
        ", userInWxApplet=" + userInWxApplet +
        ", title=" + title +
        ", description=" + description +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
