package com.sankuai.shangou.seashop.user.dao.shop.domain;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 供应商经营分类信息
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_business_category")
public class BusinessCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 分类申请ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 分类ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 分佣比例
     */
    @TableField("commission_rate")
    private BigDecimal commissionRate;

    /**
     * 保证金
     */
    @TableField("bond")
    private BigDecimal bond;

    /**
     * 是否冻结
     */
    @TableField("whether_frozen")
    private Boolean whetherFrozen;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public BigDecimal getBond() {
        return bond;
    }

    public void setBond(BigDecimal bond) {
        this.bond = bond;
    }

    public Boolean getWhetherFrozen() {
        return whetherFrozen;
    }

    public void setWhetherFrozen(Boolean whetherFrozen) {
        this.whetherFrozen = whetherFrozen;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BusinessCategory{" +
                "id=" + id +
                ", shopId=" + shopId +
                ", categoryId=" + categoryId +
                ", commissionRate=" + commissionRate +
                ", bond=" + bond +
                ", whetherFrozen=" + whetherFrozen +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
