package com.sankuai.shangou.seashop.user.dao.shop.repository;

import java.util.List;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.shop.domain.RestrictedArea;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.RestrictedAreaMapper;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/3 12:14
 */
@Repository
@Slf4j
public class RestrictedAreaRepository extends ServiceImpl<RestrictedAreaMapper, RestrictedArea> {

    public RestrictedArea selectById(Long id) {
        return this.getById(id);
    }

    public List<Long> getRestrictedRegionIds(Long templateId) {
        return listObjs(new LambdaQueryWrapper<RestrictedArea>()
                .eq(RestrictedArea::getTemplateId, templateId).select(RestrictedArea::getRegionId), item -> Long.parseLong(String.valueOf(item)));
    }

    public List<RestrictedArea> getByTemplateId(List<Long> templateIdList) {
        LambdaQueryWrapper<RestrictedArea> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RestrictedArea::getTemplateId, templateIdList);
        return super.list(wrapper);
    }
}
