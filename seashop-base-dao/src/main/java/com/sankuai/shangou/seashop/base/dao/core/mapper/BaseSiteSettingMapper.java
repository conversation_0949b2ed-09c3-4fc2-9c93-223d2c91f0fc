package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSettingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseSiteSettingMapper {
    long countByExample(BaseSiteSettingExample example);

    int deleteByExample(BaseSiteSettingExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseSiteSetting record);

    int insertSelective(BaseSiteSetting record);

    List<BaseSiteSetting> selectByExample(BaseSiteSettingExample example);

    BaseSiteSetting selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BaseSiteSetting record, @Param("example") BaseSiteSettingExample example);

    int updateByExample(@Param("record") BaseSiteSetting record, @Param("example") BaseSiteSettingExample example);

    int updateByPrimaryKeySelective(BaseSiteSetting record);

    int updateByPrimaryKey(BaseSiteSetting record);

    void updateSettingsByKey(@Param("req") BaseSiteSetting setting);

    BaseSiteSetting querySettingsValueByKey(@Param("key") String key);
}