package com.sankuai.shangou.seashop.user.dao.account.repository;//package com.sankuai.shangou.seashop.user.dao.account.repository;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
//import com.sankuai.shangou.seashop.user.dao.account.domain.ExclusiveMember;
//import com.sankuai.shangou.seashop.user.dao.account.mapper.ExclusiveMemberMapper;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Repository;
//
//import java.util.List;
//
///**
// * @description: 权限组仓库类
// * @author: LXH
// **/
//@Repository
//@Slf4j
//public class ExclusiveMemberRepository extends ServiceImpl<ExclusiveMemberMapper, ExclusiveMember> {
//
//    public List<ExclusiveMember> getList(ExclusiveMember exclusiveMember) {
//        QueryWrapper<ExclusiveMember> queryWrapper = new QueryWrapper<>();
//        if (exclusiveMember != null) {
//            if (exclusiveMember.getUserId() != null) {
//                queryWrapper.lambda().eq(ExclusiveMember::getUserId, exclusiveMember.getUserId());
//            }
//        }
//        return baseMapper.selectList(queryWrapper);
//    }
//
//    /**
//     * 通过店铺ID查询专属商家列表
//     */
//    public List<ExclusiveMember> getListByShopId(Long shopId) {
//        return lambdaQuery().eq(ExclusiveMember::getShopId, shopId).list();
//    }
//}
