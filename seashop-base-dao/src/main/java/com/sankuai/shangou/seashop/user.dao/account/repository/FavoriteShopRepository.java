package com.sankuai.shangou.seashop.user.dao.account.repository;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.account.domain.FavoriteShop;
import com.sankuai.shangou.seashop.user.dao.account.mapper.FavoriteShopMapper;
import com.sankuai.shangou.seashop.user.dao.account.mapper.ext.FavoriteShopExtMapper;
import com.sankuai.shangou.seashop.user.dao.account.model.FavoriteCountModel;
import com.sankuai.shangou.seashop.user.dao.account.model.FavoriteShopExt;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @description: 收藏店铺仓库类
 * @author: LXH
 **/
@Repository
@Slf4j
public class FavoriteShopRepository extends ServiceImpl<FavoriteShopMapper, FavoriteShop> {

    @Resource
    private FavoriteShopExtMapper favoriteShopExtMapper;


    /**
     * 通过店铺列表统计收藏数量
     */
    public List<FavoriteCountModel> countFavoriteShopByShopIdList(List<Long> shopIdList, Long memberId) {

        List<FavoriteCountModel> favoriteCountModelList = new ArrayList<>(shopIdList.size());
        for (Long shopId : shopIdList) {
            FavoriteCountModel favoriteCountModel = new FavoriteCountModel();
            favoriteCountModel.setShopId(shopId);
            favoriteCountModel.setCount(0);
            favoriteCountModel.setFavoriteFlag(false);
            favoriteCountModelList.add(favoriteCountModel);
        }

        LambdaQueryWrapper<FavoriteShop> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FavoriteShop::getShopId, shopIdList);
        List<FavoriteShop> favoriteShopList = this.list(queryWrapper);
        if (CollUtil.isNotEmpty(favoriteShopList)) {
            Map<Long, Long> shopCount = favoriteShopList.stream().collect(Collectors.groupingBy(FavoriteShop::getShopId, Collectors.counting()));
            if (null != memberId && memberId > 0) {
                List<Long> shopIdList1 = favoriteShopList.stream().filter(favoriteShop -> favoriteShop.getUserId().equals(memberId)).map(FavoriteShop::getShopId).collect(Collectors.toList());
                favoriteCountModelList.parallelStream().forEach(favoriteCountModel -> {
                    if (shopIdList1.contains(favoriteCountModel.getShopId())) {
                        favoriteCountModel.setFavoriteFlag(true);
                    }
                });

            }
            favoriteCountModelList.parallelStream().forEach(favoriteCountModel -> {
                if (shopCount.containsKey(favoriteCountModel.getShopId())) {
                    favoriteCountModel.setCount(shopCount.get(favoriteCountModel.getShopId()).intValue());
                }
            });
        }

        return favoriteCountModelList;
    }

    public List<FavoriteShopExt> queryList(Long userId) {
        return favoriteShopExtMapper.queryList(userId);
    }
}
