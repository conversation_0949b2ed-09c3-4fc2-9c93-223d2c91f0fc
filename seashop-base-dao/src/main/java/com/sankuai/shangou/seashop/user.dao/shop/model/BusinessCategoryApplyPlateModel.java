package com.sankuai.shangou.seashop.user.dao.shop.model;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;

import java.util.Date;

/**
 * @description: 商家申请板块dto
 * @author: LXH
 **/
public class BusinessCategoryApplyPlateModel {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 申请日期
     */
    @TableField("apply_date")
    private Date applyDate;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 审核状态
     */
    @TableField("audited_status")
    private Integer auditedStatus;

    @TableField("audited_date")
    private Date auditedDate;

    /**
     * 拒绝原因
     */
    @TableField("refuse_reason")
    private String refuseReason;

    /**
     * 签署协议状态,待签署=0,已签署=1
     */
    @TableField("agreement_status")
    private Integer agreementStatus;

    /**
     * 签署协议的对应的协议Id
     */
    @TableField("shop_agreement_id")
    private Long shopAgreementId;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;
}
