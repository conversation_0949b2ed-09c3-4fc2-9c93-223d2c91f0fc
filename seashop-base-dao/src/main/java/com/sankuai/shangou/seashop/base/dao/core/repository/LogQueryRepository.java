package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseOperationLog;
import com.sankuai.shangou.seashop.base.dao.core.model.BaseOperationLogModel;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseOperationLogMapper;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/12/1 14:17
 */
@Repository
public class LogQueryRepository {

    @Resource
    private BaseOperationLogMapper baseOperationLogMapper;

    public List<BaseOperationLog> selectList(BaseOperationLogModel req){
        QueryWrapper<BaseOperationLog> queryWrapper = new QueryWrapper<>();
        if(req.getId() != null){
            queryWrapper.lambda().eq(BaseOperationLog::getId, req.getId());
        }
        if(req.getShopId() != null){
            queryWrapper.lambda().eq(BaseOperationLog::getShopId, req.getShopId());
        }
        if(StringUtils.isNotEmpty(req.getUserName())){
            queryWrapper.lambda().eq(BaseOperationLog::getOperationUserId, req.getUserName());
        }
        if(req.getStartTime() != null){
            queryWrapper.lambda().gt(BaseOperationLog::getOperationTime, req.getStartTime());
        }
        if(req.getEndTime() != null){
            queryWrapper.lambda().le(BaseOperationLog::getOperationTime, req.getEndTime());
        }
        if(StringUtils.isNotEmpty(req.getOperationName())){
            queryWrapper.lambda().like(BaseOperationLog::getOperationName, req.getOperationName());
        }
        if(req.getModuleId() != null){
            queryWrapper.lambda().eq(BaseOperationLog::getModuleId, req.getModuleId());
        }
        if(StringUtils.isNotEmpty(req.getOperationContent())) {
            queryWrapper.lambda().like(BaseOperationLog::getOperationContent, req.getOperationContent());
        }
        if (StringUtils.isNotEmpty(req.getActionName())) {
            queryWrapper.lambda().like(BaseOperationLog::getActionName, req.getActionName());
        }
        queryWrapper.lambda().orderByDesc(BaseOperationLog::getOperationTime);
        return baseOperationLogMapper.selectList(queryWrapper);
    }
}
