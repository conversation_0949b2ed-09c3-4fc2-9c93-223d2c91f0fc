package com.sankuai.shangou.seashop.base.dao.core.domain;

public class BaseTemplatePageWithBLOBs extends BaseTemplatePage {
    private String lModules;

    private String pModules;

    private String jPage;

    public String getlModules() {
        return lModules;
    }

    public void setlModules(String lModules) {
        this.lModules = lModules == null ? null : lModules.trim();
    }

    public String getpModules() {
        return pModules;
    }

    public void setpModules(String pModules) {
        this.pModules = pModules == null ? null : pModules.trim();
    }

    public String getjPage() {
        return jPage;
    }

    public void setjPage(String jPage) {
        this.jPage = jPage == null ? null : jPage.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BaseTemplatePageWithBLOBs other = (BaseTemplatePageWithBLOBs) that;
        return (this.getShopId() == null ? other.getShopId() == null : this.getShopId().equals(other.getShopId()))
            && (this.getvShopId() == null ? other.getvShopId() == null : this.getvShopId().equals(other.getvShopId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getClient() == null ? other.getClient() == null : this.getClient().equals(other.getClient()))
            && (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTitle() == null ? other.getTitle() == null : this.getTitle().equals(other.getTitle()))
            && (this.getAddTime() == null ? other.getAddTime() == null : this.getAddTime().equals(other.getAddTime()))
            && (this.getUpdateTime() == null ? other.getUpdateTime() == null : this.getUpdateTime().equals(other.getUpdateTime()))
            && (this.getlModules() == null ? other.getlModules() == null : this.getlModules().equals(other.getlModules()))
            && (this.getpModules() == null ? other.getpModules() == null : this.getpModules().equals(other.getpModules()))
            && (this.getjPage() == null ? other.getjPage() == null : this.getjPage().equals(other.getjPage()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getShopId() == null) ? 0 : getShopId().hashCode());
        result = prime * result + ((getvShopId() == null) ? 0 : getvShopId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getClient() == null) ? 0 : getClient().hashCode());
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTitle() == null) ? 0 : getTitle().hashCode());
        result = prime * result + ((getAddTime() == null) ? 0 : getAddTime().hashCode());
        result = prime * result + ((getUpdateTime() == null) ? 0 : getUpdateTime().hashCode());
        result = prime * result + ((getlModules() == null) ? 0 : getlModules().hashCode());
        result = prime * result + ((getpModules() == null) ? 0 : getpModules().hashCode());
        result = prime * result + ((getjPage() == null) ? 0 : getjPage().hashCode());
        return result;
    }
}