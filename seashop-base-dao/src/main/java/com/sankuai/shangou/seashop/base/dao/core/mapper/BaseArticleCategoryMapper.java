package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategory;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseArticleCategoryMapper {
    long countByExample(BaseArticleCategoryExample example);

    int deleteByExample(BaseArticleCategoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseArticleCategory record);

    int insertSelective(BaseArticleCategory record);

    List<BaseArticleCategory> selectByExample(BaseArticleCategoryExample example);

    BaseArticleCategory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BaseArticleCategory record, @Param("example") BaseArticleCategoryExample example);

    int updateByExample(@Param("record") BaseArticleCategory record, @Param("example") BaseArticleCategoryExample example);

    int updateByPrimaryKeySelective(BaseArticleCategory record);

    int updateByPrimaryKey(BaseArticleCategory record);
}