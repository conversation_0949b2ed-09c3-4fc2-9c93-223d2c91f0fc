package com.sankuai.shangou.seashop.base.dao.core.repository;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettled;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledExample;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledWithBLOBs;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSettledMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class BaseSettledRepository {
    @Resource
    private BaseSettledMapper baseSettledMapper;

    public Boolean save(BaseSettledWithBLOBs settled) {
        BaseSettledExample example = new BaseSettledExample();

        Boolean isPresent = baseSettledMapper.selectByExampleWithBLOBs(example).stream().findFirst().isPresent();
        if (!isPresent) {
            baseSettledMapper.insert(settled);
        } else {
            BaseSettledWithBLOBs oldSettled = baseSettledMapper.selectByExampleWithBLOBs(example).stream().findFirst().get();
            settled.setId(oldSettled.getId());
            baseSettledMapper.updateByPrimaryKeySelective(settled);
        }
        return true;
    }

    public BaseSettled get() {
        BaseSettledExample example = new BaseSettledExample();
        BaseSettled settled = baseSettledMapper.selectByExampleWithBLOBs(example).stream().findFirst().get();
        return settled;
    }

    public BaseSettled get(Long id) {
        BaseSettledExample example = new BaseSettledExample();
        BaseSettled settled = baseSettledMapper.selectByExampleWithBLOBs(example).stream().findFirst().get();
        return settled;
    }
}
