package com.sankuai.shangou.seashop.base.dao.core.domain;

import java.util.ArrayList;
import java.util.List;

public class BaseSettledExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public BaseSettledExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNull() {
            addCriterion("business_type is null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIsNotNull() {
            addCriterion("business_type is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeEqualTo(Integer value) {
            addCriterion("business_type =", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotEqualTo(Integer value) {
            addCriterion("business_type <>", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThan(Integer value) {
            addCriterion("business_type >", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("business_type >=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThan(Integer value) {
            addCriterion("business_type <", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeLessThanOrEqualTo(Integer value) {
            addCriterion("business_type <=", value, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeIn(List<Integer> values) {
            addCriterion("business_type in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotIn(List<Integer> values) {
            addCriterion("business_type not in", values, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeBetween(Integer value1, Integer value2) {
            addCriterion("business_type between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andBusinessTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("business_type not between", value1, value2, "businessType");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeIsNull() {
            addCriterion("settlement_account_type is null");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeIsNotNull() {
            addCriterion("settlement_account_type is not null");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeEqualTo(Integer value) {
            addCriterion("settlement_account_type =", value, "settlementAccountType");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeNotEqualTo(Integer value) {
            addCriterion("settlement_account_type <>", value, "settlementAccountType");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeGreaterThan(Integer value) {
            addCriterion("settlement_account_type >", value, "settlementAccountType");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("settlement_account_type >=", value, "settlementAccountType");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeLessThan(Integer value) {
            addCriterion("settlement_account_type <", value, "settlementAccountType");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeLessThanOrEqualTo(Integer value) {
            addCriterion("settlement_account_type <=", value, "settlementAccountType");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeIn(List<Integer> values) {
            addCriterion("settlement_account_type in", values, "settlementAccountType");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeNotIn(List<Integer> values) {
            addCriterion("settlement_account_type not in", values, "settlementAccountType");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeBetween(Integer value1, Integer value2) {
            addCriterion("settlement_account_type between", value1, value2, "settlementAccountType");
            return (Criteria) this;
        }

        public Criteria andSettlementAccountTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("settlement_account_type not between", value1, value2, "settlementAccountType");
            return (Criteria) this;
        }

        public Criteria andTrialDaysIsNull() {
            addCriterion("trial_days is null");
            return (Criteria) this;
        }

        public Criteria andTrialDaysIsNotNull() {
            addCriterion("trial_days is not null");
            return (Criteria) this;
        }

        public Criteria andTrialDaysEqualTo(Integer value) {
            addCriterion("trial_days =", value, "trialDays");
            return (Criteria) this;
        }

        public Criteria andTrialDaysNotEqualTo(Integer value) {
            addCriterion("trial_days <>", value, "trialDays");
            return (Criteria) this;
        }

        public Criteria andTrialDaysGreaterThan(Integer value) {
            addCriterion("trial_days >", value, "trialDays");
            return (Criteria) this;
        }

        public Criteria andTrialDaysGreaterThanOrEqualTo(Integer value) {
            addCriterion("trial_days >=", value, "trialDays");
            return (Criteria) this;
        }

        public Criteria andTrialDaysLessThan(Integer value) {
            addCriterion("trial_days <", value, "trialDays");
            return (Criteria) this;
        }

        public Criteria andTrialDaysLessThanOrEqualTo(Integer value) {
            addCriterion("trial_days <=", value, "trialDays");
            return (Criteria) this;
        }

        public Criteria andTrialDaysIn(List<Integer> values) {
            addCriterion("trial_days in", values, "trialDays");
            return (Criteria) this;
        }

        public Criteria andTrialDaysNotIn(List<Integer> values) {
            addCriterion("trial_days not in", values, "trialDays");
            return (Criteria) this;
        }

        public Criteria andTrialDaysBetween(Integer value1, Integer value2) {
            addCriterion("trial_days between", value1, value2, "trialDays");
            return (Criteria) this;
        }

        public Criteria andTrialDaysNotBetween(Integer value1, Integer value2) {
            addCriterion("trial_days not between", value1, value2, "trialDays");
            return (Criteria) this;
        }

        public Criteria andIsCityIsNull() {
            addCriterion("is_city is null");
            return (Criteria) this;
        }

        public Criteria andIsCityIsNotNull() {
            addCriterion("is_city is not null");
            return (Criteria) this;
        }

        public Criteria andIsCityEqualTo(Integer value) {
            addCriterion("is_city =", value, "isCity");
            return (Criteria) this;
        }

        public Criteria andIsCityNotEqualTo(Integer value) {
            addCriterion("is_city <>", value, "isCity");
            return (Criteria) this;
        }

        public Criteria andIsCityGreaterThan(Integer value) {
            addCriterion("is_city >", value, "isCity");
            return (Criteria) this;
        }

        public Criteria andIsCityGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_city >=", value, "isCity");
            return (Criteria) this;
        }

        public Criteria andIsCityLessThan(Integer value) {
            addCriterion("is_city <", value, "isCity");
            return (Criteria) this;
        }

        public Criteria andIsCityLessThanOrEqualTo(Integer value) {
            addCriterion("is_city <=", value, "isCity");
            return (Criteria) this;
        }

        public Criteria andIsCityIn(List<Integer> values) {
            addCriterion("is_city in", values, "isCity");
            return (Criteria) this;
        }

        public Criteria andIsCityNotIn(List<Integer> values) {
            addCriterion("is_city not in", values, "isCity");
            return (Criteria) this;
        }

        public Criteria andIsCityBetween(Integer value1, Integer value2) {
            addCriterion("is_city between", value1, value2, "isCity");
            return (Criteria) this;
        }

        public Criteria andIsCityNotBetween(Integer value1, Integer value2) {
            addCriterion("is_city not between", value1, value2, "isCity");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberIsNull() {
            addCriterion("is_people_number is null");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberIsNotNull() {
            addCriterion("is_people_number is not null");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberEqualTo(Integer value) {
            addCriterion("is_people_number =", value, "isPeopleNumber");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberNotEqualTo(Integer value) {
            addCriterion("is_people_number <>", value, "isPeopleNumber");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberGreaterThan(Integer value) {
            addCriterion("is_people_number >", value, "isPeopleNumber");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_people_number >=", value, "isPeopleNumber");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberLessThan(Integer value) {
            addCriterion("is_people_number <", value, "isPeopleNumber");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberLessThanOrEqualTo(Integer value) {
            addCriterion("is_people_number <=", value, "isPeopleNumber");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberIn(List<Integer> values) {
            addCriterion("is_people_number in", values, "isPeopleNumber");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberNotIn(List<Integer> values) {
            addCriterion("is_people_number not in", values, "isPeopleNumber");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberBetween(Integer value1, Integer value2) {
            addCriterion("is_people_number between", value1, value2, "isPeopleNumber");
            return (Criteria) this;
        }

        public Criteria andIsPeopleNumberNotBetween(Integer value1, Integer value2) {
            addCriterion("is_people_number not between", value1, value2, "isPeopleNumber");
            return (Criteria) this;
        }

        public Criteria andIsAddressIsNull() {
            addCriterion("is_address is null");
            return (Criteria) this;
        }

        public Criteria andIsAddressIsNotNull() {
            addCriterion("is_address is not null");
            return (Criteria) this;
        }

        public Criteria andIsAddressEqualTo(Integer value) {
            addCriterion("is_address =", value, "isAddress");
            return (Criteria) this;
        }

        public Criteria andIsAddressNotEqualTo(Integer value) {
            addCriterion("is_address <>", value, "isAddress");
            return (Criteria) this;
        }

        public Criteria andIsAddressGreaterThan(Integer value) {
            addCriterion("is_address >", value, "isAddress");
            return (Criteria) this;
        }

        public Criteria andIsAddressGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_address >=", value, "isAddress");
            return (Criteria) this;
        }

        public Criteria andIsAddressLessThan(Integer value) {
            addCriterion("is_address <", value, "isAddress");
            return (Criteria) this;
        }

        public Criteria andIsAddressLessThanOrEqualTo(Integer value) {
            addCriterion("is_address <=", value, "isAddress");
            return (Criteria) this;
        }

        public Criteria andIsAddressIn(List<Integer> values) {
            addCriterion("is_address in", values, "isAddress");
            return (Criteria) this;
        }

        public Criteria andIsAddressNotIn(List<Integer> values) {
            addCriterion("is_address not in", values, "isAddress");
            return (Criteria) this;
        }

        public Criteria andIsAddressBetween(Integer value1, Integer value2) {
            addCriterion("is_address between", value1, value2, "isAddress");
            return (Criteria) this;
        }

        public Criteria andIsAddressNotBetween(Integer value1, Integer value2) {
            addCriterion("is_address not between", value1, value2, "isAddress");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeIsNull() {
            addCriterion("is_business_license_code is null");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeIsNotNull() {
            addCriterion("is_business_license_code is not null");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeEqualTo(Integer value) {
            addCriterion("is_business_license_code =", value, "isBusinessLicenseCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeNotEqualTo(Integer value) {
            addCriterion("is_business_license_code <>", value, "isBusinessLicenseCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeGreaterThan(Integer value) {
            addCriterion("is_business_license_code >", value, "isBusinessLicenseCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_business_license_code >=", value, "isBusinessLicenseCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeLessThan(Integer value) {
            addCriterion("is_business_license_code <", value, "isBusinessLicenseCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeLessThanOrEqualTo(Integer value) {
            addCriterion("is_business_license_code <=", value, "isBusinessLicenseCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeIn(List<Integer> values) {
            addCriterion("is_business_license_code in", values, "isBusinessLicenseCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeNotIn(List<Integer> values) {
            addCriterion("is_business_license_code not in", values, "isBusinessLicenseCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeBetween(Integer value1, Integer value2) {
            addCriterion("is_business_license_code between", value1, value2, "isBusinessLicenseCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("is_business_license_code not between", value1, value2, "isBusinessLicenseCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeIsNull() {
            addCriterion("is_business_scope is null");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeIsNotNull() {
            addCriterion("is_business_scope is not null");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeEqualTo(Integer value) {
            addCriterion("is_business_scope =", value, "isBusinessScope");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeNotEqualTo(Integer value) {
            addCriterion("is_business_scope <>", value, "isBusinessScope");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeGreaterThan(Integer value) {
            addCriterion("is_business_scope >", value, "isBusinessScope");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_business_scope >=", value, "isBusinessScope");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeLessThan(Integer value) {
            addCriterion("is_business_scope <", value, "isBusinessScope");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeLessThanOrEqualTo(Integer value) {
            addCriterion("is_business_scope <=", value, "isBusinessScope");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeIn(List<Integer> values) {
            addCriterion("is_business_scope in", values, "isBusinessScope");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeNotIn(List<Integer> values) {
            addCriterion("is_business_scope not in", values, "isBusinessScope");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeBetween(Integer value1, Integer value2) {
            addCriterion("is_business_scope between", value1, value2, "isBusinessScope");
            return (Criteria) this;
        }

        public Criteria andIsBusinessScopeNotBetween(Integer value1, Integer value2) {
            addCriterion("is_business_scope not between", value1, value2, "isBusinessScope");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseIsNull() {
            addCriterion("is_business_license is null");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseIsNotNull() {
            addCriterion("is_business_license is not null");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseEqualTo(Integer value) {
            addCriterion("is_business_license =", value, "isBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseNotEqualTo(Integer value) {
            addCriterion("is_business_license <>", value, "isBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseGreaterThan(Integer value) {
            addCriterion("is_business_license >", value, "isBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_business_license >=", value, "isBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseLessThan(Integer value) {
            addCriterion("is_business_license <", value, "isBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseLessThanOrEqualTo(Integer value) {
            addCriterion("is_business_license <=", value, "isBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseIn(List<Integer> values) {
            addCriterion("is_business_license in", values, "isBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseNotIn(List<Integer> values) {
            addCriterion("is_business_license not in", values, "isBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseBetween(Integer value1, Integer value2) {
            addCriterion("is_business_license between", value1, value2, "isBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenseNotBetween(Integer value1, Integer value2) {
            addCriterion("is_business_license not between", value1, value2, "isBusinessLicense");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeIsNull() {
            addCriterion("is_agency_code is null");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeIsNotNull() {
            addCriterion("is_agency_code is not null");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeEqualTo(Integer value) {
            addCriterion("is_agency_code =", value, "isAgencyCode");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeNotEqualTo(Integer value) {
            addCriterion("is_agency_code <>", value, "isAgencyCode");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeGreaterThan(Integer value) {
            addCriterion("is_agency_code >", value, "isAgencyCode");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_agency_code >=", value, "isAgencyCode");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLessThan(Integer value) {
            addCriterion("is_agency_code <", value, "isAgencyCode");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLessThanOrEqualTo(Integer value) {
            addCriterion("is_agency_code <=", value, "isAgencyCode");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeIn(List<Integer> values) {
            addCriterion("is_agency_code in", values, "isAgencyCode");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeNotIn(List<Integer> values) {
            addCriterion("is_agency_code not in", values, "isAgencyCode");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeBetween(Integer value1, Integer value2) {
            addCriterion("is_agency_code between", value1, value2, "isAgencyCode");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeNotBetween(Integer value1, Integer value2) {
            addCriterion("is_agency_code not between", value1, value2, "isAgencyCode");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseIsNull() {
            addCriterion("is_agency_code_license is null");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseIsNotNull() {
            addCriterion("is_agency_code_license is not null");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseEqualTo(Integer value) {
            addCriterion("is_agency_code_license =", value, "isAgencyCodeLicense");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseNotEqualTo(Integer value) {
            addCriterion("is_agency_code_license <>", value, "isAgencyCodeLicense");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseGreaterThan(Integer value) {
            addCriterion("is_agency_code_license >", value, "isAgencyCodeLicense");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_agency_code_license >=", value, "isAgencyCodeLicense");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseLessThan(Integer value) {
            addCriterion("is_agency_code_license <", value, "isAgencyCodeLicense");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseLessThanOrEqualTo(Integer value) {
            addCriterion("is_agency_code_license <=", value, "isAgencyCodeLicense");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseIn(List<Integer> values) {
            addCriterion("is_agency_code_license in", values, "isAgencyCodeLicense");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseNotIn(List<Integer> values) {
            addCriterion("is_agency_code_license not in", values, "isAgencyCodeLicense");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseBetween(Integer value1, Integer value2) {
            addCriterion("is_agency_code_license between", value1, value2, "isAgencyCodeLicense");
            return (Criteria) this;
        }

        public Criteria andIsAgencyCodeLicenseNotBetween(Integer value1, Integer value2) {
            addCriterion("is_agency_code_license not between", value1, value2, "isAgencyCodeLicense");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveIsNull() {
            addCriterion("is_taxpayer_to_prove is null");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveIsNotNull() {
            addCriterion("is_taxpayer_to_prove is not null");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveEqualTo(Integer value) {
            addCriterion("is_taxpayer_to_prove =", value, "isTaxpayerToProve");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveNotEqualTo(Integer value) {
            addCriterion("is_taxpayer_to_prove <>", value, "isTaxpayerToProve");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveGreaterThan(Integer value) {
            addCriterion("is_taxpayer_to_prove >", value, "isTaxpayerToProve");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_taxpayer_to_prove >=", value, "isTaxpayerToProve");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveLessThan(Integer value) {
            addCriterion("is_taxpayer_to_prove <", value, "isTaxpayerToProve");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveLessThanOrEqualTo(Integer value) {
            addCriterion("is_taxpayer_to_prove <=", value, "isTaxpayerToProve");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveIn(List<Integer> values) {
            addCriterion("is_taxpayer_to_prove in", values, "isTaxpayerToProve");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveNotIn(List<Integer> values) {
            addCriterion("is_taxpayer_to_prove not in", values, "isTaxpayerToProve");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveBetween(Integer value1, Integer value2) {
            addCriterion("is_taxpayer_to_prove between", value1, value2, "isTaxpayerToProve");
            return (Criteria) this;
        }

        public Criteria andIsTaxpayerToProveNotBetween(Integer value1, Integer value2) {
            addCriterion("is_taxpayer_to_prove not between", value1, value2, "isTaxpayerToProve");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeIsNull() {
            addCriterion("company_verification_type is null");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeIsNotNull() {
            addCriterion("company_verification_type is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeEqualTo(Integer value) {
            addCriterion("company_verification_type =", value, "companyVerificationType");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeNotEqualTo(Integer value) {
            addCriterion("company_verification_type <>", value, "companyVerificationType");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeGreaterThan(Integer value) {
            addCriterion("company_verification_type >", value, "companyVerificationType");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("company_verification_type >=", value, "companyVerificationType");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeLessThan(Integer value) {
            addCriterion("company_verification_type <", value, "companyVerificationType");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeLessThanOrEqualTo(Integer value) {
            addCriterion("company_verification_type <=", value, "companyVerificationType");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeIn(List<Integer> values) {
            addCriterion("company_verification_type in", values, "companyVerificationType");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeNotIn(List<Integer> values) {
            addCriterion("company_verification_type not in", values, "companyVerificationType");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeBetween(Integer value1, Integer value2) {
            addCriterion("company_verification_type between", value1, value2, "companyVerificationType");
            return (Criteria) this;
        }

        public Criteria andCompanyVerificationTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("company_verification_type not between", value1, value2, "companyVerificationType");
            return (Criteria) this;
        }

        public Criteria andIsSNameIsNull() {
            addCriterion("is_s_name is null");
            return (Criteria) this;
        }

        public Criteria andIsSNameIsNotNull() {
            addCriterion("is_s_name is not null");
            return (Criteria) this;
        }

        public Criteria andIsSNameEqualTo(Integer value) {
            addCriterion("is_s_name =", value, "isSName");
            return (Criteria) this;
        }

        public Criteria andIsSNameNotEqualTo(Integer value) {
            addCriterion("is_s_name <>", value, "isSName");
            return (Criteria) this;
        }

        public Criteria andIsSNameGreaterThan(Integer value) {
            addCriterion("is_s_name >", value, "isSName");
            return (Criteria) this;
        }

        public Criteria andIsSNameGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_s_name >=", value, "isSName");
            return (Criteria) this;
        }

        public Criteria andIsSNameLessThan(Integer value) {
            addCriterion("is_s_name <", value, "isSName");
            return (Criteria) this;
        }

        public Criteria andIsSNameLessThanOrEqualTo(Integer value) {
            addCriterion("is_s_name <=", value, "isSName");
            return (Criteria) this;
        }

        public Criteria andIsSNameIn(List<Integer> values) {
            addCriterion("is_s_name in", values, "isSName");
            return (Criteria) this;
        }

        public Criteria andIsSNameNotIn(List<Integer> values) {
            addCriterion("is_s_name not in", values, "isSName");
            return (Criteria) this;
        }

        public Criteria andIsSNameBetween(Integer value1, Integer value2) {
            addCriterion("is_s_name between", value1, value2, "isSName");
            return (Criteria) this;
        }

        public Criteria andIsSNameNotBetween(Integer value1, Integer value2) {
            addCriterion("is_s_name not between", value1, value2, "isSName");
            return (Criteria) this;
        }

        public Criteria andIsSCityIsNull() {
            addCriterion("is_s_city is null");
            return (Criteria) this;
        }

        public Criteria andIsSCityIsNotNull() {
            addCriterion("is_s_city is not null");
            return (Criteria) this;
        }

        public Criteria andIsSCityEqualTo(Integer value) {
            addCriterion("is_s_city =", value, "isSCity");
            return (Criteria) this;
        }

        public Criteria andIsSCityNotEqualTo(Integer value) {
            addCriterion("is_s_city <>", value, "isSCity");
            return (Criteria) this;
        }

        public Criteria andIsSCityGreaterThan(Integer value) {
            addCriterion("is_s_city >", value, "isSCity");
            return (Criteria) this;
        }

        public Criteria andIsSCityGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_s_city >=", value, "isSCity");
            return (Criteria) this;
        }

        public Criteria andIsSCityLessThan(Integer value) {
            addCriterion("is_s_city <", value, "isSCity");
            return (Criteria) this;
        }

        public Criteria andIsSCityLessThanOrEqualTo(Integer value) {
            addCriterion("is_s_city <=", value, "isSCity");
            return (Criteria) this;
        }

        public Criteria andIsSCityIn(List<Integer> values) {
            addCriterion("is_s_city in", values, "isSCity");
            return (Criteria) this;
        }

        public Criteria andIsSCityNotIn(List<Integer> values) {
            addCriterion("is_s_city not in", values, "isSCity");
            return (Criteria) this;
        }

        public Criteria andIsSCityBetween(Integer value1, Integer value2) {
            addCriterion("is_s_city between", value1, value2, "isSCity");
            return (Criteria) this;
        }

        public Criteria andIsSCityNotBetween(Integer value1, Integer value2) {
            addCriterion("is_s_city not between", value1, value2, "isSCity");
            return (Criteria) this;
        }

        public Criteria andIsSAddressIsNull() {
            addCriterion("is_s_address is null");
            return (Criteria) this;
        }

        public Criteria andIsSAddressIsNotNull() {
            addCriterion("is_s_address is not null");
            return (Criteria) this;
        }

        public Criteria andIsSAddressEqualTo(Integer value) {
            addCriterion("is_s_address =", value, "isSAddress");
            return (Criteria) this;
        }

        public Criteria andIsSAddressNotEqualTo(Integer value) {
            addCriterion("is_s_address <>", value, "isSAddress");
            return (Criteria) this;
        }

        public Criteria andIsSAddressGreaterThan(Integer value) {
            addCriterion("is_s_address >", value, "isSAddress");
            return (Criteria) this;
        }

        public Criteria andIsSAddressGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_s_address >=", value, "isSAddress");
            return (Criteria) this;
        }

        public Criteria andIsSAddressLessThan(Integer value) {
            addCriterion("is_s_address <", value, "isSAddress");
            return (Criteria) this;
        }

        public Criteria andIsSAddressLessThanOrEqualTo(Integer value) {
            addCriterion("is_s_address <=", value, "isSAddress");
            return (Criteria) this;
        }

        public Criteria andIsSAddressIn(List<Integer> values) {
            addCriterion("is_s_address in", values, "isSAddress");
            return (Criteria) this;
        }

        public Criteria andIsSAddressNotIn(List<Integer> values) {
            addCriterion("is_s_address not in", values, "isSAddress");
            return (Criteria) this;
        }

        public Criteria andIsSAddressBetween(Integer value1, Integer value2) {
            addCriterion("is_s_address between", value1, value2, "isSAddress");
            return (Criteria) this;
        }

        public Criteria andIsSAddressNotBetween(Integer value1, Integer value2) {
            addCriterion("is_s_address not between", value1, value2, "isSAddress");
            return (Criteria) this;
        }

        public Criteria andIsSidCardIsNull() {
            addCriterion("is_sid_card is null");
            return (Criteria) this;
        }

        public Criteria andIsSidCardIsNotNull() {
            addCriterion("is_sid_card is not null");
            return (Criteria) this;
        }

        public Criteria andIsSidCardEqualTo(Integer value) {
            addCriterion("is_sid_card =", value, "isSidCard");
            return (Criteria) this;
        }

        public Criteria andIsSidCardNotEqualTo(Integer value) {
            addCriterion("is_sid_card <>", value, "isSidCard");
            return (Criteria) this;
        }

        public Criteria andIsSidCardGreaterThan(Integer value) {
            addCriterion("is_sid_card >", value, "isSidCard");
            return (Criteria) this;
        }

        public Criteria andIsSidCardGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_sid_card >=", value, "isSidCard");
            return (Criteria) this;
        }

        public Criteria andIsSidCardLessThan(Integer value) {
            addCriterion("is_sid_card <", value, "isSidCard");
            return (Criteria) this;
        }

        public Criteria andIsSidCardLessThanOrEqualTo(Integer value) {
            addCriterion("is_sid_card <=", value, "isSidCard");
            return (Criteria) this;
        }

        public Criteria andIsSidCardIn(List<Integer> values) {
            addCriterion("is_sid_card in", values, "isSidCard");
            return (Criteria) this;
        }

        public Criteria andIsSidCardNotIn(List<Integer> values) {
            addCriterion("is_sid_card not in", values, "isSidCard");
            return (Criteria) this;
        }

        public Criteria andIsSidCardBetween(Integer value1, Integer value2) {
            addCriterion("is_sid_card between", value1, value2, "isSidCard");
            return (Criteria) this;
        }

        public Criteria andIsSidCardNotBetween(Integer value1, Integer value2) {
            addCriterion("is_sid_card not between", value1, value2, "isSidCard");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlIsNull() {
            addCriterion("is_sid_card_url is null");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlIsNotNull() {
            addCriterion("is_sid_card_url is not null");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlEqualTo(Integer value) {
            addCriterion("is_sid_card_url =", value, "isSidCardUrl");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlNotEqualTo(Integer value) {
            addCriterion("is_sid_card_url <>", value, "isSidCardUrl");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlGreaterThan(Integer value) {
            addCriterion("is_sid_card_url >", value, "isSidCardUrl");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_sid_card_url >=", value, "isSidCardUrl");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlLessThan(Integer value) {
            addCriterion("is_sid_card_url <", value, "isSidCardUrl");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlLessThanOrEqualTo(Integer value) {
            addCriterion("is_sid_card_url <=", value, "isSidCardUrl");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlIn(List<Integer> values) {
            addCriterion("is_sid_card_url in", values, "isSidCardUrl");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlNotIn(List<Integer> values) {
            addCriterion("is_sid_card_url not in", values, "isSidCardUrl");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlBetween(Integer value1, Integer value2) {
            addCriterion("is_sid_card_url between", value1, value2, "isSidCardUrl");
            return (Criteria) this;
        }

        public Criteria andIsSidCardUrlNotBetween(Integer value1, Integer value2) {
            addCriterion("is_sid_card_url not between", value1, value2, "isSidCardUrl");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeIsNull() {
            addCriterion("self_verification_type is null");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeIsNotNull() {
            addCriterion("self_verification_type is not null");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeEqualTo(Integer value) {
            addCriterion("self_verification_type =", value, "selfVerificationType");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeNotEqualTo(Integer value) {
            addCriterion("self_verification_type <>", value, "selfVerificationType");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeGreaterThan(Integer value) {
            addCriterion("self_verification_type >", value, "selfVerificationType");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("self_verification_type >=", value, "selfVerificationType");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeLessThan(Integer value) {
            addCriterion("self_verification_type <", value, "selfVerificationType");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeLessThanOrEqualTo(Integer value) {
            addCriterion("self_verification_type <=", value, "selfVerificationType");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeIn(List<Integer> values) {
            addCriterion("self_verification_type in", values, "selfVerificationType");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeNotIn(List<Integer> values) {
            addCriterion("self_verification_type not in", values, "selfVerificationType");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeBetween(Integer value1, Integer value2) {
            addCriterion("self_verification_type between", value1, value2, "selfVerificationType");
            return (Criteria) this;
        }

        public Criteria andSelfVerificationTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("self_verification_type not between", value1, value2, "selfVerificationType");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}