package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.dao.core.domain.SlideAd;
import com.sankuai.shangou.seashop.base.dao.core.mapper.SlideAdMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/12/11/011
 * @description:
 */
@Repository
public class SlideAdRepository extends ServiceImpl<SlideAdMapper, SlideAd> {

    /**
     * 根据类型查询轮播图
     *
     * @param type
     * @return
     */
    public List<SlideAd> queryByType(Integer type) {
        LambdaQueryWrapper<SlideAd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SlideAd::getTypeId, type);
        queryWrapper.eq(SlideAd::getDelFlag,Boolean.FALSE);
        queryWrapper.orderByAsc(SlideAd::getDisplaySequence);
        return this.list(queryWrapper);
    }

    /**
     * 查询第一个小于当前排序的轮播图
     */
    public SlideAd queryFirstLessThanDisplaySequence(Integer type, Long displaySequence) {
        LambdaQueryWrapper<SlideAd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SlideAd::getTypeId, type);
        queryWrapper.eq(SlideAd::getDelFlag,Boolean.FALSE);
        queryWrapper.lt(SlideAd::getDisplaySequence, displaySequence);
        queryWrapper.orderByDesc(SlideAd::getDisplaySequence);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }

    /**
     * 查询第一个大于当前排序的轮播图
     */
    public SlideAd queryFirstGreaterThanDisplaySequence(Integer type, Long displaySequence) {
        LambdaQueryWrapper<SlideAd> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(SlideAd::getTypeId, type);
        queryWrapper.eq(SlideAd::getDelFlag,Boolean.FALSE);
        queryWrapper.gt(SlideAd::getDisplaySequence, displaySequence);
        queryWrapper.orderByAsc(SlideAd::getDisplaySequence);
        queryWrapper.last("limit 1");
        return this.getOne(queryWrapper);
    }
}
