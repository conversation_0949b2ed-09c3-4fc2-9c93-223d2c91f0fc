package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2023-12-09
 */
@TableName("base_send_message_record")
public class SendMessageRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 消息类别
     */
    @TableField("message_type")
    private Integer messageType;

    /**
     * 内容类型
     */
    @TableField("content_type")
    private Integer contentType;

    /**
     * 发送内容
     */
    @TableField("send_content")
    private String sendContent;

    /**
     * 发送对象
     */
    @TableField("to_user_label")
    private String toUserLabel;

    /**
     * 发送状态
     */
    @TableField("send_state")
    private Integer sendState;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private Date sendTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getMessageType() {
        return messageType;
    }

    public void setMessageType(Integer messageType) {
        this.messageType = messageType;
    }

    public Integer getContentType() {
        return contentType;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public String getSendContent() {
        return sendContent;
    }

    public void setSendContent(String sendContent) {
        this.sendContent = sendContent;
    }

    public String getToUserLabel() {
        return toUserLabel;
    }

    public void setToUserLabel(String toUserLabel) {
        this.toUserLabel = toUserLabel;
    }

    public Integer getSendState() {
        return sendState;
    }

    public void setSendState(Integer sendState) {
        this.sendState = sendState;
    }

    public Date getSendTime() {
        return sendTime;
    }

    public void setSendTime(Date sendTime) {
        this.sendTime = sendTime;
    }

    @Override
    public String toString() {
        return "SendMessageRecord{" +
        "id=" + id +
        ", messageType=" + messageType +
        ", contentType=" + contentType +
        ", sendContent=" + sendContent +
        ", toUserLabel=" + toUserLabel +
        ", sendState=" + sendState +
        ", sendTime=" + sendTime +
        "}";
    }
}
