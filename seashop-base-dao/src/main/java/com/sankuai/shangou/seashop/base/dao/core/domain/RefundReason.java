package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 售后原因
 * </p>
 *
 * <AUTHOR> @since 2023-11-23
 */
public class RefundReason implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 售后原因
     */
    @TableField("after_sales_text")
    private String afterSalesText;

    /**
     * 排序
     */
    @TableField("sequence")
    private Integer sequence;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAfterSalesText() {
        return afterSalesText;
    }

    public void setAfterSalesText(String afterSalesText) {
        this.afterSalesText = afterSalesText;
    }

    public Integer getSequence() {
        return sequence;
    }

    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "RefundReason{" +
        "id=" + id +
        ", afterSalesText=" + afterSalesText +
        ", sequence=" + sequence +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
