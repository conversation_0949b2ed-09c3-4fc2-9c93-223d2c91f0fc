package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2024-01-09
 */
@TableName("base_wx_applet_form_data")
public class WxAppletFormData implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 事件ID
     */
    @TableField("event_id")
    private Long eventId;

    /**
     * 事件值
     */
    @TableField("event_value")
    private String eventValue;

    /**
     * 事件的表单ID
     */
    @TableField("form_id")
    private String formId;

    /**
     * 事件时间
     */
    @TableField("event_time")
    private Date eventTime;

    /**
     * FormId过期时间
     */
    @TableField("expire_time")
    private Date expireTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getEventId() {
        return eventId;
    }

    public void setEventId(Long eventId) {
        this.eventId = eventId;
    }

    public String getEventValue() {
        return eventValue;
    }

    public void setEventValue(String eventValue) {
        this.eventValue = eventValue;
    }

    public String getFormId() {
        return formId;
    }

    public void setFormId(String formId) {
        this.formId = formId;
    }

    public Date getEventTime() {
        return eventTime;
    }

    public void setEventTime(Date eventTime) {
        this.eventTime = eventTime;
    }

    public Date getExpireTime() {
        return expireTime;
    }

    public void setExpireTime(Date expireTime) {
        this.expireTime = expireTime;
    }

    @Override
    public String toString() {
        return "WxAppletFormData{" +
        "id=" + id +
        ", eventId=" + eventId +
        ", eventValue=" + eventValue +
        ", formId=" + formId +
        ", eventTime=" + eventTime +
        ", expireTime=" + expireTime +
        "}";
    }
}
