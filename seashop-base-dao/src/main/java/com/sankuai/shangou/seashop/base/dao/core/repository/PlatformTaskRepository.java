package com.sankuai.shangou.seashop.base.dao.core.repository;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.dao.core.domain.PlatformTaskInfo;
import com.sankuai.shangou.seashop.base.dao.core.mapper.PlatformTaskInfoMapper;
import com.sankuai.shangou.seashop.base.dao.core.po.task.QueryTaskPo;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class PlatformTaskRepository extends ServiceImpl<PlatformTaskInfoMapper, PlatformTaskInfo> {

    /**
     * 根据ID修改任务信息，传入的对象是只需要修改的部分字段
     * @param task 任务信息，需要修改的部分字段
     */
    public void updateTaskById(Long taskId, PlatformTaskInfo task) {
        LambdaUpdateWrapper<PlatformTaskInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(PlatformTaskInfo::getId, taskId);
        this.update(task, wrapper);
    }

    public List<PlatformTaskInfo> getByCondition(QueryTaskPo queryParam) {
        LambdaQueryWrapper<PlatformTaskInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(queryParam.getOperatorIdEq() != null, PlatformTaskInfo::getOperatorId, queryParam.getOperatorIdEq())
                .eq(StrUtil.isNotBlank(queryParam.getEnvEq()), PlatformTaskInfo::getEnv, queryParam.getEnvEq())
                .eq(queryParam.getBizTypeEq() != null, PlatformTaskInfo::getBizType, queryParam.getBizTypeEq())
                .eq(queryParam.getTaskTypeEq() != null, PlatformTaskInfo::getTaskType, queryParam.getTaskTypeEq())
                .in(CollUtil.isNotEmpty(queryParam.getTaskStatusListIn()), PlatformTaskInfo::getTaskStatus, queryParam.getTaskStatusListIn());
        wrapper.orderByDesc(PlatformTaskInfo::getCreateTime);
        return this.list(wrapper);
    }

}
