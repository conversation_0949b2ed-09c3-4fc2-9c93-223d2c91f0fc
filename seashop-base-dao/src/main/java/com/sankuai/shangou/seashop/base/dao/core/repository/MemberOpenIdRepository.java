package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.shangou.seashop.base.dao.core.domain.MemberOpenId;
import com.sankuai.shangou.seashop.base.dao.core.mapper.MemberOpenIdMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/26 11:06
 */
@Repository
public class MemberOpenIdRepository {

    @Resource
    private MemberOpenIdMapper memberOpenIdMapper;

    public MemberOpenId selectById(Long id){
        return memberOpenIdMapper.selectById(id);
    }

    public String getMemberOpenIdInfoByuserIdAndType(Long userId, String serviceProvider){
        if(userId == null || serviceProvider == null){
            return null;
        }
        QueryWrapper<MemberOpenId> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MemberOpenId::getUserId, userId);
        queryWrapper.lambda().eq(MemberOpenId::getServiceProvider, serviceProvider);
        MemberOpenId result = memberOpenIdMapper.selectOne(queryWrapper);
        if(Objects.isNull(result)){
            return null;
        }
        return result.getOpenId();
    }

    public MemberOpenId getInfoByUserIdAndType(Long userId, String weiXinSmallProg) {
        if(userId == null || weiXinSmallProg == null){
            return null;
        }
        QueryWrapper<MemberOpenId> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MemberOpenId::getUserId, userId);
        queryWrapper.lambda().eq(MemberOpenId::getServiceProvider, weiXinSmallProg);
        queryWrapper.lambda().orderByDesc(MemberOpenId::getCreateTime);
        MemberOpenId result = memberOpenIdMapper.selectOne(queryWrapper);
        if(Objects.isNull(result)){
            return null;
        }
        return result;
    }

    public void updateById(MemberOpenId memberOpenId) {
        memberOpenIdMapper.updateById(memberOpenId);
    }

    public void save(MemberOpenId memberOpenId) {
        memberOpenIdMapper.insert(memberOpenId);
    }
}
