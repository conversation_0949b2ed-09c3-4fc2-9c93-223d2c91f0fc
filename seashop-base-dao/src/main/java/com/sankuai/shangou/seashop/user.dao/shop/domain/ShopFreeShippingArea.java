package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 包邮地区表
 * </p>
 *
 * <AUTHOR> @since 2023-11-15
 */
@TableName("user_shop_free_shipping_area")
public class ShopFreeShippingArea implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 供应商ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 地区ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 地区全路径
     */
    @TableField("region_path")
    private String regionPath;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",exist = false)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",exist = false)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public String getRegionPath() {
        return regionPath;
    }

    public void setRegionPath(String regionPath) {
        this.regionPath = regionPath;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ShopFreeShippingArea{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", regionId=" + regionId +
        ", regionPath=" + regionPath +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
