package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 店铺订单设置表
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_order_setting")
public class OrderSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 起购量校验方式：1：起购数量和起购金额同时满足 2：起购数量和起购金额满足其一
     */
    @TableField("purchase_min_valid_type")
    private Integer purchaseMinValidType;

    /**
     * 起购数量
     */
    @TableField("purchase_min_quantity")
    private Integer purchaseMinQuantity;

    /**
     * 起购金额
     */
    @TableField("purchase_min_price")
    private BigDecimal purchaseMinPrice;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @TableField("create_user")
    private Long createUser;

    /**
     * 更新人
     */
    @TableField("update_user")
    private Long updateUser;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getPurchaseMinValidType() {
        return purchaseMinValidType;
    }

    public void setPurchaseMinValidType(Integer purchaseMinValidType) {
        this.purchaseMinValidType = purchaseMinValidType;
    }

    public Integer getPurchaseMinQuantity() {
        return purchaseMinQuantity;
    }

    public void setPurchaseMinQuantity(Integer purchaseMinQuantity) {
        this.purchaseMinQuantity = purchaseMinQuantity;
    }

    public BigDecimal getPurchaseMinPrice() {
        return purchaseMinPrice;
    }

    public void setPurchaseMinPrice(BigDecimal purchaseMinPrice) {
        this.purchaseMinPrice = purchaseMinPrice;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public Long getCreateUser() {
        return createUser;
    }

    public void setCreateUser(Long createUser) {
        this.createUser = createUser;
    }

    public Long getUpdateUser() {
        return updateUser;
    }

    public void setUpdateUser(Long updateUser) {
        this.updateUser = updateUser;
    }

    @Override
    public String toString() {
        return "OrderSetting{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", purchaseMinValidType=" + purchaseMinValidType +
        ", purchaseMinQuantity=" + purchaseMinQuantity +
        ", purchaseMinPrice=" + purchaseMinPrice +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", createUser=" + createUser +
        ", updateUser=" + updateUser +
        "}";
    }
}
