package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 运费包邮
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_shipping_free_group")
public class ShippingFreeGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运费模版ID
     */
    @TableField("template_id")
    private Long templateId;

    /**
     * 包邮条件类型
     */
    @TableField("condition_type")
    private Integer conditionType;

    /**
     * 包邮条件值
     */
    @TableField("condition_number")
    private String conditionNumber;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public Integer getConditionType() {
        return conditionType;
    }

    public void setConditionType(Integer conditionType) {
        this.conditionType = conditionType;
    }

    public String getConditionNumber() {
        return conditionNumber;
    }

    public void setConditionNumber(String conditionNumber) {
        this.conditionNumber = conditionNumber;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ShippingFreeGroup{" +
        "id=" + id +
        ", templateId=" + templateId +
        ", conditionType=" + conditionType +
        ", conditionNumber=" + conditionNumber +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
