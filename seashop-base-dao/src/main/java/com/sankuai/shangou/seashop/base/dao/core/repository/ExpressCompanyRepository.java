package com.sankuai.shangou.seashop.base.dao.core.repository;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseExpressCompany;
import com.sankuai.shangou.seashop.base.dao.core.mapper.ExpressCompanyMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ExpressCompanyRepository {

    @Resource
    private ExpressCompanyMapper expressCompanyMapper;

    public BaseExpressCompany selectById(Long id){
        return expressCompanyMapper.selectById(id);
    }

    public List<BaseExpressCompany> queryExpressCompanyList(BaseExpressCompany baseExpressCompany){
        QueryWrapper<BaseExpressCompany> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(baseExpressCompany.getId() != null,
                        BaseExpressCompany::getId, baseExpressCompany.getId())
                .eq(baseExpressCompany.getName() != null,
                        BaseExpressCompany::getName, baseExpressCompany.getName())
                .eq(baseExpressCompany.getStatus() != null, BaseExpressCompany::getStatus, baseExpressCompany.getStatus())
                .eq(baseExpressCompany.getWangdiantongCode() != null,
                        BaseExpressCompany::getWangdiantongCode, baseExpressCompany.getWangdiantongCode())
                .eq(baseExpressCompany.getJushuitanCode() != null,
                        BaseExpressCompany::getJushuitanCode, baseExpressCompany.getJushuitanCode())
                .eq(baseExpressCompany.getBoluopaiCode() != null,
                        BaseExpressCompany::getBoluopaiCode, baseExpressCompany.getBoluopaiCode())
                .eq(baseExpressCompany.getKuaidiniaoCode() != null,
                        BaseExpressCompany::getKuaidiniaoCode, baseExpressCompany.getKuaidiniaoCode())
                .eq(baseExpressCompany.getMeituanCode() != null,
                        BaseExpressCompany::getMeituanCode, baseExpressCompany.getMeituanCode());
        queryWrapper.orderByAsc("create_date");

        return expressCompanyMapper.selectList(queryWrapper);
    }


    public List<BaseExpressCompany> queryExpressCompanyByCompanyNames(List<String> companyNames) {
        QueryWrapper<BaseExpressCompany> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(BaseExpressCompany::getName, companyNames);
        queryWrapper.orderByAsc("create_date");

        return expressCompanyMapper.selectList(queryWrapper);
    }

    public List<BaseExpressCompany> queryExpressCompanyByKuaiDiNiaoCompanyCodes(List<String> companyCodes) {
        QueryWrapper<BaseExpressCompany> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(BaseExpressCompany::getKuaidiniaoCode, companyCodes);
        queryWrapper.orderByAsc("create_date");

        return expressCompanyMapper.selectList(queryWrapper);
    }

    public int insert(BaseExpressCompany baseExpressCompany){
        return expressCompanyMapper.insert(baseExpressCompany);
    }

    public int updateById(BaseExpressCompany baseExpressCompany){
        return expressCompanyMapper.updateById(baseExpressCompany);
    }

    public int deleteById(Long id){
        return expressCompanyMapper.deleteById(id);
    }
}
