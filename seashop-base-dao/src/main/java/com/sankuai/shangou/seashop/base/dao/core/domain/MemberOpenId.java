package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 登录小程序的openId
 * </p>
 *
 * <AUTHOR> @since 2023-12-26
 */
@TableName("base_member_open_id")
public class MemberOpenId implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 微信OpenID
     */
    @TableField("open_id")
    private String openId;

    /**
     * 开发平台Openid
     */
    @TableField("union_open_id")
    private String unionOpenId;

    /**
     * 开发平台Unionid
     */
    @TableField("union_id")
    private String unionId;

    /**
     * 插件名称（Himall.Plugin.OAuth.WeiXin）
     */
    @TableField("service_provider")
    private String serviceProvider;

    @TableField("app_id_type")
    private Integer appIdType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getUnionOpenId() {
        return unionOpenId;
    }

    public void setUnionOpenId(String unionOpenId) {
        this.unionOpenId = unionOpenId;
    }

    public String getUnionId() {
        return unionId;
    }

    public void setUnionId(String unionId) {
        this.unionId = unionId;
    }

    public String getServiceProvider() {
        return serviceProvider;
    }

    public void setServiceProvider(String serviceProvider) {
        this.serviceProvider = serviceProvider;
    }

    public Integer getAppIdType() {
        return appIdType;
    }

    public void setAppIdType(Integer appIdType) {
        this.appIdType = appIdType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "MemberOpenId{" +
        "id=" + id +
        ", userId=" + userId +
        ", openId=" + openId +
        ", unionOpenId=" + unionOpenId +
        ", unionId=" + unionId +
        ", serviceProvider=" + serviceProvider +
        ", appIdType=" + appIdType +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
