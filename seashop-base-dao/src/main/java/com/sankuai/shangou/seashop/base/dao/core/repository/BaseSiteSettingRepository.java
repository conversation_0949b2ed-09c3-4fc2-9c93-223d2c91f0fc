package com.sankuai.shangou.seashop.base.dao.core.repository;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.boot.utils.JsonUtil;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSettingExample;
import com.sankuai.shangou.seashop.base.dao.core.domain.MsgTemplateApplet;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class BaseSiteSettingRepository {

    @Resource
    private BaseSiteSettingMapper baseSiteSettingMapper;

    public Long create(BaseSiteSetting baseSiteSetting) {
        return (long) baseSiteSettingMapper.insert(baseSiteSetting);
    }

    public Long update(BaseSiteSetting baseSiteSetting) {
        BaseSiteSettingExample example = new BaseSiteSettingExample();
        BaseSiteSettingExample.Criteria criteria = example.createCriteria();
        criteria.andKeyEqualTo(baseSiteSetting.getKey());
        return (long) baseSiteSettingMapper.updateByExampleSelective(baseSiteSetting, example);
    }

    public void updateSettingsByKey(BaseSiteSetting setting) {
        baseSiteSettingMapper.updateSettingsByKey(setting);
    }

    public Boolean exist(BaseSiteSetting baseSiteSetting) {
        BaseSiteSettingExample example = new BaseSiteSettingExample();
        BaseSiteSettingExample.Criteria criteria = example.createCriteria();
        criteria.andKeyEqualTo(baseSiteSetting.getKey());
        return baseSiteSettingMapper.countByExample(example) > 0;
    }

    public BaseSiteSetting querySettingsValueByKey(String key) {
        return baseSiteSettingMapper.querySettingsValueByKey(key);
    }

    public List<BaseSiteSetting> query(BaseSiteSettingExample example) {
        return baseSiteSettingMapper.selectByExample(example);
    }

    public Long updateByPrimaryKey(BaseSiteSetting baseSiteSetting) {
        return (long) baseSiteSettingMapper.updateByPrimaryKeySelective(baseSiteSetting);
    }


    /**
     * 获取站点设置，
     *
     * @param id 这个id 其实可以不用  为了配合操作日志写的方法
     * @return
     */
    public BaseSiteSetting selectById(Long id) {
        BaseSiteSetting res = new BaseSiteSetting();

        Field[] fields = res.getClass().getDeclaredFields();
        List<String> settingKeys = new ArrayList<>();
        for (Field field : fields) {
            if (field.isSynthetic()) {
                //针对某些情况，编译器会引入一些字段，需要过滤掉
                continue;
            }
            String name = field.getName();
            settingKeys.add(name);
        }
        log.info(JsonUtil.toJsonString(settingKeys));

        BaseSiteSettingExample example = new BaseSiteSettingExample();
        BaseSiteSettingExample.Criteria criteria = example.createCriteria();
        criteria.andKeyIn(settingKeys);
        //根据keys 获取属性值
        List<BaseSiteSetting> list = query(example);
        log.info(JsonUtil.toJsonString(list));
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                String name = field.getName();
                if (list.stream().filter(t -> t.getKey().equals(name)).findFirst().isPresent()) {
                    BaseSiteSetting setting = list.stream().filter(t -> t.getKey().equals(name)).findFirst().get();
                    if (!setting.equals(null)) {
                        field.set(res, setting.getValue());
                    }
                }

            }
        }
        catch (Exception exception) {
            throw new RuntimeException(exception);
        }
        return res;
    }

    public MsgTemplateApplet selectMsgTemplateAppletById(Long id) {
        MsgTemplateApplet res = new MsgTemplateApplet();

        Field[] fields = res.getClass().getDeclaredFields();
        List<String> settingKeys = new ArrayList<>();
        for (Field field : fields) {
            if (field.isSynthetic()) {
                //针对某些情况，编译器会引入一些字段，需要过滤掉
                continue;
            }
            String name = field.getName();
            settingKeys.add(name);
        }
        log.info(JsonUtil.toJsonString(settingKeys));

        BaseSiteSettingExample example = new BaseSiteSettingExample();
        BaseSiteSettingExample.Criteria criteria = example.createCriteria();
        criteria.andKeyIn(settingKeys);
        //根据keys 获取属性值
        List<BaseSiteSetting> list = query(example);
        log.info(JsonUtil.toJsonString(list));
        try {
            for (Field field : fields) {
                field.setAccessible(true);
                String name = field.getName();
                if (list.stream().filter(t -> t.getKey().equals(name)).findFirst().isPresent()) {
                    BaseSiteSetting setting = list.stream().filter(t -> t.getKey().equals(name)).findFirst().get();
                    if (!setting.equals(null)) {
                        field.set(res, setting.getValue());
                    }
                }

            }
        }
        catch (Exception exception) {
            throw new RuntimeException(exception);
        }
        return res;
    }
}
