package com.sankuai.shangou.seashop.base.dao.core.domain;

import java.util.Date;
import java.util.List;

public class BaseTopic {
    private Long id;

    private String name;

    private String frontCoverImage;

    private String topImage;

    private String backgroundImage;

    private Integer platForm;

    private String tags;

    private Long shopId;

    private Boolean isRecommend;

    private String selfDefinetext;

    private Date createTime;
    private Date modifyTime;

    public Boolean getHome() {
        return home;
    }

    public void setHome(Boolean home) {
        this.home = home;
    }

    private Boolean home;
    public List<BaseTopicModule> getTopicModules() {
        return topicModules;
    }

    public void setTopicModules(List<BaseTopicModule> topicModules) {
        this.topicModules = topicModules;
    }

    private List<BaseTopicModule>  topicModules;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getFrontCoverImage() {
        return frontCoverImage;
    }

    public void setFrontCoverImage(String frontCoverImage) {
        this.frontCoverImage = frontCoverImage == null ? null : frontCoverImage.trim();
    }

    public String getTopImage() {
        return topImage;
    }

    public void setTopImage(String topImage) {
        this.topImage = topImage == null ? null : topImage.trim();
    }

    public String getBackgroundImage() {
        return backgroundImage;
    }

    public void setBackgroundImage(String backgroundImage) {
        this.backgroundImage = backgroundImage == null ? null : backgroundImage.trim();
    }

    public Integer getPlatForm() {
        return platForm;
    }

    public void setPlatForm(Integer platForm) {
        this.platForm = platForm;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags == null ? null : tags.trim();
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Boolean getIsRecommend() {
        return isRecommend;
    }

    public void setIsRecommend(Boolean isRecommend) {
        this.isRecommend = isRecommend;
    }

    public String getSelfDefinetext() {
        return selfDefinetext;
    }

    public void setSelfDefinetext(String selfDefinetext) {
        this.selfDefinetext = selfDefinetext == null ? null : selfDefinetext.trim();
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date date) {
        this.createTime = date;
    }

    public Date getModifyTime() {
        return modifyTime;
    }

    public void setModifyTime(Date date) {
        this.modifyTime = date;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BaseTopic other = (BaseTopic) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getFrontCoverImage() == null ? other.getFrontCoverImage() == null : this.getFrontCoverImage().equals(other.getFrontCoverImage()))
            && (this.getTopImage() == null ? other.getTopImage() == null : this.getTopImage().equals(other.getTopImage()))
            && (this.getBackgroundImage() == null ? other.getBackgroundImage() == null : this.getBackgroundImage().equals(other.getBackgroundImage()))
            && (this.getPlatForm() == null ? other.getPlatForm() == null : this.getPlatForm().equals(other.getPlatForm()))
            && (this.getTags() == null ? other.getTags() == null : this.getTags().equals(other.getTags()))
            && (this.getShopId() == null ? other.getShopId() == null : this.getShopId().equals(other.getShopId()))
            && (this.getIsRecommend() == null ? other.getIsRecommend() == null : this.getIsRecommend().equals(other.getIsRecommend()))
            && (this.getSelfDefinetext() == null ? other.getSelfDefinetext() == null : this.getSelfDefinetext().equals(other.getSelfDefinetext()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getFrontCoverImage() == null) ? 0 : getFrontCoverImage().hashCode());
        result = prime * result + ((getTopImage() == null) ? 0 : getTopImage().hashCode());
        result = prime * result + ((getBackgroundImage() == null) ? 0 : getBackgroundImage().hashCode());
        result = prime * result + ((getPlatForm() == null) ? 0 : getPlatForm().hashCode());
        result = prime * result + ((getTags() == null) ? 0 : getTags().hashCode());
        result = prime * result + ((getShopId() == null) ? 0 : getShopId().hashCode());
        result = prime * result + ((getIsRecommend() == null) ? 0 : getIsRecommend().hashCode());
        result = prime * result + ((getSelfDefinetext() == null) ? 0 : getSelfDefinetext().hashCode());
        return result;
    }
}