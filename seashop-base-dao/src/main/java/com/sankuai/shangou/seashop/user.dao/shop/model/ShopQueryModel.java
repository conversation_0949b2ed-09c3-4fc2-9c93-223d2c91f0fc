package com.sankuai.shangou.seashop.user.dao.shop.model;

import com.sankuai.shangou.seashop.base.boot.request.BasePageReq;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * @description:
 * @author: LXH
 **/
@Data
public class ShopQueryModel extends BasePageReq {

    // 状态
    private Integer status;
    // 审核状态
    private Integer auditStatus;
    // 店铺名
    private String shopName;
    // 店铺账户
    private String shopAccount;
    // 分类ID
    private Long categoryId;
    // 品牌ID
    private Long brandId;
    // 创建时间
    private Date createDateBegin;
    // 结束时间
    private Date createDateEnd;
    // 过期时间

    private Integer stage;
    // 店铺id集合
    private List<Long> shopIds;
    // 是否签署合同
    private Boolean whetherAgreement;
    // 是否支付保证金
    private Boolean whetherPayBond;
    // 是否需要补充资料
    private Boolean whetherSupply;
    // 是否需要续签合同
    private Boolean whetherAgainSign;
    // 商家ID
    private Long userId;
    // 是否前台搜索
    private Boolean whetherFrontSearch;
    // 合同续签最后时间
    private Date beforeExpDay;
    // 店铺id
    private Long shopId;
    // 店铺类型
    private Integer shopType;
}
