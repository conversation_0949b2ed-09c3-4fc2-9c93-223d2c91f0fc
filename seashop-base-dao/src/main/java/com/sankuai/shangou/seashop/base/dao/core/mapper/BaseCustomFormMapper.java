package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomForm;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseCustomFormMapper {
    long countByExample(BaseCustomFormExample example);

    int deleteByExample(BaseCustomFormExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseCustomForm record);

    int insertSelective(BaseCustomForm record);

    List<BaseCustomForm> selectByExample(BaseCustomFormExample example);

    BaseCustomForm selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BaseCustomForm record, @Param("example") BaseCustomFormExample example);

    int updateByExample(@Param("record") BaseCustomForm record, @Param("example") BaseCustomFormExample example);

    int updateByPrimaryKeySelective(BaseCustomForm record);

    int updateByPrimaryKey(BaseCustomForm record);
}