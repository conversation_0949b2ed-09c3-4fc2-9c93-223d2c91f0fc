package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2023-11-30
 */
@TableName("user_shop_open_api_setting")
public class ShopOpenApiSetting implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺编号
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * app_key
     */
    @TableField("app_key")
    private String appKey;

    /**
     * app_secret
     */
    @TableField("app_secret")
    private String appSecret;

    /**
     * 增加时间
     */
    @TableField("add_date")
    private Date addDate;

    /**
     * 最后重置时间
     */
    @TableField("last_edit_date")
    private Date lastEditDate;

    /**
     * 是否开启
     */
    @TableField("whether_enable")
    private Boolean whetherEnable;

    /**
     * 是否已注册
     */
    @TableField("whether_registered")
    private Boolean whetherRegistered;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public Date getAddDate() {
        return addDate;
    }

    public void setAddDate(Date addDate) {
        this.addDate = addDate;
    }

    public Date getLastEditDate() {
        return lastEditDate;
    }

    public void setLastEditDate(Date lastEditDate) {
        this.lastEditDate = lastEditDate;
    }

    public Boolean getWhetherEnable() {
        return whetherEnable;
    }

    public void setWhetherEnable(Boolean whetherEnable) {
        this.whetherEnable = whetherEnable;
    }

    public Boolean getWhetherRegistered() {
        return whetherRegistered;
    }

    public void setWhetherRegistered(Boolean whetherRegistered) {
        this.whetherRegistered = whetherRegistered;
    }

    @Override
    public String toString() {
        return "ShopOpenApiSetting{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", appKey=" + appKey +
        ", appSecret=" + appSecret +
        ", addDate=" + addDate +
        ", lastEditDate=" + lastEditDate +
        ", whetherEnable=" + whetherEnable +
        ", whetherRegistered=" + whetherRegistered +
        "}";
    }
}
