package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategoryExample;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BasePhotoSpaceCategoryMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class BasePhotoSpaceCategoryRepository {

    @Resource
    private BasePhotoSpaceCategoryMapper photoSpaceCategoryMapper;


    /**
     * 获取所有的图片分组
     *
     * @return
     */
    public List<BasePhotoSpaceCategory> getList(Long shopId) {
        BasePhotoSpaceCategoryExample example = new BasePhotoSpaceCategoryExample();
        BasePhotoSpaceCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andShopIdEqualTo(shopId);
        example.setOrderByClause(" id desc");
        return photoSpaceCategoryMapper.selectByExample(example);
    }

    /**
     * 添加分组
     *
     * @param category 分组对象
     * @return
     */
    public Long create(BasePhotoSpaceCategory category) {
        photoSpaceCategoryMapper.insert(category);
        return category.getId();
    }

    /**
     * 修改分类
     *
     * @param category 分类对象
     * @return
     */
    public Boolean update(BasePhotoSpaceCategory category) {
        BasePhotoSpaceCategoryExample example = new BasePhotoSpaceCategoryExample();
        BasePhotoSpaceCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andIdEqualTo(category.getId());
        criteria.andShopIdEqualTo(category.getShopId());
        photoSpaceCategoryMapper.updateByExampleSelective(category, example);
        return true;
    }

    /**
     * 删除分类
     *
     * @param ids    分类id集合
     * @param shopId 店铺id
     * @return
     */
    public Boolean delete(List<Long> ids, long shopId) {
        BasePhotoSpaceCategoryExample example = new BasePhotoSpaceCategoryExample();
        BasePhotoSpaceCategoryExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        criteria.andShopIdEqualTo(shopId);
        photoSpaceCategoryMapper.deleteByExample(example);
        return true;
    }

}
