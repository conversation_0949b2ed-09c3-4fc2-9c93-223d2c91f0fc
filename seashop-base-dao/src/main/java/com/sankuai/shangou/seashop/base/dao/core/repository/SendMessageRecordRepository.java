package com.sankuai.shangou.seashop.base.dao.core.repository;

import java.io.Serializable;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.dao.core.domain.SendMessageRecord;
import com.sankuai.shangou.seashop.base.dao.core.mapper.SendMessageRecordMapper;

/**
 * @description:
 * @author: LXH
 **/
@Repository
public class SendMessageRecordRepository extends ServiceImpl<SendMessageRecordMapper, SendMessageRecord> {

    public SendMessageRecord selectById(Serializable id) {
        return super.getById(id);
    }
}
