package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettled;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledExample;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseSettledMapper {
    long countByExample(BaseSettledExample example);

    int deleteByExample(BaseSettledExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseSettledWithBLOBs record);

    int insertSelective(BaseSettledWithBLOBs record);

    List<BaseSettledWithBLOBs> selectByExampleWithBLOBs(BaseSettledExample example);

    List<BaseSettledWithBLOBs> selectByExample(BaseSettledExample example);

    BaseSettledWithBLOBs selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BaseSettledWithBLOBs record, @Param("example") BaseSettledExample example);

    int updateByExampleWithBLOBs(@Param("record") BaseSettledWithBLOBs record, @Param("example") BaseSettledExample example);

    int updateByExample(@Param("record") BaseSettled record, @Param("example") BaseSettledExample example);

    int updateByPrimaryKeySelective(BaseSettledWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(BaseSettledWithBLOBs record);

    int updateByPrimaryKey(BaseSettled record);
}