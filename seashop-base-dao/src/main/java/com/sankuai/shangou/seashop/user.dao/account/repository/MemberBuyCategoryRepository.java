package com.sankuai.shangou.seashop.user.dao.account.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.user.dao.account.domain.MemberBuyCategory;
import com.sankuai.shangou.seashop.user.dao.account.mapper.MemberBuyCategoryMapper;
import org.springframework.stereotype.Repository;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Repository
public class MemberBuyCategoryRepository extends ServiceImpl<MemberBuyCategoryMapper, MemberBuyCategory> {

    public Map<Long, List<Long>> queryThreeCategoryId(List<Long> userIdList) {
        List<MemberBuyCategory> list = MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<MemberBuyCategory>()
            .select(MemberBuyCategory::getUserId, MemberBuyCategory::getCategoryId, MemberBuyCategory::getCreateTime)
            .orderByDesc(MemberBuyCategory::getCreateTime)
            .in(MemberBuyCategory::getUserId, ids)), userIdList);
        // list根据userId分组
        Map<Long, List<MemberBuyCategory>> map = list.stream().collect(Collectors.groupingBy(MemberBuyCategory::getUserId));
        // 每组保留三个
        map.forEach(
            (userId, memberBuyCategories) -> {

                if (memberBuyCategories.size() > 3) {
                    memberBuyCategories.sort((o1, o2) -> o2.getCreateTime().compareTo(o1.getCreateTime()));
                    memberBuyCategories.subList(3, memberBuyCategories.size()).clear();
                }
            }
        );

        // 转换为userId-categoryId形式
        Map<Long, List<Long>> result = new HashMap<>();

        map.forEach(
            (userId, memberBuyCategories) ->
                result.put(userId, memberBuyCategories.stream().map(MemberBuyCategory::getCategoryId).collect(Collectors.toList()))
        );
        return result;
    }
}
