package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;
import lombok.*;

/**
 * 站点设置返回对象
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Data
public class BaseSitSetting extends BaseThriftDto {
    /**
     * 站点名称
     */
    private String siteName;

    /**
     * 站点logo
     */
    private String logo;

    /**
     * 微信logo
     */
    private String wxLogo;

    /**
     * 卖家端logo
     */
    private String memberLogo;

    /**
     * 二维码
     */
    private String qrCode;

    /**
     * pc商城登录图片
     */
    private String pcLoginPic;

    /**
     * pc商城登录按钮
     */
    private String pcBottomPic;

    /**
     * 客服电话
     */
    private String sitePhone;

    /**
     * 网店授权域名
     */
    private String siteUrl;

    /**
     * 输入框关键字
     */
    private String inputBoxKeyWords;

    /**
     * 热门关键字
     */
    private String popularKeyWords;

    /**
     * 页脚
     */
    private String pageFoot;

    /**
     * SEO标题
     */
    private String siteSEOTitle;

    /**
     * SEO关键字
     */
    private String siteSEOKeywords;
    /**
     * SEO详情
     */
    private String siteSEODescription;

    /**
     * 注册类型 0 普通账号  1 手机号
     */
    private String registerType;

    /**
     * 是否打开邮箱效验
     */
    private String emailVerifOpen;

    /**
     * 是否强制绑定手机
     */
    private String isConBindCellPhone;

    /**
     * QQ地图key
     */
    private String qQMapAPIKey;

    /**
     * 高德地图key
     */
    private String jDRegionAppKey;

    /**
     * 底部服务
     */
    private String footServer;
}
