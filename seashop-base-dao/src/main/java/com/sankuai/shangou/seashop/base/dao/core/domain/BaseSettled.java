package com.sankuai.shangou.seashop.base.dao.core.domain;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BaseSettled {
    private Long id;

    private int businessType;

    private int settlementAccountType;

    private Integer trialDays;

    private int isCity;

    private int isPeopleNumber;

    private int isAddress;

    private int isBusinessLicenseCode;

    private int isBusinessScope;

    private int isBusinessLicense;

    private int isAgencyCode;

    private int isAgencyCodeLicense;

    private int isTaxpayerToProve;

    private int companyVerificationType;

    private int isSName;

    private int isSCity;

    private int isSAddress;

    private int isSidCard;

    private int isSidCardUrl;

    private int selfVerificationType;
    private String customFormJson;
    private String personalCustomFormJson;

}