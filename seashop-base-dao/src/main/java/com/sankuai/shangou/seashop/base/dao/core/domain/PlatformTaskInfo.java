package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;

/**
 * <p>
 * 供应商任务表
 * </p>
 *
 * <AUTHOR> @since 2023-12-21
 */
@TableName("base_platform_task_info")
public class PlatformTaskInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.NONE)
    private Long id;

    /**
     * 业务类型。1：导出任务
     */
    @TableField("biz_type")
    private Integer bizType;

    /**
     * 任务类型。具体的业务指定，需要唯一，最好具有一定的规则
     */
    @TableField("task_type")
    private Integer taskType;

    /**
     * 任务名称
     */
    @TableField("task_name")
    private String taskName;

    /**
     * 任务状态：10-准备就绪(初始状态)，20-执行中，30-执行成功，40-执行失败
     */
    @TableField("task_status")
    private Integer taskStatus;

    /**
     * 开始时间
     */
    @TableField("begin_time")
    private Date beginTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    private Date endTime;

    /**
     * 任务执行耗时，单位毫秒
     */
    @TableField("cost")
    private Integer cost;

    /**
     * 总记录数。多sheet导出的是所有sheet的总数
     */
    @TableField("total_num")
    private Long totalNum;

    /**
     * 成功数
     */
    @TableField("success_num")
    private Long successNum;

    /**
     * 失败数
     */
    @TableField("failed_num")
    private Long failedNum;

    /**
     * 任务执行参数
     */
    @TableField("execute_param")
    private String executeParam;

    /**
     * 任务执行结果。如果执行失败内容为部分异常内容
     */
    @TableField("execute_result")
    private String executeResult;

    /**
     * 文件路径
     */
    @TableField("file_path")
    private String filePath;

    /**
     * 操作人id
     */
    @TableField("operator_id")
    private Long operatorId;

    /**
     * 操作人账号
     */
    @TableField("operator_account")
    private String operatorAccount;

    /**
     * 重试次数
     */
    @TableField("retry_times")
    private Integer retryTimes;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 环境。主要是区分uat和prd
     */
    @TableField("env")
    private String env;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getBizType() {
        return bizType;
    }

    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    public Integer getTaskType() {
        return taskType;
    }

    public void setTaskType(Integer taskType) {
        this.taskType = taskType;
    }

    public String getTaskName() {
        return taskName;
    }

    public void setTaskName(String taskName) {
        this.taskName = taskName;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public Date getBeginTime() {
        return beginTime;
    }

    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public Integer getCost() {
        return cost;
    }

    public void setCost(Integer cost) {
        this.cost = cost;
    }

    public Long getTotalNum() {
        return totalNum;
    }

    public void setTotalNum(Long totalNum) {
        this.totalNum = totalNum;
    }

    public Long getSuccessNum() {
        return successNum;
    }

    public void setSuccessNum(Long successNum) {
        this.successNum = successNum;
    }

    public Long getFailedNum() {
        return failedNum;
    }

    public void setFailedNum(Long failedNum) {
        this.failedNum = failedNum;
    }

    public String getExecuteParam() {
        return executeParam;
    }

    public void setExecuteParam(String executeParam) {
        this.executeParam = executeParam;
    }

    public String getExecuteResult() {
        return executeResult;
    }

    public void setExecuteResult(String executeResult) {
        this.executeResult = executeResult;
    }

    public Long getOperatorId() {
        return operatorId;
    }

    public void setOperatorId(Long operatorId) {
        this.operatorId = operatorId;
    }

    public String getOperatorAccount() {
        return operatorAccount;
    }

    public void setOperatorAccount(String operatorAccount) {
        this.operatorAccount = operatorAccount;
    }

    public Integer getRetryTimes() {
        return retryTimes;
    }

    public void setRetryTimes(Integer retryTimes) {
        this.retryTimes = retryTimes;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    @Override
    public String toString() {
        return "PlatformTaskInfo{" +
        "id=" + id +
        ", bizType=" + bizType +
        ", taskType=" + taskType +
        ", taskName=" + taskName +
        ", taskStatus=" + taskStatus +
        ", beginTime=" + beginTime +
        ", endTime=" + endTime +
        ", cost=" + cost +
        ", totalNum=" + totalNum +
        ", successNum=" + successNum +
        ", failedNum=" + failedNum +
        ", executeParam=" + executeParam +
        ", executeResult=" + executeResult +
        ", operatorId=" + operatorId +
        ", operatorAccount=" + operatorAccount +
        ", retryTimes=" + retryTimes +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        ", env=" + env +
                ", filePath=" + filePath +
        "}";
    }
}
