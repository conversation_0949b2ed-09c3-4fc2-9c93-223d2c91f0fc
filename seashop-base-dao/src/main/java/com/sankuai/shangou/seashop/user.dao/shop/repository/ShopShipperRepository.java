package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopShipper;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopShipperMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/27 10:51
 */
@Repository
public class ShopShipperRepository {

    @Resource
    private ShopShipperMapper shopShipperMapper;

    public ShopShipper selectById(Long id){
        return shopShipperMapper.selectById(id);
    }

    public void insert(ShopShipper shopShipper){
        shopShipperMapper.insert(shopShipper);
    }

    public void updateById(ShopShipper shopShipper){
        shopShipperMapper.updateById(shopShipper);
    }

    public void update(ShopShipper shopShipper){
        LambdaUpdateWrapper<ShopShipper> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ShopShipper::getShopId, shopShipper.getShopId());
        shopShipperMapper.update(shopShipper,updateWrapper);
    }

    public void deleteById(Long id){
        shopShipperMapper.deleteById(id);
    }

    public List<ShopShipper> selectList(ShopShipper shopShipper) {
        QueryWrapper<ShopShipper> queryWrapper = new QueryWrapper<>();
        if(shopShipper.getId() != null){
            queryWrapper.lambda().eq(ShopShipper::getId, shopShipper.getId());
        }
        if(shopShipper.getShopId() != null){
            queryWrapper.lambda().eq(ShopShipper::getShopId, shopShipper.getShopId());
        }
        if(shopShipper.getDefaultSendGoodsFlag() != null){
            queryWrapper.lambda().eq(ShopShipper::getDefaultSendGoodsFlag, shopShipper.getDefaultSendGoodsFlag());
        }
        if(shopShipper.getDefaultGetGoodsFlag() != null){
            queryWrapper.lambda().eq(ShopShipper::getDefaultGetGoodsFlag, shopShipper.getDefaultGetGoodsFlag());
        }
        return shopShipperMapper.selectList(queryWrapper);
    }

    public List<ShopShipper> selectBatchShopShipperList(List<Long> shopIds) {
        QueryWrapper<ShopShipper> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ShopShipper::getShopId, shopIds);
        return shopShipperMapper.selectList(queryWrapper);
    }
}
