package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryApplyDetail;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.BusinessCategoryApplyDetailMapper;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 分类申请仓库类
 * @author: LXH
 **/
@Repository
public class BusinessCategoryApplyDetailRepository extends ServiceImpl<BusinessCategoryApplyDetailMapper, BusinessCategoryApplyDetail> {
    public List<BusinessCategoryApplyDetail> getByApplyId(Long applyId) {
        return lambdaQuery().eq(BusinessCategoryApplyDetail::getApplyId, applyId).list();
    }

    /**
     * 根据申请记录id 查询申请详情
     *
     * @param applyIds 申请记录id
     * @return 申请详情
     */
    public List<BusinessCategoryApplyDetail> listByApplyIds(List<Long> applyIds) {
        if (CollectionUtils.isEmpty(applyIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> list(new LambdaQueryWrapper<BusinessCategoryApplyDetail>().in(BusinessCategoryApplyDetail::getApplyId, ids)), applyIds);
    }

    public List<Long> listCategoryIdByApplyId(Long applyId) {
        List<BusinessCategoryApplyDetail> applyDetails = lambdaQuery().select(BusinessCategoryApplyDetail::getCategoryId).eq(BusinessCategoryApplyDetail::getApplyId, applyId).list();
        if (CollectionUtils.isEmpty(applyDetails)) {
            return Collections.emptyList();
        }
        return applyDetails.stream().map(BusinessCategoryApplyDetail::getCategoryId).collect(Collectors.toList());
    }
}
