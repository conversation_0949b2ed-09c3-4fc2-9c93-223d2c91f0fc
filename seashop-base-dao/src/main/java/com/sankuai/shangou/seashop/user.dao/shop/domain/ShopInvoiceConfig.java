package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 发票设置
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_shop_invoice_config")
public class ShopInvoiceConfig implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 是否提供发票
     */
    @TableField("whether_invoice")
    private Boolean whetherInvoice;

    /**
     * 是否提供普通发票
     */
    @TableField("whether_plain_invoice")
    private Boolean whetherPlainInvoice;

    /**
     * 是否提供电子发票
     */
    @TableField("whether_electronic_invoice")
    private Boolean whetherElectronicInvoice;

    /**
     * 普通发票税率
     */
    @TableField("plain_invoice_rate")
    private BigDecimal plainInvoiceRate;

    /**
     * 是否提供增值税发票
     */
    @TableField("whether_vat_invoice")
    private Boolean whetherVatInvoice;

    /**
     * 订单完成后多少天开具增值税发票
     */
    @TableField("vat_invoice_day")
    private Integer vatInvoiceDay;

    /**
     * 增值税税率
     */
    @TableField("vat_invoice_rate")
    private BigDecimal vatInvoiceRate;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Boolean getWhetherInvoice() {
        return whetherInvoice;
    }

    public void setWhetherInvoice(Boolean whetherInvoice) {
        this.whetherInvoice = whetherInvoice;
    }

    public Boolean getWhetherPlainInvoice() {
        return whetherPlainInvoice;
    }

    public void setWhetherPlainInvoice(Boolean whetherPlainInvoice) {
        this.whetherPlainInvoice = whetherPlainInvoice;
    }

    public Boolean getWhetherElectronicInvoice() {
        return whetherElectronicInvoice;
    }

    public void setWhetherElectronicInvoice(Boolean whetherElectronicInvoice) {
        this.whetherElectronicInvoice = whetherElectronicInvoice;
    }

    public BigDecimal getPlainInvoiceRate() {
        return plainInvoiceRate;
    }

    public void setPlainInvoiceRate(BigDecimal plainInvoiceRate) {
        this.plainInvoiceRate = plainInvoiceRate;
    }

    public Boolean getWhetherVatInvoice() {
        return whetherVatInvoice;
    }

    public void setWhetherVatInvoice(Boolean whetherVatInvoice) {
        this.whetherVatInvoice = whetherVatInvoice;
    }

    public Integer getVatInvoiceDay() {
        return vatInvoiceDay;
    }

    public void setVatInvoiceDay(Integer vatInvoiceDay) {
        this.vatInvoiceDay = vatInvoiceDay;
    }

    public BigDecimal getVatInvoiceRate() {
        return vatInvoiceRate;
    }

    public void setVatInvoiceRate(BigDecimal vatInvoiceRate) {
        this.vatInvoiceRate = vatInvoiceRate;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ShopInvoiceConfig{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", whetherInvoice=" + whetherInvoice +
        ", whetherPlainInvoice=" + whetherPlainInvoice +
        ", whetherElectronicInvoice=" + whetherElectronicInvoice +
        ", plainInvoiceRate=" + plainInvoiceRate +
        ", whetherVatInvoice=" + whetherVatInvoice +
        ", vatInvoiceDay=" + vatInvoiceDay +
        ", vatInvoiceRate=" + vatInvoiceRate +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
