package com.sankuai.shangou.seashop.user.dao.account.model;

import com.sankuai.shangou.seashop.base.boot.common.BaseThriftDto;

import java.util.Date;

/**
 * @description: 收藏店铺结果
 * @author: LXH
 **/
public class FavoriteShopExt extends BaseThriftDto {

    //收藏店铺id
    private Long userId;

    //店铺id
    private Long shopId;

    //店铺名称
    private String shopName;

    //店铺logo
    private String shopLogo;

    //人气
    private Integer popularity;

    //收藏时间
    private Date createTime;

    /**
     * 店铺状态
     */
    private Integer shopStatus;

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public String getShopLogo() {
        return shopLogo;
    }

    public void setShopLogo(String shopLogo) {
        this.shopLogo = shopLogo;
    }

    public Integer getPopularity() {
        return popularity;
    }

    public void setPopularity(Integer popularity) {
        this.popularity = popularity;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Long getCreateTimeLong() {
        return this.date2Long(this.createTime);
    }

    public void setCreateTimeLong(Long createTime) {
        this.createTime = this.long2Date(createTime);
    }

    public Integer getShopStatus() {
        return shopStatus;
    }

    public void setShopStatus(Integer shopStatus) {
        this.shopStatus = shopStatus;
    }
}
