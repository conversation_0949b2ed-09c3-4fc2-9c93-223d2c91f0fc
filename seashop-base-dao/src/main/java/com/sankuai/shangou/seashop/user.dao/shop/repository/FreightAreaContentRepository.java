package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.shop.domain.FreightAreaContent;
import com.sankuai.shangou.seashop.user.dao.shop.domain.FreightAreaDetail;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.FreightAreaContentMapper;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.FreightAreaDetailMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;


@Repository
public class FreightAreaContentRepository extends ServiceImpl<FreightAreaContentMapper, FreightAreaContent> {

    @Resource
    private FreightAreaDetailMapper freightAreaDetailMapper;

    public FreightAreaContent selectById(Long id) {
        return this.getById(id);
    }

    public int getDetailReqListSize(Long contentId) {
        LambdaQueryWrapper<FreightAreaDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FreightAreaDetail::getFreightAreaId, contentId);
        return Math.toIntExact(freightAreaDetailMapper.selectCount(queryWrapper));
    }

    public List<FreightAreaContent> getByTemplateIdList(List<Long> templateIdList) {
        LambdaQueryWrapper<FreightAreaContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FreightAreaContent::getFreightTemplateId, templateIdList);
        return this.list(queryWrapper);
    }
}
