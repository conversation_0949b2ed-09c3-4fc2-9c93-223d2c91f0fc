package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategoryExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BasePhotoSpaceCategoryMapper {
    long countByExample(BasePhotoSpaceCategoryExample example);

    int deleteByExample(BasePhotoSpaceCategoryExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BasePhotoSpaceCategory record);

    int insertSelective(BasePhotoSpaceCategory record);

    List<BasePhotoSpaceCategory> selectByExample(BasePhotoSpaceCategoryExample example);

    BasePhotoSpaceCategory selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BasePhotoSpaceCategory record, @Param("example") BasePhotoSpaceCategoryExample example);

    int updateByExample(@Param("record") BasePhotoSpaceCategory record, @Param("example") BasePhotoSpaceCategoryExample example);

    int updateByPrimaryKeySelective(BasePhotoSpaceCategory record);

    int updateByPrimaryKey(BasePhotoSpaceCategory record);
}