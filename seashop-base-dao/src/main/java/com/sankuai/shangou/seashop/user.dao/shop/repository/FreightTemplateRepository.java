package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.shop.domain.FreightAreaContent;
import com.sankuai.shangou.seashop.user.dao.shop.domain.FreightTemplate;
import com.sankuai.shangou.seashop.user.dao.shop.domain.RestrictedArea;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShippingFreeGroup;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.FreightAreaContentMapper;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.FreightTemplateMapper;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.RestrictedAreaMapper;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ShippingFreeGroupMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class FreightTemplateRepository extends ServiceImpl<FreightTemplateMapper, FreightTemplate> {

    @Resource
    private FreightAreaContentMapper areaContentMapper;

    @Resource
    private ShippingFreeGroupMapper shippingFreeGroupMapper;

    @Resource
    private RestrictedAreaMapper restrictedAreaMapper;

    public FreightTemplate selectById(Long id) {
        return this.getById(id);
    }

    public int getGroupReqListSize(Long tempId) {
        LambdaQueryWrapper<ShippingFreeGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShippingFreeGroup::getTemplateId, tempId);
        return Math.toIntExact(shippingFreeGroupMapper.selectCount(queryWrapper));
    }

    public int getRestrictedAreaReqListSize(Long tempId) {
        LambdaQueryWrapper<RestrictedArea> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RestrictedArea::getTemplateId, tempId);
        return Math.toIntExact(restrictedAreaMapper.selectCount(queryWrapper));
    }

    public int getContentReqListSize(Long tempId) {
        LambdaQueryWrapper<FreightAreaContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FreightAreaContent::getFreightTemplateId, tempId);
        return Math.toIntExact(areaContentMapper.selectCount(queryWrapper));
    }

    /**
     * 查询所有开启非销售区域隐藏的运费模板
     * @return 运费模板列表
     */
    public List<FreightTemplate> getAllNonSaleAreaHideOn() {
        LambdaQueryWrapper<FreightTemplate> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(FreightTemplate::getNonSalesAreaHide, true);
        return this.list(queryWrapper);
    }

}
