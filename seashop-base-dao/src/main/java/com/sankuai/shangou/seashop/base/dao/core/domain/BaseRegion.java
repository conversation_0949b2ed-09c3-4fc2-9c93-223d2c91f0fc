package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@EqualsAndHashCode
@ToString
public class BaseRegion {
    private Long id;

    private String code;

    private String name;

    private String shortName;

    private Integer status;

    private Long parentId;

    private Integer regionLevel;

    private Integer left;

    private Integer right;

    private Date createTime;

    @TableField(exist = false)
    private Integer subCount;

    private Date updateTime;

    private Boolean custom;

    private String center;
}