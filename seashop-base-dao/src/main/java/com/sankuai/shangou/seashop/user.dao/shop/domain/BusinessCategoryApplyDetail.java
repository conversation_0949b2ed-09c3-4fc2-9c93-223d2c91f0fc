package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 供应商申请经营分类明细
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_business_category_apply_detail")
public class BusinessCategoryApplyDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分佣比例
     */
    @TableField("commission_rate")
    private BigDecimal commissionRate;

    /**
     * 类目ID
     */
    @TableField("category_id")
    private Long categoryId;

    /**
     * 申请Id
     */
    @TableField("apply_id")
    private Long applyId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getCommissionRate() {
        return commissionRate;
    }

    public void setCommissionRate(BigDecimal commissionRate) {
        this.commissionRate = commissionRate;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public Long getApplyId() {
        return applyId;
    }

    public void setApplyId(Long applyId) {
        this.applyId = applyId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BusinessCategoryApplyDetail{" +
                "id=" + id +
                ", commissionRate=" + commissionRate +
                ", categoryId=" + categoryId +
                ", applyId=" + applyId +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
