package com.sankuai.shangou.seashop.user.dao.account.repository;//package com.sankuai.shangou.seashop.user.dao.account.repository;
//
//import java.util.List;
//import java.util.Set;
//
//import javax.annotation.Resource;
//
//import org.springframework.stereotype.Repository;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.github.pagehelper.Page;
//import com.github.pagehelper.PageHelper;
//import com.sankuai.shangou.seashop.user.dao.account.domain.UserExclusiveMember;
//import com.sankuai.shangou.seashop.user.dao.account.mapper.UserExclusiveMemberMapper;
//import com.sankuai.shangou.seashop.user.dao.account.model.CmdUserShopModel;
//import com.sankuai.shangou.seashop.user.dao.account.model.QueryExclusiveMemberModel;
//import com.sankuai.shangou.seashop.user.dao.account.model.UserExclusiveMemberExt;
//import com.sankuai.shangou.seashop.user.dao.account.model.UserMemberModel;
//
//import lombok.extern.slf4j.Slf4j;
//
//
///**
// * @description: 专属商家
// * @author: liweisong
// **/
//@Repository
//@Slf4j
//public class UserExclusiveMemberRepository {
//
//    @Resource
//    private UserExclusiveMemberMapper userExclusiveMemberMapper;
//
//    public Boolean queryIzOpenExclusiveMember(Long shopId){
//        return userExclusiveMemberMapper.queryIzOpenExclusiveMember(shopId);
//    }
//
//    public Page<UserExclusiveMemberExt> queryExclusivePage(QueryExclusiveMemberModel query, Integer pageNo, Integer pageSize){
//        Page<UserExclusiveMemberExt> exclusiveExtPage = PageHelper.startPage(pageNo, pageSize);
//        userExclusiveMemberMapper.queryExclusiveList(query);
//        return exclusiveExtPage;
//    }
//
//    public UserExclusiveMember queryUserExclusiveMember(UserExclusiveMember userExclusiveMember) {
//        QueryWrapper<UserExclusiveMember> memberQueryWrapper = new QueryWrapper<UserExclusiveMember>();
//        if (userExclusiveMember.getShopId() != null) {
//            memberQueryWrapper.lambda().eq(UserExclusiveMember::getShopId, userExclusiveMember.getShopId());
//        }
//        if (userExclusiveMember.getUserId() != null) {
//            memberQueryWrapper.lambda().eq(UserExclusiveMember::getUserId, userExclusiveMember.getUserId());
//        }
//        return userExclusiveMemberMapper.selectOne(memberQueryWrapper);
//    }
//
//
//    public Set<Long> queryMatchUserExclusiveShops(Long userId, List<Long> shopIds) {
//        return userExclusiveMemberMapper.queryMatchUserExclusiveShops(userId, shopIds);
//    }
//
//    public int insert(UserExclusiveMember userExclusiveMember){
//        return userExclusiveMemberMapper.insert(userExclusiveMember);
//    }
//
//    public Integer batchInsert(List<UserExclusiveMember> userExclusiveMemberList){
//        return userExclusiveMemberMapper.insertBatchSomeColumn(userExclusiveMemberList);
//    }
//
//    public int deleteById(Long id){
//        return userExclusiveMemberMapper.deleteById(id);
//    }
//
//    public List<UserMemberModel> queryMemberByNames(List<String> userNames){
//        return userExclusiveMemberMapper.queryMemberByNames(userNames);
//    }
//
//    public void openUserShop(CmdUserShopModel req){
//        userExclusiveMemberMapper.openUserShop(req);
//    }
//}
