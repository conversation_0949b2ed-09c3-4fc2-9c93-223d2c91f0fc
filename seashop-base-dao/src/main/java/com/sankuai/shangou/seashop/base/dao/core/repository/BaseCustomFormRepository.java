package com.sankuai.shangou.seashop.base.dao.core.repository;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import com.sankuai.shangou.seashop.base.boot.exception.BusinessException;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomForm;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormExample;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormFieldExample;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormFieldMapper;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class BaseCustomFormRepository {

    @Resource
    private BaseCustomFormMapper baseCustomFormMapper;

    @Resource
    private BaseCustomFormFieldMapper baseCustomFormFieldMapper;

    @Transactional
    public Long create(BaseCustomForm customForm) {
        BaseCustomFormExample example = new BaseCustomFormExample();
        BaseCustomFormExample.Criteria criteria = example.createCriteria();
        criteria.andNameEqualTo(customForm.getName());
        long count = baseCustomFormMapper.countByExample(example);
        if (count > 0) {
            throw new BusinessException("已经存在该名称的表单");
        }
        baseCustomFormMapper.insert(customForm);

        for (BaseCustomFormField formField : customForm.getFormFields()) {
            formField.setFormId(customForm.getId());
            formField.setAddedDate(customForm.getCreateDate());
            baseCustomFormFieldMapper.insert(formField);
        }

        return customForm.getId();
    }

    public Boolean update(BaseCustomForm customForm) {

        BaseCustomFormExample example = new BaseCustomFormExample();
        BaseCustomFormExample.Criteria criteria = example.createCriteria();
        criteria.andNameEqualTo(customForm.getName());
        criteria.andIdNotEqualTo(customForm.getId());
        long count = baseCustomFormMapper.countByExample(example);
        if (count > 0) {
            throw new BusinessException("已经存在该名称的表单");
        }
        baseCustomFormMapper.updateByPrimaryKey(customForm);
        // 字段ID不能变
        if(CollectionUtils.isEmpty(customForm.getFormFields())){
            baseCustomFormFieldMapper.deleteByFormId(customForm.getId());
            return true;
        }
        List<BaseCustomFormField> fieldList = customForm.getFormFields().stream().filter(v -> v.getId() != null).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(fieldList)){
            baseCustomFormFieldMapper.deleteByFormId(customForm.getId());
            for (BaseCustomFormField formField : customForm.getFormFields()) {
                formField.setFormId(customForm.getId());
                formField.setAddedDate(customForm.getCreateDate());
                baseCustomFormFieldMapper.insert(formField);
            }
            return true;
        }
        // 删掉属于这个表单但是前端没传过来的字段ID
        List<Long> fieldIds = fieldList.stream().map(BaseCustomFormField::getId).collect(Collectors.toList());
        baseCustomFormFieldMapper.deleteByFormIdNotInIds(customForm.getId(), fieldIds);
        for (BaseCustomFormField formField : customForm.getFormFields()) {
            formField.setFormId(customForm.getId());
            formField.setAddedDate(customForm.getCreateDate());
            if(formField.getId() == null){
                baseCustomFormFieldMapper.insert(formField);
            } else {
                baseCustomFormFieldMapper.updateByPrimaryKey(formField);
            }
        }
        return true;
    }

    public Boolean deletes(List<Long> customFormIds)  {
        BaseCustomFormExample example = new BaseCustomFormExample();
        BaseCustomFormExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(customFormIds);
        baseCustomFormMapper.deleteByExample(example);

        BaseCustomFormFieldExample formFieldExample = new BaseCustomFormFieldExample();
        BaseCustomFormFieldExample.Criteria formFieldCriteria = formFieldExample.createCriteria();
        formFieldCriteria.andFormIdIn(customFormIds);
        baseCustomFormFieldMapper.deleteByExample(formFieldExample);
        return true;
    }

    public List<BaseCustomForm> query(BaseCustomFormExample example) {
        return baseCustomFormMapper.selectByExample(example);
    }


    public BaseCustomForm get(long id) {
        BaseCustomForm result = baseCustomFormMapper.selectByPrimaryKey(id);

        if (result == null) {
            return null;
        }
        BaseCustomFormFieldExample formFieldExample = new BaseCustomFormFieldExample();
        BaseCustomFormFieldExample.Criteria formFieldCriteria = formFieldExample.createCriteria();
        formFieldCriteria.andFormIdEqualTo(id);

        List<BaseCustomFormField> fields = baseCustomFormFieldMapper.selectByExample(formFieldExample);
        result.setFormFields(fields);
        return result;
    }

    public List<BaseCustomForm> queryCustomFormByFormIds(List<Long> formIds) {
        if (CollectionUtils.isEmpty(formIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> {
            BaseCustomFormExample example = new BaseCustomFormExample();
            BaseCustomFormExample.Criteria criteria = example.createCriteria();
            criteria.andIdIn(formIds);
            return baseCustomFormMapper.selectByExample(example);
        }, formIds);
    }

    public List<BaseCustomFormField> queryCustomFieldByFieldIds(List<Long> fieldIds) {
        if (CollectionUtils.isEmpty(fieldIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> {
            BaseCustomFormFieldExample example = new BaseCustomFormFieldExample();
            BaseCustomFormFieldExample.Criteria criteria = example.createCriteria();
            criteria.andIdIn(ids);
            return baseCustomFormFieldMapper.selectByExample(example);
        }, fieldIds);
    }

    public List<BaseCustomFormField> queryCustomFieldByFormIds(List<Long> formIds) {
        if (CollectionUtils.isEmpty(formIds)) {
            return Collections.EMPTY_LIST;
        }

        return MybatisUtil.queryBatch(ids -> {
            BaseCustomFormFieldExample example = new BaseCustomFormFieldExample();
            BaseCustomFormFieldExample.Criteria criteria = example.createCriteria();
            criteria.andFormIdIn(ids);
            return baseCustomFormFieldMapper.selectByExample(example);
        }, formIds);
    }

    public Boolean existCustomFrom(String name) {
        BaseCustomFormExample example = new BaseCustomFormExample();
        BaseCustomFormExample.Criteria criteria = example.createCriteria();
        criteria.andNameEqualTo(name);
        List<BaseCustomForm> list = baseCustomFormMapper.selectByExample(example);
        if (list != null && list.stream().count() > 0) {
            return true;
        }
        return false;
    }
}
