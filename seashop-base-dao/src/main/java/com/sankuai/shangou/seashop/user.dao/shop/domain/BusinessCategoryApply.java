package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 供应商申请经营分类表
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_business_category_apply")
public class BusinessCategoryApply implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 申请日期
     */
    @TableField("apply_date")
    private Date applyDate;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 审核状态
     */
    @TableField("audited_status")
    private Integer auditedStatus;

    @TableField("audited_date")
    private Date auditedDate;

    /**
     * 拒绝原因
     */
    @TableField("refuse_reason")
    private String refuseReason;

    /**
     * 签署协议状态,待签署=0,已签署=1
     */
    @TableField("agreement_status")
    private Integer agreementStatus;

    /**
     * 签署协议的对应的协议Id
     */
    @TableField("shop_agreement_id")
    private Long shopAgreementId;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getApplyDate() {
        return applyDate;
    }

    public void setApplyDate(Date applyDate) {
        this.applyDate = applyDate;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getShopName() {
        return shopName;
    }

    public void setShopName(String shopName) {
        this.shopName = shopName;
    }

    public Integer getAuditedStatus() {
        return auditedStatus;
    }

    public void setAuditedStatus(Integer auditedStatus) {
        this.auditedStatus = auditedStatus;
    }

    public Date getAuditedDate() {
        return auditedDate;
    }

    public void setAuditedDate(Date auditedDate) {
        this.auditedDate = auditedDate;
    }

    public String getRefuseReason() {
        return refuseReason;
    }

    public void setRefuseReason(String refuseReason) {
        this.refuseReason = refuseReason;
    }

    public Integer getAgreementStatus() {
        return agreementStatus;
    }

    public void setAgreementStatus(Integer agreementStatus) {
        this.agreementStatus = agreementStatus;
    }

    public Long getShopAgreementId() {
        return shopAgreementId;
    }

    public void setShopAgreementId(Long shopAgreementId) {
        this.shopAgreementId = shopAgreementId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BusinessCategoryApply{" +
        "id=" + id +
        ", applyDate=" + applyDate +
        ", shopId=" + shopId +
        ", shopName=" + shopName +
        ", auditedStatus=" + auditedStatus +
        ", auditedDate=" + auditedDate +
        ", refuseReason=" + refuseReason +
        ", agreementStatus=" + agreementStatus +
        ", shopAgreementId=" + shopAgreementId +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
