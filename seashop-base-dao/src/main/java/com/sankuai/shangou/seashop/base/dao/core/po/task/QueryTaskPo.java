package com.sankuai.shangou.seashop.base.dao.core.po.task;

import lombok.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class QueryTaskPo {

    /**
     * 操作人ID
     */
    private Long operatorIdEq;
    /**
     * 查询环境
     */
    private String envEq;
    /**
     * 业务类型。1：导出任务
     */
    private Integer bizTypeEq;
    /**
     * 任务类型。具体的业务指定，需要唯一，最好具有一定的规则
     */
    private Integer taskTypeEq;
    /**
     * 任务状态列表
     */
    private List<Integer> taskStatusListIn;

}
