package com.sankuai.shangou.seashop.user.dao.account.repository;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.account.domain.Privilege;
import com.sankuai.shangou.seashop.user.dao.account.mapper.PrivilegeMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;

/**
 * @description: 权限组仓库类
 * @author: LXH
 **/
@Repository
@Slf4j
public class PrivilegeRepository extends ServiceImpl<PrivilegeMapper, Privilege> {

    public List<Privilege> selectList(Privilege privilege) {
        QueryWrapper<Privilege> queryWrapper = new QueryWrapper<>();
        if (privilege.getPlatformId() != null) {
            queryWrapper.lambda().eq(Privilege::getPlatformId, privilege.getPlatformId());
        }
        if (privilege.getPowerType() != null) {
            queryWrapper.lambda().eq(Privilege::getPowerType, privilege.getPowerType());
        }
        if (privilege.getShopType() != null) {
            queryWrapper.lambda().ne(Privilege::getShopType, privilege.getShopType());
        }
        queryWrapper.lambda().eq(Privilege::getWhetherDelete, false);
        queryWrapper.lambda().orderByAsc(Privilege::getDisplaySequence);
        return baseMapper.selectList(queryWrapper);
    }

    public void updateByIdAndPlat(Long id, int i, String path, String icon, String activedIcon) {
        this.lambdaUpdate()
            .eq(Privilege::getId, id)
            .eq(Privilege::getPlatformId, i)
            .set(Privilege::getUrl, path)
            .set(Privilege::getIcon, icon)
            .set(Privilege::getActivedIcon, activedIcon).update();
    }

    public List<Privilege> selectUrlByIds(List<Integer> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<Privilege> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Privilege::getId, ids);
        List<Privilege> list = baseMapper.selectList(queryWrapper);
        return list;
    }
}
