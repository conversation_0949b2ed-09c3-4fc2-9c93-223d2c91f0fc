package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.IdType;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR> @since 2023-12-01
 */
@TableName("base_operation_log")
public class BaseOperationLog implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 业务板块id
     */
    @TableField("module_id")
    private Integer moduleId;

    /**
     * 业务板块名称
     */
    @TableField("module_name")
    private String moduleName;

    /**
     * 操作类型
     */
    @TableField("operation_type")
    private Integer operationType;

    /**
     * 操作类型名称
     */
    @TableField("operation_name")
    private String operationName;

    /**
     * 操作时间
     */
    @TableField("operation_time")
    private Date operationTime;

    /**
     * 操作人id
     */
    @TableField("operation_user_id")
    private Long operationUserId;

    /**
     * 操作人账号
     */
    @TableField("operation_user_account")
    private String operationUserAccount;

    /**
     * 操作人名称
     */
    @TableField("operation_user_name")
    private String operationUserName;

    /**
     * 操作数据详情
     */
    @TableField("operation_content")
    private String operationContent;


    @TableField("action_name")
    private String actionName;

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getModuleId() {
        return moduleId;
    }

    public void setModuleId(Integer moduleId) {
        this.moduleId = moduleId;
    }

    public String getModuleName() {
        return moduleName;
    }

    public void setModuleName(String moduleName) {
        this.moduleName = moduleName;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public void setOperationType(Integer operationType) {
        this.operationType = operationType;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public Date getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    public Long getOperationUserId() {
        return operationUserId;
    }

    public void setOperationUserId(Long operationUserId) {
        this.operationUserId = operationUserId;
    }

    public String getOperationUserAccount() {
        return operationUserAccount;
    }

    public void setOperationUserAccount(String operationUserAccount) {
        this.operationUserAccount = operationUserAccount;
    }

    public String getOperationUserName() {
        return operationUserName;
    }

    public void setOperationUserName(String operationUserName) {
        this.operationUserName = operationUserName;
    }

    public String getOperationContent() {
        return operationContent;
    }

    public void setOperationContent(String operationContent) {
        this.operationContent = operationContent;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    @Override
    public String toString() {
        return "BaseOperationLog{" +
                "id=" + id +
                ", moduleId=" + moduleId +
                ", moduleName=" + moduleName +
                ", operationType=" + operationType +
                ", operationName=" + operationName +
                ", operationTime=" + operationTime +
                ", operationUserId=" + operationUserId +
                ", operationUserAccount=" + operationUserAccount +
                ", operationUserName=" + operationUserName +
                ", operationContent=" + operationContent +
                ", shopId=" + shopId +
                ", actionName=" + actionName +
                "}";
    }
}
