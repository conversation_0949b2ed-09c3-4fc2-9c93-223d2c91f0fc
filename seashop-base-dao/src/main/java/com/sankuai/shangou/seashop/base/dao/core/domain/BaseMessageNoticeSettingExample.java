package com.sankuai.shangou.seashop.base.dao.core.domain;

import java.util.ArrayList;
import java.util.List;

public class BaseMessageNoticeSettingExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public BaseMessageNoticeSettingExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andMessageTypeIsNull() {
            addCriterion("message_type is null");
            return (Criteria) this;
        }

        public Criteria andMessageTypeIsNotNull() {
            addCriterion("message_type is not null");
            return (Criteria) this;
        }

        public Criteria andMessageTypeEqualTo(Integer value) {
            addCriterion("message_type =", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNotEqualTo(Integer value) {
            addCriterion("message_type <>", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeGreaterThan(Integer value) {
            addCriterion("message_type >", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("message_type >=", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeLessThan(Integer value) {
            addCriterion("message_type <", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeLessThanOrEqualTo(Integer value) {
            addCriterion("message_type <=", value, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeIn(List<Integer> values) {
            addCriterion("message_type in", values, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNotIn(List<Integer> values) {
            addCriterion("message_type not in", values, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeBetween(Integer value1, Integer value2) {
            addCriterion("message_type between", value1, value2, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("message_type not between", value1, value2, "messageType");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameIsNull() {
            addCriterion("message_type_name is null");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameIsNotNull() {
            addCriterion("message_type_name is not null");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameEqualTo(String value) {
            addCriterion("message_type_name =", value, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameNotEqualTo(String value) {
            addCriterion("message_type_name <>", value, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameGreaterThan(String value) {
            addCriterion("message_type_name >", value, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameGreaterThanOrEqualTo(String value) {
            addCriterion("message_type_name >=", value, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameLessThan(String value) {
            addCriterion("message_type_name <", value, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameLessThanOrEqualTo(String value) {
            addCriterion("message_type_name <=", value, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameLike(String value) {
            addCriterion("message_type_name like", value, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameNotLike(String value) {
            addCriterion("message_type_name not like", value, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameIn(List<String> values) {
            addCriterion("message_type_name in", values, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameNotIn(List<String> values) {
            addCriterion("message_type_name not in", values, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameBetween(String value1, String value2) {
            addCriterion("message_type_name between", value1, value2, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andMessageTypeNameNotBetween(String value1, String value2) {
            addCriterion("message_type_name not between", value1, value2, "messageTypeName");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeIsNull() {
            addCriterion("emaill_notice is null");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeIsNotNull() {
            addCriterion("emaill_notice is not null");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeEqualTo(Boolean value) {
            addCriterion("emaill_notice =", value, "emaillNotice");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeNotEqualTo(Boolean value) {
            addCriterion("emaill_notice <>", value, "emaillNotice");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeGreaterThan(Boolean value) {
            addCriterion("emaill_notice >", value, "emaillNotice");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("emaill_notice >=", value, "emaillNotice");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeLessThan(Boolean value) {
            addCriterion("emaill_notice <", value, "emaillNotice");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeLessThanOrEqualTo(Boolean value) {
            addCriterion("emaill_notice <=", value, "emaillNotice");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeIn(List<Boolean> values) {
            addCriterion("emaill_notice in", values, "emaillNotice");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeNotIn(List<Boolean> values) {
            addCriterion("emaill_notice not in", values, "emaillNotice");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeBetween(Boolean value1, Boolean value2) {
            addCriterion("emaill_notice between", value1, value2, "emaillNotice");
            return (Criteria) this;
        }

        public Criteria andEmaillNoticeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("emaill_notice not between", value1, value2, "emaillNotice");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeIsNull() {
            addCriterion("sms_notice is null");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeIsNotNull() {
            addCriterion("sms_notice is not null");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeEqualTo(Boolean value) {
            addCriterion("sms_notice =", value, "smsNotice");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeNotEqualTo(Boolean value) {
            addCriterion("sms_notice <>", value, "smsNotice");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeGreaterThan(Boolean value) {
            addCriterion("sms_notice >", value, "smsNotice");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("sms_notice >=", value, "smsNotice");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeLessThan(Boolean value) {
            addCriterion("sms_notice <", value, "smsNotice");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeLessThanOrEqualTo(Boolean value) {
            addCriterion("sms_notice <=", value, "smsNotice");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeIn(List<Boolean> values) {
            addCriterion("sms_notice in", values, "smsNotice");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeNotIn(List<Boolean> values) {
            addCriterion("sms_notice not in", values, "smsNotice");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeBetween(Boolean value1, Boolean value2) {
            addCriterion("sms_notice between", value1, value2, "smsNotice");
            return (Criteria) this;
        }

        public Criteria andSmsNoticeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("sms_notice not between", value1, value2, "smsNotice");
            return (Criteria) this;
        }

        public Criteria andWxNoticeIsNull() {
            addCriterion("wx_notice is null");
            return (Criteria) this;
        }

        public Criteria andWxNoticeIsNotNull() {
            addCriterion("wx_notice is not null");
            return (Criteria) this;
        }

        public Criteria andWxNoticeEqualTo(Boolean value) {
            addCriterion("wx_notice =", value, "wxNotice");
            return (Criteria) this;
        }

        public Criteria andWxNoticeNotEqualTo(Boolean value) {
            addCriterion("wx_notice <>", value, "wxNotice");
            return (Criteria) this;
        }

        public Criteria andWxNoticeGreaterThan(Boolean value) {
            addCriterion("wx_notice >", value, "wxNotice");
            return (Criteria) this;
        }

        public Criteria andWxNoticeGreaterThanOrEqualTo(Boolean value) {
            addCriterion("wx_notice >=", value, "wxNotice");
            return (Criteria) this;
        }

        public Criteria andWxNoticeLessThan(Boolean value) {
            addCriterion("wx_notice <", value, "wxNotice");
            return (Criteria) this;
        }

        public Criteria andWxNoticeLessThanOrEqualTo(Boolean value) {
            addCriterion("wx_notice <=", value, "wxNotice");
            return (Criteria) this;
        }

        public Criteria andWxNoticeIn(List<Boolean> values) {
            addCriterion("wx_notice in", values, "wxNotice");
            return (Criteria) this;
        }

        public Criteria andWxNoticeNotIn(List<Boolean> values) {
            addCriterion("wx_notice not in", values, "wxNotice");
            return (Criteria) this;
        }

        public Criteria andWxNoticeBetween(Boolean value1, Boolean value2) {
            addCriterion("wx_notice between", value1, value2, "wxNotice");
            return (Criteria) this;
        }

        public Criteria andWxNoticeNotBetween(Boolean value1, Boolean value2) {
            addCriterion("wx_notice not between", value1, value2, "wxNotice");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}