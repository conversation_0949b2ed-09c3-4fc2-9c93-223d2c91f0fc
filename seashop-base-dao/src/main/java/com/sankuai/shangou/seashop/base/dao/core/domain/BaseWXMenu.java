package com.sankuai.shangou.seashop.base.dao.core.domain;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

@TableName("base_wx_menu")
public class BaseWXMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 菜单标题
     */
    @TableField("name")
    private String name;

    /**
     * 链接类型：0无链接，1微商城，2小程序
     */
    @TableField("link_type")
    private int linkType;

    /**
     * 链接值
     */
    @TableField("link_value")
    private String linkValue;

    /**
     * 父级id
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 是否自定义菜单
     */
    @TableField("whether_custom")
    private int whetherCustom;

    @TableField("create_time")
    private Date createTime;
    @TableField("update_time")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getLinkType() {
        return linkType;
    }

    public void setLinkType(int linkType) {
        this.linkType = linkType;
    }

    public String getLinkValue() {
        return linkValue;
    }

    public void setLinkValue(String linkValue) {
        this.linkValue = linkValue;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getWhetherCustom() {
        return whetherCustom;
    }

    public void setWhetherCustom(Integer whetherCustom) {
        this.whetherCustom = whetherCustom;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }


    @Override
    public String toString() {
        return "BaseWXMenu{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", linkType=" + linkType +
            ", linkValue='" + linkValue + '\'' +
            ", parentId=" + parentId +
            ", whetherCustom=" + whetherCustom +
            ", createTime=" + createTime +
            ", updateTime=" + updateTime +
            '}';
    }
}
