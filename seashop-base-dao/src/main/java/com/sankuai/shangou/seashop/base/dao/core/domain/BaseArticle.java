package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.TableField;

import java.util.Date;

public class BaseArticle {
    private Long id;

    /**
     * 文章分类id
     */
    private Long categoryId;

    /**
     * 标题
     */
    private String title;

    /**
     * icon url
     */
    private String iconUrl;

    /**
     * 添加时间
     */
    private Date addDate;

    /**
     * 排序字段
     */
    private Long displaySequence;

    /**
     * 是否显示
     */
    private Boolean isRelease;


    /**
     * 文章seo标题
     */
    @TableField(exist = false)
    private String seoTitle;

    /**
     * 文档seo详情
     */
    @TableField(exist = false)
    private String seoDescription;

    /**
     * 文章seo关键字
     */
    @TableField(exist = false)
    private String seoKeywords;
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    public String getIconUrl() {
        return iconUrl;
    }

    public void setIconUrl(String iconUrl) {
        this.iconUrl = iconUrl == null ? null : iconUrl.trim();
    }

    public Date getAddDate() {
        return addDate;
    }

    public void setAddDate(Date addDate) {
        this.addDate = addDate;
    }

    public Long getDisplaySequence() {
        return displaySequence;
    }

    public void setDisplaySequence(Long displaySequence) {
        this.displaySequence = displaySequence;
    }

    public Boolean getIsRelease() {
        return isRelease;
    }

    public void setIsRelease(Boolean isRelease) {
        this.isRelease = isRelease;
    }


    public String getSeoTitle() {
        return seoTitle;
    }

    public void setSeoTitle(String seoTitle) {
        this.seoTitle = seoTitle == null ? null : seoTitle.trim();
    }

    public String getSeoDescription() {
        return seoDescription;
    }

    public void setSeoDescription(String seoDescription) {
        this.seoDescription = seoDescription == null ? null : seoDescription.trim();
    }

    public String getSeoKeywords() {
        return seoKeywords;
    }

    public void setSeoKeywords(String seoKeywords) {
        this.seoKeywords = seoKeywords == null ? null : seoKeywords.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BaseArticle other = (BaseArticle) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getCategoryId() == null ? other.getCategoryId() == null : this.getCategoryId().equals(other.getCategoryId()))
            && (this.getTitle() == null ? other.getTitle() == null : this.getTitle().equals(other.getTitle()))
            && (this.getIconUrl() == null ? other.getIconUrl() == null : this.getIconUrl().equals(other.getIconUrl()))
            && (this.getAddDate() == null ? other.getAddDate() == null : this.getAddDate().equals(other.getAddDate()))
            && (this.getDisplaySequence() == null ? other.getDisplaySequence() == null : this.getDisplaySequence().equals(other.getDisplaySequence()))
            && (this.getIsRelease() == null ? other.getIsRelease() == null : this.getIsRelease().equals(other.getIsRelease()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getCategoryId() == null) ? 0 : getCategoryId().hashCode());
        result = prime * result + ((getTitle() == null) ? 0 : getTitle().hashCode());
        result = prime * result + ((getIconUrl() == null) ? 0 : getIconUrl().hashCode());
        result = prime * result + ((getAddDate() == null) ? 0 : getAddDate().hashCode());
        result = prime * result + ((getDisplaySequence() == null) ? 0 : getDisplaySequence().hashCode());
        result = prime * result + ((getIsRelease() == null) ? 0 : getIsRelease().hashCode());
        return result;
    }
}