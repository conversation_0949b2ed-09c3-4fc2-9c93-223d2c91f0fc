package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.shop.domain.FreightAreaDetail;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.FreightAreaDetailMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class FreightAreaDetailRepository extends ServiceImpl<FreightAreaDetailMapper, FreightAreaDetail> {
    public FreightAreaDetail selectById(Long id) {
        return this.getById(id);
    }

    public List<FreightAreaDetail> getByTemplateIdList(List<Long> templateIdList) {
        LambdaQueryWrapper<FreightAreaDetail> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(FreightAreaDetail::getFreightTemplateId, templateIdList);
        return this.list(queryWrapper);
    }

}
