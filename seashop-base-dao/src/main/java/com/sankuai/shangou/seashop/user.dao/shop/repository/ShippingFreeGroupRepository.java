package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShippingFreeGroup;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShippingFreeRegion;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ShippingFreeGroupMapper;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ShippingFreeRegionMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Repository
public class ShippingFreeGroupRepository extends ServiceImpl<ShippingFreeGroupMapper, ShippingFreeGroup> {

    @Resource
    private ShippingFreeRegionMapper shippingFreeRegionMapper;
    /**
     * 根据模板ID获取包邮组
     * <AUTHOR>
     * @param templateIdList 模板ID
     */
    public List<ShippingFreeGroup> getByTemplateIdList(List<Long> templateIdList) {
        LambdaQueryWrapper<ShippingFreeGroup> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ShippingFreeGroup::getTemplateId, templateIdList);
        return baseMapper.selectList(queryWrapper);
    }

    public ShippingFreeGroup selectById(Long id){
        return this.getById(id);
    }

    public int getRegionReqListSize(Long groupId) {
        LambdaQueryWrapper<ShippingFreeRegion> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShippingFreeRegion::getGroupId, groupId);
        return Math.toIntExact(shippingFreeRegionMapper.selectCount(queryWrapper));
    }
}
