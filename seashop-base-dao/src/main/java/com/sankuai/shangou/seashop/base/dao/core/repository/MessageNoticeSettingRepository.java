package com.sankuai.shangou.seashop.base.dao.core.repository;


import com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSetting;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSettingExample;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseMessageNoticeSettingMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
public class MessageNoticeSettingRepository {
    @Resource
    private BaseMessageNoticeSettingMapper baseMessageNoticeSettingMapper;

    public Boolean setSetting(BaseMessageNoticeSetting setting) {
        baseMessageNoticeSettingMapper.updateByPrimaryKeySelective(setting);
        return true;
    }

    public Boolean addSetting(BaseMessageNoticeSetting setting) {
        baseMessageNoticeSettingMapper.insert(setting);
        return true;
    }
    public List<BaseMessageNoticeSetting> getSettings() {
        BaseMessageNoticeSettingExample example = new BaseMessageNoticeSettingExample();
        List<BaseMessageNoticeSetting> result = baseMessageNoticeSettingMapper.selectByExample(example);
        return result;
    }

    public BaseMessageNoticeSetting getSettings(int type) {
        BaseMessageNoticeSetting result = baseMessageNoticeSettingMapper.selectByPrimaryKey(type);
        return result;
    }
}
