package com.sankuai.shangou.seashop.base.dao.core.domain;

public class BaseMessageNoticeSetting {
    private Integer messageType;

    private String messageTypeName;

    private Boolean emaillNotice;

    private Boolean smsNotice;

    private Boolean wxNotice;


    public Integer getTemplateId() {
        return templateId;
    }

    public void setTemplateId(Integer templateId) {
        this.templateId = templateId;
    }

    private Integer templateId;
    public Integer getMessageType() {
        return messageType;
    }

    public void setMessageType(Integer messageType) {
        this.messageType = messageType;
    }

    public String getMessageTypeName() {
        return messageTypeName;
    }

    public void setMessageTypeName(String messageTypeName) {
        this.messageTypeName = messageTypeName == null ? null : messageTypeName.trim();
    }

    public Boolean getEmaillNotice() {
        return emaillNotice;
    }

    public void setEmaillNotice(Boolean emaillNotice) {
        this.emaillNotice = emaillNotice;
    }

    public Boolean getSmsNotice() {
        return smsNotice;
    }

    public void setSmsNotice(Boolean smsNotice) {
        this.smsNotice = smsNotice;
    }

    public Boolean getWxNotice() {
        return wxNotice;
    }

    public void setWxNotice(Boolean wxNotice) {
        this.wxNotice = wxNotice;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BaseMessageNoticeSetting other = (BaseMessageNoticeSetting) that;
        return (this.getMessageType() == null ? other.getMessageType() == null : this.getMessageType().equals(other.getMessageType()))
            && (this.getMessageTypeName() == null ? other.getMessageTypeName() == null : this.getMessageTypeName().equals(other.getMessageTypeName()))
            && (this.getEmaillNotice() == null ? other.getEmaillNotice() == null : this.getEmaillNotice().equals(other.getEmaillNotice()))
            && (this.getSmsNotice() == null ? other.getSmsNotice() == null : this.getSmsNotice().equals(other.getSmsNotice()))
            && (this.getWxNotice() == null ? other.getWxNotice() == null : this.getWxNotice().equals(other.getWxNotice()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getMessageType() == null) ? 0 : getMessageType().hashCode());
        result = prime * result + ((getMessageTypeName() == null) ? 0 : getMessageTypeName().hashCode());
        result = prime * result + ((getEmaillNotice() == null) ? 0 : getEmaillNotice().hashCode());
        result = prime * result + ((getSmsNotice() == null) ? 0 : getSmsNotice().hashCode());
        result = prime * result + ((getWxNotice() == null) ? 0 : getWxNotice().hashCode());
        return result;
    }
}