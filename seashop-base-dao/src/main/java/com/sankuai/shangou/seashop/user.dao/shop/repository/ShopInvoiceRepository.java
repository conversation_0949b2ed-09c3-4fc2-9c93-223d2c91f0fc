package com.sankuai.shangou.seashop.user.dao.shop.repository;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopInvoiceConfig;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopInvoiceConfigMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/29 9:07
 */
@Repository
public class ShopInvoiceRepository {

    @Resource
    private ShopInvoiceConfigMapper shopInvoiceConfigMapper;

    public ShopInvoiceConfig selectById(Long id) {
        return shopInvoiceConfigMapper.selectById(id);
    }

    public int save(ShopInvoiceConfig shopInvoiceConfig){
       LambdaQueryWrapper<ShopInvoiceConfig> queryWrapper = new LambdaQueryWrapper<>();
       queryWrapper.eq(ShopInvoiceConfig::getShopId, shopInvoiceConfig.getShopId());
        ShopInvoiceConfig selectOne = shopInvoiceConfigMapper.selectOne(queryWrapper);
        if(ObjectUtil.isNotNull(selectOne)){
            shopInvoiceConfig.setId(selectOne.getId());
            return shopInvoiceConfigMapper.updateById(shopInvoiceConfig);
        }
        shopInvoiceConfig.setCreateTime(new Date());
        return shopInvoiceConfigMapper.insert(shopInvoiceConfig);
    }

    public ShopInvoiceConfig selectOne(ShopInvoiceConfig shopInvoiceConfig){
        QueryWrapper<ShopInvoiceConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopInvoiceConfig::getShopId, shopInvoiceConfig.getShopId());
        return shopInvoiceConfigMapper.selectOne(queryWrapper);
    }

    public List<ShopInvoiceConfig> getByShopIdList(List<Long> shopIdList) {
        QueryWrapper<ShopInvoiceConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(ShopInvoiceConfig::getShopId, shopIdList);
        return shopInvoiceConfigMapper.selectList(queryWrapper);
    }
}
