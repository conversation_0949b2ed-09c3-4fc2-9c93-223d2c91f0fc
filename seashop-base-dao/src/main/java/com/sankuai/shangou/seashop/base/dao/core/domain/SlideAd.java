package com.sankuai.shangou.seashop.base.dao.core.domain;

import com.baomidou.mybatisplus.annotation.*;

import java.util.Date;
import java.io.Serializable;

/**
 * <p>
 * 滚动广告表
 * </p>
 *
 * <AUTHOR> @since 2023-12-11
 */
@TableName("base_slide_ad")
public class SlideAd implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺ID，0：平台轮播图
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 图片保存URL
     */
    @TableField("image_url")
    private String imageUrl;

    /**
     * 图片跳转URL
     */
    @TableField("url")
    private String url;

    /**
     * 排序
     */
    @TableField("display_sequence")
    private Long displaySequence;

    /**
     * 类型
     */
    @TableField("type_id")
    private Integer typeId;

    @TableField("description")
    private String description;

    /**
     * 是否删除
     */
    @TableField("del_flag")
    private Boolean delFlag;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",exist = false)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(value = "update_time",exist = false)
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public Long getDisplaySequence() {
        return displaySequence;
    }

    public void setDisplaySequence(Long displaySequence) {
        this.displaySequence = displaySequence;
    }

    public Integer getTypeId() {
        return typeId;
    }

    public void setTypeId(Integer typeId) {
        this.typeId = typeId;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(Boolean delFlag) {
        this.delFlag = delFlag;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "SlideAd{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", imageUrl=" + imageUrl +
        ", url=" + url +
        ", displaySequence=" + displaySequence +
        ", typeId=" + typeId +
        ", description=" + description +
        ", delFlag=" + delFlag +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
