package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseOperationLog;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseOperationLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class BaseOperationLogRepository {
    @Resource
    private BaseOperationLogMapper baseOperationLogMapper;

    public long insert(BaseOperationLog operationLog){
        baseOperationLogMapper.insert(operationLog);
        return operationLog.getId();
    }
}
