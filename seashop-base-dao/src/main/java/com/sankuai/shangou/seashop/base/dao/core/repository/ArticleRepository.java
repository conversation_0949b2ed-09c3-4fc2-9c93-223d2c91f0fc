package com.sankuai.shangou.seashop.base.dao.core.repository;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticle;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleExample;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleWithBLOBs;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseArticleMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class ArticleRepository {

    @Resource
    private BaseArticleMapper articleMapper;

    public Long create(BaseArticleWithBLOBs article) {
        articleMapper.insert(article);
        return article.getId();
    }

    public Boolean update(BaseArticleWithBLOBs article) {
        articleMapper.updateByPrimaryKeySelective(article);
        return true;
    }

    public Boolean deletes(List<Long> ids) {
        BaseArticleExample example = new BaseArticleExample();
        BaseArticleExample.Criteria criteria = example.createCriteria();
        criteria.andIdIn(ids);
        articleMapper.deleteByExample(example);
        return true;
    }

    public List<BaseArticle> query(BaseArticleExample example) {
        List<BaseArticle> list = articleMapper.selectByExample(example);
        return list;
    }


    public Long getCountByCateId(List<Long> cateIds) {
        BaseArticleExample example = new BaseArticleExample();
        BaseArticleExample.Criteria criteria = example.createCriteria();
        criteria.andCategoryIdIn(cateIds);
        long count = articleMapper.countByExample(example);
        return count;
    }

    public BaseArticleWithBLOBs selectById(Long id) {
        BaseArticleWithBLOBs result = articleMapper.selectByPrimaryKey(id);
        return result;
    }
}
