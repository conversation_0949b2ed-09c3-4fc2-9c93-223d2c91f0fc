package com.sankuai.shangou.seashop.user.dao.account.repository;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.domain.MemberExt;
import com.sankuai.shangou.seashop.user.dao.account.mapper.MemberMapper;
import com.sankuai.shangou.seashop.user.dao.account.mapper.ext.MemberExtMapper;
import com.sankuai.shangou.seashop.user.dao.account.model.QueryMemberListModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.elasticsearch.monitor.os.OsStats;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @description: 商家仓库类
 * @author: LXH
 **/
@Repository
@Slf4j
public class MemberRepository extends ServiceImpl<MemberMapper, Member> {
    @Resource
    private MemberExtMapper memberExtMapper;

    public List<MemberExt> queryMemberPage(QueryMemberListModel queryMemberListDto) {
        return memberExtMapper.queryMember(queryMemberListDto);
    }

    public Member queryMember(Member member) {
        QueryWrapper<Member> memberQueryWrapper = new QueryWrapper<Member>();
        if (member.getId() != null) {
            memberQueryWrapper.lambda().eq(Member::getId, member.getId());
        }
        if (member.getUserName() != null) {
            memberQueryWrapper.lambda().eq(Member::getUserName, member.getUserName());
        }

        memberQueryWrapper.lambda().eq(Member::getWhetherLogOut, false);
        return baseMapper.selectOne(memberQueryWrapper);
    }

    public Member queryMemberByEpAccountId(Member member) {
        QueryWrapper<Member> memberQueryWrapper = new QueryWrapper<Member>();
        memberQueryWrapper.lambda()
                .eq(Member::getWhetherLogOut, false);
        return baseMapper.selectOne(memberQueryWrapper);
    }

    public Member queryMemberByUserName(String userName) {
        QueryWrapper<Member> memberQueryWrapper = new QueryWrapper<Member>();
        memberQueryWrapper.lambda()
                .eq(Member::getUserName, userName)
                .eq(Member::getWhetherLogOut, false);
        return baseMapper.selectOne(memberQueryWrapper);
    }

    public void editMember(Member member) {
        baseMapper.updateById(member);
    }

    public void batchDelete(List<Long> longList) {
        baseMapper.deleteBatchIds(longList);
    }

    public Member getById(Long userId) {
        return baseMapper.selectById(userId);
    }

    public Member getByUserName(String userName) {
        QueryWrapper<Member> memberQueryWrapper = new QueryWrapper<Member>();
        memberQueryWrapper.lambda()
                .eq(Member::getUserName, userName)
                .eq(Member::getWhetherLogOut, false);
        return baseMapper.selectOne(memberQueryWrapper);
    }

    public Member getByPhone(String phone) {
        QueryWrapper<Member> memberQueryWrapper = new QueryWrapper<Member>();
        memberQueryWrapper.lambda()
                .eq(Member::getCellPhone, phone)
                .eq(Member::getWhetherLogOut, false);
        return baseMapper.selectOne(memberQueryWrapper);
    }

    public List<Member> getByIds(List<Long> ids) {
        QueryWrapper<Member> memberQueryWrapper = new QueryWrapper<Member>();
        memberQueryWrapper.lambda()
                .in(Member::getId, ids)
                .eq(Member::getWhetherLogOut, false);
        return baseMapper.selectList(memberQueryWrapper);
    }

    public List<Member> getMemberList(QueryMemberListModel queryMemberListDto) {
        QueryWrapper<Member> memberQueryWrapper = new QueryWrapper<Member>();
        if (CollectionUtils.isNotEmpty(queryMemberListDto.getUserNames())) {
            memberQueryWrapper.lambda().in(Member::getUserName, queryMemberListDto.getUserNames());
        }
        if (CollectionUtils.isNotEmpty(queryMemberListDto.getMemberIds())) {
            memberQueryWrapper.lambda().in(Member::getId, queryMemberListDto.getMemberIds());
        }
        memberQueryWrapper.lambda().eq(Member::getWhetherLogOut, false);
        return baseMapper.selectList(memberQueryWrapper);
    }

    public int insert(Member member) {
        return baseMapper.insert(member);
    }

    public Member selectById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 根据手机号模糊查询用户id
     * <p>因为用户表手机号码是加密的，所以是关联联系人表查询的</p>
     * <p>如果手机号码为空，则返回null</p>
     *
     * @param mobile 手机号码
     * @return 用户ID列表
     */
    public List<Long> queryUserIdByMobile(String mobile) {
        if (StrUtil.isBlank(mobile)) {
            return null;
        }
        return memberExtMapper.queryUserIdByMobile(mobile);
    }

    /**
     * 根据微信openId跟unionId获取会员信息
     *
     * @param openId
     * @param unionId
     * @return
     */
    public Member getByWxId(String openId, String unionId) {
        QueryWrapper<Member> memberQueryWrapper = new QueryWrapper<Member>();
        memberQueryWrapper.lambda()
                .eq(Member::getOpenId, openId)
                .eq(Member::getUnionId, unionId);
        return baseMapper.selectOne(memberQueryWrapper);
    }

    public Long countByUserName(String userName) {
        return baseMapper.selectCount(new QueryWrapper<Member>().lambda().likeRight(Member::getUserName, userName).eq(Member::getWhetherLogOut, false));
    }

    public void updateMemberLastLoginTime(Long memberId) {
        Member member = new Member();
        member.setId(memberId);
        member.setLastLoginDate(new Date());
        baseMapper.updateById(member);
    }

    public List<Member> getByUpdateTime(Date createTime) {
        LambdaQueryWrapper<Member> queryWrapper = new LambdaQueryWrapper<>();
        if (createTime != null) {
            queryWrapper.ge(Member::getUpdateTime, createTime);
        }
        List<Member> members = baseMapper.selectList(queryWrapper);
        return members;
    }
}
