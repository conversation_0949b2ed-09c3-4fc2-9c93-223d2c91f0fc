package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.dao.core.domain.JobLogInfo;
import com.sankuai.shangou.seashop.base.dao.core.mapper.JobLogInfoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

@Repository
@Slf4j
public class JobLogInfoRepository extends ServiceImpl<JobLogInfoMapper, JobLogInfo> {

    public JobLogInfo getLastSuccessInfoByJobId(Long jobId) {
        LambdaQueryWrapper<JobLogInfo> jobLogInfoLambdaQueryWrapper = new LambdaQueryWrapper<JobLogInfo>();
        jobLogInfoLambdaQueryWrapper
                .eq(JobLogInfo::getJobId, jobId)
                .and(t -> t.eq(JobLogInfo::getTriggerCode, 200).or().eq(JobLogInfo::getTriggerCode, 0))
                .eq(JobLogInfo::getHandleCode,200)
                .orderByDesc(JobLogInfo::getTriggerTime).last("limit 1");

        return baseMapper.selectOne(jobLogInfoLambdaQueryWrapper);

    }
}
