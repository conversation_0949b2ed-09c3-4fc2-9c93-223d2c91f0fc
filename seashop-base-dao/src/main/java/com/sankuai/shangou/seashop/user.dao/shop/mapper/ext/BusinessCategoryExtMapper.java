package com.sankuai.shangou.seashop.user.dao.shop.mapper.ext;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryForm;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 经营分类自定义表单 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
public interface BusinessCategoryExtMapper extends EnhancedMapper<BusinessCategoryForm> {
    List<Long> listByCategory(@Param("categoryIds") List<Long> categoryId);

    List<Long> listByShopAndCategory(@Param("shopId") Long shopId, @Param("categoryIds") List<Long> categoryId);
}
