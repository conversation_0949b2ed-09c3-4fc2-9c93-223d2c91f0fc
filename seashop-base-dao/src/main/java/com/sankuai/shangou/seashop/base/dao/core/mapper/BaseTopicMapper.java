package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseTopicMapper  {
    long countByExample(BaseTopicExample example);

    int deleteByExample(BaseTopicExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseTopic record);

    int insertSelective(BaseTopic record);

    List<BaseTopic> selectByExampleWithBLOBs(BaseTopicExample example);

    List<BaseTopic> selectByExample(BaseTopicExample example);

    BaseTopic selectByPrimaryKey(Long id);

    BaseTopic selectByShopId(@Param("id")  Long id,@Param("shopId") Long shopId);
    int updateByExampleSelective(@Param("record") BaseTopic record, @Param("example") BaseTopicExample example);

    int updateByExampleWithBLOBs(@Param("record") BaseTopic record, @Param("example") BaseTopicExample example);

    int updateByExample(@Param("record") BaseTopic record, @Param("example") BaseTopicExample example);

    int updateByPrimaryKeySelective(BaseTopic record);

    int updateByPrimaryKeyWithBLOBs(BaseTopic record);

    int updateByPrimaryKey(BaseTopic record);
}