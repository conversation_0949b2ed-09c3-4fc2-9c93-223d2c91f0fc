package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 供应商发/退货地址表
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_shop_shipper")
public class ShopShipper implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商家编号
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 是否为默认发货地址
     */
    @TableField("default_send_goods_flag")
    private Boolean defaultSendGoodsFlag;

    /**
     * 是否默认收货地址
     */
    @TableField("default_get_goods_flag")
    private Boolean defaultGetGoodsFlag;

    /**
     * 发货点名称
     */
    @TableField("shipper_tag")
    private String shipperTag;

    /**
     * 发货人
     */
    @TableField("shipper_name")
    private String shipperName;

    /**
     * 区域ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 具体街道信息
     */
    @TableField("address")
    private String address;

    /**
     * 手机号码
     */
    @TableField("tel_phone")
    private String telPhone;

    /**
     * 微信OpenID用于发信息到微信给发货人
     */
    @TableField("wx_open_id")
    private String wxOpenId;

    /**
     * 经度
     */
    @TableField("longitude")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private BigDecimal latitude;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Boolean getDefaultSendGoodsFlag() {
        return defaultSendGoodsFlag;
    }

    public void setDefaultSendGoodsFlag(Boolean defaultSendGoodsFlag) {
        this.defaultSendGoodsFlag = defaultSendGoodsFlag;
    }

    public Boolean getDefaultGetGoodsFlag() {
        return defaultGetGoodsFlag;
    }

    public void setDefaultGetGoodsFlag(Boolean defaultGetGoodsFlag) {
        this.defaultGetGoodsFlag = defaultGetGoodsFlag;
    }

    public String getShipperTag() {
        return shipperTag;
    }

    public void setShipperTag(String shipperTag) {
        this.shipperTag = shipperTag;
    }

    public String getShipperName() {
        return shipperName;
    }

    public void setShipperName(String shipperName) {
        this.shipperName = shipperName;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getTelPhone() {
        return telPhone;
    }

    public void setTelPhone(String telPhone) {
        this.telPhone = telPhone;
    }


    public String getWxOpenId() {
        return wxOpenId;
    }

    public void setWxOpenId(String wxOpenId) {
        this.wxOpenId = wxOpenId;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ShopShipper{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", defaultSendGoodsFlag=" + defaultSendGoodsFlag +
        ", defaultGetGoodsFlag=" + defaultGetGoodsFlag +
        ", shipperTag=" + shipperTag +
        ", shipperName=" + shipperName +
        ", regionId=" + regionId +
        ", address=" + address +
        ", telPhone=" + telPhone +
        ", wxOpenId=" + wxOpenId +
        ", longitude=" + longitude +
        ", latitude=" + latitude +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
