package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopErp;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopErpMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @author： liweisong
 * @create： 2023/11/28 15:13
 */
@Repository
public class ShopErpRepository {

    @Resource
    private ShopErpMapper shopErpMapper;

    public ShopErp selectById(Long id) {
        return shopErpMapper.selectById(id);
    }

    public int insert(ShopErp shopErp) {
        return shopErpMapper.insert(shopErp);
    }

    public int save(ShopErp shopErp) {
        LambdaUpdateWrapper<ShopErp> lambdaUpdateWrapper = new LambdaUpdateWrapper();
        lambdaUpdateWrapper.eq(ShopErp::getShopId, shopErp.getShopId());
        return shopErpMapper.update(shopErp, lambdaUpdateWrapper);
    }

    public ShopErp selectOne(ShopErp shopErp) {
        if (Objects.isNull(shopErp)) {
            return null;
        }
        if (shopErp.getShopId() == null && StringUtils.isEmpty(shopErp.getJstShopId())) {
            return null;
        }
        QueryWrapper<ShopErp> queryWrapper = new QueryWrapper<>();
        if (shopErp.getShopId() != null) {
            queryWrapper.lambda().eq(ShopErp::getShopId, shopErp.getShopId());
        }
        if (!StringUtils.isEmpty(shopErp.getJstShopId())) {
            queryWrapper.lambda().eq(ShopErp::getJstShopId, shopErp.getJstShopId());
        }
        return shopErpMapper.selectOne(queryWrapper);
    }

    public List<ShopErp> selectListByToken(ShopErp shopErp) {
        QueryWrapper<ShopErp> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(shopErp.getErpType() != null, ShopErp::getErpType, shopErp.getErpType())
                .in(CollectionUtils.isNotEmpty(shopErp.getErpTypeList()), ShopErp::getErpType, shopErp.getErpTypeList())
                .eq(shopErp.getBlpToken() != null, ShopErp::getBlpToken, shopErp.getBlpToken())
                .eq(shopErp.getWdtToken() != null, ShopErp::getWdtToken, shopErp.getWdtToken());
        return shopErpMapper.selectList(queryWrapper);
    }

    public List<ShopErp> selectByJstShopIds(Integer erpType, List<String> jstShopIds) {
        Wrapper<ShopErp> queryWrapper = Wrappers.<ShopErp>lambdaQuery()
                .eq(erpType != null, ShopErp::getErpType, erpType)
                .in(CollectionUtils.isNotEmpty(jstShopIds), ShopErp::getJstShopId, jstShopIds);
        return shopErpMapper.selectList(queryWrapper);
    }

    public Boolean flagAlreadyExistToken(ShopErp shopErp) {
        LambdaQueryWrapper<ShopErp> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopErp::getBlpToken, shopErp.getBlpToken());
        queryWrapper.ne(ShopErp::getShopId, shopErp.getShopId());
        return shopErpMapper.selectCount(queryWrapper) > 0;
    }

    public List<ShopErp> selectJstToExpire(Page pageParam, Integer erpType, Date startDate,
                                           Date endDate) {
        QueryWrapper<ShopErp> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(erpType != null, "erp_type", erpType)
                .between("DATE_ADD(jst_token_get_time,INTERVAL jst_access_token_expires SECOND)",
                        startDate, endDate);
        return PageHelper.startPage(pageParam.getPageNum(), pageParam.getPageSize(), "id desc")
                .count(false)
                .doSelectPage(() -> shopErpMapper.selectList(queryWrapper));
    }
}
