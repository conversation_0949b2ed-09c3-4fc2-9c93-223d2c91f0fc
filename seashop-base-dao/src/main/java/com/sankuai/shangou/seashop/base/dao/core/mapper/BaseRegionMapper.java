package com.sankuai.shangou.seashop.base.dao.core.mapper;

import java.io.Serializable;
import java.io.Serializable;
import java.util.List;
import java.util.Map;
import java.util.Set;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Param;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionProvinceCity;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionExample;

public interface BaseRegionMapper extends BaseMapper<BaseRegion> {
    long countByExample(BaseRegionExample example);

    int deleteByExample(BaseRegionExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseRegion record);

    int insertSelective(BaseRegion record);

    List<BaseRegion> selectByExample(BaseRegionExample example);

    BaseRegion selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BaseRegion record, @Param("example") BaseRegionExample example);

    int updateByExample(@Param("record") BaseRegion record, @Param("example") BaseRegionExample example);

    int updateLeftAndRightByInsert(@Param("example") BaseRegionExample example);

    int updateParentLeftAndRightByInsert(@Param("example") BaseRegionExample example);

    int updateByPrimaryKeySelective(BaseRegion record);

    int updateByPrimaryKey(BaseRegion record);

    List<BaseRegion> getAllPathRegions(@Param("list") List<Integer> list);

    List<String> getTownsNameByIds(@Param("list") List<String> list);

    long getMaxRight();

    long getMaxId();

    @MapKey("parent_id")
    Map<Long, Map<String, Object>> selectCountSubRegionAsMap(@Param("subIds") List<Long> subIds);

    BaseRegion selectById(Serializable id);

    BaseRegionProvinceCity queryProvinceCity(Long id);

    void updateRegion(BaseRegion region);
}