package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 店铺补充信息表
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_shop_ext")
public class ShopExt implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 公司省市区
     */
    @TableField("company_region_id")
    private Integer companyRegionId;

    /**
     * 公司地址
     */
    @TableField("company_address")
    private String companyAddress;

    /**
     * 公司电话
     */
    @TableField("company_phone")
    private String companyPhone;

    /**
     * 公司员工数量
     */
    @TableField("company_employee_count")
    private Integer companyEmployeeCount;

    /**
     * 公司注册资金
     */
    @TableField("company_registered_capital")
    private BigDecimal companyRegisteredCapital;

    /**
     * 营业执照号
     */
    @TableField("business_license_number")
    private String businessLicenseNumber;

    /**
     * 营业执照
     */
    @TableField("business_license_number_photo")
    private String businessLicenseNumberPhoto;

    /**
     * 营业执照所在地
     */
    @TableField("business_license_region_id")
    private Integer businessLicenseRegionId;

    /**
     * 营业执照有效期开始
     */
    @TableField("business_license_start")
    private Date businessLicenseStart;

    /**
     * 营业执照有效期
     */
    @TableField("business_license_end")
    private Date businessLicenseEnd;

    /**
     * 法定经营范围
     */
    @TableField("business_sphere")
    private String businessSphere;

    /**
     * 组织机构代码
     */
    @TableField("organization_code")
    private String organizationCode;

    /**
     * 组织机构执照
     */
    @TableField("organization_code_photo")
    private String organizationCodePhoto;

    /**
     * 税务登记证
     */
    @TableField("tax_registration_certificate")
    private String taxRegistrationCertificate;

    /**
     * 税务登记证号
     */
    @TableField("taxpayer_id")
    private String taxpayerId;

    /**
     * 纳税人识别号
     */
    @TableField("tax_registration_certificate_photo")
    private String taxRegistrationCertificatePhoto;

    /**
     * 法人代表
     */
    @TableField("legal_person")
    private String legalPerson;

    /**
     * 公司成立日期
     */
    @TableField("company_founding_date")
    private Date companyFoundingDate;

    /**
     * 公司类型
     */
    @TableField("company_type")
    private String companyType;

    /**
     * 执照地址
     */
    @TableField("licence_cert_addr")
    private String licenceCertAddr;

    /**
     * 财务负责人
     */
    @TableField("finance_chief")
    private String financeChief;

    /**
     * 财务负责人联系方式
     */
    @TableField("finance_chief_phone")
    private String financeChiefPhone;

    /**
     * 服务商简介
     */
    @TableField("introduct")
    private String introduct;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public Integer getCompanyRegionId() {
        return companyRegionId;
    }

    public void setCompanyRegionId(Integer companyRegionId) {
        this.companyRegionId = companyRegionId;
    }

    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    public String getCompanyPhone() {
        return companyPhone;
    }

    public void setCompanyPhone(String companyPhone) {
        this.companyPhone = companyPhone;
    }

    public Integer getCompanyEmployeeCount() {
        return companyEmployeeCount;
    }

    public void setCompanyEmployeeCount(Integer companyEmployeeCount) {
        this.companyEmployeeCount = companyEmployeeCount;
    }

    public BigDecimal getCompanyRegisteredCapital() {
        return companyRegisteredCapital;
    }

    public void setCompanyRegisteredCapital(BigDecimal companyRegisteredCapital) {
        this.companyRegisteredCapital = companyRegisteredCapital;
    }

    public String getBusinessLicenseNumber() {
        return businessLicenseNumber;
    }

    public void setBusinessLicenseNumber(String businessLicenseNumber) {
        this.businessLicenseNumber = businessLicenseNumber;
    }

    public String getBusinessLicenseNumberPhoto() {
        return businessLicenseNumberPhoto;
    }

    public void setBusinessLicenseNumberPhoto(String businessLicenseNumberPhoto) {
        this.businessLicenseNumberPhoto = businessLicenseNumberPhoto;
    }

    public Integer getBusinessLicenseRegionId() {
        return businessLicenseRegionId;
    }

    public void setBusinessLicenseRegionId(Integer businessLicenseRegionId) {
        this.businessLicenseRegionId = businessLicenseRegionId;
    }

    public Date getBusinessLicenseStart() {
        return businessLicenseStart;
    }

    public void setBusinessLicenseStart(Date businessLicenseStart) {
        this.businessLicenseStart = businessLicenseStart;
    }

    public Date getBusinessLicenseEnd() {
        return businessLicenseEnd;
    }

    public void setBusinessLicenseEnd(Date businessLicenseEnd) {
        this.businessLicenseEnd = businessLicenseEnd;
    }

    public String getBusinessSphere() {
        return businessSphere;
    }

    public void setBusinessSphere(String businessSphere) {
        this.businessSphere = businessSphere;
    }

    public String getOrganizationCode() {
        return organizationCode;
    }

    public void setOrganizationCode(String organizationCode) {
        this.organizationCode = organizationCode;
    }

    public String getOrganizationCodePhoto() {
        return organizationCodePhoto;
    }

    public void setOrganizationCodePhoto(String organizationCodePhoto) {
        this.organizationCodePhoto = organizationCodePhoto;
    }

    public String getTaxRegistrationCertificate() {
        return taxRegistrationCertificate;
    }

    public void setTaxRegistrationCertificate(String taxRegistrationCertificate) {
        this.taxRegistrationCertificate = taxRegistrationCertificate;
    }

    public String getTaxpayerId() {
        return taxpayerId;
    }

    public void setTaxpayerId(String taxpayerId) {
        this.taxpayerId = taxpayerId;
    }

    public String getTaxRegistrationCertificatePhoto() {
        return taxRegistrationCertificatePhoto;
    }

    public void setTaxRegistrationCertificatePhoto(String taxRegistrationCertificatePhoto) {
        this.taxRegistrationCertificatePhoto = taxRegistrationCertificatePhoto;
    }

    public String getLegalPerson() {
        return legalPerson;
    }

    public void setLegalPerson(String legalPerson) {
        this.legalPerson = legalPerson;
    }

    public Date getCompanyFoundingDate() {
        return companyFoundingDate;
    }

    public void setCompanyFoundingDate(Date companyFoundingDate) {
        this.companyFoundingDate = companyFoundingDate;
    }

    public String getCompanyType() {
        return companyType;
    }

    public void setCompanyType(String companyType) {
        this.companyType = companyType;
    }

    public String getLicenceCertAddr() {
        return licenceCertAddr;
    }

    public void setLicenceCertAddr(String licenceCertAddr) {
        this.licenceCertAddr = licenceCertAddr;
    }

    public String getFinanceChief() {
        return financeChief;
    }

    public void setFinanceChief(String financeChief) {
        this.financeChief = financeChief;
    }

    public String getFinanceChiefPhone() {
        return financeChiefPhone;
    }

    public void setFinanceChiefPhone(String financeChiefPhone) {
        this.financeChiefPhone = financeChiefPhone;
    }

    public String getIntroduct() {
        return introduct;
    }

    public void setIntroduct(String introduct) {
        this.introduct = introduct;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ShopExt{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", companyName=" + companyName +
        ", companyRegionId=" + companyRegionId +
        ", companyAddress=" + companyAddress +
        ", companyPhone=" + companyPhone +
        ", companyEmployeeCount=" + companyEmployeeCount +
        ", companyRegisteredCapital=" + companyRegisteredCapital +
        ", businessLicenseNumber=" + businessLicenseNumber +
        ", businessLicenseNumberPhoto=" + businessLicenseNumberPhoto +
        ", businessLicenseRegionId=" + businessLicenseRegionId +
        ", businessLicenseStart=" + businessLicenseStart +
        ", businessLicenseEnd=" + businessLicenseEnd +
        ", businessSphere=" + businessSphere +
        ", organizationCode=" + organizationCode +
        ", organizationCodePhoto=" + organizationCodePhoto +
        ", taxRegistrationCertificate=" + taxRegistrationCertificate +
        ", taxpayerId=" + taxpayerId +
        ", taxRegistrationCertificatePhoto=" + taxRegistrationCertificatePhoto +
        ", legalPerson=" + legalPerson +
        ", companyFoundingDate=" + companyFoundingDate +
        ", companyType=" + companyType +
        ", licenceCertAddr=" + licenceCertAddr +
        ", financeChief=" + financeChief +
        ", financeChiefPhone=" + financeChiefPhone +
        ", introduct=" + introduct +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
