package com.sankuai.shangou.seashop.user.dao.account.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 收货地址表
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_shipping_address")
public class ShippingAddress implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 区域ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 收货人
     */
    @TableField("ship_to")
    private String shipTo;

    /**
     * 收货地址
     */
    @TableField("address")
    private String address;

    /**
     * 详细地址
     */
    @TableField("address_detail")
    private String addressDetail;

    /**
     * 收货人电话
     */
    @TableField("phone")
    private String phone;

    /**
     * 是否为默认
     */
    @TableField("whether_default")
    private Boolean whetherDefault;

    /**
     * 是否为轻松购地址
     */
    @TableField("whether_quick")
    private Boolean whetherQuick;

    /**
     * 经度
     */
    @TableField("longitude")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @TableField("latitude")
    private BigDecimal latitude;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public String getShipTo() {
        return shipTo;
    }

    public void setShipTo(String shipTo) {
        this.shipTo = shipTo;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getAddressDetail() {
        return addressDetail;
    }

    public void setAddressDetail(String addressDetail) {
        this.addressDetail = addressDetail;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Boolean getWhetherDefault() {
        return whetherDefault;
    }

    public void setWhetherDefault(Boolean whetherDefault) {
        this.whetherDefault = whetherDefault;
    }

    public Boolean getWhetherQuick() {
        return whetherQuick;
    }

    public void setWhetherQuick(Boolean whetherQuick) {
        this.whetherQuick = whetherQuick;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "ShippingAddress{" +
        "id=" + id +
        ", userId=" + userId +
        ", regionId=" + regionId +
        ", shipTo=" + shipTo +
        ", address=" + address +
        ", addressDetail=" + addressDetail +
        ", phone=" + phone +
        ", whetherDefault=" + whetherDefault +
        ", whetherQuick=" + whetherQuick +
        ", longitude=" + longitude +
        ", latitude=" + latitude +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
