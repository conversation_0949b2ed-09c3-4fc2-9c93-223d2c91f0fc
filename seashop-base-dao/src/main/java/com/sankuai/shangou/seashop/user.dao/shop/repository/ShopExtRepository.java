package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopExt;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopExtMapper;
import org.springframework.stereotype.Repository;

/**
 * @description: 店铺仓库类
 * @author: LXH
 **/
@Repository
public class ShopExtRepository extends ServiceImpl<ShopExtMapper, ShopExt> {

    public ShopExt selectById(Long shopId) {
        return getByShopId(shopId);
    }
    public ShopExt getByShopId(Long shopId) {
        QueryWrapper<ShopExt> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ShopExt::getShopId, shopId);
        return baseMapper.selectOne(queryWrapper);
    }
}
