package com.sankuai.shangou.seashop.user.dao.account.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.common.constant.ManagerConstant;
import com.sankuai.shangou.seashop.user.dao.account.domain.Manager;
import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.account.mapper.ManagerMapper;
import com.sankuai.shangou.seashop.user.dao.account.mapper.ext.ManagerExtMapper;
import com.sankuai.shangou.seashop.user.dao.account.model.ManagerExtModel;
import com.sankuai.shangou.seashop.user.dao.account.model.ManagerQueryModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 管理员仓库类
 * @author: LXH
 **/
@Repository
@Slf4j
public class ManagerRepository extends ServiceImpl<ManagerMapper, Manager> {

    @Resource
    private ManagerExtMapper managerExtMapper;

    public List<Manager> selectList(Manager manager){
        QueryWrapper<Manager> managerQueryWrapper = new QueryWrapper<Manager>();
        if (manager.getShopId() != null){
            managerQueryWrapper.lambda().eq(Manager::getShopId, manager.getShopId());
        }
        if (manager.getRoleId() != null){
            managerQueryWrapper.lambda().eq(Manager::getRoleId, manager.getRoleId());
        }
        return baseMapper.selectList(managerQueryWrapper);
    }

    public List<Manager> selectBatchShopSuperManager(List<Long> shopIds){
        QueryWrapper<Manager> managerQueryWrapper = new QueryWrapper<Manager>();
        managerQueryWrapper.lambda().in(Manager::getShopId, shopIds);
        managerQueryWrapper.lambda().eq(Manager::getRoleId, ManagerConstant.DEFAULT_ROLE_ID);
        return baseMapper.selectList(managerQueryWrapper);
    }

    public Manager selectById(Long id){
        return baseMapper.selectById(id);
    }

    public int updateByPrimaryKeySelective(Manager manager){
        return baseMapper.updateById(manager);
    }

    public int insert(Manager manager){
        return baseMapper.insert(manager);
    }

    public int updateByExampleSelective(Manager manager){
        return baseMapper.updateById(manager);
    }

    public Integer deleteById(Long id) {
        return baseMapper.deleteById(id);
    }

    public Integer deleteBatchIds(List<Long> ids) {
        return baseMapper.deleteBatchIds(ids);
    }

    public Manager getByUserName(String userName) {
        QueryWrapper<Manager> managerQueryWrapper = new QueryWrapper<Manager>();
        managerQueryWrapper.lambda().eq(Manager::getUserName, userName);
        return baseMapper.selectOne(managerQueryWrapper);
    }

    public Manager getByUserNameOrCellPhone(String userName){
        QueryWrapper<Manager> managerQueryWrapper = new QueryWrapper<>();
        managerQueryWrapper.lambda().eq(Manager::getSubAccount,true);
        managerQueryWrapper.lambda().eq(Manager::getUserName,userName);
        managerQueryWrapper.or();
        managerQueryWrapper.lambda().eq(Manager::getCellphone,userName);
        return baseMapper.selectOne(managerQueryWrapper);
    }

    public Manager getByPhone(String phone) {
        QueryWrapper<Manager> managerQueryWrapper = new QueryWrapper<Manager>();
        managerQueryWrapper.lambda().eq(Manager::getCellphone, phone);
        return baseMapper.selectOne(managerQueryWrapper);
    }

    public Manager getByManager(Manager member) {
        QueryWrapper<Manager> managerQueryWrapper = new QueryWrapper<Manager>();
        if (member.getId() != null){
            managerQueryWrapper.lambda().eq(Manager::getId, member.getId());
        }
        if (StringUtils.isNotEmpty(member.getUserName())){
            managerQueryWrapper.lambda().eq(Manager::getUserName, member.getUserName());
        }
        if (member.getShopId() != null && member.getShopId() > 0){
            managerQueryWrapper.lambda().eq(Manager::getShopId, member.getShopId());
        }
        return baseMapper.selectOne(managerQueryWrapper);
    }

    public Manager getByShopId(Long id) {
        QueryWrapper<Manager> managerQueryWrapper = new QueryWrapper<Manager>();
        managerQueryWrapper.lambda().eq(Manager::getShopId, id)
                .eq(Manager::getRoleId, ManagerConstant.DEFAULT_ROLE_ID);
        return baseMapper.selectOne(managerQueryWrapper);
    }

    public List<ManagerExtModel> selectExtList(ManagerQueryModel manager) {
        // 已经在入参处校验了 managerIds 不会超过200
        return managerExtMapper.selectExtList(manager);
    }

    public List<Member> getManagerIdsByUserIds(List<Long> ids) {
        return managerExtMapper.getManagerIdsByUserIds(ids);
    }

    public Manager getShopByUserName(String userName) {
        QueryWrapper<Manager> managerQueryWrapper = new QueryWrapper<Manager>();
        managerQueryWrapper.lambda().eq(Manager::getUserName, userName)
                .eq(Manager::getRoleId, ManagerConstant.DEFAULT_ROLE_ID)
                .gt(Manager::getShopId, ManagerConstant.DEFAULT_ROLE_ID);
        return baseMapper.selectOne(managerQueryWrapper);
    }

    public Manager getPlatByUserName(String userName) {
        QueryWrapper<Manager> managerQueryWrapper = new QueryWrapper<Manager>();
        managerQueryWrapper.lambda().eq(Manager::getUserName, userName)
                .eq(Manager::getShopId, ManagerConstant.DEFAULT_ROLE_ID);
        return baseMapper.selectOne(managerQueryWrapper);
    }
}
