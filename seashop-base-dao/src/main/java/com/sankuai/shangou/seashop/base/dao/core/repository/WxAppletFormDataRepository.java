package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.shangou.seashop.base.dao.core.domain.WxAppletFormData;
import com.sankuai.shangou.seashop.base.dao.core.mapper.WxAppletFormDataMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 14:24
 */
@Repository
public class WxAppletFormDataRepository {

    @Resource
    private WxAppletFormDataMapper wxAppletFormDataMapper;

    public WxAppletFormData selectById(Long id){
        return wxAppletFormDataMapper.selectById(id);
    }

    public int insert(WxAppletFormData req){
        return wxAppletFormDataMapper.insert(req);
    }

    public List<WxAppletFormData> selectList(WxAppletFormData req){
        QueryWrapper<WxAppletFormData> queryWrapper = new QueryWrapper<>();
        if(req.getId() != null){
            queryWrapper.lambda().eq(WxAppletFormData::getId, req.getId());
        }
        if(req.getEventId() != null){
            queryWrapper.lambda().eq(WxAppletFormData::getEventId, req.getEventId());
        }
        if(StringUtils.isNotBlank(req.getEventValue())){
            queryWrapper.lambda().eq(WxAppletFormData::getEventValue, req.getEventValue());
        }
        return wxAppletFormDataMapper.selectList(queryWrapper);
    }
}
