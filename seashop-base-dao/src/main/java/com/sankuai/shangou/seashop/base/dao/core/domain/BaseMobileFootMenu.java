package com.sankuai.shangou.seashop.base.dao.core.domain;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <p>
 * 底部导航栏
 * </p>
 *
 * <AUTHOR> @since 2023-11-29
 */
@TableName(value = "base_mobile_foot_menu")
public class BaseMobileFootMenu implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 导航名称
     */
    @TableField(value = "name",updateStrategy = FieldStrategy.IGNORED)
    private String name;

    /**
     * 链接地址
     */
    @TableField(value = "url",updateStrategy = FieldStrategy.IGNORED)
    private String url;

    /**
     * 链接名称
     */
    @TableField(value = "url_name",updateStrategy = FieldStrategy.IGNORED)
    private String urlName;

    /**
     * 显示图片
     */
    @TableField(value = "menu_icon",updateStrategy = FieldStrategy.IGNORED)
    private String menuIcon;

    /**
     * 未选中显示图片
     */
    @TableField(value = "menu_icon_sel",updateStrategy = FieldStrategy.IGNORED)
    private String menuIconSel;

    /**
     * 菜单类型（1代表微信、2代表小程序）
     */
    @TableField(value = "type",updateStrategy = FieldStrategy.IGNORED)
    private Integer type;

    /**
     * 店铺Id(0默认是平台)
     */
    @TableField(value = "shop_id",updateStrategy = FieldStrategy.IGNORED)
    private Long shopId;

    @TableField(value = "create_time")
    private Date createTime;

    @TableField(value = "update_time")
    private Date updateTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrlName() {
        return urlName;
    }

    public void setUrlName(String urlName) {
        this.urlName = urlName;
    }

    public String getMenuIcon() {
        return menuIcon;
    }

    public void setMenuIcon(String menuIcon) {
        this.menuIcon = menuIcon;
    }

    public String getMenuIconSel() {
        return menuIconSel;
    }

    public void setMenuIconSel(String menuIconSel) {
        this.menuIconSel = menuIconSel;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "BaseMobileFootMenu{" +
        "id=" + id +
        ", name=" + name +
        ", url=" + url +
        ", menuIcon=" + menuIcon +
        ", menuIconSel=" + menuIconSel +
        ", type=" + type +
        ", shopId=" + shopId +
        "}";
    }
}
