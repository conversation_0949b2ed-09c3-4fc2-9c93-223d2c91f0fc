package com.sankuai.shangou.seashop.user.dao.account.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class QueryMemberListModel {

    private String memberName;

    private Long label;

    private Boolean whetherSeller;

    private Boolean whetherFocusWeiXin;

    private Date registerTimeStart;

    private Date registerTimeEnd;

    private Integer gradeId;

    private String mobile;

    private String weChatNick;

    private Integer status;

    private Integer platform;

    private String sortField;

    private List<String> userNames;

    private List<Long> memberIds;

    private Boolean seller;
    //购买次数区间
    private Integer buyCountStart;
    private Integer buyCountEnd;
    //笔单价区间
    private Double priceStart;
    private Double priceEnd;
    //累计消费金额区间
    private Double totalAmountStart;
    private Double totalAmountEnd;
}