package com.sankuai.shangou.seashop.base.dao.core.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.collect.Maps;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionProvinceCity;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegion;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseRegionExample;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseRegionMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BaseRegionRepository {
    @Resource
    private BaseRegionMapper regionMapper;

    public Long create(BaseRegion region) {
        regionMapper.insert(region);
        return region.getId();
    }

    public BaseRegion getById(Long id) {
        return regionMapper.selectByPrimaryKey(id);
    }

    public List<BaseRegion> getRegionByParentId(long parentId) {
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria criteria = example.createCriteria();
        criteria.andParentIdEqualTo(parentId);
        criteria.andStatusEqualTo(0);
        return regionMapper.selectByExample(example);
    }

    public BaseRegion getRegionByShortNameAndLevel(String shortName, int level) {
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria criteria = example.createCriteria();
        criteria.andShortNameLike(shortName);
        criteria.andRegionLevelEqualTo(level);
        criteria.andStatusEqualTo(0);
        return regionMapper.selectByExample(example).stream().findFirst().get();
    }

    public List<BaseRegion> getAllRegions() {
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(0);
        return regionMapper.selectByExample(example);
    }

    public List<BaseRegion> getRegions(BaseRegionExample example) {
        return regionMapper.selectByExample(example);
    }

    public List<BaseRegion> getAllRegions(int level) {
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria criteria = example.createCriteria();
        criteria.andRegionLevelLessThanOrEqualTo(level);
        criteria.andStatusEqualTo(0);
        return regionMapper.selectByExample(example);
    }

    public void update(BaseRegion dbRegion) {
        regionMapper.updateByPrimaryKeySelective(dbRegion);
    }

    public List<BaseRegion> getAllPathRegions(List<Integer> list) {
        return regionMapper.getAllPathRegions(list);
    }

    public List<String> getTownsNameByIds(List<String> list) {
        return regionMapper.getTownsNameByIds(list);
    }

    /**
     * 腾位
     *
     * @param example
     * @return
     */
    public int updateLeftAndRightByInsert(BaseRegionExample example) {
        return regionMapper.updateLeftAndRightByInsert(example);
    }

    /**
     * 添加新节点时 更新所有父级的右值
     *
     * @param example
     * @return
     */
    public int updateParentLeftAndRightByInsert(BaseRegionExample example) {
        return regionMapper.updateParentLeftAndRightByInsert(example);
    }

    public long getMaxRight() {
        return regionMapper.getMaxRight();
    }

    public long getMaxId() {
        return regionMapper.getMaxId();
    }

    public Boolean update(BaseRegion region, BaseRegionExample example) {
        regionMapper.updateByExampleSelective(region, example);
        return true;
    }

    public Boolean delete(BaseRegionExample example) {
        regionMapper.deleteByExample(example);
        return true;
    }

    public List<BaseRegion> getByUpdateTime(Date createTime) {
        BaseRegionExample example = new BaseRegionExample();
        BaseRegionExample.Criteria criteria = example.createCriteria();
        if (createTime != null) {
            criteria.andUpdateTimeGreaterThanOrEqualTo(createTime);
        }
        List<BaseRegion> regions = regionMapper.selectByExample(example);
        return regions;
    }

    public Map<Long, Integer> selectCountSubRegionAsMap(List<Long> subIds) {
        if (CollectionUtil.isEmpty(subIds)) {
            return Maps.newHashMap();
        }
        return regionMapper.selectCountSubRegionAsMap(subIds).entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, value -> ((Number) value.getValue().get("count")).intValue()));
    }
    
    public BaseRegionProvinceCity getProvinceCity(Long id) {
        return regionMapper.queryProvinceCity(id);
    }
}
