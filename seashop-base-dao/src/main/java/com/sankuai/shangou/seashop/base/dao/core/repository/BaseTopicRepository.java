package com.sankuai.shangou.seashop.base.dao.core.repository;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.springframework.stereotype.Component;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseModuleProduct;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseModuleProductExample;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicExample;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicModule;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicModuleExample;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseModuleProductMapper;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseTopicMapper;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseTopicModuleMapper;

import lombok.extern.slf4j.Slf4j;

@Component
@Slf4j
public class BaseTopicRepository {

    @Resource
    private BaseTopicMapper baseTopicMapper;

    @Resource
    private BaseTopicModuleMapper baseTopicModuleMapper;

    @Resource
    private BaseModuleProductMapper baseModuleProductMapper;


    public Long create(BaseTopic topic) {
        baseTopicMapper.insert(topic);
        Long topicId = topic.getId();
        if (topic.getTopicModules() != null && topic.getTopicModules().size() > 0) {
            for (BaseTopicModule module : topic.getTopicModules()) {
                module.setTopicId(topicId);
                baseTopicModuleMapper.insert(module);
                Long moduleId = module.getId();
                for (BaseModuleProduct moduleProduct : module.getModuleProducts()) {
                    moduleProduct.setModuleId(moduleId);
                    baseModuleProductMapper.insert(moduleProduct);
                }
            }
        }
        return topicId;
    }

    public Boolean update(BaseTopic topic) {
        BaseTopicExample topicExample = new BaseTopicExample();
        BaseTopicExample.Criteria topicExampleCriteria = topicExample.createCriteria();
        topicExampleCriteria.andIdEqualTo(topic.getId());
        topicExampleCriteria.andShopIdEqualTo(topic.getShopId());
        baseTopicMapper.updateByExample(topic, topicExample);
        if (topic.getTopicModules() == null || topic.getTopicModules().size() < 1) {
            return true;
        }

        if (topic.getTopicModules() != null && topic.getTopicModules().size() > 0) {

            BaseTopicModuleExample topicModuleExample = new BaseTopicModuleExample();
            BaseTopicModuleExample.Criteria moduleExample = topicModuleExample.createCriteria();
            moduleExample.andTopicIdEqualTo(topic.getId());
            List<BaseTopicModule> topicModule = baseTopicModuleMapper.selectByExample(topicModuleExample);

            baseTopicModuleMapper.deleteByExample(topicModuleExample);
            List<Long> moduleIds = topicModule.stream().map(t -> t.getId()).collect(Collectors.toList());

            if (moduleIds != null && moduleIds.stream().count() > 0) {
                BaseModuleProductExample productExample = new BaseModuleProductExample();
                BaseModuleProductExample.Criteria productCriteria = productExample.createCriteria();
                productCriteria.andModuleIdIn(moduleIds);
                baseModuleProductMapper.deleteByExample(productExample);
            }


            for (BaseTopicModule module : topic.getTopicModules()) {
                module.setId(0L);
                module.setTopicId(topic.getId());
                baseTopicModuleMapper.insert(module);
                Long moduleId = module.getId();
                for (BaseModuleProduct moduleProduct : module.getModuleProducts()) {
                    moduleProduct.setId(0L);
                    moduleProduct.setModuleId(moduleId);
                    baseModuleProductMapper.insert(moduleProduct);
                }
            }
        }
        return true;
    }

    public Boolean delete(long id, long shopId) {
        BaseTopicExample topicExample = new BaseTopicExample();
        BaseTopicExample.Criteria topicExampleCriteria = topicExample.createCriteria();
        topicExampleCriteria.andIdEqualTo(id);
        topicExampleCriteria.andShopIdEqualTo(shopId);
        baseTopicMapper.deleteByExample(topicExample);

        BaseTopicModuleExample topicModuleExample = new BaseTopicModuleExample();
        BaseTopicModuleExample.Criteria topicModuleExampleCriteria = topicModuleExample.createCriteria();
        topicModuleExampleCriteria.andTopicIdEqualTo(id);
        baseTopicModuleMapper.deleteByExample(topicModuleExample);
        return true;
    }

    public void updateOnlyModel(BaseTopic topic,BaseTopicExample example){
        baseTopicMapper.updateByExampleSelective(topic,example);
    }

    public BaseTopic getById(long id, long shopId) {
        BaseTopic baseTopic = baseTopicMapper.selectByShopId(id, shopId);
        if (!baseTopic.equals(null)) {
            BaseTopicModuleExample topicModuleExample = new BaseTopicModuleExample();
            BaseTopicModuleExample.Criteria topicModuleExampleCriteria = topicModuleExample.createCriteria();
            topicModuleExampleCriteria.andTopicIdEqualTo(id);

            List<BaseTopicModule> modules = baseTopicModuleMapper.selectByExample(topicModuleExample);
            if (!modules.isEmpty()) {
                List<Long> moduleIds = modules.stream().map(t -> t.getId()).collect(Collectors.toList());

                BaseModuleProductExample moduleProductExample = new BaseModuleProductExample();
                BaseModuleProductExample.Criteria moduleProductExampleCriteria = moduleProductExample.createCriteria();
                moduleProductExampleCriteria.andModuleIdIn(moduleIds);
                List<BaseModuleProduct> moduleProducts = baseModuleProductMapper.selectByExample(moduleProductExample);

                for (BaseTopicModule module : modules) {
                    List<BaseModuleProduct> childModules = moduleProducts.stream()
                            .filter(t -> t.getModuleId().equals(module.getId()))
                            .collect(Collectors.toList());
                    module.setModuleProducts(childModules);
                }
                baseTopic.setTopicModules(modules);
            }

        }
        return baseTopic;
    }

    public BaseTopic selectById(Long id) {
        BaseTopic baseTopic = baseTopicMapper.selectByPrimaryKey(id);
        if (!baseTopic.equals(null)) {
            BaseTopicModuleExample topicModuleExample = new BaseTopicModuleExample();
            BaseTopicModuleExample.Criteria topicModuleExampleCriteria = topicModuleExample.createCriteria();
            topicModuleExampleCriteria.andTopicIdEqualTo(id);

            List<BaseTopicModule> modules = baseTopicModuleMapper.selectByExample(topicModuleExample);
            if (!modules.isEmpty()) {
                List<Long> moduleIds = modules.stream().map(t -> t.getId()).collect(Collectors.toList());

                BaseModuleProductExample moduleProductExample = new BaseModuleProductExample();
                BaseModuleProductExample.Criteria moduleProductExampleCriteria = moduleProductExample.createCriteria();
                moduleProductExampleCriteria.andModuleIdIn(moduleIds);
                List<BaseModuleProduct> moduleProducts = baseModuleProductMapper.selectByExample(moduleProductExample);

                for (BaseTopicModule module : modules) {
                    List<BaseModuleProduct> childModules = moduleProducts.stream()
                        .filter(t -> t.getModuleId().equals(module.getId()))
                        .collect(Collectors.toList());
                    module.setModuleProducts(childModules);
                }
                baseTopic.setTopicModules(modules);
            }

        }
        return baseTopic;
    }

    public int getTopicModulesSize(Long topicId) {
        BaseTopicModuleExample topicModuleExample = new BaseTopicModuleExample();
        BaseTopicModuleExample.Criteria criteria = topicModuleExample.createCriteria();
        criteria.andTopicIdEqualTo(topicId);
        long count = baseTopicModuleMapper.countByExample(topicModuleExample);
        return (int) count;
    }

    public List<BaseTopic> query(BaseTopicExample example) {
        return baseTopicMapper.selectByExample(example);
    }
}
