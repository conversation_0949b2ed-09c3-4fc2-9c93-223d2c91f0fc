package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.shop.domain.*;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.*;
import com.sankuai.shangou.seashop.user.dao.shop.model.CmdFreightTemplateModel;
import com.sankuai.shangou.seashop.user.dao.shop.model.FreightTemplateModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Repository
@Slf4j
public class FreightAreaRepository extends ServiceImpl<FreightTemplateMapper, FreightTemplate> {

    @Resource
    private FreightTemplateMapper freightTemplateMapper;

    @Resource
    private FreightAreaContentMapper freightAreaContentMapper;

    @Resource
    private FreightAreaDetailMapper freightAreaDetailMapper;

    @Resource
    private ShippingFreeGroupMapper shippingFreeGroupMapper;

    @Resource
    private ShippingFreeRegionMapper shippingFreeRegionMapper;

    @Resource
    private RestrictedAreaMapper restrictedAreaMapper;

    public FreightTemplate selectByName(Long shopId, String name){
        QueryWrapper<FreightTemplate> freightQueryWrapper = new QueryWrapper<>();
        freightQueryWrapper.lambda().eq(FreightTemplate::getName, name);
        freightQueryWrapper.lambda().eq(FreightTemplate::getShopId, shopId);
        return freightTemplateMapper.selectOne(freightQueryWrapper);
    }
    public List<FreightTemplate> queryFreightTemplateList(FreightTemplateModel freightTemplate){
        QueryWrapper<FreightTemplate> freightQueryWrapper = new QueryWrapper<>();
        if(freightTemplate.getId() != null){
            freightQueryWrapper.lambda().eq(FreightTemplate::getId, freightTemplate.getId());
        }
        if (freightTemplate.getShopId() != null) {
            freightQueryWrapper.lambda().eq(FreightTemplate::getShopId, freightTemplate.getShopId());
        }
        if (CollectionUtils.isNotEmpty(freightTemplate.getTemplateNames())) {
            freightQueryWrapper.lambda().in(FreightTemplate::getName, freightTemplate.getTemplateNames());
        }
        if(freightTemplate.getValuationMethod() != null){
            freightQueryWrapper.lambda().eq(FreightTemplate::getValuationMethod, freightTemplate.getValuationMethod());
        }
        if(!CollectionUtils.isEmpty(freightTemplate.getValuationMethods())){
            freightQueryWrapper.lambda().in(FreightTemplate::getValuationMethod, freightTemplate.getValuationMethods());
        }
        freightQueryWrapper.lambda().orderByDesc(FreightTemplate::getCreateTime);
        return freightTemplateMapper.selectList(freightQueryWrapper);
    }

    public List<FreightAreaContent> queryFreightAreaContentList(FreightAreaContent freightAreaContent){
        QueryWrapper<FreightAreaContent> freightQueryWrapper = new QueryWrapper<>();
        if(freightAreaContent.getId() != null){
            freightQueryWrapper.lambda().eq(FreightAreaContent::getId, freightAreaContent.getId());
        }
        if (freightAreaContent.getFreightTemplateId() != null) {
            freightQueryWrapper.lambda().eq(FreightAreaContent::getFreightTemplateId, freightAreaContent.getFreightTemplateId());
        }
//        freightQueryWrapper.orderByDesc("create_time");
        return freightAreaContentMapper.selectList(freightQueryWrapper);
    }

    public List<FreightAreaDetail> queryFreightAreaDetailList(FreightAreaDetail freightAreaDetail){
        QueryWrapper<FreightAreaDetail> freightQueryWrapper = new QueryWrapper<>();
        if(freightAreaDetail.getId() != null){
            freightQueryWrapper.lambda().eq(FreightAreaDetail::getId, freightAreaDetail.getId());
        }
        if (freightAreaDetail.getFreightTemplateId() != null) {
            freightQueryWrapper.lambda().eq(FreightAreaDetail::getFreightTemplateId, freightAreaDetail.getFreightTemplateId());
        }
        if (freightAreaDetail.getFreightAreaId() != null) {
            freightQueryWrapper.lambda().eq(FreightAreaDetail::getFreightAreaId, freightAreaDetail.getFreightAreaId());
        }
        return freightAreaDetailMapper.selectList(freightQueryWrapper);
    }

    public List<ShippingFreeGroup> queryShippingFreeGroupList(ShippingFreeGroup shippingFreeGroup){
        QueryWrapper<ShippingFreeGroup> freightQueryWrapper = new QueryWrapper<>();
        if(shippingFreeGroup.getId() != null){
            freightQueryWrapper.lambda().eq(ShippingFreeGroup::getId, shippingFreeGroup.getId());
        }
        if (shippingFreeGroup.getTemplateId() != null) {
            freightQueryWrapper.lambda().eq(ShippingFreeGroup::getTemplateId, shippingFreeGroup.getTemplateId());
        }
        return shippingFreeGroupMapper.selectList(freightQueryWrapper);
    }

    public List<ShippingFreeRegion> queryShippingFreeRegionList(ShippingFreeRegion shippingFreeRegion){
        QueryWrapper<ShippingFreeRegion> freightQueryWrapper = new QueryWrapper<>();
        if(shippingFreeRegion.getId() != null){
            freightQueryWrapper.lambda().eq(ShippingFreeRegion::getId, shippingFreeRegion.getId());
        }
        if (shippingFreeRegion.getTemplateId() != null) {
            freightQueryWrapper.lambda().eq(ShippingFreeRegion::getTemplateId, shippingFreeRegion.getTemplateId());
        }
        if (shippingFreeRegion.getGroupId() != null) {
            freightQueryWrapper.lambda().eq(ShippingFreeRegion::getGroupId, shippingFreeRegion.getGroupId());
        }
        return shippingFreeRegionMapper.selectList(freightQueryWrapper);
    }

    public List<RestrictedArea> queryRestrictedAreaList(RestrictedArea restrictedArea){
        QueryWrapper<RestrictedArea> freightQueryWrapper = new QueryWrapper<>();
        if(restrictedArea.getId() != null){
            freightQueryWrapper.lambda().eq(RestrictedArea::getId, restrictedArea.getId());
        }
        if (restrictedArea.getTemplateId() != null) {
            freightQueryWrapper.lambda().eq(RestrictedArea::getTemplateId, restrictedArea.getTemplateId());
        }
        return restrictedAreaMapper.selectList(freightQueryWrapper);
    }

    // 删除主表 user_freight_template
    public int deleteFreightTemplate(CmdFreightTemplateModel cmdFreightTemplateDto){
        QueryWrapper<FreightTemplate> freightTemplateWrapper = new QueryWrapper<FreightTemplate>();
        if (cmdFreightTemplateDto.getTemplateId() != null) {
            freightTemplateWrapper.lambda().eq(FreightTemplate::getId, cmdFreightTemplateDto.getTemplateId());
        }
        if (cmdFreightTemplateDto.getShopId() != null) {
            freightTemplateWrapper.lambda().eq(FreightTemplate::getShopId, cmdFreightTemplateDto.getShopId());
        }
        return freightTemplateMapper.delete(freightTemplateWrapper);
    }

    // 删除指定可配送区域的运费 user_freight_area_content
    public int deleteUserFreightAreaContent(CmdFreightTemplateModel cmdFreightTemplateDto){
        QueryWrapper<FreightAreaContent> freightAreaContentWrapper = new QueryWrapper<>();
        if (cmdFreightTemplateDto.getTemplateId() != null) {
            freightAreaContentWrapper.lambda().eq(FreightAreaContent::getFreightTemplateId, cmdFreightTemplateDto.getTemplateId());
        }
        return freightAreaContentMapper.delete(freightAreaContentWrapper);
    }

    //删除指定可配送区域的运费详细地址 user_freight_area_detail
    public int deleteUserFreightAreaDetail(CmdFreightTemplateModel cmdFreightTemplateDto){
        QueryWrapper<FreightAreaDetail> freightAreaDetailWrapper = new QueryWrapper<>();
        if (cmdFreightTemplateDto.getTemplateId() != null) {
            freightAreaDetailWrapper.lambda().eq(FreightAreaDetail::getFreightTemplateId, cmdFreightTemplateDto.getTemplateId());
        }
        return freightAreaDetailMapper.delete(freightAreaDetailWrapper);
    }

    //删除指定城市包邮 user_shipping_free_group
    public int deleteUserShippingFreeGroup(CmdFreightTemplateModel cmdFreightTemplateDto){
        QueryWrapper<ShippingFreeGroup> shippingFreeGroupWrapper = new QueryWrapper<>();
        if (cmdFreightTemplateDto.getTemplateId() != null) {
            shippingFreeGroupWrapper.lambda().eq(ShippingFreeGroup::getTemplateId, cmdFreightTemplateDto.getTemplateId());
        }
        return shippingFreeGroupMapper.delete(shippingFreeGroupWrapper);
    }

    //删除指定城市包邮详细地址 user_shipping_free_region
    public int deleteUserShippingFreeRegion(CmdFreightTemplateModel cmdFreightTemplateDto){
        QueryWrapper<ShippingFreeRegion> shippingFreeRegionWrapper = new QueryWrapper<>();
        if (cmdFreightTemplateDto.getTemplateId() != null) {
            shippingFreeRegionWrapper.lambda().eq(ShippingFreeRegion::getTemplateId, cmdFreightTemplateDto.getTemplateId());
        }
        return shippingFreeRegionMapper.delete(shippingFreeRegionWrapper);
    }

    //删除非销售区域 user_restricted_area
    public int deleteUserRestrictedArea(CmdFreightTemplateModel cmdFreightTemplateDto){
        QueryWrapper<RestrictedArea> restrictedAreaWrapper = new QueryWrapper<>();
        if (cmdFreightTemplateDto.getTemplateId() != null) {
            restrictedAreaWrapper.lambda().eq(RestrictedArea::getTemplateId, cmdFreightTemplateDto.getTemplateId());
        }
        return restrictedAreaMapper.delete(restrictedAreaWrapper);
    }

    public Long saveOrUpdateTemplate(FreightTemplate template){
        if(freightTemplateMapper.updateById(template) == 0){
            freightTemplateMapper.insert(template);
        }
        return template.getId();
    }

    public Long saveOrUpdateContent(FreightAreaContent content){
        freightAreaContentMapper.insert(content);
        return content.getId();
    }

    public Long saveOrUpdateDetail(FreightAreaDetail detail){
        freightAreaDetailMapper.insert(detail);
        return detail.getId();
    }

    public Long saveOrUpdateGroup(ShippingFreeGroup group){
        shippingFreeGroupMapper.insert(group);
        return group.getId();
    }

    public Long saveOrUpdateRegion(ShippingFreeRegion region){
        shippingFreeRegionMapper.insert(region);
        return region.getId();
    }

    public Long saveOrUpdateRestricted(RestrictedArea restricted){
        restrictedAreaMapper.insert(restricted);
        return restricted.getId();
    }
}
