package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BasePhotoSpaceMapper {
    long countByExample(BasePhotoSpaceExample example);

    int deleteByExample(BasePhotoSpaceExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BasePhotoSpace record);

    int insertSelective(BasePhotoSpace record);

    List<BasePhotoSpace> selectByExample(BasePhotoSpaceExample example);

    BasePhotoSpace selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BasePhotoSpace record, @Param("example") BasePhotoSpaceExample example);

    int updateByExample(@Param("record") BasePhotoSpace record, @Param("example") BasePhotoSpaceExample example);

    int updateByPrimaryKeySelective(BasePhotoSpace record);

    int updateByPrimaryKey(BasePhotoSpace record);
}