package com.sankuai.shangou.seashop.user.dao.account.repository;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.sankuai.shangou.seashop.user.dao.account.domain.Label;
import com.sankuai.shangou.seashop.user.dao.account.mapper.LabelMapper;
import com.sankuai.shangou.seashop.user.dao.account.mapper.ext.LabelExtMapper;
import com.sankuai.shangou.seashop.user.dao.account.model.LabelExtModel;
import com.sankuai.shangou.seashop.user.dao.account.model.MemberLabelNameExt;

import lombok.extern.slf4j.Slf4j;

/**
 * @description: 标签仓库类
 * @author: LXH
 **/
@Repository
@Slf4j
public class LabelRepository extends ServiceImpl<LabelMapper, Label> {
    @Resource
    private LabelExtMapper labelExtMapper;

    /**
     * 添加标签信息
     *
     * @param label 标签信息
     */
    public void addLabel(Label label) {
        baseMapper.insert(label);
    }

    /**
     * 根据id获取标签信息
     *
     * @param id id
     */
    public Label getOneById(Long id) {
        return baseMapper.selectById(id);
    }

    /**
     * 编辑标签信息
     *
     * @param label 标签信息
     */
    public void editLabel(Label label) {
        baseMapper.updateById(label);
    }

    /**
     * 根据id删除标签信息
     *
     * @param id 标签信息
     */
    public void deleteLabel(Long id) {
        baseMapper.deleteById(id);
    }

    /**
     * 分页查询标签信息
     * @param labelName 标签名称
     * @param pageNo 页码
     * @param pageSize 分页大小
     * @return 分页结果
     */
    public Page<LabelExtModel> queryLabelPage(String labelName, Integer pageNo, Integer pageSize) {
        Page<LabelExtModel> labelExtDtoPage = PageHelper.startPage(pageNo, pageSize);
        labelExtMapper.queryLabelPage(labelName);
        return labelExtDtoPage;
    }

    public List<MemberLabelNameExt> queryLabelName(List<Long> list) {
        return labelExtMapper.queryLabelName(list);
    }
}
