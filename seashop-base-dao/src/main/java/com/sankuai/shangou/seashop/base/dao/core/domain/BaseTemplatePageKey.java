package com.sankuai.shangou.seashop.base.dao.core.domain;

public class BaseTemplatePageKey {
    private Long shopId;

    private Long vShopId;

    private Integer type;

    private String client;

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Long getvShopId() {
        return vShopId;
    }

    public void setvShopId(Long vShopId) {
        this.vShopId = vShopId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getClient() {
        return client;
    }

    public void setClient(String client) {
        this.client = client == null ? null : client.trim();
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BaseTemplatePageKey other = (BaseTemplatePageKey) that;
        return (this.getShopId() == null ? other.getShopId() == null : this.getShopId().equals(other.getShopId()))
            && (this.getvShopId() == null ? other.getvShopId() == null : this.getvShopId().equals(other.getvShopId()))
            && (this.getType() == null ? other.getType() == null : this.getType().equals(other.getType()))
            && (this.getClient() == null ? other.getClient() == null : this.getClient().equals(other.getClient()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getShopId() == null) ? 0 : getShopId().hashCode());
        result = prime * result + ((getvShopId() == null) ? 0 : getvShopId().hashCode());
        result = prime * result + ((getType() == null) ? 0 : getType().hashCode());
        result = prime * result + ((getClient() == null) ? 0 : getClient().hashCode());
        return result;
    }
}