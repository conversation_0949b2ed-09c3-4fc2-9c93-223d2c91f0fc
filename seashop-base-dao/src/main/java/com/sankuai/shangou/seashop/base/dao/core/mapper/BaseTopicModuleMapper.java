package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicModule;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicModuleExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseTopicModuleMapper {
    long countByExample(BaseTopicModuleExample example);

    int deleteByExample(BaseTopicModuleExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseTopicModule record);

    int insertSelective(BaseTopicModule record);

    List<BaseTopicModule> selectByExample(BaseTopicModuleExample example);

    BaseTopicModule selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BaseTopicModule record, @Param("example") BaseTopicModuleExample example);

    int updateByExample(@Param("record") BaseTopicModule record, @Param("example") BaseTopicModuleExample example);

    int updateByPrimaryKeySelective(BaseTopicModule record);

    int updateByPrimaryKey(BaseTopicModule record);
}