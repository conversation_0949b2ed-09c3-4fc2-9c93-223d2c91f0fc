package com.sankuai.shangou.seashop.user.dao.shop.repository;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.sankuai.shangou.seashop.user.dao.shop.domain.OrderSetting;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.OrderSettingMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/27 16:53
 */
@Repository
public class OrderSettingRepository {

    @Resource
    private OrderSettingMapper orderSettingMapper;

    public OrderSetting queryOrderSetting(OrderSetting orderSetting){
        QueryWrapper<OrderSetting> queryWrapper = new QueryWrapper<>();
        if(orderSetting.getShopId() != null){
            queryWrapper.lambda().eq(OrderSetting::getShopId, orderSetting.getShopId());
        }
        return orderSettingMapper.selectOne(queryWrapper);
    }

    public List<OrderSetting> queryBatchOrderSetting(List<Long> shopIdList){
        QueryWrapper<OrderSetting> queryWrapper = new QueryWrapper<>();
        if(!CollectionUtil.isEmpty(shopIdList)){
            queryWrapper.lambda().in(OrderSetting::getShopId, shopIdList);
        }
        return orderSettingMapper.selectList(queryWrapper);
    }

    public int insert(OrderSetting orderSetting){
        return orderSettingMapper.insert(orderSetting);
    }

    public int update(OrderSetting orderSetting){
        LambdaUpdateWrapper<OrderSetting> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(OrderSetting::getShopId, orderSetting.getShopId());
        return orderSettingMapper.update(orderSetting, lambdaUpdateWrapper);
    }
}
