package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.shangou.seashop.base.dao.core.domain.RefundReason;
import com.sankuai.shangou.seashop.base.dao.core.mapper.RefundReasonMapper;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/23 9:45
 */
@Component
@Slf4j
public class RefundReasonRepository {

    @Resource
    private RefundReasonMapper refundReasonMapper;

    public RefundReason selectById(Long id){
        return refundReasonMapper.selectById(id);
    }

    public List<RefundReason> queryRefundReasonList(RefundReason refundReason){
        QueryWrapper<RefundReason> queryWrapper = new QueryWrapper<>();
        if(refundReason.getId() != null){
            queryWrapper.lambda().eq(RefundReason::getId, refundReason.getId());
        }
        if(!StringUtils.isEmpty(refundReason.getAfterSalesText())){
            queryWrapper.lambda().eq(RefundReason::getAfterSalesText, refundReason.getAfterSalesText());
        }
        queryWrapper.orderByDesc("create_time");
        return refundReasonMapper.selectList(queryWrapper);
    }

    public int addRefundReason(RefundReason refundReason){
        return refundReasonMapper.insert(refundReason);
    }

    public int updateRefundReason(RefundReason refundReason){
        return refundReasonMapper.updateById(refundReason);
    }

    public int deleteRefundReason(Long id){
        return refundReasonMapper.deleteById(id);
    }
}
