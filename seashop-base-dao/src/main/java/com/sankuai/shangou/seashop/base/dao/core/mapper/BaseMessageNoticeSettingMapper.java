package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSetting;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSettingExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseMessageNoticeSettingMapper {
    long countByExample(BaseMessageNoticeSettingExample example);

    int deleteByExample(BaseMessageNoticeSettingExample example);

    int deleteByPrimaryKey(Integer messageType);

    int insert(BaseMessageNoticeSetting record);

    int insertSelective(BaseMessageNoticeSetting record);

    List<BaseMessageNoticeSetting> selectByExample(BaseMessageNoticeSettingExample example);

    BaseMessageNoticeSetting selectByPrimaryKey(Integer messageType);

    int updateByExampleSelective(@Param("record") BaseMessageNoticeSetting record, @Param("example") BaseMessageNoticeSettingExample example);

    int updateByExample(@Param("record") BaseMessageNoticeSetting record, @Param("example") BaseMessageNoticeSettingExample example);

    int updateByPrimaryKeySelective(BaseMessageNoticeSetting record);

    int updateByPrimaryKey(BaseMessageNoticeSetting record);
}