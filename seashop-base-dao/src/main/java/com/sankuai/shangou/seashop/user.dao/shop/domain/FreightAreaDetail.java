package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 运费模板详情
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_freight_area_detail")
public class FreightAreaDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运费模板ID
     */
    @TableField("freight_template_id")
    private Long freightTemplateId;

    /**
     * 模板地区Id
     */
    @TableField("freight_area_id")
    private Long freightAreaId;

    /**
     * 省份ID
     */
    @TableField("province_id")
    private Integer provinceId;

    /**
     * 城市ID
     */
    @TableField("city_id")
    private Integer cityId;

    /**
     * 区ID
     */
    @TableField("county_id")
    private Integer countyId;

    /**
     * 乡镇的ID用逗号隔开
     */
    @TableField("town_ids")
    private String townIds;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFreightTemplateId() {
        return freightTemplateId;
    }

    public void setFreightTemplateId(Long freightTemplateId) {
        this.freightTemplateId = freightTemplateId;
    }

    public Long getFreightAreaId() {
        return freightAreaId;
    }

    public void setFreightAreaId(Long freightAreaId) {
        this.freightAreaId = freightAreaId;
    }

    public Integer getProvinceId() {
        return provinceId;
    }

    public void setProvinceId(Integer provinceId) {
        this.provinceId = provinceId;
    }

    public Integer getCityId() {
        return cityId;
    }

    public void setCityId(Integer cityId) {
        this.cityId = cityId;
    }

    public Integer getCountyId() {
        return countyId;
    }

    public void setCountyId(Integer countyId) {
        this.countyId = countyId;
    }

    public String getTownIds() {
        return townIds;
    }

    public void setTownIds(String townIds) {
        this.townIds = townIds;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "FreightAreaDetail{" +
        "id=" + id +
        ", freightTemplateId=" + freightTemplateId +
        ", freightAreaId=" + freightAreaId +
        ", provinceId=" + provinceId +
        ", cityId=" + cityId +
        ", countyId=" + countyId +
        ", townIds=" + townIds +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
