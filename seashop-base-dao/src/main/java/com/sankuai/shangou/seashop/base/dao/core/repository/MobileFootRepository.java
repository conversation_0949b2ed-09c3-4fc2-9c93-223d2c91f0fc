package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseMobileFootMenu;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseMobileFootMenuMapper;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author： liweisong
 * @create： 2023/11/29 13:51
 */
@Repository
public class MobileFootRepository {

    @Resource
    private BaseMobileFootMenuMapper baseMobileFootMenuMapper;

    public BaseMobileFootMenu selectById(Long id){
        return baseMobileFootMenuMapper.selectById(id);
    }

    public BaseMobileFootMenu selectByNameAndType(String name, Integer type){
        QueryWrapper<BaseMobileFootMenu> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BaseMobileFootMenu::getName, name);
        queryWrapper.lambda().eq(BaseMobileFootMenu::getType, type);
        return baseMobileFootMenuMapper.selectOne(queryWrapper);
    }

    public List<BaseMobileFootMenu> selectList(BaseMobileFootMenu baseMobileFootMenu){
        QueryWrapper<BaseMobileFootMenu> queryWrapper = new QueryWrapper<>();
        if(baseMobileFootMenu.getId() != null){
            queryWrapper.lambda().eq(BaseMobileFootMenu::getId, baseMobileFootMenu.getId());
        }
        if(baseMobileFootMenu.getShopId() != null){
            queryWrapper.lambda().eq(BaseMobileFootMenu::getShopId, baseMobileFootMenu.getShopId());
        }
        if(baseMobileFootMenu.getType() != null){
            queryWrapper.lambda().eq(BaseMobileFootMenu::getType, baseMobileFootMenu.getType());
        }
        return baseMobileFootMenuMapper.selectList(queryWrapper);
    }

    public int insert(BaseMobileFootMenu baseMobileFootMenu){
        return baseMobileFootMenuMapper.insert(baseMobileFootMenu);
    }

    public int updateById(BaseMobileFootMenu baseMobileFootMenu){
        return baseMobileFootMenuMapper.updateById(baseMobileFootMenu);
    }

    public int update(BaseMobileFootMenu baseMobileFootMenu){
        return baseMobileFootMenuMapper.update(baseMobileFootMenu, new LambdaQueryWrapper<BaseMobileFootMenu>().eq(BaseMobileFootMenu::getId, baseMobileFootMenu.getId()));
    }

    public int deleteById(Long id){
        return baseMobileFootMenuMapper.deleteById(id);
    }
}
