package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.sankuai.shangou.seashop.base.boot.log.annotation.ExaminField;
import com.sankuai.shangou.seashop.base.boot.log.annotation.PrimaryField;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 运费模板区域信息表
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_freight_area_content")
public class FreightAreaContent implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @PrimaryField
    private Long id;

    /**
     * 运费模板ID
     */
    @TableField("freight_template_id")
    private Long freightTemplateId;

    /**
     * 地区选择
     */
    @TableField("area_content")
    @ExaminField
    private String areaContent;

    /**
     * 首笔单元计量
     */
    @TableField("first_unit")
    @ExaminField
    private Integer firstUnit;

    /**
     * 首笔单元费用
     */
    @TableField("first_unit_monry")
    @ExaminField
    private BigDecimal firstUnitMonry;

    /**
     * 递增单元计量
     */
    @TableField("accumulation_unit")
    @ExaminField
    private Integer accumulationUnit;

    /**
     * 递增单元费用
     */
    @TableField("accumulation_unit_money")
    @ExaminField
    private BigDecimal accumulationUnitMoney;

    /**
     * 是否为默认
     */
    @TableField("whether_default")
    @ExaminField
    private Boolean whetherDefault;

    /**
     * 创建时间
     */
    @TableField("create_time")
    @ExaminField
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    @ExaminField
    private Date updateTime;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getFreightTemplateId() {
        return freightTemplateId;
    }

    public void setFreightTemplateId(Long freightTemplateId) {
        this.freightTemplateId = freightTemplateId;
    }

    public String getAreaContent() {
        return areaContent;
    }

    public void setAreaContent(String areaContent) {
        this.areaContent = areaContent;
    }

    public Integer getFirstUnit() {
        return firstUnit;
    }

    public void setFirstUnit(Integer firstUnit) {
        this.firstUnit = firstUnit;
    }

    public BigDecimal getFirstUnitMonry() {
        return firstUnitMonry;
    }

    public void setFirstUnitMonry(BigDecimal firstUnitMonry) {
        this.firstUnitMonry = firstUnitMonry;
    }

    public Integer getAccumulationUnit() {
        return accumulationUnit;
    }

    public void setAccumulationUnit(Integer accumulationUnit) {
        this.accumulationUnit = accumulationUnit;
    }

    public BigDecimal getAccumulationUnitMoney() {
        return accumulationUnitMoney;
    }

    public void setAccumulationUnitMoney(BigDecimal accumulationUnitMoney) {
        this.accumulationUnitMoney = accumulationUnitMoney;
    }

    public Boolean getWhetherDefault() {
        return whetherDefault;
    }

    public void setWhetherDefault(Boolean whetherDefault) {
        this.whetherDefault = whetherDefault;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "FreightAreaContent{" +
        "id=" + id +
        ", freightTemplateId=" + freightTemplateId +
        ", areaContent=" + areaContent +
        ", firstUnit=" + firstUnit +
        ", firstUnitMonry=" + firstUnitMonry +
        ", accumulationUnit=" + accumulationUnit +
        ", accumulationUnitMoney=" + accumulationUnitMoney +
        ", whetherDefault=" + whetherDefault +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
