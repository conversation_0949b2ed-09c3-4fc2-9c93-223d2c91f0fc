package com.sankuai.shangou.seashop.user.dao.account.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.account.domain.Role;
import com.sankuai.shangou.seashop.user.dao.account.mapper.RoleMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @description: 权限组仓库类
 * @author: LXH
 **/
@Repository
@Slf4j
public class RoleRepository extends ServiceImpl<RoleMapper, Role> {

    public List<Role> selectList(Role role){
        QueryWrapper<Role> roleQueryWrapper = new QueryWrapper<Role>();
        if (role.getShopId() != null){
            roleQueryWrapper.lambda().eq(Role::getShopId, role.getShopId());
        }
        if (role.getRoleName() != null){
            roleQueryWrapper.lambda().eq(Role::getRoleName, role.getRoleName());
        }
        return baseMapper.selectList(roleQueryWrapper);
    }

    public int insert(Role role){
        return baseMapper.insert(role);
    }

    public int update(Role role){
        return baseMapper.updateById(role);
    }

    public int delete(Long id){
        return baseMapper.deleteById(id);
    }

    public Role selectById(Long id){
        return baseMapper.selectById(id);
    }

    public List<Role> selectListByShopId(Long id) {
        QueryWrapper<Role> roleQueryWrapper = new QueryWrapper<Role>();
        roleQueryWrapper.lambda().eq(Role::getShopId, id);
        roleQueryWrapper.orderByDesc("id");
        return baseMapper.selectList(roleQueryWrapper);
    }
}
