package com.sankuai.shangou.seashop.user.dao.account.repository;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.account.domain.RolePrivilege;
import com.sankuai.shangou.seashop.user.dao.account.mapper.RolePrivilegeMapper;

import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * @description: 权限组仓库类
 * @author: LXH
 **/
@Repository
@Slf4j
public class RolePrivilegeRepository extends ServiceImpl<RolePrivilegeMapper, RolePrivilege> {

    public int insert(Long roleId, List<Long> privilegeId){
        if (CollectionUtil.isEmpty(privilegeId)){
            return 0;
        }
        Date now = new Date();
        //TODO 批量插入
        List<RolePrivilege> rolePrivileges = new ArrayList<>();
        for (Long id : privilegeId) {
            RolePrivilege rolePrivilege = new RolePrivilege();
            rolePrivilege.setRoleId(roleId);
            rolePrivilege.setPrivilegeId(Math.toIntExact(id));
            rolePrivilege.setCreateTime(now);
            rolePrivilege.setUpdateTime(now);
            rolePrivileges.add(rolePrivilege);
        }
        saveBatch(rolePrivileges);
        return privilegeId.size();
    }

    public int deleteByRoleId(Long roleId){
        QueryWrapper<RolePrivilege> rolePrivilegeQueryWrapper = new QueryWrapper<>();
        rolePrivilegeQueryWrapper.lambda().eq(RolePrivilege::getRoleId, roleId);
        return baseMapper.delete(rolePrivilegeQueryWrapper);
    }

    public List<Integer> selectPrivilegeIdsByRoleId(Long id) {
        List<RolePrivilege> list = this.lambdaQuery().eq(RolePrivilege::getRoleId, id).list();
        return list.stream().map(RolePrivilege::getPrivilegeId).collect(Collectors.toList());
    }

    public List<Integer> selectPrivilegeIdsByRoleIds(List<Long> roleIds) {
        if (CollUtil.isEmpty(roleIds)){
            return new ArrayList<>();
        }
        List<RolePrivilege> list = this.lambdaQuery().in(RolePrivilege::getRoleId, roleIds).list();
        return list.stream().map(RolePrivilege::getPrivilegeId).collect(Collectors.toList());
    }

    public List<Long> getPrivilegeIdByRoleId(Long roleId) {
        List<RolePrivilege> rolePrivilegeList = list(new LambdaQueryWrapper<RolePrivilege>()
                .eq(RolePrivilege::getRoleId, roleId)
                .select(RolePrivilege::getPrivilegeId));
        return rolePrivilegeList.stream().map(privilege -> privilege.getPrivilegeId().longValue()).collect(Collectors.toList());
    }
}
