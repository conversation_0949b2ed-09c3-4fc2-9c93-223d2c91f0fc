package com.sankuai.shangou.seashop.base.dao.core.domain;

import java.util.List;

public class BaseTopicModule {
    private Long id;

    private Long topicId;

    private String name;

    private Integer titleAlign;

    public List<BaseModuleProduct> getModuleProducts() {
        return moduleProducts;
    }

    public void setModuleProducts(List<BaseModuleProduct> moduleProducts) {
        this.moduleProducts = moduleProducts;
    }

    private List<BaseModuleProduct> moduleProducts;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getTopicId() {
        return topicId;
    }

    public void setTopicId(Long topicId) {
        this.topicId = topicId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public Integer getTitleAlign() {
        return titleAlign;
    }

    public void setTitleAlign(Integer titleAlign) {
        this.titleAlign = titleAlign;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        BaseTopicModule other = (BaseTopicModule) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getTopicId() == null ? other.getTopicId() == null : this.getTopicId().equals(other.getTopicId()))
            && (this.getName() == null ? other.getName() == null : this.getName().equals(other.getName()))
            && (this.getTitleAlign() == null ? other.getTitleAlign() == null : this.getTitleAlign().equals(other.getTitleAlign()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getTopicId() == null) ? 0 : getTopicId().hashCode());
        result = prime * result + ((getName() == null) ? 0 : getName().hashCode());
        result = prime * result + ((getTitleAlign() == null) ? 0 : getTitleAlign().hashCode());
        return result;
    }
}