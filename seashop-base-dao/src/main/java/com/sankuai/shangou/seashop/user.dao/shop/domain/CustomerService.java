package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2023-11-29
 */
@TableName("user_customer_service")
public class CustomerService implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("shop_id")
    private Long shopId;

    /**
     * 工具类型（qq、旺旺）
     */
    @TableField("tool")
    private Integer tool;

    @TableField("type")
    private Integer type;

    /**
     * 客服名称
     */
    @TableField("name")
    private String name;

    /**
     * 通信账号
     */
    @TableField("account_code")
    private String accountCode;

    /**
     * 终端类型
     */
    @TableField("terminal_type")
    private Integer terminalType;

    /**
     * 客服状态
     */
    @TableField("server_status")
    private Integer serverStatus;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getTool() {
        return tool;
    }

    public void setTool(Integer tool) {
        this.tool = tool;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getAccountCode() {
        return accountCode;
    }

    public void setAccountCode(String accountCode) {
        this.accountCode = accountCode;
    }

    public Integer getTerminalType() {
        return terminalType;
    }

    public void setTerminalType(Integer terminalType) {
        this.terminalType = terminalType;
    }

    public Integer getServerStatus() {
        return serverStatus;
    }

    public void setServerStatus(Integer serverStatus) {
        this.serverStatus = serverStatus;
    }

    @Override
    public String toString() {
        return "CustomerService{" +
        "id=" + id +
        ", shopId=" + shopId +
        ", tool=" + tool +
        ", type=" + type +
        ", name=" + name +
        ", accountCode=" + accountCode +
        ", terminalType=" + terminalType +
        ", serverStatus=" + serverStatus +
        "}";
    }
}
