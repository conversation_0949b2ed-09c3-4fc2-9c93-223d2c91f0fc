package com.sankuai.shangou.seashop.user.dao.account.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 权限信息表
 * </p>
 *
 * <AUTHOR> @since 2024-04-08
 */
@TableName("user_privilege")
public class Privilege implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId("id")
    private Long id;

    /**
     * 名称
     */
    @TableField("name")
    private String name;

    /**
     * url链接
     */
    @TableField("url")
    private String url;

    /**
     * 菜单图标
     */
    @TableField("icon")
    private String icon;

    /**
     * 菜单选中图标
     */
    @TableField("actived_icon")
    private String activedIcon;

    /**
     * 动作
     */
    @TableField("action")
    private String action;

    /**
     * 父类权限ID，祖辈则为0
     */
    @TableField("parent_id")
    private Long parentId;

    /**
     * 是否删除
     */
    @TableField("whether_delete")
    private Boolean whetherDelete;

    /**
     * 排序
     */
    @TableField("display_sequence")
    private Integer displaySequence;

    /**
     * 权限平台，0平台，1供应商，2商家
     */
    @TableField("platform_id")
    private Integer platformId;

    /**
     * 对指定店铺类型隐藏
     */
    @TableField("shop_type")
    private Integer shopType;

    /**
     * 权限类型，0菜单，1子页面，2按钮
     */
    @TableField("power_type")
    private Integer powerType;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 编码
     */
    @TableField("code")
    private String code;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public Boolean getIzTab() {
        return izTab;
    }

    public void setIzTab(Boolean izTab) {
        this.izTab = izTab;
    }

    @TableField("izTab")
    /**
     * 是否是tab
     */
    private Boolean izTab;
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getActivedIcon() {
        return activedIcon;
    }

    public void setActivedIcon(String activedIcon) {
        this.activedIcon = activedIcon;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Boolean getWhetherDelete() {
        return whetherDelete;
    }

    public void setWhetherDelete(Boolean whetherDelete) {
        this.whetherDelete = whetherDelete;
    }

    public Integer getDisplaySequence() {
        return displaySequence;
    }

    public void setDisplaySequence(Integer displaySequence) {
        this.displaySequence = displaySequence;
    }

    public Integer getPlatformId() {
        return platformId;
    }

    public void setPlatformId(Integer platformId) {
        this.platformId = platformId;
    }

    public Integer getShopType() {
        return shopType;
    }

    public void setShopType(Integer shopType) {
        this.shopType = shopType;
    }

    public Integer getPowerType() {
        return powerType;
    }

    public void setPowerType(Integer powerType) {
        this.powerType = powerType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "Privilege{" +
        "id=" + id +
        ", name=" + name +
        ", url=" + url +
        ", icon=" + icon +
            ", activedIcon=" + activedIcon +
            ", action=" + action +
        ", parentId=" + parentId +
        ", whetherDelete=" + whetherDelete +
        ", displaySequence=" + displaySequence +
        ", platformId=" + platformId +
            ", shopType=" + shopType +
        ", powerType=" + powerType +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }
}
