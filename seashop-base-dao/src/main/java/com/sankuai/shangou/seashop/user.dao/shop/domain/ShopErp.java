package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * erp管理
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_shop_erp")
public class ShopErp implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺id
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 开启erp类型 0未开启 1旺店通 2聚水潭 3网店管家 4吉客云
     */
    @TableField("erp_type")
    private Integer erpType;

    /**
     * 聚水潭授权 0未授权 1已授权
     */
    @TableField("jst_status")
    private Boolean jstStatus;

    /**
     * 聚水潭授权url
     */
    @TableField("jst_url")
    private String jstUrl;

    /**
     * 聚水潭授权url创建时间
     */
    @TableField("jst_url_create_time")
    private Date jstUrlCreateTime;

    /**
     * 聚水潭授权码
     */
    @TableField("jst_code")
    private String jstCode;

    /**
     * 聚水潭授权码获得时间
     */
    @TableField("jst_code_get_time")
    private Date jstCodeGetTime;

    /**
     * 聚水潭访问令牌
     */
    @TableField("jst_access_token")
    private String jstAccessToken;

    /**
     * 聚水潭访问令牌多少秒后过期
     */
    @TableField("jst_access_token_expires")
    private Integer jstAccessTokenExpires;

    /**
     * 聚水潭更新令牌
     */
    @TableField("jst_refresh_token")
    private String jstRefreshToken;

    /**
     * 聚水潭令牌获取时间
     */
    @TableField("jst_token_get_time")
    private Date jstTokenGetTime;

    /**
     * 聚水潭店铺编号
     */
    @TableField("jst_shop_id")
    private String jstShopId;

    /**
     * 聚水潭公司编号
     */
    @TableField("jst_co_id")
    private String jstCoId;

    /**
     * 菠萝派供应商token
     */
    @TableField("blp_token")
    private String blpToken;

    /**
     * 旺店通访问令牌 (弃用  这个是美团的)
     */
    @TableField("wdt_token")
    private String wdtToken;

    /**
     * 旺店通 账号id
     */
    @TableField(value = "wdt_sid", exist = false)
    private String wdtSid;

    /**
     * 旺店通开放应用 appkey
     */
    @TableField(value = "wdt_app_key", exist = false)
    private String wdtAppKey;

    /**
     * 旺店通店铺编码
     */
    @TableField(value = "wdt_shop_no", exist = false)
    private String wdtShopNo;

    /**
     * 旺店通开放应用 appSecret
     */
    @TableField(value = "wdt_app_secret", exist = false)
    private String wdtAppSecret;

    public String getWdtSid() {
        return wdtSid;
    }

    public void setWdtSid(String wdtSid) {
        this.wdtSid = wdtSid;
    }

    public String getWdtAppKey() {
        return wdtAppKey;
    }

    public void setWdtAppKey(String wdtAppKey) {
        this.wdtAppKey = wdtAppKey;
    }

    public String getWdtShopNo() {
        return wdtShopNo;
    }

    public void setWdtShopNo(String wdtShopNo) {
        this.wdtShopNo = wdtShopNo;
    }

    public String getWdtAppSecret() {
        return wdtAppSecret;
    }

    public void setWdtAppSecret(String wdtAppSecret) {
        this.wdtAppSecret = wdtAppSecret;
    }

    /**
     * 是否发送短信
     */
    @TableField("whether_send_sms")
    private Boolean whetherSendSms;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    @TableField(exist = false)
    private List<Integer> erpTypeList;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Integer getErpType() {
        return erpType;
    }

    public void setErpType(Integer erpType) {
        this.erpType = erpType;
    }

    public Boolean getJstStatus() {
        return jstStatus;
    }

    public void setJstStatus(Boolean jstStatus) {
        this.jstStatus = jstStatus;
    }

    public String getJstUrl() {
        return jstUrl;
    }

    public void setJstUrl(String jstUrl) {
        this.jstUrl = jstUrl;
    }

    public Date getJstUrlCreateTime() {
        return jstUrlCreateTime;
    }

    public void setJstUrlCreateTime(Date jstUrlCreateTime) {
        this.jstUrlCreateTime = jstUrlCreateTime;
    }

    public String getJstCode() {
        return jstCode;
    }

    public void setJstCode(String jstCode) {
        this.jstCode = jstCode;
    }

    public Date getJstCodeGetTime() {
        return jstCodeGetTime;
    }

    public void setJstCodeGetTime(Date jstCodeGetTime) {
        this.jstCodeGetTime = jstCodeGetTime;
    }

    public String getJstAccessToken() {
        return jstAccessToken;
    }

    public void setJstAccessToken(String jstAccessToken) {
        this.jstAccessToken = jstAccessToken;
    }

    public Integer getJstAccessTokenExpires() {
        return jstAccessTokenExpires;
    }

    public void setJstAccessTokenExpires(Integer jstAccessTokenExpires) {
        this.jstAccessTokenExpires = jstAccessTokenExpires;
    }

    public String getJstRefreshToken() {
        return jstRefreshToken;
    }

    public void setJstRefreshToken(String jstRefreshToken) {
        this.jstRefreshToken = jstRefreshToken;
    }

    public Date getJstTokenGetTime() {
        return jstTokenGetTime;
    }

    public void setJstTokenGetTime(Date jstTokenGetTime) {
        this.jstTokenGetTime = jstTokenGetTime;
    }

    public String getJstShopId() {
        return jstShopId;
    }

    public void setJstShopId(String jstShopId) {
        this.jstShopId = jstShopId;
    }

    public String getJstCoId() {
        return jstCoId;
    }

    public void setJstCoId(String jstCoId) {
        this.jstCoId = jstCoId;
    }

    public String getBlpToken() {
        return blpToken;
    }

    public void setBlpToken(String blpToken) {
        this.blpToken = blpToken;
    }

    public String getWdtToken() {
        return wdtToken;
    }

    public void setWdtToken(String wdtToken) {
        this.wdtToken = wdtToken;
    }

    public Boolean getWhetherSendSms() {
        return whetherSendSms;
    }

    public void setWhetherSendSms(Boolean whetherSendSms) {
        this.whetherSendSms = whetherSendSms;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public List<Integer> getErpTypeList() {
        return erpTypeList;
    }

    public void setErpTypeList(List<Integer> erpTypeList) {
        this.erpTypeList = erpTypeList;
    }

    @Override
    public String toString() {
        return "ShopErp{" +
                "id=" + id +
                ", shopId=" + shopId +
                ", erpType=" + erpType +
                ", jstStatus=" + jstStatus +
                ", jstUrl=" + jstUrl +
                ", jstUrlCreateTime=" + jstUrlCreateTime +
                ", jstCode=" + jstCode +
                ", jstCodeGetTime=" + jstCodeGetTime +
                ", jstAccessToken=" + jstAccessToken +
                ", jstAccessTokenExpires=" + jstAccessTokenExpires +
                ", jstRefreshToken=" + jstRefreshToken +
                ", jstTokenGetTime=" + jstTokenGetTime +
                ", jstShopId=" + jstShopId +
                ", jstCoId=" + jstCoId +
                ", blpToken=" + blpToken +
                ", wdtToken=" + wdtToken +
                ", whetherSendSms=" + whetherSendSms +
                ", createTime=" + createTime +
                ", updateTime=" + updateTime +
                "}";
    }
}
