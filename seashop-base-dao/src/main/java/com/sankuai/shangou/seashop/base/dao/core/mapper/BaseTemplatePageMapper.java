package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePage;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageExample;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageKey;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseTemplatePageMapper {
    long countByExample(BaseTemplatePageExample example);

    int deleteByExample(BaseTemplatePageExample example);

    int deleteByPrimaryKey(BaseTemplatePageKey key);

    int insert(BaseTemplatePageWithBLOBs record);

    int insertSelective(BaseTemplatePageWithBLOBs record);

    List<BaseTemplatePageWithBLOBs> selectByExampleWithBLOBs(BaseTemplatePageExample example);

    List<BaseTemplatePage> selectByExample(BaseTemplatePageExample example);

    BaseTemplatePageWithBLOBs selectByPrimaryKey(BaseTemplatePageKey key);

    int updateByExampleSelective(@Param("record") BaseTemplatePageWithBLOBs record, @Param("example") BaseTemplatePageExample example);

    int updateByExampleWithBLOBs(@Param("record") BaseTemplatePageWithBLOBs record, @Param("example") BaseTemplatePageExample example);

    int updateByExample(@Param("record") BaseTemplatePage record, @Param("example") BaseTemplatePageExample example);

    int updateByPrimaryKeySelective(BaseTemplatePageWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(BaseTemplatePageWithBLOBs record);

    int updateByPrimaryKey(BaseTemplatePage record);
}