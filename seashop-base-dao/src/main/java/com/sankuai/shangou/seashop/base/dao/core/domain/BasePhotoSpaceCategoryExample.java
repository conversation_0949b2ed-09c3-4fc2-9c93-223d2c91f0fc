package com.sankuai.shangou.seashop.base.dao.core.domain;

import java.util.ArrayList;
import java.util.List;

public class BasePhotoSpaceCategoryExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public BasePhotoSpaceCategoryExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andShopIdIsNull() {
            addCriterion("shop_id is null");
            return (Criteria) this;
        }

        public Criteria andShopIdIsNotNull() {
            addCriterion("shop_id is not null");
            return (Criteria) this;
        }

        public Criteria andShopIdEqualTo(Long value) {
            addCriterion("shop_id =", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdNotEqualTo(Long value) {
            addCriterion("shop_id <>", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdGreaterThan(Long value) {
            addCriterion("shop_id >", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdGreaterThanOrEqualTo(Long value) {
            addCriterion("shop_id >=", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdLessThan(Long value) {
            addCriterion("shop_id <", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdLessThanOrEqualTo(Long value) {
            addCriterion("shop_id <=", value, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdIn(List<Long> values) {
            addCriterion("shop_id in", values, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdNotIn(List<Long> values) {
            addCriterion("shop_id not in", values, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdBetween(Long value1, Long value2) {
            addCriterion("shop_id between", value1, value2, "shopId");
            return (Criteria) this;
        }

        public Criteria andShopIdNotBetween(Long value1, Long value2) {
            addCriterion("shop_id not between", value1, value2, "shopId");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameIsNull() {
            addCriterion("photo_space_catrgory_name is null");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameIsNotNull() {
            addCriterion("photo_space_catrgory_name is not null");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameEqualTo(String value) {
            addCriterion("photo_space_catrgory_name =", value, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameNotEqualTo(String value) {
            addCriterion("photo_space_catrgory_name <>", value, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameGreaterThan(String value) {
            addCriterion("photo_space_catrgory_name >", value, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameGreaterThanOrEqualTo(String value) {
            addCriterion("photo_space_catrgory_name >=", value, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameLessThan(String value) {
            addCriterion("photo_space_catrgory_name <", value, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameLessThanOrEqualTo(String value) {
            addCriterion("photo_space_catrgory_name <=", value, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameLike(String value) {
            addCriterion("photo_space_catrgory_name like", value, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameNotLike(String value) {
            addCriterion("photo_space_catrgory_name not like", value, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameIn(List<String> values) {
            addCriterion("photo_space_catrgory_name in", values, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameNotIn(List<String> values) {
            addCriterion("photo_space_catrgory_name not in", values, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameBetween(String value1, String value2) {
            addCriterion("photo_space_catrgory_name between", value1, value2, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andPhotoSpaceCatrgoryNameNotBetween(String value1, String value2) {
            addCriterion("photo_space_catrgory_name not between", value1, value2, "photoSpaceCatrgoryName");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceIsNull() {
            addCriterion("displayS_sequence is null");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceIsNotNull() {
            addCriterion("displayS_sequence is not null");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceEqualTo(Long value) {
            addCriterion("displayS_sequence =", value, "displaysSequence");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceNotEqualTo(Long value) {
            addCriterion("displayS_sequence <>", value, "displaysSequence");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceGreaterThan(Long value) {
            addCriterion("displayS_sequence >", value, "displaysSequence");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceGreaterThanOrEqualTo(Long value) {
            addCriterion("displayS_sequence >=", value, "displaysSequence");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceLessThan(Long value) {
            addCriterion("displayS_sequence <", value, "displaysSequence");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceLessThanOrEqualTo(Long value) {
            addCriterion("displayS_sequence <=", value, "displaysSequence");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceIn(List<Long> values) {
            addCriterion("displayS_sequence in", values, "displaysSequence");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceNotIn(List<Long> values) {
            addCriterion("displayS_sequence not in", values, "displaysSequence");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceBetween(Long value1, Long value2) {
            addCriterion("displayS_sequence between", value1, value2, "displaysSequence");
            return (Criteria) this;
        }

        public Criteria andDisplaysSequenceNotBetween(Long value1, Long value2) {
            addCriterion("displayS_sequence not between", value1, value2, "displaysSequence");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}