package com.sankuai.shangou.seashop.user.dao.account.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR> @since 2024-01-03
 */
@TableName("user_invoice_title")
public class InvoiceTitle implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 发票类型（1:普通发票、2:电子发票、3:增值税发票）
     */
    @TableField("invoice_type")
    private Integer invoiceType;

    /**
     * 抬头名称
     */
    @TableField("name")
    private String name;

    /**
     * 税号
     */
    @TableField("code")
    private String code;

    /**
     * 发票明细
     */
    @TableField("invoice_context")
    private String invoiceContext;

    /**
     * 注册地址
     */
    @TableField("register_address")
    private String registerAddress;

    /**
     * 注册电话
     */
    @TableField("register_phone")
    private String registerPhone;

    /**
     * 开户银行
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 银行帐号
     */
    @TableField("bank_no")
    private String bankNo;

    /**
     * 收票人姓名
     */
    @TableField("real_name")
    private String realName;

    /**
     * 收票人手机号
     */
    @TableField("cell_phone")
    private String cellPhone;

    /**
     * 收票人邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 收票人地址区域ID
     */
    @TableField("region_id")
    private Integer regionId;

    /**
     * 收票人详细地址
     */
    @TableField("address")
    private String address;

    /**
     * 是否默认
     */
    @TableField("is_default")
    private Boolean whetherDefault;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public Integer getInvoiceType() {
        return invoiceType;
    }

    public void setInvoiceType(Integer invoiceType) {
        this.invoiceType = invoiceType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getInvoiceContext() {
        return invoiceContext;
    }

    public void setInvoiceContext(String invoiceContext) {
        this.invoiceContext = invoiceContext;
    }

    public String getRegisterAddress() {
        return registerAddress;
    }

    public void setRegisterAddress(String registerAddress) {
        this.registerAddress = registerAddress;
    }

    public String getRegisterPhone() {
        return registerPhone;
    }

    public void setRegisterPhone(String registerPhone) {
        this.registerPhone = registerPhone;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getBankNo() {
        return bankNo;
    }

    public void setBankNo(String bankNo) {
        this.bankNo = bankNo;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getCellPhone() {
        return cellPhone;
    }

    public void setCellPhone(String cellPhone) {
        this.cellPhone = cellPhone;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getRegionId() {
        return regionId;
    }

    public void setRegionId(Integer regionId) {
        this.regionId = regionId;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Boolean getWhetherDefault() {
        return whetherDefault;
    }

    public void setWhetherDefault(Boolean whetherDefault) {
        this.whetherDefault = whetherDefault;
    }

    @Override
    public String toString() {
        return "InvoiceTitle{" +
        "id=" + id +
        ", userId=" + userId +
        ", invoiceType=" + invoiceType +
        ", name=" + name +
        ", code=" + code +
        ", invoiceContext=" + invoiceContext +
        ", registerAddress=" + registerAddress +
        ", registerPhone=" + registerPhone +
        ", bankName=" + bankName +
        ", bankNo=" + bankNo +
        ", realName=" + realName +
        ", cellPhone=" + cellPhone +
        ", email=" + email +
        ", regionId=" + regionId +
        ", address=" + address +
        ", whetherDefault=" + whetherDefault +
        "}";
    }
}
