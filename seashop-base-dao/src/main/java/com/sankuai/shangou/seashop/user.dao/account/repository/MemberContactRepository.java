package com.sankuai.shangou.seashop.user.dao.account.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.account.domain.MemberContact;
import com.sankuai.shangou.seashop.user.dao.account.mapper.MemberContactMapper;
import com.sankuai.shangou.seashop.user.dao.account.mapper.ext.MemberContactExtMapper;
import com.sankuai.shangou.seashop.user.dao.account.model.MemberContactExtModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @description: 商家仓库类
 * @author: LXH
 **/
@Repository
@Slf4j
public class MemberContactRepository extends ServiceImpl<MemberContactMapper, MemberContact> {

    @Resource
    private MemberContactExtMapper memberContactExtMapper;

    @Resource
    private MemberContactMapper memberContactMapper;

    public void deleteMemberContact(MemberContact memberContact) {
        QueryWrapper<MemberContact> memberContactQueryWrapper = new QueryWrapper<>();
        if (memberContact.getUserId() != null) {
            memberContactQueryWrapper.lambda().eq(MemberContact::getUserId, memberContact.getUserId());
        }
        if (memberContact.getServiceProvider() != null) {
            memberContactQueryWrapper.lambda().eq(MemberContact::getServiceProvider, memberContact.getServiceProvider());
        }
        baseMapper.delete(memberContactQueryWrapper);
    }

    public List<MemberContact> getMemberContactByLabelId(List<Long> longs,String serviceProvider) {
        return memberContactExtMapper.queryList(longs,serviceProvider);
    }

    public List<MemberContactExtModel> getMemberContact(List<Long> shopIds, List<Long> userIds) {
        return memberContactExtMapper.queryExtList(shopIds, userIds);
    }

    public List<MemberContact> queryMemberContact(MemberContact req){
        if(Objects.isNull(req)){
            return new ArrayList<>();
        }
        QueryWrapper<MemberContact> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(MemberContact::getUserId, req.getUserId());
        return memberContactMapper.selectList(queryWrapper);
    }

    public String getMemberContactById(Long id, String userType) {
        LambdaQueryWrapper<MemberContact> memberContactQueryWrapper = new LambdaQueryWrapper<>();
        memberContactQueryWrapper.eq(MemberContact::getUserId, id);
        memberContactQueryWrapper.eq(MemberContact::getServiceProvider, userType);
        MemberContact memberContact = baseMapper.selectOne(memberContactQueryWrapper);
        if (memberContact != null) {
            return memberContact.getContact();
        }else {
            return null;
        }
    }
}
