package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.meituan.servicecatalog.api.annotations.FieldDoc;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * 运费模板主表
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
@TableName("user_freight_template")
public class FreightTemplate implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 运费模板名称
     */
    @TableField("name")
    private String name;

    /**
     * 宝贝发货地
     */
    @TableField("source_address")
    private Integer sourceAddress;

    /**
     * 发送时间
     */
    @TableField("send_time")
    private String sendTime;

    /**
     * 是否商家负责运费
     */
    @TableField("whether_free")
    private Integer whetherFree;

    /**
     * 定价方法(按体积、重量计算）
     */
    @TableField("valuation_method")
    private Integer valuationMethod;

    /**
     * 运送类型（物流、快递）
     */
    @TableField("shipping_method")
    private Integer shippingMethod;

    public Boolean getNonSalesAreaProductHide() {
        return nonSalesAreaProductHide;
    }

    public void setNonSalesAreaProductHide(Boolean nonSalesAreaProductHide) {
        this.nonSalesAreaProductHide = nonSalesAreaProductHide;
    }

    /**
     * 店铺ID
     */
    @TableField("shop_id")
    private Long shopId;

    /**
     * 非销售区域是否隐藏
     */
    @TableField("non_sales_area_hide")
    private Boolean nonSalesAreaHide;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private Date updateTime;

    @TableField("non_sales_area_product_hide")
    private Boolean nonSalesAreaProductHide;
    @TableField(exist = false)
    private List<String> templateNames;



    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSourceAddress() {
        return sourceAddress;
    }

    public void setSourceAddress(Integer sourceAddress) {
        this.sourceAddress = sourceAddress;
    }

    public String getSendTime() {
        return sendTime;
    }

    public void setSendTime(String sendTime) {
        this.sendTime = sendTime;
    }

    public Integer getWhetherFree() {
        return whetherFree;
    }

    public void setWhetherFree(Integer whetherFree) {
        this.whetherFree = whetherFree;
    }

    public Integer getValuationMethod() {
        return valuationMethod;
    }

    public void setValuationMethod(Integer valuationMethod) {
        this.valuationMethod = valuationMethod;
    }

    public Integer getShippingMethod() {
        return shippingMethod;
    }

    public void setShippingMethod(Integer shippingMethod) {
        this.shippingMethod = shippingMethod;
    }

    public Long getShopId() {
        return shopId;
    }

    public void setShopId(Long shopId) {
        this.shopId = shopId;
    }

    public Boolean getNonSalesAreaHide() {
        return nonSalesAreaHide;
    }

    public void setNonSalesAreaHide(Boolean nonSalesAreaHide) {
        this.nonSalesAreaHide = nonSalesAreaHide;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public List<String> getTemplateNames() {
        return templateNames;
    }

    public void setTemplateNames(List<String> templateNames) {
        this.templateNames = templateNames;
    }

    @Override
    public String toString() {
        return "FreightTemplate{" +
        "id=" + id +
        ", name=" + name +
        ", sourceAddress=" + sourceAddress +
        ", sendTime=" + sendTime +
        ", whetherFree=" + whetherFree +
        ", valuationMethod=" + valuationMethod +
        ", shippingMethod=" + shippingMethod +
        ", shopId=" + shopId +
        ", nonSalesAreaHide=" + nonSalesAreaHide +
        ", createTime=" + createTime +
        ", updateTime=" + updateTime +
        "}";
    }

}
