package com.sankuai.shangou.seashop.user.dao.shop.mapper.ext;

import com.sankuai.shangou.seashop.user.dao.account.domain.Member;
import com.sankuai.shangou.seashop.user.dao.shop.model.ShopModel;
import com.sankuai.shangou.seashop.user.dao.shop.model.ShopQueryModel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface ShopManagerMapper {
    List<ShopModel> selectList(@Param("query") ShopQueryModel shopQueryDto);

    List<String> getCellPhoneList(List<Long> shopIds);

    List<Member> getShopIdsByUserIds(List<Long> userIds);

    List<Member> getOpenShopIdsByUserIds(List<Long> userIds);
}
