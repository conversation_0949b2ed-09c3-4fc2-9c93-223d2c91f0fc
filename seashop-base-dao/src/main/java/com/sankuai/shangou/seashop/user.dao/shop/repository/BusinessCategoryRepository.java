package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.base.boot.utils.MybatisUtil;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategory;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.BusinessCategoryMapper;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ext.BusinessCategoryExtMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 分类申请仓库类
 * @author: LXH
 **/
@Repository
public class BusinessCategoryRepository extends ServiceImpl<BusinessCategoryMapper, BusinessCategory> {
    @Resource
    private BusinessCategoryExtMapper businessCategoryExtMapper;

    public List<BusinessCategory> selectList(BusinessCategory category) {
        QueryWrapper<BusinessCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BusinessCategory::getShopId, category.getShopId())
                .orderByDesc(BusinessCategory::getId);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 查询店铺下拥有的经营类目的id集合
     *
     * @param shopId 店铺id
     * @return 类目id集合
     */
    public List<Long> listCategoryIdsByShopId(Long shopId) {
        QueryWrapper<BusinessCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BusinessCategory::getShopId, shopId);
        return baseMapper.selectList(queryWrapper).stream().map(BusinessCategory::getCategoryId).collect(Collectors.toList());
    }

    /**
     * 查询店铺下有效的经营类目的id集合(排除已经冻结的)
     *
     * @param shopId 店铺id
     * @return 类目id集合
     */
    public List<Long> listValidCategoryIdsByShopId(Long shopId) {
        QueryWrapper<BusinessCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BusinessCategory::getShopId, shopId);
        queryWrapper.lambda().eq(BusinessCategory::getWhetherFrozen, Boolean.FALSE);
        return baseMapper.selectList(queryWrapper).stream().map(BusinessCategory::getCategoryId).collect(Collectors.toList());
    }

    public List<BusinessCategory> listByShopId(Long shopId) {
        QueryWrapper<BusinessCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(BusinessCategory::getShopId, shopId);
        return baseMapper.selectList(queryWrapper);
    }

    public void freezeOrUnfreezeCategory(Long shopId, List<Long> categoryIds, Boolean freeze) {
        if (CollectionUtils.isEmpty(categoryIds)) {
            return;
        }

        BusinessCategory updBusinessCategory = new BusinessCategory();
        updBusinessCategory.setWhetherFrozen(freeze);
        MybatisUtil.executeBatch(ids -> {
            QueryWrapper<BusinessCategory> queryWrapper = new QueryWrapper<>();
            queryWrapper.lambda().eq(BusinessCategory::getShopId, shopId).in(BusinessCategory::getCategoryId, ids);
            baseMapper.update(updBusinessCategory, queryWrapper);
        }, categoryIds);
    }

    public List<Long> listWaitAuditCategoryIdsByShopId(Long shopId) {
        return baseMapper.listWaitAuditCategoryIdsByShopId(shopId);
    }

    public List<Long> listByCategory(List<Long> categoryIds) {
        return businessCategoryExtMapper.listByCategory(categoryIds);
    }

    public List<Long> listByShopAndCategory(Long shopId, List<Long> categoryIds) {
        return businessCategoryExtMapper.listByShopAndCategory(shopId, categoryIds);
    }
}
