package com.sankuai.shangou.seashop.user.dao.shop.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 店铺信息表
 * </p>
 *
 * <AUTHOR> @since 2024-04-21
 */
@Data
@TableName("user_shop")
public class Shop implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 店铺等级
     */
    @TableField("grade_id")
    private Long gradeId;

    /**
     * 店铺名称
     */
    @TableField("shop_name")
    private String shopName;

    /**
     * 店铺LOGO路径
     */
    @TableField("logo")
    private String logo;

    /**
     * 预留子域名，未使用
     */
    @TableField("sub_domains")
    private String subDomains;

    /**
     * 预留主题，未使用
     */
    @TableField("theme")
    private String theme;

    /**
     * 是否是官方自营店
     */
    @TableField("whether_self")
    private Boolean whetherSelf;

    /**
     * 店铺状态
     */
    @TableField("shop_status")
    private Integer shopStatus;

    /**
     * 审核拒绝原因
     */
    @TableField("refuse_reason")
    private String refuseReason;

    /**
     * 汇付操作状态
     */
    @TableField("adapay_status")
    private Integer adapayStatus;

    /**
     * 汇付创建失败原因
     */
    @TableField("adapay_reason")
    private String adapayReason;

    /**
     * 平台审核状态
     */
    @TableField("plate_status")
    private Integer plateStatus;

    @TableField("create_date")
    private Date createDate;

    /**
     * 联系人姓名
     */
    @TableField("contacts_name")
    private String contactsName;

    /**
     * 联系电话
     */
    @TableField("contacts_phone")
    private String contactsPhone;

    /**
     * 联系Email
     */
    @TableField("contacts_email")
    private String contactsEmail;

    /**
     * 一般纳税人证明
     */
    @TableField("general_taxpayer_phot")
    private String generalTaxpayerPhot;

    /**
     * 银行开户名
     */
    @TableField("bank_account_name")
    private String bankAccountName;

    /**
     * 公司银行账号
     */
    @TableField("bank_account_number")
    private String bankAccountNumber;

    /**
     * 开户银行支行名称
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 支行联行号
     */
    @TableField("bank_code")
    private String bankCode;

    /**
     * 开户银行所在地
     */
    @TableField("bank_region_id")
    private Integer bankRegionId;

    /**
     * 开户银行许可证照
     */
    @TableField("bank_photo")
    private String bankPhoto;

    /**
     * 开户银行类型（1对公，2对私）
     */
    @TableField("bank_type")
    private Integer bankType;

    /**
     * 税务登记证
     */
    @TableField("tax_registration_certificate")
    private String taxRegistrationCertificate;

    /**
     * 税务登记证号
     */
    @TableField("taxpayer_id")
    private String taxpayerId;

    /**
     * 纳税人识别号
     */
    @TableField("tax_registration_certificate_photo")
    private String taxRegistrationCertificatePhoto;

    /**
     * 支付凭证
     */
    @TableField("pay_photo")
    private String payPhoto;

    /**
     * 支付注释
     */
    @TableField("pay_remark")
    private String payRemark;

    /**
     * 商家发货人名称
     */
    @TableField("sender_name")
    private String senderName;

    /**
     * 商家发货人地址
     */
    @TableField("sender_address")
    private String senderAddress;

    /**
     * 商家发货人电话
     */
    @TableField("sender_phone")
    private String senderPhone;

    /**
     * 运费
     */
    @TableField("freight")
    private BigDecimal freight;

    /**
     * 多少钱开始免运费
     */
    @TableField("free_freight")
    private BigDecimal freeFreight;

    /**
     * 店铺运费-单笔订单实付金额门槛
     */
    @TableField("amount_freight_condition")
    private BigDecimal amountFreightCondition;

    /**
     * 店铺单笔订单实付金额满足条件后的运费
     */
    @TableField("amount_freight")
    private BigDecimal amountFreight;

    /**
     * 店铺运费-单笔订单商品数量门槛
     */
    @TableField("quantity_freight_condition")
    private Integer quantityFreightCondition;

    /**
     * 店铺单笔订单商品数量满足条件后的运费
     */
    @TableField("quantity_freight")
    private BigDecimal quantityFreight;

    /**
     * 单笔订单满减金额门槛
     */
    @TableField("money_off_condition")
    private BigDecimal moneyOffCondition;

    /**
     * 单笔订单满减金额
     */
    @TableField("money_off_fee")
    private BigDecimal moneyOffFee;

    /**
     * 是否叠加优惠
     */
    @TableField("money_off_overlay")
    private Boolean moneyOffOverlay;

    /**
     * 注册步骤
     */
    @TableField("stage")
    private Integer stage;

    /**
     * 商家发货人省市区
     */
    @TableField("sender_region_id")
    private Integer senderRegionId;

    /**
     * 商品证书
     */
    @TableField("product_cert")
    private String productCert;

    /**
     * 其他证书
     */
    @TableField("other_cert")
    private String otherCert;

    /**
     * 0、企业；1、个人
     */
    @TableField("business_type")
    private Integer businessType;

    /**
     * 身份证号
     */
    @TableField("id_card")
    private String idCard;

    /**
     * 身份证URL
     */
    @TableField("id_cardurl")
    private String idCardurl;

    /**
     * 身份证照片URL2
     */
    @TableField("id_cardurl2")
    private String idCardurl2;

    /**
     * 商家是否开启自动分配订单
     */
    @TableField("auto_allot_order")
    private Boolean autoAllotOrder;

    /**
     * 商家是否开启自动打印
     */
    @TableField("whether_auto_print")
    private Boolean whetherAutoPrint;

    /**
     * 打印张数
     */
    @TableField("print_count")
    private Integer printCount;

    /**
     * 是否开启头部图片广告
     */
    @TableField("whether_open_top_image_ad")
    private Boolean whetherOpenTopImageAd;

    /**
     * 是否注册了海商客服平台
     */
    @TableField("whether_open_hi_chat")
    private Boolean whetherOpenHiChat;

    /**
     * 是否缴纳保证金
     */
    @TableField("whether_pay_bond")
    private Boolean whetherPayBond;

    /**
     * 是否签署协议
     */
    @TableField("whether_agreement")
    private Boolean whetherAgreement;

    /**
     * 是否已发送短信提醒
     */
    @TableField("whether_sms_tips")
    private Boolean whetherSmsTips;

    /**
     * 签名照
     */
    @TableField("autograph_img")
    private String autographImg;

    /**
     * 身份证有效期类型
     */
    @TableField("id_card_expire_type")
    private Integer idCardExpireType;

    /**
     * 份证有效期开始日期
     */
    @TableField("id_card_start_date")
    private Date idCardStartDate;

    /**
     * 身份证有效期开始日期
     */
    @TableField("id_card_end_date")
    private Date idCardEndDate;

    /**
     * 执照地址
     */
    @TableField("licence_cert_addr")
    private String licenceCertAddr;

    /**
     * 币种
     */
    @TableField("currency_type")
    private String currencyType;

    /**
     * 服务商简介
     */
    @TableField("introduct")
    private String introduct;

    /**
     * 自定义数据
     */
    @TableField("form_data")
    private String formData;

    /**
     * 是否开启专属商家
     */
    @TableField("whether_open_exclusive_member")
    private Boolean whetherOpenExclusiveMember;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;

    /**
     * 订单支付是否短信通知
     */
    @TableField("order_pay_iz_send_sms")
    private Boolean orderPayIzSendSms;

    /**
     * 店铺类型 1商城店铺 2牵牛花店铺 3易久批
     */
    @TableField("shop_type")
    private Integer shopType;

    @TableField("id_card_date")
    private Date idCardDate;

    /**
     * 前端排序顺序
     */
    @TableField("serial_number")
    private Integer serialNumber;

    /**
     * 是否需要补充资料
     */
    @TableField("whether_supply")
    private Boolean whetherSupply;

    /**
     * 是否欠费保证金
     */
    @TableField("whether_arrear")
    private Boolean whetherArrear;

    @TableField("app_key")
    private String appKey;
    @TableField("app_secret")
    private String appSecret;

    @TableField("is_registed")
    private Boolean registed;

    @TableField("is_enable")
    private Boolean enable;
}
