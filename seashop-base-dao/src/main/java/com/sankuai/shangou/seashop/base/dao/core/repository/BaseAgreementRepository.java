package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseAgreement;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseAgreementExample;
import com.sankuai.shangou.seashop.base.dao.core.enums.AgreementStatus;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BaseAgreementMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class BaseAgreementRepository {

    @Resource
    private BaseAgreementMapper agreementMapper;

//    public Boolean save(BaseAgreement agreement) {
//        BaseAgreement oldAgreement = agreementMapper.selectByType(agreement.getAgreementType());
//        if (oldAgreement == null) {
//            agreementMapper.insert(agreement);
//        } else {
//            BaseAgreementExample example = new BaseAgreementExample();
//            BaseAgreementExample.Criteria criteria = example.createCriteria();
//            criteria.andAgreementTypeEqualTo(agreement.getAgreementType());
//            agreementMapper.updateByExampleSelective(agreement, example);
//        }
//        return true;
//    }
//
    public BaseAgreement get(int agreementType) {
        BaseAgreement agreement = agreementMapper.selectByType(agreementType);
        return agreement;
    }


    public List<BaseAgreement> getAll() {
        BaseAgreementExample example = new BaseAgreementExample();
        BaseAgreementExample.Criteria criteria = example.createCriteria();
        criteria.andStatusEqualTo(0);
        List<BaseAgreement> result = agreementMapper.selectByExampleWithBLOBs(example);
        return result;
    }

    public Boolean save(BaseAgreement agreement) {
        BaseAgreementExample example = new BaseAgreementExample();
        BaseAgreementExample.Criteria criteria = example.createCriteria();
        criteria.andAgreementTypeEqualTo(agreement.getAgreementType());
        //把旧的变为已过期的状态
        BaseAgreement old = new BaseAgreement();
        old.setStatus(AgreementStatus.Expire.getCode());
        agreementMapper.updateByExampleSelective(old, example);
        agreementMapper.insert(agreement);
        return true;
    }
}
