package com.sankuai.shangou.seashop.user.dao.shop.mapper;

import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
import com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 供应商经营分类信息 Mapper 接口
 * </p>
 *
 * <AUTHOR> @since 2023-11-27
 */
public interface BusinessCategoryMapper extends EnhancedMapper<BusinessCategory> {

    List<Long> listWaitAuditCategoryIdsByShopId(@Param("shopId") Long shopId);

}
