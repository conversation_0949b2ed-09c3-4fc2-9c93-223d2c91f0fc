package com.sankuai.shangou.seashop.base.dao.core.mapper;

import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField;
import com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormFieldExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface BaseCustomFormFieldMapper {
    long countByExample(BaseCustomFormFieldExample example);

    int deleteByExample(BaseCustomFormFieldExample example);

    int deleteByPrimaryKey(Long id);

    int insert(BaseCustomFormField record);

    int insertSelective(BaseCustomFormField record);

    List<BaseCustomFormField> selectByExample(BaseCustomFormFieldExample example);

    BaseCustomFormField selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BaseCustomFormField record, @Param("example") BaseCustomFormFieldExample example);

    int updateByExample(@Param("record") BaseCustomFormField record, @Param("example") BaseCustomFormFieldExample example);

    int updateByPrimaryKeySelective(BaseCustomFormField record);

    int updateByPrimaryKey(BaseCustomFormField record);

    int deleteByFormId(Long id);

    int deleteByFormIdNotInIds(@Param("formId") Long formId, @Param("ids") List<Long> ids);
}