package com.sankuai.shangou.seashop.user.dao.account.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.account.domain.ShippingAddress;
import com.sankuai.shangou.seashop.user.dao.account.mapper.ShippingAddressMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户收货地址仓库
 * <AUTHOR>
 */
@Repository
public class ShippingAddressRepository extends ServiceImpl<ShippingAddressMapper, ShippingAddress> {

    public ShippingAddress selectById(Long id){
        return baseMapper.selectById(id);
    }
    /**
     * 根据用户ID获取收货地址列表
     * <AUTHOR>
     * @param userId 用户ID
     */
    public List<ShippingAddress> getByUserIdList(Long userId) {
        LambdaQueryWrapper<ShippingAddress> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShippingAddress::getUserId, userId);
        queryWrapper.orderByDesc(ShippingAddress::getWhetherDefault);
        return baseMapper.selectList(queryWrapper);
    }

    /**
     * 清除用户已有的默认地址
     * <AUTHOR>
     * @param userId 用户ID
     * int 修改的数据行数
     */
    public int clearIzDefault(Long userId) {
        ShippingAddress address = new ShippingAddress();
        address.setWhetherDefault(false);
        LambdaUpdateWrapper<ShippingAddress> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ShippingAddress::getUserId, userId);
        return baseMapper.update(address, updateWrapper);
    }

}
