package com.sankuai.shangou.seashop.user.dao.account.mapper;//package com.sankuai.shangou.seashop.user.dao.account.mapper;
//
//import java.util.List;
//import java.util.Set;
//
//import org.apache.ibatis.annotations.Param;
//
//import com.meituan.xframe.boot.mybatisplus.autoconfigure.mapper.EnhancedMapper;
//import com.sankuai.shangou.seashop.user.dao.account.domain.UserExclusiveMember;
//import com.sankuai.shangou.seashop.user.dao.account.model.CmdUserShopModel;
//import com.sankuai.shangou.seashop.user.dao.account.model.QueryExclusiveMemberModel;
//import com.sankuai.shangou.seashop.user.dao.account.model.UserExclusiveMemberExt;
//import com.sankuai.shangou.seashop.user.dao.account.model.UserMemberModel;
//
///**
// * @description: 专属商家
// * @author: liweisong
// **/
//public interface UserExclusiveMemberMapper extends EnhancedMapper<UserExclusiveMember> {
//
//    Boolean queryIzOpenExclusiveMember(@Param("shopId") Long shopId);
//
//    List<UserExclusiveMemberExt> queryExclusiveList(@Param("req") QueryExclusiveMemberModel query);
//
//    List<UserMemberModel> queryMemberByNames(@Param("userNames") List<String> userNames);
//
//    void openUserShop(@Param("req") CmdUserShopModel req);
//
//    Set<Long> queryMatchUserExclusiveShops(@Param("userId") Long userId, @Param("shopIds") List<Long> shopIds);
//
//
//}
