package com.sankuai.shangou.seashop.base.dao.core.repository;

import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace;
import com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceExample;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BasePhotoSpaceCategoryMapper;
import com.sankuai.shangou.seashop.base.dao.core.mapper.BasePhotoSpaceMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

@Component
@Slf4j
public class BasePhotoSpaceRepository {
    @Resource
    private BasePhotoSpaceMapper photoSpaceMapper;

    @Resource
    private BasePhotoSpaceCategoryMapper basePhotoSpaceCategoryMapper;


    public Long create(BasePhotoSpace photoSpace) {
        return (long) photoSpaceMapper.insert(photoSpace);
    }

    public void delete(long id, long shopId) {
        BasePhotoSpaceExample example = new BasePhotoSpaceExample();
        BasePhotoSpaceExample.Criteria criteria = example.createCriteria();
        criteria.andShopIdEqualTo(shopId);
        criteria.andIdEqualTo(id);
        photoSpaceMapper.deleteByExample(example);
        return;
    }


    public List<BasePhotoSpace> query(BasePhotoSpaceExample example) {
        return photoSpaceMapper.selectByExample(example);
    }

    public boolean update(BasePhotoSpace photoSpace, BasePhotoSpaceExample example) {
        photoSpaceMapper.updateByExampleSelective(photoSpace, example);
        return true;
    }

    public boolean setNoGroup(Long cateId, long shopId) {
        BasePhotoSpaceExample example = new BasePhotoSpaceExample();
        BasePhotoSpaceExample.Criteria criteria = example.createCriteria();
        criteria.andPhotoCategoryIdEqualTo(cateId);
        criteria.andShopIdEqualTo(shopId);
        BasePhotoSpace basePhotoSpace = new BasePhotoSpace();
        basePhotoSpace.setPhotoCategoryId(0L);
        photoSpaceMapper.updateByExampleSelective(basePhotoSpace, example);
        return true;
    }

    public boolean setNoGroup(List<Long> cateIds, long shopId) {
        BasePhotoSpaceExample example = new BasePhotoSpaceExample();
        BasePhotoSpaceExample.Criteria criteria = example.createCriteria();
        criteria.andPhotoCategoryIdIn(cateIds);
        criteria.andShopIdEqualTo(shopId);
        BasePhotoSpace basePhotoSpace = new BasePhotoSpace();
        basePhotoSpace.setPhotoCategoryId(0L);
        photoSpaceMapper.updateByExampleSelective(basePhotoSpace, example);
        return true;
    }

    public Boolean deletes(BasePhotoSpaceExample example) {
        photoSpaceMapper.deleteByExample(example);
        return true;
    }

    public int getCountByExample(BasePhotoSpaceExample example) {
        return (int) photoSpaceMapper.countByExample(example);
    }
}
