package com.sankuai.shangou.seashop.user.dao.shop.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.sankuai.shangou.seashop.user.dao.shop.domain.ShopFreeShippingArea;
import com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopFreeShippingAreaMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: lhx
 * @date: 2023/11/15/015
 * @description:
 */
@Repository
@Slf4j
public class ShopFreeShippingAreaRepository extends ServiceImpl<ShopFreeShippingAreaMapper, ShopFreeShippingArea> {

    public void deleteByShopId(Long shopId) {
        LambdaQueryWrapper<ShopFreeShippingArea> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopFreeShippingArea::getShopId, shopId);
        baseMapper.delete(queryWrapper);
    }

    public List<ShopFreeShippingArea> listByShopId(Long shopId) {
        LambdaQueryWrapper<ShopFreeShippingArea> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ShopFreeShippingArea::getShopId, shopId);
        queryWrapper.orderByAsc(ShopFreeShippingArea::getId);
        return baseMapper.selectList(queryWrapper);
    }

    public List<ShopFreeShippingArea> getByShopIdList(List<Long> shopIdList) {
        LambdaQueryWrapper<ShopFreeShippingArea> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ShopFreeShippingArea::getShopId, shopIdList);
        return baseMapper.selectList(queryWrapper);
    }
}
