package com.sankuai.shangou.seashop.base.dao.core.model;

import lombok.Data;

import java.util.Date;

/**
 * @author： liweisong
 * @create： 2023/12/1 14:22
 */
@Data
public class BaseOperationLogModel {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 店铺ID
     */
    private Long shopId;

    /**
     * 操作人
     */
    private String userName;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 操作名称
     */
    private String operationName;

    /**
     * 业务板块
     */
    private Integer moduleId;

    /**
     * 具体的操作功能
     */
    private String actionName;

    /**
     * 操作内容
     */
    private String operationContent;
}
