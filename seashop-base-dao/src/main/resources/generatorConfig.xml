<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="MybatisGenerator" targetRuntime="MyBatis3">

        <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>

        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="suppressAllComments" value="true"/>
            <property name="javaFileEncoding" value="UTF-8"/>
        </commentGenerator>

        <!--替换成自己数据库的jdbcRef -->
        <zebra jdbcRef="shangousgb2bseashop_shangou_sgb2b_seashop_base_test"/>

        <javaModelGenerator targetPackage="com.sankuai.shangou.seashop.base.dao.core.domain"
                            targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <javaClientGenerator type="XMLMAPPER" targetPackage="com.sankuai.shangou.seashop.base.dao.core.mapper"
                             targetProject="src/main/java">
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>


        <!--     <table tableName="base_photo_space" domainObjectName="BasePhotoSpace">-->
        <!--         <property name="useActualColumnNames" value="false"/>-->
        <!--        <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--     </table>-->

        <!--      <table tableName="base_photo_space_category" domainObjectName="BasePhotoSpaceCategory">-->
        <!--          <property name="useActualColumnNames" value="false"/>-->
        <!--          <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--      </table>-->

<!--              <table tableName="base_settled" domainObjectName="BaseSettled">-->
<!--                  <property name="useActualColumnNames" value="false"/>-->
<!--                  <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--              </table>-->

        <!--      <table tableName="base_topic" domainObjectName="BaseTopic">-->
        <!--          <property name="useActualColumnNames" value="false"/>-->
        <!--          <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--      </table>-->

        <!--      <table tableName="base_topic_module" domainObjectName="BaseTopicModule">-->
        <!--          <property name="useActualColumnNames" value="false"/>-->
        <!--          <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--      </table>-->

        <!--      <table tableName="base_module_product" domainObjectName="BaseModuleProduct">-->
        <!--          <property name="useActualColumnNames" value="false"/>-->
        <!--          <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--      </table>-->
        <!--      <table tableName="base_site_setting" domainObjectName="BaseSiteSetting">-->
        <!--          <property name="useActualColumnNames" value="false"/>-->
        <!--          <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--      </table>-->

<!--              <table tableName="base_agreement" domainObjectName="BaseAgreement">-->
<!--                  <property name="useActualColumnNames" value="false"/>-->
<!--                  <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--              </table>-->

<!--              <table tableName="base_custom_form" domainObjectName="BaseCustomForm">-->
<!--                  <property name="useActualColumnNames" value="false"/>-->
<!--                  <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--              </table>-->

        <!--      <table tableName="base_custom_form_field" domainObjectName="BaseCustomFormField">-->
        <!--          <property name="useActualColumnNames" value="false"/>-->
        <!--          <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--      </table>-->

        <!--        <table tableName="base_article_category" domainObjectName="BaseArticleCategory">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
        <!--        <table tableName="base_article" domainObjectName="BaseArticle">-->
        <!--            <property name="useActualColumnNames" value="false"/>-->
        <!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
        <!--        </table>-->
<!--        <table tableName="base_region" domainObjectName="BaseRegion">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="false"/>-->
<!--        </table>-->
<!--        <table tableName="base_message_notice_setting" domainObjectName="BaseMessageNoticeSetting">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="message_type" sqlStatement="MySql" identity="false"/>-->
<!--        </table>-->
<!--                <table tableName="base_risk" domainObjectName="BaseRisk">-->
<!--                    <property name="useActualColumnNames" value="false"/>-->
<!--                    <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--                </table>-->
<!--        <table tableName="base_risk_content" domainObjectName="BaseRiskContent">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>-->
<!--        <table tableName="base_risk_response" domainObjectName="BaseRiskResponse">-->
<!--            <property name="useActualColumnNames" value="false"/>-->
<!--            <generatedKey column="id" sqlStatement="MySql" identity="true"/>-->
<!--        </table>&lt;!&ndash;&ndash;&gt;-->

        <table tableName="base_template_page" domainObjectName="BaseTemplatePage">
                          <property name="useActualColumnNames" value="false"/>
                          <generatedKey column="id" sqlStatement="MySql" identity="true"/>
                      </table>

    </context>
</generatorConfiguration>
