<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.SlideAdMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.SlideAd">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="image_url" property="imageUrl" />
        <result column="url" property="url" />
        <result column="display_sequence" property="displaySequence" />
        <result column="type_id" property="typeId" />
        <result column="description" property="description" />
        <result column="del_flag" property="delFlag" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, image_url, url, display_sequence, type_id, description, del_flag, create_time, update_time
    </sql>

</mapper>
