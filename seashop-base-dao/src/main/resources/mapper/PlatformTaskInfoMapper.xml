<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.PlatformTaskInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.PlatformTaskInfo">
        <id column="id" property="id" />
        <result column="biz_type" property="bizType" />
        <result column="task_type" property="taskType" />
        <result column="task_name" property="taskName" />
        <result column="task_status" property="taskStatus" />
        <result column="begin_time" property="beginTime" />
        <result column="end_time" property="endTime" />
        <result column="cost" property="cost" />
        <result column="total_num" property="totalNum" />
        <result column="success_num" property="successNum" />
        <result column="failed_num" property="failedNum" />
        <result column="execute_param" property="executeParam" />
        <result column="execute_result" property="executeResult" />
        <result column="operator_id" property="operatorId" />
        <result column="operator_account" property="operatorAccount" />
        <result column="retry_times" property="retryTimes" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="env" property="env" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, biz_type, task_type, task_name, task_status, begin_time, end_time, cost, total_num, success_num, failed_num, execute_param, execute_result, operator_id, operator_account, retry_times, create_time, update_time, env
    </sql>

</mapper>
