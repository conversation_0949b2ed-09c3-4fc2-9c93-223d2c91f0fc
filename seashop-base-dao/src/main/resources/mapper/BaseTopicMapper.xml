<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseTopicMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="front_cover_image" jdbcType="VARCHAR" property="frontCoverImage" />
    <result column="top_image" jdbcType="VARCHAR" property="topImage" />
    <result column="background_image" jdbcType="VARCHAR" property="backgroundImage" />
    <result column="plat_form" jdbcType="INTEGER" property="platForm" />
    <result column="tags" jdbcType="VARCHAR" property="tags" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="is_recommend" jdbcType="BIT" property="isRecommend" />
    <result column="home" jdbcType="BIT" property="home" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic">
    <result column="self_defineText" jdbcType="LONGVARCHAR" property="selfDefinetext" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, front_cover_image, top_image, background_image, plat_form, tags, shop_id, 
    is_recommend,home,create_time,modify_time
  </sql>
  <sql id="Blob_Column_List">
    self_defineText
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from base_topic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from base_topic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from base_topic
    where id = #{id,jdbcType=BIGINT}
  </select>

  <select id="selectByShopId"  resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from base_topic
    where id = #{id,jdbcType=BIGINT} and shop_id=#{shopId,jdbcType=BIGINT}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from base_topic
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicExample">
    delete from base_topic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_topic (name, front_cover_image, top_image, 
      background_image, plat_form, tags, 
      shop_id, is_recommend, self_defineText,home,create_time,modify_time
      )
    values (#{name,jdbcType=VARCHAR}, #{frontCoverImage,jdbcType=VARCHAR}, #{topImage,jdbcType=VARCHAR}, 
      #{backgroundImage,jdbcType=VARCHAR}, #{platForm,jdbcType=INTEGER}, #{tags,jdbcType=VARCHAR}, 
      #{shopId,jdbcType=BIGINT}, #{isRecommend,jdbcType=BIT}, #{selfDefinetext,jdbcType=LONGVARCHAR},
      #{home,jdbcType=BIT},#{createTime,jdbcType=TIMESTAMP},#{modifyTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_topic
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="name != null">
        name,
      </if>
      <if test="frontCoverImage != null">
        front_cover_image,
      </if>
      <if test="topImage != null">
        top_image,
      </if>
      <if test="backgroundImage != null">
        background_image,
      </if>
      <if test="platForm != null">
        plat_form,
      </if>
      <if test="tags != null">
        tags,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="isRecommend != null">
        is_recommend,
      </if>
      <if test="selfDefinetext != null">
        self_defineText,
      </if>
      <if test="home != null">
        home,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="frontCoverImage != null">
        #{frontCoverImage,jdbcType=VARCHAR},
      </if>
      <if test="topImage != null">
        #{topImage,jdbcType=VARCHAR},
      </if>
      <if test="backgroundImage != null">
        #{backgroundImage,jdbcType=VARCHAR},
      </if>
      <if test="platForm != null">
        #{platForm,jdbcType=INTEGER},
      </if>
      <if test="tags != null">
        #{tags,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="isRecommend != null">
        #{isRecommend,jdbcType=BIT},
      </if>
      <if test="selfDefinetext != null">
        #{selfDefinetext,jdbcType=LONGVARCHAR},
      </if>
      <if test="home != null">
        #{home,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopicExample" resultType="java.lang.Long">
    select count(*) from base_topic
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update base_topic
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.frontCoverImage != null">
        front_cover_image = #{record.frontCoverImage,jdbcType=VARCHAR},
      </if>
      <if test="record.topImage != null">
        top_image = #{record.topImage,jdbcType=VARCHAR},
      </if>
      <if test="record.backgroundImage != null">
        background_image = #{record.backgroundImage,jdbcType=VARCHAR},
      </if>
      <if test="record.platForm != null">
        plat_form = #{record.platForm,jdbcType=INTEGER},
      </if>
      <if test="record.tags != null">
        tags = #{record.tags,jdbcType=VARCHAR},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.isRecommend != null">
        is_recommend = #{record.isRecommend,jdbcType=BIT},
      </if>
      <if test="record.selfDefinetext != null">
        self_defineText = #{record.selfDefinetext,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.home != null">
        home = #{record.home,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update base_topic
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      front_cover_image = #{record.frontCoverImage,jdbcType=VARCHAR},
      top_image = #{record.topImage,jdbcType=VARCHAR},
      background_image = #{record.backgroundImage,jdbcType=VARCHAR},
      plat_form = #{record.platForm,jdbcType=INTEGER},
      tags = #{record.tags,jdbcType=VARCHAR},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      is_recommend = #{record.isRecommend,jdbcType=BIT},
      self_defineText = #{record.selfDefinetext,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update base_topic
    set id = #{record.id,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      front_cover_image = #{record.frontCoverImage,jdbcType=VARCHAR},
      top_image = #{record.topImage,jdbcType=VARCHAR},
      background_image = #{record.backgroundImage,jdbcType=VARCHAR},
      plat_form = #{record.platForm,jdbcType=INTEGER},
      tags = #{record.tags,jdbcType=VARCHAR},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      is_recommend = #{record.isRecommend,jdbcType=BIT},
    modify_time = #{record.modifyTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic">
    update base_topic
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="frontCoverImage != null">
        front_cover_image = #{frontCoverImage,jdbcType=VARCHAR},
      </if>
      <if test="topImage != null">
        top_image = #{topImage,jdbcType=VARCHAR},
      </if>
      <if test="backgroundImage != null">
        background_image = #{backgroundImage,jdbcType=VARCHAR},
      </if>
      <if test="platForm != null">
        plat_form = #{platForm,jdbcType=INTEGER},
      </if>
      <if test="tags != null">
        tags = #{tags,jdbcType=VARCHAR},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="isRecommend != null">
        is_recommend = #{isRecommend,jdbcType=BIT},
      </if>
      <if test="selfDefinetext != null">
        self_defineText = #{selfDefinetext,jdbcType=LONGVARCHAR},
      </if>
      <if test="home != null">
        home = #{home,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic">
    update base_topic
    set name = #{name,jdbcType=VARCHAR},
      front_cover_image = #{frontCoverImage,jdbcType=VARCHAR},
      top_image = #{topImage,jdbcType=VARCHAR},
      background_image = #{backgroundImage,jdbcType=VARCHAR},
      plat_form = #{platForm,jdbcType=INTEGER},
      tags = #{tags,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=BIGINT},
      is_recommend = #{isRecommend,jdbcType=BIT},
      self_defineText = #{selfDefinetext,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTopic">
    update base_topic
    set name = #{name,jdbcType=VARCHAR},
      front_cover_image = #{frontCoverImage,jdbcType=VARCHAR},
      top_image = #{topImage,jdbcType=VARCHAR},
      background_image = #{backgroundImage,jdbcType=VARCHAR},
      plat_form = #{platForm,jdbcType=INTEGER},
      tags = #{tags,jdbcType=VARCHAR},
      shop_id = #{shopId,jdbcType=BIGINT},
      is_recommend = #{isRecommend,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>