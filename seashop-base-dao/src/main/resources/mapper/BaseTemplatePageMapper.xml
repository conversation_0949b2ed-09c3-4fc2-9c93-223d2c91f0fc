<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseTemplatePageMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePage">
    <id column="shop_id" jdbcType="BIGINT" property="shopId" />
    <id column="v_shop_id" jdbcType="BIGINT" property="vShopId" />
    <id column="type" jdbcType="INTEGER" property="type" />
    <id column="client" jdbcType="VARCHAR" property="client" />
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="add_time" jdbcType="TIMESTAMP" property="addTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs">
    <result column="l_modules" jdbcType="LONGVARCHAR" property="lModules" />
    <result column="p_modules" jdbcType="LONGVARCHAR" property="pModules" />
    <result column="j_page" jdbcType="LONGVARCHAR" property="jPage" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    shop_id, v_shop_id, type, client, id, title, add_time, update_time
  </sql>
  <sql id="Blob_Column_List">
    l_modules, p_modules, j_page
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from base_template_page
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from base_template_page
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageKey" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from base_template_page
    where shop_id = #{shopId,jdbcType=BIGINT}
      and type = #{type,jdbcType=INTEGER}
      and client = #{client,jdbcType=VARCHAR}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageKey">
    delete from base_template_page
    where shop_id = #{shopId,jdbcType=BIGINT}
      and v_shop_id = #{vShopId,jdbcType=BIGINT}
      and type = #{type,jdbcType=INTEGER}
      and client = #{client,jdbcType=VARCHAR}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageExample">
    delete from base_template_page
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_template_page (shop_id, v_shop_id, type, 
      client, title, add_time, 
      update_time, l_modules, p_modules, 
      j_page)
    values (#{shopId,jdbcType=BIGINT}, #{vShopId,jdbcType=BIGINT}, #{type,jdbcType=INTEGER}, 
      #{client,jdbcType=VARCHAR}, #{title,jdbcType=VARCHAR}, #{addTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{lModules,jdbcType=LONGVARCHAR}, #{pModules,jdbcType=LONGVARCHAR}, 
      #{jPage,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_template_page
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="vShopId != null">
        v_shop_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="client != null">
        client,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="addTime != null">
        add_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="lModules != null">
        l_modules,
      </if>
      <if test="pModules != null">
        p_modules,
      </if>
      <if test="jPage != null">
        j_page,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="vShopId != null">
        #{vShopId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="client != null">
        #{client,jdbcType=VARCHAR},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lModules != null">
        #{lModules,jdbcType=LONGVARCHAR},
      </if>
      <if test="pModules != null">
        #{pModules,jdbcType=LONGVARCHAR},
      </if>
      <if test="jPage != null">
        #{jPage,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageExample" resultType="java.lang.Long">
    select count(*) from base_template_page
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update base_template_page
    <set>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.vShopId != null">
        v_shop_id = #{record.vShopId,jdbcType=BIGINT},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.client != null">
        client = #{record.client,jdbcType=VARCHAR},
      </if>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.addTime != null">
        add_time = #{record.addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lModules != null">
        l_modules = #{record.lModules,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.pModules != null">
        p_modules = #{record.pModules,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.jPage != null">
        j_page = #{record.jPage,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update base_template_page
    set shop_id = #{record.shopId,jdbcType=BIGINT},
      v_shop_id = #{record.vShopId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      client = #{record.client,jdbcType=VARCHAR},
      id = #{record.id,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      l_modules = #{record.lModules,jdbcType=LONGVARCHAR},
      p_modules = #{record.pModules,jdbcType=LONGVARCHAR},
      j_page = #{record.jPage,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update base_template_page
    set shop_id = #{record.shopId,jdbcType=BIGINT},
      v_shop_id = #{record.vShopId,jdbcType=BIGINT},
      type = #{record.type,jdbcType=INTEGER},
      client = #{record.client,jdbcType=VARCHAR},
      id = #{record.id,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      add_time = #{record.addTime,jdbcType=TIMESTAMP},
      update_time = #{record.updateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs">
    update base_template_page
    <set>
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="addTime != null">
        add_time = #{addTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lModules != null">
        l_modules = #{lModules,jdbcType=LONGVARCHAR},
      </if>
      <if test="pModules != null">
        p_modules = #{pModules,jdbcType=LONGVARCHAR},
      </if>
      <if test="jPage != null">
        j_page = #{jPage,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where shop_id = #{shopId,jdbcType=BIGINT}
      and v_shop_id = #{vShopId,jdbcType=BIGINT}
      and type = #{type,jdbcType=INTEGER}
      and client = #{client,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePageWithBLOBs">
    update base_template_page
    set id = #{id,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      l_modules = #{lModules,jdbcType=LONGVARCHAR},
      p_modules = #{pModules,jdbcType=LONGVARCHAR},
      j_page = #{jPage,jdbcType=LONGVARCHAR}
    where shop_id = #{shopId,jdbcType=BIGINT}
      and v_shop_id = #{vShopId,jdbcType=BIGINT}
      and type = #{type,jdbcType=INTEGER}
      and client = #{client,jdbcType=VARCHAR}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseTemplatePage">
    update base_template_page
    set id = #{id,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      add_time = #{addTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where shop_id = #{shopId,jdbcType=BIGINT}
      and v_shop_id = #{vShopId,jdbcType=BIGINT}
      and type = #{type,jdbcType=INTEGER}
      and client = #{client,jdbcType=VARCHAR}
  </update>
</mapper>