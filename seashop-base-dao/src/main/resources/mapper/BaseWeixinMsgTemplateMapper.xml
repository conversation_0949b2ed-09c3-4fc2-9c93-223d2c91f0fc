<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseWeixinMsgTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseWeixinMsgTemplate">
        <id column="id" property="id" />
        <result column="message_type" property="messageType" />
        <result column="template_num" property="templateNum" />
        <result column="template_id" property="templateId" />
        <result column="open_flag" property="openFlag" />
        <result column="user_in_wx_applet" property="userInWxApplet" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_type, template_num, template_id, open_flag, user_in_wx_applet, create_time, update_time
    </sql>

</mapper>
