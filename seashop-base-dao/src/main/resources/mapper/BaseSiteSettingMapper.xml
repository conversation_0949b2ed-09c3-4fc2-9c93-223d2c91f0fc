<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSiteSettingMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="key" jdbcType="VARCHAR" property="key" />
    <result column="value" jdbcType="VARCHAR" property="value" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, `key`, `value`
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSettingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from base_site_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from base_site_setting
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from base_site_setting
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSettingExample">
    delete from base_site_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_site_setting (`key`, `value`)
    values (#{key,jdbcType=VARCHAR}, #{value,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_site_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="`key` != null">
        `key`,
      </if>
      <if test="value != null">
        `value`,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="`key` != null">
        #{key,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        #{value,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSettingExample" resultType="java.lang.Long">
    select count(*) from base_site_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update base_site_setting
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.key != null">
        `key` = #{record.key,jdbcType=VARCHAR},
      </if>
      <if test="record.value != null">
        `value` = #{record.value,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update base_site_setting
    set id = #{record.id,jdbcType=BIGINT},
      `key` = #{record.key,jdbcType=VARCHAR},
      `value` = #{record.value,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting">
    update base_site_setting
    <set>
      <if test="`key` != null">
        `key` = #{key,jdbcType=VARCHAR},
      </if>
      <if test="value != null">
        `value` = #{value,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting">
    update base_site_setting
    set `key` = #{key,jdbcType=VARCHAR},
      value = #{value,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="updateSettingsByKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting">
        update base_site_setting set value = #{req.value} where `key` = #{req.key}
  </select>
  
  <select id="querySettingsValueByKey" parameterType="java.lang.String" resultType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSiteSetting">
        select id,`key`,`value` from base_site_setting where `key` = #{key}
  </select>
</mapper>