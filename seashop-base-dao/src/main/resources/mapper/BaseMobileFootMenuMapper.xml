<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseMobileFootMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseMobileFootMenu">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="url" property="url" />
        <result column="menu_icon" property="menuIcon" />
        <result column="menu_icon_sel" property="menuIconSel" />
        <result column="type" property="type" />
        <result column="shop_id" property="shopId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, url, menu_icon, menu_icon_sel, type, shop_id, create_time, update_time
    </sql>

</mapper>
