<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseCustomFormFieldMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="form_id" jdbcType="BIGINT" property="formId" />
    <result column="field_name" jdbcType="VARCHAR" property="fieldName" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="format" jdbcType="INTEGER" property="format" />
    <result column="option" jdbcType="VARCHAR" property="option" />
    <result column="is_required" jdbcType="BIT" property="isRequired" />
    <result column="display_sequence" jdbcType="BIGINT" property="displaySequence" />
    <result column="added_date" jdbcType="TIMESTAMP" property="addedDate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, form_id, field_name, type, `format`, `option`, is_required, display_sequence, added_date
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormFieldExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from base_custom_form_field
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from base_custom_form_field
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from base_custom_form_field
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormFieldExample">
    delete from base_custom_form_field
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_custom_form_field (form_id, field_name, type, 
      `format`, `option`, is_required,
      display_sequence, added_date)
    values (#{formId,jdbcType=BIGINT}, #{fieldName,jdbcType=VARCHAR}, #{type,jdbcType=INTEGER}, 
      #{format,jdbcType=INTEGER}, #{option,jdbcType=VARCHAR}, #{isRequired,jdbcType=BIT}, 
      #{displaySequence,jdbcType=BIGINT}, #{addedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_custom_form_field
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="formId != null">
        form_id,
      </if>
      <if test="fieldName != null">
        field_name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="format != null">
        `format`,
      </if>
      <if test="option != null">
        `option`,
      </if>
      <if test="isRequired != null">
        is_required,
      </if>
      <if test="displaySequence != null">
        display_sequence,
      </if>
      <if test="addedDate != null">
        added_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="formId != null">
        #{formId,jdbcType=BIGINT},
      </if>
      <if test="fieldName != null">
        #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=INTEGER},
      </if>
      <if test="format != null">
        #{format,jdbcType=INTEGER},
      </if>
      <if test="option != null">
        #{option,jdbcType=VARCHAR},
      </if>
      <if test="isRequired != null">
        #{isRequired,jdbcType=BIT},
      </if>
      <if test="displaySequence != null">
        #{displaySequence,jdbcType=BIGINT},
      </if>
      <if test="addedDate != null">
        #{addedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormFieldExample" resultType="java.lang.Long">
    select count(*) from base_custom_form_field
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update base_custom_form_field
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.formId != null">
        form_id = #{record.formId,jdbcType=BIGINT},
      </if>
      <if test="record.fieldName != null">
        field_name = #{record.fieldName,jdbcType=VARCHAR},
      </if>
      <if test="record.type != null">
        type = #{record.type,jdbcType=INTEGER},
      </if>
      <if test="record.format != null">
        `format` = #{record.format,jdbcType=INTEGER},
      </if>
      <if test="record.option != null">
        `option` = #{record.option,jdbcType=VARCHAR},
      </if>
      <if test="record.isRequired != null">
        is_required = #{record.isRequired,jdbcType=BIT},
      </if>
      <if test="record.displaySequence != null">
        display_sequence = #{record.displaySequence,jdbcType=BIGINT},
      </if>
      <if test="record.addedDate != null">
        added_date = #{record.addedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update base_custom_form_field
    set id = #{record.id,jdbcType=BIGINT},
      form_id = #{record.formId,jdbcType=BIGINT},
      field_name = #{record.fieldName,jdbcType=VARCHAR},
      type = #{record.type,jdbcType=INTEGER},
      `format` = #{record.format,jdbcType=INTEGER},
      `option` = #{record.option,jdbcType=VARCHAR},
      is_required = #{record.isRequired,jdbcType=BIT},
      display_sequence = #{record.displaySequence,jdbcType=BIGINT},
      added_date = #{record.addedDate,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField">
    update base_custom_form_field
    <set>
      <if test="formId != null">
        form_id = #{formId,jdbcType=BIGINT},
      </if>
      <if test="fieldName != null">
        field_name = #{fieldName,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=INTEGER},
      </if>
      <if test="format != null">
        `format` = #{format,jdbcType=INTEGER},
      </if>
      <if test="option != null">
        `option` = #{option,jdbcType=VARCHAR},
      </if>
      <if test="isRequired != null">
        is_required = #{isRequired,jdbcType=BIT},
      </if>
      <if test="displaySequence != null">
        display_sequence = #{displaySequence,jdbcType=BIGINT},
      </if>
      <if test="addedDate != null">
        added_date = #{addedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseCustomFormField">
    update base_custom_form_field
    set form_id = #{formId,jdbcType=BIGINT},
      field_name = #{fieldName,jdbcType=VARCHAR},
      type = #{type,jdbcType=INTEGER},
      `format` = #{format,jdbcType=INTEGER},
      `option` = #{option,jdbcType=VARCHAR},
      is_required = #{isRequired,jdbcType=BIT},
      display_sequence = #{displaySequence,jdbcType=BIGINT},
      added_date = #{addedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <delete id="deleteByFormId" parameterType="java.lang.Long">
    delete from base_custom_form_field
    where form_id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByFormIdNotInIds" parameterType="java.lang.Long">
    delete from base_custom_form_field
    where form_id = #{formId,jdbcType=BIGINT}
    and id not in
    <foreach close=")" collection="ids" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </delete>
</mapper>