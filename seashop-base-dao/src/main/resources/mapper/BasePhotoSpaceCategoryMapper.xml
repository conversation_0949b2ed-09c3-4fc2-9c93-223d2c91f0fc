<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BasePhotoSpaceCategoryMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="photo_space_catrgory_name" jdbcType="VARCHAR" property="photoSpaceCatrgoryName" />
    <result column="displayS_sequence" jdbcType="BIGINT" property="displaysSequence" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, shop_id, photo_space_catrgory_name, displayS_sequence
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategoryExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from base_photo_space_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from base_photo_space_category
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from base_photo_space_category
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategoryExample">
    delete from base_photo_space_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_photo_space_category (shop_id, photo_space_catrgory_name, displayS_sequence
      )
    values (#{shopId,jdbcType=BIGINT}, #{photoSpaceCatrgoryName,jdbcType=VARCHAR}, #{displaysSequence,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_photo_space_category
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="photoSpaceCatrgoryName != null">
        photo_space_catrgory_name,
      </if>
      <if test="displaysSequence != null">
        displayS_sequence,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="photoSpaceCatrgoryName != null">
        #{photoSpaceCatrgoryName,jdbcType=VARCHAR},
      </if>
      <if test="displaysSequence != null">
        #{displaysSequence,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategoryExample" resultType="java.lang.Long">
    select count(*) from base_photo_space_category
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update base_photo_space_category
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.photoSpaceCatrgoryName != null">
        photo_space_catrgory_name = #{record.photoSpaceCatrgoryName,jdbcType=VARCHAR},
      </if>
      <if test="record.displaysSequence != null">
        displayS_sequence = #{record.displaysSequence,jdbcType=BIGINT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update base_photo_space_category
    set id = #{record.id,jdbcType=BIGINT},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      photo_space_catrgory_name = #{record.photoSpaceCatrgoryName,jdbcType=VARCHAR},
      displayS_sequence = #{record.displaysSequence,jdbcType=BIGINT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory">
    update base_photo_space_category
    <set>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="photoSpaceCatrgoryName != null">
        photo_space_catrgory_name = #{photoSpaceCatrgoryName,jdbcType=VARCHAR},
      </if>
      <if test="displaysSequence != null">
        displayS_sequence = #{displaysSequence,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceCategory">
    update base_photo_space_category
    set shop_id = #{shopId,jdbcType=BIGINT},
      photo_space_catrgory_name = #{photoSpaceCatrgoryName,jdbcType=VARCHAR},
      displayS_sequence = #{displaysSequence,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>