<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BasePhotoSpaceMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="photo_category_id" jdbcType="BIGINT" property="photoCategoryId" />
    <result column="shop_id" jdbcType="BIGINT" property="shopId" />
    <result column="photo_name" jdbcType="VARCHAR" property="photoName" />
    <result column="photo_path" jdbcType="VARCHAR" property="photoPath" />
    <result column="file_size" jdbcType="BIGINT" property="fileSize" />
    <result column="upload_time" jdbcType="TIMESTAMP" property="uploadTime" />
    <result column="lastUpdate_time" jdbcType="TIMESTAMP" property="lastupdateTime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, photo_category_id, shop_id, photo_name, photo_path, file_size, upload_time, lastUpdate_time
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from base_photo_space
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from base_photo_space
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from base_photo_space
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceExample">
    delete from base_photo_space
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_photo_space (photo_category_id, shop_id, photo_name, 
      photo_path, file_size, upload_time, 
      lastUpdate_time)
    values (#{photoCategoryId,jdbcType=BIGINT}, #{shopId,jdbcType=BIGINT}, #{photoName,jdbcType=VARCHAR}, 
      #{photoPath,jdbcType=VARCHAR}, #{fileSize,jdbcType=BIGINT}, #{uploadTime,jdbcType=TIMESTAMP}, 
      #{lastupdateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_photo_space
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="photoCategoryId != null">
        photo_category_id,
      </if>
      <if test="shopId != null">
        shop_id,
      </if>
      <if test="photoName != null">
        photo_name,
      </if>
      <if test="photoPath != null">
        photo_path,
      </if>
      <if test="fileSize != null">
        file_size,
      </if>
      <if test="uploadTime != null">
        upload_time,
      </if>
      <if test="lastupdateTime != null">
        lastUpdate_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="photoCategoryId != null">
        #{photoCategoryId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        #{shopId,jdbcType=BIGINT},
      </if>
      <if test="photoName != null">
        #{photoName,jdbcType=VARCHAR},
      </if>
      <if test="photoPath != null">
        #{photoPath,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        #{fileSize,jdbcType=BIGINT},
      </if>
      <if test="uploadTime != null">
        #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastupdateTime != null">
        #{lastupdateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpaceExample" resultType="java.lang.Long">
    select count(*) from base_photo_space
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update base_photo_space
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.photoCategoryId != null">
        photo_category_id = #{record.photoCategoryId,jdbcType=BIGINT},
      </if>
      <if test="record.shopId != null">
        shop_id = #{record.shopId,jdbcType=BIGINT},
      </if>
      <if test="record.photoName != null">
        photo_name = #{record.photoName,jdbcType=VARCHAR},
      </if>
      <if test="record.photoPath != null">
        photo_path = #{record.photoPath,jdbcType=VARCHAR},
      </if>
      <if test="record.fileSize != null">
        file_size = #{record.fileSize,jdbcType=BIGINT},
      </if>
      <if test="record.uploadTime != null">
        upload_time = #{record.uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.lastupdateTime != null">
        lastUpdate_time = #{record.lastupdateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update base_photo_space
    set id = #{record.id,jdbcType=BIGINT},
      photo_category_id = #{record.photoCategoryId,jdbcType=BIGINT},
      shop_id = #{record.shopId,jdbcType=BIGINT},
      photo_name = #{record.photoName,jdbcType=VARCHAR},
      photo_path = #{record.photoPath,jdbcType=VARCHAR},
      file_size = #{record.fileSize,jdbcType=BIGINT},
      upload_time = #{record.uploadTime,jdbcType=TIMESTAMP},
      lastUpdate_time = #{record.lastupdateTime,jdbcType=TIMESTAMP}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace">
    update base_photo_space
    <set>
      <if test="photoCategoryId != null">
        photo_category_id = #{photoCategoryId,jdbcType=BIGINT},
      </if>
      <if test="shopId != null">
        shop_id = #{shopId,jdbcType=BIGINT},
      </if>
      <if test="photoName != null">
        photo_name = #{photoName,jdbcType=VARCHAR},
      </if>
      <if test="photoPath != null">
        photo_path = #{photoPath,jdbcType=VARCHAR},
      </if>
      <if test="fileSize != null">
        file_size = #{fileSize,jdbcType=BIGINT},
      </if>
      <if test="uploadTime != null">
        upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastupdateTime != null">
        lastUpdate_time = #{lastupdateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BasePhotoSpace">
    update base_photo_space
    set photo_category_id = #{photoCategoryId,jdbcType=BIGINT},
      shop_id = #{shopId,jdbcType=BIGINT},
      photo_name = #{photoName,jdbcType=VARCHAR},
      photo_path = #{photoPath,jdbcType=VARCHAR},
      file_size = #{fileSize,jdbcType=BIGINT},
      upload_time = #{uploadTime,jdbcType=TIMESTAMP},
      lastUpdate_time = #{lastupdateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>