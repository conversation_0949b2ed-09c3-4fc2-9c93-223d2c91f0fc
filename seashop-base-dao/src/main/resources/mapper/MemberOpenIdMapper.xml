<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.MemberOpenIdMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.MemberOpenId">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="open_id" property="openId" />
        <result column="union_open_id" property="unionOpenId" />
        <result column="union_id" property="unionId" />
        <result column="service_provider" property="serviceProvider" />
        <result column="app_id_type" property="appIdType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, open_id, union_open_id, union_id, service_provider, app_id_type, create_time, update_time
    </sql>

</mapper>
