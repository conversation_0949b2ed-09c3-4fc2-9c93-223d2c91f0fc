<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseOperationLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseOperationLog">
        <id column="id" property="id" />
        <result column="module_id" property="moduleId" />
        <result column="module_name" property="moduleName" />
        <result column="operation_type" property="operationType" />
        <result column="operation_name" property="operationName" />
        <result column="operation_time" property="operationTime" />
        <result column="operation_user_id" property="operationUserId" />
        <result column="operation_user_account" property="operationUserAccount" />
        <result column="operation_user_name" property="operationUserName" />
        <result column="operation_content" property="operationContent" />
        <result column="shop_id" property="shopId" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, module_id, module_name, operation_type, operation_name, operation_time, operation_user_id, operation_user_account, operation_user_name, operation_content, shop_id
    </sql>

</mapper>
