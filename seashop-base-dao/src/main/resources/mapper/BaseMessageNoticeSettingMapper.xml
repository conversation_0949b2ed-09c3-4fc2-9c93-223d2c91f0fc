<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseMessageNoticeSettingMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSetting">
    <id column="message_type" jdbcType="INTEGER" property="messageType" />

    <result column="message_type_name" jdbcType="VARCHAR" property="messageTypeName" />
    <result column="emaill_notice" jdbcType="BIT" property="emaillNotice" />
    <result column="sms_notice" jdbcType="BIT" property="smsNotice" />
    <result column="wx_notice" jdbcType="BIT" property="wxNotice" />
      <result column="template_id" jdbcType="INTEGER" property="templateId"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
      message_type, message_type_name, emaill_notice, sms_notice, wx_notice,template_id
  </sql>
  <select id="selectByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSettingExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from base_message_notice_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from base_message_notice_setting
    where message_type = #{messageType,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from base_message_notice_setting
    where message_type = #{messageType,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSettingExample">
    delete from base_message_notice_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSetting">
    <selectKey keyProperty="messageType" order="BEFORE" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_message_notice_setting (message_type, message_type_name, emaill_notice, 
      sms_notice, wx_notice)
    values (#{messageType,jdbcType=INTEGER}, #{messageTypeName,jdbcType=VARCHAR}, #{emaillNotice,jdbcType=BIT}, 
      #{smsNotice,jdbcType=BIT}, #{wxNotice,jdbcType=BIT})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSetting">
    <selectKey keyProperty="messageType" order="BEFORE" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_message_notice_setting
    <trim prefix="(" suffix=")" suffixOverrides=",">
      message_type,
      <if test="messageTypeName != null">
        message_type_name,
      </if>
      <if test="emaillNotice != null">
        emaill_notice,
      </if>
      <if test="smsNotice != null">
        sms_notice,
      </if>
      <if test="wxNotice != null">
        wx_notice,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      #{messageType,jdbcType=INTEGER},
      <if test="messageTypeName != null">
        #{messageTypeName,jdbcType=VARCHAR},
      </if>
      <if test="emaillNotice != null">
        #{emaillNotice,jdbcType=BIT},
      </if>
      <if test="smsNotice != null">
        #{smsNotice,jdbcType=BIT},
      </if>
      <if test="wxNotice != null">
        #{wxNotice,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSettingExample" resultType="java.lang.Long">
    select count(*) from base_message_notice_setting
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update base_message_notice_setting
    <set>
      <if test="record.messageType != null">
        message_type = #{record.messageType,jdbcType=INTEGER},
      </if>
      <if test="record.messageTypeName != null">
        message_type_name = #{record.messageTypeName,jdbcType=VARCHAR},
      </if>
      <if test="record.emaillNotice != null">
        emaill_notice = #{record.emaillNotice,jdbcType=BIT},
      </if>
      <if test="record.smsNotice != null">
        sms_notice = #{record.smsNotice,jdbcType=BIT},
      </if>
      <if test="record.wxNotice != null">
        wx_notice = #{record.wxNotice,jdbcType=BIT},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update base_message_notice_setting
    set message_type = #{record.messageType,jdbcType=INTEGER},
      message_type_name = #{record.messageTypeName,jdbcType=VARCHAR},
      emaill_notice = #{record.emaillNotice,jdbcType=BIT},
      sms_notice = #{record.smsNotice,jdbcType=BIT},
      wx_notice = #{record.wxNotice,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSetting">
    update base_message_notice_setting
    <set>
      <if test="messageTypeName != null">
        message_type_name = #{messageTypeName,jdbcType=VARCHAR},
      </if>
      <if test="emaillNotice != null">
        emaill_notice = #{emaillNotice,jdbcType=BIT},
      </if>
      <if test="smsNotice != null">
        sms_notice = #{smsNotice,jdbcType=BIT},
      </if>
      <if test="wxNotice != null">
        wx_notice = #{wxNotice,jdbcType=BIT},
      </if>
    </set>
    where message_type = #{messageType,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseMessageNoticeSetting">
    update base_message_notice_setting
    set message_type_name = #{messageTypeName,jdbcType=VARCHAR},
      emaill_notice = #{emaillNotice,jdbcType=BIT},
      sms_notice = #{smsNotice,jdbcType=BIT},
      wx_notice = #{wxNotice,jdbcType=BIT}
    where message_type = #{messageType,jdbcType=INTEGER}
  </update>
</mapper>