<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.account.mapper.MemberMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.account.domain.Member">
        <id column="id" property="id" />
        <result column="user_name" property="userName" />
        <result column="password" property="password" />
        <result column="password_salt" property="passwordSalt" />
        <result column="nick" property="nick" />
        <result column="sex" property="sex" />
        <result column="email" property="email" />
        <result column="top_region_id" property="topRegionId" />
        <result column="region_id" property="regionId" />
        <result column="real_name" property="realName" />
        <result column="cell_phone" property="cellPhone" />
        <result column="qq" property="qq" />
        <result column="address" property="address" />
        <result column="disabled" property="disabled" />
        <result column="last_login_date" property="lastLoginDate" />
        <result column="order_number" property="orderNumber" />
        <result column="total_amount" property="totalAmount" />
        <result column="expenditure" property="expenditure" />
        <result column="points" property="points" />
        <result column="photo" property="photo" />
        <result column="parent_seller_id" property="parentSellerId" />
        <result column="remark" property="remark" />
        <result column="pay_pwd" property="payPwd" />
        <result column="pay_pwd_salt" property="payPwdSalt" />
        <result column="invite_user_id" property="inviteUserId" />
        <result column="birth_day" property="birthDay" />
        <result column="occupation" property="occupation" />
        <result column="net_amount" property="netAmount" />
        <result column="last_consumption_time" property="lastConsumptionTime" />
        <result column="platform" property="platform" />
        <result column="encryption_mode" property="encryptionMode" />
        <result column="whether_notice_join" property="whetherNoticeJoin" />
        <result column="whether_log_out" property="whetherLogOut" />
        <result column="log_out_time" property="logOutTime" />
        <result column="register_source" property="registerSource" />
        <result column="open_id" property="openId" />
        <result column="union_id" property="unionId"/>
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_name, password, password_salt, nick, sex, email, top_region_id, region_id, real_name, cell_phone, qq, address, disabled, last_login_date, order_number, total_amount, expenditure, points, photo, parent_seller_id, remark, pay_pwd, pay_pwd_salt, invite_user_id, birth_day, occupation, net_amount, last_consumption_time, platform, encryption_mode, whether_notice_join, whether_log_out, log_out_time, register_source, open_id,union_id, create_time, update_time
    </sql>

</mapper>
