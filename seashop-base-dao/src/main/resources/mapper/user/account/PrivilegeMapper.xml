<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.account.mapper.PrivilegeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.account.domain.Privilege">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="url" property="url" />
        <result column="icon" property="icon" />
        <result column="actived_icon" property="activedIcon" />
        <result column="action" property="action" />
        <result column="parent_id" property="parentId" />
        <result column="whether_delete" property="whetherDelete" />
        <result column="display_sequence" property="displaySequence" />
        <result column="platform_id" property="platformId" />
        <result column="shop_type" property="shopType" />
        <result column="power_type" property="powerType" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, url, icon, actived_icon, action, parent_id, whether_delete, display_sequence, platform_id, shop_type, power_type, create_time, update_time
    </sql>

</mapper>
