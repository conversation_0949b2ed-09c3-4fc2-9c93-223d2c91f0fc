<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.account.mapper.ManagerMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.account.domain.Manager">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="role_id" property="roleId" />
        <result column="user_name" property="userName" />
        <result column="password" property="password" />
        <result column="password_salt" property="passwordSalt" />
        <result column="create_date" property="createDate" />
        <result column="remark" property="remark" />
        <result column="real_name" property="realName" />
        <result column="cellphone" property="cellphone" />
        <result column="encryption_mode" property="encryptionMode" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, role_id, user_name, password, password_salt, create_date, remark, real_name, cellphone, encryption_mode, create_time, update_time, create_user, update_user
    </sql>

</mapper>
