<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.account.mapper.ShippingAddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.account.domain.ShippingAddress">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="region_id" property="regionId" />
        <result column="ship_to" property="shipTo" />
        <result column="address" property="address" />
        <result column="address_detail" property="addressDetail" />
        <result column="phone" property="phone" />
        <result column="whether_default" property="whetherDefault" />
        <result column="whether_quick" property="whetherQuick" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, region_id, ship_to, address, address_detail, phone, whether_default, whether_quick, longitude, latitude, create_time, update_time
    </sql>

</mapper>
