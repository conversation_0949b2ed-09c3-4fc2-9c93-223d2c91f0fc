<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.account.mapper.ext.MemberContactExtMapper">

    <select id="queryList" resultType="com.sankuai.shangou.seashop.user.dao.account.domain.MemberContact">
    select
        <include refid="Base_Column_List" />
    from user_member_contact umc
    <where>
        <if test="longs != null and longs.size() > 0">
            and user_id in
            ( select distinct mem_id from user_member_label hml where hml.label_id in
                <foreach collection="longs" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            )
        </if>
        <if test="serviceProvider != null and serviceProvider != ''">
            and service_provider = #{serviceProvider}
        </if>
    </where>
    group by umc.id
    </select>
    <select id="queryExtList" resultType="com.sankuai.shangou.seashop.user.dao.account.model.MemberContactExtModel">
        select
        <include refid="Base_Column_List" />, um.user_name,usm.shop_id
        from user_member_contact umc left join user_member um on umc.user_id = um.id
            left join (
            select distinct uma.user_name,us.id as shop_id from user_shop us left join user_manager uma on uma.role_id = 0 and us.id = uma.shop_id ) usm on um.user_name = usm.user_name
        <where>
            <if test="shopIds != null and shopIds.size() > 0">
                and usm.shop_id in
                <foreach collection="shopIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="userIds != null and userIds.size() > 0">
                and user_id in
                <foreach collection="userIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        group by umc.id
    </select>

    <sql id="Base_Column_List">
        umc.id, umc.user_id, umc.user_type, umc.service_provider, umc.contact, umc.create_time, umc.update_time
    </sql>
</mapper>