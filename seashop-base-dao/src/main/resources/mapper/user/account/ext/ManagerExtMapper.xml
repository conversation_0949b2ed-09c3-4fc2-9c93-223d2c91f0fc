<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.account.mapper.ext.ManagerExtMapper">

    <select id="selectExtList" resultType="com.sankuai.shangou.seashop.user.dao.account.model.ManagerExtModel">
        select
            <include refid="Base_Column_List" />, ur.role_name
        from
        user_manager um
        left join user_role ur on um.role_id = ur.id
        where 1=1
        <if test="query.shopId != null">
            and um.shop_id = #{query.shopId}
        </if>
        <if test="query.roleId != null">
            and um.role_id = #{query.roleId}
        </if>
        <if test="query.managerIds != null and query.managerIds.size() > 0">
            and um.id IN
            <foreach collection="query.managerIds" item="managerId" open="(" separator="," close=")">
                #{managerId}
            </foreach>
        </if>
        order by um.create_time desc
    </select>
    <select id="getManagerIdsByUserIds"
            resultType="com.sankuai.shangou.seashop.user.dao.account.domain.Member">
        select
            ume.id,ume.user_name
        from user_member ume
            left join user_manager uma on ume.user_name = uma.user_name
        where shop_id &lt;&gt; 0 and ume.id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <sql id="Base_Column_List">
        um.id, um.shop_id, um.role_id, um.user_name, um.password, um.password_salt, um.remark, um.real_name, um.cellphone, um.encryption_mode, um.create_time, um.update_time, um.create_user, um.update_user
    </sql>
</mapper>