<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.account.mapper.ext.FavoriteShopExtMapper">
  <select id="queryList" resultType="com.sankuai.shangou.seashop.user.dao.account.model.FavoriteShopExt">

    select ufs1.id, ufs1.user_id, ufs1.shop_id, ufs1.shop_status, ufs1.tags, ufs1.create_time, ufs1.shop_name, ufs1.logo as shop_logo, ufs3.popularity
    from
      (select
         ufs.id, ufs.user_id, ufs.shop_id, ufs.tags, ufs.create_time, us.shop_name, us.logo ,us.shop_status
       from user_favorite_shop ufs
              left join user_shop us on ufs.shop_id = us.id
       where
         ufs.user_id = #{userId}
      ) as ufs1 left join
      (
        select
          ufs2.shop_id,count(distinct ufs2.user_id) as popularity
        from user_favorite_shop ufs2
        group by ufs2.shop_id
      ) as ufs3 on ufs1.shop_id = ufs3.shop_id
    order by ufs1.create_time desc
  </select>

</mapper>