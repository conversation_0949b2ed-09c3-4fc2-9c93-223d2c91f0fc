<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.account.mapper.ext.MemberExtMapper">
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.account.domain.Member">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="user_name" jdbcType="VARCHAR" property="userName" />
        <result column="password" jdbcType="VARCHAR" property="password" />
        <result column="password_salt" jdbcType="VARCHAR" property="passwordSalt" />
        <result column="nick" jdbcType="VARCHAR" property="nick" />
        <result column="sex" jdbcType="INTEGER" property="sex" />
        <result column="email" jdbcType="VARCHAR" property="email" />
        <result column="top_region_id" jdbcType="INTEGER" property="topRegionId" />
        <result column="region_id" jdbcType="INTEGER" property="regionId" />
        <result column="real_name" jdbcType="VARCHAR" property="realName" />
        <result column="cell_phone" jdbcType="VARCHAR" property="cellPhone" />
        <result column="qq" jdbcType="VARCHAR" property="qq" />
        <result column="address" jdbcType="VARCHAR" property="address" />
        <result column="disabled" jdbcType="BIT" property="disabled" />
        <result column="last_login_date" jdbcType="TIMESTAMP" property="lastLoginDate" />
        <result column="order_number" jdbcType="INTEGER" property="orderNumber" />
        <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
        <result column="expenditure" jdbcType="DECIMAL" property="expenditure" />
        <result column="points" jdbcType="INTEGER" property="points" />
        <result column="photo" jdbcType="VARCHAR" property="photo" />
        <result column="parent_seller_id" jdbcType="BIGINT" property="parentSellerId" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
        <result column="pay_pwd" jdbcType="VARCHAR" property="payPwd" />
        <result column="pay_pwd_salt" jdbcType="VARCHAR" property="payPwdSalt" />
        <result column="invite_user_id" jdbcType="BIGINT" property="inviteUserId" />
        <result column="birth_day" jdbcType="DATE" property="birthDay" />
        <result column="occupation" jdbcType="VARCHAR" property="occupation" />
        <result column="net_amount" jdbcType="DECIMAL" property="netAmount" />
        <result column="last_consumption_time" jdbcType="TIMESTAMP" property="lastConsumptionTime" />
        <result column="platform" jdbcType="INTEGER" property="platform" />
        <result column="encryption_mode" jdbcType="VARCHAR" property="encryptionMode" />
        <result column="whether_notice_join" jdbcType="BIT" property="whetherLogOut" />
        <result column="whether_log_out" jdbcType="BIT" property="whetherLogOut" />
        <result column="log_out_time" jdbcType="TIMESTAMP" property="logOutTime" />
        <result column="register_source" jdbcType="INTEGER" property="registerSource" />
        <result column="open_id" jdbcType="VARCHAR" property="openId" />
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    </resultMap>
    <sql id="Base_Column_List">
        um.id, um.user_name, um.password, um.password_salt, um.nick, um.sex, um.top_region_id,
    um.region_id, um.real_name, um.cell_phone, um.qq, um.address, um.disabled, um.last_login_date, um.order_number,
    um.total_amount, um.expenditure, um.points, um.photo, um.parent_seller_id, um.remark, um.pay_pwd, um.pay_pwd_salt,
    um.invite_user_id, um.birth_day, um.occupation, um.net_amount, um.last_consumption_time, um.platform,
    um.encryption_mode, um.whether_notice_join, um.whether_log_out, um.log_out_time, um.register_source, um.open_id,
    um.create_time, um.update_time
    </sql>
    <select id="queryMember" resultType="com.sankuai.shangou.seashop.user.dao.account.domain.MemberExt"
            parameterType="com.sankuai.shangou.seashop.user.dao.account.model.QueryMemberListModel">
        select
         distinct <include refid="Base_Column_List" />,if(uma.shop_id is null,0,1) as seller, umc.contact as email
        from
        user_member um
        left join user_manager uma on um.user_name = uma.user_name and uma.shop_id > 0 and uma.role_id =0
        left join user_member_contact umc on umc.user_id = um.id and umc.service_provider = 'Himall.Plugin.Message.Email'
        <where>
            um.whether_log_out = 0
            <if test="memberName != null and memberName != ''">
                and um.user_name like concat('%',#{memberName},'%')
            </if>
            <if test="label != null">
                and um.id in (
                select distinct mem_id from user_member_label where label_id = #{label})
            </if>
            <if test="registerTimeStart != null and registerTimeEnd != null ">
                and um.create_time between #{registerTimeStart} and #{registerTimeEnd}
            </if>
            <!--            <if test="gradeId != null">-->
            <!--                and um.create_date between #{registerTimeStart,jdbcType=TIMESTAMP} and #{registerTimeEnd,jdbcType=TIMESTAMP},-->
            <!--            </if>-->
            <!--            <if test="izFocusWeiXin != null and izFocusWeiXin">-->
            <!--                and uma.shop_id = 0,-->
            <!--            </if>-->
            <if test="mobile != null and mobile != ''">
                and um.cell_phone = #{mobile}
            </if>
            <if test="weChatNick != null and weChatNick != ''">
                and um.nick like concat('%',#{weChatNick},'%')
            </if>
            <if test="seller != null and seller">
                and uma.shop_id is not null
            </if>
            <if test="seller != null and !seller">
                and uma.shop_id is null
            </if>
            <if test="status != null">
                and um.disabled = #{status}
            </if>
            <if test="platform != null">
                and um.platform = #{platform}
            </if>
            <if test="buyCountStart != null and buyCountEnd != null">
                and um.order_number between #{buyCountStart} and #{buyCountEnd}
            </if>
            <if test="totalAmountStart != null and totalAmountEnd != null">
                and um.total_amount between #{totalAmountStart} and #{totalAmountEnd}
            </if>
            <if test="priceStart != null and priceEnd != null">
                and IFNULL(um.total_amount/um.order_number,0) between #{priceStart} and #{priceEnd}
            </if>
        </where>
        <if test="sortField != null and sortField != ''">
            order by ${sortField}
        </if>
        <if test="sortField == null or sortField == ''">
            order by um.id desc
        </if>
    </select>
    <select id="queryUserIdByMobile" resultType="java.lang.Long">
        select distinct um.id
        from user_member um
        left join user_member_contact umc on umc.user_id = um.id
        where contact like concat('%',#{mobile},'%')
    </select>
</mapper>