<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.account.mapper.ext.LabelExtMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.account.model.LabelExtModel">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="label_name" jdbcType="VARCHAR" property="labelName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user" jdbcType="BIGINT" property="createUser" />
    <result column="update_user" jdbcType="BIGINT" property="updateUser" />
    <result column="update_user" jdbcType="BIGINT" property="updateUser" />
    <result column="member_count" jdbcType="BIGINT" property="memberCount" />
  </resultMap>
  <select id="queryLabelPage" resultMap="BaseResultMap">
    select
        hl.id, hl.label_name, hl.create_time, hl.update_time, hl.create_user, hl.update_user,count(DISTINCT hml.mem_id) member_count
    from user_label hl left join user_member_label hml on hl.id = hml.label_id left join  user_member hm on hm.id = hml.mem_id and hm.whether_log_out = 0
    <where>
        <if test="labelName != null and labelName != ''">
            and label_name like concat('%',#{labelName},'%')
        </if>
    </where>
    group by hl.id
    order by hl.create_time desc
  </select>

    <select id="queryLabelName" resultType="com.sankuai.shangou.seashop.user.dao.account.model.MemberLabelNameExt">
        select
        hml.mem_id as user_id,GROUP_CONCAT(hl.label_name) as label_name
        from  user_member_label hml left join user_label hl on hl.id = hml.label_id
        <where>
            hml.mem_id in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        group by hml.mem_id
    </select>

</mapper>