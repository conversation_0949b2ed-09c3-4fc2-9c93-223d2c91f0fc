<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.account.mapper.InvoiceTitleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.account.domain.InvoiceTitle">
        <id column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="invoice_type" property="invoiceType" />
        <result column="name" property="name" />
        <result column="code" property="code" />
        <result column="invoice_context" property="invoiceContext" />
        <result column="register_address" property="registerAddress" />
        <result column="register_phone" property="registerPhone" />
        <result column="bank_name" property="bankName" />
        <result column="bank_no" property="bankNo" />
        <result column="real_name" property="realName" />
        <result column="cell_phone" property="cellPhone" />
        <result column="email" property="email" />
        <result column="region_id" property="regionId" />
        <result column="address" property="address" />
        <result column="is_default" property="whetherDefault" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, user_id, invoice_type, name, code, invoice_context, register_address, register_phone, bank_name, bank_no, real_name, cell_phone, email, region_id, address, is_default
    </sql>

</mapper>
