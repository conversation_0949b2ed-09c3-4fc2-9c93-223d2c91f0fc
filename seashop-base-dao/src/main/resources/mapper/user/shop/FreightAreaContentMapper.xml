<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.FreightAreaContentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.FreightAreaContent">
        <id column="id" property="id" />
        <result column="freight_template_id" property="freightTemplateId" />
        <result column="area_content" property="areaContent" />
        <result column="first_unit" property="firstUnit" />
        <result column="first_unit_monry" property="firstUnitMonry" />
        <result column="accumulation_unit" property="accumulationUnit" />
        <result column="accumulation_unit_money" property="accumulationUnitMoney" />
        <result column="whether_default" property="whetherDefault" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, freight_template_id, area_content, first_unit, first_unit_monry, accumulation_unit, accumulation_unit_money, whether_default, create_time, update_time
    </sql>

</mapper>
