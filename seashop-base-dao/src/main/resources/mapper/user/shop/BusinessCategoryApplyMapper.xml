<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.BusinessCategoryApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryApply">
        <id column="id" property="id" />
        <result column="apply_date" property="applyDate" />
        <result column="shop_id" property="shopId" />
        <result column="shop_name" property="shopName" />
        <result column="audited_status" property="auditedStatus" />
        <result column="audited_date" property="auditedDate" />
        <result column="refuse_reason" property="refuseReason" />
        <result column="agreement_status" property="agreementStatus" />
        <result column="shop_agreement_id" property="shopAgreementId" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, apply_date, shop_id, shop_name, audited_status, audited_date, refuse_reason, agreement_status, shop_agreement_id, create_time, update_time
    </sql>

    <select id="listBusinessApply" resultMap="BaseResultMap">
        SELECT
            a.*
        FROM
            user_business_category_apply a
        INNER JOIN user_shop s ON a.shop_id = s.id
        <where>
            AND s.shop_status IN (6, 7)
            <if test="param.auditedStatus!= null">
                AND a.audited_status = #{param.auditedStatus}
            </if>
            <if test="param.agreementStatus!= null">
                AND a.agreement_status = #{param.agreementStatus}
            </if>
            <if test="param.shopId != null">
                AND a.shop_id = #{param.shopId}
            </if>
            <if test="param.shopName != null and param.shopName != ''">
                AND s.shop_name LIKE CONCAT("%", #{param.shopName}, "%")
            </if>
        </where>
        ORDER BY a.apply_date DESC
    </select>

</mapper>
