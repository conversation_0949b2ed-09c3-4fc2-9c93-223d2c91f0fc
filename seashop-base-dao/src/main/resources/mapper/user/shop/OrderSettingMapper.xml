<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.OrderSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.OrderSetting">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="purchase_min_valid_type" property="purchaseMinValidType" />
        <result column="purchase_min_quantity" property="purchaseMinQuantity" />
        <result column="purchase_min_price" property="purchaseMinPrice" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, purchase_min_valid_type, purchase_min_quantity, purchase_min_price, create_time, update_time, create_user, update_user
    </sql>

</mapper>
