<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.CustomerServiceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.CustomerService">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="tool" property="tool" />
        <result column="type" property="type" />
        <result column="name" property="name" />
        <result column="account_code" property="accountCode" />
        <result column="terminal_type" property="terminalType" />
        <result column="server_status" property="serverStatus" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, tool, type, name, account_code, terminal_type, server_status
    </sql>

</mapper>
