<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopInvoiceConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.ShopInvoiceConfig">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="whether_invoice" property="whetherInvoice" />
        <result column="whether_plain_invoice" property="whetherPlainInvoice" />
        <result column="whether_electronic_invoice" property="whetherElectronicInvoice" />
        <result column="plain_invoice_rate" property="plainInvoiceRate" />
        <result column="whether_vat_invoice" property="whetherVatInvoice" />
        <result column="vat_invoice_day" property="vatInvoiceDay" />
        <result column="vat_invoice_rate" property="vatInvoiceRate" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, whether_invoice, whether_plain_invoice, whether_electronic_invoice, plain_invoice_rate, whether_vat_invoice, vat_invoice_day, vat_invoice_rate, create_time, update_time
    </sql>

</mapper>
