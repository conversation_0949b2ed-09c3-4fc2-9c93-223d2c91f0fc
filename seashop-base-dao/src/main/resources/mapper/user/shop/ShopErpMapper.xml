<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopErpMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.ShopErp">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="erp_type" property="erpType" />
        <result column="jst_status" property="jstStatus" />
        <result column="jst_url" property="jstUrl" />
        <result column="jst_url_create_time" property="jstUrlCreateTime" />
        <result column="jst_code" property="jstCode" />
        <result column="jst_code_get_time" property="jstCodeGetTime" />
        <result column="jst_access_token" property="jstAccessToken" />
        <result column="jst_access_token_expires" property="jstAccessTokenExpires" />
        <result column="jst_refresh_token" property="jstRefreshToken" />
        <result column="jst_token_get_time" property="jstTokenGetTime" />
        <result column="jst_shop_id" property="jstShopId" />
        <result column="jst_co_id" property="jstCoId" />
        <result column="blp_token" property="blpToken" />
        <result column="wdt_token" property="wdtToken" />
        <result column="whether_send_sms" property="whetherSendSms" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, erp_type, jst_status, jst_url, jst_url_create_time, jst_code, jst_code_get_time, jst_access_token, jst_access_token_expires, jst_refresh_token, jst_token_get_time, jst_shop_id, jst_co_id, blp_token, wdt_token, whether_send_sms, create_time, update_time
    </sql>

</mapper>
