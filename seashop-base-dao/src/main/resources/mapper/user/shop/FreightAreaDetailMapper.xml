<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.FreightAreaDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.FreightAreaDetail">
        <id column="id" property="id" />
        <result column="freight_template_id" property="freightTemplateId" />
        <result column="freight_area_id" property="freightAreaId" />
        <result column="province_id" property="provinceId" />
        <result column="city_id" property="cityId" />
        <result column="county_id" property="countyId" />
        <result column="town_ids" property="townIds" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, freight_template_id, freight_area_id, province_id, city_id, county_id, town_ids, create_time, update_time
    </sql>

</mapper>
