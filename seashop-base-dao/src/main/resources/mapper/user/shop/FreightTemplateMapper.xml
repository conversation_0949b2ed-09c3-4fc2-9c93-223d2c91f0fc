<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.FreightTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.FreightTemplate">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="source_address" property="sourceAddress" />
        <result column="send_time" property="sendTime" />
        <result column="whether_free" property="whetherFree" />
        <result column="valuation_method" property="valuationMethod" />
        <result column="shipping_method" property="shippingMethod" />
        <result column="shop_id" property="shopId" />
        <result column="non_sales_area_hide" property="nonSalesAreaHide" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, source_address, send_time, whether_free, valuation_method, shipping_method, shop_id, non_sales_area_hide, create_time, update_time
    </sql>

</mapper>
