<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.BusinessCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategory">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="category_id" property="categoryId" />
        <result column="commission_rate" property="commissionRate" />
        <result column="bond" property="bond" />
        <result column="whether_frozen" property="whetherFrozen" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, category_id, commis_rate, bond, whether_frozen, create_time, update_time
    </sql>

    <select id="listWaitAuditCategoryIdsByShopId" resultType="long">
        SELECT
            DISTINCT d.category_id
        FROM
            user_business_category_apply a
        INNER JOIN user_business_category_apply_detail d ON a.id = d.apply_id
        WHERE
            a.audited_status = 0
        AND a.shop_id = #{shopId}
    </select>

</mapper>
