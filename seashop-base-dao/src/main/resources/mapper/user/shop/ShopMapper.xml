<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.Shop">
        <id column="id" property="id" />
        <result column="grade_id" property="gradeId" />
        <result column="shop_name" property="shopName" />
        <result column="logo" property="logo" />
        <result column="sub_domains" property="subDomains" />
        <result column="theme" property="theme" />
        <result column="whether_self" property="whetherSelf" />
        <result column="shop_status" property="shopStatus" />
        <result column="refuse_reason" property="refuseReason" />
        <result column="adapay_status" property="adapayStatus" />
        <result column="adapay_reason" property="adapayReason" />
        <result column="plate_status" property="plateStatus" />
        <result column="create_date" property="createDate" />
        <result column="contacts_name" property="contactsName" />
        <result column="contacts_phone" property="contactsPhone" />
        <result column="contacts_email" property="contactsEmail" />
        <result column="general_taxpayer_phot" property="generalTaxpayerPhot" />
        <result column="bank_account_name" property="bankAccountName" />
        <result column="bank_account_number" property="bankAccountNumber" />
        <result column="bank_name" property="bankName" />
        <result column="bank_code" property="bankCode" />
        <result column="bank_region_id" property="bankRegionId" />
        <result column="bank_photo" property="bankPhoto" />
        <result column="bank_type" property="bankType" />
        <result column="tax_registration_certificate" property="taxRegistrationCertificate" />
        <result column="taxpayer_id" property="taxpayerId" />
        <result column="tax_registration_certificate_photo" property="taxRegistrationCertificatePhoto" />
        <result column="pay_photo" property="payPhoto" />
        <result column="pay_remark" property="payRemark" />
        <result column="sender_name" property="senderName" />
        <result column="sender_address" property="senderAddress" />
        <result column="sender_phone" property="senderPhone" />
        <result column="freight" property="freight" />
        <result column="free_freight" property="freeFreight" />
        <result column="amount_freight_condition" property="amountFreightCondition" />
        <result column="amount_freight" property="amountFreight" />
        <result column="quantity_freight_condition" property="quantityFreightCondition" />
        <result column="quantity_freight" property="quantityFreight" />
        <result column="money_off_condition" property="moneyOffCondition" />
        <result column="money_off_fee" property="moneyOffFee" />
        <result column="money_off_overlay" property="moneyOffOverlay" />
        <result column="stage" property="stage" />
        <result column="sender_region_id" property="senderRegionId" />
        <result column="product_cert" property="productCert" />
        <result column="other_cert" property="otherCert" />
        <result column="business_type" property="businessType" />
        <result column="id_card" property="idCard" />
        <result column="id_cardurl" property="idCardurl" />
        <result column="id_cardurl2" property="idCardurl2" />
        <result column="auto_allot_order" property="autoAllotOrder" />
        <result column="whether_auto_print" property="whetherAutoPrint" />
        <result column="print_count" property="printCount" />
        <result column="whether_open_top_image_ad" property="whetherOpenTopImageAd" />
        <result column="whether_open_hi_chat" property="whetherOpenHiChat" />
        <result column="whether_pay_bond" property="whetherPayBond" />
        <result column="whether_agreement" property="whetherAgreement" />
        <result column="whether_sms_tips" property="whetherSmsTips" />
        <result column="autograph_img" property="autographImg" />
        <result column="id_card_start_date" property="idCardStartDate" />
        <result column="id_card_end_date" property="idCardEndDate" />
        <result column="licence_cert_addr" property="licenceCertAddr" />
        <result column="currency_type" property="currencyType" />
        <result column="introduct" property="introduct" />
        <result column="form_data" property="formData" />
        <result column="whether_open_exclusive_member" property="whetherOpenExclusiveMember" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="order_pay_iz_send_sms" property="orderPayIzSendSms" />
        <result column="shop_type" property="shopType" />
        <result column="id_card_date" property="idCardDate" />
        <result column="serial_number" property="serialNumber" />
        <result column="whether_supply" property="whetherSupply" />
        <result column="whether_arrear" property="whetherArrear" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, grade_id, shop_name, logo, sub_domains, theme, whether_self, shop_status, refuse_reason, adapay_status, adapay_reason, plate_status, create_date, contacts_name, contacts_phone, contacts_email, general_taxpayer_phot, bank_account_name, bank_account_number, bank_name, bank_code, bank_region_id, bank_photo, bank_type, tax_registration_certificate, taxpayer_id, tax_registration_certificate_photo, pay_photo, pay_remark, sender_name, sender_address, sender_phone, freight, free_freight, amount_freight_condition, amount_freight, quantity_freight_condition, quantity_freight, money_off_condition, money_off_fee, money_off_overlay, stage, sender_region_id, product_cert, other_cert, business_type, id_card, id_cardurl, id_cardurl2, auto_allot_order, whether_auto_print, print_count, whether_open_top_image_ad, whether_open_hi_chat, whether_pay_bond, whether_agreement, whether_sms_tips, autograph_img, id_card_start_date, id_card_end_date, licence_cert_addr, currency_type, introduct, form_data, whether_open_exclusive_member, create_time, update_time, order_pay_iz_send_sms, shop_type, id_card_date, serial_number, whether_supply, whether_arrear
    </sql>

</mapper>
