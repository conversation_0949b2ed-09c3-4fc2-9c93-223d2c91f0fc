<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopExtMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.ShopExt">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="company_name" property="companyName" />
        <result column="company_region_id" property="companyRegionId" />
        <result column="company_address" property="companyAddress" />
        <result column="company_phone" property="companyPhone" />
        <result column="company_employee_count" property="companyEmployeeCount" />
        <result column="company_registered_capital" property="companyRegisteredCapital" />
        <result column="business_license_number" property="businessLicenseNumber" />
        <result column="business_license_number_photo" property="businessLicenseNumberPhoto" />
        <result column="business_license_region_id" property="businessLicenseRegionId" />
        <result column="business_license_start" property="businessLicenseStart" />
        <result column="business_license_end" property="businessLicenseEnd" />
        <result column="business_sphere" property="businessSphere" />
        <result column="organization_code" property="organizationCode" />
        <result column="organization_code_photo" property="organizationCodePhoto" />
        <result column="tax_registration_certificate" property="taxRegistrationCertificate" />
        <result column="taxpayer_id" property="taxpayerId" />
        <result column="tax_registration_certificate_photo" property="taxRegistrationCertificatePhoto" />
        <result column="legal_person" property="legalPerson" />
        <result column="company_founding_date" property="companyFoundingDate" />
        <result column="company_type" property="companyType" />
        <result column="licence_cert_addr" property="licenceCertAddr" />
        <result column="finance_chief" property="financeChief" />
        <result column="finance_chief_phone" property="financeChiefPhone" />
        <result column="introduct" property="introduct" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, company_name, company_region_id, company_address, company_phone, company_employee_count, company_registered_capital, business_license_number, business_license_number_photo, business_license_region_id, business_license_start, business_license_end, business_sphere, organization_code, organization_code_photo, tax_registration_certificate, taxpayer_id, tax_registration_certificate_photo, legal_person, company_founding_date, company_type, licence_cert_addr, finance_chief, finance_chief_phone, introduct, create_time, update_time
    </sql>

</mapper>
