<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.BusinessCategoryFormMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryForm">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="category_id" property="categoryId" />
        <result column="form_id" property="formId" />
        <result column="form_data" property="formData" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, category_id, form_id, form_data, create_time, update_time
    </sql>

</mapper>
