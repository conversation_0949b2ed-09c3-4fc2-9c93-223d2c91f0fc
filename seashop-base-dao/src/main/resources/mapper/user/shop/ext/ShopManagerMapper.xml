<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.ext.ShopManagerMapper">


    <select id="selectList" resultType="com.sankuai.shangou.seashop.user.dao.shop.model.ShopModel"
    parameterType="com.sankuai.shangou.seashop.user.dao.shop.model.ShopQueryModel">
        select <include refid="Base_Column_List"/>
            ,um.user_name as shop_account
        from user_shop us
            left join user_manager um
                on us.id = um.shop_id and um.role_id = 0 and um.shop_id != 0
        <where>
            us.shop_status &gt; 1
            <if test="query.shopName != null and query.shopName != ''">
                and us.shop_name like concat('%',#{query.shopName},'%')
            </if>
            <if test="query.shopAccount != null and query.shopAccount != ''">
                and um.user_name like concat('%',#{query.shopAccount},'%')
            </if>
            <if test="query.status != null">
                and us.shop_status = #{query.status}
            </if>
            <if test="query.whetherAgreement!= null">
                and us.whether_agreement = #{query.whetherAgreement}
            </if>
            <if test="query.whetherPayBond!= null">
                and us.whether_arrear= #{query.whetherPayBond}
            </if>
            <if test="query.whetherSupply != null ">
                and us.whether_supply = #{query.whetherSupply}
            </if>
            <if test="query.shopId!= null">
                and us.id= #{query.shopId}
            </if>
            <if test="query.shopType != null || query.shopType == 0">
                and us.shop_type= #{query.shopType}
            </if>
        </where>
        order by us.id desc
    </select>
    <sql id="Base_Column_List">
        us.id, us.grade_id, us.shop_name, us.logo, us.sub_domains, us.theme, us.whether_self, us.shop_status, us.refuse_reason, us.adapay_status, us.adapay_reason, us.plate_status, us.create_date, us.contacts_name, us.contacts_phone, us.contacts_email, us.general_taxpayer_phot, us.bank_account_name, us.bank_account_number, us.bank_name, us.bank_code, us.bank_region_id, us.bank_photo, us.bank_type, us.tax_registration_certificate, us.taxpayer_id, us.tax_registration_certificate_photo, us.pay_photo, us.pay_remark, us.sender_name, us.sender_address, us.sender_phone, us.freight, us.free_freight, us.amount_freight_condition, us.amount_freight, us.quantity_freight_condition, us.quantity_freight, us.money_off_condition, us.money_off_fee, us.money_off_overlay, us.stage, us.sender_region_id, us.product_cert, us.other_cert, us.business_type, us.id_card, us.id_cardurl, us.id_cardurl2, us.auto_allot_order, us.whether_auto_print, us.print_count, us.whether_open_top_image_ad, us.whether_open_hi_chat, us.whether_pay_bond, us.whether_agreement, us.whether_sms_tips, us.autograph_img, us.id_card_start_date, us.id_card_end_date, us.licence_cert_addr, us.currency_type, us.introduct, us.form_data, us.whether_open_exclusive_member, us.create_time, us.update_time, us.order_pay_iz_send_sms, us.shop_type, us.serial_number, us.whether_supply, us.whether_arrear
    </sql>
    <select id="getCellPhoneList" resultType="java.lang.String">
        select umc.contact from user_shop us
            left join user_manager um
                on us.id = um.shop_id and um.role_id = 0 and um.shop_id != 0
            left join user_member ume
                on um.user_name = ume.user_name
            left join user_member_contact umc
                on umc.service_provider = 'SMS' and umc.user_id = ume.id
        where us.id in
            <foreach collection="shopIds" item="shopId" open="(" separator="," close=")">
                #{shopId}
            </foreach>
    </select>
    <select id="getShopIdsByUserIds" resultType="com.sankuai.shangou.seashop.user.dao.account.domain.Member">
        select
            umm.id,umm.user_name
        from user_shop us
        left join user_manager um
        on us.id = um.shop_id and um.role_id = 0 and um.shop_id != 0
        left join user_member umm
        on um.user_name = umm.user_name
        where umm.id in
            <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
            </foreach>
            and us.id is not null
            and us.shop_status in (6,7)
    </select>
    <select id="getOpenShopIdsByUserIds"
            resultType="com.sankuai.shangou.seashop.user.dao.account.domain.Member">
        select
        umm.id,umm.user_name
        from user_shop us
        left join user_manager um
        on us.id = um.shop_id and um.role_id = 0 and um.shop_id != 0
        left join user_member umm
        on um.user_name = umm.user_name
        where umm.id in
        <foreach collection="userIds" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        and us.id is not null
        and us.shop_status in (7)
    </select>
</mapper>
