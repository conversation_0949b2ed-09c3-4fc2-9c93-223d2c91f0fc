<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.ext.BusinessCategoryFormExtMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryForm">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="category_id" property="categoryId" />
        <result column="form_id" property="formId" />
        <result column="form_data" property="formData" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, category_id, form_id, form_data, create_time, update_time
    </sql>
    <select id="listByApplyStatus"
            resultType="com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategoryForm">
        select
            bcf.id, bcf.shop_id, bcf.category_id, bcf.form_id, bcf.form_data, bcf.create_time, bcf.update_time
        from user_business_category_form bcf
        left join(select
                 distinct bcfa.shop_id,bcfad.category_id
            from user_business_category_apply_detail bcfad
            left join user_business_category_apply bcfa
                on bcfad.apply_id = bcfa.id
            where bcfa.audited_status = #{applyStatus}) bcfap
        on bcf.shop_id = bcfap.shop_id and bcf.category_id = bcfap.category_id
    </select>


</mapper>
