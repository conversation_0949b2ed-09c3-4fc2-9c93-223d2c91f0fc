<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.ext.BusinessCategoryExtMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.BusinessCategory">
        <id column="id" property="id"/>
        <result column="shop_id" property="shopId"/>
        <result column="category_id" property="categoryId"/>
        <result column="commission_rate" property="commissionRate"/>
        <result column="bond" property="bond"/>
        <result column="whether_frozen" property="whetherFrozen"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, category_id, commis_rate, bond, whether_frozen, create_time, update_time
    </sql>


    <select id="listByShopAndCategory" resultType="java.lang.Long">
        SELECT distinct shop_id
        FROM user_business_category
        WHERE
        category_id in
        <foreach collection="categoryIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
        <if test="shopId != null">
            and shop_id = #{shopId}
        </if>
    </select>
    <select id="listByCategory" resultType="java.lang.Long">
        SELECT distinct shop_id
        FROM user_business_category
        WHERE
        category_id in
        <foreach collection="categoryIds" item="item" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

</mapper>
