<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopOpenApiSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.ShopOpenApiSetting">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="app_key" property="appKey" />
        <result column="app_secret" property="appSecret" />
        <result column="add_date" property="addDate" />
        <result column="last_edit_date" property="lastEditDate" />
        <result column="whether_enable" property="whetherEnable" />
        <result column="whether_registered" property="whetherRegistered" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, app_key, app_secret, add_date, last_edit_date, whether_enable, whether_registered
    </sql>

</mapper>
