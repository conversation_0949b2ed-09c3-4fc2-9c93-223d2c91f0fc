<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.user.dao.shop.mapper.ShopShipperMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.user.dao.shop.domain.ShopShipper">
        <id column="id" property="id" />
        <result column="shop_id" property="shopId" />
        <result column="default_send_goods_flag" property="defaultSendGoodsFlag" />
        <result column="default_get_goods_flag" property="defaultGetGoodsFlag" />
        <result column="shipper_tag" property="shipperTag" />
        <result column="shipper_name" property="shipperName" />
        <result column="region_id" property="regionId" />
        <result column="address" property="address" />
        <result column="tel_phone" property="telPhone" />
        <result column="wx_open_id" property="wxOpenId" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, shop_id, default_send_goods_flag, default_get_goods_flag,  shipper_tag, shipper_name, region_id, address, tel_phone,  wx_open_id, longitude, latitude, create_time, update_time
    </sql>

</mapper>
