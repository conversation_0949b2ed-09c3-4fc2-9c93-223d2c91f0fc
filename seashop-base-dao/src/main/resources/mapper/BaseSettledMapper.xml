<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseSettledMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettled">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="settlement_account_type" jdbcType="INTEGER" property="settlementAccountType" />
    <result column="trial_days" jdbcType="INTEGER" property="trialDays" />
    <result column="is_city" jdbcType="INTEGER" property="isCity" />
    <result column="is_people_number" jdbcType="INTEGER" property="isPeopleNumber" />
    <result column="is_address" jdbcType="INTEGER" property="isAddress" />
    <result column="is_business_license_code" jdbcType="INTEGER" property="isBusinessLicenseCode" />
    <result column="is_business_scope" jdbcType="INTEGER" property="isBusinessScope" />
    <result column="is_business_license" jdbcType="INTEGER" property="isBusinessLicense" />
    <result column="is_agency_code" jdbcType="INTEGER" property="isAgencyCode" />
    <result column="is_agency_code_license" jdbcType="INTEGER" property="isAgencyCodeLicense" />
    <result column="is_taxpayer_to_prove" jdbcType="INTEGER" property="isTaxpayerToProve" />
    <result column="company_verification_type" jdbcType="INTEGER" property="companyVerificationType" />
    <result column="is_s_name" jdbcType="INTEGER" property="isSName" />
    <result column="is_s_city" jdbcType="INTEGER" property="isSCity" />
    <result column="is_s_address" jdbcType="INTEGER" property="isSAddress" />
    <result column="is_sid_card" jdbcType="INTEGER" property="isSidCard" />
    <result column="is_sid_card_url" jdbcType="INTEGER" property="isSidCardUrl" />
    <result column="self_verification_type" jdbcType="INTEGER" property="selfVerificationType" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledWithBLOBs">
    <result column="custom_form_Json" jdbcType="LONGVARCHAR" property="customFormJson" />
    <result column="personal_custom_form_Json" jdbcType="LONGVARCHAR" property="personalCustomFormJson" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_type, settlement_account_type, trial_days, is_city, is_people_number, 
    is_address, is_business_license_code, is_business_scope, is_business_license, is_agency_code, 
    is_agency_code_license, is_taxpayer_to_prove, company_verification_type, is_s_name, 
    is_s_city, is_s_address, is_sid_card, is_sid_card_url, self_verification_type
  </sql>
  <sql id="Blob_Column_List">
    custom_form_Json, personal_custom_form_Json
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from base_settled
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from base_settled
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from base_settled
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from base_settled
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledExample">
    delete from base_settled
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_settled (business_type, settlement_account_type, 
      trial_days, is_city, is_people_number, 
      is_address, is_business_license_code, is_business_scope, 
      is_business_license, is_agency_code, is_agency_code_license, 
      is_taxpayer_to_prove, company_verification_type, 
      is_s_name, is_s_city, is_s_address, 
      is_sid_card, is_sid_card_url, self_verification_type, 
      custom_form_Json, personal_custom_form_Json
      )
    values (#{businessType,jdbcType=INTEGER}, #{settlementAccountType,jdbcType=INTEGER}, 
      #{trialDays,jdbcType=INTEGER}, #{isCity,jdbcType=INTEGER}, #{isPeopleNumber,jdbcType=INTEGER}, 
      #{isAddress,jdbcType=INTEGER}, #{isBusinessLicenseCode,jdbcType=INTEGER}, #{isBusinessScope,jdbcType=INTEGER}, 
      #{isBusinessLicense,jdbcType=INTEGER}, #{isAgencyCode,jdbcType=INTEGER}, #{isAgencyCodeLicense,jdbcType=INTEGER}, 
      #{isTaxpayerToProve,jdbcType=INTEGER}, #{companyVerificationType,jdbcType=INTEGER}, 
      #{isSName,jdbcType=INTEGER}, #{isSCity,jdbcType=INTEGER}, #{isSAddress,jdbcType=INTEGER}, 
      #{isSidCard,jdbcType=INTEGER}, #{isSidCardUrl,jdbcType=INTEGER}, #{selfVerificationType,jdbcType=INTEGER}, 
      #{customFormJson,jdbcType=LONGVARCHAR}, #{personalCustomFormJson,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_settled
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessType != null">
        business_type,
      </if>
      <if test="settlementAccountType != null">
        settlement_account_type,
      </if>
      <if test="trialDays != null">
        trial_days,
      </if>
      <if test="isCity != null">
        is_city,
      </if>
      <if test="isPeopleNumber != null">
        is_people_number,
      </if>
      <if test="isAddress != null">
        is_address,
      </if>
      <if test="isBusinessLicenseCode != null">
        is_business_license_code,
      </if>
      <if test="isBusinessScope != null">
        is_business_scope,
      </if>
      <if test="isBusinessLicense != null">
        is_business_license,
      </if>
      <if test="isAgencyCode != null">
        is_agency_code,
      </if>
      <if test="isAgencyCodeLicense != null">
        is_agency_code_license,
      </if>
      <if test="isTaxpayerToProve != null">
        is_taxpayer_to_prove,
      </if>
      <if test="companyVerificationType != null">
        company_verification_type,
      </if>
      <if test="isSName != null">
        is_s_name,
      </if>
      <if test="isSCity != null">
        is_s_city,
      </if>
      <if test="isSAddress != null">
        is_s_address,
      </if>
      <if test="isSidCard != null">
        is_sid_card,
      </if>
      <if test="isSidCardUrl != null">
        is_sid_card_url,
      </if>
      <if test="selfVerificationType != null">
        self_verification_type,
      </if>
      <if test="customFormJson != null">
        custom_form_Json,
      </if>
      <if test="personalCustomFormJson != null">
        personal_custom_form_Json,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessType != null">
        #{businessType,jdbcType=INTEGER},
      </if>
      <if test="settlementAccountType != null">
        #{settlementAccountType,jdbcType=INTEGER},
      </if>
      <if test="trialDays != null">
        #{trialDays,jdbcType=INTEGER},
      </if>
      <if test="isCity != null">
        #{isCity,jdbcType=INTEGER},
      </if>
      <if test="isPeopleNumber != null">
        #{isPeopleNumber,jdbcType=INTEGER},
      </if>
      <if test="isAddress != null">
        #{isAddress,jdbcType=INTEGER},
      </if>
      <if test="isBusinessLicenseCode != null">
        #{isBusinessLicenseCode,jdbcType=INTEGER},
      </if>
      <if test="isBusinessScope != null">
        #{isBusinessScope,jdbcType=INTEGER},
      </if>
      <if test="isBusinessLicense != null">
        #{isBusinessLicense,jdbcType=INTEGER},
      </if>
      <if test="isAgencyCode != null">
        #{isAgencyCode,jdbcType=INTEGER},
      </if>
      <if test="isAgencyCodeLicense != null">
        #{isAgencyCodeLicense,jdbcType=INTEGER},
      </if>
      <if test="isTaxpayerToProve != null">
        #{isTaxpayerToProve,jdbcType=INTEGER},
      </if>
      <if test="companyVerificationType != null">
        #{companyVerificationType,jdbcType=INTEGER},
      </if>
      <if test="isSName != null">
        #{isSName,jdbcType=INTEGER},
      </if>
      <if test="isSCity != null">
        #{isSCity,jdbcType=INTEGER},
      </if>
      <if test="isSAddress != null">
        #{isSAddress,jdbcType=INTEGER},
      </if>
      <if test="isSidCard != null">
        #{isSidCard,jdbcType=INTEGER},
      </if>
      <if test="isSidCardUrl != null">
        #{isSidCardUrl,jdbcType=INTEGER},
      </if>
      <if test="selfVerificationType != null">
        #{selfVerificationType,jdbcType=INTEGER},
      </if>
      <if test="customFormJson != null">
        #{customFormJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="personalCustomFormJson != null">
        #{personalCustomFormJson,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledExample" resultType="java.lang.Long">
    select count(*) from base_settled
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update base_settled
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessType != null">
        business_type = #{record.businessType,jdbcType=INTEGER},
      </if>
      <if test="record.settlementAccountType != null">
        settlement_account_type = #{record.settlementAccountType,jdbcType=INTEGER},
      </if>
      <if test="record.trialDays != null">
        trial_days = #{record.trialDays,jdbcType=INTEGER},
      </if>
      <if test="record.isCity != null">
        is_city = #{record.isCity,jdbcType=INTEGER},
      </if>
      <if test="record.isPeopleNumber != null">
        is_people_number = #{record.isPeopleNumber,jdbcType=INTEGER},
      </if>
      <if test="record.isAddress != null">
        is_address = #{record.isAddress,jdbcType=INTEGER},
      </if>
      <if test="record.isBusinessLicenseCode != null">
        is_business_license_code = #{record.isBusinessLicenseCode,jdbcType=INTEGER},
      </if>
      <if test="record.isBusinessScope != null">
        is_business_scope = #{record.isBusinessScope,jdbcType=INTEGER},
      </if>
      <if test="record.isBusinessLicense != null">
        is_business_license = #{record.isBusinessLicense,jdbcType=INTEGER},
      </if>
      <if test="record.isAgencyCode != null">
        is_agency_code = #{record.isAgencyCode,jdbcType=INTEGER},
      </if>
      <if test="record.isAgencyCodeLicense != null">
        is_agency_code_license = #{record.isAgencyCodeLicense,jdbcType=INTEGER},
      </if>
      <if test="record.isTaxpayerToProve != null">
        is_taxpayer_to_prove = #{record.isTaxpayerToProve,jdbcType=INTEGER},
      </if>
      <if test="record.companyVerificationType != null">
        company_verification_type = #{record.companyVerificationType,jdbcType=INTEGER},
      </if>
      <if test="record.isSName != null">
        is_s_name = #{record.isSName,jdbcType=INTEGER},
      </if>
      <if test="record.isSCity != null">
        is_s_city = #{record.isSCity,jdbcType=INTEGER},
      </if>
      <if test="record.isSAddress != null">
        is_s_address = #{record.isSAddress,jdbcType=INTEGER},
      </if>
      <if test="record.isSidCard != null">
        is_sid_card = #{record.isSidCard,jdbcType=INTEGER},
      </if>
      <if test="record.isSidCardUrl != null">
        is_sid_card_url = #{record.isSidCardUrl,jdbcType=INTEGER},
      </if>
      <if test="record.selfVerificationType != null">
        self_verification_type = #{record.selfVerificationType,jdbcType=INTEGER},
      </if>
      <if test="record.customFormJson != null">
        custom_form_Json = #{record.customFormJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="record.personalCustomFormJson != null">
        personal_custom_form_Json = #{record.personalCustomFormJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update base_settled
    set id = #{record.id,jdbcType=BIGINT},
      business_type = #{record.businessType,jdbcType=INTEGER},
      settlement_account_type = #{record.settlementAccountType,jdbcType=INTEGER},
      trial_days = #{record.trialDays,jdbcType=INTEGER},
      is_city = #{record.isCity,jdbcType=INTEGER},
      is_people_number = #{record.isPeopleNumber,jdbcType=INTEGER},
      is_address = #{record.isAddress,jdbcType=INTEGER},
      is_business_license_code = #{record.isBusinessLicenseCode,jdbcType=INTEGER},
      is_business_scope = #{record.isBusinessScope,jdbcType=INTEGER},
      is_business_license = #{record.isBusinessLicense,jdbcType=INTEGER},
      is_agency_code = #{record.isAgencyCode,jdbcType=INTEGER},
      is_agency_code_license = #{record.isAgencyCodeLicense,jdbcType=INTEGER},
      is_taxpayer_to_prove = #{record.isTaxpayerToProve,jdbcType=INTEGER},
      company_verification_type = #{record.companyVerificationType,jdbcType=INTEGER},
      is_s_name = #{record.isSName,jdbcType=INTEGER},
      is_s_city = #{record.isSCity,jdbcType=INTEGER},
      is_s_address = #{record.isSAddress,jdbcType=INTEGER},
      is_sid_card = #{record.isSidCard,jdbcType=INTEGER},
      is_sid_card_url = #{record.isSidCardUrl,jdbcType=INTEGER},
      self_verification_type = #{record.selfVerificationType,jdbcType=INTEGER},
      custom_form_Json = #{record.customFormJson,jdbcType=LONGVARCHAR},
      personal_custom_form_Json = #{record.personalCustomFormJson,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update base_settled
    set id = #{record.id,jdbcType=BIGINT},
      business_type = #{record.businessType,jdbcType=INTEGER},
      settlement_account_type = #{record.settlementAccountType,jdbcType=INTEGER},
      trial_days = #{record.trialDays,jdbcType=INTEGER},
      is_city = #{record.isCity,jdbcType=INTEGER},
      is_people_number = #{record.isPeopleNumber,jdbcType=INTEGER},
      is_address = #{record.isAddress,jdbcType=INTEGER},
      is_business_license_code = #{record.isBusinessLicenseCode,jdbcType=INTEGER},
      is_business_scope = #{record.isBusinessScope,jdbcType=INTEGER},
      is_business_license = #{record.isBusinessLicense,jdbcType=INTEGER},
      is_agency_code = #{record.isAgencyCode,jdbcType=INTEGER},
      is_agency_code_license = #{record.isAgencyCodeLicense,jdbcType=INTEGER},
      is_taxpayer_to_prove = #{record.isTaxpayerToProve,jdbcType=INTEGER},
      company_verification_type = #{record.companyVerificationType,jdbcType=INTEGER},
      is_s_name = #{record.isSName,jdbcType=INTEGER},
      is_s_city = #{record.isSCity,jdbcType=INTEGER},
      is_s_address = #{record.isSAddress,jdbcType=INTEGER},
      is_sid_card = #{record.isSidCard,jdbcType=INTEGER},
      is_sid_card_url = #{record.isSidCardUrl,jdbcType=INTEGER},
      self_verification_type = #{record.selfVerificationType,jdbcType=INTEGER}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledWithBLOBs">
    update base_settled
    <set>
      <if test="businessType != null">
        business_type = #{businessType,jdbcType=INTEGER},
      </if>
      <if test="settlementAccountType != null">
        settlement_account_type = #{settlementAccountType,jdbcType=INTEGER},
      </if>
      <if test="trialDays != null">
        trial_days = #{trialDays,jdbcType=INTEGER},
      </if>
      <if test="isCity != null">
        is_city = #{isCity,jdbcType=INTEGER},
      </if>
      <if test="isPeopleNumber != null">
        is_people_number = #{isPeopleNumber,jdbcType=INTEGER},
      </if>
      <if test="isAddress != null">
        is_address = #{isAddress,jdbcType=INTEGER},
      </if>
      <if test="isBusinessLicenseCode != null">
        is_business_license_code = #{isBusinessLicenseCode,jdbcType=INTEGER},
      </if>
      <if test="isBusinessScope != null">
        is_business_scope = #{isBusinessScope,jdbcType=INTEGER},
      </if>
      <if test="isBusinessLicense != null">
        is_business_license = #{isBusinessLicense,jdbcType=INTEGER},
      </if>
      <if test="isAgencyCode != null">
        is_agency_code = #{isAgencyCode,jdbcType=INTEGER},
      </if>
      <if test="isAgencyCodeLicense != null">
        is_agency_code_license = #{isAgencyCodeLicense,jdbcType=INTEGER},
      </if>
      <if test="isTaxpayerToProve != null">
        is_taxpayer_to_prove = #{isTaxpayerToProve,jdbcType=INTEGER},
      </if>
      <if test="companyVerificationType != null">
        company_verification_type = #{companyVerificationType,jdbcType=INTEGER},
      </if>
      <if test="isSName != null">
        is_s_name = #{isSName,jdbcType=INTEGER},
      </if>
      <if test="isSCity != null">
        is_s_city = #{isSCity,jdbcType=INTEGER},
      </if>
      <if test="isSAddress != null">
        is_s_address = #{isSAddress,jdbcType=INTEGER},
      </if>
      <if test="isSidCard != null">
        is_sid_card = #{isSidCard,jdbcType=INTEGER},
      </if>
      <if test="isSidCardUrl != null">
        is_sid_card_url = #{isSidCardUrl,jdbcType=INTEGER},
      </if>
      <if test="selfVerificationType != null">
        self_verification_type = #{selfVerificationType,jdbcType=INTEGER},
      </if>
      <if test="customFormJson != null">
        custom_form_Json = #{customFormJson,jdbcType=LONGVARCHAR},
      </if>
      <if test="personalCustomFormJson != null">
        personal_custom_form_Json = #{personalCustomFormJson,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettledWithBLOBs">
    update base_settled
    set business_type = #{businessType,jdbcType=INTEGER},
      settlement_account_type = #{settlementAccountType,jdbcType=INTEGER},
      trial_days = #{trialDays,jdbcType=INTEGER},
      is_city = #{isCity,jdbcType=INTEGER},
      is_people_number = #{isPeopleNumber,jdbcType=INTEGER},
      is_address = #{isAddress,jdbcType=INTEGER},
      is_business_license_code = #{isBusinessLicenseCode,jdbcType=INTEGER},
      is_business_scope = #{isBusinessScope,jdbcType=INTEGER},
      is_business_license = #{isBusinessLicense,jdbcType=INTEGER},
      is_agency_code = #{isAgencyCode,jdbcType=INTEGER},
      is_agency_code_license = #{isAgencyCodeLicense,jdbcType=INTEGER},
      is_taxpayer_to_prove = #{isTaxpayerToProve,jdbcType=INTEGER},
      company_verification_type = #{companyVerificationType,jdbcType=INTEGER},
      is_s_name = #{isSName,jdbcType=INTEGER},
      is_s_city = #{isSCity,jdbcType=INTEGER},
      is_s_address = #{isSAddress,jdbcType=INTEGER},
      is_sid_card = #{isSidCard,jdbcType=INTEGER},
      is_sid_card_url = #{isSidCardUrl,jdbcType=INTEGER},
      self_verification_type = #{selfVerificationType,jdbcType=INTEGER},
      custom_form_Json = #{customFormJson,jdbcType=LONGVARCHAR},
      personal_custom_form_Json = #{personalCustomFormJson,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseSettled">
    update base_settled
    set business_type = #{businessType,jdbcType=INTEGER},
      settlement_account_type = #{settlementAccountType,jdbcType=INTEGER},
      trial_days = #{trialDays,jdbcType=INTEGER},
      is_city = #{isCity,jdbcType=INTEGER},
      is_people_number = #{isPeopleNumber,jdbcType=INTEGER},
      is_address = #{isAddress,jdbcType=INTEGER},
      is_business_license_code = #{isBusinessLicenseCode,jdbcType=INTEGER},
      is_business_scope = #{isBusinessScope,jdbcType=INTEGER},
      is_business_license = #{isBusinessLicense,jdbcType=INTEGER},
      is_agency_code = #{isAgencyCode,jdbcType=INTEGER},
      is_agency_code_license = #{isAgencyCodeLicense,jdbcType=INTEGER},
      is_taxpayer_to_prove = #{isTaxpayerToProve,jdbcType=INTEGER},
      company_verification_type = #{companyVerificationType,jdbcType=INTEGER},
      is_s_name = #{isSName,jdbcType=INTEGER},
      is_s_city = #{isSCity,jdbcType=INTEGER},
      is_s_address = #{isSAddress,jdbcType=INTEGER},
      is_sid_card = #{isSidCard,jdbcType=INTEGER},
      is_sid_card_url = #{isSidCardUrl,jdbcType=INTEGER},
      self_verification_type = #{selfVerificationType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>