<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseArticleMapper">
  <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticle">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="category_id" jdbcType="BIGINT" property="categoryId" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="icon_url" jdbcType="VARCHAR" property="iconUrl" />
    <result column="add_date" jdbcType="TIMESTAMP" property="addDate" />
    <result column="display_sequence" jdbcType="BIGINT" property="displaySequence" />
    <result column="is_release" jdbcType="BIT" property="isRelease" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleWithBLOBs">
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, category_id, title, icon_url, add_date, display_sequence, is_release
  </sql>
  <sql id="Blob_Column_List">
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from base_article
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from base_article
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from base_article
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from base_article
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleExample">
    delete from base_article
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_article (category_id, title, icon_url, 
      add_date, display_sequence, is_release, 
      content)
    values (#{categoryId,jdbcType=BIGINT}, #{title,jdbcType=VARCHAR}, #{iconUrl,jdbcType=VARCHAR}, 
      #{addDate,jdbcType=TIMESTAMP}, #{displaySequence,jdbcType=BIGINT}, #{isRelease,jdbcType=BIT}, 
      #{content,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleWithBLOBs">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into base_article
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        category_id,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="iconUrl != null">
        icon_url,
      </if>
      <if test="addDate != null">
        add_date,
      </if>
      <if test="displaySequence != null">
        display_sequence,
      </if>
      <if test="isRelease != null">
        is_release,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="categoryId != null">
        #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="iconUrl != null">
        #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="addDate != null">
        #{addDate,jdbcType=TIMESTAMP},
      </if>
      <if test="displaySequence != null">
        #{displaySequence,jdbcType=BIGINT},
      </if>
      <if test="isRelease != null">
        #{isRelease,jdbcType=BIT},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="seoTitle != null">
        #{seoTitle,jdbcType=LONGVARCHAR},
      </if>
      <if test="seoDescription != null">
        #{seoDescription,jdbcType=LONGVARCHAR},
      </if>
      <if test="seoKeywords != null">
        #{seoKeywords,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleExample" resultType="java.lang.Long">
    select count(*) from base_article
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update base_article
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.categoryId != null">
        category_id = #{record.categoryId,jdbcType=BIGINT},
      </if>
      <if test="record.title != null">
        title = #{record.title,jdbcType=VARCHAR},
      </if>
      <if test="record.iconUrl != null">
        icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.addDate != null">
        add_date = #{record.addDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.displaySequence != null">
        display_sequence = #{record.displaySequence,jdbcType=BIGINT},
      </if>
      <if test="record.isRelease != null">
        is_release = #{record.isRelease,jdbcType=BIT},
      </if>
      <if test="record.content != null">
        content = #{record.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update base_article
    set id = #{record.id,jdbcType=BIGINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      add_date = #{record.addDate,jdbcType=TIMESTAMP},
      display_sequence = #{record.displaySequence,jdbcType=BIGINT},
      is_release = #{record.isRelease,jdbcType=BIT},
      content = #{record.content,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update base_article
    set id = #{record.id,jdbcType=BIGINT},
      category_id = #{record.categoryId,jdbcType=BIGINT},
      title = #{record.title,jdbcType=VARCHAR},
      icon_url = #{record.iconUrl,jdbcType=VARCHAR},
      add_date = #{record.addDate,jdbcType=TIMESTAMP},
      display_sequence = #{record.displaySequence,jdbcType=BIGINT},
      is_release = #{record.isRelease,jdbcType=BIT}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleWithBLOBs">
    update base_article
    <set>
      <if test="categoryId != null">
        category_id = #{categoryId,jdbcType=BIGINT},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="iconUrl != null">
        icon_url = #{iconUrl,jdbcType=VARCHAR},
      </if>
      <if test="addDate != null">
        add_date = #{addDate,jdbcType=TIMESTAMP},
      </if>
      <if test="displaySequence != null">
        display_sequence = #{displaySequence,jdbcType=BIGINT},
      </if>
      <if test="isRelease != null">
        is_release = #{isRelease,jdbcType=BIT},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticleWithBLOBs">
    update base_article
    set category_id = #{categoryId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      icon_url = #{iconUrl,jdbcType=VARCHAR},
      add_date = #{addDate,jdbcType=TIMESTAMP},
      display_sequence = #{displaySequence,jdbcType=BIGINT},
      is_release = #{isRelease,jdbcType=BIT},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sankuai.shangou.seashop.base.dao.core.domain.BaseArticle">
    update base_article
    set category_id = #{categoryId,jdbcType=BIGINT},
      title = #{title,jdbcType=VARCHAR},
      icon_url = #{iconUrl,jdbcType=VARCHAR},
      add_date = #{addDate,jdbcType=TIMESTAMP},
      display_sequence = #{displaySequence,jdbcType=BIGINT},
      is_release = #{isRelease,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>