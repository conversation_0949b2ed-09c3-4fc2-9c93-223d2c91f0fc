<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.BaseWXMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseWXMenu">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="link_type" property="linkType" />
        <result column="link_value" property="linkValue" />
        <result column="parent_id" property="parentId" />
        <result column="whether_custom" property="whetherCustom" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, `name`, link_type, link_value, parent_id, whether_custom, create_time, update_time
    </sql>

</mapper>
