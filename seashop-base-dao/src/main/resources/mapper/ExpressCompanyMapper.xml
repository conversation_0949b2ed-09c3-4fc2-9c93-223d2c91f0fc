<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.ExpressCompanyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.BaseExpressCompany">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="taobao_code" property="taobaoCode" />
        <result column="kuaidi100_code" property="kuaidi100Code" />
        <result column="kuaidiniao_code" property="kuaidiniaoCode" />
        <result column="width" property="width" />
        <result column="height" property="height" />
        <result column="logo" property="logo" />
        <result column="background_image" property="backgroundImage" />
        <result column="status" property="status" />
        <result column="create_date" property="createDate" />
        <result column="wangdiantong_code" property="wangdiantongCode" />
        <result column="jushuitan_code" property="jushuitanCode" />
        <result column="boluopai_code" property="boluopaiCode" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, name, taobao_code, kuaidi100_code, kuaidiniao_code, width, height, logo, background_image, status, create_date, wangdiantong_code, jushuitan_code, boluopai_code
    </sql>

</mapper>
