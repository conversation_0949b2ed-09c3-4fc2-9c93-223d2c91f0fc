<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.sankuai.shangou.seashop.base.dao.core.mapper.SendMessageRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.sankuai.shangou.seashop.base.dao.core.domain.SendMessageRecord">
        <id column="id" property="id" />
        <result column="message_type" property="messageType" />
        <result column="content_type" property="contentType" />
        <result column="send_content" property="sendContent" />
        <result column="to_user_label" property="toUserLabel" />
        <result column="send_state" property="sendState" />
        <result column="send_time" property="sendTime" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, message_type, content_type, send_content, to_user_label, send_state, send_time
    </sql>

</mapper>
